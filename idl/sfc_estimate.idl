namespace php Dirpc.SDK.Mamba
namespace go Dirpc.SDK.Mamba

struct SFCEstimateRequest {
    1:  required string    token
    2:  required i32       access_key_id
    3:  required string    app_version
    4:  optional double    lat
    5:  optional double    lng
    6:  required string    map_type
    7:  required i32       order_type            (validate="$>0")
    8:  required double    from_lat
    9:  required double    from_lng
    10: required string    from_poi_id           (validate="len($)>0")
    11: optional string    from_poi_type
    12: optional string    from_address
    13: required string    from_name             (validate="len($)>0")
    14: optional string    choose_f_searchid
    15: required double    to_lat
    16: required double    to_lng
    17: required string    to_poi_id             (validate="len($)>0")
    18: optional string    to_poi_type
    19: optional string    to_address
    20: required string    to_name               (validate="len($)>0")
    21: optional string    choose_t_searchid
    22: optional string    multi_require_product                       // 用户勾选状态
    23: optional i64       departure_time                              // 出发时间戳
    24: optional list<i64> departure_range                             // 出发时间段
    25: optional i32       passenger_nums                              // 乘车人数
    26: optional string    from_page_id                                //来源的page_id
    27: optional string    extra_params                                //透传参数
    28: required i32       from_area
    29: required i32       to_area
    30: required i32       channel
    31: optional string    wsgenv
    32: required string    menu_id
    33: optional string select_route_id
    34: required string    from_type
    35: required string    pre_oid
    36: required string    toll_select // 乘客高速费选择 "toll_no_select"-未选择高速费   "toll_negotiate"-协商高速费  "toll_full"-愿付全部高速费  "toll_none"-不付高速费
}

//透传参数
struct ExtraParams {
    1: optional string wyc_bubble_id
}

struct SFCEstimateData {
    1: required string      estimate_id
    2: required string      car_icon
    3: required string      car_title
    4: required string      sub_title
    5: optional string      bubble
    6: required PriceInfo   price_info
    7: required i32         is_selected
    8: required SFCExtraMap extra_map   // 发单参数
    9: optional list<CouponEstimateInfo>  coupon_list
    10: optional list<AdditionalEstimateData> link_list
}

struct AdditionalEstimateData {
    1: required string      estimate_id
    2: required string      car_icon
    3: required string      car_title
    4: required string      subtitle
    5: required i32         is_selected
    6: required PriceInfo   price_info
    7: required SFCExtraMap extra_map   // 发单参数
}

struct AdditionalInfo {
    1: optional list<i32>  product_list
    2: required string      title
    3: required string      legacy_text
    4: required string      legacy_url
    5: required string      legacy_type
}

struct CouponEstimateDcmpInfo {
    1: required string title
    2: required string icon
    3: required list<string> background_colors
    4: required string type
    5: required string title_oid
}

struct CouponEstimateInfo {
    1: required string title
    2: required string icon
    3: required list<string> background_colors
    4: required string type
    5: optional string border_color
    6: optional string title_color
}

struct SFCPriceRollInfo {
    1: required string text_color
    2: required string title_pre
    3: required string title_end
    4: required string price_from
    5: required string price_to
}

struct PriceInfo {
    1: required string title
    2: required string subtitle
    3: required string fare_amount
    4: required string award_amount
    5: required string icon
    6: required string jump_url
    7: optional i32 is_roll
    8: optional SFCPriceRollInfo title_roll_info
    9: optional SFCPriceRollInfo subtitle_roll_info
    10: optional i32 is_decimal_small
}

struct SFCExtraMap {
    1: required i32 product_category
    2: required i32 require_level
    3: required i32 business_id
    4: required i32 product_id
    5: required i32 combo_type
    6: required i32 level_type
    7: required i32 carpool_type
    8: required i32 count_price_type
    9: required i32 pay_type
    10: required i32 trigger_time
    11: required i32 bubble_time
    12: required i32 polymeric_product_category
}

struct PassengerSeatInfo {
    1: required string title
    2: required string sub_title
    3: required i32    total_num
    4: required i32    max_seat_num
}

struct InsuranceInfo {
    1: required string title
    2: required string url
}

struct ExtraTab {
    1: required string extra_text
    2: required string extra_url
}

struct MenuInfo {
    1: required string title
    2: required string jump_url
}

struct SfcBubbleInfo {
    1: required string text
    2: required i32 can_close
    3: optional string text_alignment
    4: optional string arrow_direction
}

struct SfcCasperInfo {
    1: required string id
    2: required string template
    3: required string hummer_cdn
    4: required string weex_cdn
}

struct SfcTollCasperInfo {
    1: required SfcCasperInfo casper_content
}

struct SfcTollFeeInfo {
    1: required string title
    2: required string select_title_pre
    3: required string icon
    4: required string action_type
    5: required SfcBubbleInfo highway_info
    6: required string url
    7: optional string casper_id
}

struct SFCPreCancelButtonInfo {
    1: required SfcSubmitButton left_button
    2: required SfcSubmitButton right_button
}

struct SFCPreCancelOmegaParam {
    1: required string batch_id
    2: required string estimate_trace_id
}

struct SFCPreCancelDetail {
    1: optional string title
    2: optional i64 timestamp
    3: optional SFCPreCancelButtonInfo button_info
    4: optional SFCPreCancelOmegaParam omega_params
}

struct SFCEstimateResponse {
    1:  optional MenuInfo              menu_info
    2:  optional list<SFCEstimateData> estimate_data
    3:  optional PassengerSeatInfo     passenger_seat_info
    4:  optional SFCOperationInfo      operation_info
    5:  optional InsuranceInfo         insurance_info
    6:  optional SfcSubmitButton       submit_button
    7:  optional SfcBottomInfo         bottom_info
    8:  optional string                title
    9:  optional i32                   is_multi
    10: required string                timepicker_scenes_type // timepicker 入参
    11: optional AnimationInfo         animation_info         // 动效物料
    12: optional SfcTollFeeInfo        toll_fee_info
    13: optional BannerInfo            banner_info
    14: optional SfcOmegaParams        omega_params
    15: optional SFCPreCancelDetail    pre_cancel_detail
    16: optional AnimationInfo         roll_fireworks_info
    17: optional string                sps_id
    18: optional AdditionalInfo        additional_info
    19: optional string                map_route_id
}

struct SfcOmegaParams {
    1: optional string carp_coupon_type
}

struct SfcSubmitButton {
    1: required string action_type
    2: required string title
    3: optional string subtitle
}

struct SfcBottomInfo {
    1: required string title
    2: required string jump_url
    3: required string legacy_type
    4: required i64 policy_id
}

struct AnimationInfo{
    1: required string animation_key
    2: required string animation_img
    3: required string default_img
    4: required string close_img
    5: required i32 animation_time
}

struct SFCMultiEstimateResponse {
    1: required i32                 errno
    2: required string              errmsg
    3: required SFCEstimateResponse data
    4: required string              trace_id
}

struct SFCEstimateRollInfo {
    1: required string text_color
    2: required string title_pre
    3: required string title_end
    4: optional string sub_text_color
    5: optional string sub_title_pre
    6: optional string sub_title_end
}

//dcmp结构体
struct SFCEstimateDcmp {
	1: required map<string,SFCDcmpPriceInfo> price_info
	2: required string                       price_icon
	3: required SFCDcmpSeatInfo              seat_info
	4: required MenuInfo                     menu_info
	5: required ExtraTab                     extra_tab
	6: required map<string,string>           title
	7: required SfcSubmitButton              submit_button
	8: required string                       submit_button_subtitle
	9: required SfcBottomInfo                bottom_info
	10: required string                      discount_text
	11: required string                      insurance_url
	12: required string                      declare_url
	13: optional SFCOperationInfo            operation_info
	14: optional AnimationInfo               animation_info
	15: optional SfcTollFeeInfo              toll_fee_info
	16: optional AnimationInfo               fireworks_info
	17: optional map<string, SFCEstimateRollInfo> roll_info
    18: required string                      insurance_legacy_type
    19: optional string                decimal_text
    20: optional string                end_text
    21: optional BannerInfoData home_coming_banner_info
    22:required string                re_appointment_text
    23: optional map<string, SFCEstimateRollInfo> sub_roll_info
}

struct SFCDcmpPriceInfo {
    1: required string sub_title
    2: required string price_title
    3: required string price_sub_title
}

struct SFCDcmpSeatInfo {
    1: required string title
    2: required string sub_title
    3: required i32    max_num
    4: required string new_sub_title
}

struct SFCOperationInfo {
    1: optional string title
    2: optional string select_title
    3: optional string url
    4: optional string type
}

struct BannerInfo {
    1: optional i32             card_type
    2: optional CasperContent   casper_content
    3: optional BannerInfoData  data
}

struct CasperContent {
    1: optional string id
    2: optional string template
    3: optional string weex_cdn
}

struct BannerInfoData {
    1: optional string  background
    2: optional string  left_icon
    3: optional string  title
    4: optional string  subtitle
    5: optional OmegaCk omega_sw
    6: optional string  title_img
    7: optional list<PassengerNoticeBar> passenger_notice_bar
    8: optional i32 stay_time
    9: optional list<string> background_colors
}

struct OmegaCk {
    1: optional string event_id
    2: optional OmegaCkParams parameters
}

struct OmegaCkParams {
    1: optional string coupon_type
    2: optional string sw_type
}

struct PassengerNoticeBar {
    1: optional string T
    2: optional LogData log_data
    3: optional string title
    4: optional string icon
    5: optional string link
}


struct LogData {
    1: optional i32 act_id // eg: 960081
    2: optional string apolloGroupName // eg: ""
    3: optional string business_id // eg: "260"
    4: optional i32 city_id // eg: 1
    5: optional i32 cost // eg: 10
    6: optional string entrance_channel // eg: ""
    7: optional i32 entrance_yewuxian // eg: 0
    8: optional bool is_commercial_ad // eg: false
    9: optional string order_id // eg: ""
    10: optional string pvid // eg: "a7e31811-fa71-4526-93d6-43e70a6f5c6a"
    11: optional string resource_id // eg: "287"
    12: optional string resource_name // eg: "dididri_profile_banners_1"
    13: optional string schedule_id // eg: "233951"
    14: optional i32 slide_id // eg: 1
    15: optional string trace_id // eg: "ac199a29615023252521b82310240303"
    16: optional string type // eg: "engine"
    17: optional i64 uid // eg: 580544957962027
    18: optional string unit_id // eg: "828065"
    19: optional string work_flow_id // eg: "eve_ads_default_20210427114416350-strategy"
}

struct SFCModifyEstimateRequest {
    1: required string    oid
    2: optional list<i64> departure_range   // 出发时间段
    3: required string token
    4: optional i32 passenger_nums
    5: required i32       access_key_id
    6: required string    app_version
}

struct SFCModifyEstimateResponse {
    1: required i32                 errno
    2: required string              errmsg
    3: required SFCModifyEstimateData data
    4: required string              trace_id
}

struct SFCModifyEstimateData{
    1:  required string    estimate_id
    2:  optional list<double> price_list   // 出发时间段
}

struct SFCSimpleEstimateReq {
    1:  required i64       uid
    2:  required i32       access_key_id
    3:  required string    app_version
    4:  optional double    lat
    5:  optional double    lng
    6:  required string    map_type
    7:  required i32       order_type            (validate="$>0")
    8:  required double    from_lat
    9:  required double    from_lng
    10: required string    from_poi_id           (validate="len($)>0")
    11: optional string    from_poi_type
    12: optional string    from_address
    13: required string    from_name             (validate="len($)>0")
    15: required double    to_lat
    16: required double    to_lng
    17: required string    to_poi_id             (validate="len($)>0")
    18: optional string    to_poi_type
    19: optional string    to_address
    20: required string    to_name               (validate="len($)>0")
    21: optional i64       departure_time                              // 出发时间戳
    22: optional list<i64> departure_range                             // 出发时间段
    23: optional i32       passenger_nums                              // 乘车人数
    24: required i32       from_area
    25: required i32       to_area
    26: optional string    phone
    27: optional string    user_channel
    28: optional i64       channel
}

struct SFCSimpleEstimatePriceInfo {
    1: required double estimate_carpool_fail_price
    2: required double estimate_carpool_price
    3: required string estimate_id
    4: required i64    product_category
}

struct SFCSimpleEstimateData {
    1: optional list<SFCSimpleEstimatePriceInfo> price_info
}

struct SFCSimpleEstimateResponse {
    1: required i32                 errno
    2: required string              errmsg
    3: required SFCSimpleEstimateData data
    4: required string              trace_id
}