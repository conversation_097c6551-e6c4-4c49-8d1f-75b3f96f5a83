// Code generated by http-gen compiler. DO NOT EDIT.
// source: business.idl
// argument: --client_style=protoc

package proto

import (
	"fmt"
	"git.xiaojukeji.com/nuwa/golibs/json"
	"reflect"
)

var _ = fmt.Printf

func init() {
	json.Pretouch(reflect.TypeOf((*BusinessRentEstimateReq)(nil)))
	json.Pretouch(reflect.TypeOf((*GetBusinessFormRealDataRequest)(nil)))
	json.Pretouch(reflect.TypeOf((*MultiProduct)(nil)))
	json.Pretouch(reflect.TypeOf((*GetBusinessFormRealDataResponse)(nil)))
	json.Pretouch(reflect.TypeOf((*RealExpectInfoData)(nil)))
	json.Pretouch(reflect.TypeOf((*TotalExpectInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*GlobalExpectInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*ProductExpectInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*ProductExpectInfoItem)(nil)))
	json.Pretouch(reflect.TypeOf((*ExpectInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*ExpectQueueInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*GlobalOrderMatchExpectInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*ProductOrderMatchExpectInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*SaveTimeExpectInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*LuxMultiEstimatePriceRequest)(nil)))
	json.Pretouch(reflect.TypeOf((*BusinessTailorServiceResponse)(nil)))
	json.Pretouch(reflect.TypeOf((*BusinessTailorServiceData)(nil)))
	json.Pretouch(reflect.TypeOf((*BusinessServiceData)(nil)))
	json.Pretouch(reflect.TypeOf((*BusinessPreferOption)(nil)))
}

type BusinessRentEstimateReq struct {
	Token         string   `json:"token" form:"token"`
	AccessKeyId   int32    `json:"access_key_id" form:"access_key_id"`
	AppVersion    string   `json:"app_version" form:"app_version"`
	Lang          string   `json:"lang" form:"lang"`
	Channel       string   `json:"channel" form:"channel"`
	ClientType    string   `json:"client_type" form:"client_type"`
	PlatformType  string   `json:"platform_type" form:"platform_type"`
	MapType       string   `json:"map_type" form:"map_type"`
	Lat           float64  `json:"lat" form:"lat"`           //定位点
	Lng           float64  `json:"lng" form:"lng"`           //定位点
	FromLat       float64  `json:"from_lat" form:"from_lat"` //起点
	FromLng       float64  `json:"from_lng" form:"from_lng"` //起点
	FromPoiId     string   `json:"from_poi_id" form:"from_poi_id"`
	FromPoiType   string   `json:"from_poi_type" form:"from_poi_type"`
	FromAddress   string   `json:"from_address" form:"from_address"`
	FromName      string   `json:"from_name" form:"from_name"`
	ToLat         *float64 `json:"to_lat,omitempty" form:"to_lat"` //终点
	ToLng         *float64 `json:"to_lng,omitempty" form:"to_lng"` //终点
	ToPoiId       *string  `json:"to_poi_id,omitempty" form:"to_poi_id"`
	ToPoiType     *string  `json:"to_poi_type,omitempty" form:"to_poi_type"`
	ToAddress     *string  `json:"to_address,omitempty" form:"to_address"`
	ToName        *string  `json:"to_name,omitempty" form:"to_name"`
	Timestamp     string   `json:"timestamp" form:"timestamp"`
	MenuId        string   `json:"menu_id" form:"menu_id"`
	SourceId      int32    `json:"source_id" form:"source_id"`
	ComboId       int32    `json:"combo_id" form:"combo_id"`
	BusinessIds   []int64  `json:"business_ids" form:"business_ids"`
	PaymentsType  string   `json:"payments_type" form:"payments_type"`
	CallCarType   int32    `json:"call_car_type" form:"call_car_type"`
	OrderType     int32    `json:"order_type" form:"order_type"`
	DepartureTime string   `json:"departure_time" form:"departure_time"`
	CallCarPhone  string   `json:"call_car_phone" form:"call_car_phone"`
	UserType      int32    `json:"user_type" form:"user_type"`
}

func (x *BusinessRentEstimateReq) GetToken() (r string) {
	if x != nil {
		return x.Token
	}
	return r
}

func (x *BusinessRentEstimateReq) GetAccessKeyId() (r int32) {
	if x != nil {
		return x.AccessKeyId
	}
	return r
}

func (x *BusinessRentEstimateReq) GetAppVersion() (r string) {
	if x != nil {
		return x.AppVersion
	}
	return r
}

func (x *BusinessRentEstimateReq) GetLang() (r string) {
	if x != nil {
		return x.Lang
	}
	return r
}

func (x *BusinessRentEstimateReq) GetChannel() (r string) {
	if x != nil {
		return x.Channel
	}
	return r
}

func (x *BusinessRentEstimateReq) GetClientType() (r string) {
	if x != nil {
		return x.ClientType
	}
	return r
}

func (x *BusinessRentEstimateReq) GetPlatformType() (r string) {
	if x != nil {
		return x.PlatformType
	}
	return r
}

func (x *BusinessRentEstimateReq) GetMapType() (r string) {
	if x != nil {
		return x.MapType
	}
	return r
}

func (x *BusinessRentEstimateReq) GetLat() (r float64) {
	if x != nil {
		return x.Lat
	}
	return r
}

func (x *BusinessRentEstimateReq) GetLng() (r float64) {
	if x != nil {
		return x.Lng
	}
	return r
}

func (x *BusinessRentEstimateReq) GetFromLat() (r float64) {
	if x != nil {
		return x.FromLat
	}
	return r
}

func (x *BusinessRentEstimateReq) GetFromLng() (r float64) {
	if x != nil {
		return x.FromLng
	}
	return r
}

func (x *BusinessRentEstimateReq) GetFromPoiId() (r string) {
	if x != nil {
		return x.FromPoiId
	}
	return r
}

func (x *BusinessRentEstimateReq) GetFromPoiType() (r string) {
	if x != nil {
		return x.FromPoiType
	}
	return r
}

func (x *BusinessRentEstimateReq) GetFromAddress() (r string) {
	if x != nil {
		return x.FromAddress
	}
	return r
}

func (x *BusinessRentEstimateReq) GetFromName() (r string) {
	if x != nil {
		return x.FromName
	}
	return r
}

func (x *BusinessRentEstimateReq) GetToLat() (r float64) {
	if x != nil && x.ToLat != nil {
		return *x.ToLat
	}
	return r
}

func (x *BusinessRentEstimateReq) GetToLng() (r float64) {
	if x != nil && x.ToLng != nil {
		return *x.ToLng
	}
	return r
}

func (x *BusinessRentEstimateReq) GetToPoiId() (r string) {
	if x != nil && x.ToPoiId != nil {
		return *x.ToPoiId
	}
	return r
}

func (x *BusinessRentEstimateReq) GetToPoiType() (r string) {
	if x != nil && x.ToPoiType != nil {
		return *x.ToPoiType
	}
	return r
}

func (x *BusinessRentEstimateReq) GetToAddress() (r string) {
	if x != nil && x.ToAddress != nil {
		return *x.ToAddress
	}
	return r
}

func (x *BusinessRentEstimateReq) GetToName() (r string) {
	if x != nil && x.ToName != nil {
		return *x.ToName
	}
	return r
}

func (x *BusinessRentEstimateReq) GetTimestamp() (r string) {
	if x != nil {
		return x.Timestamp
	}
	return r
}

func (x *BusinessRentEstimateReq) GetMenuId() (r string) {
	if x != nil {
		return x.MenuId
	}
	return r
}

func (x *BusinessRentEstimateReq) GetSourceId() (r int32) {
	if x != nil {
		return x.SourceId
	}
	return r
}

func (x *BusinessRentEstimateReq) GetComboId() (r int32) {
	if x != nil {
		return x.ComboId
	}
	return r
}

func (x *BusinessRentEstimateReq) GetBusinessIds() (r []int64) {
	if x != nil {
		return x.BusinessIds
	}
	return r
}

func (x *BusinessRentEstimateReq) GetPaymentsType() (r string) {
	if x != nil {
		return x.PaymentsType
	}
	return r
}

func (x *BusinessRentEstimateReq) GetCallCarType() (r int32) {
	if x != nil {
		return x.CallCarType
	}
	return r
}

func (x *BusinessRentEstimateReq) GetOrderType() (r int32) {
	if x != nil {
		return x.OrderType
	}
	return r
}

func (x *BusinessRentEstimateReq) GetDepartureTime() (r string) {
	if x != nil {
		return x.DepartureTime
	}
	return r
}

func (x *BusinessRentEstimateReq) GetCallCarPhone() (r string) {
	if x != nil {
		return x.CallCarPhone
	}
	return r
}

func (x *BusinessRentEstimateReq) GetUserType() (r int32) {
	if x != nil {
		return x.UserType
	}
	return r
}

func (x *BusinessRentEstimateReq) SetToken(v string) {
	if x != nil {
		x.Token = v
	}
}

func (x *BusinessRentEstimateReq) SetAccessKeyId(v int32) {
	if x != nil {
		x.AccessKeyId = v
	}
}

func (x *BusinessRentEstimateReq) SetAppVersion(v string) {
	if x != nil {
		x.AppVersion = v
	}
}

func (x *BusinessRentEstimateReq) SetLang(v string) {
	if x != nil {
		x.Lang = v
	}
}

func (x *BusinessRentEstimateReq) SetChannel(v string) {
	if x != nil {
		x.Channel = v
	}
}

func (x *BusinessRentEstimateReq) SetClientType(v string) {
	if x != nil {
		x.ClientType = v
	}
}

func (x *BusinessRentEstimateReq) SetPlatformType(v string) {
	if x != nil {
		x.PlatformType = v
	}
}

func (x *BusinessRentEstimateReq) SetMapType(v string) {
	if x != nil {
		x.MapType = v
	}
}

func (x *BusinessRentEstimateReq) SetLat(v float64) {
	if x != nil {
		x.Lat = v
	}
}

func (x *BusinessRentEstimateReq) SetLng(v float64) {
	if x != nil {
		x.Lng = v
	}
}

func (x *BusinessRentEstimateReq) SetFromLat(v float64) {
	if x != nil {
		x.FromLat = v
	}
}

func (x *BusinessRentEstimateReq) SetFromLng(v float64) {
	if x != nil {
		x.FromLng = v
	}
}

func (x *BusinessRentEstimateReq) SetFromPoiId(v string) {
	if x != nil {
		x.FromPoiId = v
	}
}

func (x *BusinessRentEstimateReq) SetFromPoiType(v string) {
	if x != nil {
		x.FromPoiType = v
	}
}

func (x *BusinessRentEstimateReq) SetFromAddress(v string) {
	if x != nil {
		x.FromAddress = v
	}
}

func (x *BusinessRentEstimateReq) SetFromName(v string) {
	if x != nil {
		x.FromName = v
	}
}

func (x *BusinessRentEstimateReq) SetToLat(v float64) {
	if x != nil {
		x.ToLat = &v
	}
}

func (x *BusinessRentEstimateReq) SetToLng(v float64) {
	if x != nil {
		x.ToLng = &v
	}
}

func (x *BusinessRentEstimateReq) SetToPoiId(v string) {
	if x != nil {
		x.ToPoiId = &v
	}
}

func (x *BusinessRentEstimateReq) SetToPoiType(v string) {
	if x != nil {
		x.ToPoiType = &v
	}
}

func (x *BusinessRentEstimateReq) SetToAddress(v string) {
	if x != nil {
		x.ToAddress = &v
	}
}

func (x *BusinessRentEstimateReq) SetToName(v string) {
	if x != nil {
		x.ToName = &v
	}
}

func (x *BusinessRentEstimateReq) SetTimestamp(v string) {
	if x != nil {
		x.Timestamp = v
	}
}

func (x *BusinessRentEstimateReq) SetMenuId(v string) {
	if x != nil {
		x.MenuId = v
	}
}

func (x *BusinessRentEstimateReq) SetSourceId(v int32) {
	if x != nil {
		x.SourceId = v
	}
}

func (x *BusinessRentEstimateReq) SetComboId(v int32) {
	if x != nil {
		x.ComboId = v
	}
}

func (x *BusinessRentEstimateReq) SetBusinessIds(v []int64) {
	if x != nil {
		x.BusinessIds = v
	}
}

func (x *BusinessRentEstimateReq) SetPaymentsType(v string) {
	if x != nil {
		x.PaymentsType = v
	}
}

func (x *BusinessRentEstimateReq) SetCallCarType(v int32) {
	if x != nil {
		x.CallCarType = v
	}
}

func (x *BusinessRentEstimateReq) SetOrderType(v int32) {
	if x != nil {
		x.OrderType = v
	}
}

func (x *BusinessRentEstimateReq) SetDepartureTime(v string) {
	if x != nil {
		x.DepartureTime = v
	}
}

func (x *BusinessRentEstimateReq) SetCallCarPhone(v string) {
	if x != nil {
		x.CallCarPhone = v
	}
}

func (x *BusinessRentEstimateReq) SetUserType(v int32) {
	if x != nil {
		x.UserType = v
	}
}

func (p *BusinessRentEstimateReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BusinessRentEstimateReq(%+v)", *p)
}

type GetBusinessFormRealDataRequest struct {
	MultiProduct  []*MultiProduct `json:"multi_product" form:"multi_product"` //请求车型数据
	ExpectScene   []string        `json:"expect_scene" form:"expect_scene"`   //预期能力: global_answer_rate 全局应答率
	Token         string          `json:"token" form:"token"`                 //预期能力: global_answer_rate 全局应答率
	AccessKeyId   int32           `json:"access_key_id" form:"access_key_id"`
	City          int64           `json:"city" form:"city"`
	AppVersion    string          `json:"app_version" form:"app_version"`
	ClientType    string          `json:"client_type" form:"client_type"` //1: 安卓； 101：IOS； 201：webapp(小程序)； 301：企业级； 401：openAPI
	Channel       int64           `json:"channel" form:"channel"`         //1: 安卓； 101：IOS； 201：webapp(小程序)； 301：企业级； 401：openAPI
	Lang          string          `json:"lang" form:"lang"`
	FromLng       float64         `json:"from_lng" form:"from_lng"`             //出发地位置经度
	FromLat       float64         `json:"from_lat" form:"from_lat"`             //出发地当前位置纬度
	ToLng         float64         `json:"to_lng" form:"to_lng"`                 //目的地当前位置经度
	ToLat         float64         `json:"to_lat" form:"to_lat"`                 //目的地当前位置纬度
	FromName      *string         `json:"from_name,omitempty" form:"from_name"` //目的地当前位置纬度
	ToName        *string         `json:"to_name,omitempty" form:"to_name"`
	BubbleTraceId *string         `json:"bubble_trace_id,omitempty" form:"bubble_trace_id"`
	Oid           *string         `json:"oid,omitempty" form:"oid"`
	SourceType    *int32          `json:"source_type,omitempty" form:"source_type"`
}

func (x *GetBusinessFormRealDataRequest) GetMultiProduct() (r []*MultiProduct) {
	if x != nil {
		return x.MultiProduct
	}
	return r
}

func (x *GetBusinessFormRealDataRequest) GetExpectScene() (r []string) {
	if x != nil {
		return x.ExpectScene
	}
	return r
}

func (x *GetBusinessFormRealDataRequest) GetToken() (r string) {
	if x != nil {
		return x.Token
	}
	return r
}

func (x *GetBusinessFormRealDataRequest) GetAccessKeyId() (r int32) {
	if x != nil {
		return x.AccessKeyId
	}
	return r
}

func (x *GetBusinessFormRealDataRequest) GetCity() (r int64) {
	if x != nil {
		return x.City
	}
	return r
}

func (x *GetBusinessFormRealDataRequest) GetAppVersion() (r string) {
	if x != nil {
		return x.AppVersion
	}
	return r
}

func (x *GetBusinessFormRealDataRequest) GetClientType() (r string) {
	if x != nil {
		return x.ClientType
	}
	return r
}

func (x *GetBusinessFormRealDataRequest) GetChannel() (r int64) {
	if x != nil {
		return x.Channel
	}
	return r
}

func (x *GetBusinessFormRealDataRequest) GetLang() (r string) {
	if x != nil {
		return x.Lang
	}
	return r
}

func (x *GetBusinessFormRealDataRequest) GetFromLng() (r float64) {
	if x != nil {
		return x.FromLng
	}
	return r
}

func (x *GetBusinessFormRealDataRequest) GetFromLat() (r float64) {
	if x != nil {
		return x.FromLat
	}
	return r
}

func (x *GetBusinessFormRealDataRequest) GetToLng() (r float64) {
	if x != nil {
		return x.ToLng
	}
	return r
}

func (x *GetBusinessFormRealDataRequest) GetToLat() (r float64) {
	if x != nil {
		return x.ToLat
	}
	return r
}

func (x *GetBusinessFormRealDataRequest) GetFromName() (r string) {
	if x != nil && x.FromName != nil {
		return *x.FromName
	}
	return r
}

func (x *GetBusinessFormRealDataRequest) GetToName() (r string) {
	if x != nil && x.ToName != nil {
		return *x.ToName
	}
	return r
}

func (x *GetBusinessFormRealDataRequest) GetBubbleTraceId() (r string) {
	if x != nil && x.BubbleTraceId != nil {
		return *x.BubbleTraceId
	}
	return r
}

func (x *GetBusinessFormRealDataRequest) GetOid() (r string) {
	if x != nil && x.Oid != nil {
		return *x.Oid
	}
	return r
}

func (x *GetBusinessFormRealDataRequest) GetSourceType() (r int32) {
	if x != nil && x.SourceType != nil {
		return *x.SourceType
	}
	return r
}

func (x *GetBusinessFormRealDataRequest) SetMultiProduct(v []*MultiProduct) {
	if x != nil {
		x.MultiProduct = v
	}
}

func (x *GetBusinessFormRealDataRequest) SetExpectScene(v []string) {
	if x != nil {
		x.ExpectScene = v
	}
}

func (x *GetBusinessFormRealDataRequest) SetToken(v string) {
	if x != nil {
		x.Token = v
	}
}

func (x *GetBusinessFormRealDataRequest) SetAccessKeyId(v int32) {
	if x != nil {
		x.AccessKeyId = v
	}
}

func (x *GetBusinessFormRealDataRequest) SetCity(v int64) {
	if x != nil {
		x.City = v
	}
}

func (x *GetBusinessFormRealDataRequest) SetAppVersion(v string) {
	if x != nil {
		x.AppVersion = v
	}
}

func (x *GetBusinessFormRealDataRequest) SetClientType(v string) {
	if x != nil {
		x.ClientType = v
	}
}

func (x *GetBusinessFormRealDataRequest) SetChannel(v int64) {
	if x != nil {
		x.Channel = v
	}
}

func (x *GetBusinessFormRealDataRequest) SetLang(v string) {
	if x != nil {
		x.Lang = v
	}
}

func (x *GetBusinessFormRealDataRequest) SetFromLng(v float64) {
	if x != nil {
		x.FromLng = v
	}
}

func (x *GetBusinessFormRealDataRequest) SetFromLat(v float64) {
	if x != nil {
		x.FromLat = v
	}
}

func (x *GetBusinessFormRealDataRequest) SetToLng(v float64) {
	if x != nil {
		x.ToLng = v
	}
}

func (x *GetBusinessFormRealDataRequest) SetToLat(v float64) {
	if x != nil {
		x.ToLat = v
	}
}

func (x *GetBusinessFormRealDataRequest) SetFromName(v string) {
	if x != nil {
		x.FromName = &v
	}
}

func (x *GetBusinessFormRealDataRequest) SetToName(v string) {
	if x != nil {
		x.ToName = &v
	}
}

func (x *GetBusinessFormRealDataRequest) SetBubbleTraceId(v string) {
	if x != nil {
		x.BubbleTraceId = &v
	}
}

func (x *GetBusinessFormRealDataRequest) SetOid(v string) {
	if x != nil {
		x.Oid = &v
	}
}

func (x *GetBusinessFormRealDataRequest) SetSourceType(v int32) {
	if x != nil {
		x.SourceType = &v
	}
}

func (p *GetBusinessFormRealDataRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetBusinessFormRealDataRequest(%+v)", *p)
}

type MultiProduct struct {
	EstimateId string `json:"estimate_id" form:"estimate_id"` //预估ID
	IsSelected bool   `json:"is_selected" form:"is_selected"` //是否选中
}

func (x *MultiProduct) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *MultiProduct) GetIsSelected() (r bool) {
	if x != nil {
		return x.IsSelected
	}
	return r
}

func (x *MultiProduct) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *MultiProduct) SetIsSelected(v bool) {
	if x != nil {
		x.IsSelected = v
	}
}

func (p *MultiProduct) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MultiProduct(%+v)", *p)
}

type GetBusinessFormRealDataResponse struct {
	Errno   int32               `json:"errno" form:"errno"`
	Errmsg  string              `json:"errmsg" form:"errmsg"`
	Data    *RealExpectInfoData `json:"data,omitempty" form:"data"`
	TraceId string              `json:"trace_id" form:"trace_id"`
}

func (x *GetBusinessFormRealDataResponse) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *GetBusinessFormRealDataResponse) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *GetBusinessFormRealDataResponse) GetData() (r *RealExpectInfoData) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *GetBusinessFormRealDataResponse) GetTraceId() (r string) {
	if x != nil {
		return x.TraceId
	}
	return r
}

func (x *GetBusinessFormRealDataResponse) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *GetBusinessFormRealDataResponse) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *GetBusinessFormRealDataResponse) SetData(v *RealExpectInfoData) {
	if x != nil {
		x.Data = v
	}
}

func (x *GetBusinessFormRealDataResponse) SetTraceId(v string) {
	if x != nil {
		x.TraceId = v
	}
}

func (p *GetBusinessFormRealDataResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetBusinessFormRealDataResponse(%+v)", *p)
}

type RealExpectInfoData struct {
	EnterpriseGlobalSceneExpect    *TotalExpectInfo             `json:"enterprise_global_scene_expect,omitempty" form:"enterprise_global_scene_expect"`
	EnterpriseProductSceneExpect   *TotalExpectInfo             `json:"enterprise_product_scene_expect,omitempty" form:"enterprise_product_scene_expect"`
	EnterpriseGlobalEtsSceneExpect *TotalExpectInfo             `json:"enterprise_global_ets_scene_expect,omitempty" form:"enterprise_global_ets_scene_expect"`
	GlobalSceneExpect              *GlobalOrderMatchExpectInfo  `json:"global_scene_expect,omitempty" form:"global_scene_expect"`       //enterprise_order_match_global_scene 全局预期场景
	ProductSceneExpect             *ProductOrderMatchExpectInfo `json:"product_scene_expect,omitempty" form:"product_scene_expect"`     //enterprise_order_match_product_scene 品类预期场景（已发单车型、可追加车型的品类信息）
	SaveTimeSceneExpect            *SaveTimeExpectInfo          `json:"save_time_scene_expect,omitempty" form:"save_time_scene_expect"` //enterprise_order_match_save_time_scene 品类省时信息场景（会员加速、可追加车型省时等）
	ExtraInfo                      map[string]string            `json:"extra_info,omitempty" form:"extra_info"`
}

func (x *RealExpectInfoData) GetEnterpriseGlobalSceneExpect() (r *TotalExpectInfo) {
	if x != nil {
		return x.EnterpriseGlobalSceneExpect
	}
	return r
}

func (x *RealExpectInfoData) GetEnterpriseProductSceneExpect() (r *TotalExpectInfo) {
	if x != nil {
		return x.EnterpriseProductSceneExpect
	}
	return r
}

func (x *RealExpectInfoData) GetEnterpriseGlobalEtsSceneExpect() (r *TotalExpectInfo) {
	if x != nil {
		return x.EnterpriseGlobalEtsSceneExpect
	}
	return r
}

func (x *RealExpectInfoData) GetGlobalSceneExpect() (r *GlobalOrderMatchExpectInfo) {
	if x != nil {
		return x.GlobalSceneExpect
	}
	return r
}

func (x *RealExpectInfoData) GetProductSceneExpect() (r *ProductOrderMatchExpectInfo) {
	if x != nil {
		return x.ProductSceneExpect
	}
	return r
}

func (x *RealExpectInfoData) GetSaveTimeSceneExpect() (r *SaveTimeExpectInfo) {
	if x != nil {
		return x.SaveTimeSceneExpect
	}
	return r
}

func (x *RealExpectInfoData) GetExtraInfo() (r map[string]string) {
	if x != nil {
		return x.ExtraInfo
	}
	return r
}

func (x *RealExpectInfoData) SetEnterpriseGlobalSceneExpect(v *TotalExpectInfo) {
	if x != nil {
		x.EnterpriseGlobalSceneExpect = v
	}
}

func (x *RealExpectInfoData) SetEnterpriseProductSceneExpect(v *TotalExpectInfo) {
	if x != nil {
		x.EnterpriseProductSceneExpect = v
	}
}

func (x *RealExpectInfoData) SetEnterpriseGlobalEtsSceneExpect(v *TotalExpectInfo) {
	if x != nil {
		x.EnterpriseGlobalEtsSceneExpect = v
	}
}

func (x *RealExpectInfoData) SetGlobalSceneExpect(v *GlobalOrderMatchExpectInfo) {
	if x != nil {
		x.GlobalSceneExpect = v
	}
}

func (x *RealExpectInfoData) SetProductSceneExpect(v *ProductOrderMatchExpectInfo) {
	if x != nil {
		x.ProductSceneExpect = v
	}
}

func (x *RealExpectInfoData) SetSaveTimeSceneExpect(v *SaveTimeExpectInfo) {
	if x != nil {
		x.SaveTimeSceneExpect = v
	}
}

func (x *RealExpectInfoData) SetExtraInfo(v map[string]string) {
	if x != nil {
		x.ExtraInfo = v
	}
}

func (p *RealExpectInfoData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RealExpectInfoData(%+v)", *p)
}

type TotalExpectInfo struct {
	GlobalExpectInfo  *GlobalExpectInfo  `json:"global_expect_info,omitempty" form:"global_expect_info"`   //全局预期信息
	ProductExpectInfo *ProductExpectInfo `json:"product_expect_info,omitempty" form:"product_expect_info"` //品类预期信息
	ExtraInfo         map[string]string  `json:"extra_info,omitempty" form:"extra_info"`                   //品类预期信息
}

func (x *TotalExpectInfo) GetGlobalExpectInfo() (r *GlobalExpectInfo) {
	if x != nil {
		return x.GlobalExpectInfo
	}
	return r
}

func (x *TotalExpectInfo) GetProductExpectInfo() (r *ProductExpectInfo) {
	if x != nil {
		return x.ProductExpectInfo
	}
	return r
}

func (x *TotalExpectInfo) GetExtraInfo() (r map[string]string) {
	if x != nil {
		return x.ExtraInfo
	}
	return r
}

func (x *TotalExpectInfo) SetGlobalExpectInfo(v *GlobalExpectInfo) {
	if x != nil {
		x.GlobalExpectInfo = v
	}
}

func (x *TotalExpectInfo) SetProductExpectInfo(v *ProductExpectInfo) {
	if x != nil {
		x.ProductExpectInfo = v
	}
}

func (x *TotalExpectInfo) SetExtraInfo(v map[string]string) {
	if x != nil {
		x.ExtraInfo = v
	}
}

func (p *TotalExpectInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TotalExpectInfo(%+v)", *p)
}

type GlobalExpectInfo struct {
	ShowType   string            `json:"show_type" form:"show_type"`               //预期类型  枚举:etp、ets、answer_rate
	ExpectInfo *ExpectInfo       `json:"expect_info,omitempty" form:"expect_info"` //预期值
	ExtraInfo  map[string]string `json:"extra_info,omitempty" form:"extra_info"`
}

func (x *GlobalExpectInfo) GetShowType() (r string) {
	if x != nil {
		return x.ShowType
	}
	return r
}

func (x *GlobalExpectInfo) GetExpectInfo() (r *ExpectInfo) {
	if x != nil {
		return x.ExpectInfo
	}
	return r
}

func (x *GlobalExpectInfo) GetExtraInfo() (r map[string]string) {
	if x != nil {
		return x.ExtraInfo
	}
	return r
}

func (x *GlobalExpectInfo) SetShowType(v string) {
	if x != nil {
		x.ShowType = v
	}
}

func (x *GlobalExpectInfo) SetExpectInfo(v *ExpectInfo) {
	if x != nil {
		x.ExpectInfo = v
	}
}

func (x *GlobalExpectInfo) SetExtraInfo(v map[string]string) {
	if x != nil {
		x.ExtraInfo = v
	}
}

func (p *GlobalExpectInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GlobalExpectInfo(%+v)", *p)
}

type ProductExpectInfo struct {
	ProductInfos []*ProductExpectInfoItem `json:"product_infos,omitempty" form:"product_infos"` //品类信息
	ExtraInfo    map[string]string        `json:"extra_info,omitempty" form:"extra_info"`
}

func (x *ProductExpectInfo) GetProductInfos() (r []*ProductExpectInfoItem) {
	if x != nil {
		return x.ProductInfos
	}
	return r
}

func (x *ProductExpectInfo) GetExtraInfo() (r map[string]string) {
	if x != nil {
		return x.ExtraInfo
	}
	return r
}

func (x *ProductExpectInfo) SetProductInfos(v []*ProductExpectInfoItem) {
	if x != nil {
		x.ProductInfos = v
	}
}

func (x *ProductExpectInfo) SetExtraInfo(v map[string]string) {
	if x != nil {
		x.ExtraInfo = v
	}
}

func (p *ProductExpectInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProductExpectInfo(%+v)", *p)
}

type ProductExpectInfoItem struct {
	ProductCategory *int32            `json:"product_category,omitempty" form:"product_category"` //唯一标识每个品类
	ShowType        *string           `json:"show_type,omitempty" form:"show_type"`               //预期类型, 预期类型,枚举同上  枚举:etp、ets、answer_rate
	ExpectInfo      *ExpectInfo       `json:"expect_info,omitempty" form:"expect_info"`           //预期值
	SceneFlag       *int32            `json:"scene_flag,omitempty" form:"scene_flag"`             //场景信息
	QueueInfo       *ExpectQueueInfo  `json:"queue_info,omitempty" form:"queue_info"`             //场景信息
	ExtraInfo       map[string]string `json:"extra_info,omitempty" form:"extra_info"`
}

func (x *ProductExpectInfoItem) GetProductCategory() (r int32) {
	if x != nil && x.ProductCategory != nil {
		return *x.ProductCategory
	}
	return r
}

func (x *ProductExpectInfoItem) GetShowType() (r string) {
	if x != nil && x.ShowType != nil {
		return *x.ShowType
	}
	return r
}

func (x *ProductExpectInfoItem) GetExpectInfo() (r *ExpectInfo) {
	if x != nil {
		return x.ExpectInfo
	}
	return r
}

func (x *ProductExpectInfoItem) GetSceneFlag() (r int32) {
	if x != nil && x.SceneFlag != nil {
		return *x.SceneFlag
	}
	return r
}

func (x *ProductExpectInfoItem) GetQueueInfo() (r *ExpectQueueInfo) {
	if x != nil {
		return x.QueueInfo
	}
	return r
}

func (x *ProductExpectInfoItem) GetExtraInfo() (r map[string]string) {
	if x != nil {
		return x.ExtraInfo
	}
	return r
}

func (x *ProductExpectInfoItem) SetProductCategory(v int32) {
	if x != nil {
		x.ProductCategory = &v
	}
}

func (x *ProductExpectInfoItem) SetShowType(v string) {
	if x != nil {
		x.ShowType = &v
	}
}

func (x *ProductExpectInfoItem) SetExpectInfo(v *ExpectInfo) {
	if x != nil {
		x.ExpectInfo = v
	}
}

func (x *ProductExpectInfoItem) SetSceneFlag(v int32) {
	if x != nil {
		x.SceneFlag = &v
	}
}

func (x *ProductExpectInfoItem) SetQueueInfo(v *ExpectQueueInfo) {
	if x != nil {
		x.QueueInfo = v
	}
}

func (x *ProductExpectInfoItem) SetExtraInfo(v map[string]string) {
	if x != nil {
		x.ExtraInfo = v
	}
}

func (p *ProductExpectInfoItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProductExpectInfoItem(%+v)", *p)
}

type ExpectInfo struct {
	QueueInfo *ExpectQueueInfo  `json:"queue_info,omitempty" form:"queue_info"`
	Etp       *int32            `json:"etp,omitempty" form:"etp"`
	Ets       *int32            `json:"ets,omitempty" form:"ets"`
	RespRate  *float64          `json:"resp_rate,omitempty" form:"resp_rate"` //预期值
	ExtraInfo map[string]string `json:"extra_info,omitempty" form:"extra_info"`
}

func (x *ExpectInfo) GetQueueInfo() (r *ExpectQueueInfo) {
	if x != nil {
		return x.QueueInfo
	}
	return r
}

func (x *ExpectInfo) GetEtp() (r int32) {
	if x != nil && x.Etp != nil {
		return *x.Etp
	}
	return r
}

func (x *ExpectInfo) GetEts() (r int32) {
	if x != nil && x.Ets != nil {
		return *x.Ets
	}
	return r
}

func (x *ExpectInfo) GetRespRate() (r float64) {
	if x != nil && x.RespRate != nil {
		return *x.RespRate
	}
	return r
}

func (x *ExpectInfo) GetExtraInfo() (r map[string]string) {
	if x != nil {
		return x.ExtraInfo
	}
	return r
}

func (x *ExpectInfo) SetQueueInfo(v *ExpectQueueInfo) {
	if x != nil {
		x.QueueInfo = v
	}
}

func (x *ExpectInfo) SetEtp(v int32) {
	if x != nil {
		x.Etp = &v
	}
}

func (x *ExpectInfo) SetEts(v int32) {
	if x != nil {
		x.Ets = &v
	}
}

func (x *ExpectInfo) SetRespRate(v float64) {
	if x != nil {
		x.RespRate = &v
	}
}

func (x *ExpectInfo) SetExtraInfo(v map[string]string) {
	if x != nil {
		x.ExtraInfo = v
	}
}

func (p *ExpectInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExpectInfo(%+v)", *p)
}

type ExpectQueueInfo struct {
	QueueId       *int32            `json:"queue_id,omitempty" form:"queue_id"`
	QueueType     *int32            `json:"queue_type,omitempty" form:"queue_type"`
	QueueOpenFlag *int32            `json:"queue_open_flag,omitempty" form:"queue_open_flag"`
	QueueLen      *int32            `json:"queue_len,omitempty" form:"queue_len"`
	Etq           *int32            `json:"etq,omitempty" form:"etq"`
	Rank          *int32            `json:"rank,omitempty" form:"rank"`                 //排名 位置
	QueueStatus   *int32            `json:"queue_status,omitempty" form:"queue_status"` //0：非排队 1：排队 2:查询失败
	GroupKey      *string           `json:"group_key,omitempty" form:"group_key"`       //0：非排队 1：排队 2:查询失败
	ExtraInfo     map[string]string `json:"extra_info,omitempty" form:"extra_info"`
}

func (x *ExpectQueueInfo) GetQueueId() (r int32) {
	if x != nil && x.QueueId != nil {
		return *x.QueueId
	}
	return r
}

func (x *ExpectQueueInfo) GetQueueType() (r int32) {
	if x != nil && x.QueueType != nil {
		return *x.QueueType
	}
	return r
}

func (x *ExpectQueueInfo) GetQueueOpenFlag() (r int32) {
	if x != nil && x.QueueOpenFlag != nil {
		return *x.QueueOpenFlag
	}
	return r
}

func (x *ExpectQueueInfo) GetQueueLen() (r int32) {
	if x != nil && x.QueueLen != nil {
		return *x.QueueLen
	}
	return r
}

func (x *ExpectQueueInfo) GetEtq() (r int32) {
	if x != nil && x.Etq != nil {
		return *x.Etq
	}
	return r
}

func (x *ExpectQueueInfo) GetRank() (r int32) {
	if x != nil && x.Rank != nil {
		return *x.Rank
	}
	return r
}

func (x *ExpectQueueInfo) GetQueueStatus() (r int32) {
	if x != nil && x.QueueStatus != nil {
		return *x.QueueStatus
	}
	return r
}

func (x *ExpectQueueInfo) GetGroupKey() (r string) {
	if x != nil && x.GroupKey != nil {
		return *x.GroupKey
	}
	return r
}

func (x *ExpectQueueInfo) GetExtraInfo() (r map[string]string) {
	if x != nil {
		return x.ExtraInfo
	}
	return r
}

func (x *ExpectQueueInfo) SetQueueId(v int32) {
	if x != nil {
		x.QueueId = &v
	}
}

func (x *ExpectQueueInfo) SetQueueType(v int32) {
	if x != nil {
		x.QueueType = &v
	}
}

func (x *ExpectQueueInfo) SetQueueOpenFlag(v int32) {
	if x != nil {
		x.QueueOpenFlag = &v
	}
}

func (x *ExpectQueueInfo) SetQueueLen(v int32) {
	if x != nil {
		x.QueueLen = &v
	}
}

func (x *ExpectQueueInfo) SetEtq(v int32) {
	if x != nil {
		x.Etq = &v
	}
}

func (x *ExpectQueueInfo) SetRank(v int32) {
	if x != nil {
		x.Rank = &v
	}
}

func (x *ExpectQueueInfo) SetQueueStatus(v int32) {
	if x != nil {
		x.QueueStatus = &v
	}
}

func (x *ExpectQueueInfo) SetGroupKey(v string) {
	if x != nil {
		x.GroupKey = &v
	}
}

func (x *ExpectQueueInfo) SetExtraInfo(v map[string]string) {
	if x != nil {
		x.ExtraInfo = v
	}
}

func (p *ExpectQueueInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExpectQueueInfo(%+v)", *p)
}

type GlobalOrderMatchExpectInfo struct {
	ShowType     *string                  `json:"show_type,omitempty" form:"show_type"`         //预期类型,枚举:etp、ets、answer_rate、
	Scene        int32                    `json:"scene" form:"scene"`                           //1短 2中 3长 4超长
	TimeStart    int64                    `json:"time_start" form:"time_start"`                 //最早应答时间, 时间戳
	TimeEnd      int64                    `json:"time_end" form:"time_end"`                     //最晚应答时间，时间戳，用于长时场景
	OriginScene  int32                    `json:"origin_scene" form:"origin_scene"`             //首次全局预期结果
	ProductInfos []*ProductExpectInfoItem `json:"product_infos,omitempty" form:"product_infos"` //存储发单车型排队信息
	ExtraInfo    map[string]string        `json:"extra_info,omitempty" form:"extra_info"`
}

func (x *GlobalOrderMatchExpectInfo) GetShowType() (r string) {
	if x != nil && x.ShowType != nil {
		return *x.ShowType
	}
	return r
}

func (x *GlobalOrderMatchExpectInfo) GetScene() (r int32) {
	if x != nil {
		return x.Scene
	}
	return r
}

func (x *GlobalOrderMatchExpectInfo) GetTimeStart() (r int64) {
	if x != nil {
		return x.TimeStart
	}
	return r
}

func (x *GlobalOrderMatchExpectInfo) GetTimeEnd() (r int64) {
	if x != nil {
		return x.TimeEnd
	}
	return r
}

func (x *GlobalOrderMatchExpectInfo) GetOriginScene() (r int32) {
	if x != nil {
		return x.OriginScene
	}
	return r
}

func (x *GlobalOrderMatchExpectInfo) GetProductInfos() (r []*ProductExpectInfoItem) {
	if x != nil {
		return x.ProductInfos
	}
	return r
}

func (x *GlobalOrderMatchExpectInfo) GetExtraInfo() (r map[string]string) {
	if x != nil {
		return x.ExtraInfo
	}
	return r
}

func (x *GlobalOrderMatchExpectInfo) SetShowType(v string) {
	if x != nil {
		x.ShowType = &v
	}
}

func (x *GlobalOrderMatchExpectInfo) SetScene(v int32) {
	if x != nil {
		x.Scene = v
	}
}

func (x *GlobalOrderMatchExpectInfo) SetTimeStart(v int64) {
	if x != nil {
		x.TimeStart = v
	}
}

func (x *GlobalOrderMatchExpectInfo) SetTimeEnd(v int64) {
	if x != nil {
		x.TimeEnd = v
	}
}

func (x *GlobalOrderMatchExpectInfo) SetOriginScene(v int32) {
	if x != nil {
		x.OriginScene = v
	}
}

func (x *GlobalOrderMatchExpectInfo) SetProductInfos(v []*ProductExpectInfoItem) {
	if x != nil {
		x.ProductInfos = v
	}
}

func (x *GlobalOrderMatchExpectInfo) SetExtraInfo(v map[string]string) {
	if x != nil {
		x.ExtraInfo = v
	}
}

func (p *GlobalOrderMatchExpectInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GlobalOrderMatchExpectInfo(%+v)", *p)
}

type ProductOrderMatchExpectInfo struct {
	ProductInfos []*ProductExpectInfoItem `json:"product_infos,omitempty" form:"product_infos"` //品类信息
	ExtraInfo    map[string]string        `json:"extra_info,omitempty" form:"extra_info"`
}

func (x *ProductOrderMatchExpectInfo) GetProductInfos() (r []*ProductExpectInfoItem) {
	if x != nil {
		return x.ProductInfos
	}
	return r
}

func (x *ProductOrderMatchExpectInfo) GetExtraInfo() (r map[string]string) {
	if x != nil {
		return x.ExtraInfo
	}
	return r
}

func (x *ProductOrderMatchExpectInfo) SetProductInfos(v []*ProductExpectInfoItem) {
	if x != nil {
		x.ProductInfos = v
	}
}

func (x *ProductOrderMatchExpectInfo) SetExtraInfo(v map[string]string) {
	if x != nil {
		x.ExtraInfo = v
	}
}

func (p *ProductOrderMatchExpectInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProductOrderMatchExpectInfo(%+v)", *p)
}

type SaveTimeExpectInfo struct {
	WaitTimeSaved *int32            `json:"wait_time_saved,omitempty" form:"wait_time_saved"` //省时时间
	ExtraInfo     map[string]string `json:"extra_info,omitempty" form:"extra_info"`
}

func (x *SaveTimeExpectInfo) GetWaitTimeSaved() (r int32) {
	if x != nil && x.WaitTimeSaved != nil {
		return *x.WaitTimeSaved
	}
	return r
}

func (x *SaveTimeExpectInfo) GetExtraInfo() (r map[string]string) {
	if x != nil {
		return x.ExtraInfo
	}
	return r
}

func (x *SaveTimeExpectInfo) SetWaitTimeSaved(v int32) {
	if x != nil {
		x.WaitTimeSaved = &v
	}
}

func (x *SaveTimeExpectInfo) SetExtraInfo(v map[string]string) {
	if x != nil {
		x.ExtraInfo = v
	}
}

func (p *SaveTimeExpectInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SaveTimeExpectInfo(%+v)", *p)
}

type LuxMultiEstimatePriceRequest struct {
	Token                 string  `json:"token" form:"token"`
	Appversion            string  `json:"appversion" form:"appversion"`
	AccessKeyId           int32   `json:"access_key_id" form:"access_key_id"` //端来源标识 http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=118857095
	Channel               int32   `json:"channel" form:"channel"`             //客户端下载来源
	ClientType            int32   `json:"client_type" form:"client_type"`     //区分客户端：安卓乘客端 1； IOS乘客端 101； 所有webapp客户端 201； 企业级 301 ；openApi 401；导流系统 501；
	Lang                  string  `json:"lang" form:"lang"`                   //语言
	A3Token               string  `json:"a3_token" form:"a3_token"`           //反作弊token
	Pixels                string  `json:"pixels" form:"pixels"`               //分辨率
	Maptype               string  `json:"maptype" form:"maptype"`             //地图类型
	Imei                  string  `json:"imei" form:"imei"`                   //设备号
	Suuid                 string  `json:"suuid" form:"suuid"`                 //设备识别id
	TerminalId            string  `json:"terminal_id" form:"terminal_id"`     //终端标识，区分不同端来源;
	OriginId              int32   `json:"origin_id" form:"origin_id"`         //品牌ID:1滴滴； 2优步；3长平,默认滴滴
	PlatformType          int32   `json:"platform_type" form:"platform_type"` //1 IOS 2 android 3 webapp 4 oepenapi 5 b2b 6 guide
	Openid                string  `json:"openid" form:"openid"`               //第三方平台id 微信openid 支付宝openid
	From                  string  `json:"from" form:"from"`                   //暂不知道用途，建议透传主预估参数
	FromLat               float64 `json:"from_lat" form:"from_lat"`
	FromLng               float64 `json:"from_lng" form:"from_lng"`
	FromPoiId             string  `json:"from_poi_id" form:"from_poi_id"`
	FromPoiType           string  `json:"from_poi_type" form:"from_poi_type"`
	FromAddress           string  `json:"from_address" form:"from_address"`
	FromName              string  `json:"from_name" form:"from_name"`
	ToLat                 float64 `json:"to_lat" form:"to_lat"`
	ToLng                 float64 `json:"to_lng" form:"to_lng"`
	ToPoiId               string  `json:"to_poi_id" form:"to_poi_id"`
	ToPoiType             string  `json:"to_poi_type" form:"to_poi_type"`
	ToAddress             string  `json:"to_address" form:"to_address"`
	ToName                string  `json:"to_name" form:"to_name"`
	MenuId                string  `json:"menu_id" form:"menu_id"`                                   //顶导ID
	PageType              int32   `json:"page_type" form:"page_type"`                               //页面类型
	CallCarType           int32   `json:"call_car_type" form:"call_car_type"`                       //代叫类型
	CallCarPhone          string  `json:"call_car_phone" form:"call_car_phone"`                     //代叫手机号
	UserType              int32   `json:"user_type" form:"user_type"`                               //用户类型：1普通用户；2企业用户
	DepartureTime         string  `json:"departure_time" form:"departure_time"`                     //时间戳
	PaymentsType          int32   `json:"payments_type" form:"payments_type"`                       //支付类型
	OrderType             int32   `json:"order_type" form:"order_type"`                             //订单类型
	OriginPageType        int32   `json:"origin_page_type" form:"origin_page_type"`                 //原始入口页面（如预约跳转接送机）
	FlightDepCode         *string `json:"flight_dep_code,omitempty" form:"flight_dep_code"`         //航班出发地三字码,如CTU
	FlightDepTerminal     *string `json:"flight_dep_terminal,omitempty" form:"flight_dep_terminal"` //航班出发航站楼，如T2
	TrafficDepTime        *string `json:"traffic_dep_time,omitempty" form:"traffic_dep_time"`       //航班起飞时间字符串
	FlightArrCode         *string `json:"flight_arr_code,omitempty" form:"flight_arr_code"`         //航班落地三字码,如CTU
	FlightArrTerminal     *string `json:"flight_arr_terminal,omitempty" form:"flight_arr_terminal"` //航班落地航站楼，如T2
	TrafficArrTime        *string `json:"traffic_arr_time,omitempty" form:"traffic_arr_time"`       //航班到达时间字符串
	TrafficNumber         *string `json:"traffic_number,omitempty" form:"traffic_number"`           //航班号，如CA1405
	AirportType           *int32  `json:"airport_type,omitempty" form:"airport_type"`               //接送机类型 1-接机，2-送机，端上入口，无入口传0
	AirportId             *int32  `json:"airport_id,omitempty" form:"airport_id"`                   //接机时为航班落地航站楼id，送机时为出发航站楼id，如1573
	ShiftTime             *int32  `json:"shift_time,omitempty" form:"shift_time"`                   //用车偏移时间（接机时，单位：秒）
	RailwayType           *int32  `json:"railway_type,omitempty" form:"railway_type"`               //用车偏移时间（接机时，单位：秒）
	RailwayId             *int32  `json:"railway_id,omitempty" form:"railway_id"`
	LuxurySelectCarlevels *string `json:"luxury_select_carlevels,omitempty" form:"luxury_select_carlevels"` //选中车型, 示例: 1000 1500
	LuxurySelectDriver    *string `json:"luxury_select_driver,omitempty" form:"luxury_select_driver"`       //选中司机 示例: -1 580543123784568
	BusinessId            *int32  `json:"business_id,omitempty" form:"business_id"`
	RequireLevel          *int32  `json:"require_level,omitempty" form:"require_level"`
	ProductId             *int32  `json:"product_id,omitempty" form:"product_id"`
	ProductCategory       *int32  `json:"product_category,omitempty" form:"product_category"`
	Oid                   *string `json:"oid,omitempty" form:"oid"`
	TabType               *int32  `json:"tab_type,omitempty" form:"tab_type"`               //预估tab 0车型 1司机
	IsMultiSelect         *int32  `json:"is_multi_select,omitempty" form:"is_multi_select"` //是否多勾
	SourceId              *int32  `json:"source_id,omitempty" form:"source_id"`             //是否多勾
}

func (x *LuxMultiEstimatePriceRequest) GetToken() (r string) {
	if x != nil {
		return x.Token
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetAppversion() (r string) {
	if x != nil {
		return x.Appversion
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetAccessKeyId() (r int32) {
	if x != nil {
		return x.AccessKeyId
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetChannel() (r int32) {
	if x != nil {
		return x.Channel
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetClientType() (r int32) {
	if x != nil {
		return x.ClientType
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetLang() (r string) {
	if x != nil {
		return x.Lang
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetA3Token() (r string) {
	if x != nil {
		return x.A3Token
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetPixels() (r string) {
	if x != nil {
		return x.Pixels
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetMaptype() (r string) {
	if x != nil {
		return x.Maptype
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetImei() (r string) {
	if x != nil {
		return x.Imei
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetSuuid() (r string) {
	if x != nil {
		return x.Suuid
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetTerminalId() (r string) {
	if x != nil {
		return x.TerminalId
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetOriginId() (r int32) {
	if x != nil {
		return x.OriginId
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetPlatformType() (r int32) {
	if x != nil {
		return x.PlatformType
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetOpenid() (r string) {
	if x != nil {
		return x.Openid
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetFrom() (r string) {
	if x != nil {
		return x.From
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetFromLat() (r float64) {
	if x != nil {
		return x.FromLat
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetFromLng() (r float64) {
	if x != nil {
		return x.FromLng
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetFromPoiId() (r string) {
	if x != nil {
		return x.FromPoiId
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetFromPoiType() (r string) {
	if x != nil {
		return x.FromPoiType
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetFromAddress() (r string) {
	if x != nil {
		return x.FromAddress
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetFromName() (r string) {
	if x != nil {
		return x.FromName
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetToLat() (r float64) {
	if x != nil {
		return x.ToLat
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetToLng() (r float64) {
	if x != nil {
		return x.ToLng
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetToPoiId() (r string) {
	if x != nil {
		return x.ToPoiId
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetToPoiType() (r string) {
	if x != nil {
		return x.ToPoiType
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetToAddress() (r string) {
	if x != nil {
		return x.ToAddress
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetToName() (r string) {
	if x != nil {
		return x.ToName
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetMenuId() (r string) {
	if x != nil {
		return x.MenuId
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetPageType() (r int32) {
	if x != nil {
		return x.PageType
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetCallCarType() (r int32) {
	if x != nil {
		return x.CallCarType
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetCallCarPhone() (r string) {
	if x != nil {
		return x.CallCarPhone
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetUserType() (r int32) {
	if x != nil {
		return x.UserType
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetDepartureTime() (r string) {
	if x != nil {
		return x.DepartureTime
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetPaymentsType() (r int32) {
	if x != nil {
		return x.PaymentsType
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetOrderType() (r int32) {
	if x != nil {
		return x.OrderType
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetOriginPageType() (r int32) {
	if x != nil {
		return x.OriginPageType
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetFlightDepCode() (r string) {
	if x != nil && x.FlightDepCode != nil {
		return *x.FlightDepCode
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetFlightDepTerminal() (r string) {
	if x != nil && x.FlightDepTerminal != nil {
		return *x.FlightDepTerminal
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetTrafficDepTime() (r string) {
	if x != nil && x.TrafficDepTime != nil {
		return *x.TrafficDepTime
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetFlightArrCode() (r string) {
	if x != nil && x.FlightArrCode != nil {
		return *x.FlightArrCode
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetFlightArrTerminal() (r string) {
	if x != nil && x.FlightArrTerminal != nil {
		return *x.FlightArrTerminal
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetTrafficArrTime() (r string) {
	if x != nil && x.TrafficArrTime != nil {
		return *x.TrafficArrTime
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetTrafficNumber() (r string) {
	if x != nil && x.TrafficNumber != nil {
		return *x.TrafficNumber
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetAirportType() (r int32) {
	if x != nil && x.AirportType != nil {
		return *x.AirportType
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetAirportId() (r int32) {
	if x != nil && x.AirportId != nil {
		return *x.AirportId
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetShiftTime() (r int32) {
	if x != nil && x.ShiftTime != nil {
		return *x.ShiftTime
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetRailwayType() (r int32) {
	if x != nil && x.RailwayType != nil {
		return *x.RailwayType
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetRailwayId() (r int32) {
	if x != nil && x.RailwayId != nil {
		return *x.RailwayId
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetLuxurySelectCarlevels() (r string) {
	if x != nil && x.LuxurySelectCarlevels != nil {
		return *x.LuxurySelectCarlevels
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetLuxurySelectDriver() (r string) {
	if x != nil && x.LuxurySelectDriver != nil {
		return *x.LuxurySelectDriver
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetBusinessId() (r int32) {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetRequireLevel() (r int32) {
	if x != nil && x.RequireLevel != nil {
		return *x.RequireLevel
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetProductId() (r int32) {
	if x != nil && x.ProductId != nil {
		return *x.ProductId
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetProductCategory() (r int32) {
	if x != nil && x.ProductCategory != nil {
		return *x.ProductCategory
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetOid() (r string) {
	if x != nil && x.Oid != nil {
		return *x.Oid
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetTabType() (r int32) {
	if x != nil && x.TabType != nil {
		return *x.TabType
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetIsMultiSelect() (r int32) {
	if x != nil && x.IsMultiSelect != nil {
		return *x.IsMultiSelect
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) GetSourceId() (r int32) {
	if x != nil && x.SourceId != nil {
		return *x.SourceId
	}
	return r
}

func (x *LuxMultiEstimatePriceRequest) SetToken(v string) {
	if x != nil {
		x.Token = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetAppversion(v string) {
	if x != nil {
		x.Appversion = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetAccessKeyId(v int32) {
	if x != nil {
		x.AccessKeyId = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetChannel(v int32) {
	if x != nil {
		x.Channel = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetClientType(v int32) {
	if x != nil {
		x.ClientType = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetLang(v string) {
	if x != nil {
		x.Lang = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetA3Token(v string) {
	if x != nil {
		x.A3Token = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetPixels(v string) {
	if x != nil {
		x.Pixels = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetMaptype(v string) {
	if x != nil {
		x.Maptype = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetImei(v string) {
	if x != nil {
		x.Imei = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetSuuid(v string) {
	if x != nil {
		x.Suuid = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetTerminalId(v string) {
	if x != nil {
		x.TerminalId = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetOriginId(v int32) {
	if x != nil {
		x.OriginId = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetPlatformType(v int32) {
	if x != nil {
		x.PlatformType = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetOpenid(v string) {
	if x != nil {
		x.Openid = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetFrom(v string) {
	if x != nil {
		x.From = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetFromLat(v float64) {
	if x != nil {
		x.FromLat = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetFromLng(v float64) {
	if x != nil {
		x.FromLng = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetFromPoiId(v string) {
	if x != nil {
		x.FromPoiId = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetFromPoiType(v string) {
	if x != nil {
		x.FromPoiType = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetFromAddress(v string) {
	if x != nil {
		x.FromAddress = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetFromName(v string) {
	if x != nil {
		x.FromName = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetToLat(v float64) {
	if x != nil {
		x.ToLat = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetToLng(v float64) {
	if x != nil {
		x.ToLng = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetToPoiId(v string) {
	if x != nil {
		x.ToPoiId = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetToPoiType(v string) {
	if x != nil {
		x.ToPoiType = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetToAddress(v string) {
	if x != nil {
		x.ToAddress = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetToName(v string) {
	if x != nil {
		x.ToName = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetMenuId(v string) {
	if x != nil {
		x.MenuId = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetPageType(v int32) {
	if x != nil {
		x.PageType = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetCallCarType(v int32) {
	if x != nil {
		x.CallCarType = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetCallCarPhone(v string) {
	if x != nil {
		x.CallCarPhone = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetUserType(v int32) {
	if x != nil {
		x.UserType = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetDepartureTime(v string) {
	if x != nil {
		x.DepartureTime = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetPaymentsType(v int32) {
	if x != nil {
		x.PaymentsType = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetOrderType(v int32) {
	if x != nil {
		x.OrderType = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetOriginPageType(v int32) {
	if x != nil {
		x.OriginPageType = v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetFlightDepCode(v string) {
	if x != nil {
		x.FlightDepCode = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetFlightDepTerminal(v string) {
	if x != nil {
		x.FlightDepTerminal = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetTrafficDepTime(v string) {
	if x != nil {
		x.TrafficDepTime = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetFlightArrCode(v string) {
	if x != nil {
		x.FlightArrCode = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetFlightArrTerminal(v string) {
	if x != nil {
		x.FlightArrTerminal = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetTrafficArrTime(v string) {
	if x != nil {
		x.TrafficArrTime = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetTrafficNumber(v string) {
	if x != nil {
		x.TrafficNumber = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetAirportType(v int32) {
	if x != nil {
		x.AirportType = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetAirportId(v int32) {
	if x != nil {
		x.AirportId = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetShiftTime(v int32) {
	if x != nil {
		x.ShiftTime = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetRailwayType(v int32) {
	if x != nil {
		x.RailwayType = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetRailwayId(v int32) {
	if x != nil {
		x.RailwayId = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetLuxurySelectCarlevels(v string) {
	if x != nil {
		x.LuxurySelectCarlevels = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetLuxurySelectDriver(v string) {
	if x != nil {
		x.LuxurySelectDriver = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetBusinessId(v int32) {
	if x != nil {
		x.BusinessId = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetRequireLevel(v int32) {
	if x != nil {
		x.RequireLevel = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetProductId(v int32) {
	if x != nil {
		x.ProductId = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetProductCategory(v int32) {
	if x != nil {
		x.ProductCategory = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetOid(v string) {
	if x != nil {
		x.Oid = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetTabType(v int32) {
	if x != nil {
		x.TabType = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetIsMultiSelect(v int32) {
	if x != nil {
		x.IsMultiSelect = &v
	}
}

func (x *LuxMultiEstimatePriceRequest) SetSourceId(v int32) {
	if x != nil {
		x.SourceId = &v
	}
}

func (p *LuxMultiEstimatePriceRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LuxMultiEstimatePriceRequest(%+v)", *p)
}

type BusinessTailorServiceResponse struct {
	Errno  int32                      `json:"errno" form:"errno"`
	Errmsg string                     `json:"errmsg" form:"errmsg"`
	Data   *BusinessTailorServiceData `json:"data,omitempty" form:"data"`
}

func (x *BusinessTailorServiceResponse) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *BusinessTailorServiceResponse) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *BusinessTailorServiceResponse) GetData() (r *BusinessTailorServiceData) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *BusinessTailorServiceResponse) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *BusinessTailorServiceResponse) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *BusinessTailorServiceResponse) SetData(v *BusinessTailorServiceData) {
	if x != nil {
		x.Data = v
	}
}

func (p *BusinessTailorServiceResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BusinessTailorServiceResponse(%+v)", *p)
}

type BusinessTailorServiceData struct {
	PreferInfo  []*BusinessPreferOption `json:"prefer_info" form:"prefer_info"`             //偏好选项
	WarmInfo    []*BusinessServiceData  `json:"warm_info,omitempty" form:"warm_info"`       //暖心服务数据
	UpgradeInfo []*BusinessServiceData  `json:"upgrade_info,omitempty" form:"upgrade_info"` //升级服务数据
}

func (x *BusinessTailorServiceData) GetPreferInfo() (r []*BusinessPreferOption) {
	if x != nil {
		return x.PreferInfo
	}
	return r
}

func (x *BusinessTailorServiceData) GetWarmInfo() (r []*BusinessServiceData) {
	if x != nil {
		return x.WarmInfo
	}
	return r
}

func (x *BusinessTailorServiceData) GetUpgradeInfo() (r []*BusinessServiceData) {
	if x != nil {
		return x.UpgradeInfo
	}
	return r
}

func (x *BusinessTailorServiceData) SetPreferInfo(v []*BusinessPreferOption) {
	if x != nil {
		x.PreferInfo = v
	}
}

func (x *BusinessTailorServiceData) SetWarmInfo(v []*BusinessServiceData) {
	if x != nil {
		x.WarmInfo = v
	}
}

func (x *BusinessTailorServiceData) SetUpgradeInfo(v []*BusinessServiceData) {
	if x != nil {
		x.UpgradeInfo = v
	}
}

func (p *BusinessTailorServiceData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BusinessTailorServiceData(%+v)", *p)
}

type BusinessServiceData struct {
	Id            int32   `json:"id" form:"id"`                         //个性化服务唯一id
	Price         float64 `json:"price" form:"price"`                   //价格
	DiscountPrice float64 `json:"discount_price" form:"discount_price"` //折扣价格
	FinalPrice    float64 `json:"final_price" form:"final_price"`       //券后价
	DiscountName  string  `json:"discount_name" form:"discount_name"`   //折扣名称
	DiscountTag   string  `json:"discount_tag" form:"discount_tag"`     //折扣标识
	ServiceDesc   string  `json:"service_desc" form:"service_desc"`     //服务解释字段（不可用原因）
	Status        int32   `json:"status" form:"status"`                 //标识服务是否可用
}

func (x *BusinessServiceData) GetId() (r int32) {
	if x != nil {
		return x.Id
	}
	return r
}

func (x *BusinessServiceData) GetPrice() (r float64) {
	if x != nil {
		return x.Price
	}
	return r
}

func (x *BusinessServiceData) GetDiscountPrice() (r float64) {
	if x != nil {
		return x.DiscountPrice
	}
	return r
}

func (x *BusinessServiceData) GetFinalPrice() (r float64) {
	if x != nil {
		return x.FinalPrice
	}
	return r
}

func (x *BusinessServiceData) GetDiscountName() (r string) {
	if x != nil {
		return x.DiscountName
	}
	return r
}

func (x *BusinessServiceData) GetDiscountTag() (r string) {
	if x != nil {
		return x.DiscountTag
	}
	return r
}

func (x *BusinessServiceData) GetServiceDesc() (r string) {
	if x != nil {
		return x.ServiceDesc
	}
	return r
}

func (x *BusinessServiceData) GetStatus() (r int32) {
	if x != nil {
		return x.Status
	}
	return r
}

func (x *BusinessServiceData) SetId(v int32) {
	if x != nil {
		x.Id = v
	}
}

func (x *BusinessServiceData) SetPrice(v float64) {
	if x != nil {
		x.Price = v
	}
}

func (x *BusinessServiceData) SetDiscountPrice(v float64) {
	if x != nil {
		x.DiscountPrice = v
	}
}

func (x *BusinessServiceData) SetFinalPrice(v float64) {
	if x != nil {
		x.FinalPrice = v
	}
}

func (x *BusinessServiceData) SetDiscountName(v string) {
	if x != nil {
		x.DiscountName = v
	}
}

func (x *BusinessServiceData) SetDiscountTag(v string) {
	if x != nil {
		x.DiscountTag = v
	}
}

func (x *BusinessServiceData) SetServiceDesc(v string) {
	if x != nil {
		x.ServiceDesc = v
	}
}

func (x *BusinessServiceData) SetStatus(v int32) {
	if x != nil {
		x.Status = v
	}
}

func (p *BusinessServiceData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BusinessServiceData(%+v)", *p)
}

type BusinessPreferOption struct {
	Id       *int32 `json:"id,omitempty" form:"id"`
	IsSelect *bool  `json:"is_select,omitempty" form:"is_select"`
}

func (x *BusinessPreferOption) GetId() (r int32) {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return r
}

func (x *BusinessPreferOption) GetIsSelect() (r bool) {
	if x != nil && x.IsSelect != nil {
		return *x.IsSelect
	}
	return r
}

func (x *BusinessPreferOption) SetId(v int32) {
	if x != nil {
		x.Id = &v
	}
}

func (x *BusinessPreferOption) SetIsSelect(v bool) {
	if x != nil {
		x.IsSelect = &v
	}
}

func (p *BusinessPreferOption) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BusinessPreferOption(%+v)", *p)
}
