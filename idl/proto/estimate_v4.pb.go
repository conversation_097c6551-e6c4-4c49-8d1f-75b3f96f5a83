// Code generated by http-gen compiler. DO NOT EDIT.
// source: estimate_v4.idl
// argument: --client_style=protoc

package proto

import (
	"fmt"
	"git.xiaojukeji.com/nuwa/golibs/json"
	"reflect"
)

var _ = fmt.Printf

func init() {
	json.Pretouch(reflect.TypeOf((*PMultiEstimatePriceV4Request)(nil)))
	json.Pretouch(reflect.TypeOf((*NewFormMultiEstimatePriceV4Response)(nil)))
	json.Pretouch(reflect.TypeOf((*NewFormEstimateV4Response)(nil)))
	json.Pretouch(reflect.TypeOf((*OperationItem)(nil)))
	json.Pretouch(reflect.TypeOf((*CategoryData)(nil)))
	json.Pretouch(reflect.TypeOf((*NavigationBar)(nil)))
	json.Pretouch(reflect.TypeOf((*NavigationBarParams)(nil)))
	json.Pretouch(reflect.TypeOf((*MoreToastTipData)(nil)))
	json.Pretouch(reflect.TypeOf((*AdditionalServiceData)(nil)))
	json.Pretouch(reflect.TypeOf((*AdditionalService)(nil)))
	json.Pretouch(reflect.TypeOf((*Animation)(nil)))
}

type PMultiEstimatePriceV4Request struct {
	Token                 string   `json:"token" form:"token"`
	AppVersion            string   `json:"app_version" form:"app_version"`
	AccessKeyId           int32    `json:"access_key_id" form:"access_key_id"`
	Channel               int64    `json:"channel" form:"channel"`
	ClientType            int32    `json:"client_type" form:"client_type"`
	Lang                  string   `json:"lang" form:"lang"`
	A3Token               string   `json:"a3_token" form:"a3_token"`
	Pixels                string   `json:"pixels" form:"pixels"`
	Maptype               string   `json:"maptype" form:"maptype"`
	Imei                  string   `json:"imei" form:"imei"`
	Suuid                 string   `json:"suuid" form:"suuid"`
	TerminalId            int64    `json:"terminal_id" form:"terminal_id"`
	OriginId              int64    `json:"origin_id" form:"origin_id"`
	PlatformType          int32    `json:"platform_type" form:"platform_type"`
	Openid                string   `json:"openid" form:"openid"`
	GuideType             int32    `json:"guide_type" form:"guide_type"`
	From                  string   `json:"from" form:"from"`
	PreferredRouteId      string   `json:"preferred_route_id" form:"preferred_route_id"`
	DialogId              string   `json:"dialog_id" form:"dialog_id"`
	SourceChannel         *string  `json:"source_channel,omitempty" form:"source_channel"`
	ScreenScale           *float64 `json:"screen_scale,omitempty" form:"screen_scale"`
	EstimateStyleType     *int32   `json:"estimate_style_type,omitempty" form:"estimate_style_type"`     //预估表单样式，0:老样式，1:单行  2双排新表单 3多tab新表单 4:一站式出行
	RoutePreferenceType   *int32   `json:"route_preference_type,omitempty" form:"route_preference_type"` //路线偏好类型，例如：少附加费、大路优先
	FormHeight            *int64   `json:"form_height,omitempty" form:"form_height"`                     //路线偏好类型，例如：少附加费、大路优先
	FontScaleType         *int32   `json:"font_scale_type,omitempty" form:"font_scale_type"`             //大字模式字段 0:正常 1:大字 2:超大字
	Lat                   float64  `json:"lat" form:"lat"`
	Lng                   float64  `json:"lng" form:"lng"`
	FromLat               float64  `json:"from_lat" form:"from_lat"`
	FromLng               float64  `json:"from_lng" form:"from_lng"`
	FromPoiId             string   `json:"from_poi_id" form:"from_poi_id"`
	FromPoiType           string   `json:"from_poi_type" form:"from_poi_type"`
	FromPoiCode           string   `json:"from_poi_code" form:"from_poi_code"`
	FromAddress           string   `json:"from_address" form:"from_address"`
	FromName              string   `json:"from_name" form:"from_name"`
	ToLat                 float64  `json:"to_lat" form:"to_lat"`
	ToLng                 float64  `json:"to_lng" form:"to_lng"`
	ToPoiId               string   `json:"to_poi_id" form:"to_poi_id"`
	ToPoiType             string   `json:"to_poi_type" form:"to_poi_type"`
	ToAddress             string   `json:"to_address" form:"to_address"`
	ToName                string   `json:"to_name" form:"to_name"`
	DestPoiCode           string   `json:"dest_poi_code" form:"dest_poi_code"`
	DestPoiTag            string   `json:"dest_poi_tag" form:"dest_poi_tag"`
	ChooseFSearchid       string   `json:"choose_f_searchid" form:"choose_f_searchid"` //用户选择起点请求ID
	ChooseTSearchid       string   `json:"choose_t_searchid" form:"choose_t_searchid"` //用户选择终点请求ID
	MenuId                string   `json:"menu_id" form:"menu_id"`
	PageType              int32    `json:"page_type" form:"page_type"`
	CallCarType           int32    `json:"call_car_type" form:"call_car_type"`
	CallCarPhone          string   `json:"call_car_phone" form:"call_car_phone"`
	UserType              int32    `json:"user_type" form:"user_type"`                                 //用户类型 0表示普通用户，2表示企业用户
	DepartureTime         string   `json:"departure_time" form:"departure_time"`                       //时间戳
	PaymentsType          int32    `json:"payments_type" form:"payments_type"`                         //支付类型
	MultiRequireProduct   string   `json:"multi_require_product" form:"multi_require_product"`         //用户勾选项
	HasScroll             int32    `json:"has_scroll" form:"has_scroll"`                               //客户端是否进行过上拉操作
	OrderType             int32    `json:"order_type" form:"order_type"`                               //预约单
	OriginPageType        int32    `json:"origin_page_type" form:"origin_page_type"`                   //原始入口页面（如预约跳转接送机）
	StopoverPoints        string   `json:"stopover_points" form:"stopover_points"`                     //途经点参数 json array 字符串
	TabList               *string  `json:"tab_list,omitempty" form:"tab_list"`                         //如果在有tab的场景下，需要告知tab的列表，供Athena使用，V3接口专用
	AdditionalService     string   `json:"additional_service" form:"additional_service"`               //整个预估的附加需求
	PreferenceFilter      *string  `json:"preference_filter,omitempty" form:"preference_filter"`       //用户选择的过滤器id
	TabId                 *string  `json:"tab_id,omitempty" form:"tab_id"`                             //v3可能有normal/classify
	PreferenceFilterId    *string  `json:"preference_filter_id,omitempty" form:"preference_filter_id"` //用户选择的过滤器id delete
	CarpoolSeatNum        *int32   `json:"carpool_seat_num,omitempty" form:"carpool_seat_num"`         //用户选择的拼车座位数
	CategoryInfo          *string  `json:"category_info,omitempty" form:"category_info"`               //分框折叠态
	ShakeFlag             *int32   `json:"shake_flag,omitempty" form:"shake_flag"`
	PreTraceId            *string  `json:"pre_trace_id,omitempty" form:"pre_trace_id"`
	DepartureRange        *string  `json:"departure_range,omitempty" form:"departure_range"`         //城际拼车订单出发时间
	FlightDepCode         *string  `json:"flight_dep_code,omitempty" form:"flight_dep_code"`         //航班出发地三字码,如CTU
	FlightDepTerminal     *string  `json:"flight_dep_terminal,omitempty" form:"flight_dep_terminal"` //航班出发航站楼，如T2
	TrafficDepTime        *string  `json:"traffic_dep_time,omitempty" form:"traffic_dep_time"`       //航班起飞时间字符串
	FlightArrCode         *string  `json:"flight_arr_code,omitempty" form:"flight_arr_code"`         //航班落地三字码,如CTU
	FlightArrTerminal     *string  `json:"flight_arr_terminal,omitempty" form:"flight_arr_terminal"` //航班落地航站楼，如T2
	TrafficArrTime        *string  `json:"traffic_arr_time,omitempty" form:"traffic_arr_time"`       //航班到达时间字符串
	TrafficNumber         *string  `json:"traffic_number,omitempty" form:"traffic_number"`           //航班号，如CA1405
	AirportType           *int32   `json:"airport_type,omitempty" form:"airport_type"`               //接送机类型 1-接机，2-送机，端上入口，无入口传0
	AirportId             *int32   `json:"airport_id,omitempty" form:"airport_id"`                   //接机时为航班落地航站楼id，送机时为出发航站楼id，如1573
	ShiftTime             *int32   `json:"shift_time,omitempty" form:"shift_time"`                   //用车偏移时间（接机时，单位：秒）
	ActivityId            *int32   `json:"activity_id,omitempty" form:"activity_id"`                 //活动id，去掉原有x_activity_id
	BizTicket             *string  `json:"biz_ticket,omitempty" form:"biz_ticket"`                   //车票ID
	TooFarOrderLimit      *int32   `json:"too_far_order_limit,omitempty" form:"too_far_order_limit"` //专车是否限制超远途订单
	SpecialSceneParam     *int32   `json:"special_scene_param,omitempty" form:"special_scene_param"` //极端天气场景（如暴雨等）
	FromType              int32    `json:"from_type" form:"from_type"`                               //用来标识是否是再来一单场景，3-再来一单
	IsFemaleDriverFirst   int32    `json:"is_female_driver_first" form:"is_female_driver_first"`     //用来标识夜间场景的女司机优先场景
	GuideTraceId          *string  `json:"guide_trace_id,omitempty" form:"guide_trace_id"`           //导流来源的冒泡trace
	LuxurySelectCarlevels *string  `json:"luxury_select_carlevels,omitempty" form:"luxury_select_carlevels"`
	LuxurySelectDriver    *string  `json:"luxury_select_driver,omitempty" form:"luxury_select_driver"`
	AgentType             *string  `json:"agent_type,omitempty" form:"agent_type"`
	Xpsid                 *string  `json:"xpsid,omitempty" form:"xpsid"`
	XpsidRoot             *string  `json:"xpsid_root,omitempty" form:"xpsid_root"`
	OneStopVersion        *string  `json:"one_stop_version,omitempty" form:"one_stop_version"`
	DiffStatus            *string  `json:"diff_status,omitempty" form:"diff_status"`
}

func (x *PMultiEstimatePriceV4Request) GetToken() (r string) {
	if x != nil {
		return x.Token
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetAppVersion() (r string) {
	if x != nil {
		return x.AppVersion
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetAccessKeyId() (r int32) {
	if x != nil {
		return x.AccessKeyId
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetChannel() (r int64) {
	if x != nil {
		return x.Channel
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetClientType() (r int32) {
	if x != nil {
		return x.ClientType
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetLang() (r string) {
	if x != nil {
		return x.Lang
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetA3Token() (r string) {
	if x != nil {
		return x.A3Token
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetPixels() (r string) {
	if x != nil {
		return x.Pixels
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetMaptype() (r string) {
	if x != nil {
		return x.Maptype
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetImei() (r string) {
	if x != nil {
		return x.Imei
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetSuuid() (r string) {
	if x != nil {
		return x.Suuid
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetTerminalId() (r int64) {
	if x != nil {
		return x.TerminalId
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetOriginId() (r int64) {
	if x != nil {
		return x.OriginId
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetPlatformType() (r int32) {
	if x != nil {
		return x.PlatformType
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetOpenid() (r string) {
	if x != nil {
		return x.Openid
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetGuideType() (r int32) {
	if x != nil {
		return x.GuideType
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetFrom() (r string) {
	if x != nil {
		return x.From
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetPreferredRouteId() (r string) {
	if x != nil {
		return x.PreferredRouteId
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetDialogId() (r string) {
	if x != nil {
		return x.DialogId
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetSourceChannel() (r string) {
	if x != nil && x.SourceChannel != nil {
		return *x.SourceChannel
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetScreenScale() (r float64) {
	if x != nil && x.ScreenScale != nil {
		return *x.ScreenScale
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetEstimateStyleType() (r int32) {
	if x != nil && x.EstimateStyleType != nil {
		return *x.EstimateStyleType
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetRoutePreferenceType() (r int32) {
	if x != nil && x.RoutePreferenceType != nil {
		return *x.RoutePreferenceType
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetFormHeight() (r int64) {
	if x != nil && x.FormHeight != nil {
		return *x.FormHeight
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetFontScaleType() (r int32) {
	if x != nil && x.FontScaleType != nil {
		return *x.FontScaleType
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetLat() (r float64) {
	if x != nil {
		return x.Lat
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetLng() (r float64) {
	if x != nil {
		return x.Lng
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetFromLat() (r float64) {
	if x != nil {
		return x.FromLat
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetFromLng() (r float64) {
	if x != nil {
		return x.FromLng
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetFromPoiId() (r string) {
	if x != nil {
		return x.FromPoiId
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetFromPoiType() (r string) {
	if x != nil {
		return x.FromPoiType
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetFromPoiCode() (r string) {
	if x != nil {
		return x.FromPoiCode
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetFromAddress() (r string) {
	if x != nil {
		return x.FromAddress
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetFromName() (r string) {
	if x != nil {
		return x.FromName
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetToLat() (r float64) {
	if x != nil {
		return x.ToLat
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetToLng() (r float64) {
	if x != nil {
		return x.ToLng
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetToPoiId() (r string) {
	if x != nil {
		return x.ToPoiId
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetToPoiType() (r string) {
	if x != nil {
		return x.ToPoiType
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetToAddress() (r string) {
	if x != nil {
		return x.ToAddress
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetToName() (r string) {
	if x != nil {
		return x.ToName
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetDestPoiCode() (r string) {
	if x != nil {
		return x.DestPoiCode
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetDestPoiTag() (r string) {
	if x != nil {
		return x.DestPoiTag
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetChooseFSearchid() (r string) {
	if x != nil {
		return x.ChooseFSearchid
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetChooseTSearchid() (r string) {
	if x != nil {
		return x.ChooseTSearchid
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetMenuId() (r string) {
	if x != nil {
		return x.MenuId
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetPageType() (r int32) {
	if x != nil {
		return x.PageType
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetCallCarType() (r int32) {
	if x != nil {
		return x.CallCarType
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetCallCarPhone() (r string) {
	if x != nil {
		return x.CallCarPhone
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetUserType() (r int32) {
	if x != nil {
		return x.UserType
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetDepartureTime() (r string) {
	if x != nil {
		return x.DepartureTime
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetPaymentsType() (r int32) {
	if x != nil {
		return x.PaymentsType
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetMultiRequireProduct() (r string) {
	if x != nil {
		return x.MultiRequireProduct
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetHasScroll() (r int32) {
	if x != nil {
		return x.HasScroll
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetOrderType() (r int32) {
	if x != nil {
		return x.OrderType
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetOriginPageType() (r int32) {
	if x != nil {
		return x.OriginPageType
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetStopoverPoints() (r string) {
	if x != nil {
		return x.StopoverPoints
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetTabList() (r string) {
	if x != nil && x.TabList != nil {
		return *x.TabList
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetAdditionalService() (r string) {
	if x != nil {
		return x.AdditionalService
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetPreferenceFilter() (r string) {
	if x != nil && x.PreferenceFilter != nil {
		return *x.PreferenceFilter
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetTabId() (r string) {
	if x != nil && x.TabId != nil {
		return *x.TabId
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetPreferenceFilterId() (r string) {
	if x != nil && x.PreferenceFilterId != nil {
		return *x.PreferenceFilterId
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetCarpoolSeatNum() (r int32) {
	if x != nil && x.CarpoolSeatNum != nil {
		return *x.CarpoolSeatNum
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetCategoryInfo() (r string) {
	if x != nil && x.CategoryInfo != nil {
		return *x.CategoryInfo
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetShakeFlag() (r int32) {
	if x != nil && x.ShakeFlag != nil {
		return *x.ShakeFlag
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetPreTraceId() (r string) {
	if x != nil && x.PreTraceId != nil {
		return *x.PreTraceId
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetDepartureRange() (r string) {
	if x != nil && x.DepartureRange != nil {
		return *x.DepartureRange
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetFlightDepCode() (r string) {
	if x != nil && x.FlightDepCode != nil {
		return *x.FlightDepCode
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetFlightDepTerminal() (r string) {
	if x != nil && x.FlightDepTerminal != nil {
		return *x.FlightDepTerminal
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetTrafficDepTime() (r string) {
	if x != nil && x.TrafficDepTime != nil {
		return *x.TrafficDepTime
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetFlightArrCode() (r string) {
	if x != nil && x.FlightArrCode != nil {
		return *x.FlightArrCode
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetFlightArrTerminal() (r string) {
	if x != nil && x.FlightArrTerminal != nil {
		return *x.FlightArrTerminal
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetTrafficArrTime() (r string) {
	if x != nil && x.TrafficArrTime != nil {
		return *x.TrafficArrTime
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetTrafficNumber() (r string) {
	if x != nil && x.TrafficNumber != nil {
		return *x.TrafficNumber
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetAirportType() (r int32) {
	if x != nil && x.AirportType != nil {
		return *x.AirportType
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetAirportId() (r int32) {
	if x != nil && x.AirportId != nil {
		return *x.AirportId
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetShiftTime() (r int32) {
	if x != nil && x.ShiftTime != nil {
		return *x.ShiftTime
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetActivityId() (r int32) {
	if x != nil && x.ActivityId != nil {
		return *x.ActivityId
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetBizTicket() (r string) {
	if x != nil && x.BizTicket != nil {
		return *x.BizTicket
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetTooFarOrderLimit() (r int32) {
	if x != nil && x.TooFarOrderLimit != nil {
		return *x.TooFarOrderLimit
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetSpecialSceneParam() (r int32) {
	if x != nil && x.SpecialSceneParam != nil {
		return *x.SpecialSceneParam
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetFromType() (r int32) {
	if x != nil {
		return x.FromType
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetIsFemaleDriverFirst() (r int32) {
	if x != nil {
		return x.IsFemaleDriverFirst
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetGuideTraceId() (r string) {
	if x != nil && x.GuideTraceId != nil {
		return *x.GuideTraceId
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetLuxurySelectCarlevels() (r string) {
	if x != nil && x.LuxurySelectCarlevels != nil {
		return *x.LuxurySelectCarlevels
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetLuxurySelectDriver() (r string) {
	if x != nil && x.LuxurySelectDriver != nil {
		return *x.LuxurySelectDriver
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetAgentType() (r string) {
	if x != nil && x.AgentType != nil {
		return *x.AgentType
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetXpsid() (r string) {
	if x != nil && x.Xpsid != nil {
		return *x.Xpsid
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetXpsidRoot() (r string) {
	if x != nil && x.XpsidRoot != nil {
		return *x.XpsidRoot
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetOneStopVersion() (r string) {
	if x != nil && x.OneStopVersion != nil {
		return *x.OneStopVersion
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) GetDiffStatus() (r string) {
	if x != nil && x.DiffStatus != nil {
		return *x.DiffStatus
	}
	return r
}

func (x *PMultiEstimatePriceV4Request) SetToken(v string) {
	if x != nil {
		x.Token = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetAppVersion(v string) {
	if x != nil {
		x.AppVersion = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetAccessKeyId(v int32) {
	if x != nil {
		x.AccessKeyId = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetChannel(v int64) {
	if x != nil {
		x.Channel = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetClientType(v int32) {
	if x != nil {
		x.ClientType = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetLang(v string) {
	if x != nil {
		x.Lang = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetA3Token(v string) {
	if x != nil {
		x.A3Token = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetPixels(v string) {
	if x != nil {
		x.Pixels = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetMaptype(v string) {
	if x != nil {
		x.Maptype = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetImei(v string) {
	if x != nil {
		x.Imei = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetSuuid(v string) {
	if x != nil {
		x.Suuid = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetTerminalId(v int64) {
	if x != nil {
		x.TerminalId = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetOriginId(v int64) {
	if x != nil {
		x.OriginId = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetPlatformType(v int32) {
	if x != nil {
		x.PlatformType = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetOpenid(v string) {
	if x != nil {
		x.Openid = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetGuideType(v int32) {
	if x != nil {
		x.GuideType = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetFrom(v string) {
	if x != nil {
		x.From = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetPreferredRouteId(v string) {
	if x != nil {
		x.PreferredRouteId = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetDialogId(v string) {
	if x != nil {
		x.DialogId = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetSourceChannel(v string) {
	if x != nil {
		x.SourceChannel = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetScreenScale(v float64) {
	if x != nil {
		x.ScreenScale = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetEstimateStyleType(v int32) {
	if x != nil {
		x.EstimateStyleType = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetRoutePreferenceType(v int32) {
	if x != nil {
		x.RoutePreferenceType = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetFormHeight(v int64) {
	if x != nil {
		x.FormHeight = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetFontScaleType(v int32) {
	if x != nil {
		x.FontScaleType = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetLat(v float64) {
	if x != nil {
		x.Lat = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetLng(v float64) {
	if x != nil {
		x.Lng = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetFromLat(v float64) {
	if x != nil {
		x.FromLat = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetFromLng(v float64) {
	if x != nil {
		x.FromLng = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetFromPoiId(v string) {
	if x != nil {
		x.FromPoiId = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetFromPoiType(v string) {
	if x != nil {
		x.FromPoiType = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetFromPoiCode(v string) {
	if x != nil {
		x.FromPoiCode = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetFromAddress(v string) {
	if x != nil {
		x.FromAddress = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetFromName(v string) {
	if x != nil {
		x.FromName = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetToLat(v float64) {
	if x != nil {
		x.ToLat = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetToLng(v float64) {
	if x != nil {
		x.ToLng = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetToPoiId(v string) {
	if x != nil {
		x.ToPoiId = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetToPoiType(v string) {
	if x != nil {
		x.ToPoiType = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetToAddress(v string) {
	if x != nil {
		x.ToAddress = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetToName(v string) {
	if x != nil {
		x.ToName = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetDestPoiCode(v string) {
	if x != nil {
		x.DestPoiCode = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetDestPoiTag(v string) {
	if x != nil {
		x.DestPoiTag = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetChooseFSearchid(v string) {
	if x != nil {
		x.ChooseFSearchid = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetChooseTSearchid(v string) {
	if x != nil {
		x.ChooseTSearchid = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetMenuId(v string) {
	if x != nil {
		x.MenuId = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetPageType(v int32) {
	if x != nil {
		x.PageType = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetCallCarType(v int32) {
	if x != nil {
		x.CallCarType = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetCallCarPhone(v string) {
	if x != nil {
		x.CallCarPhone = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetUserType(v int32) {
	if x != nil {
		x.UserType = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetDepartureTime(v string) {
	if x != nil {
		x.DepartureTime = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetPaymentsType(v int32) {
	if x != nil {
		x.PaymentsType = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetMultiRequireProduct(v string) {
	if x != nil {
		x.MultiRequireProduct = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetHasScroll(v int32) {
	if x != nil {
		x.HasScroll = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetOrderType(v int32) {
	if x != nil {
		x.OrderType = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetOriginPageType(v int32) {
	if x != nil {
		x.OriginPageType = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetStopoverPoints(v string) {
	if x != nil {
		x.StopoverPoints = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetTabList(v string) {
	if x != nil {
		x.TabList = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetAdditionalService(v string) {
	if x != nil {
		x.AdditionalService = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetPreferenceFilter(v string) {
	if x != nil {
		x.PreferenceFilter = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetTabId(v string) {
	if x != nil {
		x.TabId = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetPreferenceFilterId(v string) {
	if x != nil {
		x.PreferenceFilterId = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetCarpoolSeatNum(v int32) {
	if x != nil {
		x.CarpoolSeatNum = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetCategoryInfo(v string) {
	if x != nil {
		x.CategoryInfo = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetShakeFlag(v int32) {
	if x != nil {
		x.ShakeFlag = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetPreTraceId(v string) {
	if x != nil {
		x.PreTraceId = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetDepartureRange(v string) {
	if x != nil {
		x.DepartureRange = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetFlightDepCode(v string) {
	if x != nil {
		x.FlightDepCode = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetFlightDepTerminal(v string) {
	if x != nil {
		x.FlightDepTerminal = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetTrafficDepTime(v string) {
	if x != nil {
		x.TrafficDepTime = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetFlightArrCode(v string) {
	if x != nil {
		x.FlightArrCode = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetFlightArrTerminal(v string) {
	if x != nil {
		x.FlightArrTerminal = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetTrafficArrTime(v string) {
	if x != nil {
		x.TrafficArrTime = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetTrafficNumber(v string) {
	if x != nil {
		x.TrafficNumber = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetAirportType(v int32) {
	if x != nil {
		x.AirportType = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetAirportId(v int32) {
	if x != nil {
		x.AirportId = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetShiftTime(v int32) {
	if x != nil {
		x.ShiftTime = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetActivityId(v int32) {
	if x != nil {
		x.ActivityId = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetBizTicket(v string) {
	if x != nil {
		x.BizTicket = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetTooFarOrderLimit(v int32) {
	if x != nil {
		x.TooFarOrderLimit = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetSpecialSceneParam(v int32) {
	if x != nil {
		x.SpecialSceneParam = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetFromType(v int32) {
	if x != nil {
		x.FromType = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetIsFemaleDriverFirst(v int32) {
	if x != nil {
		x.IsFemaleDriverFirst = v
	}
}

func (x *PMultiEstimatePriceV4Request) SetGuideTraceId(v string) {
	if x != nil {
		x.GuideTraceId = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetLuxurySelectCarlevels(v string) {
	if x != nil {
		x.LuxurySelectCarlevels = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetLuxurySelectDriver(v string) {
	if x != nil {
		x.LuxurySelectDriver = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetAgentType(v string) {
	if x != nil {
		x.AgentType = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetXpsid(v string) {
	if x != nil {
		x.Xpsid = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetXpsidRoot(v string) {
	if x != nil {
		x.XpsidRoot = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetOneStopVersion(v string) {
	if x != nil {
		x.OneStopVersion = &v
	}
}

func (x *PMultiEstimatePriceV4Request) SetDiffStatus(v string) {
	if x != nil {
		x.DiffStatus = &v
	}
}

func (p *PMultiEstimatePriceV4Request) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PMultiEstimatePriceV4Request(%+v)", *p)
}

type NewFormMultiEstimatePriceV4Response struct {
	Errno   int32                      `json:"errno" form:"errno"`
	Errmsg  string                     `json:"errmsg" form:"errmsg"`
	TraceId string                     `json:"trace_id" form:"trace_id"`
	Data    *NewFormEstimateV4Response `json:"data,omitempty" form:"data"`
}

func (x *NewFormMultiEstimatePriceV4Response) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *NewFormMultiEstimatePriceV4Response) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *NewFormMultiEstimatePriceV4Response) GetTraceId() (r string) {
	if x != nil {
		return x.TraceId
	}
	return r
}

func (x *NewFormMultiEstimatePriceV4Response) GetData() (r *NewFormEstimateV4Response) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *NewFormMultiEstimatePriceV4Response) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *NewFormMultiEstimatePriceV4Response) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *NewFormMultiEstimatePriceV4Response) SetTraceId(v string) {
	if x != nil {
		x.TraceId = v
	}
}

func (x *NewFormMultiEstimatePriceV4Response) SetData(v *NewFormEstimateV4Response) {
	if x != nil {
		x.Data = v
	}
}

func (p *NewFormMultiEstimatePriceV4Response) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NewFormMultiEstimatePriceV4Response(%+v)", *p)
}

type NewFormEstimateV4Response struct {
	EstimateTraceId         string                    `json:"estimate_trace_id" form:"estimate_trace_id"`
	EstimateData            map[int64]*V3EstimateData `json:"estimate_data" form:"estimate_data"`
	IsSupportMultiSelection int32                     `json:"is_support_multi_selection" form:"is_support_multi_selection"`     //是否支持多选，预约单时不支持 0-不支持；1-支持
	FeeDetailUrl            string                    `json:"fee_detail_url" form:"fee_detail_url"`                             //费用明细页地址
	PluginPageInfo          *PluginPageInfo           `json:"plugin_page_info,omitempty" form:"plugin_page_info"`               //动调、春节服务费等发单拦截页
	UserPayInfo             *PaymentOptionModule      `json:"user_pay_info,omitempty" form:"user_pay_info"`                     //6.0支付方式并集
	Layout                  []*NewFormLayout          `json:"layout" form:"layout"`                                             //布局
	ToastTip                *string                   `json:"toast_tip,omitempty" form:"toast_tip"`                             //预估完成后提示文案
	PNewOrderParams         map[string]string         `json:"p_new_order_params" form:"p_new_order_params"`                     //发单参数 预估级别
	AdditionalServiceData   *AdditionalServiceData    `json:"additional_service_data,omitempty" form:"additional_service_data"` //附加需求信息
	TravelForecast          map[string]string         `json:"travel_forecast" form:"travel_forecast"`                           //行程预测
	OperationList           []*OperationItem          `json:"operation_list" form:"operation_list"`                             //行程预测
	CategoryInfo            []*CategoryData           `json:"category_info,omitempty" form:"category_info"`                     //三方表单侧边栏信息
	OrderButtonInfo         *OrderButtonInfo          `json:"order_button_info,omitempty" form:"order_button_info"`             //发单按钮信息
	SelectionStyleType      *int32                    `json:"selection_style_type,omitempty" form:"selection_style_type"`       //预约单是否出新样式：0不出，1 出
	IsCallcarDisabled       *int32                    `json:"is_callcar_disabled,omitempty" form:"is_callcar_disabled"`         //是否屏蔽代叫按钮 1 屏蔽 0 不屏蔽
	MultiRouteTipsData      map[string]string         `json:"multi_route_tips_data,omitempty" form:"multi_route_tips_data"`     //提示文案全集
	ShowCategory            *int32                    `json:"show_category,omitempty" form:"show_category"`                     //侧边栏展示策略
	NavigationBar           []*NavigationBar          `json:"navigation_bar,omitempty" form:"navigation_bar"`                   //新版操作台
	RealParams              map[string]int32          `json:"real_params,omitempty" form:"real_params"`                         //端请求mamba透传参数
	ExpectParams            map[string]int32          `json:"expect_params,omitempty" form:"expect_params"`                     //预期信息
	GroupData               map[string]*NewFormGroup  `json:"group_data,omitempty" form:"group_data"`                           //盒子信息
	MoreToastTip            *MoreToastTipData         `json:"more_toast_tip,omitempty" form:"more_toast_tip"`                   //新版toast
	RecForm                 *int32                    `json:"rec_form,omitempty" form:"rec_form"`                               //新推荐表单标识
	RecLayout               []*NewFormLayout          `json:"rec_layout,omitempty" form:"rec_layout"`                           //推荐layout
	FormStyleExp            *int32                    `json:"form_style_exp,omitempty" form:"form_style_exp"`                   //新style标识
	SideParams              map[string]int32          `json:"side_params,omitempty" form:"side_params"`                         //端请求mamba透传参数
	TabExtraData            *TabExtraData             `json:"tab_extra_data,omitempty" form:"tab_extra_data"`                   //端请求mamba透传参数
	FeeMsgTemplate          *string                   `json:"fee_msg_template,omitempty" form:"fee_msg_template"`               //底部操作台 价格展示模版
	PhoneAdaptation         *int32                    `json:"phone_adaptation,omitempty" form:"phone_adaptation"`               //是否大屏适配
}

func (x *NewFormEstimateV4Response) GetEstimateTraceId() (r string) {
	if x != nil {
		return x.EstimateTraceId
	}
	return r
}

func (x *NewFormEstimateV4Response) GetEstimateData() (r map[int64]*V3EstimateData) {
	if x != nil {
		return x.EstimateData
	}
	return r
}

func (x *NewFormEstimateV4Response) GetIsSupportMultiSelection() (r int32) {
	if x != nil {
		return x.IsSupportMultiSelection
	}
	return r
}

func (x *NewFormEstimateV4Response) GetFeeDetailUrl() (r string) {
	if x != nil {
		return x.FeeDetailUrl
	}
	return r
}

func (x *NewFormEstimateV4Response) GetPluginPageInfo() (r *PluginPageInfo) {
	if x != nil {
		return x.PluginPageInfo
	}
	return r
}

func (x *NewFormEstimateV4Response) GetUserPayInfo() (r *PaymentOptionModule) {
	if x != nil {
		return x.UserPayInfo
	}
	return r
}

func (x *NewFormEstimateV4Response) GetLayout() (r []*NewFormLayout) {
	if x != nil {
		return x.Layout
	}
	return r
}

func (x *NewFormEstimateV4Response) GetToastTip() (r string) {
	if x != nil && x.ToastTip != nil {
		return *x.ToastTip
	}
	return r
}

func (x *NewFormEstimateV4Response) GetPNewOrderParams() (r map[string]string) {
	if x != nil {
		return x.PNewOrderParams
	}
	return r
}

func (x *NewFormEstimateV4Response) GetAdditionalServiceData() (r *AdditionalServiceData) {
	if x != nil {
		return x.AdditionalServiceData
	}
	return r
}

func (x *NewFormEstimateV4Response) GetTravelForecast() (r map[string]string) {
	if x != nil {
		return x.TravelForecast
	}
	return r
}

func (x *NewFormEstimateV4Response) GetOperationList() (r []*OperationItem) {
	if x != nil {
		return x.OperationList
	}
	return r
}

func (x *NewFormEstimateV4Response) GetCategoryInfo() (r []*CategoryData) {
	if x != nil {
		return x.CategoryInfo
	}
	return r
}

func (x *NewFormEstimateV4Response) GetOrderButtonInfo() (r *OrderButtonInfo) {
	if x != nil {
		return x.OrderButtonInfo
	}
	return r
}

func (x *NewFormEstimateV4Response) GetSelectionStyleType() (r int32) {
	if x != nil && x.SelectionStyleType != nil {
		return *x.SelectionStyleType
	}
	return r
}

func (x *NewFormEstimateV4Response) GetIsCallcarDisabled() (r int32) {
	if x != nil && x.IsCallcarDisabled != nil {
		return *x.IsCallcarDisabled
	}
	return r
}

func (x *NewFormEstimateV4Response) GetMultiRouteTipsData() (r map[string]string) {
	if x != nil {
		return x.MultiRouteTipsData
	}
	return r
}

func (x *NewFormEstimateV4Response) GetShowCategory() (r int32) {
	if x != nil && x.ShowCategory != nil {
		return *x.ShowCategory
	}
	return r
}

func (x *NewFormEstimateV4Response) GetNavigationBar() (r []*NavigationBar) {
	if x != nil {
		return x.NavigationBar
	}
	return r
}

func (x *NewFormEstimateV4Response) GetRealParams() (r map[string]int32) {
	if x != nil {
		return x.RealParams
	}
	return r
}

func (x *NewFormEstimateV4Response) GetExpectParams() (r map[string]int32) {
	if x != nil {
		return x.ExpectParams
	}
	return r
}

func (x *NewFormEstimateV4Response) GetGroupData() (r map[string]*NewFormGroup) {
	if x != nil {
		return x.GroupData
	}
	return r
}

func (x *NewFormEstimateV4Response) GetMoreToastTip() (r *MoreToastTipData) {
	if x != nil {
		return x.MoreToastTip
	}
	return r
}

func (x *NewFormEstimateV4Response) GetRecForm() (r int32) {
	if x != nil && x.RecForm != nil {
		return *x.RecForm
	}
	return r
}

func (x *NewFormEstimateV4Response) GetRecLayout() (r []*NewFormLayout) {
	if x != nil {
		return x.RecLayout
	}
	return r
}

func (x *NewFormEstimateV4Response) GetFormStyleExp() (r int32) {
	if x != nil && x.FormStyleExp != nil {
		return *x.FormStyleExp
	}
	return r
}

func (x *NewFormEstimateV4Response) GetSideParams() (r map[string]int32) {
	if x != nil {
		return x.SideParams
	}
	return r
}

func (x *NewFormEstimateV4Response) GetTabExtraData() (r *TabExtraData) {
	if x != nil {
		return x.TabExtraData
	}
	return r
}

func (x *NewFormEstimateV4Response) GetFeeMsgTemplate() (r string) {
	if x != nil && x.FeeMsgTemplate != nil {
		return *x.FeeMsgTemplate
	}
	return r
}

func (x *NewFormEstimateV4Response) GetPhoneAdaptation() (r int32) {
	if x != nil && x.PhoneAdaptation != nil {
		return *x.PhoneAdaptation
	}
	return r
}

func (x *NewFormEstimateV4Response) SetEstimateTraceId(v string) {
	if x != nil {
		x.EstimateTraceId = v
	}
}

func (x *NewFormEstimateV4Response) SetEstimateData(v map[int64]*V3EstimateData) {
	if x != nil {
		x.EstimateData = v
	}
}

func (x *NewFormEstimateV4Response) SetIsSupportMultiSelection(v int32) {
	if x != nil {
		x.IsSupportMultiSelection = v
	}
}

func (x *NewFormEstimateV4Response) SetFeeDetailUrl(v string) {
	if x != nil {
		x.FeeDetailUrl = v
	}
}

func (x *NewFormEstimateV4Response) SetPluginPageInfo(v *PluginPageInfo) {
	if x != nil {
		x.PluginPageInfo = v
	}
}

func (x *NewFormEstimateV4Response) SetUserPayInfo(v *PaymentOptionModule) {
	if x != nil {
		x.UserPayInfo = v
	}
}

func (x *NewFormEstimateV4Response) SetLayout(v []*NewFormLayout) {
	if x != nil {
		x.Layout = v
	}
}

func (x *NewFormEstimateV4Response) SetToastTip(v string) {
	if x != nil {
		x.ToastTip = &v
	}
}

func (x *NewFormEstimateV4Response) SetPNewOrderParams(v map[string]string) {
	if x != nil {
		x.PNewOrderParams = v
	}
}

func (x *NewFormEstimateV4Response) SetAdditionalServiceData(v *AdditionalServiceData) {
	if x != nil {
		x.AdditionalServiceData = v
	}
}

func (x *NewFormEstimateV4Response) SetTravelForecast(v map[string]string) {
	if x != nil {
		x.TravelForecast = v
	}
}

func (x *NewFormEstimateV4Response) SetOperationList(v []*OperationItem) {
	if x != nil {
		x.OperationList = v
	}
}

func (x *NewFormEstimateV4Response) SetCategoryInfo(v []*CategoryData) {
	if x != nil {
		x.CategoryInfo = v
	}
}

func (x *NewFormEstimateV4Response) SetOrderButtonInfo(v *OrderButtonInfo) {
	if x != nil {
		x.OrderButtonInfo = v
	}
}

func (x *NewFormEstimateV4Response) SetSelectionStyleType(v int32) {
	if x != nil {
		x.SelectionStyleType = &v
	}
}

func (x *NewFormEstimateV4Response) SetIsCallcarDisabled(v int32) {
	if x != nil {
		x.IsCallcarDisabled = &v
	}
}

func (x *NewFormEstimateV4Response) SetMultiRouteTipsData(v map[string]string) {
	if x != nil {
		x.MultiRouteTipsData = v
	}
}

func (x *NewFormEstimateV4Response) SetShowCategory(v int32) {
	if x != nil {
		x.ShowCategory = &v
	}
}

func (x *NewFormEstimateV4Response) SetNavigationBar(v []*NavigationBar) {
	if x != nil {
		x.NavigationBar = v
	}
}

func (x *NewFormEstimateV4Response) SetRealParams(v map[string]int32) {
	if x != nil {
		x.RealParams = v
	}
}

func (x *NewFormEstimateV4Response) SetExpectParams(v map[string]int32) {
	if x != nil {
		x.ExpectParams = v
	}
}

func (x *NewFormEstimateV4Response) SetGroupData(v map[string]*NewFormGroup) {
	if x != nil {
		x.GroupData = v
	}
}

func (x *NewFormEstimateV4Response) SetMoreToastTip(v *MoreToastTipData) {
	if x != nil {
		x.MoreToastTip = v
	}
}

func (x *NewFormEstimateV4Response) SetRecForm(v int32) {
	if x != nil {
		x.RecForm = &v
	}
}

func (x *NewFormEstimateV4Response) SetRecLayout(v []*NewFormLayout) {
	if x != nil {
		x.RecLayout = v
	}
}

func (x *NewFormEstimateV4Response) SetFormStyleExp(v int32) {
	if x != nil {
		x.FormStyleExp = &v
	}
}

func (x *NewFormEstimateV4Response) SetSideParams(v map[string]int32) {
	if x != nil {
		x.SideParams = v
	}
}

func (x *NewFormEstimateV4Response) SetTabExtraData(v *TabExtraData) {
	if x != nil {
		x.TabExtraData = v
	}
}

func (x *NewFormEstimateV4Response) SetFeeMsgTemplate(v string) {
	if x != nil {
		x.FeeMsgTemplate = &v
	}
}

func (x *NewFormEstimateV4Response) SetPhoneAdaptation(v int32) {
	if x != nil {
		x.PhoneAdaptation = &v
	}
}

func (p *NewFormEstimateV4Response) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NewFormEstimateV4Response(%+v)", *p)
}

type OperationItem struct {
	Key   string `json:"key" form:"key"`
	Title string `json:"title" form:"title"`
	Link  string `json:"link" form:"link"`
}

func (x *OperationItem) GetKey() (r string) {
	if x != nil {
		return x.Key
	}
	return r
}

func (x *OperationItem) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *OperationItem) GetLink() (r string) {
	if x != nil {
		return x.Link
	}
	return r
}

func (x *OperationItem) SetKey(v string) {
	if x != nil {
		x.Key = v
	}
}

func (x *OperationItem) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *OperationItem) SetLink(v string) {
	if x != nil {
		x.Link = v
	}
}

func (p *OperationItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OperationItem(%+v)", *p)
}

type CategoryData struct {
	CategoryId      int32    `json:"category_id" form:"category_id"`
	Title           string   `json:"title" form:"title"`
	SubTitle        string   `json:"sub_title" form:"sub_title"`
	Icon            string   `json:"icon" form:"icon"`
	BgGradients     []string `json:"bg_gradients" form:"bg_gradients"`
	IsSelected      int32    `json:"is_selected" form:"is_selected"`
	SectionTitle    string   `json:"section_title" form:"section_title"`         //表单上的分组标题
	FoldText        string   `json:"fold_text" form:"fold_text"`                 //折叠文案
	IsFold          int32    `json:"is_fold" form:"is_fold"`                     //是否折叠
	ClickNeedExpand int32    `json:"click_need_expand" form:"click_need_expand"` //点击后是否展开
}

func (x *CategoryData) GetCategoryId() (r int32) {
	if x != nil {
		return x.CategoryId
	}
	return r
}

func (x *CategoryData) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *CategoryData) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *CategoryData) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *CategoryData) GetBgGradients() (r []string) {
	if x != nil {
		return x.BgGradients
	}
	return r
}

func (x *CategoryData) GetIsSelected() (r int32) {
	if x != nil {
		return x.IsSelected
	}
	return r
}

func (x *CategoryData) GetSectionTitle() (r string) {
	if x != nil {
		return x.SectionTitle
	}
	return r
}

func (x *CategoryData) GetFoldText() (r string) {
	if x != nil {
		return x.FoldText
	}
	return r
}

func (x *CategoryData) GetIsFold() (r int32) {
	if x != nil {
		return x.IsFold
	}
	return r
}

func (x *CategoryData) GetClickNeedExpand() (r int32) {
	if x != nil {
		return x.ClickNeedExpand
	}
	return r
}

func (x *CategoryData) SetCategoryId(v int32) {
	if x != nil {
		x.CategoryId = v
	}
}

func (x *CategoryData) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *CategoryData) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *CategoryData) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *CategoryData) SetBgGradients(v []string) {
	if x != nil {
		x.BgGradients = v
	}
}

func (x *CategoryData) SetIsSelected(v int32) {
	if x != nil {
		x.IsSelected = v
	}
}

func (x *CategoryData) SetSectionTitle(v string) {
	if x != nil {
		x.SectionTitle = v
	}
}

func (x *CategoryData) SetFoldText(v string) {
	if x != nil {
		x.FoldText = v
	}
}

func (x *CategoryData) SetIsFold(v int32) {
	if x != nil {
		x.IsFold = v
	}
}

func (x *CategoryData) SetClickNeedExpand(v int32) {
	if x != nil {
		x.ClickNeedExpand = v
	}
}

func (p *CategoryData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CategoryData(%+v)", *p)
}

type NavigationBar struct {
	Key            string               `json:"key" form:"key"`                                   //操作台组件唯一标识
	Title          *string              `json:"title,omitempty" form:"title"`                     //展示文案 (仅在某些组件有)
	Icon           *string              `json:"icon,omitempty" form:"icon"`                       //图标
	Link           *string              `json:"link,omitempty" form:"link"`                       //跳转链接
	Animation      *Animation           `json:"animation,omitempty" form:"animation"`             //动画 可不传
	HighlightColor *string              `json:"highlight_color,omitempty" form:"highlight_color"` //高亮颜色
	IsHighlight    *int32               `json:"is_highlight,omitempty" form:"is_highlight"`       //是否高亮
	Params         *NavigationBarParams `json:"params,omitempty" form:"params"`                   //点击携带参数
	Popup          map[string]string    `json:"popup,omitempty" form:"popup"`                     //点击弹窗
}

func (x *NavigationBar) GetKey() (r string) {
	if x != nil {
		return x.Key
	}
	return r
}

func (x *NavigationBar) GetTitle() (r string) {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return r
}

func (x *NavigationBar) GetIcon() (r string) {
	if x != nil && x.Icon != nil {
		return *x.Icon
	}
	return r
}

func (x *NavigationBar) GetLink() (r string) {
	if x != nil && x.Link != nil {
		return *x.Link
	}
	return r
}

func (x *NavigationBar) GetAnimation() (r *Animation) {
	if x != nil {
		return x.Animation
	}
	return r
}

func (x *NavigationBar) GetHighlightColor() (r string) {
	if x != nil && x.HighlightColor != nil {
		return *x.HighlightColor
	}
	return r
}

func (x *NavigationBar) GetIsHighlight() (r int32) {
	if x != nil && x.IsHighlight != nil {
		return *x.IsHighlight
	}
	return r
}

func (x *NavigationBar) GetParams() (r *NavigationBarParams) {
	if x != nil {
		return x.Params
	}
	return r
}

func (x *NavigationBar) GetPopup() (r map[string]string) {
	if x != nil {
		return x.Popup
	}
	return r
}

func (x *NavigationBar) SetKey(v string) {
	if x != nil {
		x.Key = v
	}
}

func (x *NavigationBar) SetTitle(v string) {
	if x != nil {
		x.Title = &v
	}
}

func (x *NavigationBar) SetIcon(v string) {
	if x != nil {
		x.Icon = &v
	}
}

func (x *NavigationBar) SetLink(v string) {
	if x != nil {
		x.Link = &v
	}
}

func (x *NavigationBar) SetAnimation(v *Animation) {
	if x != nil {
		x.Animation = v
	}
}

func (x *NavigationBar) SetHighlightColor(v string) {
	if x != nil {
		x.HighlightColor = &v
	}
}

func (x *NavigationBar) SetIsHighlight(v int32) {
	if x != nil {
		x.IsHighlight = &v
	}
}

func (x *NavigationBar) SetParams(v *NavigationBarParams) {
	if x != nil {
		x.Params = v
	}
}

func (x *NavigationBar) SetPopup(v map[string]string) {
	if x != nil {
		x.Popup = v
	}
}

func (p *NavigationBar) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NavigationBar(%+v)", *p)
}

type NavigationBarParams struct {
	TabId    *string `json:"tab_id,omitempty" form:"tab_id"`
	PageType *int32  `json:"page_type,omitempty" form:"page_type"`
}

func (x *NavigationBarParams) GetTabId() (r string) {
	if x != nil && x.TabId != nil {
		return *x.TabId
	}
	return r
}

func (x *NavigationBarParams) GetPageType() (r int32) {
	if x != nil && x.PageType != nil {
		return *x.PageType
	}
	return r
}

func (x *NavigationBarParams) SetTabId(v string) {
	if x != nil {
		x.TabId = &v
	}
}

func (x *NavigationBarParams) SetPageType(v int32) {
	if x != nil {
		x.PageType = &v
	}
}

func (p *NavigationBarParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NavigationBarParams(%+v)", *p)
}

type MoreToastTipData struct {
	TextColor   string `json:"text_color" form:"text_color"`
	CarIcon     string `json:"car_icon" form:"car_icon"`
	BgColor     string `json:"bg_color" form:"bg_color"`
	BorderColor string `json:"border_color" form:"border_color"`
	Text        string `json:"text" form:"text"`
	ArrowIcon   string `json:"arrow_icon" form:"arrow_icon"`
}

func (x *MoreToastTipData) GetTextColor() (r string) {
	if x != nil {
		return x.TextColor
	}
	return r
}

func (x *MoreToastTipData) GetCarIcon() (r string) {
	if x != nil {
		return x.CarIcon
	}
	return r
}

func (x *MoreToastTipData) GetBgColor() (r string) {
	if x != nil {
		return x.BgColor
	}
	return r
}

func (x *MoreToastTipData) GetBorderColor() (r string) {
	if x != nil {
		return x.BorderColor
	}
	return r
}

func (x *MoreToastTipData) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *MoreToastTipData) GetArrowIcon() (r string) {
	if x != nil {
		return x.ArrowIcon
	}
	return r
}

func (x *MoreToastTipData) SetTextColor(v string) {
	if x != nil {
		x.TextColor = v
	}
}

func (x *MoreToastTipData) SetCarIcon(v string) {
	if x != nil {
		x.CarIcon = v
	}
}

func (x *MoreToastTipData) SetBgColor(v string) {
	if x != nil {
		x.BgColor = v
	}
}

func (x *MoreToastTipData) SetBorderColor(v string) {
	if x != nil {
		x.BorderColor = v
	}
}

func (x *MoreToastTipData) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (x *MoreToastTipData) SetArrowIcon(v string) {
	if x != nil {
		x.ArrowIcon = v
	}
}

func (p *MoreToastTipData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MoreToastTipData(%+v)", *p)
}

type AdditionalServiceData struct {
	PageTitle    string               `json:"page_title" form:"page_title"`
	Title        string               `json:"title" form:"title"`
	TipsUrl      string               `json:"tips_url" form:"tips_url"`
	SubTitleList []string             `json:"sub_title_list" form:"sub_title_list"`
	ServiceList  []*AdditionalService `json:"service_list" form:"service_list"`
	GuideText    string               `json:"guide_text" form:"guide_text"`
	GuideTimes   int32                `json:"guide_times" form:"guide_times"`
	Version      int32                `json:"version" form:"version"`
}

func (x *AdditionalServiceData) GetPageTitle() (r string) {
	if x != nil {
		return x.PageTitle
	}
	return r
}

func (x *AdditionalServiceData) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *AdditionalServiceData) GetTipsUrl() (r string) {
	if x != nil {
		return x.TipsUrl
	}
	return r
}

func (x *AdditionalServiceData) GetSubTitleList() (r []string) {
	if x != nil {
		return x.SubTitleList
	}
	return r
}

func (x *AdditionalServiceData) GetServiceList() (r []*AdditionalService) {
	if x != nil {
		return x.ServiceList
	}
	return r
}

func (x *AdditionalServiceData) GetGuideText() (r string) {
	if x != nil {
		return x.GuideText
	}
	return r
}

func (x *AdditionalServiceData) GetGuideTimes() (r int32) {
	if x != nil {
		return x.GuideTimes
	}
	return r
}

func (x *AdditionalServiceData) GetVersion() (r int32) {
	if x != nil {
		return x.Version
	}
	return r
}

func (x *AdditionalServiceData) SetPageTitle(v string) {
	if x != nil {
		x.PageTitle = v
	}
}

func (x *AdditionalServiceData) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *AdditionalServiceData) SetTipsUrl(v string) {
	if x != nil {
		x.TipsUrl = v
	}
}

func (x *AdditionalServiceData) SetSubTitleList(v []string) {
	if x != nil {
		x.SubTitleList = v
	}
}

func (x *AdditionalServiceData) SetServiceList(v []*AdditionalService) {
	if x != nil {
		x.ServiceList = v
	}
}

func (x *AdditionalServiceData) SetGuideText(v string) {
	if x != nil {
		x.GuideText = v
	}
}

func (x *AdditionalServiceData) SetGuideTimes(v int32) {
	if x != nil {
		x.GuideTimes = v
	}
}

func (x *AdditionalServiceData) SetVersion(v int32) {
	if x != nil {
		x.Version = v
	}
}

func (p *AdditionalServiceData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdditionalServiceData(%+v)", *p)
}

type AdditionalService struct {
	Id     int32  `json:"id" form:"id"`         //服务id
	Text   string `json:"text" form:"text"`     //需要携带宠物
	Icon   string `json:"icon" form:"icon"`     //需要携带宠物
	Select int32  `json:"select" form:"select"` //是否勾选 0未勾选 1:已勾选
	Detail string `json:"detail" form:"detail"` //是否勾选 0未勾选 1:已勾选
}

func (x *AdditionalService) GetId() (r int32) {
	if x != nil {
		return x.Id
	}
	return r
}

func (x *AdditionalService) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *AdditionalService) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *AdditionalService) GetSelect() (r int32) {
	if x != nil {
		return x.Select
	}
	return r
}

func (x *AdditionalService) GetDetail() (r string) {
	if x != nil {
		return x.Detail
	}
	return r
}

func (x *AdditionalService) SetId(v int32) {
	if x != nil {
		x.Id = v
	}
}

func (x *AdditionalService) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (x *AdditionalService) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *AdditionalService) SetSelect(v int32) {
	if x != nil {
		x.Select = v
	}
}

func (x *AdditionalService) SetDetail(v string) {
	if x != nil {
		x.Detail = v
	}
}

func (p *AdditionalService) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdditionalService(%+v)", *p)
}

type Animation struct {
	Icon  *string `json:"icon,omitempty" form:"icon"`   //动画图标
	Title *string `json:"title,omitempty" form:"title"` //动画文案
}

func (x *Animation) GetIcon() (r string) {
	if x != nil && x.Icon != nil {
		return *x.Icon
	}
	return r
}

func (x *Animation) GetTitle() (r string) {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return r
}

func (x *Animation) SetIcon(v string) {
	if x != nil {
		x.Icon = &v
	}
}

func (x *Animation) SetTitle(v string) {
	if x != nil {
		x.Title = &v
	}
}

func (p *Animation) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Animation(%+v)", *p)
}
