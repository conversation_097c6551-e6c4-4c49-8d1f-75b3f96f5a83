// Code generated by http-gen compiler. DO NOT EDIT.
// source: sfc_estimate.idl
// argument: --client_style=protoc

package proto

import (
	"fmt"
	"git.xiaojukeji.com/nuwa/golibs/json"
	"reflect"
)

var _ = fmt.Printf

func init() {
	json.Pretouch(reflect.TypeOf((*SFCEstimateRequest)(nil)))
	json.Pretouch(reflect.TypeOf((*ExtraParams)(nil)))
	json.Pretouch(reflect.TypeOf((*SFCEstimateData)(nil)))
	json.Pretouch(reflect.TypeOf((*AdditionalEstimateData)(nil)))
	json.Pretouch(reflect.TypeOf((*AdditionalInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*CouponEstimateDcmpInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*CouponEstimateInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*SFCPriceRollInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*PriceInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*SFCExtraMap)(nil)))
	json.Pretouch(reflect.TypeOf((*PassengerSeatInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*InsuranceInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*ExtraTab)(nil)))
	json.Pretouch(reflect.TypeOf((*MenuInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*SfcBubbleInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*SfcCasperInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*SfcTollCasperInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*SfcTollFeeInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*SFCPreCancelButtonInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*SFCPreCancelOmegaParam)(nil)))
	json.Pretouch(reflect.TypeOf((*SFCPreCancelDetail)(nil)))
	json.Pretouch(reflect.TypeOf((*SFCEstimateResponse)(nil)))
	json.Pretouch(reflect.TypeOf((*SfcOmegaParams)(nil)))
	json.Pretouch(reflect.TypeOf((*SfcSubmitButton)(nil)))
	json.Pretouch(reflect.TypeOf((*SfcBottomInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*AnimationInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*SFCMultiEstimateResponse)(nil)))
	json.Pretouch(reflect.TypeOf((*SFCEstimateRollInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*SFCEstimateDcmp)(nil)))
	json.Pretouch(reflect.TypeOf((*SFCDcmpPriceInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*SFCDcmpSeatInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*SFCOperationInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*BannerInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*CasperContent)(nil)))
	json.Pretouch(reflect.TypeOf((*BannerInfoData)(nil)))
	json.Pretouch(reflect.TypeOf((*OmegaCk)(nil)))
	json.Pretouch(reflect.TypeOf((*OmegaCkParams)(nil)))
	json.Pretouch(reflect.TypeOf((*PassengerNoticeBar)(nil)))
	json.Pretouch(reflect.TypeOf((*LogData)(nil)))
	json.Pretouch(reflect.TypeOf((*SFCModifyEstimateRequest)(nil)))
	json.Pretouch(reflect.TypeOf((*SFCModifyEstimateResponse)(nil)))
	json.Pretouch(reflect.TypeOf((*SFCModifyEstimateData)(nil)))
	json.Pretouch(reflect.TypeOf((*SFCSimpleEstimateReq)(nil)))
	json.Pretouch(reflect.TypeOf((*SFCSimpleEstimatePriceInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*SFCSimpleEstimateData)(nil)))
	json.Pretouch(reflect.TypeOf((*SFCSimpleEstimateResponse)(nil)))
}

type SFCEstimateRequest struct {
	Token               string   `json:"token" form:"token"`
	AccessKeyId         int32    `json:"access_key_id" form:"access_key_id"`
	AppVersion          string   `json:"app_version" form:"app_version"`
	Lat                 *float64 `json:"lat,omitempty" form:"lat"`
	Lng                 *float64 `json:"lng,omitempty" form:"lng"`
	MapType             string   `json:"map_type" form:"map_type"`
	OrderType           int32    `json:"order_type" form:"order_type"`
	FromLat             float64  `json:"from_lat" form:"from_lat"`
	FromLng             float64  `json:"from_lng" form:"from_lng"`
	FromPoiId           string   `json:"from_poi_id" form:"from_poi_id"`
	FromPoiType         *string  `json:"from_poi_type,omitempty" form:"from_poi_type"`
	FromAddress         *string  `json:"from_address,omitempty" form:"from_address"`
	FromName            string   `json:"from_name" form:"from_name"`
	ChooseFSearchid     *string  `json:"choose_f_searchid,omitempty" form:"choose_f_searchid"`
	ToLat               float64  `json:"to_lat" form:"to_lat"`
	ToLng               float64  `json:"to_lng" form:"to_lng"`
	ToPoiId             string   `json:"to_poi_id" form:"to_poi_id"`
	ToPoiType           *string  `json:"to_poi_type,omitempty" form:"to_poi_type"`
	ToAddress           *string  `json:"to_address,omitempty" form:"to_address"`
	ToName              string   `json:"to_name" form:"to_name"`
	ChooseTSearchid     *string  `json:"choose_t_searchid,omitempty" form:"choose_t_searchid"`
	MultiRequireProduct *string  `json:"multi_require_product,omitempty" form:"multi_require_product"` //用户勾选状态
	DepartureTime       *int64   `json:"departure_time,omitempty" form:"departure_time"`               //出发时间戳
	DepartureRange      []int64  `json:"departure_range,omitempty" form:"departure_range"`             //出发时间段
	PassengerNums       *int32   `json:"passenger_nums,omitempty" form:"passenger_nums"`               //乘车人数
	FromPageId          *string  `json:"from_page_id,omitempty" form:"from_page_id"`                   //来源的page_id
	ExtraParams         *string  `json:"extra_params,omitempty" form:"extra_params"`                   //透传参数
	FromArea            int32    `json:"from_area" form:"from_area"`                                   //透传参数
	ToArea              int32    `json:"to_area" form:"to_area"`
	Channel             int32    `json:"channel" form:"channel"`
	Wsgenv              *string  `json:"wsgenv,omitempty" form:"wsgenv"`
	MenuId              string   `json:"menu_id" form:"menu_id"`
	SelectRouteId       *string  `json:"select_route_id,omitempty" form:"select_route_id"`
	FromType            string   `json:"from_type" form:"from_type"`
	PreOid              string   `json:"pre_oid" form:"pre_oid"`
	TollSelect          string   `json:"toll_select" form:"toll_select"` //乘客高速费选择 "toll_no_select"-未选择高速费   "toll_negotiate"-协商高速费  "toll_full"-愿付全部高速费  "toll_none"-不付高速费
}

func (x *SFCEstimateRequest) GetToken() (r string) {
	if x != nil {
		return x.Token
	}
	return r
}

func (x *SFCEstimateRequest) GetAccessKeyId() (r int32) {
	if x != nil {
		return x.AccessKeyId
	}
	return r
}

func (x *SFCEstimateRequest) GetAppVersion() (r string) {
	if x != nil {
		return x.AppVersion
	}
	return r
}

func (x *SFCEstimateRequest) GetLat() (r float64) {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return r
}

func (x *SFCEstimateRequest) GetLng() (r float64) {
	if x != nil && x.Lng != nil {
		return *x.Lng
	}
	return r
}

func (x *SFCEstimateRequest) GetMapType() (r string) {
	if x != nil {
		return x.MapType
	}
	return r
}

func (x *SFCEstimateRequest) GetOrderType() (r int32) {
	if x != nil {
		return x.OrderType
	}
	return r
}

func (x *SFCEstimateRequest) GetFromLat() (r float64) {
	if x != nil {
		return x.FromLat
	}
	return r
}

func (x *SFCEstimateRequest) GetFromLng() (r float64) {
	if x != nil {
		return x.FromLng
	}
	return r
}

func (x *SFCEstimateRequest) GetFromPoiId() (r string) {
	if x != nil {
		return x.FromPoiId
	}
	return r
}

func (x *SFCEstimateRequest) GetFromPoiType() (r string) {
	if x != nil && x.FromPoiType != nil {
		return *x.FromPoiType
	}
	return r
}

func (x *SFCEstimateRequest) GetFromAddress() (r string) {
	if x != nil && x.FromAddress != nil {
		return *x.FromAddress
	}
	return r
}

func (x *SFCEstimateRequest) GetFromName() (r string) {
	if x != nil {
		return x.FromName
	}
	return r
}

func (x *SFCEstimateRequest) GetChooseFSearchid() (r string) {
	if x != nil && x.ChooseFSearchid != nil {
		return *x.ChooseFSearchid
	}
	return r
}

func (x *SFCEstimateRequest) GetToLat() (r float64) {
	if x != nil {
		return x.ToLat
	}
	return r
}

func (x *SFCEstimateRequest) GetToLng() (r float64) {
	if x != nil {
		return x.ToLng
	}
	return r
}

func (x *SFCEstimateRequest) GetToPoiId() (r string) {
	if x != nil {
		return x.ToPoiId
	}
	return r
}

func (x *SFCEstimateRequest) GetToPoiType() (r string) {
	if x != nil && x.ToPoiType != nil {
		return *x.ToPoiType
	}
	return r
}

func (x *SFCEstimateRequest) GetToAddress() (r string) {
	if x != nil && x.ToAddress != nil {
		return *x.ToAddress
	}
	return r
}

func (x *SFCEstimateRequest) GetToName() (r string) {
	if x != nil {
		return x.ToName
	}
	return r
}

func (x *SFCEstimateRequest) GetChooseTSearchid() (r string) {
	if x != nil && x.ChooseTSearchid != nil {
		return *x.ChooseTSearchid
	}
	return r
}

func (x *SFCEstimateRequest) GetMultiRequireProduct() (r string) {
	if x != nil && x.MultiRequireProduct != nil {
		return *x.MultiRequireProduct
	}
	return r
}

func (x *SFCEstimateRequest) GetDepartureTime() (r int64) {
	if x != nil && x.DepartureTime != nil {
		return *x.DepartureTime
	}
	return r
}

func (x *SFCEstimateRequest) GetDepartureRange() (r []int64) {
	if x != nil {
		return x.DepartureRange
	}
	return r
}

func (x *SFCEstimateRequest) GetPassengerNums() (r int32) {
	if x != nil && x.PassengerNums != nil {
		return *x.PassengerNums
	}
	return r
}

func (x *SFCEstimateRequest) GetFromPageId() (r string) {
	if x != nil && x.FromPageId != nil {
		return *x.FromPageId
	}
	return r
}

func (x *SFCEstimateRequest) GetExtraParams() (r string) {
	if x != nil && x.ExtraParams != nil {
		return *x.ExtraParams
	}
	return r
}

func (x *SFCEstimateRequest) GetFromArea() (r int32) {
	if x != nil {
		return x.FromArea
	}
	return r
}

func (x *SFCEstimateRequest) GetToArea() (r int32) {
	if x != nil {
		return x.ToArea
	}
	return r
}

func (x *SFCEstimateRequest) GetChannel() (r int32) {
	if x != nil {
		return x.Channel
	}
	return r
}

func (x *SFCEstimateRequest) GetWsgenv() (r string) {
	if x != nil && x.Wsgenv != nil {
		return *x.Wsgenv
	}
	return r
}

func (x *SFCEstimateRequest) GetMenuId() (r string) {
	if x != nil {
		return x.MenuId
	}
	return r
}

func (x *SFCEstimateRequest) GetSelectRouteId() (r string) {
	if x != nil && x.SelectRouteId != nil {
		return *x.SelectRouteId
	}
	return r
}

func (x *SFCEstimateRequest) GetFromType() (r string) {
	if x != nil {
		return x.FromType
	}
	return r
}

func (x *SFCEstimateRequest) GetPreOid() (r string) {
	if x != nil {
		return x.PreOid
	}
	return r
}

func (x *SFCEstimateRequest) GetTollSelect() (r string) {
	if x != nil {
		return x.TollSelect
	}
	return r
}

func (x *SFCEstimateRequest) SetToken(v string) {
	if x != nil {
		x.Token = v
	}
}

func (x *SFCEstimateRequest) SetAccessKeyId(v int32) {
	if x != nil {
		x.AccessKeyId = v
	}
}

func (x *SFCEstimateRequest) SetAppVersion(v string) {
	if x != nil {
		x.AppVersion = v
	}
}

func (x *SFCEstimateRequest) SetLat(v float64) {
	if x != nil {
		x.Lat = &v
	}
}

func (x *SFCEstimateRequest) SetLng(v float64) {
	if x != nil {
		x.Lng = &v
	}
}

func (x *SFCEstimateRequest) SetMapType(v string) {
	if x != nil {
		x.MapType = v
	}
}

func (x *SFCEstimateRequest) SetOrderType(v int32) {
	if x != nil {
		x.OrderType = v
	}
}

func (x *SFCEstimateRequest) SetFromLat(v float64) {
	if x != nil {
		x.FromLat = v
	}
}

func (x *SFCEstimateRequest) SetFromLng(v float64) {
	if x != nil {
		x.FromLng = v
	}
}

func (x *SFCEstimateRequest) SetFromPoiId(v string) {
	if x != nil {
		x.FromPoiId = v
	}
}

func (x *SFCEstimateRequest) SetFromPoiType(v string) {
	if x != nil {
		x.FromPoiType = &v
	}
}

func (x *SFCEstimateRequest) SetFromAddress(v string) {
	if x != nil {
		x.FromAddress = &v
	}
}

func (x *SFCEstimateRequest) SetFromName(v string) {
	if x != nil {
		x.FromName = v
	}
}

func (x *SFCEstimateRequest) SetChooseFSearchid(v string) {
	if x != nil {
		x.ChooseFSearchid = &v
	}
}

func (x *SFCEstimateRequest) SetToLat(v float64) {
	if x != nil {
		x.ToLat = v
	}
}

func (x *SFCEstimateRequest) SetToLng(v float64) {
	if x != nil {
		x.ToLng = v
	}
}

func (x *SFCEstimateRequest) SetToPoiId(v string) {
	if x != nil {
		x.ToPoiId = v
	}
}

func (x *SFCEstimateRequest) SetToPoiType(v string) {
	if x != nil {
		x.ToPoiType = &v
	}
}

func (x *SFCEstimateRequest) SetToAddress(v string) {
	if x != nil {
		x.ToAddress = &v
	}
}

func (x *SFCEstimateRequest) SetToName(v string) {
	if x != nil {
		x.ToName = v
	}
}

func (x *SFCEstimateRequest) SetChooseTSearchid(v string) {
	if x != nil {
		x.ChooseTSearchid = &v
	}
}

func (x *SFCEstimateRequest) SetMultiRequireProduct(v string) {
	if x != nil {
		x.MultiRequireProduct = &v
	}
}

func (x *SFCEstimateRequest) SetDepartureTime(v int64) {
	if x != nil {
		x.DepartureTime = &v
	}
}

func (x *SFCEstimateRequest) SetDepartureRange(v []int64) {
	if x != nil {
		x.DepartureRange = v
	}
}

func (x *SFCEstimateRequest) SetPassengerNums(v int32) {
	if x != nil {
		x.PassengerNums = &v
	}
}

func (x *SFCEstimateRequest) SetFromPageId(v string) {
	if x != nil {
		x.FromPageId = &v
	}
}

func (x *SFCEstimateRequest) SetExtraParams(v string) {
	if x != nil {
		x.ExtraParams = &v
	}
}

func (x *SFCEstimateRequest) SetFromArea(v int32) {
	if x != nil {
		x.FromArea = v
	}
}

func (x *SFCEstimateRequest) SetToArea(v int32) {
	if x != nil {
		x.ToArea = v
	}
}

func (x *SFCEstimateRequest) SetChannel(v int32) {
	if x != nil {
		x.Channel = v
	}
}

func (x *SFCEstimateRequest) SetWsgenv(v string) {
	if x != nil {
		x.Wsgenv = &v
	}
}

func (x *SFCEstimateRequest) SetMenuId(v string) {
	if x != nil {
		x.MenuId = v
	}
}

func (x *SFCEstimateRequest) SetSelectRouteId(v string) {
	if x != nil {
		x.SelectRouteId = &v
	}
}

func (x *SFCEstimateRequest) SetFromType(v string) {
	if x != nil {
		x.FromType = v
	}
}

func (x *SFCEstimateRequest) SetPreOid(v string) {
	if x != nil {
		x.PreOid = v
	}
}

func (x *SFCEstimateRequest) SetTollSelect(v string) {
	if x != nil {
		x.TollSelect = v
	}
}

func (p *SFCEstimateRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCEstimateRequest(%+v)", *p)
}

func (p *SFCEstimateRequest) Validate() error {
	if !(p.GetOrderType() > 0) {
		return fmt.Errorf("field %s of struct %s validate failed", "OrderType", "SFCEstimateRequest")
	}
	if !(len(p.GetFromPoiId()) > 0) {
		return fmt.Errorf("field %s of struct %s validate failed", "FromPoiId", "SFCEstimateRequest")
	}
	if !(len(p.GetFromName()) > 0) {
		return fmt.Errorf("field %s of struct %s validate failed", "FromName", "SFCEstimateRequest")
	}
	if !(len(p.GetToPoiId()) > 0) {
		return fmt.Errorf("field %s of struct %s validate failed", "ToPoiId", "SFCEstimateRequest")
	}
	if !(len(p.GetToName()) > 0) {
		return fmt.Errorf("field %s of struct %s validate failed", "ToName", "SFCEstimateRequest")
	}
	return nil
}

// 透传参数
type ExtraParams struct {
	WycBubbleId *string `json:"wyc_bubble_id,omitempty" form:"wyc_bubble_id"`
}

func (x *ExtraParams) GetWycBubbleId() (r string) {
	if x != nil && x.WycBubbleId != nil {
		return *x.WycBubbleId
	}
	return r
}

func (x *ExtraParams) SetWycBubbleId(v string) {
	if x != nil {
		x.WycBubbleId = &v
	}
}

func (p *ExtraParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExtraParams(%+v)", *p)
}

type SFCEstimateData struct {
	EstimateId string                    `json:"estimate_id" form:"estimate_id"`
	CarIcon    string                    `json:"car_icon" form:"car_icon"`
	CarTitle   string                    `json:"car_title" form:"car_title"`
	SubTitle   string                    `json:"sub_title" form:"sub_title"`
	Bubble     *string                   `json:"bubble,omitempty" form:"bubble"`
	PriceInfo  *PriceInfo                `json:"price_info,omitempty" form:"price_info"`
	IsSelected int32                     `json:"is_selected" form:"is_selected"`
	ExtraMap   *SFCExtraMap              `json:"extra_map,omitempty" form:"extra_map"`     //发单参数
	CouponList []*CouponEstimateInfo     `json:"coupon_list,omitempty" form:"coupon_list"` //发单参数
	LinkList   []*AdditionalEstimateData `json:"link_list,omitempty" form:"link_list"`
}

func (x *SFCEstimateData) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *SFCEstimateData) GetCarIcon() (r string) {
	if x != nil {
		return x.CarIcon
	}
	return r
}

func (x *SFCEstimateData) GetCarTitle() (r string) {
	if x != nil {
		return x.CarTitle
	}
	return r
}

func (x *SFCEstimateData) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *SFCEstimateData) GetBubble() (r string) {
	if x != nil && x.Bubble != nil {
		return *x.Bubble
	}
	return r
}

func (x *SFCEstimateData) GetPriceInfo() (r *PriceInfo) {
	if x != nil {
		return x.PriceInfo
	}
	return r
}

func (x *SFCEstimateData) GetIsSelected() (r int32) {
	if x != nil {
		return x.IsSelected
	}
	return r
}

func (x *SFCEstimateData) GetExtraMap() (r *SFCExtraMap) {
	if x != nil {
		return x.ExtraMap
	}
	return r
}

func (x *SFCEstimateData) GetCouponList() (r []*CouponEstimateInfo) {
	if x != nil {
		return x.CouponList
	}
	return r
}

func (x *SFCEstimateData) GetLinkList() (r []*AdditionalEstimateData) {
	if x != nil {
		return x.LinkList
	}
	return r
}

func (x *SFCEstimateData) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *SFCEstimateData) SetCarIcon(v string) {
	if x != nil {
		x.CarIcon = v
	}
}

func (x *SFCEstimateData) SetCarTitle(v string) {
	if x != nil {
		x.CarTitle = v
	}
}

func (x *SFCEstimateData) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *SFCEstimateData) SetBubble(v string) {
	if x != nil {
		x.Bubble = &v
	}
}

func (x *SFCEstimateData) SetPriceInfo(v *PriceInfo) {
	if x != nil {
		x.PriceInfo = v
	}
}

func (x *SFCEstimateData) SetIsSelected(v int32) {
	if x != nil {
		x.IsSelected = v
	}
}

func (x *SFCEstimateData) SetExtraMap(v *SFCExtraMap) {
	if x != nil {
		x.ExtraMap = v
	}
}

func (x *SFCEstimateData) SetCouponList(v []*CouponEstimateInfo) {
	if x != nil {
		x.CouponList = v
	}
}

func (x *SFCEstimateData) SetLinkList(v []*AdditionalEstimateData) {
	if x != nil {
		x.LinkList = v
	}
}

func (p *SFCEstimateData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCEstimateData(%+v)", *p)
}

type AdditionalEstimateData struct {
	EstimateId string       `json:"estimate_id" form:"estimate_id"`
	CarIcon    string       `json:"car_icon" form:"car_icon"`
	CarTitle   string       `json:"car_title" form:"car_title"`
	Subtitle   string       `json:"subtitle" form:"subtitle"`
	IsSelected int32        `json:"is_selected" form:"is_selected"`
	PriceInfo  *PriceInfo   `json:"price_info,omitempty" form:"price_info"`
	ExtraMap   *SFCExtraMap `json:"extra_map,omitempty" form:"extra_map"` //发单参数
}

func (x *AdditionalEstimateData) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *AdditionalEstimateData) GetCarIcon() (r string) {
	if x != nil {
		return x.CarIcon
	}
	return r
}

func (x *AdditionalEstimateData) GetCarTitle() (r string) {
	if x != nil {
		return x.CarTitle
	}
	return r
}

func (x *AdditionalEstimateData) GetSubtitle() (r string) {
	if x != nil {
		return x.Subtitle
	}
	return r
}

func (x *AdditionalEstimateData) GetIsSelected() (r int32) {
	if x != nil {
		return x.IsSelected
	}
	return r
}

func (x *AdditionalEstimateData) GetPriceInfo() (r *PriceInfo) {
	if x != nil {
		return x.PriceInfo
	}
	return r
}

func (x *AdditionalEstimateData) GetExtraMap() (r *SFCExtraMap) {
	if x != nil {
		return x.ExtraMap
	}
	return r
}

func (x *AdditionalEstimateData) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *AdditionalEstimateData) SetCarIcon(v string) {
	if x != nil {
		x.CarIcon = v
	}
}

func (x *AdditionalEstimateData) SetCarTitle(v string) {
	if x != nil {
		x.CarTitle = v
	}
}

func (x *AdditionalEstimateData) SetSubtitle(v string) {
	if x != nil {
		x.Subtitle = v
	}
}

func (x *AdditionalEstimateData) SetIsSelected(v int32) {
	if x != nil {
		x.IsSelected = v
	}
}

func (x *AdditionalEstimateData) SetPriceInfo(v *PriceInfo) {
	if x != nil {
		x.PriceInfo = v
	}
}

func (x *AdditionalEstimateData) SetExtraMap(v *SFCExtraMap) {
	if x != nil {
		x.ExtraMap = v
	}
}

func (p *AdditionalEstimateData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdditionalEstimateData(%+v)", *p)
}

type AdditionalInfo struct {
	ProductList []int32 `json:"product_list,omitempty" form:"product_list"`
	Title       string  `json:"title" form:"title"`
	LegacyText  string  `json:"legacy_text" form:"legacy_text"`
	LegacyUrl   string  `json:"legacy_url" form:"legacy_url"`
	LegacyType  string  `json:"legacy_type" form:"legacy_type"`
}

func (x *AdditionalInfo) GetProductList() (r []int32) {
	if x != nil {
		return x.ProductList
	}
	return r
}

func (x *AdditionalInfo) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *AdditionalInfo) GetLegacyText() (r string) {
	if x != nil {
		return x.LegacyText
	}
	return r
}

func (x *AdditionalInfo) GetLegacyUrl() (r string) {
	if x != nil {
		return x.LegacyUrl
	}
	return r
}

func (x *AdditionalInfo) GetLegacyType() (r string) {
	if x != nil {
		return x.LegacyType
	}
	return r
}

func (x *AdditionalInfo) SetProductList(v []int32) {
	if x != nil {
		x.ProductList = v
	}
}

func (x *AdditionalInfo) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *AdditionalInfo) SetLegacyText(v string) {
	if x != nil {
		x.LegacyText = v
	}
}

func (x *AdditionalInfo) SetLegacyUrl(v string) {
	if x != nil {
		x.LegacyUrl = v
	}
}

func (x *AdditionalInfo) SetLegacyType(v string) {
	if x != nil {
		x.LegacyType = v
	}
}

func (p *AdditionalInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdditionalInfo(%+v)", *p)
}

type CouponEstimateDcmpInfo struct {
	Title            string   `json:"title" form:"title"`
	Icon             string   `json:"icon" form:"icon"`
	BackgroundColors []string `json:"background_colors" form:"background_colors"`
	Type             string   `json:"type" form:"type"`
	TitleOid         string   `json:"title_oid" form:"title_oid"`
}

func (x *CouponEstimateDcmpInfo) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *CouponEstimateDcmpInfo) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *CouponEstimateDcmpInfo) GetBackgroundColors() (r []string) {
	if x != nil {
		return x.BackgroundColors
	}
	return r
}

func (x *CouponEstimateDcmpInfo) GetType() (r string) {
	if x != nil {
		return x.Type
	}
	return r
}

func (x *CouponEstimateDcmpInfo) GetTitleOid() (r string) {
	if x != nil {
		return x.TitleOid
	}
	return r
}

func (x *CouponEstimateDcmpInfo) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *CouponEstimateDcmpInfo) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *CouponEstimateDcmpInfo) SetBackgroundColors(v []string) {
	if x != nil {
		x.BackgroundColors = v
	}
}

func (x *CouponEstimateDcmpInfo) SetType(v string) {
	if x != nil {
		x.Type = v
	}
}

func (x *CouponEstimateDcmpInfo) SetTitleOid(v string) {
	if x != nil {
		x.TitleOid = v
	}
}

func (p *CouponEstimateDcmpInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CouponEstimateDcmpInfo(%+v)", *p)
}

type CouponEstimateInfo struct {
	Title            string   `json:"title" form:"title"`
	Icon             string   `json:"icon" form:"icon"`
	BackgroundColors []string `json:"background_colors" form:"background_colors"`
	Type             string   `json:"type" form:"type"`
	BorderColor      *string  `json:"border_color,omitempty" form:"border_color"`
	TitleColor       *string  `json:"title_color,omitempty" form:"title_color"`
}

func (x *CouponEstimateInfo) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *CouponEstimateInfo) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *CouponEstimateInfo) GetBackgroundColors() (r []string) {
	if x != nil {
		return x.BackgroundColors
	}
	return r
}

func (x *CouponEstimateInfo) GetType() (r string) {
	if x != nil {
		return x.Type
	}
	return r
}

func (x *CouponEstimateInfo) GetBorderColor() (r string) {
	if x != nil && x.BorderColor != nil {
		return *x.BorderColor
	}
	return r
}

func (x *CouponEstimateInfo) GetTitleColor() (r string) {
	if x != nil && x.TitleColor != nil {
		return *x.TitleColor
	}
	return r
}

func (x *CouponEstimateInfo) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *CouponEstimateInfo) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *CouponEstimateInfo) SetBackgroundColors(v []string) {
	if x != nil {
		x.BackgroundColors = v
	}
}

func (x *CouponEstimateInfo) SetType(v string) {
	if x != nil {
		x.Type = v
	}
}

func (x *CouponEstimateInfo) SetBorderColor(v string) {
	if x != nil {
		x.BorderColor = &v
	}
}

func (x *CouponEstimateInfo) SetTitleColor(v string) {
	if x != nil {
		x.TitleColor = &v
	}
}

func (p *CouponEstimateInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CouponEstimateInfo(%+v)", *p)
}

type SFCPriceRollInfo struct {
	TextColor string `json:"text_color" form:"text_color"`
	TitlePre  string `json:"title_pre" form:"title_pre"`
	TitleEnd  string `json:"title_end" form:"title_end"`
	PriceFrom string `json:"price_from" form:"price_from"`
	PriceTo   string `json:"price_to" form:"price_to"`
}

func (x *SFCPriceRollInfo) GetTextColor() (r string) {
	if x != nil {
		return x.TextColor
	}
	return r
}

func (x *SFCPriceRollInfo) GetTitlePre() (r string) {
	if x != nil {
		return x.TitlePre
	}
	return r
}

func (x *SFCPriceRollInfo) GetTitleEnd() (r string) {
	if x != nil {
		return x.TitleEnd
	}
	return r
}

func (x *SFCPriceRollInfo) GetPriceFrom() (r string) {
	if x != nil {
		return x.PriceFrom
	}
	return r
}

func (x *SFCPriceRollInfo) GetPriceTo() (r string) {
	if x != nil {
		return x.PriceTo
	}
	return r
}

func (x *SFCPriceRollInfo) SetTextColor(v string) {
	if x != nil {
		x.TextColor = v
	}
}

func (x *SFCPriceRollInfo) SetTitlePre(v string) {
	if x != nil {
		x.TitlePre = v
	}
}

func (x *SFCPriceRollInfo) SetTitleEnd(v string) {
	if x != nil {
		x.TitleEnd = v
	}
}

func (x *SFCPriceRollInfo) SetPriceFrom(v string) {
	if x != nil {
		x.PriceFrom = v
	}
}

func (x *SFCPriceRollInfo) SetPriceTo(v string) {
	if x != nil {
		x.PriceTo = v
	}
}

func (p *SFCPriceRollInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCPriceRollInfo(%+v)", *p)
}

type PriceInfo struct {
	Title            string            `json:"title" form:"title"`
	Subtitle         string            `json:"subtitle" form:"subtitle"`
	FareAmount       string            `json:"fare_amount" form:"fare_amount"`
	AwardAmount      string            `json:"award_amount" form:"award_amount"`
	Icon             string            `json:"icon" form:"icon"`
	JumpUrl          string            `json:"jump_url" form:"jump_url"`
	IsRoll           *int32            `json:"is_roll,omitempty" form:"is_roll"`
	TitleRollInfo    *SFCPriceRollInfo `json:"title_roll_info,omitempty" form:"title_roll_info"`
	SubtitleRollInfo *SFCPriceRollInfo `json:"subtitle_roll_info,omitempty" form:"subtitle_roll_info"`
	IsDecimalSmall   *int32            `json:"is_decimal_small,omitempty" form:"is_decimal_small"`
}

func (x *PriceInfo) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *PriceInfo) GetSubtitle() (r string) {
	if x != nil {
		return x.Subtitle
	}
	return r
}

func (x *PriceInfo) GetFareAmount() (r string) {
	if x != nil {
		return x.FareAmount
	}
	return r
}

func (x *PriceInfo) GetAwardAmount() (r string) {
	if x != nil {
		return x.AwardAmount
	}
	return r
}

func (x *PriceInfo) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *PriceInfo) GetJumpUrl() (r string) {
	if x != nil {
		return x.JumpUrl
	}
	return r
}

func (x *PriceInfo) GetIsRoll() (r int32) {
	if x != nil && x.IsRoll != nil {
		return *x.IsRoll
	}
	return r
}

func (x *PriceInfo) GetTitleRollInfo() (r *SFCPriceRollInfo) {
	if x != nil {
		return x.TitleRollInfo
	}
	return r
}

func (x *PriceInfo) GetSubtitleRollInfo() (r *SFCPriceRollInfo) {
	if x != nil {
		return x.SubtitleRollInfo
	}
	return r
}

func (x *PriceInfo) GetIsDecimalSmall() (r int32) {
	if x != nil && x.IsDecimalSmall != nil {
		return *x.IsDecimalSmall
	}
	return r
}

func (x *PriceInfo) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *PriceInfo) SetSubtitle(v string) {
	if x != nil {
		x.Subtitle = v
	}
}

func (x *PriceInfo) SetFareAmount(v string) {
	if x != nil {
		x.FareAmount = v
	}
}

func (x *PriceInfo) SetAwardAmount(v string) {
	if x != nil {
		x.AwardAmount = v
	}
}

func (x *PriceInfo) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *PriceInfo) SetJumpUrl(v string) {
	if x != nil {
		x.JumpUrl = v
	}
}

func (x *PriceInfo) SetIsRoll(v int32) {
	if x != nil {
		x.IsRoll = &v
	}
}

func (x *PriceInfo) SetTitleRollInfo(v *SFCPriceRollInfo) {
	if x != nil {
		x.TitleRollInfo = v
	}
}

func (x *PriceInfo) SetSubtitleRollInfo(v *SFCPriceRollInfo) {
	if x != nil {
		x.SubtitleRollInfo = v
	}
}

func (x *PriceInfo) SetIsDecimalSmall(v int32) {
	if x != nil {
		x.IsDecimalSmall = &v
	}
}

func (p *PriceInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PriceInfo(%+v)", *p)
}

type SFCExtraMap struct {
	ProductCategory          int32 `json:"product_category" form:"product_category"`
	RequireLevel             int32 `json:"require_level" form:"require_level"`
	BusinessId               int32 `json:"business_id" form:"business_id"`
	ProductId                int32 `json:"product_id" form:"product_id"`
	ComboType                int32 `json:"combo_type" form:"combo_type"`
	LevelType                int32 `json:"level_type" form:"level_type"`
	CarpoolType              int32 `json:"carpool_type" form:"carpool_type"`
	CountPriceType           int32 `json:"count_price_type" form:"count_price_type"`
	PayType                  int32 `json:"pay_type" form:"pay_type"`
	TriggerTime              int32 `json:"trigger_time" form:"trigger_time"`
	BubbleTime               int32 `json:"bubble_time" form:"bubble_time"`
	PolymericProductCategory int32 `json:"polymeric_product_category" form:"polymeric_product_category"`
}

func (x *SFCExtraMap) GetProductCategory() (r int32) {
	if x != nil {
		return x.ProductCategory
	}
	return r
}

func (x *SFCExtraMap) GetRequireLevel() (r int32) {
	if x != nil {
		return x.RequireLevel
	}
	return r
}

func (x *SFCExtraMap) GetBusinessId() (r int32) {
	if x != nil {
		return x.BusinessId
	}
	return r
}

func (x *SFCExtraMap) GetProductId() (r int32) {
	if x != nil {
		return x.ProductId
	}
	return r
}

func (x *SFCExtraMap) GetComboType() (r int32) {
	if x != nil {
		return x.ComboType
	}
	return r
}

func (x *SFCExtraMap) GetLevelType() (r int32) {
	if x != nil {
		return x.LevelType
	}
	return r
}

func (x *SFCExtraMap) GetCarpoolType() (r int32) {
	if x != nil {
		return x.CarpoolType
	}
	return r
}

func (x *SFCExtraMap) GetCountPriceType() (r int32) {
	if x != nil {
		return x.CountPriceType
	}
	return r
}

func (x *SFCExtraMap) GetPayType() (r int32) {
	if x != nil {
		return x.PayType
	}
	return r
}

func (x *SFCExtraMap) GetTriggerTime() (r int32) {
	if x != nil {
		return x.TriggerTime
	}
	return r
}

func (x *SFCExtraMap) GetBubbleTime() (r int32) {
	if x != nil {
		return x.BubbleTime
	}
	return r
}

func (x *SFCExtraMap) GetPolymericProductCategory() (r int32) {
	if x != nil {
		return x.PolymericProductCategory
	}
	return r
}

func (x *SFCExtraMap) SetProductCategory(v int32) {
	if x != nil {
		x.ProductCategory = v
	}
}

func (x *SFCExtraMap) SetRequireLevel(v int32) {
	if x != nil {
		x.RequireLevel = v
	}
}

func (x *SFCExtraMap) SetBusinessId(v int32) {
	if x != nil {
		x.BusinessId = v
	}
}

func (x *SFCExtraMap) SetProductId(v int32) {
	if x != nil {
		x.ProductId = v
	}
}

func (x *SFCExtraMap) SetComboType(v int32) {
	if x != nil {
		x.ComboType = v
	}
}

func (x *SFCExtraMap) SetLevelType(v int32) {
	if x != nil {
		x.LevelType = v
	}
}

func (x *SFCExtraMap) SetCarpoolType(v int32) {
	if x != nil {
		x.CarpoolType = v
	}
}

func (x *SFCExtraMap) SetCountPriceType(v int32) {
	if x != nil {
		x.CountPriceType = v
	}
}

func (x *SFCExtraMap) SetPayType(v int32) {
	if x != nil {
		x.PayType = v
	}
}

func (x *SFCExtraMap) SetTriggerTime(v int32) {
	if x != nil {
		x.TriggerTime = v
	}
}

func (x *SFCExtraMap) SetBubbleTime(v int32) {
	if x != nil {
		x.BubbleTime = v
	}
}

func (x *SFCExtraMap) SetPolymericProductCategory(v int32) {
	if x != nil {
		x.PolymericProductCategory = v
	}
}

func (p *SFCExtraMap) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCExtraMap(%+v)", *p)
}

type PassengerSeatInfo struct {
	Title      string `json:"title" form:"title"`
	SubTitle   string `json:"sub_title" form:"sub_title"`
	TotalNum   int32  `json:"total_num" form:"total_num"`
	MaxSeatNum int32  `json:"max_seat_num" form:"max_seat_num"`
}

func (x *PassengerSeatInfo) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *PassengerSeatInfo) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *PassengerSeatInfo) GetTotalNum() (r int32) {
	if x != nil {
		return x.TotalNum
	}
	return r
}

func (x *PassengerSeatInfo) GetMaxSeatNum() (r int32) {
	if x != nil {
		return x.MaxSeatNum
	}
	return r
}

func (x *PassengerSeatInfo) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *PassengerSeatInfo) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *PassengerSeatInfo) SetTotalNum(v int32) {
	if x != nil {
		x.TotalNum = v
	}
}

func (x *PassengerSeatInfo) SetMaxSeatNum(v int32) {
	if x != nil {
		x.MaxSeatNum = v
	}
}

func (p *PassengerSeatInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassengerSeatInfo(%+v)", *p)
}

type InsuranceInfo struct {
	Title string `json:"title" form:"title"`
	Url   string `json:"url" form:"url"`
}

func (x *InsuranceInfo) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *InsuranceInfo) GetUrl() (r string) {
	if x != nil {
		return x.Url
	}
	return r
}

func (x *InsuranceInfo) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *InsuranceInfo) SetUrl(v string) {
	if x != nil {
		x.Url = v
	}
}

func (p *InsuranceInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InsuranceInfo(%+v)", *p)
}

type ExtraTab struct {
	ExtraText string `json:"extra_text" form:"extra_text"`
	ExtraUrl  string `json:"extra_url" form:"extra_url"`
}

func (x *ExtraTab) GetExtraText() (r string) {
	if x != nil {
		return x.ExtraText
	}
	return r
}

func (x *ExtraTab) GetExtraUrl() (r string) {
	if x != nil {
		return x.ExtraUrl
	}
	return r
}

func (x *ExtraTab) SetExtraText(v string) {
	if x != nil {
		x.ExtraText = v
	}
}

func (x *ExtraTab) SetExtraUrl(v string) {
	if x != nil {
		x.ExtraUrl = v
	}
}

func (p *ExtraTab) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExtraTab(%+v)", *p)
}

type MenuInfo struct {
	Title   string `json:"title" form:"title"`
	JumpUrl string `json:"jump_url" form:"jump_url"`
}

func (x *MenuInfo) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *MenuInfo) GetJumpUrl() (r string) {
	if x != nil {
		return x.JumpUrl
	}
	return r
}

func (x *MenuInfo) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *MenuInfo) SetJumpUrl(v string) {
	if x != nil {
		x.JumpUrl = v
	}
}

func (p *MenuInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MenuInfo(%+v)", *p)
}

type SfcBubbleInfo struct {
	Text           string  `json:"text" form:"text"`
	CanClose       int32   `json:"can_close" form:"can_close"`
	TextAlignment  *string `json:"text_alignment,omitempty" form:"text_alignment"`
	ArrowDirection *string `json:"arrow_direction,omitempty" form:"arrow_direction"`
}

func (x *SfcBubbleInfo) GetText() (r string) {
	if x != nil {
		return x.Text
	}
	return r
}

func (x *SfcBubbleInfo) GetCanClose() (r int32) {
	if x != nil {
		return x.CanClose
	}
	return r
}

func (x *SfcBubbleInfo) GetTextAlignment() (r string) {
	if x != nil && x.TextAlignment != nil {
		return *x.TextAlignment
	}
	return r
}

func (x *SfcBubbleInfo) GetArrowDirection() (r string) {
	if x != nil && x.ArrowDirection != nil {
		return *x.ArrowDirection
	}
	return r
}

func (x *SfcBubbleInfo) SetText(v string) {
	if x != nil {
		x.Text = v
	}
}

func (x *SfcBubbleInfo) SetCanClose(v int32) {
	if x != nil {
		x.CanClose = v
	}
}

func (x *SfcBubbleInfo) SetTextAlignment(v string) {
	if x != nil {
		x.TextAlignment = &v
	}
}

func (x *SfcBubbleInfo) SetArrowDirection(v string) {
	if x != nil {
		x.ArrowDirection = &v
	}
}

func (p *SfcBubbleInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SfcBubbleInfo(%+v)", *p)
}

type SfcCasperInfo struct {
	Id        string `json:"id" form:"id"`
	Template  string `json:"template" form:"template"`
	HummerCdn string `json:"hummer_cdn" form:"hummer_cdn"`
	WeexCdn   string `json:"weex_cdn" form:"weex_cdn"`
}

func (x *SfcCasperInfo) GetId() (r string) {
	if x != nil {
		return x.Id
	}
	return r
}

func (x *SfcCasperInfo) GetTemplate() (r string) {
	if x != nil {
		return x.Template
	}
	return r
}

func (x *SfcCasperInfo) GetHummerCdn() (r string) {
	if x != nil {
		return x.HummerCdn
	}
	return r
}

func (x *SfcCasperInfo) GetWeexCdn() (r string) {
	if x != nil {
		return x.WeexCdn
	}
	return r
}

func (x *SfcCasperInfo) SetId(v string) {
	if x != nil {
		x.Id = v
	}
}

func (x *SfcCasperInfo) SetTemplate(v string) {
	if x != nil {
		x.Template = v
	}
}

func (x *SfcCasperInfo) SetHummerCdn(v string) {
	if x != nil {
		x.HummerCdn = v
	}
}

func (x *SfcCasperInfo) SetWeexCdn(v string) {
	if x != nil {
		x.WeexCdn = v
	}
}

func (p *SfcCasperInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SfcCasperInfo(%+v)", *p)
}

type SfcTollCasperInfo struct {
	CasperContent *SfcCasperInfo `json:"casper_content,omitempty" form:"casper_content"`
}

func (x *SfcTollCasperInfo) GetCasperContent() (r *SfcCasperInfo) {
	if x != nil {
		return x.CasperContent
	}
	return r
}

func (x *SfcTollCasperInfo) SetCasperContent(v *SfcCasperInfo) {
	if x != nil {
		x.CasperContent = v
	}
}

func (p *SfcTollCasperInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SfcTollCasperInfo(%+v)", *p)
}

type SfcTollFeeInfo struct {
	Title          string         `json:"title" form:"title"`
	SelectTitlePre string         `json:"select_title_pre" form:"select_title_pre"`
	Icon           string         `json:"icon" form:"icon"`
	ActionType     string         `json:"action_type" form:"action_type"`
	HighwayInfo    *SfcBubbleInfo `json:"highway_info,omitempty" form:"highway_info"`
	Url            string         `json:"url" form:"url"`
	CasperId       *string        `json:"casper_id,omitempty" form:"casper_id"`
}

func (x *SfcTollFeeInfo) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *SfcTollFeeInfo) GetSelectTitlePre() (r string) {
	if x != nil {
		return x.SelectTitlePre
	}
	return r
}

func (x *SfcTollFeeInfo) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *SfcTollFeeInfo) GetActionType() (r string) {
	if x != nil {
		return x.ActionType
	}
	return r
}

func (x *SfcTollFeeInfo) GetHighwayInfo() (r *SfcBubbleInfo) {
	if x != nil {
		return x.HighwayInfo
	}
	return r
}

func (x *SfcTollFeeInfo) GetUrl() (r string) {
	if x != nil {
		return x.Url
	}
	return r
}

func (x *SfcTollFeeInfo) GetCasperId() (r string) {
	if x != nil && x.CasperId != nil {
		return *x.CasperId
	}
	return r
}

func (x *SfcTollFeeInfo) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *SfcTollFeeInfo) SetSelectTitlePre(v string) {
	if x != nil {
		x.SelectTitlePre = v
	}
}

func (x *SfcTollFeeInfo) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *SfcTollFeeInfo) SetActionType(v string) {
	if x != nil {
		x.ActionType = v
	}
}

func (x *SfcTollFeeInfo) SetHighwayInfo(v *SfcBubbleInfo) {
	if x != nil {
		x.HighwayInfo = v
	}
}

func (x *SfcTollFeeInfo) SetUrl(v string) {
	if x != nil {
		x.Url = v
	}
}

func (x *SfcTollFeeInfo) SetCasperId(v string) {
	if x != nil {
		x.CasperId = &v
	}
}

func (p *SfcTollFeeInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SfcTollFeeInfo(%+v)", *p)
}

type SFCPreCancelButtonInfo struct {
	LeftButton  *SfcSubmitButton `json:"left_button,omitempty" form:"left_button"`
	RightButton *SfcSubmitButton `json:"right_button,omitempty" form:"right_button"`
}

func (x *SFCPreCancelButtonInfo) GetLeftButton() (r *SfcSubmitButton) {
	if x != nil {
		return x.LeftButton
	}
	return r
}

func (x *SFCPreCancelButtonInfo) GetRightButton() (r *SfcSubmitButton) {
	if x != nil {
		return x.RightButton
	}
	return r
}

func (x *SFCPreCancelButtonInfo) SetLeftButton(v *SfcSubmitButton) {
	if x != nil {
		x.LeftButton = v
	}
}

func (x *SFCPreCancelButtonInfo) SetRightButton(v *SfcSubmitButton) {
	if x != nil {
		x.RightButton = v
	}
}

func (p *SFCPreCancelButtonInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCPreCancelButtonInfo(%+v)", *p)
}

type SFCPreCancelOmegaParam struct {
	BatchId         string `json:"batch_id" form:"batch_id"`
	EstimateTraceId string `json:"estimate_trace_id" form:"estimate_trace_id"`
}

func (x *SFCPreCancelOmegaParam) GetBatchId() (r string) {
	if x != nil {
		return x.BatchId
	}
	return r
}

func (x *SFCPreCancelOmegaParam) GetEstimateTraceId() (r string) {
	if x != nil {
		return x.EstimateTraceId
	}
	return r
}

func (x *SFCPreCancelOmegaParam) SetBatchId(v string) {
	if x != nil {
		x.BatchId = v
	}
}

func (x *SFCPreCancelOmegaParam) SetEstimateTraceId(v string) {
	if x != nil {
		x.EstimateTraceId = v
	}
}

func (p *SFCPreCancelOmegaParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCPreCancelOmegaParam(%+v)", *p)
}

type SFCPreCancelDetail struct {
	Title       *string                 `json:"title,omitempty" form:"title"`
	Timestamp   *int64                  `json:"timestamp,omitempty" form:"timestamp"`
	ButtonInfo  *SFCPreCancelButtonInfo `json:"button_info,omitempty" form:"button_info"`
	OmegaParams *SFCPreCancelOmegaParam `json:"omega_params,omitempty" form:"omega_params"`
}

func (x *SFCPreCancelDetail) GetTitle() (r string) {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return r
}

func (x *SFCPreCancelDetail) GetTimestamp() (r int64) {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return r
}

func (x *SFCPreCancelDetail) GetButtonInfo() (r *SFCPreCancelButtonInfo) {
	if x != nil {
		return x.ButtonInfo
	}
	return r
}

func (x *SFCPreCancelDetail) GetOmegaParams() (r *SFCPreCancelOmegaParam) {
	if x != nil {
		return x.OmegaParams
	}
	return r
}

func (x *SFCPreCancelDetail) SetTitle(v string) {
	if x != nil {
		x.Title = &v
	}
}

func (x *SFCPreCancelDetail) SetTimestamp(v int64) {
	if x != nil {
		x.Timestamp = &v
	}
}

func (x *SFCPreCancelDetail) SetButtonInfo(v *SFCPreCancelButtonInfo) {
	if x != nil {
		x.ButtonInfo = v
	}
}

func (x *SFCPreCancelDetail) SetOmegaParams(v *SFCPreCancelOmegaParam) {
	if x != nil {
		x.OmegaParams = v
	}
}

func (p *SFCPreCancelDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCPreCancelDetail(%+v)", *p)
}

type SFCEstimateResponse struct {
	MenuInfo             *MenuInfo           `json:"menu_info,omitempty" form:"menu_info"`
	EstimateData         []*SFCEstimateData  `json:"estimate_data,omitempty" form:"estimate_data"`
	PassengerSeatInfo    *PassengerSeatInfo  `json:"passenger_seat_info,omitempty" form:"passenger_seat_info"`
	OperationInfo        *SFCOperationInfo   `json:"operation_info,omitempty" form:"operation_info"`
	InsuranceInfo        *InsuranceInfo      `json:"insurance_info,omitempty" form:"insurance_info"`
	SubmitButton         *SfcSubmitButton    `json:"submit_button,omitempty" form:"submit_button"`
	BottomInfo           *SfcBottomInfo      `json:"bottom_info,omitempty" form:"bottom_info"`
	Title                *string             `json:"title,omitempty" form:"title"`
	IsMulti              *int32              `json:"is_multi,omitempty" form:"is_multi"`
	TimepickerScenesType string              `json:"timepicker_scenes_type" form:"timepicker_scenes_type"` //timepicker 入参
	AnimationInfo        *AnimationInfo      `json:"animation_info,omitempty" form:"animation_info"`       //动效物料
	TollFeeInfo          *SfcTollFeeInfo     `json:"toll_fee_info,omitempty" form:"toll_fee_info"`         //动效物料
	BannerInfo           *BannerInfo         `json:"banner_info,omitempty" form:"banner_info"`
	OmegaParams          *SfcOmegaParams     `json:"omega_params,omitempty" form:"omega_params"`
	PreCancelDetail      *SFCPreCancelDetail `json:"pre_cancel_detail,omitempty" form:"pre_cancel_detail"`
	RollFireworksInfo    *AnimationInfo      `json:"roll_fireworks_info,omitempty" form:"roll_fireworks_info"`
	SpsId                *string             `json:"sps_id,omitempty" form:"sps_id"`
	AdditionalInfo       *AdditionalInfo     `json:"additional_info,omitempty" form:"additional_info"`
	MapRouteId           *string             `json:"map_route_id,omitempty" form:"map_route_id"`
}

func (x *SFCEstimateResponse) GetMenuInfo() (r *MenuInfo) {
	if x != nil {
		return x.MenuInfo
	}
	return r
}

func (x *SFCEstimateResponse) GetEstimateData() (r []*SFCEstimateData) {
	if x != nil {
		return x.EstimateData
	}
	return r
}

func (x *SFCEstimateResponse) GetPassengerSeatInfo() (r *PassengerSeatInfo) {
	if x != nil {
		return x.PassengerSeatInfo
	}
	return r
}

func (x *SFCEstimateResponse) GetOperationInfo() (r *SFCOperationInfo) {
	if x != nil {
		return x.OperationInfo
	}
	return r
}

func (x *SFCEstimateResponse) GetInsuranceInfo() (r *InsuranceInfo) {
	if x != nil {
		return x.InsuranceInfo
	}
	return r
}

func (x *SFCEstimateResponse) GetSubmitButton() (r *SfcSubmitButton) {
	if x != nil {
		return x.SubmitButton
	}
	return r
}

func (x *SFCEstimateResponse) GetBottomInfo() (r *SfcBottomInfo) {
	if x != nil {
		return x.BottomInfo
	}
	return r
}

func (x *SFCEstimateResponse) GetTitle() (r string) {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return r
}

func (x *SFCEstimateResponse) GetIsMulti() (r int32) {
	if x != nil && x.IsMulti != nil {
		return *x.IsMulti
	}
	return r
}

func (x *SFCEstimateResponse) GetTimepickerScenesType() (r string) {
	if x != nil {
		return x.TimepickerScenesType
	}
	return r
}

func (x *SFCEstimateResponse) GetAnimationInfo() (r *AnimationInfo) {
	if x != nil {
		return x.AnimationInfo
	}
	return r
}

func (x *SFCEstimateResponse) GetTollFeeInfo() (r *SfcTollFeeInfo) {
	if x != nil {
		return x.TollFeeInfo
	}
	return r
}

func (x *SFCEstimateResponse) GetBannerInfo() (r *BannerInfo) {
	if x != nil {
		return x.BannerInfo
	}
	return r
}

func (x *SFCEstimateResponse) GetOmegaParams() (r *SfcOmegaParams) {
	if x != nil {
		return x.OmegaParams
	}
	return r
}

func (x *SFCEstimateResponse) GetPreCancelDetail() (r *SFCPreCancelDetail) {
	if x != nil {
		return x.PreCancelDetail
	}
	return r
}

func (x *SFCEstimateResponse) GetRollFireworksInfo() (r *AnimationInfo) {
	if x != nil {
		return x.RollFireworksInfo
	}
	return r
}

func (x *SFCEstimateResponse) GetSpsId() (r string) {
	if x != nil && x.SpsId != nil {
		return *x.SpsId
	}
	return r
}

func (x *SFCEstimateResponse) GetAdditionalInfo() (r *AdditionalInfo) {
	if x != nil {
		return x.AdditionalInfo
	}
	return r
}

func (x *SFCEstimateResponse) GetMapRouteId() (r string) {
	if x != nil && x.MapRouteId != nil {
		return *x.MapRouteId
	}
	return r
}

func (x *SFCEstimateResponse) SetMenuInfo(v *MenuInfo) {
	if x != nil {
		x.MenuInfo = v
	}
}

func (x *SFCEstimateResponse) SetEstimateData(v []*SFCEstimateData) {
	if x != nil {
		x.EstimateData = v
	}
}

func (x *SFCEstimateResponse) SetPassengerSeatInfo(v *PassengerSeatInfo) {
	if x != nil {
		x.PassengerSeatInfo = v
	}
}

func (x *SFCEstimateResponse) SetOperationInfo(v *SFCOperationInfo) {
	if x != nil {
		x.OperationInfo = v
	}
}

func (x *SFCEstimateResponse) SetInsuranceInfo(v *InsuranceInfo) {
	if x != nil {
		x.InsuranceInfo = v
	}
}

func (x *SFCEstimateResponse) SetSubmitButton(v *SfcSubmitButton) {
	if x != nil {
		x.SubmitButton = v
	}
}

func (x *SFCEstimateResponse) SetBottomInfo(v *SfcBottomInfo) {
	if x != nil {
		x.BottomInfo = v
	}
}

func (x *SFCEstimateResponse) SetTitle(v string) {
	if x != nil {
		x.Title = &v
	}
}

func (x *SFCEstimateResponse) SetIsMulti(v int32) {
	if x != nil {
		x.IsMulti = &v
	}
}

func (x *SFCEstimateResponse) SetTimepickerScenesType(v string) {
	if x != nil {
		x.TimepickerScenesType = v
	}
}

func (x *SFCEstimateResponse) SetAnimationInfo(v *AnimationInfo) {
	if x != nil {
		x.AnimationInfo = v
	}
}

func (x *SFCEstimateResponse) SetTollFeeInfo(v *SfcTollFeeInfo) {
	if x != nil {
		x.TollFeeInfo = v
	}
}

func (x *SFCEstimateResponse) SetBannerInfo(v *BannerInfo) {
	if x != nil {
		x.BannerInfo = v
	}
}

func (x *SFCEstimateResponse) SetOmegaParams(v *SfcOmegaParams) {
	if x != nil {
		x.OmegaParams = v
	}
}

func (x *SFCEstimateResponse) SetPreCancelDetail(v *SFCPreCancelDetail) {
	if x != nil {
		x.PreCancelDetail = v
	}
}

func (x *SFCEstimateResponse) SetRollFireworksInfo(v *AnimationInfo) {
	if x != nil {
		x.RollFireworksInfo = v
	}
}

func (x *SFCEstimateResponse) SetSpsId(v string) {
	if x != nil {
		x.SpsId = &v
	}
}

func (x *SFCEstimateResponse) SetAdditionalInfo(v *AdditionalInfo) {
	if x != nil {
		x.AdditionalInfo = v
	}
}

func (x *SFCEstimateResponse) SetMapRouteId(v string) {
	if x != nil {
		x.MapRouteId = &v
	}
}

func (p *SFCEstimateResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCEstimateResponse(%+v)", *p)
}

type SfcOmegaParams struct {
	CarpCouponType *string `json:"carp_coupon_type,omitempty" form:"carp_coupon_type"`
}

func (x *SfcOmegaParams) GetCarpCouponType() (r string) {
	if x != nil && x.CarpCouponType != nil {
		return *x.CarpCouponType
	}
	return r
}

func (x *SfcOmegaParams) SetCarpCouponType(v string) {
	if x != nil {
		x.CarpCouponType = &v
	}
}

func (p *SfcOmegaParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SfcOmegaParams(%+v)", *p)
}

type SfcSubmitButton struct {
	ActionType string  `json:"action_type" form:"action_type"`
	Title      string  `json:"title" form:"title"`
	Subtitle   *string `json:"subtitle,omitempty" form:"subtitle"`
}

func (x *SfcSubmitButton) GetActionType() (r string) {
	if x != nil {
		return x.ActionType
	}
	return r
}

func (x *SfcSubmitButton) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *SfcSubmitButton) GetSubtitle() (r string) {
	if x != nil && x.Subtitle != nil {
		return *x.Subtitle
	}
	return r
}

func (x *SfcSubmitButton) SetActionType(v string) {
	if x != nil {
		x.ActionType = v
	}
}

func (x *SfcSubmitButton) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *SfcSubmitButton) SetSubtitle(v string) {
	if x != nil {
		x.Subtitle = &v
	}
}

func (p *SfcSubmitButton) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SfcSubmitButton(%+v)", *p)
}

type SfcBottomInfo struct {
	Title      string `json:"title" form:"title"`
	JumpUrl    string `json:"jump_url" form:"jump_url"`
	LegacyType string `json:"legacy_type" form:"legacy_type"`
	PolicyId   int64  `json:"policy_id" form:"policy_id"`
}

func (x *SfcBottomInfo) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *SfcBottomInfo) GetJumpUrl() (r string) {
	if x != nil {
		return x.JumpUrl
	}
	return r
}

func (x *SfcBottomInfo) GetLegacyType() (r string) {
	if x != nil {
		return x.LegacyType
	}
	return r
}

func (x *SfcBottomInfo) GetPolicyId() (r int64) {
	if x != nil {
		return x.PolicyId
	}
	return r
}

func (x *SfcBottomInfo) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *SfcBottomInfo) SetJumpUrl(v string) {
	if x != nil {
		x.JumpUrl = v
	}
}

func (x *SfcBottomInfo) SetLegacyType(v string) {
	if x != nil {
		x.LegacyType = v
	}
}

func (x *SfcBottomInfo) SetPolicyId(v int64) {
	if x != nil {
		x.PolicyId = v
	}
}

func (p *SfcBottomInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SfcBottomInfo(%+v)", *p)
}

type AnimationInfo struct {
	AnimationKey  string `json:"animation_key" form:"animation_key"`
	AnimationImg  string `json:"animation_img" form:"animation_img"`
	DefaultImg    string `json:"default_img" form:"default_img"`
	CloseImg      string `json:"close_img" form:"close_img"`
	AnimationTime int32  `json:"animation_time" form:"animation_time"`
}

func (x *AnimationInfo) GetAnimationKey() (r string) {
	if x != nil {
		return x.AnimationKey
	}
	return r
}

func (x *AnimationInfo) GetAnimationImg() (r string) {
	if x != nil {
		return x.AnimationImg
	}
	return r
}

func (x *AnimationInfo) GetDefaultImg() (r string) {
	if x != nil {
		return x.DefaultImg
	}
	return r
}

func (x *AnimationInfo) GetCloseImg() (r string) {
	if x != nil {
		return x.CloseImg
	}
	return r
}

func (x *AnimationInfo) GetAnimationTime() (r int32) {
	if x != nil {
		return x.AnimationTime
	}
	return r
}

func (x *AnimationInfo) SetAnimationKey(v string) {
	if x != nil {
		x.AnimationKey = v
	}
}

func (x *AnimationInfo) SetAnimationImg(v string) {
	if x != nil {
		x.AnimationImg = v
	}
}

func (x *AnimationInfo) SetDefaultImg(v string) {
	if x != nil {
		x.DefaultImg = v
	}
}

func (x *AnimationInfo) SetCloseImg(v string) {
	if x != nil {
		x.CloseImg = v
	}
}

func (x *AnimationInfo) SetAnimationTime(v int32) {
	if x != nil {
		x.AnimationTime = v
	}
}

func (p *AnimationInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AnimationInfo(%+v)", *p)
}

type SFCMultiEstimateResponse struct {
	Errno   int32                `json:"errno" form:"errno"`
	Errmsg  string               `json:"errmsg" form:"errmsg"`
	Data    *SFCEstimateResponse `json:"data,omitempty" form:"data"`
	TraceId string               `json:"trace_id" form:"trace_id"`
}

func (x *SFCMultiEstimateResponse) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *SFCMultiEstimateResponse) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *SFCMultiEstimateResponse) GetData() (r *SFCEstimateResponse) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *SFCMultiEstimateResponse) GetTraceId() (r string) {
	if x != nil {
		return x.TraceId
	}
	return r
}

func (x *SFCMultiEstimateResponse) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *SFCMultiEstimateResponse) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *SFCMultiEstimateResponse) SetData(v *SFCEstimateResponse) {
	if x != nil {
		x.Data = v
	}
}

func (x *SFCMultiEstimateResponse) SetTraceId(v string) {
	if x != nil {
		x.TraceId = v
	}
}

func (p *SFCMultiEstimateResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCMultiEstimateResponse(%+v)", *p)
}

type SFCEstimateRollInfo struct {
	TextColor    string  `json:"text_color" form:"text_color"`
	TitlePre     string  `json:"title_pre" form:"title_pre"`
	TitleEnd     string  `json:"title_end" form:"title_end"`
	SubTextColor *string `json:"sub_text_color,omitempty" form:"sub_text_color"`
	SubTitlePre  *string `json:"sub_title_pre,omitempty" form:"sub_title_pre"`
	SubTitleEnd  *string `json:"sub_title_end,omitempty" form:"sub_title_end"`
}

func (x *SFCEstimateRollInfo) GetTextColor() (r string) {
	if x != nil {
		return x.TextColor
	}
	return r
}

func (x *SFCEstimateRollInfo) GetTitlePre() (r string) {
	if x != nil {
		return x.TitlePre
	}
	return r
}

func (x *SFCEstimateRollInfo) GetTitleEnd() (r string) {
	if x != nil {
		return x.TitleEnd
	}
	return r
}

func (x *SFCEstimateRollInfo) GetSubTextColor() (r string) {
	if x != nil && x.SubTextColor != nil {
		return *x.SubTextColor
	}
	return r
}

func (x *SFCEstimateRollInfo) GetSubTitlePre() (r string) {
	if x != nil && x.SubTitlePre != nil {
		return *x.SubTitlePre
	}
	return r
}

func (x *SFCEstimateRollInfo) GetSubTitleEnd() (r string) {
	if x != nil && x.SubTitleEnd != nil {
		return *x.SubTitleEnd
	}
	return r
}

func (x *SFCEstimateRollInfo) SetTextColor(v string) {
	if x != nil {
		x.TextColor = v
	}
}

func (x *SFCEstimateRollInfo) SetTitlePre(v string) {
	if x != nil {
		x.TitlePre = v
	}
}

func (x *SFCEstimateRollInfo) SetTitleEnd(v string) {
	if x != nil {
		x.TitleEnd = v
	}
}

func (x *SFCEstimateRollInfo) SetSubTextColor(v string) {
	if x != nil {
		x.SubTextColor = &v
	}
}

func (x *SFCEstimateRollInfo) SetSubTitlePre(v string) {
	if x != nil {
		x.SubTitlePre = &v
	}
}

func (x *SFCEstimateRollInfo) SetSubTitleEnd(v string) {
	if x != nil {
		x.SubTitleEnd = &v
	}
}

func (p *SFCEstimateRollInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCEstimateRollInfo(%+v)", *p)
}

// dcmp结构体
type SFCEstimateDcmp struct {
	PriceInfo            map[string]*SFCDcmpPriceInfo    `json:"price_info" form:"price_info"`
	PriceIcon            string                          `json:"price_icon" form:"price_icon"`
	SeatInfo             *SFCDcmpSeatInfo                `json:"seat_info,omitempty" form:"seat_info"`
	MenuInfo             *MenuInfo                       `json:"menu_info,omitempty" form:"menu_info"`
	ExtraTab             *ExtraTab                       `json:"extra_tab,omitempty" form:"extra_tab"`
	Title                map[string]string               `json:"title" form:"title"`
	SubmitButton         *SfcSubmitButton                `json:"submit_button,omitempty" form:"submit_button"`
	SubmitButtonSubtitle string                          `json:"submit_button_subtitle" form:"submit_button_subtitle"`
	BottomInfo           *SfcBottomInfo                  `json:"bottom_info,omitempty" form:"bottom_info"`
	DiscountText         string                          `json:"discount_text" form:"discount_text"`
	InsuranceUrl         string                          `json:"insurance_url" form:"insurance_url"`
	DeclareUrl           string                          `json:"declare_url" form:"declare_url"`
	OperationInfo        *SFCOperationInfo               `json:"operation_info,omitempty" form:"operation_info"`
	AnimationInfo        *AnimationInfo                  `json:"animation_info,omitempty" form:"animation_info"`
	TollFeeInfo          *SfcTollFeeInfo                 `json:"toll_fee_info,omitempty" form:"toll_fee_info"`
	FireworksInfo        *AnimationInfo                  `json:"fireworks_info,omitempty" form:"fireworks_info"`
	RollInfo             map[string]*SFCEstimateRollInfo `json:"roll_info,omitempty" form:"roll_info"`
	InsuranceLegacyType  string                          `json:"insurance_legacy_type" form:"insurance_legacy_type"`
	DecimalText          *string                         `json:"decimal_text,omitempty" form:"decimal_text"`
	EndText              *string                         `json:"end_text,omitempty" form:"end_text"`
	HomeComingBannerInfo *BannerInfoData                 `json:"home_coming_banner_info,omitempty" form:"home_coming_banner_info"`
	ReAppointmentText    string                          `json:"re_appointment_text" form:"re_appointment_text"`
	SubRollInfo          map[string]*SFCEstimateRollInfo `json:"sub_roll_info,omitempty" form:"sub_roll_info"`
}

func (x *SFCEstimateDcmp) GetPriceInfo() (r map[string]*SFCDcmpPriceInfo) {
	if x != nil {
		return x.PriceInfo
	}
	return r
}

func (x *SFCEstimateDcmp) GetPriceIcon() (r string) {
	if x != nil {
		return x.PriceIcon
	}
	return r
}

func (x *SFCEstimateDcmp) GetSeatInfo() (r *SFCDcmpSeatInfo) {
	if x != nil {
		return x.SeatInfo
	}
	return r
}

func (x *SFCEstimateDcmp) GetMenuInfo() (r *MenuInfo) {
	if x != nil {
		return x.MenuInfo
	}
	return r
}

func (x *SFCEstimateDcmp) GetExtraTab() (r *ExtraTab) {
	if x != nil {
		return x.ExtraTab
	}
	return r
}

func (x *SFCEstimateDcmp) GetTitle() (r map[string]string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *SFCEstimateDcmp) GetSubmitButton() (r *SfcSubmitButton) {
	if x != nil {
		return x.SubmitButton
	}
	return r
}

func (x *SFCEstimateDcmp) GetSubmitButtonSubtitle() (r string) {
	if x != nil {
		return x.SubmitButtonSubtitle
	}
	return r
}

func (x *SFCEstimateDcmp) GetBottomInfo() (r *SfcBottomInfo) {
	if x != nil {
		return x.BottomInfo
	}
	return r
}

func (x *SFCEstimateDcmp) GetDiscountText() (r string) {
	if x != nil {
		return x.DiscountText
	}
	return r
}

func (x *SFCEstimateDcmp) GetInsuranceUrl() (r string) {
	if x != nil {
		return x.InsuranceUrl
	}
	return r
}

func (x *SFCEstimateDcmp) GetDeclareUrl() (r string) {
	if x != nil {
		return x.DeclareUrl
	}
	return r
}

func (x *SFCEstimateDcmp) GetOperationInfo() (r *SFCOperationInfo) {
	if x != nil {
		return x.OperationInfo
	}
	return r
}

func (x *SFCEstimateDcmp) GetAnimationInfo() (r *AnimationInfo) {
	if x != nil {
		return x.AnimationInfo
	}
	return r
}

func (x *SFCEstimateDcmp) GetTollFeeInfo() (r *SfcTollFeeInfo) {
	if x != nil {
		return x.TollFeeInfo
	}
	return r
}

func (x *SFCEstimateDcmp) GetFireworksInfo() (r *AnimationInfo) {
	if x != nil {
		return x.FireworksInfo
	}
	return r
}

func (x *SFCEstimateDcmp) GetRollInfo() (r map[string]*SFCEstimateRollInfo) {
	if x != nil {
		return x.RollInfo
	}
	return r
}

func (x *SFCEstimateDcmp) GetInsuranceLegacyType() (r string) {
	if x != nil {
		return x.InsuranceLegacyType
	}
	return r
}

func (x *SFCEstimateDcmp) GetDecimalText() (r string) {
	if x != nil && x.DecimalText != nil {
		return *x.DecimalText
	}
	return r
}

func (x *SFCEstimateDcmp) GetEndText() (r string) {
	if x != nil && x.EndText != nil {
		return *x.EndText
	}
	return r
}

func (x *SFCEstimateDcmp) GetHomeComingBannerInfo() (r *BannerInfoData) {
	if x != nil {
		return x.HomeComingBannerInfo
	}
	return r
}

func (x *SFCEstimateDcmp) GetReAppointmentText() (r string) {
	if x != nil {
		return x.ReAppointmentText
	}
	return r
}

func (x *SFCEstimateDcmp) GetSubRollInfo() (r map[string]*SFCEstimateRollInfo) {
	if x != nil {
		return x.SubRollInfo
	}
	return r
}

func (x *SFCEstimateDcmp) SetPriceInfo(v map[string]*SFCDcmpPriceInfo) {
	if x != nil {
		x.PriceInfo = v
	}
}

func (x *SFCEstimateDcmp) SetPriceIcon(v string) {
	if x != nil {
		x.PriceIcon = v
	}
}

func (x *SFCEstimateDcmp) SetSeatInfo(v *SFCDcmpSeatInfo) {
	if x != nil {
		x.SeatInfo = v
	}
}

func (x *SFCEstimateDcmp) SetMenuInfo(v *MenuInfo) {
	if x != nil {
		x.MenuInfo = v
	}
}

func (x *SFCEstimateDcmp) SetExtraTab(v *ExtraTab) {
	if x != nil {
		x.ExtraTab = v
	}
}

func (x *SFCEstimateDcmp) SetTitle(v map[string]string) {
	if x != nil {
		x.Title = v
	}
}

func (x *SFCEstimateDcmp) SetSubmitButton(v *SfcSubmitButton) {
	if x != nil {
		x.SubmitButton = v
	}
}

func (x *SFCEstimateDcmp) SetSubmitButtonSubtitle(v string) {
	if x != nil {
		x.SubmitButtonSubtitle = v
	}
}

func (x *SFCEstimateDcmp) SetBottomInfo(v *SfcBottomInfo) {
	if x != nil {
		x.BottomInfo = v
	}
}

func (x *SFCEstimateDcmp) SetDiscountText(v string) {
	if x != nil {
		x.DiscountText = v
	}
}

func (x *SFCEstimateDcmp) SetInsuranceUrl(v string) {
	if x != nil {
		x.InsuranceUrl = v
	}
}

func (x *SFCEstimateDcmp) SetDeclareUrl(v string) {
	if x != nil {
		x.DeclareUrl = v
	}
}

func (x *SFCEstimateDcmp) SetOperationInfo(v *SFCOperationInfo) {
	if x != nil {
		x.OperationInfo = v
	}
}

func (x *SFCEstimateDcmp) SetAnimationInfo(v *AnimationInfo) {
	if x != nil {
		x.AnimationInfo = v
	}
}

func (x *SFCEstimateDcmp) SetTollFeeInfo(v *SfcTollFeeInfo) {
	if x != nil {
		x.TollFeeInfo = v
	}
}

func (x *SFCEstimateDcmp) SetFireworksInfo(v *AnimationInfo) {
	if x != nil {
		x.FireworksInfo = v
	}
}

func (x *SFCEstimateDcmp) SetRollInfo(v map[string]*SFCEstimateRollInfo) {
	if x != nil {
		x.RollInfo = v
	}
}

func (x *SFCEstimateDcmp) SetInsuranceLegacyType(v string) {
	if x != nil {
		x.InsuranceLegacyType = v
	}
}

func (x *SFCEstimateDcmp) SetDecimalText(v string) {
	if x != nil {
		x.DecimalText = &v
	}
}

func (x *SFCEstimateDcmp) SetEndText(v string) {
	if x != nil {
		x.EndText = &v
	}
}

func (x *SFCEstimateDcmp) SetHomeComingBannerInfo(v *BannerInfoData) {
	if x != nil {
		x.HomeComingBannerInfo = v
	}
}

func (x *SFCEstimateDcmp) SetReAppointmentText(v string) {
	if x != nil {
		x.ReAppointmentText = v
	}
}

func (x *SFCEstimateDcmp) SetSubRollInfo(v map[string]*SFCEstimateRollInfo) {
	if x != nil {
		x.SubRollInfo = v
	}
}

func (p *SFCEstimateDcmp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCEstimateDcmp(%+v)", *p)
}

type SFCDcmpPriceInfo struct {
	SubTitle      string `json:"sub_title" form:"sub_title"`
	PriceTitle    string `json:"price_title" form:"price_title"`
	PriceSubTitle string `json:"price_sub_title" form:"price_sub_title"`
}

func (x *SFCDcmpPriceInfo) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *SFCDcmpPriceInfo) GetPriceTitle() (r string) {
	if x != nil {
		return x.PriceTitle
	}
	return r
}

func (x *SFCDcmpPriceInfo) GetPriceSubTitle() (r string) {
	if x != nil {
		return x.PriceSubTitle
	}
	return r
}

func (x *SFCDcmpPriceInfo) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *SFCDcmpPriceInfo) SetPriceTitle(v string) {
	if x != nil {
		x.PriceTitle = v
	}
}

func (x *SFCDcmpPriceInfo) SetPriceSubTitle(v string) {
	if x != nil {
		x.PriceSubTitle = v
	}
}

func (p *SFCDcmpPriceInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCDcmpPriceInfo(%+v)", *p)
}

type SFCDcmpSeatInfo struct {
	Title       string `json:"title" form:"title"`
	SubTitle    string `json:"sub_title" form:"sub_title"`
	MaxNum      int32  `json:"max_num" form:"max_num"`
	NewSubTitle string `json:"new_sub_title" form:"new_sub_title"`
}

func (x *SFCDcmpSeatInfo) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *SFCDcmpSeatInfo) GetSubTitle() (r string) {
	if x != nil {
		return x.SubTitle
	}
	return r
}

func (x *SFCDcmpSeatInfo) GetMaxNum() (r int32) {
	if x != nil {
		return x.MaxNum
	}
	return r
}

func (x *SFCDcmpSeatInfo) GetNewSubTitle() (r string) {
	if x != nil {
		return x.NewSubTitle
	}
	return r
}

func (x *SFCDcmpSeatInfo) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *SFCDcmpSeatInfo) SetSubTitle(v string) {
	if x != nil {
		x.SubTitle = v
	}
}

func (x *SFCDcmpSeatInfo) SetMaxNum(v int32) {
	if x != nil {
		x.MaxNum = v
	}
}

func (x *SFCDcmpSeatInfo) SetNewSubTitle(v string) {
	if x != nil {
		x.NewSubTitle = v
	}
}

func (p *SFCDcmpSeatInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCDcmpSeatInfo(%+v)", *p)
}

type SFCOperationInfo struct {
	Title       *string `json:"title,omitempty" form:"title"`
	SelectTitle *string `json:"select_title,omitempty" form:"select_title"`
	Url         *string `json:"url,omitempty" form:"url"`
	Type        *string `json:"type,omitempty" form:"type"`
}

func (x *SFCOperationInfo) GetTitle() (r string) {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return r
}

func (x *SFCOperationInfo) GetSelectTitle() (r string) {
	if x != nil && x.SelectTitle != nil {
		return *x.SelectTitle
	}
	return r
}

func (x *SFCOperationInfo) GetUrl() (r string) {
	if x != nil && x.Url != nil {
		return *x.Url
	}
	return r
}

func (x *SFCOperationInfo) GetType() (r string) {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return r
}

func (x *SFCOperationInfo) SetTitle(v string) {
	if x != nil {
		x.Title = &v
	}
}

func (x *SFCOperationInfo) SetSelectTitle(v string) {
	if x != nil {
		x.SelectTitle = &v
	}
}

func (x *SFCOperationInfo) SetUrl(v string) {
	if x != nil {
		x.Url = &v
	}
}

func (x *SFCOperationInfo) SetType(v string) {
	if x != nil {
		x.Type = &v
	}
}

func (p *SFCOperationInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCOperationInfo(%+v)", *p)
}

type BannerInfo struct {
	CardType      *int32          `json:"card_type,omitempty" form:"card_type"`
	CasperContent *CasperContent  `json:"casper_content,omitempty" form:"casper_content"`
	Data          *BannerInfoData `json:"data,omitempty" form:"data"`
}

func (x *BannerInfo) GetCardType() (r int32) {
	if x != nil && x.CardType != nil {
		return *x.CardType
	}
	return r
}

func (x *BannerInfo) GetCasperContent() (r *CasperContent) {
	if x != nil {
		return x.CasperContent
	}
	return r
}

func (x *BannerInfo) GetData() (r *BannerInfoData) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *BannerInfo) SetCardType(v int32) {
	if x != nil {
		x.CardType = &v
	}
}

func (x *BannerInfo) SetCasperContent(v *CasperContent) {
	if x != nil {
		x.CasperContent = v
	}
}

func (x *BannerInfo) SetData(v *BannerInfoData) {
	if x != nil {
		x.Data = v
	}
}

func (p *BannerInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BannerInfo(%+v)", *p)
}

type CasperContent struct {
	Id       *string `json:"id,omitempty" form:"id"`
	Template *string `json:"template,omitempty" form:"template"`
	WeexCdn  *string `json:"weex_cdn,omitempty" form:"weex_cdn"`
}

func (x *CasperContent) GetId() (r string) {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return r
}

func (x *CasperContent) GetTemplate() (r string) {
	if x != nil && x.Template != nil {
		return *x.Template
	}
	return r
}

func (x *CasperContent) GetWeexCdn() (r string) {
	if x != nil && x.WeexCdn != nil {
		return *x.WeexCdn
	}
	return r
}

func (x *CasperContent) SetId(v string) {
	if x != nil {
		x.Id = &v
	}
}

func (x *CasperContent) SetTemplate(v string) {
	if x != nil {
		x.Template = &v
	}
}

func (x *CasperContent) SetWeexCdn(v string) {
	if x != nil {
		x.WeexCdn = &v
	}
}

func (p *CasperContent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CasperContent(%+v)", *p)
}

type BannerInfoData struct {
	Background         *string               `json:"background,omitempty" form:"background"`
	LeftIcon           *string               `json:"left_icon,omitempty" form:"left_icon"`
	Title              *string               `json:"title,omitempty" form:"title"`
	Subtitle           *string               `json:"subtitle,omitempty" form:"subtitle"`
	OmegaSw            *OmegaCk              `json:"omega_sw,omitempty" form:"omega_sw"`
	TitleImg           *string               `json:"title_img,omitempty" form:"title_img"`
	PassengerNoticeBar []*PassengerNoticeBar `json:"passenger_notice_bar,omitempty" form:"passenger_notice_bar"`
	StayTime           *int32                `json:"stay_time,omitempty" form:"stay_time"`
	BackgroundColors   []string              `json:"background_colors,omitempty" form:"background_colors"`
}

func (x *BannerInfoData) GetBackground() (r string) {
	if x != nil && x.Background != nil {
		return *x.Background
	}
	return r
}

func (x *BannerInfoData) GetLeftIcon() (r string) {
	if x != nil && x.LeftIcon != nil {
		return *x.LeftIcon
	}
	return r
}

func (x *BannerInfoData) GetTitle() (r string) {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return r
}

func (x *BannerInfoData) GetSubtitle() (r string) {
	if x != nil && x.Subtitle != nil {
		return *x.Subtitle
	}
	return r
}

func (x *BannerInfoData) GetOmegaSw() (r *OmegaCk) {
	if x != nil {
		return x.OmegaSw
	}
	return r
}

func (x *BannerInfoData) GetTitleImg() (r string) {
	if x != nil && x.TitleImg != nil {
		return *x.TitleImg
	}
	return r
}

func (x *BannerInfoData) GetPassengerNoticeBar() (r []*PassengerNoticeBar) {
	if x != nil {
		return x.PassengerNoticeBar
	}
	return r
}

func (x *BannerInfoData) GetStayTime() (r int32) {
	if x != nil && x.StayTime != nil {
		return *x.StayTime
	}
	return r
}

func (x *BannerInfoData) GetBackgroundColors() (r []string) {
	if x != nil {
		return x.BackgroundColors
	}
	return r
}

func (x *BannerInfoData) SetBackground(v string) {
	if x != nil {
		x.Background = &v
	}
}

func (x *BannerInfoData) SetLeftIcon(v string) {
	if x != nil {
		x.LeftIcon = &v
	}
}

func (x *BannerInfoData) SetTitle(v string) {
	if x != nil {
		x.Title = &v
	}
}

func (x *BannerInfoData) SetSubtitle(v string) {
	if x != nil {
		x.Subtitle = &v
	}
}

func (x *BannerInfoData) SetOmegaSw(v *OmegaCk) {
	if x != nil {
		x.OmegaSw = v
	}
}

func (x *BannerInfoData) SetTitleImg(v string) {
	if x != nil {
		x.TitleImg = &v
	}
}

func (x *BannerInfoData) SetPassengerNoticeBar(v []*PassengerNoticeBar) {
	if x != nil {
		x.PassengerNoticeBar = v
	}
}

func (x *BannerInfoData) SetStayTime(v int32) {
	if x != nil {
		x.StayTime = &v
	}
}

func (x *BannerInfoData) SetBackgroundColors(v []string) {
	if x != nil {
		x.BackgroundColors = v
	}
}

func (p *BannerInfoData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BannerInfoData(%+v)", *p)
}

type OmegaCk struct {
	EventId    *string        `json:"event_id,omitempty" form:"event_id"`
	Parameters *OmegaCkParams `json:"parameters,omitempty" form:"parameters"`
}

func (x *OmegaCk) GetEventId() (r string) {
	if x != nil && x.EventId != nil {
		return *x.EventId
	}
	return r
}

func (x *OmegaCk) GetParameters() (r *OmegaCkParams) {
	if x != nil {
		return x.Parameters
	}
	return r
}

func (x *OmegaCk) SetEventId(v string) {
	if x != nil {
		x.EventId = &v
	}
}

func (x *OmegaCk) SetParameters(v *OmegaCkParams) {
	if x != nil {
		x.Parameters = v
	}
}

func (p *OmegaCk) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OmegaCk(%+v)", *p)
}

type OmegaCkParams struct {
	CouponType *string `json:"coupon_type,omitempty" form:"coupon_type"`
	SwType     *string `json:"sw_type,omitempty" form:"sw_type"`
}

func (x *OmegaCkParams) GetCouponType() (r string) {
	if x != nil && x.CouponType != nil {
		return *x.CouponType
	}
	return r
}

func (x *OmegaCkParams) GetSwType() (r string) {
	if x != nil && x.SwType != nil {
		return *x.SwType
	}
	return r
}

func (x *OmegaCkParams) SetCouponType(v string) {
	if x != nil {
		x.CouponType = &v
	}
}

func (x *OmegaCkParams) SetSwType(v string) {
	if x != nil {
		x.SwType = &v
	}
}

func (p *OmegaCkParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OmegaCkParams(%+v)", *p)
}

type PassengerNoticeBar struct {
	T       *string  `json:"T,omitempty" form:"T"`
	LogData *LogData `json:"log_data,omitempty" form:"log_data"`
	Title   *string  `json:"title,omitempty" form:"title"`
	Icon    *string  `json:"icon,omitempty" form:"icon"`
	Link    *string  `json:"link,omitempty" form:"link"`
}

func (x *PassengerNoticeBar) GetT() (r string) {
	if x != nil && x.T != nil {
		return *x.T
	}
	return r
}

func (x *PassengerNoticeBar) GetLogData() (r *LogData) {
	if x != nil {
		return x.LogData
	}
	return r
}

func (x *PassengerNoticeBar) GetTitle() (r string) {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return r
}

func (x *PassengerNoticeBar) GetIcon() (r string) {
	if x != nil && x.Icon != nil {
		return *x.Icon
	}
	return r
}

func (x *PassengerNoticeBar) GetLink() (r string) {
	if x != nil && x.Link != nil {
		return *x.Link
	}
	return r
}

func (x *PassengerNoticeBar) SetT(v string) {
	if x != nil {
		x.T = &v
	}
}

func (x *PassengerNoticeBar) SetLogData(v *LogData) {
	if x != nil {
		x.LogData = v
	}
}

func (x *PassengerNoticeBar) SetTitle(v string) {
	if x != nil {
		x.Title = &v
	}
}

func (x *PassengerNoticeBar) SetIcon(v string) {
	if x != nil {
		x.Icon = &v
	}
}

func (x *PassengerNoticeBar) SetLink(v string) {
	if x != nil {
		x.Link = &v
	}
}

func (p *PassengerNoticeBar) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassengerNoticeBar(%+v)", *p)
}

type LogData struct {
	ActId            *int32  `json:"act_id,omitempty" form:"act_id"`                       //eg: 960081
	ApolloGroupName  *string `json:"apolloGroupName,omitempty" form:"apolloGroupName"`     //eg: ""
	BusinessId       *string `json:"business_id,omitempty" form:"business_id"`             //eg: "260"
	CityId           *int32  `json:"city_id,omitempty" form:"city_id"`                     //eg: 1
	Cost             *int32  `json:"cost,omitempty" form:"cost"`                           //eg: 10
	EntranceChannel  *string `json:"entrance_channel,omitempty" form:"entrance_channel"`   //eg: ""
	EntranceYewuxian *int32  `json:"entrance_yewuxian,omitempty" form:"entrance_yewuxian"` //eg: 0
	IsCommercialAd   *bool   `json:"is_commercial_ad,omitempty" form:"is_commercial_ad"`   //eg: false
	OrderId          *string `json:"order_id,omitempty" form:"order_id"`                   //eg: ""
	Pvid             *string `json:"pvid,omitempty" form:"pvid"`                           //eg: "a7e31811-fa71-4526-93d6-43e70a6f5c6a"
	ResourceId       *string `json:"resource_id,omitempty" form:"resource_id"`             //eg: "287"
	ResourceName     *string `json:"resource_name,omitempty" form:"resource_name"`         //eg: "dididri_profile_banners_1"
	ScheduleId       *string `json:"schedule_id,omitempty" form:"schedule_id"`             //eg: "233951"
	SlideId          *int32  `json:"slide_id,omitempty" form:"slide_id"`                   //eg: 1
	TraceId          *string `json:"trace_id,omitempty" form:"trace_id"`                   //eg: "ac199a29615023252521b82310240303"
	Type             *string `json:"type,omitempty" form:"type"`                           //eg: "engine"
	Uid              *int64  `json:"uid,omitempty" form:"uid"`                             //eg: 580544957962027
	UnitId           *string `json:"unit_id,omitempty" form:"unit_id"`                     //eg: "828065"
	WorkFlowId       *string `json:"work_flow_id,omitempty" form:"work_flow_id"`           //eg: "eve_ads_default_20210427114416350-strategy"
}

func (x *LogData) GetActId() (r int32) {
	if x != nil && x.ActId != nil {
		return *x.ActId
	}
	return r
}

func (x *LogData) GetApolloGroupName() (r string) {
	if x != nil && x.ApolloGroupName != nil {
		return *x.ApolloGroupName
	}
	return r
}

func (x *LogData) GetBusinessId() (r string) {
	if x != nil && x.BusinessId != nil {
		return *x.BusinessId
	}
	return r
}

func (x *LogData) GetCityId() (r int32) {
	if x != nil && x.CityId != nil {
		return *x.CityId
	}
	return r
}

func (x *LogData) GetCost() (r int32) {
	if x != nil && x.Cost != nil {
		return *x.Cost
	}
	return r
}

func (x *LogData) GetEntranceChannel() (r string) {
	if x != nil && x.EntranceChannel != nil {
		return *x.EntranceChannel
	}
	return r
}

func (x *LogData) GetEntranceYewuxian() (r int32) {
	if x != nil && x.EntranceYewuxian != nil {
		return *x.EntranceYewuxian
	}
	return r
}

func (x *LogData) GetIsCommercialAd() (r bool) {
	if x != nil && x.IsCommercialAd != nil {
		return *x.IsCommercialAd
	}
	return r
}

func (x *LogData) GetOrderId() (r string) {
	if x != nil && x.OrderId != nil {
		return *x.OrderId
	}
	return r
}

func (x *LogData) GetPvid() (r string) {
	if x != nil && x.Pvid != nil {
		return *x.Pvid
	}
	return r
}

func (x *LogData) GetResourceId() (r string) {
	if x != nil && x.ResourceId != nil {
		return *x.ResourceId
	}
	return r
}

func (x *LogData) GetResourceName() (r string) {
	if x != nil && x.ResourceName != nil {
		return *x.ResourceName
	}
	return r
}

func (x *LogData) GetScheduleId() (r string) {
	if x != nil && x.ScheduleId != nil {
		return *x.ScheduleId
	}
	return r
}

func (x *LogData) GetSlideId() (r int32) {
	if x != nil && x.SlideId != nil {
		return *x.SlideId
	}
	return r
}

func (x *LogData) GetTraceId() (r string) {
	if x != nil && x.TraceId != nil {
		return *x.TraceId
	}
	return r
}

func (x *LogData) GetType() (r string) {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return r
}

func (x *LogData) GetUid() (r int64) {
	if x != nil && x.Uid != nil {
		return *x.Uid
	}
	return r
}

func (x *LogData) GetUnitId() (r string) {
	if x != nil && x.UnitId != nil {
		return *x.UnitId
	}
	return r
}

func (x *LogData) GetWorkFlowId() (r string) {
	if x != nil && x.WorkFlowId != nil {
		return *x.WorkFlowId
	}
	return r
}

func (x *LogData) SetActId(v int32) {
	if x != nil {
		x.ActId = &v
	}
}

func (x *LogData) SetApolloGroupName(v string) {
	if x != nil {
		x.ApolloGroupName = &v
	}
}

func (x *LogData) SetBusinessId(v string) {
	if x != nil {
		x.BusinessId = &v
	}
}

func (x *LogData) SetCityId(v int32) {
	if x != nil {
		x.CityId = &v
	}
}

func (x *LogData) SetCost(v int32) {
	if x != nil {
		x.Cost = &v
	}
}

func (x *LogData) SetEntranceChannel(v string) {
	if x != nil {
		x.EntranceChannel = &v
	}
}

func (x *LogData) SetEntranceYewuxian(v int32) {
	if x != nil {
		x.EntranceYewuxian = &v
	}
}

func (x *LogData) SetIsCommercialAd(v bool) {
	if x != nil {
		x.IsCommercialAd = &v
	}
}

func (x *LogData) SetOrderId(v string) {
	if x != nil {
		x.OrderId = &v
	}
}

func (x *LogData) SetPvid(v string) {
	if x != nil {
		x.Pvid = &v
	}
}

func (x *LogData) SetResourceId(v string) {
	if x != nil {
		x.ResourceId = &v
	}
}

func (x *LogData) SetResourceName(v string) {
	if x != nil {
		x.ResourceName = &v
	}
}

func (x *LogData) SetScheduleId(v string) {
	if x != nil {
		x.ScheduleId = &v
	}
}

func (x *LogData) SetSlideId(v int32) {
	if x != nil {
		x.SlideId = &v
	}
}

func (x *LogData) SetTraceId(v string) {
	if x != nil {
		x.TraceId = &v
	}
}

func (x *LogData) SetType(v string) {
	if x != nil {
		x.Type = &v
	}
}

func (x *LogData) SetUid(v int64) {
	if x != nil {
		x.Uid = &v
	}
}

func (x *LogData) SetUnitId(v string) {
	if x != nil {
		x.UnitId = &v
	}
}

func (x *LogData) SetWorkFlowId(v string) {
	if x != nil {
		x.WorkFlowId = &v
	}
}

func (p *LogData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LogData(%+v)", *p)
}

type SFCModifyEstimateRequest struct {
	Oid            string  `json:"oid" form:"oid"`
	DepartureRange []int64 `json:"departure_range,omitempty" form:"departure_range"` //出发时间段
	Token          string  `json:"token" form:"token"`                               //出发时间段
	PassengerNums  *int32  `json:"passenger_nums,omitempty" form:"passenger_nums"`
	AccessKeyId    int32   `json:"access_key_id" form:"access_key_id"`
	AppVersion     string  `json:"app_version" form:"app_version"`
}

func (x *SFCModifyEstimateRequest) GetOid() (r string) {
	if x != nil {
		return x.Oid
	}
	return r
}

func (x *SFCModifyEstimateRequest) GetDepartureRange() (r []int64) {
	if x != nil {
		return x.DepartureRange
	}
	return r
}

func (x *SFCModifyEstimateRequest) GetToken() (r string) {
	if x != nil {
		return x.Token
	}
	return r
}

func (x *SFCModifyEstimateRequest) GetPassengerNums() (r int32) {
	if x != nil && x.PassengerNums != nil {
		return *x.PassengerNums
	}
	return r
}

func (x *SFCModifyEstimateRequest) GetAccessKeyId() (r int32) {
	if x != nil {
		return x.AccessKeyId
	}
	return r
}

func (x *SFCModifyEstimateRequest) GetAppVersion() (r string) {
	if x != nil {
		return x.AppVersion
	}
	return r
}

func (x *SFCModifyEstimateRequest) SetOid(v string) {
	if x != nil {
		x.Oid = v
	}
}

func (x *SFCModifyEstimateRequest) SetDepartureRange(v []int64) {
	if x != nil {
		x.DepartureRange = v
	}
}

func (x *SFCModifyEstimateRequest) SetToken(v string) {
	if x != nil {
		x.Token = v
	}
}

func (x *SFCModifyEstimateRequest) SetPassengerNums(v int32) {
	if x != nil {
		x.PassengerNums = &v
	}
}

func (x *SFCModifyEstimateRequest) SetAccessKeyId(v int32) {
	if x != nil {
		x.AccessKeyId = v
	}
}

func (x *SFCModifyEstimateRequest) SetAppVersion(v string) {
	if x != nil {
		x.AppVersion = v
	}
}

func (p *SFCModifyEstimateRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCModifyEstimateRequest(%+v)", *p)
}

type SFCModifyEstimateResponse struct {
	Errno   int32                  `json:"errno" form:"errno"`
	Errmsg  string                 `json:"errmsg" form:"errmsg"`
	Data    *SFCModifyEstimateData `json:"data,omitempty" form:"data"`
	TraceId string                 `json:"trace_id" form:"trace_id"`
}

func (x *SFCModifyEstimateResponse) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *SFCModifyEstimateResponse) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *SFCModifyEstimateResponse) GetData() (r *SFCModifyEstimateData) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *SFCModifyEstimateResponse) GetTraceId() (r string) {
	if x != nil {
		return x.TraceId
	}
	return r
}

func (x *SFCModifyEstimateResponse) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *SFCModifyEstimateResponse) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *SFCModifyEstimateResponse) SetData(v *SFCModifyEstimateData) {
	if x != nil {
		x.Data = v
	}
}

func (x *SFCModifyEstimateResponse) SetTraceId(v string) {
	if x != nil {
		x.TraceId = v
	}
}

func (p *SFCModifyEstimateResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCModifyEstimateResponse(%+v)", *p)
}

type SFCModifyEstimateData struct {
	EstimateId string    `json:"estimate_id" form:"estimate_id"`
	PriceList  []float64 `json:"price_list,omitempty" form:"price_list"` //出发时间段
}

func (x *SFCModifyEstimateData) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *SFCModifyEstimateData) GetPriceList() (r []float64) {
	if x != nil {
		return x.PriceList
	}
	return r
}

func (x *SFCModifyEstimateData) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *SFCModifyEstimateData) SetPriceList(v []float64) {
	if x != nil {
		x.PriceList = v
	}
}

func (p *SFCModifyEstimateData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCModifyEstimateData(%+v)", *p)
}

type SFCSimpleEstimateReq struct {
	Uid            int64    `json:"uid" form:"uid"`
	AccessKeyId    int32    `json:"access_key_id" form:"access_key_id"`
	AppVersion     string   `json:"app_version" form:"app_version"`
	Lat            *float64 `json:"lat,omitempty" form:"lat"`
	Lng            *float64 `json:"lng,omitempty" form:"lng"`
	MapType        string   `json:"map_type" form:"map_type"`
	OrderType      int32    `json:"order_type" form:"order_type"`
	FromLat        float64  `json:"from_lat" form:"from_lat"`
	FromLng        float64  `json:"from_lng" form:"from_lng"`
	FromPoiId      string   `json:"from_poi_id" form:"from_poi_id"`
	FromPoiType    *string  `json:"from_poi_type,omitempty" form:"from_poi_type"`
	FromAddress    *string  `json:"from_address,omitempty" form:"from_address"`
	FromName       string   `json:"from_name" form:"from_name"`
	ToLat          float64  `json:"to_lat" form:"to_lat"`
	ToLng          float64  `json:"to_lng" form:"to_lng"`
	ToPoiId        string   `json:"to_poi_id" form:"to_poi_id"`
	ToPoiType      *string  `json:"to_poi_type,omitempty" form:"to_poi_type"`
	ToAddress      *string  `json:"to_address,omitempty" form:"to_address"`
	ToName         string   `json:"to_name" form:"to_name"`
	DepartureTime  *int64   `json:"departure_time,omitempty" form:"departure_time"`   //出发时间戳
	DepartureRange []int64  `json:"departure_range,omitempty" form:"departure_range"` //出发时间段
	PassengerNums  *int32   `json:"passenger_nums,omitempty" form:"passenger_nums"`   //乘车人数
	FromArea       int32    `json:"from_area" form:"from_area"`                       //乘车人数
	ToArea         int32    `json:"to_area" form:"to_area"`
	Phone          *string  `json:"phone,omitempty" form:"phone"`
	UserChannel    *string  `json:"user_channel,omitempty" form:"user_channel"`
	Channel        *int64   `json:"channel,omitempty" form:"channel"`
}

func (x *SFCSimpleEstimateReq) GetUid() (r int64) {
	if x != nil {
		return x.Uid
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetAccessKeyId() (r int32) {
	if x != nil {
		return x.AccessKeyId
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetAppVersion() (r string) {
	if x != nil {
		return x.AppVersion
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetLat() (r float64) {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetLng() (r float64) {
	if x != nil && x.Lng != nil {
		return *x.Lng
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetMapType() (r string) {
	if x != nil {
		return x.MapType
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetOrderType() (r int32) {
	if x != nil {
		return x.OrderType
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetFromLat() (r float64) {
	if x != nil {
		return x.FromLat
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetFromLng() (r float64) {
	if x != nil {
		return x.FromLng
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetFromPoiId() (r string) {
	if x != nil {
		return x.FromPoiId
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetFromPoiType() (r string) {
	if x != nil && x.FromPoiType != nil {
		return *x.FromPoiType
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetFromAddress() (r string) {
	if x != nil && x.FromAddress != nil {
		return *x.FromAddress
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetFromName() (r string) {
	if x != nil {
		return x.FromName
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetToLat() (r float64) {
	if x != nil {
		return x.ToLat
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetToLng() (r float64) {
	if x != nil {
		return x.ToLng
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetToPoiId() (r string) {
	if x != nil {
		return x.ToPoiId
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetToPoiType() (r string) {
	if x != nil && x.ToPoiType != nil {
		return *x.ToPoiType
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetToAddress() (r string) {
	if x != nil && x.ToAddress != nil {
		return *x.ToAddress
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetToName() (r string) {
	if x != nil {
		return x.ToName
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetDepartureTime() (r int64) {
	if x != nil && x.DepartureTime != nil {
		return *x.DepartureTime
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetDepartureRange() (r []int64) {
	if x != nil {
		return x.DepartureRange
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetPassengerNums() (r int32) {
	if x != nil && x.PassengerNums != nil {
		return *x.PassengerNums
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetFromArea() (r int32) {
	if x != nil {
		return x.FromArea
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetToArea() (r int32) {
	if x != nil {
		return x.ToArea
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetPhone() (r string) {
	if x != nil && x.Phone != nil {
		return *x.Phone
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetUserChannel() (r string) {
	if x != nil && x.UserChannel != nil {
		return *x.UserChannel
	}
	return r
}

func (x *SFCSimpleEstimateReq) GetChannel() (r int64) {
	if x != nil && x.Channel != nil {
		return *x.Channel
	}
	return r
}

func (x *SFCSimpleEstimateReq) SetUid(v int64) {
	if x != nil {
		x.Uid = v
	}
}

func (x *SFCSimpleEstimateReq) SetAccessKeyId(v int32) {
	if x != nil {
		x.AccessKeyId = v
	}
}

func (x *SFCSimpleEstimateReq) SetAppVersion(v string) {
	if x != nil {
		x.AppVersion = v
	}
}

func (x *SFCSimpleEstimateReq) SetLat(v float64) {
	if x != nil {
		x.Lat = &v
	}
}

func (x *SFCSimpleEstimateReq) SetLng(v float64) {
	if x != nil {
		x.Lng = &v
	}
}

func (x *SFCSimpleEstimateReq) SetMapType(v string) {
	if x != nil {
		x.MapType = v
	}
}

func (x *SFCSimpleEstimateReq) SetOrderType(v int32) {
	if x != nil {
		x.OrderType = v
	}
}

func (x *SFCSimpleEstimateReq) SetFromLat(v float64) {
	if x != nil {
		x.FromLat = v
	}
}

func (x *SFCSimpleEstimateReq) SetFromLng(v float64) {
	if x != nil {
		x.FromLng = v
	}
}

func (x *SFCSimpleEstimateReq) SetFromPoiId(v string) {
	if x != nil {
		x.FromPoiId = v
	}
}

func (x *SFCSimpleEstimateReq) SetFromPoiType(v string) {
	if x != nil {
		x.FromPoiType = &v
	}
}

func (x *SFCSimpleEstimateReq) SetFromAddress(v string) {
	if x != nil {
		x.FromAddress = &v
	}
}

func (x *SFCSimpleEstimateReq) SetFromName(v string) {
	if x != nil {
		x.FromName = v
	}
}

func (x *SFCSimpleEstimateReq) SetToLat(v float64) {
	if x != nil {
		x.ToLat = v
	}
}

func (x *SFCSimpleEstimateReq) SetToLng(v float64) {
	if x != nil {
		x.ToLng = v
	}
}

func (x *SFCSimpleEstimateReq) SetToPoiId(v string) {
	if x != nil {
		x.ToPoiId = v
	}
}

func (x *SFCSimpleEstimateReq) SetToPoiType(v string) {
	if x != nil {
		x.ToPoiType = &v
	}
}

func (x *SFCSimpleEstimateReq) SetToAddress(v string) {
	if x != nil {
		x.ToAddress = &v
	}
}

func (x *SFCSimpleEstimateReq) SetToName(v string) {
	if x != nil {
		x.ToName = v
	}
}

func (x *SFCSimpleEstimateReq) SetDepartureTime(v int64) {
	if x != nil {
		x.DepartureTime = &v
	}
}

func (x *SFCSimpleEstimateReq) SetDepartureRange(v []int64) {
	if x != nil {
		x.DepartureRange = v
	}
}

func (x *SFCSimpleEstimateReq) SetPassengerNums(v int32) {
	if x != nil {
		x.PassengerNums = &v
	}
}

func (x *SFCSimpleEstimateReq) SetFromArea(v int32) {
	if x != nil {
		x.FromArea = v
	}
}

func (x *SFCSimpleEstimateReq) SetToArea(v int32) {
	if x != nil {
		x.ToArea = v
	}
}

func (x *SFCSimpleEstimateReq) SetPhone(v string) {
	if x != nil {
		x.Phone = &v
	}
}

func (x *SFCSimpleEstimateReq) SetUserChannel(v string) {
	if x != nil {
		x.UserChannel = &v
	}
}

func (x *SFCSimpleEstimateReq) SetChannel(v int64) {
	if x != nil {
		x.Channel = &v
	}
}

func (p *SFCSimpleEstimateReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCSimpleEstimateReq(%+v)", *p)
}

func (p *SFCSimpleEstimateReq) Validate() error {
	if !(p.GetOrderType() > 0) {
		return fmt.Errorf("field %s of struct %s validate failed", "OrderType", "SFCSimpleEstimateReq")
	}
	if !(len(p.GetFromPoiId()) > 0) {
		return fmt.Errorf("field %s of struct %s validate failed", "FromPoiId", "SFCSimpleEstimateReq")
	}
	if !(len(p.GetFromName()) > 0) {
		return fmt.Errorf("field %s of struct %s validate failed", "FromName", "SFCSimpleEstimateReq")
	}
	if !(len(p.GetToPoiId()) > 0) {
		return fmt.Errorf("field %s of struct %s validate failed", "ToPoiId", "SFCSimpleEstimateReq")
	}
	if !(len(p.GetToName()) > 0) {
		return fmt.Errorf("field %s of struct %s validate failed", "ToName", "SFCSimpleEstimateReq")
	}
	return nil
}

type SFCSimpleEstimatePriceInfo struct {
	EstimateCarpoolFailPrice float64 `json:"estimate_carpool_fail_price" form:"estimate_carpool_fail_price"`
	EstimateCarpoolPrice     float64 `json:"estimate_carpool_price" form:"estimate_carpool_price"`
	EstimateId               string  `json:"estimate_id" form:"estimate_id"`
	ProductCategory          int64   `json:"product_category" form:"product_category"`
}

func (x *SFCSimpleEstimatePriceInfo) GetEstimateCarpoolFailPrice() (r float64) {
	if x != nil {
		return x.EstimateCarpoolFailPrice
	}
	return r
}

func (x *SFCSimpleEstimatePriceInfo) GetEstimateCarpoolPrice() (r float64) {
	if x != nil {
		return x.EstimateCarpoolPrice
	}
	return r
}

func (x *SFCSimpleEstimatePriceInfo) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *SFCSimpleEstimatePriceInfo) GetProductCategory() (r int64) {
	if x != nil {
		return x.ProductCategory
	}
	return r
}

func (x *SFCSimpleEstimatePriceInfo) SetEstimateCarpoolFailPrice(v float64) {
	if x != nil {
		x.EstimateCarpoolFailPrice = v
	}
}

func (x *SFCSimpleEstimatePriceInfo) SetEstimateCarpoolPrice(v float64) {
	if x != nil {
		x.EstimateCarpoolPrice = v
	}
}

func (x *SFCSimpleEstimatePriceInfo) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *SFCSimpleEstimatePriceInfo) SetProductCategory(v int64) {
	if x != nil {
		x.ProductCategory = v
	}
}

func (p *SFCSimpleEstimatePriceInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCSimpleEstimatePriceInfo(%+v)", *p)
}

type SFCSimpleEstimateData struct {
	PriceInfo []*SFCSimpleEstimatePriceInfo `json:"price_info,omitempty" form:"price_info"`
}

func (x *SFCSimpleEstimateData) GetPriceInfo() (r []*SFCSimpleEstimatePriceInfo) {
	if x != nil {
		return x.PriceInfo
	}
	return r
}

func (x *SFCSimpleEstimateData) SetPriceInfo(v []*SFCSimpleEstimatePriceInfo) {
	if x != nil {
		x.PriceInfo = v
	}
}

func (p *SFCSimpleEstimateData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCSimpleEstimateData(%+v)", *p)
}

type SFCSimpleEstimateResponse struct {
	Errno   int32                  `json:"errno" form:"errno"`
	Errmsg  string                 `json:"errmsg" form:"errmsg"`
	Data    *SFCSimpleEstimateData `json:"data,omitempty" form:"data"`
	TraceId string                 `json:"trace_id" form:"trace_id"`
}

func (x *SFCSimpleEstimateResponse) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *SFCSimpleEstimateResponse) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *SFCSimpleEstimateResponse) GetData() (r *SFCSimpleEstimateData) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *SFCSimpleEstimateResponse) GetTraceId() (r string) {
	if x != nil {
		return x.TraceId
	}
	return r
}

func (x *SFCSimpleEstimateResponse) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *SFCSimpleEstimateResponse) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *SFCSimpleEstimateResponse) SetData(v *SFCSimpleEstimateData) {
	if x != nil {
		x.Data = v
	}
}

func (x *SFCSimpleEstimateResponse) SetTraceId(v string) {
	if x != nil {
		x.TraceId = v
	}
}

func (p *SFCSimpleEstimateResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SFCSimpleEstimateResponse(%+v)", *p)
}
