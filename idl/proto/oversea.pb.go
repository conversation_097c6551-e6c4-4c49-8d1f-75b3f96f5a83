// Code generated by http-gen compiler. DO NOT EDIT.
// source: oversea.idl
// argument: --client_style=protoc

package proto

import (
	"fmt"
	"git.xiaojukeji.com/nuwa/golibs/json"
	"reflect"
)

var _ = fmt.Printf

func init() {
	json.Pretouch(reflect.TypeOf((*OverseaEstimateReq)(nil)))
	json.Pretouch(reflect.TypeOf((*OverseaEstimateResponse)(nil)))
	json.Pretouch(reflect.TypeOf((*OverseaNoCarInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*OverseaEstimateData)(nil)))
	json.Pretouch(reflect.TypeOf((*OverseaRecommendInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*OverseaThemeData)(nil)))
	json.Pretouch(reflect.TypeOf((*ProtocolInfo)(nil)))
	json.Pretouch(reflect.TypeOf((*ActionParams)(nil)))
	json.Pretouch(reflect.TypeOf((*OverseaEstimateCard)(nil)))
}

type OverseaEstimateReq struct {
	Token               string  `json:"token" form:"token"` //用户认证token
	AccessKeyId         int32   `json:"access_key_id" form:"access_key_id"`
	Appversion          string  `json:"appversion" form:"appversion"`
	Channel             string  `json:"channel" form:"channel"`
	ClientType          int32   `json:"client_type" form:"client_type"`
	Lang                string  `json:"lang" form:"lang"`
	MapType             string  `json:"map_type" form:"map_type"`
	TerminalId          int64   `json:"terminal_id" form:"terminal_id"`
	PlatformType        int32   `json:"platform_type" form:"platform_type"`
	Ddfp                *string `json:"ddfp,omitempty" form:"ddfp"`
	FromArea            int32   `json:"from_area" form:"from_area"` //起点
	FromLat             float64 `json:"from_lat" form:"from_lat"`   //起点
	FromLng             float64 `json:"from_lng" form:"from_lng"`
	FromPoiId           string  `json:"from_poi_id" form:"from_poi_id"`
	FromPoiType         string  `json:"from_poi_type" form:"from_poi_type"`
	FromAddress         string  `json:"from_address" form:"from_address"`
	FromName            string  `json:"from_name" form:"from_name"`
	ToArea              int32   `json:"to_area" form:"to_area"` //终点
	ToLat               float64 `json:"to_lat" form:"to_lat"`   //终点
	ToLng               float64 `json:"to_lng" form:"to_lng"`
	ToPoiId             string  `json:"to_poi_id" form:"to_poi_id"`
	ToPoiType           string  `json:"to_poi_type" form:"to_poi_type"`
	ToAddress           string  `json:"to_address" form:"to_address"`
	ToName              string  `json:"to_name" form:"to_name"`
	Lat                 float64 `json:"lat" form:"lat"`                                               //定位点
	Lng                 float64 `json:"lng" form:"lng"`                                               //定位点
	ChooseFSearchid     string  `json:"choose_f_searchid" form:"choose_f_searchid"`                   //用户选择起点请求ID
	ChooseTSearchid     string  `json:"choose_t_searchid" form:"choose_t_searchid"`                   //用户选择终点请求ID
	MultiRequireProduct *string `json:"multi_require_product,omitempty" form:"multi_require_product"` //用户勾选项
}

func (x *OverseaEstimateReq) GetToken() (r string) {
	if x != nil {
		return x.Token
	}
	return r
}

func (x *OverseaEstimateReq) GetAccessKeyId() (r int32) {
	if x != nil {
		return x.AccessKeyId
	}
	return r
}

func (x *OverseaEstimateReq) GetAppversion() (r string) {
	if x != nil {
		return x.Appversion
	}
	return r
}

func (x *OverseaEstimateReq) GetChannel() (r string) {
	if x != nil {
		return x.Channel
	}
	return r
}

func (x *OverseaEstimateReq) GetClientType() (r int32) {
	if x != nil {
		return x.ClientType
	}
	return r
}

func (x *OverseaEstimateReq) GetLang() (r string) {
	if x != nil {
		return x.Lang
	}
	return r
}

func (x *OverseaEstimateReq) GetMapType() (r string) {
	if x != nil {
		return x.MapType
	}
	return r
}

func (x *OverseaEstimateReq) GetTerminalId() (r int64) {
	if x != nil {
		return x.TerminalId
	}
	return r
}

func (x *OverseaEstimateReq) GetPlatformType() (r int32) {
	if x != nil {
		return x.PlatformType
	}
	return r
}

func (x *OverseaEstimateReq) GetDdfp() (r string) {
	if x != nil && x.Ddfp != nil {
		return *x.Ddfp
	}
	return r
}

func (x *OverseaEstimateReq) GetFromArea() (r int32) {
	if x != nil {
		return x.FromArea
	}
	return r
}

func (x *OverseaEstimateReq) GetFromLat() (r float64) {
	if x != nil {
		return x.FromLat
	}
	return r
}

func (x *OverseaEstimateReq) GetFromLng() (r float64) {
	if x != nil {
		return x.FromLng
	}
	return r
}

func (x *OverseaEstimateReq) GetFromPoiId() (r string) {
	if x != nil {
		return x.FromPoiId
	}
	return r
}

func (x *OverseaEstimateReq) GetFromPoiType() (r string) {
	if x != nil {
		return x.FromPoiType
	}
	return r
}

func (x *OverseaEstimateReq) GetFromAddress() (r string) {
	if x != nil {
		return x.FromAddress
	}
	return r
}

func (x *OverseaEstimateReq) GetFromName() (r string) {
	if x != nil {
		return x.FromName
	}
	return r
}

func (x *OverseaEstimateReq) GetToArea() (r int32) {
	if x != nil {
		return x.ToArea
	}
	return r
}

func (x *OverseaEstimateReq) GetToLat() (r float64) {
	if x != nil {
		return x.ToLat
	}
	return r
}

func (x *OverseaEstimateReq) GetToLng() (r float64) {
	if x != nil {
		return x.ToLng
	}
	return r
}

func (x *OverseaEstimateReq) GetToPoiId() (r string) {
	if x != nil {
		return x.ToPoiId
	}
	return r
}

func (x *OverseaEstimateReq) GetToPoiType() (r string) {
	if x != nil {
		return x.ToPoiType
	}
	return r
}

func (x *OverseaEstimateReq) GetToAddress() (r string) {
	if x != nil {
		return x.ToAddress
	}
	return r
}

func (x *OverseaEstimateReq) GetToName() (r string) {
	if x != nil {
		return x.ToName
	}
	return r
}

func (x *OverseaEstimateReq) GetLat() (r float64) {
	if x != nil {
		return x.Lat
	}
	return r
}

func (x *OverseaEstimateReq) GetLng() (r float64) {
	if x != nil {
		return x.Lng
	}
	return r
}

func (x *OverseaEstimateReq) GetChooseFSearchid() (r string) {
	if x != nil {
		return x.ChooseFSearchid
	}
	return r
}

func (x *OverseaEstimateReq) GetChooseTSearchid() (r string) {
	if x != nil {
		return x.ChooseTSearchid
	}
	return r
}

func (x *OverseaEstimateReq) GetMultiRequireProduct() (r string) {
	if x != nil && x.MultiRequireProduct != nil {
		return *x.MultiRequireProduct
	}
	return r
}

func (x *OverseaEstimateReq) SetToken(v string) {
	if x != nil {
		x.Token = v
	}
}

func (x *OverseaEstimateReq) SetAccessKeyId(v int32) {
	if x != nil {
		x.AccessKeyId = v
	}
}

func (x *OverseaEstimateReq) SetAppversion(v string) {
	if x != nil {
		x.Appversion = v
	}
}

func (x *OverseaEstimateReq) SetChannel(v string) {
	if x != nil {
		x.Channel = v
	}
}

func (x *OverseaEstimateReq) SetClientType(v int32) {
	if x != nil {
		x.ClientType = v
	}
}

func (x *OverseaEstimateReq) SetLang(v string) {
	if x != nil {
		x.Lang = v
	}
}

func (x *OverseaEstimateReq) SetMapType(v string) {
	if x != nil {
		x.MapType = v
	}
}

func (x *OverseaEstimateReq) SetTerminalId(v int64) {
	if x != nil {
		x.TerminalId = v
	}
}

func (x *OverseaEstimateReq) SetPlatformType(v int32) {
	if x != nil {
		x.PlatformType = v
	}
}

func (x *OverseaEstimateReq) SetDdfp(v string) {
	if x != nil {
		x.Ddfp = &v
	}
}

func (x *OverseaEstimateReq) SetFromArea(v int32) {
	if x != nil {
		x.FromArea = v
	}
}

func (x *OverseaEstimateReq) SetFromLat(v float64) {
	if x != nil {
		x.FromLat = v
	}
}

func (x *OverseaEstimateReq) SetFromLng(v float64) {
	if x != nil {
		x.FromLng = v
	}
}

func (x *OverseaEstimateReq) SetFromPoiId(v string) {
	if x != nil {
		x.FromPoiId = v
	}
}

func (x *OverseaEstimateReq) SetFromPoiType(v string) {
	if x != nil {
		x.FromPoiType = v
	}
}

func (x *OverseaEstimateReq) SetFromAddress(v string) {
	if x != nil {
		x.FromAddress = v
	}
}

func (x *OverseaEstimateReq) SetFromName(v string) {
	if x != nil {
		x.FromName = v
	}
}

func (x *OverseaEstimateReq) SetToArea(v int32) {
	if x != nil {
		x.ToArea = v
	}
}

func (x *OverseaEstimateReq) SetToLat(v float64) {
	if x != nil {
		x.ToLat = v
	}
}

func (x *OverseaEstimateReq) SetToLng(v float64) {
	if x != nil {
		x.ToLng = v
	}
}

func (x *OverseaEstimateReq) SetToPoiId(v string) {
	if x != nil {
		x.ToPoiId = v
	}
}

func (x *OverseaEstimateReq) SetToPoiType(v string) {
	if x != nil {
		x.ToPoiType = v
	}
}

func (x *OverseaEstimateReq) SetToAddress(v string) {
	if x != nil {
		x.ToAddress = v
	}
}

func (x *OverseaEstimateReq) SetToName(v string) {
	if x != nil {
		x.ToName = v
	}
}

func (x *OverseaEstimateReq) SetLat(v float64) {
	if x != nil {
		x.Lat = v
	}
}

func (x *OverseaEstimateReq) SetLng(v float64) {
	if x != nil {
		x.Lng = v
	}
}

func (x *OverseaEstimateReq) SetChooseFSearchid(v string) {
	if x != nil {
		x.ChooseFSearchid = v
	}
}

func (x *OverseaEstimateReq) SetChooseTSearchid(v string) {
	if x != nil {
		x.ChooseTSearchid = v
	}
}

func (x *OverseaEstimateReq) SetMultiRequireProduct(v string) {
	if x != nil {
		x.MultiRequireProduct = &v
	}
}

func (p *OverseaEstimateReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OverseaEstimateReq(%+v)", *p)
}

type OverseaEstimateResponse struct {
	Errno     int32                `json:"errno" form:"errno"`
	Errmsg    string               `json:"errmsg" form:"errmsg"`
	Data      *OverseaEstimateData `json:"data,omitempty" form:"data"`
	NoCarInfo *OverseaNoCarInfo    `json:"no_car_info,omitempty" form:"no_car_info"`
}

func (x *OverseaEstimateResponse) GetErrno() (r int32) {
	if x != nil {
		return x.Errno
	}
	return r
}

func (x *OverseaEstimateResponse) GetErrmsg() (r string) {
	if x != nil {
		return x.Errmsg
	}
	return r
}

func (x *OverseaEstimateResponse) GetData() (r *OverseaEstimateData) {
	if x != nil {
		return x.Data
	}
	return r
}

func (x *OverseaEstimateResponse) GetNoCarInfo() (r *OverseaNoCarInfo) {
	if x != nil {
		return x.NoCarInfo
	}
	return r
}

func (x *OverseaEstimateResponse) SetErrno(v int32) {
	if x != nil {
		x.Errno = v
	}
}

func (x *OverseaEstimateResponse) SetErrmsg(v string) {
	if x != nil {
		x.Errmsg = v
	}
}

func (x *OverseaEstimateResponse) SetData(v *OverseaEstimateData) {
	if x != nil {
		x.Data = v
	}
}

func (x *OverseaEstimateResponse) SetNoCarInfo(v *OverseaNoCarInfo) {
	if x != nil {
		x.NoCarInfo = v
	}
}

func (p *OverseaEstimateResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OverseaEstimateResponse(%+v)", *p)
}

type OverseaNoCarInfo struct {
	Img        string `json:"img" form:"img"`
	BottomText string `json:"bottom_text" form:"bottom_text"`
	ButtonText string `json:"button_text" form:"button_text"`
	ButtonMsg  string `json:"button_msg" form:"button_msg"`
}

func (x *OverseaNoCarInfo) GetImg() (r string) {
	if x != nil {
		return x.Img
	}
	return r
}

func (x *OverseaNoCarInfo) GetBottomText() (r string) {
	if x != nil {
		return x.BottomText
	}
	return r
}

func (x *OverseaNoCarInfo) GetButtonText() (r string) {
	if x != nil {
		return x.ButtonText
	}
	return r
}

func (x *OverseaNoCarInfo) GetButtonMsg() (r string) {
	if x != nil {
		return x.ButtonMsg
	}
	return r
}

func (x *OverseaNoCarInfo) SetImg(v string) {
	if x != nil {
		x.Img = v
	}
}

func (x *OverseaNoCarInfo) SetBottomText(v string) {
	if x != nil {
		x.BottomText = v
	}
}

func (x *OverseaNoCarInfo) SetButtonText(v string) {
	if x != nil {
		x.ButtonText = v
	}
}

func (x *OverseaNoCarInfo) SetButtonMsg(v string) {
	if x != nil {
		x.ButtonMsg = v
	}
}

func (p *OverseaNoCarInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OverseaNoCarInfo(%+v)", *p)
}

type OverseaEstimateData struct {
	EstimateTraceId         string                 `json:"estimate_trace_id" form:"estimate_trace_id"`
	EstimateData            []*OverseaEstimateCard `json:"estimate_data" form:"estimate_data"`                           //普通品类数据
	BottomText              string                 `json:"bottom_text" form:"bottom_text"`                               //运营方说明文案（呼叫按钮上方）
	IsSupportMultiSelection int32                  `json:"is_support_multi_selection" form:"is_support_multi_selection"` //是否支持多勾
	FeeDetailUrl            string                 `json:"fee_detail_url" form:"fee_detail_url"`                         //费用详情
	ButtonText              string                 `json:"button_text" form:"button_text"`                               //呼叫按钮文案
	ProtocolInfo            *ProtocolInfo          `json:"protocol_info,omitempty" form:"protocol_info"`                 //授权提示
	RecommendInfo           *OverseaRecommendInfo  `json:"recommend_info,omitempty" form:"recommend_info"`               //包框置顶品类数据
}

func (x *OverseaEstimateData) GetEstimateTraceId() (r string) {
	if x != nil {
		return x.EstimateTraceId
	}
	return r
}

func (x *OverseaEstimateData) GetEstimateData() (r []*OverseaEstimateCard) {
	if x != nil {
		return x.EstimateData
	}
	return r
}

func (x *OverseaEstimateData) GetBottomText() (r string) {
	if x != nil {
		return x.BottomText
	}
	return r
}

func (x *OverseaEstimateData) GetIsSupportMultiSelection() (r int32) {
	if x != nil {
		return x.IsSupportMultiSelection
	}
	return r
}

func (x *OverseaEstimateData) GetFeeDetailUrl() (r string) {
	if x != nil {
		return x.FeeDetailUrl
	}
	return r
}

func (x *OverseaEstimateData) GetButtonText() (r string) {
	if x != nil {
		return x.ButtonText
	}
	return r
}

func (x *OverseaEstimateData) GetProtocolInfo() (r *ProtocolInfo) {
	if x != nil {
		return x.ProtocolInfo
	}
	return r
}

func (x *OverseaEstimateData) GetRecommendInfo() (r *OverseaRecommendInfo) {
	if x != nil {
		return x.RecommendInfo
	}
	return r
}

func (x *OverseaEstimateData) SetEstimateTraceId(v string) {
	if x != nil {
		x.EstimateTraceId = v
	}
}

func (x *OverseaEstimateData) SetEstimateData(v []*OverseaEstimateCard) {
	if x != nil {
		x.EstimateData = v
	}
}

func (x *OverseaEstimateData) SetBottomText(v string) {
	if x != nil {
		x.BottomText = v
	}
}

func (x *OverseaEstimateData) SetIsSupportMultiSelection(v int32) {
	if x != nil {
		x.IsSupportMultiSelection = v
	}
}

func (x *OverseaEstimateData) SetFeeDetailUrl(v string) {
	if x != nil {
		x.FeeDetailUrl = v
	}
}

func (x *OverseaEstimateData) SetButtonText(v string) {
	if x != nil {
		x.ButtonText = v
	}
}

func (x *OverseaEstimateData) SetProtocolInfo(v *ProtocolInfo) {
	if x != nil {
		x.ProtocolInfo = v
	}
}

func (x *OverseaEstimateData) SetRecommendInfo(v *OverseaRecommendInfo) {
	if x != nil {
		x.RecommendInfo = v
	}
}

func (p *OverseaEstimateData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OverseaEstimateData(%+v)", *p)
}

type OverseaRecommendInfo struct {
	ThemeData    *OverseaThemeData      `json:"theme_data,omitempty" form:"theme_data"` //包框信息
	EstimateData []*OverseaEstimateCard `json:"estimate_data" form:"estimate_data"`     //品类数据
}

func (x *OverseaRecommendInfo) GetThemeData() (r *OverseaThemeData) {
	if x != nil {
		return x.ThemeData
	}
	return r
}

func (x *OverseaRecommendInfo) GetEstimateData() (r []*OverseaEstimateCard) {
	if x != nil {
		return x.EstimateData
	}
	return r
}

func (x *OverseaRecommendInfo) SetThemeData(v *OverseaThemeData) {
	if x != nil {
		x.ThemeData = v
	}
}

func (x *OverseaRecommendInfo) SetEstimateData(v []*OverseaEstimateCard) {
	if x != nil {
		x.EstimateData = v
	}
}

func (p *OverseaRecommendInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OverseaRecommendInfo(%+v)", *p)
}

type OverseaThemeData struct {
	BgGradients []string `json:"bg_gradients" form:"bg_gradients"` //背景色（支持渐变）
	Title       string   `json:"title" form:"title"`               //主标题
	Icon        string   `json:"icon" form:"icon"`                 //主标题
	BorderColor string   `json:"border_color" form:"border_color"` //边框颜色
}

func (x *OverseaThemeData) GetBgGradients() (r []string) {
	if x != nil {
		return x.BgGradients
	}
	return r
}

func (x *OverseaThemeData) GetTitle() (r string) {
	if x != nil {
		return x.Title
	}
	return r
}

func (x *OverseaThemeData) GetIcon() (r string) {
	if x != nil {
		return x.Icon
	}
	return r
}

func (x *OverseaThemeData) GetBorderColor() (r string) {
	if x != nil {
		return x.BorderColor
	}
	return r
}

func (x *OverseaThemeData) SetBgGradients(v []string) {
	if x != nil {
		x.BgGradients = v
	}
}

func (x *OverseaThemeData) SetTitle(v string) {
	if x != nil {
		x.Title = v
	}
}

func (x *OverseaThemeData) SetIcon(v string) {
	if x != nil {
		x.Icon = v
	}
}

func (x *OverseaThemeData) SetBorderColor(v string) {
	if x != nil {
		x.BorderColor = v
	}
}

func (p *OverseaThemeData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OverseaThemeData(%+v)", *p)
}

type ProtocolInfo struct {
	ProtocolText string        `json:"protocol_text" form:"protocol_text"`           //授权提示文案
	Link         string        `json:"link" form:"link"`                             //跳转链接
	ActionParams *ActionParams `json:"action_params,omitempty" form:"action_params"` //端上透传参数
}

func (x *ProtocolInfo) GetProtocolText() (r string) {
	if x != nil {
		return x.ProtocolText
	}
	return r
}

func (x *ProtocolInfo) GetLink() (r string) {
	if x != nil {
		return x.Link
	}
	return r
}

func (x *ProtocolInfo) GetActionParams() (r *ActionParams) {
	if x != nil {
		return x.ActionParams
	}
	return r
}

func (x *ProtocolInfo) SetProtocolText(v string) {
	if x != nil {
		x.ProtocolText = v
	}
}

func (x *ProtocolInfo) SetLink(v string) {
	if x != nil {
		x.Link = v
	}
}

func (x *ProtocolInfo) SetActionParams(v *ActionParams) {
	if x != nil {
		x.ActionParams = v
	}
}

func (p *ProtocolInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProtocolInfo(%+v)", *p)
}

type ActionParams struct {
	OverseaIsDefaultAuth int32 `json:"oversea_is_default_auth" form:"oversea_is_default_auth"` //是否默认授权，1-默认授权
}

func (x *ActionParams) GetOverseaIsDefaultAuth() (r int32) {
	if x != nil {
		return x.OverseaIsDefaultAuth
	}
	return r
}

func (x *ActionParams) SetOverseaIsDefaultAuth(v int32) {
	if x != nil {
		x.OverseaIsDefaultAuth = v
	}
}

func (p *ActionParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ActionParams(%+v)", *p)
}

type OverseaEstimateCard struct {
	EstimateId      string            `json:"estimate_id" form:"estimate_id"`           //预估id
	ProductCategory int64             `json:"product_category" form:"product_category"` //品类id
	ExtraMap        *ProductNTuple    `json:"extra_map,omitempty" form:"extra_map"`     //品类n元组
	FeeAmount       string            `json:"fee_amount" form:"fee_amount"`             //费用
	FeeMsg          string            `json:"fee_msg" form:"fee_msg"`
	CarTitle        string            `json:"car_title" form:"car_title"`           //渲染
	SubTitleList    []*SubTitle       `json:"sub_title_list" form:"sub_title_list"` //字段废弃，改用car_tag_list
	CarIcon         string            `json:"car_icon" form:"car_icon"`             //车型分类（经济5座）
	IsSelected      int32             `json:"is_selected" form:"is_selected"`       //车型分类（经济5座）
	FeeDescList     []*NewFormFeeDesc `json:"fee_desc_list,omitempty" form:"fee_desc_list"`
	CarTagList      []*SubTitle       `json:"car_tag_list" form:"car_tag_list"`
}

func (x *OverseaEstimateCard) GetEstimateId() (r string) {
	if x != nil {
		return x.EstimateId
	}
	return r
}

func (x *OverseaEstimateCard) GetProductCategory() (r int64) {
	if x != nil {
		return x.ProductCategory
	}
	return r
}

func (x *OverseaEstimateCard) GetExtraMap() (r *ProductNTuple) {
	if x != nil {
		return x.ExtraMap
	}
	return r
}

func (x *OverseaEstimateCard) GetFeeAmount() (r string) {
	if x != nil {
		return x.FeeAmount
	}
	return r
}

func (x *OverseaEstimateCard) GetFeeMsg() (r string) {
	if x != nil {
		return x.FeeMsg
	}
	return r
}

func (x *OverseaEstimateCard) GetCarTitle() (r string) {
	if x != nil {
		return x.CarTitle
	}
	return r
}

func (x *OverseaEstimateCard) GetSubTitleList() (r []*SubTitle) {
	if x != nil {
		return x.SubTitleList
	}
	return r
}

func (x *OverseaEstimateCard) GetCarIcon() (r string) {
	if x != nil {
		return x.CarIcon
	}
	return r
}

func (x *OverseaEstimateCard) GetIsSelected() (r int32) {
	if x != nil {
		return x.IsSelected
	}
	return r
}

func (x *OverseaEstimateCard) GetFeeDescList() (r []*NewFormFeeDesc) {
	if x != nil {
		return x.FeeDescList
	}
	return r
}

func (x *OverseaEstimateCard) GetCarTagList() (r []*SubTitle) {
	if x != nil {
		return x.CarTagList
	}
	return r
}

func (x *OverseaEstimateCard) SetEstimateId(v string) {
	if x != nil {
		x.EstimateId = v
	}
}

func (x *OverseaEstimateCard) SetProductCategory(v int64) {
	if x != nil {
		x.ProductCategory = v
	}
}

func (x *OverseaEstimateCard) SetExtraMap(v *ProductNTuple) {
	if x != nil {
		x.ExtraMap = v
	}
}

func (x *OverseaEstimateCard) SetFeeAmount(v string) {
	if x != nil {
		x.FeeAmount = v
	}
}

func (x *OverseaEstimateCard) SetFeeMsg(v string) {
	if x != nil {
		x.FeeMsg = v
	}
}

func (x *OverseaEstimateCard) SetCarTitle(v string) {
	if x != nil {
		x.CarTitle = v
	}
}

func (x *OverseaEstimateCard) SetSubTitleList(v []*SubTitle) {
	if x != nil {
		x.SubTitleList = v
	}
}

func (x *OverseaEstimateCard) SetCarIcon(v string) {
	if x != nil {
		x.CarIcon = v
	}
}

func (x *OverseaEstimateCard) SetIsSelected(v int32) {
	if x != nil {
		x.IsSelected = v
	}
}

func (x *OverseaEstimateCard) SetFeeDescList(v []*NewFormFeeDesc) {
	if x != nil {
		x.FeeDescList = v
	}
}

func (x *OverseaEstimateCard) SetCarTagList(v []*SubTitle) {
	if x != nil {
		x.CarTagList = v
	}
}

func (p *OverseaEstimateCard) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OverseaEstimateCard(%+v)", *p)
}
