namespace php Dirpc.SDK.Mamba
namespace go Dirpc.SDK.Mamba

struct StationBusInsteadOrderEstimateReq {
    1: required string      token                 // 用户认证token
    2: required string      lang                     // 端语种
    3: required i32         access_key_id            // 端来源
    4: required string      app_version              // 端版本
    5: required string      channel         //渠道
    6: required i32         day_time //日期时间戳
    7: required i64        start_station_id           //起点站点id
    8: required i64        end_station_id //终点站点id
    9: required i32        start_city //起点城市id
    10: required i32        end_city //终点城市id
    11: required i32        start_county_id  // 起点区县ID
    12: required i32        end_county_id // 终点区县ID
    13: required string     shift_id //路线id
    14: required string     passenger_info //乘客信息
    15: required i32        source_id // 来源标识
    16: required i32        scene_type // 场景类型。0：首次进入，1非首次进入
    17: required string     estimate_id // 预估id。本次预估返回的预估id作为下次预估的入参
    18: required i64        product_id // product_id
    19: required string     from_name  // 起点name
    20: required string     to_name    // 终点name
    21: optional i32        from_fence_id // 起点围栏id
    22: optional i32        to_fence_id // 终点围栏id
    23: required double     from_lng
    24: required double     from_lat
    25: required double     to_lng
    26: required double     to_lat
}

struct StationBusInsteadOrderEstimateRsp {
    1: required i32                         errno
    2: required string                      errmsg
    3: required StationBusInsteadOrderEstimateData data
}

struct StationBusInsteadOrderEstimateData {
    1:  required    string                     estimate_trace_id
    2:  required    i32                        mode
    3:  required    BusHeadCard                head_card
    4:  optional    BusSeatInfoCard            seat_info_card
    5:  required    BusRuleCard                rule_card
    6:  required    BusBottomCard              bottom_card
    7:  optional    BusOrderParams             order_params
    8:  required    list<DailyBusShiftList>    daily_shift_list
    9:  required    AlertInfo                  alert_info
}

struct DailyBusShiftList {
    1:  required    i32                 day_time  //零点时间戳
    2:  required    list<ShiftList>     shift_list
}

struct  ShiftList {
    1:  required    string      shift_id
    2:  required    i64         departure_time
    3:  required    i32         remain_seats        //剩余座位数
    4:  required    i32         carry_children_inventory  //剩余携童座位数
    5:  required    bool        child_occupy_seat   //携童是否占座
    6:  required    bool        is_need_verified    //是否实名制
    7:  required    bool        support_child       //是否支持儿童票
    8:  required    bool        support_carry_child       //是否支持携童票
    9:  required    bool        support_coupon_ticket     //是否支持优待票
}

struct BusHeadCard {
    1:  required    string              icon
    2:  required    string              title
    3:  required    string              background_img
    4:  required    BusStationFullInfo     station_info
}

struct BusStationFullInfo {
    1:  required    recentBusServiceShiftId       recent_bus_service_shift_id
    2:  required    BusStationDataInfo     start_station
    3:  required    BusStationDataInfo     end_station
}

struct BusStationDataInfo {
    1:  required    string          display_name
    2:  optional    double          lat
    3:  optional    double          lng
}

struct recentBusServiceShiftId {
    1:  required    string              icon
    2:  required    string              title
    3:  required    string              right_title
    4:  required    BusPopupDetail      popup_detail
}

struct BusPopupDetail {
    1:  required    string              title
    2:  required    list<ShiftIdItem>   bus_service_shift_id_list
    3:  required    string              button_text
}

struct ShiftIdItem {
    1:  required    string              departure_msg
    2:  required    string              inventory_msg
    3:  required    i32                 is_selected
    4:  required    string              shift_id
    5:  required    i32                 inventory_num
}



struct BusSeatInfoCard {
    1:  required    BusRuleLimit   carry_children_limit
    2:  required    BusRuleLimit   passenger_limit
    3: required    i32             max_inventory //最大库存
    4: required    i32             carry_children_max_inventory //携童最大库存
    5: required    list<BusPassengerInfo>            passenger_list //乘客列表
    6: required    SupportTicketTypeInfo            support_ticket_type_info //票类型是否支持
    7: required    string real_name_carry_children_title //实名制携童提示文案
    8: optional    string coupon_ticket_bubble_text      //录入实名制选择优待票类型，或者非实名制选择了优待票，展示的气泡文案
}
struct BusPassengerInfo {
    1:  required    string   title
    2:  required    string   subtitle
    3:  required    string   label
    4: required    i32       number
    5: required    string    iconClass
    6: required    i32       type
    7: optional    string    bubble_text
}
struct SupportTicketTypeInfo {
    1:  required    i32   adult
    2:  required    i32   children
    3:  required    i32   carry_children
    4:  required    i32   coupon_ticket   // 是否支持优待票
}

struct BusRuleLimit {
    1:  required    i32                 max_inventory
    2:  required    string              toast
    3:  required    list<i32>           ticket_types
}

struct BusRuleCard {
    1:  required    list<string>    rule_list
}

struct BusBottomCard {
    1:  required    BusEstimateInfo    estimate_info
}

struct BusEstimateInfo {
    1:  required    string                      estimate_id
    2:  required    string                      fee_amount
    3:  required    string                      fee_msg
    4:  optional    list<TagWithIconAndBorder>  fee_desc_list
    5:  required    string                      fee_detail_url
    6:  required    IntercityNewOrderParam      extra_map
    7:  required    string                      confirm_button_text
    8:  required    i32                         disable
    9:  optional    string                      disable_toast
}


struct BusOrderParams {
    1:  required    string          bus_service_shift_id
    2:  required    string          departure_time
    3:  required    i32             combo_id
}
