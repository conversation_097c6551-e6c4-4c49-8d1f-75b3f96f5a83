namespace php Dirpc.SDK.Mamba
namespace go Dirpc.SDK.Mamba

struct PbdStationBusDetailEstimateReq {
    1: required string      lang                     // 端语种
    2: required i32         access_key_id            // 端来源
    3: required string      app_version              // 端版本
    4: required string      channel         //渠道
    5: required i32         day_time //日期时间戳（当天0点）
    6: required string        estimate_id  // 预估id
    7: required string     passenger_info //乘客信息
    8: optional  string          dchn //投放标识
}

struct PbdStationBusDetailEstimateRsp {
    1: required i32                         errno
    2: required string                      errmsg
    3: required PsbEstimateData data
}

struct PsbEstimateData {
    1:  required    PsbShiftInfo                     shift_info //班次信息
    2:  required    string                     estimate_trace_id//预估traceId
}
struct PsbShiftInfo {
    1:  required    string                     estimate_id //预估id
    2:  required    i64                     estimate_fee //预估金额
    3:  required    i64                     departure_time //发车时间
    4:  required    string                     bus_service_shift_id // 班次id
    5:  required    PbdStationBusStationInfo                station_info //站点明细
    6:  required    i32                     mode // 类型 实名、非实名
    7:  required    list<vote>                    votes // 票列表 1、成人 2 儿童
    8:  required    PsOrderParams                     order_params //发单参数
    9:  required    i64                     product_id // 产品线id
}
struct vote{
    1:  required    i32                     type //票类型
}

struct PsOrderParams {
   1:  required    i64                        route_id //路线id
   2:  required    string                     bus_service_shift_id //班次id
   3:  required    i64                     pool_seat //座位数
   4:  required    PsOrderAreaInfo           origin //起点信息
   5:  required    PsOrderAreaInfo           destination //终点信息
   6:  required    string                     estimate_id //预估id
   7:  required    i64                        departure_time //出发时间
   8:  required    string                     product //品类基本信息
}

struct PsOrderAreaInfo {
   1:  required    string                       lng //纬度
   2:  required    string                       lat //经度
   3:  required    i64                       city_id //城市id
   4:  required    i64                       station_id //站点id
}

//常规查询&改签需要的参数见https://cooper.didichuxing.com/knowledge/share/page/zFmuSbyED56c#页面《02大巴接入珠海》,2.3.3和 2.3.4小节
struct PbdStationBusMultiEstimateReq {
    1: required string      lang                     // 端语种
    2: required i32         access_key_id            // 端来源
    3: required string      app_version              // 端版本
    4: required string      channel         //渠道
    5: required string         biz_scene_type // standard 常规查询  rebook 改签批量查询班次
    6: required i32        start_city //起点城市id
    7: required i32        start_county_id //起点区县id
    8: required i32        start_station_id         //起点站点id
    9: required i32         end_city //终点城市id
    10: required i32        end_county_id //终点区县id
    11: required i32        end_station_id //终点站点id
    12: required i32         day_time //日期时间戳（当天0点）
    13: required string        rebook_pre_order_id //改签原订单号
    14: required string        rebook_ticket_ids // 改签票列表
    15: required string        last_shift_id //本页最后班次id、分页使用
    16: optional  string          dchn //投放标识
    17: required i32        route_id         //路线id
    18: required bool need_station_price // 是否需要同时获取站点定价

}

struct PbdStationBusMultiEstimateRsp {
    1: required i32                         errno
    2: required string                      errmsg
    3: required PbdStationBusMultiEstimateData data
}

struct PbdStationBusMultiEstimateData {
    1:  required    list<ShiftInfo>                     shift_info_list //班次列表
    2:  required    string                     last_shift_id //分页的最后一个班次
    3:  required    bool                     is_last_page //是否最后一页
    4:  required    string                     estimate_trace_id //预估traceId
}
struct ShiftInfo {
    1:  required    string                     estimate_id //预估id
    2:  required    i64                     estimate_fee //预估金额
    3:  required    i64                     departure_time //出发时间
    4:  required    string                     bus_service_shift_id //班次id
    5:  required    PbdStationBusStationInfo                station_info //站点信息
    6:  required    PsOrderParams                     order_params //发单参数、改签发单使用
    9:  required    i64                     product_id // 产品线id
    10: optional    list<PbdStationPriceInfo>  station_price_list // 站点定价
}
struct  PbdStationBusStationInfo {
    1:  required    PbdStationBusStationItem                     start //开始站点信息
    2:  required    PbdStationBusStationItem                     end //结束站点信息
    3:  required    PbdStationBusStationDetail                     detail //站点明细
}

struct PbdStationBusStationItem {
   1:  required    string                     name // 名称
   2:  required    i64                     id //站点id
   3:  required    string                     lat //纬度
   4:  required    string                     lng //经度
   5: required string display_name // 地图 display_name
}

struct PbdStationBusStationDetail {
   1:  required    list<PbdStationBusStationDetailItem> station_list //途径站点列表
   2:  required    i64                     route_id //路线id
   3:  optional    i64                     inventory //库存
}

struct PbdStationBusStationDetailItem {
   1:  required    i64                        id
   2:  required    string                     name //名称
   3:  required    string                     lat //纬度
   4:  required    string                     lng //经度
   5:  required    i64                         type //类型 1上车点 2 下车点
   6:  required    i64                     departure_time //出发时间
   7: required string display_name // 地图 display_name
}

struct PbdStationBusMultiStationPriceReq {
    1: required string      lang                     // 端语种
    2: required i32         access_key_id            // 端来源
    3: required string      app_version              // 端版本
    4: required string      channel         //渠道
    5: required i32         day_time //日期时间戳
    6: required i32        start_city //起点城市id
    7: required i32        end_city //终点城市id
    8: required string        start_city_name //起点城市名称
    9: required string        end_city_name //终点城市名称
    10: optional  string          dchn //投放标识
    11: required i32 start_county_id  // 起点区县ID
    12: required i32 end_county_id // 终点区县ID
}


struct PbdStationBusMultiStationPriceRsp {
    1: required i32                         errno
    2: required string                      errmsg
    3: required StationBusMultiStationPriceData data
}

struct StationBusMultiStationPriceData {
   1: required list<LineSchedule> lineSchedule; //线路列表
   2: required string toCity;//到达城市名称
   3: required string fromCity;//出发城市名称
}

struct StationBasic {
    1: required i32 sequence;//标识站点顺序，值越小离出发站越近
    2: required i32 boardingType;//标识上车点(1)或者下车点(2)
    3: required i32 offsetMinutes;//距离始发点耗时，单位分钟
    4: required string code;//站点唯一标识
    5: required string cityName;//站点所在城市
    6: required string name;//站点名称
    7: optional string address;//站点地址
    8: optional string latitude;//站点纬度
    9: optional string longitude;//站点经度
    10: required bool shuttleService;//此站是否提供接送服务，即上门接或送上门
}

struct SegmentPrice {
    1: required double price;//价格
    2: required string toStationCode;//下车站点唯一标识
    3: required string fromStationCode;//上车站点唯一标识
}

struct LineBasic {
    1: required string id;//路线id
    2: required string name;//路线名称
    3: required i32 fromTimeType;//发车时间类型，1固定时间点发车、2按航班进出港发车、3流水班（如果时刻表中isFlow传的1，代表用户选择发车时间；如果isFlow传的是2，代表是流水班，用户无法选择发车时间）、4固定时间段发车
    4: required i32 timeExpendMinutes;//路程耗时，单位分钟
    5: required list<string> checkTicketMethods;//检票方式，纸质票、短信、身份证、二维码、验票码（数字）
    6: required i32 limitTicketCntMax;//每个订单购买的最多票数，建议填写，不填写默认为10
    7: required list<StationBasic> stations;//线路经过的上车站点或下车站点（最少两个），第一个为始发站，最后一个为终点站，通过Station.sequence字段由小到大排序
}

struct Schedule {
    1: required string scheduleId//班次id
    2: required i32 leftTicketCnt;//余票数量
    3: required string fromTime;//起始站出发时间，发车时间类型为 1、3、4 时必填。 如果为 3、4 时，取 fromDurationBegin 的值填写。
    4: required i32 advanceBookMinutes;//此班次需要至少提前多长时间预定，单位分钟。如果需要超过发车时间后依然可售卖，这里需要传负数，并告知携程有此类需求
    5: required double bottomPrice;//线路票价,根据不同上下车点，这里取最低的票价，至少为1元
    6: required list<SegmentPrice> segmentPrices;//分站点价格，当不同上下车点价格不同时必填。只需要填写与bottomPrice价格不同的线路段
}

struct LineSchedule {
    1: required list<Schedule> schedule;//时刻表
    2: required LineBasic line;//线路信息
}

struct PbdStationBusOrderEstimateReq {
    1: required string      lang                     // 端语种
    2: required i32         access_key_id            // 端来源
    3: required string      app_version              // 端版本
    4: required string      channel         //渠道
    5: required i32         day_time // 出发日期的时间戳
    6: required i32        start_city //起点城市id
    7: required i32        end_city //终点城市id
    8: required i64        start_station_id           //起点站点id
    9: required i64        end_station_id //终点站点id
    10: required string     route_id // 路线id
    11: required string     shift_id //班次id
    12: required string     passenger_info //乘客信息
    13: optional  string          dchn //投放标识
    14: required i32 start_county_id  // 起点区县ID
    15: required i32 end_county_id // 终点区县ID
}

struct PbdStationBusOrderEstimateRsp {
    1: required i32                         errno
    2: required string                      errmsg
    3: required PbdStationBusOrderEstimateData data
}

struct PbdStationBusOrderEstimateData {
    1:  required    string                     estimate_trace_id
    2:  optional    PsoParams             order_params
}
struct PsoParams {
   1:  required    i64                        route_id //路线id
   2:  required    string                     bus_service_shift_id //班次id
   3:  required    i64                     pool_seat //座位数
   4:  required    PsoAreaInfo           origin //起点信息
   5:  required    PsoAreaInfo           destination //终点信息
   6:  required    string                     estimate_id //预估id
   7:  required    i64                        departure_time //出发时间
   8:  required    string                     product //品类基本信息
   9:  required    double                      estimate_fee //预估价，单位：元
}

struct PsoAreaInfo {
   1:  required    string                       lng //纬度
   2:  required    string                       lat //经度
   3:  required    i64                       city_id //城市id
   4:  required    i64                       station_id //站点id
}

struct PbdStationPriceInfo {
    1: required i32 start_station_id
    2: required i32 end_station_id
    3: required i64 price // 站点价格，单位为分
}