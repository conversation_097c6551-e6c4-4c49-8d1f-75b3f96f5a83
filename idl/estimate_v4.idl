namespace php Dirpc.SDK.Mamba
namespace go Dirpc.SDK.Mamba

struct PMultiEstimatePriceV4Request {
    /** 客户端参数**/
    1: required string    token
    2: required string    app_version
    3: required i32       access_key_id
    4: required i64       channel
    5: required i32       client_type
    6: required string    lang
    7: required string    a3_token
    8: required string    pixels
    9: required string    maptype
    10: required string   imei
    11: required string   suuid
    12: required i64   terminal_id
    13: required i64      origin_id
    14: required i32      platform_type
    15: required string   openid
    16: required i32      guide_type
    17: required string   from
    18: required string   preferred_route_id
    19: required string   dialog_id
    20: optional string   source_channel
    21: optional double   screen_scale
    22: optional i32      estimate_style_type // 预估表单样式，0:老样式，1:单行  2双排新表单 3多tab新表单 4:一站式出行
    23: optional i32      route_preference_type   // 路线偏好类型，例如：少附加费、大路优先
    24: optional i64      form_height
    25: optional i32      font_scale_type // 大字模式字段 0:正常 1:大字 2:超大字

    /**起终点相关信息**/
    30: required double     lat
    31: required double     lng
    32: required double     from_lat
    33: required double     from_lng
    34: required string     from_poi_id
    35: required string     from_poi_type
    36: required string     from_poi_code
    37: required string     from_address
    38: required string     from_name
    39: required double     to_lat
    40: required double     to_lng
    41: required string     to_poi_id
    42: required string     to_poi_type
    43: required string     to_address
    44: required string     to_name
    45: required string     dest_poi_code
    47: required string     dest_poi_tag
    48: required string     choose_f_searchid //用户选择起点请求ID
    49: required string     choose_t_searchid //用户选择终点请求ID

    /*订单属性等信息*/
    50: required string     menu_id
    51: required i32        page_type
    52: required i32        call_car_type
    53: required string     call_car_phone
    54: required i32        user_type // 用户类型 0表示普通用户，2表示企业用户
    55: required string     departure_time  //时间戳
    56: required i32        payments_type //支付类型
    57: required string     multi_require_product //用户勾选项
    58: required i32        has_scroll //客户端是否进行过上拉操作
    59: required i32        order_type //预约单
    60: required i32        origin_page_type // 原始入口页面（如预约跳转接送机）
    61: required string     stopover_points //途经点参数 json array 字符串
    62: optional string     tab_list //如果在有tab的场景下，需要告知tab的列表，供Athena使用，V3接口专用
    63: required string     additional_service // 整个预估的附加需求
    65: optional string     preference_filter //用户选择的过滤器id
    66: optional string     tab_id           // v3可能有normal/classify
    67: optional string     preference_filter_id //用户选择的过滤器id delete
    68: optional i32        carpool_seat_num  // 用户选择的拼车座位数
    69: optional string     category_info  //分框折叠态

    /*业务信息*/
    80: optional i32        shake_flag
    81: optional string     pre_trace_id
    82: optional string     departure_range //城际拼车订单出发时间

    /*接送机相关*/
    120: optional string    flight_dep_code // 航班出发地三字码,如CTU
    121: optional string    flight_dep_terminal // 航班出发航站楼，如T2
    122: optional string    traffic_dep_time // 航班起飞时间字符串
    123: optional string    flight_arr_code // 航班落地三字码,如CTU
    124: optional string    flight_arr_terminal //航班落地航站楼，如T2
    125: optional string    traffic_arr_time // 航班到达时间字符串
    126: optional string    traffic_number // 航班号，如CA1405
    127: optional i16       airport_type // 接送机类型 1-接机，2-送机，端上入口，无入口传0
    128: optional i32       airport_id // 接机时为航班落地航站楼id，送机时为出发航站楼id，如1573
    129: optional i32       shift_time // 用车偏移时间（接机时，单位：秒）

    /*活动相关*/
    150: optional i32       activity_id // 活动id，去掉原有x_activity_id

    /*B端车票*/
    155: optional string       biz_ticket // 车票ID

    /*专车相关*/
    160: optional i16       too_far_order_limit // 专车是否限制超远途订单

    /*特殊场景*/
    170: optional i32       special_scene_param // 极端天气场景（如暴雨等）
    171: required i32       from_type //用来标识是否是再来一单场景，3-再来一单
    172: required i32       is_female_driver_first //用来标识夜间场景的女司机优先场景
    173: optional string    guide_trace_id // 导流来源的冒泡trace

    180: optional string    luxury_select_carlevels
    181: optional string    luxury_select_driver
    190: optional string    agent_type

    /*webx公参*/
    191: optional string    xpsid
    192: optional string    xpsid_root

    193: optional string one_stop_version
    194: optional string    diff_status
}

struct NewFormMultiEstimatePriceV4Response {
    1: required i32           errno
    2: required string           errmsg
    3: required string           trace_id
    4: optional NewFormEstimateV4Response data
}

struct NewFormEstimateV4Response {
    1:  required string estimate_trace_id
    2:  required map<i64,V3EstimateData> estimate_data
    3:  required i32 is_support_multi_selection //是否支持多选，预约单时不支持 0-不支持；1-支持
    4:  required string fee_detail_url //费用明细页地址
    5:  optional PluginPageInfo plugin_page_info //动调、春节服务费等发单拦截页
    6:  optional PaymentOptionModule user_pay_info // 6.0支付方式并集
    7:  required list<NewFormLayout> layout //布局
    8:  optional string toast_tip //预估完成后提示文案
    9:  required map<string,string> p_new_order_params //发单参数 预估级别
    
    10: optional AdditionalServiceData additional_service_data // 附加需求信息
    
//    11: optional FilterInfo filter_info // 筛选器信息
    12: required map<string,string> travel_forecast // 行程预测
    13: required list<OperationItem> operation_list
    14: optional list<CategoryData> category_info   // 三方表单侧边栏信息
    15: optional OrderButtonInfo order_button_info  // 发单按钮信息
    16: optional i32 selection_style_type // 预约单是否出新样式：0不出，1 出
    17: optional i32 is_callcar_disabled // 是否屏蔽代叫按钮 1 屏蔽 0 不屏蔽
    18: optional map<string,string> multi_route_tips_data // 提示文案全集
//    19: optional PriceAxle price_axle // 价格轴
    20: optional i32 show_category // 侧边栏展示策略
    21: optional list<NavigationBar> navigation_bar // 新版操作台
    22: optional map<string,i32> real_params // 端请求mamba透传参数
    23: optional map<string,i32> expect_params // 预期信息
   
    24: optional map<string,NewFormGroup> group_data //盒子信息
    25: optional MoreToastTipData more_toast_tip  //新版toast
    26: optional i32 rec_form      // 新推荐表单标识
    27: optional list<NewFormLayout>  rec_layout // 推荐layout
    28: optional i32 form_style_exp // 新style标识
    29: optional map<string,i32> side_params // 端请求mamba透传参数
    30: optional TabExtraData tab_extra_data
    31: optional string fee_msg_template //底部操作台 价格展示模版
    32: optional i32 phone_adaptation // 是否大屏适配
}


struct OperationItem {
    1: required string key
    2: required string title
    3: required string link
}

struct CategoryData {
    1: required i32          category_id
    2: required string       title
    3: required string       sub_title
    4: required string       icon
    5: required list<string> bg_gradients
    6: required i32          is_selected
    7: required string       section_title     //表单上的分组标题
    8: required string       fold_text  //  折叠文案
    9: required i32          is_fold  //是否折叠
    10:required i32          click_need_expand // 点击后是否展开
}

struct NavigationBar {
    1: required string key // 操作台组件唯一标识
    2: optional string title // 展示文案 (仅在某些组件有)
    3: optional string icon // 图标
    4: optional string link // 跳转链接
    5: optional Animation animation // 动画 可不传
    6: optional string highlight_color // 高亮颜色
    7: optional i32 is_highlight // 是否高亮
    8: optional NavigationBarParams params // 点击携带参数
    9: optional map<string,string> popup // 点击弹窗
}

struct NavigationBarParams {
    1: optional string tab_id
    2: optional i32 page_type
}

struct MoreToastTipData {
    1: required string text_color
    2: required string car_icon
    3: required string bg_color
    4: required string border_color
    5: required string text
    6: required string arrow_icon
}

struct AdditionalServiceData {
    1: required string page_title
    2: required string title
    3: required string tips_url
    4: required list<string> sub_title_list
    5: required list<AdditionalService> service_list

    6: required string guide_text
    7: required i32    guide_times
    8: required i32    version
}

struct AdditionalService {
    1: required i32 id // 服务id
    2: required string text // 需要携带宠物
    3: required string icon
    4: required i32 select // 是否勾选 0未勾选 1:已勾选
    5: required string detail
}

struct Animation {
    1: optional string icon // 动画图标
    2: optional string title // 动画文案
}