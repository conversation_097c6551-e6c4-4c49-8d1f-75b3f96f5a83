namespace php Dirpc.SDK.Mamba
namespace go Dirpc.SDK.Mamba

struct BargainEstimateReq {
    1: required string    token                 // 用户认证token
    2: required i32       user_type             // 1普通用户；2企业用户

    3: required string app_version              // 端版本
    4: required i32    access_key_id            // 端来源
    5: required string channel                  // 渠道号
    6: required i32    client_type              // 端类型
    7: required string lang                     // 端语种
    8: required i32    platform_type            // 端(平台)类型-2

   11: required string map_type                 // 地图类型
   12: required double lat                      // 定位点
   13: required double lng
   14: required double from_lat                 // 起点
   15: required double from_lng
   16: required string from_poi_id
   17: required string from_poi_type
   18: required string from_address
   19: required string from_name
   20: required string choose_f_searchid        // 用户选择起点请求ID
   21: required double to_lat                   // 终点
   22: required double to_lng
   23: required string to_poi_id
   24: required string to_poi_type
   25: required string to_address
   26: required string to_name
   27: required string choose_t_searchid        //用户选择终点请求ID
   28: required string  from       // 来源标识：2.0:estimate ,1.0:home。1.0的获取有些问题，需要和前端沟通再使用
   29: required string  order_id //订单号

    /*webx公参*/
   100: optional string    xpsid
   101: optional string    xpsid_root
}






struct BargainSpecialPriceText {
    1: required list<i32> rule_type     //规则
    2: required string text             //文案
}
struct BargainSpecialPriceTextV2 {
    1: optional list<SpecialPriceRuleInfo> rule_info //规则文案信息
}
struct SpecialPriceRuleInfo {
    1: required list<i32> rule_type     //规则
    2: required string text            //文案
}

struct BargainFeeMarginItem {
    1: required string amount           // 具体金额
    2: required string notice           // 提示文案
}

struct BargainFeeMargin {
    1: optional BargainFeeMarginItem ceil       // 上限
    2: optional BargainFeeMarginItem floor1     // 下限
    3: optional BargainFeeMarginItem floor2     // 下下限
}

struct AnycarBargainFeeMargin {
    1: optional BargainFeeMarginItem ceil       // 上限
    2: optional BargainFeeMarginItem floor     // 下限
    3: optional BargainFeeMarginItem lower_floor     // 下下限
}

struct BargainCommentTag {
    1: required i32 id
    2: required string text
}

struct BargainRecommendData {
    1: required string recommend_price
    2: required string recommend_text
    3: required string recommend_right_icon //推荐价右icon
    4: required string recommend_style //推荐价样式 1：光条特效
}
struct AnycarRecommendData {
    1: required string recommend_price
}

struct TopCommunicateData {
    1: required string text
    2: required string img_url
}

struct RetainFrameData {
    1:optional RetainFrameContent coupon
    2:optional RetainFrameContent normal
}
struct RetainFrameContent {
    1:required string title //标题
    2:required string sub_title //子标题
    3:required string bg_img_url //背景图片地址
    4:required string left_button_text //左按钮文案
    5:required string right_button_text //右按钮文案
}

struct BargainEstimateItem {
    1: required string intro_msg // 预估价格和里程文案
    3: required i32 require_level // 车型
    4: required i32 business_id // 业务线
    5: required i32 product_id // 产品线ID
    6: required i32 combo_type // combo_type
    7: required i32 product_category // 品类ID
    8: required string estimate_id // 预估id
    9: required string fee_amount // 单纯价格
    10: optional BargainFeeMargin fee_margin // 费用边际
    11: optional list<BargainCommentTag> comment_tags // 备注标签
    12: optional BargainSpecialPriceText special_price_text // 特殊价格沟通，比如高速费
    13: required string estimate_text // 新页面预估文案
    14: optional list<BargainRecommendData> recommend_data // 推荐价
    15: required TutorialInfo tutorial_info // 新客蒙层样式信息
    16: optional TopCommunicateData  top_communicate_data // 价值透传文案
    17: optional RetainFrameData retain_frame_data //离开挽留弹框文案
    18: optional BargainSpecialPriceTextV2 special_price_text_v2 // 特殊价格沟通V2，比如高速费
    19: optional AnycarBargainFeeMargin anycar_fee_margin // 等待应答费用边际
    20: required double coupon_amount //优惠总金额
    21: optional list<AnycarRecommendData> anycar_recommend_data // 等待应答推荐价
}
struct TutorialInfo {
    1:required string img_url //图片地址
    2:required string text //标题
}
struct BargainEstimateData {
    1: required list<BargainEstimateItem> estimate_data // 预估价格和里程文案
    2: required string fee_detail_url // 费用详情H5
}

struct BargainEstimateRsp {
    1: required i32              errno
    2: required string           errmsg
    3: optional BargainEstimateData data
    4: required string           trace_id
}

struct BargainRangeEstimateReq {
    1: required string    token                 // 用户认证token
    2: required i32       user_type             // 1普通用户；2企业用户

    3: required string app_version              // 端版本
    4: required i32    access_key_id            // 端来源
    5: required string channel                  // 渠道号
    6: required i32    client_type              // 端类型
    7: required string lang                     // 端语种
    8: required i32    platform_type            // 端(平台)类型-2

   11: required string map_type                 // 地图类型
   12: required double lat                      // 定位点
   13: required double lng
   14: required double from_lat                 // 起点
   15: required double from_lng
   16: required string from_poi_id
   17: required string from_poi_type
   18: required string from_address
   19: required string from_name
   20: required double to_lat                   // 终点
   21: required double to_lng
   22: required string to_poi_id
   23: required string to_poi_type
   24: required string to_address
   25: required string to_name
   26: optional double fast_car_estimate_fee //快车预估价
   27: optional string estimate_id              //自选车价格range对应预估id
   28: required string estimate_trace_id        //预估traceid
   29: optional double sp_fast_car_estimate_fee //特惠快车预估价
   30: optional double basic_fee //基础费（不含附加费，由定价引擎返回）

    /*webx公参*/
   100: optional string    xpsid
   101: optional string    xpsid_root
}

struct BargainRangeEstimateRsp {
    1: required i32              errno
    2: required string           errmsg
    3: optional BargainRangeEstimateData data
    4: required string           trace_id
}

struct BargainRangeEstimateData{
     1: required string              back_button_text //返回按钮文案
     2: required string              answer_rate_text //应答率文案
     3: required string              answer_rate_color //应答率颜色
     4: required string            low_price_bubble_text//低价气泡文案
     5: required string            high_price_bubble_text//高价气泡文案
     6: required string              price_limit_upper_text //价格上界文案
     7: required string              sub_title //副标题
     8: optional BargainRangeSpecialPriceRuleInfo special_price_rule_info//价格沟通
     9: required string              btn_text //按钮文案
     10: required i32                 step_size//步长
     11: required i32              price_limit_upper //价格上界
     12: required i32              price_limit_lower//价格下界
     13: required i32              recommend_price_upper //推荐价上界
     14: required i32              recommend_price_lower//推荐价下界
     15: required i32              wait_reply_price_upper//等待应答上上界
     16: required double              fast_car_estimate_fee//快车预估价
     17: required double              sp_fast_car_estimate_fee//特惠快车预估价
     18: required string            multi_require_product//发单参数
     19: required string            background_img //背景图片

}
struct BargainRangeSpecialPriceRuleInfo{
     1:required list<i32> rule_type//规则类型
     2:required string text//文案
     3:required string estimate_id
     4:required string event
}
