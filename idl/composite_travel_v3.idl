namespace php Dirpc.SDK.Mamba
namespace go Dirpc.SDK.Mamba

// 请求
struct CompositeTravelV3Req {
    1:  required string token

    /**起终点相关**/
    10: required double from_lat        // 起点
    11: required double from_lng
    12: required string from_poi_id
    13: required string from_poi_type
    14: required string from_address
    15: required string from_name
    16: required double to_lat          // 终点
    17: required double to_lng
    18: required string to_poi_id
    19: required string to_poi_type
    20: required string to_address
    21: required string to_name
    22: required double lat             // 定位点
    23: required double lng
    24: required string choose_f_searchid //用户选择起点请求ID
    25: required string choose_t_searchid //用户选择终点请求ID

    32:  required i32    access_key_id
    33:  required string app_version
    34:  required i64    channel
    35:  required i32    client_type
    36:  required string lang
    37:  required string map_type
    38:  required string terminal_id
    39:  required i32    platform_type
    40:  optional string ddfp
    41: required i32        user_type // 用户类型 0表示普通用户，2表示企业用户

    /**订单属性等信息**/
    50: required i32    order_type
    51: required string departure_time


    /**用户上车点信息**/
    80: required double wyc_from_lat        // 起点
    81: required double wyc_from_lng
    82: required string wyc_from_poi_id
    83: required string wyc_from_poi_type
    84: required string wyc_from_address
    85: required string wyc_from_name
    86: required double wyc_to_lat          // 终点
    87: required double wyc_to_lng
    88: required string wyc_to_poi_id
    89: required string wyc_to_poi_type
    90: required string wyc_to_address
    91: required string wyc_to_name
    92: required string wyc_f_searchid //用户选择起点请求ID
    93: required string wyc_t_searchid //用户选择终点请求ID

    94: optional string stopover_points //途经点参数 json array 字符串

     /*webx公参*/
    200: optional string    xpsid
    201: optional string    xpsid_root

    301: optional i32   is_rec_landing
    302: required string cycling_style
    303: required i32   maas_type
    304: required string sort_type
}

// 响应
struct CompositeTravelV3Res {
    1: required i32    errno
    2: required string errmsg
    3: required string trace_id
    4: optional CompositeTravelV3Data data
}

struct CompositeTravelV3Data {
    1: required list<PlanV3> plan_list
    2: required TabExtraData tab_extra_data
    3: required list<SortType> sort_type_list
    4: required string select_sort_type
}

struct SortType {
    1: required string key
    2: required string value

}

struct PlanV3 {
    1: required string        title
    5: required i32           plan_type // 1:网约车	2.公交地铁  3.组合出行
    6: optional TipData       tip_data
    7: optional string        button_content
    8: optional list<Segment> segment_list
    9: optional list<string>  desc_list
    10: required i32          is_selected  // 1:选中
    11: required string  right_title // 时间描述
    12: required string  right_desc // 价格描述
    13: required SubTitleV3 sub_title
    14: optional RecommendTagV3 recommend_tag  // 推荐标签
    15: optional list<Product> product_list // 品类N元组
    16: optional RecommendReason recommend_reason // 推荐理由

    20: required i32    link_type // 1:tab_id	2.原生页地址
    21: required string link_url
    22: optional string scene_id  //

    50: optional map<string,string> params
    51: optional map<string,string> map_params
    52: optional map<string,string> extra_data
    53: required string omega_info (go.type="map[string]interface{}",php.type="array")
}

struct TabExtraData {
    1: required CompositeRecommend composite_recommend
    2: required Classify classify
    3: required PublicTransit public_transit
    4: required Bicycle bicycle
    5: required Walking walking
    6: required Daijia  daijia

}

struct RecommendTagV3 {
    1: required string       bg_color
    2: required string       icon
    3: required string       content
    4: required string       font_color
}

struct RecommendReason {
   1: required string content
   2: required string font_color
}

struct SubTitleV3 {
   1: required string icon
   2: required string content
   3: required string font_color
}

struct CompositeRecommend {
    1: required string time_text
}

struct Classify {
    1: required string time_text
}

struct PublicTransit {
    1: required string time_text
}

struct Bicycle {
    1: required string time_text
}

struct Walking {
    1: required string time_text
}

struct Daijia{
    1: required string time_text
}