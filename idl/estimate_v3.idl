namespace php Dirpc.SDK.Mamba
namespace go Dirpc.SDK.Mamba

struct PEstimateV3DataReq {
    1: required V3BizCommonInfo  biz_common_info
    2: required list<V3BizProduct>  biz_product_map
}

struct V3BizCommonInfo {
    1: required V3AreaInfo area_info
    2: required V3CommonInfo common_info
    3: required V3PassengerInfo passenger_info
    4: required V3CommonBizInfo common_biz_info
}

struct V3AreaInfo {
    1: required i32 city
    2: required string district
    3: required i32 from_county
    4: required i32 to_county
//    5: required string from_city_name
//    6: required string to_city_name
    7: required double from_lat
    8: required double from_lng
    9: required string from_address
    10: required string from_name
    11: required string from_poi_id
    12: required string from_poi_type
    13: required double to_lat
    14: required double to_lng
    15: required string to_address
    16: required string to_name
    17: required string to_poi_id
    18: required string to_poi_type
    19: required double cur_lng
    20: required double cur_lat
//    21: required double lng
//    22: required double lat

    24: required i32 to_area
    25: required string map_type
    26: required string abstract_district
    27: required string starting_name
    28: required string dest_name
//    29: required string from_county_name
//    30: required string to_county_name
}

struct V3CommonInfo {
    1: required string app_version
    2: required string menu_id
    3: required i32 page_type
//    4: required i32 source_id
    5: required i32 access_key_id
    6: required i64 origin_id
    7: required i32 call_car_type
    8: required i64 channel
    9: optional string screen_pixels // 使用optional，因为json标签中有omitempty
    10: optional double screen_scale // 使用optional，因为json标签中有omitempty
    11: required i32 order_type
    12: required i32 platform_type
    13: required string lang
    14: required i32 client_type
    15: required string imei
//    16: required string agent_type
    17: required i64 terminal_id
    18: required i64 route_id
    19: required string from
    20: required i64 departure_time
    21: list<i64> departure_range // 使用list<i64>表示[]int64
//    22: required i32 activity_id
    23: required i32 payments_type
    24: required string luxury_select_carlevels
    25: required string luxury_select_driver
    26: required i32 airport_type
//    27: required string call_car_phone
    28: required i32 user_type
    29: optional i32     font_scale_type // 大字版标识


    37: required string tab_list
    38: required string tab_id
   
    56: required string selected_carlevel
    57: required string six_seat_selected_carlevel
    58: required string designated_driver
    59: required string six_seat_designated_driver
}

struct V3PassengerInfo {
    1: required i64 uid
    2: required i64 pid
    3: required string phone

    7: required i32 user_type
    8: required string origin_id
    9: required string token

    10: required i32 user_gender // 用户性别
}

struct V3CommonBizInfo {
    1: optional string multi_require_product
    2: required string recommend_extra_info_map
    3: required string top_rec
    4: optional string pricing_by_meter_price
    5: optional bool   only_mini_bus
}

struct V3BizProduct {
    1: required V3ProductInfo product_info
    2: required string  price_info
    3: required map<string,string> extra_info
}

struct V3ProductInfo {
    1: required string estimate_id
    2: required i64 product_category
    3: required i16 order_type
    4: required i64 product_id
    5: required i64 business_id
    6: required string require_level

    8: required i64 carpool_type
    9: required i32 level_type
    10: required i32 spacious_car_alliance
    11: required i64 combo_type
    12: required i64 scene_type
    13: required bool is_special_price
    14: required i32 carpool_price_type
    15: required bool is_dual_carpool_price
    16: required i32 airport_type
    17: required i32 railway_type
    18: required i32 hotel_type
    19: required i32 station_service_control
    20: required i32 otype
    21: required i32 payments_type
    22: required i32 sub_group_id // 聚合ID
//    23: required i16 long_rent_type
//    24: required i32 emergency_service_type
    25: required i64 route_type
    26: required i32 exam_type
    27: required i16 is_pick_on_time
    28: required string route_id
//    29: required string shift_id
    30: required V3PrivateBizInfo private_biz_info
    31: required bool is_trip_cloud
}

struct PEstimateV3DataResponse {
    1: required i32    errno // 错误码
    2: required string errmsg // 错误信息
    3: required string trace_id
    4: optional V3Data data // 预估数据
}

struct V3Data {
    1: required map<i64,V3EstimateData> estimate_data
}

struct V3EstimateData{
	// 标识
	1: required string estimate_id
	2: required i64    product_category

	// 一些场景标识
	3: required i32  hit_dynamic_price
	4: required i32  hit_show_h5_type
	5: required bool is_tripcloud

	// 车型数据
	6: required string car_title
	7: required string car_icon

	// 价格信息
	8: required string           fee_amount
	9: required string   fee_msg
    10: optional list<NewFormFeeDesc>    fee_desc_list
    11: optional list<V3MultiPrice> multi_price_list
    27: optional NewFormUnselectedData   unselected_data
    37: optional string min_fee_amount
    38: optional string fee_range_template
    42: required string fee_msg_template
    44: optional string  fee_msg_prefix_icon //费用描述前置icon

	// 支付信息
	12: required NewFormUserPayInfo user_pay_info

	// 附加
	13: optional V3PreferData              prefer_data
	14: optional list<NewFormCarpoolSeatOption> carpool_seat_list
    15: optional list<string>                   route_id_list

	// 发单参数
	16: required ExtraMapV3 extra_map

	// 其他额外
	17: optional NewFormExtraEstimateData        extra_estimate_data // 木得办法
	18: optional NewFormAutoDriveRedirectionInfo auto_driving_address_info
	19: required i32                             is_selected //勾选状态
    20: optional string                          depart_tag
    21: optional string                          sub_intro_icon // 出租车盒子选中车型前的icon
    22: required RadioSetting                    radio_setting // 拼车选座组件/拼车顺路组件
    23: optional OrderOption                     order_option //市内拼车的顺路标签（实验，后续验证无收益记得下掉）

    24: optional string               car_sub_title   // 车型副标题
    25: optional string               tips_icon       // 车型提示icon
    26: optional GroupSubTitle              sub_title         // 车型标签
    28: optional i32 is_hide_price //是否计算价格 0:默认需要计算价格  1:不计算

    29: optional GroupCarTag          car_tag   // tp泛快车型标签
    30: optional double           need_pay_fee_amount //券前价，目前只有司乘议价会用到
    31: optional NewCarpoolSeatModule carpool_seat_module //小巴座位数组件
    32: optional NoticeInfo notice_info // 品类下发横条+跳转组件
    33: optional i16 disabled //不可用状态
    35: optional DisabledInfo disabled_info // 不可用信息
    36: optional MapInfo map_info // 带给地图的数据
    39: optional string multi_route_tip_type // 提示文案
    40: optional BargainRangePopup bargain_range_popup //多勾惠选车弹窗数据
    41: optional MapCurveInfo map_curve_info // 地图画线和气泡信息
    // 是否只展示一条预估路线
    43: optional i32 is_single_route
    45: optional i32 car_icon_type
}

struct V3MultiPrice {
    1: required string fee_msg
    2: required double fee_amount
	3: optional NewFormFeeDesc fee_desc
	// 价差信息
	4: optional string fee_diff_msg
	5: optional double fee_diff_amount
	6: optional NewFormFeeDesc fee_diff_desc
	7: required i32 is_large_font
	8: required i32 font_size
	9: required string fee_msg_template
}


struct V3PrivateBizInfo {
    1: required string mini_bus_pre_match
    2: required string custom_feature_list
    3: required i32    carpool_seat_num
    4: optional string bargainRange_data // 惠选车数据
    5: optional string bargain_data      // 自选车数据
    6: optional bool   is_need_default_auth // 是否需要默认授权
    7: optional i64    combo_id
    8: required string guide_station_bus_data // 大巴导流渲染数据
    9: optional string user_member_profile // 会员信息
    10: optional string taxi_sps // 出租车峰期加价出口
    11: optional bool  is_intercity_surprise_alone // 是否惊喜独享
    12: optional string  departure_range
    13: optional i64 mini_bus_dist_limit
    14: optional list<string> need_auth_list
}

struct V3PreferData {
    1: required string desc
    2: required string fee_msg
    3: optional string fee_amount
    4: required string info_url
    5: optional i32    id
    6: required i32    count
    7: required i32    is_selected
    8: required list<NewFormPreferDataTag> tag_list
    9: optional string jump_url
    10: optional i32  single_style
    11: optional list<NewFormFeeDesc> fee_desc_list
}

struct ExtraMapV3 {
    1: required i64    product_id
    2: required i64    business_id
    3: required i64    combo_type
    4: required i32    require_level
    5: required i32    level_type
    6: required i64    combo_id
    7: required i64    route_type
    8: required i32    is_special_price
    9: required i32    count_price_type
   10: optional string port_type
   11: required i32    bargain_from_type
   12: required i32    is_default_auth
   13: required i32    etp
   14: optional string extra_custom_feature
   15: optional string     departure_range //城际拼车订单出发时间
   16: optional i32        carpool_seat_num  // 用户选择的拼车座位数
   17: optional string     is_intercity_surprise_alone //城际自营惊喜独享标识\
   18: optional list<string> need_auth_list // 待授权协议列表
   
}




