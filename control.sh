#!/bin/bash

set -ex

#############################################
## main
## 以托管方式, 启动服务
## control.sh脚本, 必须实现start方法
#############################################
workspace=$(cd $(dirname $0) && pwd -P)
cd $workspace
module=mamba
app=$module
export SONIC_PRETOUCH_SWITCH=off

function enable_goc() {
  echo 'goc mode start'
    count=`ps -ef | grep nuwa_goc | grep -v grep | wc -l`
    if [ $count -eq 0 ]; then
      nohup ./bin/nuwa_goc server > ./log/goc_server.log 2>&1 &
      echo 'goc server start'
    fi

    count=`ps -ef | grep goc_ticker | grep -v grep | wc -l`
    if [ $count -eq 0 ]; then
      nohup ./bin/goc_ticker -i 3600 -p './bin/nuwa_goc' -m 'mamba' > ./log/ticker.log 2>&1 &
      echo 'goc ticker start'
    fi

    # 启动服务, 以前台方式启动, 否则无法托管
    exec &> >(while read line || [ -n "$line" ]; do echo "[$(date "+%Y-%m-%d %H:%M:%S")] $line"; done) ./bin/${app}_cover
}

function run_command() {
	action=$1
	case $action in
	"start")
		if [ ${DIDIENV_DDCLOUD_ENV_TYPE}x = "dev"x ]; then
			cp ./conf/app_dev.toml ./conf/app.toml
		elif [ ${DIDIENV_DDCLOUD_ENV_TYPE}x = "sim"x ]; then
			cp ./conf/app_preview.toml ./conf/app.toml
		elif [ ${DIDIENV_DDCLOUD_ENV_TYPE}x = "pre"x ]; then
			cp ./conf/app_preview.toml ./conf/app.toml
		else
			cp ./conf/app_online.toml ./conf/app.toml
		fi

    hostname=$(hostname)
    if [ $hostname == "biz-pre-sale-core-sf-dcd76-3.docker.gz01" ]; then
      enable_goc
    else
      # 启动服务, 以前台方式启动, 否则无法托管
      exec &> >(while read line || [ -n "$line" ]; do echo "[$(date "+%Y-%m-%d %H:%M:%S")] $line"; done) ./bin/$app
    fi
		;;
	*)
		# 非法命令, 已非0码退出
		echo "unknown command"
		exit 1
		;;
	esac
}

run_command $1
