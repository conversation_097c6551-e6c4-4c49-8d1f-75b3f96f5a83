package param_handler

import (
	"context"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
)

type ParamsHandler struct {
	Req *EstimateRequest
}

type RequestWrapper func(ctx context.Context, req *EstimateRequest) BizError.BizError

func NewHandler(req *EstimateRequest) *ParamsHandler {
	return &ParamsHandler{
		Req: req,
	}
}

func (p *ParamsHandler) Do(ctx context.Context, funcList []RequestWrapper) BizError.BizError {
	for _, opt := range funcList {
		if err := opt(ctx, p.Req); err != nil {
			return err
		}
	}
	return nil

}
