package side

import (
	"context"

	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/public_log"
	"git.xiaojukeji.com/gulfstream/mamba/controller/base_controller"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_business_form_real_data"
)

type GetBusinessFormRealDataController struct {
	base_controller.BaseController
}

func (g *GetBusinessFormRealDataController) PGetBusinessFormRealData(ctx context.Context, request *proto.GetBusinessFormRealDataRequest) *proto.GetBusinessFormRealDataResponse {
	var (
		err                NewErrors.BizError
		resp               = &proto.GetBusinessFormRealDataResponse{}
		writePublicProcess public_log.WritePublicFunc
	)

	defer g.RecoverPanic(ctx, "PGetFormRealExpectInfo", resp)

	resp.Data, writePublicProcess, err = get_business_form_real_data.GetFormRealExpectInfo(ctx, request)

	g.HandleResp(ctx, resp, err)
	g.SetIsAsync(true).WritePublicLog(ctx, writePublicProcess)

	return resp
}
