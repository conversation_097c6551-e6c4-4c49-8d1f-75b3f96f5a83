package side

import (
	"context"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/logic/anycar_estimate_cache"
	"runtime/debug"

	trace "git.xiaojukeji.com/lego/context-go"

	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/controller/base_controller"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

type DelAnycarEstimateCacheController struct {
	base_controller.BaseController
}

func (d *DelAnycarEstimateCacheController) DelAnycarEstimateCache(ctx context.Context, request *proto.AnycarEstimateCacheReq) *proto.AnycarEstimateDelCacheResp {
	var (
		bizError = NewErrors.Success
		resp     = &proto.AnycarEstimateDelCacheResp{}
	)

	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
			bizError = NewErrors.ErrSystem
		}

		if bizError.Errno() != NewErrors.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()

	err := anycar_estimate_cache.DelAnycarEstimateCache(ctx, request)
	if err != nil {
		bizError = NewErrors.ErrSystem
		if _err, ok := err.(NewErrors.BizError); ok {
			bizError = _err
		}
		return resp
	}

	return resp
}
