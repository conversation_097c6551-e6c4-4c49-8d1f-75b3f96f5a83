package composite_travel

import (
	"context"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_options"
	trace "git.xiaojukeji.com/lego/context-go"
	"runtime/debug"
)

// 聚合时间接口
type PCompositeTravelOptionsController struct{}

func (c *PCompositeTravelOptionsController) PCompositeTravelOptions(ctx context.Context, req *proto.CompositeTravelOptionsReq) (resp *proto.CompositeTravelOptionsRes) {
	var (
		bizError = BizError.Success
	)
	resp = new(proto.CompositeTravelOptionsRes)
	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}

		resp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()

	// 1. 初始化参数
	service, err := composite_travel_options.NewService(ctx, req)
	if err != nil {
		resp.Errno = consts.ErrnoParams
		resp.Errmsg = err.Error()
		return resp
	}

	// 2. 执行
	data := service.Do(ctx)
	resp.Data = data
	return resp

}
