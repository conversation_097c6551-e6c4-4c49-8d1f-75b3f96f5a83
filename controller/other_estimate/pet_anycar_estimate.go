package other_estimate

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pet_anycar_estimate"
)

type PetAnyCarEstimateController struct {
}

func (c *PetAnyCarEstimateController) PetAnyCarEstimate(ctx context.Context, req *proto.AnyCarEstimateV4Req) *proto.AnyCarEstimateV4Resp {
	var (
		rspData *proto.AnyCarEstimateAppendCarForm
		errno   int
		rsp     = &proto.AnyCarEstimateV4Resp{}
	)
	rspData, errno = pet_anycar_estimate.PetEstimateAnyCar(ctx, req)

	if consts.NoErr != errno {
		rsp.Errno = int32(errno)
		rsp.Errmsg = consts.GetErrMessage(errno)
	} else {
		rsp.Errno = consts.NoErr
		rsp.Data = rspData
	}

	return rsp
}
