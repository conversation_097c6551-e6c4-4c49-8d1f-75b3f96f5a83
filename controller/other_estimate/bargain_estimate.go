package other_estimate

import (
	"context"
	"runtime/debug"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/bargain_estimate"
	"git.xiaojukeji.com/nuwa/trace"
)

type BargainEstimateController struct{}

func (p *BargainEstimateController) PBargainEstimate(ctx context.Context, req *proto.BargainEstimateReq) (rsp *proto.BargainEstimateRsp) { //nolint
	rsp = new(proto.BargainEstimateRsp)
	var (
		err      error
		logic    *bargain_estimate.Logic
		bizError = BizError.Success
	)
	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}

		rsp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
		if bizError.Errno() != BizError.ErrnoSuccess {
			rsp.Errno = int32(bizError.Errno())
			rsp.Errmsg = bizError.Error()
		}
	}()

	if logic, err = bargain_estimate.NewLogic(ctx, req); err != nil {
		bizError = BizError.ErrSystem
		if _err, ok := err.(BizError.BizError); ok {
			bizError = _err
		}
		return
	}

	if rsp.Data, err = logic.Do(ctx); err != nil {
		bizError = BizError.ErrSystem
		if _err, ok := err.(BizError.BizError); ok {
			bizError = _err
		}
		return
	}
	return rsp
}
