package other_estimate

import (
	"context"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_estimate_detail"
	Render "git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_estimate_detail"
)

// IntercityEstimateDetailController 站点巴士发单确认页预估
type IntercityEstimateDetailController struct {
}

func (r *IntercityEstimateDetailController) PIntercityEstimateDetail(ctx context.Context, req *proto.IntercityEstimateDetailRequest) *proto.IntercityEstimateDetailResponse {
	var (
		err      error
		respData *proto.IntercityEstimateDetailData
		resp     = &proto.IntercityEstimateDetailResponse{}
	)

	respData, err = intercity_estimate_detail.IntercityEstimateDetail(ctx, req)
	resp.Data = respData

	r.handleResp(ctx, resp, err)

	return resp
}

func (r *IntercityEstimateDetailController) handleResp(ctx context.Context, resp *proto.IntercityEstimateDetailResponse, err error) {
	var bizErr BizError.BizError

	if resp == nil {
		resp = &proto.IntercityEstimateDetailResponse{}
	}
	if err == consts.ErrorGetRoutePriceFail {
		bizErr = BizError.BizErrorFromErrNo(BizError.ErrnoGetTcRoutePrice)
		Render.InitJumpUrl(ctx, resp)
	} else if err == consts.ErrorSelectedBusCard {
		bizErr = BizError.BizErrorFromErrNo(BizError.ErrorSelectedBusCard)
	} else if err != nil {
		bizErr = BizError.BizErrorFromErrNo(BizError.ErrnoSystemError)
	} else {
		bizErr = BizError.BizErrorFromErrNo(BizError.ErrnoSuccess)
	}

	resp.Errno = int32(bizErr.Errno())
	resp.Errmsg = bizErr.Error()
}
