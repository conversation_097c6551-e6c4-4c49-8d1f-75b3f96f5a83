package other_estimate

import (
	"context"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/sfc_modify_estimate"
)

// SFCEstimateModifyController 顺风车预估
type SFCEstimateModifyController struct {
}

// SFCEstimate 顺风车预估
func (c *SFCEstimateController) PSFCModifyEstimate(ctx context.Context, req *proto.SFCModifyEstimateRequest) (resp *proto.SFCModifyEstimateResponse) {
	//var resp *proto.SFCModifyEstimateResponse
	resp = new(proto.SFCModifyEstimateResponse)
	var (
		err      error
		logic    *sfc_modify_estimate.Logic
		bizError = BizError.Success
	)
	resp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)

	defer func() {
		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()

	//获取一些品类，出发时间，最大座位数等参数信息
	if logic, err = sfc_modify_estimate.NewLogic(ctx, req, resp.TraceId); err != nil {
		bizError = BizError.ErrSystem
		if _err, ok := err.(BizError.BizError); ok {
			bizError = _err
		}
		return resp
	}
	//获取一些价格信息
	if resp.Data, err = logic.Do(ctx); err != nil {
		bizError = BizError.ErrSystem
		if _err, ok := err.(BizError.BizError); ok {
			bizError = _err
		}
		return resp
	}

	return resp
}
