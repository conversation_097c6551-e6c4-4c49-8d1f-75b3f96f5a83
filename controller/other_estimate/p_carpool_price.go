package other_estimate

import (
	"context"
	"runtime/debug"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/carpool_estimate"
	"git.xiaojukeji.com/nuwa/trace"
)

type CarpoolPriceController struct {
}

// PCarpoolPrice 拼车纯价格预估
func (cc CarpoolPriceController) PCarpoolPrice(ctx context.Context, req *proto.CarpoolEstimateRequest) (resp *proto.CarpoolPriceResponse) {
	resp = new(proto.CarpoolPriceResponse)
	var (
		bizError = BizError.Success
		err      error
	)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}

		resp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()

	serviceRequest, paramErr := carpool_estimate.NewMiniServiceRequest(ctx, req)
	if paramErr != nil {
		bizError = paramErr
		return
	}

	// service
	service, err := carpool_estimate.BuildService(ctx, serviceRequest)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		return
	}

	resp.Data, err = service.DoPriceOnlyLogic(ctx)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		log.Trace.Warnf(ctx, consts.TagErrGenProducts, "err %v", err)
		return
	}

	return
}
