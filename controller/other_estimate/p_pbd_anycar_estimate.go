package other_estimate

import (
	"context"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pbd_anycar_estimate"
)

type PPBDAnyCarEstimateController struct {
}

func (c *PPBDAnyCarEstimateController) PPBDAnyCarEstimate(ctx context.Context, req *proto.PBDAnyCarEstimateReq) (rsp *proto.B2BAnyCarEstimateRsp) {
	rsp = new(proto.B2BAnyCarEstimateRsp)
	var (
		bizError  = BizError.Success
		err       error
		passenger *passport.UserInfo
	)
	defer func() {
		if rsp.Data == nil {
			rsp.Data = &proto.B2BEstimateData{}
		}
		rsp.Data.EstimateTraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)

		if bizError.Errno() != BizError.ErrnoSuccess {
			rsp.Errno = int32(bizError.Errno())
			rsp.Errmsg = bizError.Error()
		}
	}()
	err = pbd_anycar_estimate.CheckParams(ctx, req)
	if err != nil {
		return
	}
	if passenger, err = tryGetUserInfo(ctx, req.Token, int(req.AccessKeyId)); err != nil {
		err = BizError.ErrNotLogin
		return
	}
	pg, err := pbd_anycar_estimate.InitLogic(ctx, passenger, req)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		return
	}
	rsp.Data, err = pg.DoBizLogic(ctx)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		// log.Trace.Warnf(ctx, consts.TagErrGenProducts, "err %v", err)
		return
	}
	return
}
