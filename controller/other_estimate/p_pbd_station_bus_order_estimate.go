package other_estimate

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pbd_station_bus_order_estimate"
	"runtime/debug"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace"
)

type PbdStationBusOrderEstimateController struct{}

func (p *PbdStationBusOrderEstimateController) PPbdStationBusOrderEstimate(ctx context.Context, req *proto.PbdStationBusOrderEstimateReq) (resp *proto.PbdStationBusOrderEstimateRsp) {
	resp = new(proto.PbdStationBusOrderEstimateRsp)
	var (
		bizError = BizError.Success
		err      error
	)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}
		if resp.Data == nil {
			resp.Data = new(proto.PbdStationBusOrderEstimateData)
		}
		resp.Data.EstimateTraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)

		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()
	estimateRequest, b := NewPbdStationBusOrderEstimateServiceRequest(ctx, req)
	if b != nil {
		bizError = b
		return
	}

	// service
	service, err := pbd_station_bus_order_estimate.BuildService(ctx, estimateRequest)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		return
	}
	resp.Data, err = service.DoBizLogicList(ctx, req)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		log.Trace.Warnf(ctx, consts.TagErrGenProducts, "err=%v", err)
		return
	}

	return
}

func NewPbdStationBusOrderEstimateServiceRequest(ctx context.Context, httpReq *proto.PbdStationBusOrderEstimateReq) (*param_handler.EstimateRequest, BizError.BizError) {
	serviceReq := &param_handler.EstimateRequest{
		ReqFromParams: httpReq,
	}
	serviceReq.SetPageType(page_type.PageTypeUndefined)
	handler := param_handler.NewHandler(serviceReq)
	do := handler.Do(ctx, []param_handler.RequestWrapper{
		// 需要保证顺序
		CheckPbdStationBusOrderParams(ctx, serviceReq), // 参数校验
	})
	return serviceReq, do

}

func CheckPbdStationBusOrderParams(ctx context.Context, serviceReq *param_handler.EstimateRequest) param_handler.RequestWrapper {
	return func(ctx context.Context, serviceReq *param_handler.EstimateRequest) BizError.BizError {

		req, ok := serviceReq.ReqFromParams.(*proto.PbdStationBusOrderEstimateReq)
		if !ok {
			res := BizError.ErrInvalidArgument
			return res
		}
		if req.AccessKeyId == 0 || req.AppVersion == "" || req.StartStationId == 0 || req.EndStationId == 0 || req.RouteId == "" || req.ShiftId == "" {
			res := BizError.ErrInvalidArgument
			return res
		}
		return nil
	}
}
