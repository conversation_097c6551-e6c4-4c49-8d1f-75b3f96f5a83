package other_estimate

import (
	"context"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/carpool_invitation"
	LegoContext "git.xiaojukeji.com/lego/context-go"
	"runtime/debug"
)

// CarpoolInvitationEstimateController 邀约同行预估
type CarpoolInvitationEstimateController struct {
}

func (c *CarpoolInvitationEstimateController) PCarpoolInvitationEstimate(ctx context.Context, req *proto.CarpoolInvitationEstimateRequest) *proto.CarpoolInvitationEstimateResp {
	var (
		rspData *proto.InvitationEstimateData
		errno   int
		rsp     = &proto.CarpoolInvitationEstimateResp{}
	)

	defer func() {
		rsp.TraceId = LegoContext.GetTrace(ctx).GetTraceId()
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, LegoContext.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
			rsp.Errno = consts.ErrnoPanic
			rsp.Errmsg = consts.GetErrMessage(consts.ErrnoPanic)
		}
	}()

	rspData, errno = carpool_invitation.CarpoolInvitationEstimate(ctx, req)

	if consts.NoErr != errno {
		rsp.Errno = int32(errno)
		rsp.Errmsg = consts.GetErrMessage(errno)
	} else {
		rsp.Errno = consts.NoErr
		rsp.Data = rspData
	}

	return rsp
}
