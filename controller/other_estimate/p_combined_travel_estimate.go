package other_estimate

import (
	"context"
	"fmt"
	"runtime/debug"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/combinedtravel"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	LegoContext "git.xiaojukeji.com/lego/context-go"
	"git.xiaojukeji.com/nuwa/golibs/goutils"
)

type CombinedTravelEstimateController struct {
}

// CombinedTravelEstimate 组合出行价格预估
func (c *CombinedTravelEstimateController) CombinedTravelEstimate(ctx context.Context, req *proto.MultiEstimatePriceRequest) (
	res *proto.CombinedTravelEstimateRes) {

	var (
		resData *proto.CombinedTravelEstimateResData
		errno   int
	)
	res = &proto.CombinedTravelEstimateRes{}
	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, LegoContext.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
			res.Errno = consts.ErrnoPanic
			res.Errmsg = consts.GetErrMessage(consts.ErrnoPanic)
		}
	}()

	resData, errno = c.do(ctx, req)
	if consts.NoErr != errno {
		res.Errno = int32(errno)
		res.Errmsg = consts.GetErrMessage(errno)
	} else {
		res.Errno = consts.NoErr
		res.Data = resData
	}
	return res
}

func (c *CombinedTravelEstimateController) do(ctx context.Context, req *proto.MultiEstimatePriceRequest) (
	resData *proto.CombinedTravelEstimateResData, errno int) {

	var (
		products []*biz_runtime.ProductInfoFull
	)

	defer func() {
		goutils.Go(
			ctx,
			func(ctx context.Context, args ...interface{}) {
				combinedtravel.AddPublicLog(ctx, products, req.GetCombinedTravelBizType())
			},
		)
	}()

	// 1 初始化productsGenerator：初始化用户信息、地理信息，注册product过滤器
	productsGenerator, errno := combinedtravel.InitProductGenerator(ctx, req)
	if consts.NoErr != errno {
		return nil, errno
	}

	// 2 生成预估数据：根据产品开城获取预估价并执行product过滤器
	products, err := productsGenerator.GenProducts(ctx)
	if err != nil || len(products) == 0 {
		log.Trace.Errorf(ctx, consts.TagErrGenProducts, "error: %v", err)
		return nil, consts.ErrnoNoProductOpen
	}

	// 3 渲染返回数据
	resData, errno = combinedtravel.BuildResponse(ctx, products)
	if consts.NoErr != errno {
		return nil, errno
	}

	return resData, consts.NoErr
}
