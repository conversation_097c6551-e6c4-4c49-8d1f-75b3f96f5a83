package other_estimate

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/logic/pick_on_time_estimate"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

// PPickOnTimeEstimateController 必有车预估
type PPickOnTimeEstimateController struct {
}

func (c *PPickOnTimeEstimateController) PPickOnTimeEstimate(ctx context.Context, req *proto.PickOnTimeEstimateReq) *proto.PickOnTimeEstimateRsp {
	var (
		rspData *proto.PickOnTimeEstimateData
		errno   int
		rsp     = &proto.PickOnTimeEstimateRsp{}
	)

	rspData, errno = pick_on_time_estimate.PickOnTimeEstimate(ctx, req)

	if consts.NoErr != errno {
		rsp.Errno = int32(errno)
		rsp.Errmsg = consts.GetErrMessage(errno)
	} else {
		rsp.Errno = consts.NoErr
		rsp.Data = rspData
	}

	return rsp
}
