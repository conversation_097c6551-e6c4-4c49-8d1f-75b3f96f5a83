package other_estimate

import (
	"context"
	"errors"
	"runtime/debug"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pets_travel_estimate"
	"git.xiaojukeji.com/nuwa/trace"
)

type PetsTravelEstimateController struct{}

func (p *PetsTravelEstimateController) PPetsTravelEstimate(ctx context.Context, req *proto.PetsTravelEstimateReq) (resp *proto.PetsTravelEstimateResponse) {
	resp = new(proto.PetsTravelEstimateResponse)
	var (
		bizError = BizError.Success
		err      error
	)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrPetsTravelRender
		}
		if resp.Data == nil {
			resp.Data = new(proto.PetsTravelEstimateData)
		}
		resp.Data.EstimateTraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)

		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()
	estimateRequest, b := p.NewServiceRequestPets(ctx, req)
	if b != nil {
		bizError = b
		return
	}

	service, err := pets_travel_estimate.BuildService(ctx, estimateRequest)
	if err != nil {
		bizError = BizError.ErrPetsTravelRender
		if shadow, ok := err.(BizError.BizError); ok {
			// todo pet_list为空则拉起添加宠物弹窗 灰度控制 版本控制
			bizError = shadow
			if shadow.Errno() == BizError.ErrnoInterception && pets_travel_estimate.IsAllowPopup(ctx, estimateRequest) {
				data, petMetaErr := pets_travel_estimate.BuildPetsMetaData(ctx)
				if petMetaErr != nil {
					bizError = BizError.ErrEstimatePetTripMeta
					return
				}
				button, butErr := pets_travel_estimate.BuildBackButton(ctx, req)
				if butErr != nil {
					return
				}
				if resp.Data == nil {
					resp.Data = new(proto.PetsTravelEstimateData)
				}
				resp.Data.BackButton = button
				resp.Data.PetMetaData = data
			}
		}
		return
	}

	resp.Data, err = service.DoBizLogicList(ctx, req)
	if err != nil {
		log.Trace.Warnf(ctx, consts.TagErrGenProducts, "err=%v", err)

		if errors.Is(err, BizError.ErrStopoverPointsConflictsWithScenes) {
			bizError = BizError.ErrStopoverPointsConflictsWithScenes
			return
		}

		bizError = BizError.ErrNotOpenPetsTravelService
		return
	}
	if resp.Data == nil {
		log.Trace.Warnf(ctx, consts.TagErrGenProducts, "err=%v", err)
		bizError = BizError.ErrNotOpenPetsTravelService
		return
	}

	return
}

func (p *PetsTravelEstimateController) NewServiceRequestPets(ctx context.Context, httpReq *proto.PetsTravelEstimateReq) (*param_handler.EstimateRequest, BizError.BizError) {
	serviceReq := &param_handler.EstimateRequest{
		ReqFromParams: httpReq,
	}
	serviceReq.SetPageType(page_type.PageTypePetsTravel)
	handler := param_handler.NewHandler(serviceReq)
	do := handler.Do(ctx, []param_handler.RequestWrapper{
		// 需要保证顺序
		p.CheckParamsMutli(ctx, serviceReq),                                // 参数校验
		param_handler.GetUserInfo(ctx, httpReq.Token, httpReq.AccessKeyId), // 用户信息获取 + 校验
	})

	return serviceReq, do

}

func (p *PetsTravelEstimateController) CheckParamsMutli(ctx context.Context, serviceReq *param_handler.EstimateRequest) param_handler.RequestWrapper {
	return func(ctx context.Context, serviceReq *param_handler.EstimateRequest) BizError.BizError {

		req, ok := serviceReq.ReqFromParams.(*proto.PetsTravelEstimateReq)
		if !ok {
			res := BizError.ErrInvalidArgument
			return res
		}
		if req.AccessKeyId == 0 || req.AppVersion == "" || req.Lang == "" || req.Token == "" {
			res := BizError.ErrInvalidArgument
			return res
		}
		return nil
	}
}
