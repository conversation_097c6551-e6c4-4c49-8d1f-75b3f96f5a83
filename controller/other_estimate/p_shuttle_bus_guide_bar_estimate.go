package other_estimate

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/logic/shuttle_bus_estimate"
	"runtime/debug"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace"
)

type ShuttleBusGuideBarEstimateController struct{}

func (p *ShuttleBusGuideBarEstimateController) PShuttleBusGuideBarEstimate(ctx context.Context, req *proto.ShuttleBusGuideBarEstimateReq) (rsp *proto.ShuttleBusGuideBarEstimateRsp) {
	rsp = new(proto.ShuttleBusGuideBarEstimateRsp)
	var (
		err      error
		logic    *shuttle_bus_estimate.GuideBarLogic
		bizError = BizError.Success
	)
	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}

		rsp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
		if bizError.Errno() != BizError.ErrnoSuccess {
			rsp.Errno = int32(bizError.Errno())
			rsp.Errmsg = bizError.Error()
		}
	}()
	if logic, err = shuttle_bus_estimate.GuideBarNewLogic(ctx, req); err != nil {
		return
	}

	if err = logic.Do(ctx, rsp); err != nil {
		bizError = BizError.ErrNotInSceneArea
		if _err, ok := err.(BizError.BizError); ok {
			bizError = _err
		}
		return
	}
	return rsp
}
