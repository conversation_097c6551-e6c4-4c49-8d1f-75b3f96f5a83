package other_estimate

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pbd_station_bus_estimate/dispatcher"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pbd_station_bus_multi_estimate"
	"runtime/debug"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace"
)

type PbdStationBusMultiEstimateController struct{}

func (p *PbdStationBusMultiEstimateController) PPbdStationBusMultiEstimate(ctx context.Context, req *proto.PbdStationBusMultiEstimateReq) (resp *proto.PbdStationBusMultiEstimateRsp) {
	resp = new(proto.PbdStationBusMultiEstimateRsp)
	var (
		bizError = BizError.Success
		err      error
	)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}
		if resp.Data == nil {
			resp.Data = new(proto.PbdStationBusMultiEstimateData)
		}
		resp.Data.EstimateTraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)

		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()
	estimateRequest, dispatcherRes, b := NewPbdStationBusMultiEstimateServiceRequest(ctx, req)
	if b != nil {
		bizError = b
		return
	}

	// service
	service, err := pbd_station_bus_multi_estimate.BuildService(ctx, estimateRequest, dispatcherRes)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		return
	}
	resp.Data, err = service.DoBizLogicList(ctx, req)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		log.Trace.Infof(ctx, consts.TagErrGenProducts, "err=%v", err)
		return
	}

	return
}

func NewPbdStationBusMultiEstimateServiceRequest(ctx context.Context, httpReq *proto.PbdStationBusMultiEstimateReq) (*param_handler.EstimateRequest, dispatcher.PbdMultiDispatcher, BizError.BizError) {
	targetDispatcher := dispatcher.GetDispatcher(ctx, httpReq)
	checkError := targetDispatcher.CheckParam(ctx, httpReq)
	if checkError != nil {
		return nil, nil, checkError
	}
	estimateParam, initErr := targetDispatcher.InitParam(ctx, httpReq)
	if initErr != nil {
		return nil, nil, initErr
	}
	return estimateParam, targetDispatcher, nil
}
