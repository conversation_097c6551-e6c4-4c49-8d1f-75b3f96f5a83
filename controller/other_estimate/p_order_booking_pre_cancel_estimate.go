package other_estimate

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	BookingPreCancel "git.xiaojukeji.com/gulfstream/mamba/logic/order_booking_pre_cancel_estimate"
)

type PreCancelEstimateController struct {
}

func (c *PreCancelEstimateController) POrderBookingPreCancelEstimate(ctx context.Context, req *proto.AnyCarEstimateReq) *proto.AnyCarEstimateV3Rsp {
	var (
		rspData *proto.NewFormEstimateResponse
		errno   int
		rsp     = &proto.AnyCarEstimateV3Rsp{}
	)

	rspData, errno = BookingPreCancel.EstimateBookingPreCancel(ctx, req)

	if consts.NoErr != errno {
		rsp.Errno = int32(errno)
		rsp.Errmsg = consts.GetErrMessage(errno)
	} else {
		rsp.Errno = consts.NoErr
		rsp.Data = rspData
	}

	return rsp
}
