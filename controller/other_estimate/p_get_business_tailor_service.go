package other_estimate

import (
	"context"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/business_tailor_service"
	"git.xiaojukeji.com/nuwa/trace"
	"runtime/debug"
)

type GetBusinessTailorService struct{}

func (a *GetBusinessTailorService) PGetBusinessTailorService(ctx context.Context, req *proto.LuxMultiEstimatePriceRequest) *proto.BusinessTailorServiceResponse {
	var (
		rspData *proto.BusinessTailorServiceData
		errno   int
		resp    = &proto.BusinessTailorServiceResponse{}
	)

	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
			resp.Errno = consts.ErrnoPanic
			resp.Errmsg = consts.GetErrMessage(consts.ErrnoPanic)
		}
	}()

	rspData, errno = business_tailor_service.GetTailorService(ctx, req)

	resp.Errno = int32(errno)
	resp.Errmsg = consts.GetErrMessage(errno)
	resp.Data = rspData
	return resp
}
