package other_estimate

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/logic/station_bus_instead_order_estimate"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"runtime/debug"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace"
)

type StationBusInsteadOrderEstimateController struct{}

func (p *StationBusInsteadOrderEstimateController) PStationBusInsteadOrderEstimate(ctx context.Context, req *proto.StationBusInsteadOrderEstimateReq) (resp *proto.StationBusInsteadOrderEstimateRsp) {
	resp = new(proto.StationBusInsteadOrderEstimateRsp)
	var (
		bizError = BizError.Success
		err      error
	)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}
		if resp.Data == nil {
			resp.Data = new(proto.StationBusInsteadOrderEstimateData)
		}
		resp.Data.EstimateTraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)

		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}
	}()
	estimateRequest, quotationInfo, b := NewInsteadOrderServiceRequest(ctx, req)
	if b != nil {
		bizError = b
		return
	}

	// service
	service, err := station_bus_instead_order_estimate.BuildService(ctx, estimateRequest, quotationInfo)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		return
	}
	resp.Data, err = service.DoBizLogicList(ctx)
	if err != nil {
		bizError = BizError.ErrSystem
		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}
		log.Trace.Warnf(ctx, consts.TagErrGenProducts, "err=%v", err)
		return
	}

	return
}

func NewInsteadOrderServiceRequest(ctx context.Context, httpReq *proto.StationBusInsteadOrderEstimateReq) (*param_handler.EstimateRequest, *biz_runtime.Quotation, BizError.BizError) {
	serviceReq := &param_handler.EstimateRequest{
		ReqFromParams: httpReq,
	}
	serviceReq.SetPageType(page_type.PageTypeUndefined)
	handler := param_handler.NewHandler(serviceReq)
	do := handler.Do(ctx, []param_handler.RequestWrapper{
		// 需要保证顺序
		CheckInsteadOrderParams(ctx, serviceReq),                           // 参数校验
		param_handler.GetUserInfo(ctx, httpReq.Token, httpReq.AccessKeyId), // 用户信息获取 + 校验
	})
	if httpReq.SceneType == consts.SceneTypeChangePassenger { //乘车人变动的时候需要用到报价单数据，所以查询报价单
		quotationInfo, err := station_bus_instead_order_estimate.NewQuotationInfo(httpReq).Do(ctx)
		if err != nil {
			return nil, nil, err
		}
		return serviceReq, quotationInfo, do
	}
	return serviceReq, nil, do

}

func CheckInsteadOrderParams(ctx context.Context, serviceReq *param_handler.EstimateRequest) param_handler.RequestWrapper {
	return func(ctx context.Context, serviceReq *param_handler.EstimateRequest) BizError.BizError {

		req, ok := serviceReq.ReqFromParams.(*proto.StationBusInsteadOrderEstimateReq)
		if !ok {
			res := BizError.ErrInvalidArgument
			return res
		}
		if req.AccessKeyId == 0 || req.AppVersion == "" || req.EndCity == 0 || req.StartCity == 0 || req.Token == "" || req.StartStationId == 0 || req.EndStationId == 0 {
			res := BizError.ErrInvalidArgument
			return res
		}
		return nil
	}
}
