package order_estimate

import (
	"context"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/multi_point_estimate"
)

// MultiPointEstimateController 路线预估
type MultiPointEstimateController struct {
}

func (m *MultiPointEstimateController) PMultiPointEstimate(ctx context.Context, req *proto.MultiPointEstimateRequest) *proto.MultiPointEstimateResponse {
	var (
		err      error
		respData *proto.MultiPointEstimateData
		resp     = &proto.MultiPointEstimateResponse{}
	)

	respData, err = multi_point_estimate.MultiPointEstimate(ctx, req)
	resp.Data = respData

	m.handleResp(resp, err)

	return resp
}

func (m *MultiPointEstimateController) handleResp(resp *proto.MultiPointEstimateResponse, err error) {
	var bizErr BizError.BizError

	if resp == nil {
		resp = &proto.MultiPointEstimateResponse{}
	}

	if err != nil {
		bizErr = BizError.BizErrorFromErrNo(BizError.ErrnoSystemError)
	} else {
		bizErr = BizError.BizErrorFromErrNo(BizError.ErrnoSuccess)
	}

	resp.Errno = int32(bizErr.Errno())
	resp.Errmsg = bizErr.Error()
}
