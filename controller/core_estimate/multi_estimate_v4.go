package core_estimate

import (
	"context"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v4_multi"
	"git.xiaojukeji.com/nuwa/trace"
	"git.xiaojukeji.com/s3e/common-lib/v2/component/diff"
	"runtime/debug"
)

// MultiEstimateV4Controller 主预估go版本
type MultiEstimateV4Controller struct {
}

func (m *MultiEstimateV4Controller) PMultiEstimateV4(ctx context.Context, req *proto.PMultiEstimatePriceV4Request) (resp *proto.NewFormMultiEstimatePriceV4Response) {
	resp = &proto.NewFormMultiEstimatePriceV4Response{}
	var (
		bizError = BizError.Success
		err      error
	)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}

		if shadow, ok := err.(BizError.BizError); ok {
			bizError = shadow
		}

		resp.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
		if bizError.Errno() != BizError.ErrnoSuccess {
			resp.Errno = int32(bizError.Errno())
			resp.Errmsg = bizError.Error()
		}

		if BizError.IsDegradeErrno(bizError.Errno()) {
			proto.SetHttpCode(ctx, BizError.StatusRateLimit)
		}
	}()

	ctx = diff.SetDiffStatus(ctx, req.GetDiffStatus())
	service, err := estimate_v4_multi.NewService(ctx, req)
	if err != BizError.Success || service == nil {
		bizError = BizError.ErrSystem
		return
	}
	if err = service.Estimate(ctx); err != BizError.Success {
		bizError = BizError.ErrSystem
		return
	}

	newRender := estimate_v4_multi.NewRender(service.GetProductList(), service.GetBaseReq())
	resp.Data, err = newRender.Render(ctx)
	if err != nil {
		bizError = BizError.ErrRender
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "render is nil err:%v", err)
		return
	}

	diff.CheckDiffAndLog(ctx, diff.DownStreamDirpcType, "/gulfstream/pre-sale/v1/core/pMultiEstimatePriceV3", resp)
	return resp
}
