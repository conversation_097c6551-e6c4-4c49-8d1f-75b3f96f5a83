package core_estimate

import (
	"context"
	"runtime/debug"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v4"
	trace "git.xiaojukeji.com/lego/context-go"
)

type EstimateV4Controller struct {
}

type Res struct {
	DataForNA      *proto.PEstimateV4Response     // 对端数据
	DataForService []*estimate_v4.CarEstimateData // 对上游数据
}

func (cc EstimateV4Controller) PEstimateV4(ctx context.Context, req *estimate_v4.EstimateV4Req) (respFinal *Res) {
	respFinal = &Res{
		DataForNA:      &proto.PEstimateV4Response{},
		DataForService: make([]*estimate_v4.CarEstimateData, 0),
	}
	var (
		bizError = BizError.Success
	)

	defer func() {
		if r := recover(); r != nil {
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			bizError = BizError.ErrSystem
		}

		respFinal.DataForNA.TraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
		if bizError.Errno() != BizError.ErrnoSuccess {
			respFinal.DataForNA.Errno = int32(bizError.Errno())
			respFinal.DataForNA.Errmsg = bizError.Error()
		}
	}()

	service, err0 := estimate_v4.NewService(ctx, req)
	if err0 != BizError.Success || service == nil {
		return
	}

	if err1 := service.Estimate(ctx); err1 != nil {
		bizError = BizError.ErrSystem
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "wyc plan is nil err:%v", err1)
		return
	}

	// 渲染
	newRender := estimate_v4.NewRender(service.GetProductList(), service.GetBaseReq(), service.GetIsImbalanced())
	render, carList, err := newRender.Render(ctx)
	if err != nil {
		bizError = BizError.ErrRender
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "wyc plan is nil err:%v", err)
		return
	}
	respFinal.DataForNA.Data = render
	respFinal.DataForService = carList
	return
}
