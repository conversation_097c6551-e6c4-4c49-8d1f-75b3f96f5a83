package test

import (
	"context"
	"reflect"
	"testing"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/anycar_v4/data"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/sub_title_list"
)

func TestGetSubTitleList(t *testing.T) {
	type args struct {
		ctx  context.Context
		prov sub_title_list.SubTitleListProvider
	}
	tests := []struct {
		name string
		args args
		want []*proto.SubTitle
	}{
		// TODO: Add test cases.
		{
			name: "a",
			args: args{
				ctx: context.Background(),
				prov: &data.AnyCarV4Adapter{ProductInfoFull: &biz_runtime.ProductInfoFull{
					BaseReqData: &models.BaseReqData{
						CommonInfo: models.CommonInfo{
							FontScaleType: 1,
						},
					},
				}},
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := sub_title_list.GetSubTitleList(tt.args.ctx, tt.args.prov); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetSubTitleList() = %v, want %v", got, tt.want)
			}
		})
	}
}
