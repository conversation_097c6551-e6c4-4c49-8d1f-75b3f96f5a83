package invited

import (
	"context"
	"math"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type EstimateFeeProvider interface {
}

func CouponInfo(ctx context.Context, products []*biz_runtime.ProductInfoFull) *proto.CouponInfoData {
	var (
		maxCouponInfo *proto.CouponInfoData //最大的优惠信息
	)
	//找到预估中最大优惠金额对应的券信息
	for _, product := range products {
		if product == nil {
			continue
		}
		userPhone, params := product.ApolloParamsGen(apollo_model.WithPhoneKey, apollo_model.WithProductCategory)
		if !apollo.FeatureToggle(ctx, "invited_show_coupon_product_switch", userPhone, params) {
			continue
		}
		couponInfo := product.GetCouponInfo()
		if couponInfo == nil {
			continue
		}
		adapterCouponInfo := new(proto.CouponInfoData)
		adapterCouponInfo.EstimateId = product.GetEstimateID()
		adapterCouponInfo.CouponType = couponInfo.CouponType
		couponAmount := util.ToFloat64(couponInfo.Amount) / 100
		if couponAmount == math.Trunc(couponAmount) {
			adapterCouponInfo.CouponAmount = math.Trunc(couponAmount)
		} else {
			adapterCouponInfo.CouponAmount = couponAmount
		}
		discount := float64(couponInfo.Discount) / 10
		if discount == math.Trunc(discount) {
			adapterCouponInfo.CouponDiscount = strconv.FormatInt(int64(discount), 10)
		} else {
			adapterCouponInfo.CouponDiscount = util.FormatPrice(discount, -1)
		}
		if maxCouponInfo == nil {
			maxCouponInfo = adapterCouponInfo
			continue
		}
		if adapterCouponInfo.CouponAmount > maxCouponInfo.CouponAmount {
			maxCouponInfo = adapterCouponInfo
		}
	}
	return maxCouponInfo
}
