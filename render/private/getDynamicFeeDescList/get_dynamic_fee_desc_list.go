package get_dynamic_fee_desc_list

import (
	"context"
	"fmt"
	"strconv"

	BizConsts "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	productutil "git.xiaojukeji.com/gulfstream/biz-common-go/v6/product"
	mambaconsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/dynamic_fee_desc_list/data"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/ab"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input/render_callback"
	engine_model "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
	feedescutil "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/util"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/simple_estimate/utils"
	"github.com/spf13/cast"
)

type productFull struct {
	*biz_runtime.Quotation
	*data.UserInfo
	*data.DynamicFeeDescListAdapter

	_pc *apollo_model.ParamsConnector
}

type DynamicFeeDescList struct {
	isSingleCheck bool
	data          *data.DynamicFeeDescListAdapter

	businessMax          *engine_model.FeeOutput
	incrMax              *engine_model.FeeOutput
	decrMax              *engine_model.FeeOutput
	ziyoubaoMax          *engine_model.FeeOutput
	revolvingDiscountMax *engine_model.FeeOutput
	revolvingRebateMax   *engine_model.FeeOutput
	dynamicMax           *engine_model.FeeOutput

	businessGroup          []*engine_model.FeeOutput
	decrGroup              []*engine_model.FeeOutput
	neutralGroup           []*engine_model.FeeOutput
	incrGroup              []*engine_model.FeeOutput
	ziyoubaoGroup          []*engine_model.FeeOutput
	revolvingDiscountGroup []*engine_model.FeeOutput
	revolvingRebateGroup   []*engine_model.FeeOutput
	otherGroup             []*engine_model.FeeOutput
}

func NewProductFull(quotation *biz_runtime.Quotation, userInfo *data.UserInfo, req *data.DynamicFeeDescListAdapter) *productFull {
	return &productFull{
		Quotation:                 quotation,
		UserInfo:                  userInfo,
		DynamicFeeDescListAdapter: req,
	}
}

func NewDynamicFeeDescList(data *data.DynamicFeeDescListAdapter, isSingleCheck bool) *DynamicFeeDescList {
	return &DynamicFeeDescList{
		isSingleCheck: isSingleCheck,
		data:          data,

		businessGroup:          make([]*engine_model.FeeOutput, 0),
		decrGroup:              make([]*engine_model.FeeOutput, 0),
		neutralGroup:           make([]*engine_model.FeeOutput, 0),
		incrGroup:              make([]*engine_model.FeeOutput, 0),
		ziyoubaoGroup:          make([]*engine_model.FeeOutput, 0),
		revolvingDiscountGroup: make([]*engine_model.FeeOutput, 0),
		revolvingRebateGroup:   make([]*engine_model.FeeOutput, 0),
		otherGroup:             make([]*engine_model.FeeOutput, 0),
	}
}

func (d *DynamicFeeDescList) GetFeeDescList(ctx context.Context) *proto.DynamicFeeDescListData {
	quotations := d.data.GetQuotations()
	userInfo := d.data.GetUserInfo()
	formType := d.data.GetFormType()
	if formType == nil {
		log.Trace.Warnf(ctx, "GetFeeDescList", "form type is nil")
		return nil
	}

	isHitExp := d.data.GetRequest().GetFormStyleExp() != 0
	outputs := make(map[int64][]*engine_model.FeeOutput)

	// 香港品类特殊聚合逻辑
	isContainHkProduct := false
	for _, quotation := range quotations {
		var (
			fi     *engine_model.FeeInput
			config *render_callback.RenderFeeDescDCMPConfig
		)
		product := NewProductFull(quotation, userInfo, d.data)

		env := fee_desc_engine.NewEnv(*formType).SetApolloParams(product).SetDcmpKeyByFormType(*formType)

		if isHitExp {
			env.SetDcmpKey(consts.EstimateV3FeeDescNewStyle)
			config = render_callback.NewStyleExpRenderFeeDescDCMPConfig()
		}

		if utils.IsUnTaxi(quotation.GetProductId()) {
			fi = input.BuildTaxiInputByQuotation(ctx, product, int(*formType), config)
		} else if productutil.IsHongKongProduct(BizConsts.ProductID(quotation.GetProductId())) {
			isContainHkProduct = true
			env = fee_desc_engine.NewEnv(consts.HarbourForm).SetApolloParams(product).SetDcmpKey(consts.DcmpKeyHarbourFeeDesc)
			fi = input.BuildHkProductFeeInputByQuotation(ctx, product, consts.HarbourForm)
		} else {
			if isHitExp {
				fi = input.BuildNormalInputByQuotation4NewStyleExp(ctx, product, int(*formType))
			} else {
				fi = input.BuildNormalInputByQuotation(ctx, product, int(*formType))
			}
		}

		output := fee_desc_engine.NewFeeEngine(fi, env).SetProductCategory(product.GetProductCategory()).Do(ctx)
		outputs[quotation.GetProductCategory()] = output
	}

	if isContainHkProduct {
		return d.BattleHarbour(ctx, outputs)
	} else {
		return d.Battle(ctx, outputs)
	}
}

func (d *DynamicFeeDescList) BattleHarbour(ctx context.Context, outputs map[int64][]*engine_model.FeeOutput) *proto.DynamicFeeDescListData {
	var pcIdToDescs = make(map[int64][]*proto.NewFormFeeDesc)
	for pcId, outs := range outputs {
		pcIdToDescs[pcId] = feedescutil.ConvertFeeOutputToDesc(ctx, outs)
	}

	cnt := 2
	if d.data.GetLang() == mambaconsts.LangEnUS {
		cnt = 1
	}
	return &proto.DynamicFeeDescListData{
		FeeDescList: feedescutil.BattleHarbourFeeDesc(ctx, pcIdToDescs, cnt),
	}
}

func (d *DynamicFeeDescList) Battle(ctx context.Context, outputs map[int64][]*engine_model.FeeOutput) *proto.DynamicFeeDescListData {
	if len(outputs) <= 0 {
		return nil
	}

	var (
		respData    = &proto.DynamicFeeDescListData{}
		feeDescList = make([]*proto.NewFormFeeDesc, 0)
	)

	outputList := d.core(ctx, outputs)
	for _, desc := range outputList {
		if desc == nil {
			continue
		}

		feeDescList = append(feeDescList, &proto.NewFormFeeDesc{
			BorderColor: desc.BorderColor,
			Content:     desc.Content,
			Icon:        desc.Icon,
			TextColor:   util.StringPtr(desc.TextColor),
		})
	}

	respData.FeeDescList = feeDescList
	return respData
}

func (d *DynamicFeeDescList) core(ctx context.Context, outputs map[int64][]*engine_model.FeeOutput) []*engine_model.FeeOutput {
	if len(outputs) < 0 {
		return nil
	}

	for _, output := range outputs {
		if len(output) <= 0 {
			continue
		}

		for _, feeDetail := range output {
			if feeDetail.Type == consts.TypeDynamic {
				if d.dynamicMax.Less(feeDetail) {
					d.dynamicMax = feeDetail
				}
			} else if feeDetail.Type == consts.TypeBusinessPay {
				d.businessGroup = append(d.businessGroup, feeDetail)
				d.businessMax = d.compareAndSet(feeDetail, d.businessMax)
			} else if feeDetail.Type == consts.TypeIncrement {
				d.incrGroup = append(d.incrGroup, feeDetail)
				d.incrMax = d.compareAndSet(feeDetail, d.incrMax)
			} else if feeDetail.Type == consts.TypeNeutral {
				d.neutralGroup = append(d.neutralGroup, feeDetail)
			} else if feeDetail.Type == consts.TypeDecrement {
				d.decrGroup = append(d.decrGroup, feeDetail)
				d.decrMax = d.compareAndSet(feeDetail, d.decrMax)
			} else if feeDetail.Type == consts.TypeRevolvingDiscount {
				d.revolvingDiscountGroup = append(d.revolvingDiscountGroup, feeDetail)
				d.revolvingDiscountMax = d.compareAndSet(feeDetail, d.revolvingDiscountMax)
			} else if feeDetail.Type == consts.TypeFeeRevolvingRebate {
				d.revolvingRebateGroup = append(d.revolvingRebateGroup, feeDetail)
				d.revolvingRebateMax = d.compareAndSet(feeDetail, d.revolvingRebateMax)
			} else if feeDetail.Type == consts.TypeZiyoubao {
				d.ziyoubaoGroup = append(d.ziyoubaoGroup, feeDetail)
				d.ziyoubaoMax = d.compareAndSet(feeDetail, d.ziyoubaoMax)
			} else {
				d.otherGroup = append(d.otherGroup, feeDetail)
			}
		}
	}

	res := d.formatOutput(ctx)

	return res
}

func (d *DynamicFeeDescList) formatOutput(ctx context.Context) []*engine_model.FeeOutput {
	var (
		businessMaxText string
		decrMaxText     string
		incrMaxText     string
		res             = make([]*engine_model.FeeOutput, 0)
	)
	if d.data == nil {
		businessMaxText = consts.FeeBusinessMaxV3
		decrMaxText = consts.FeeDecrMaxV3
		incrMaxText = consts.FeeIncrMaxV3
	} else if d.data.GetFormType() != nil && *d.data.GetFormType() == consts.SimpleForm {
		businessMaxText = consts.FeeBusinessMax
		decrMaxText = consts.FeeDecrMax
		incrMaxText = consts.FeeIncrMax
	} else if d.data.GetFormType() != nil && *d.data.GetFormType() == consts.AnyCarForm {
		businessMaxText = consts.FeeBusinessMaxAnyCar
		decrMaxText = consts.FeeDecrMaxAnyCar
		incrMaxText = consts.FeeIncrMaxAnyCar
	} else {
		businessMaxText = consts.FeeBusinessMaxV3
		decrMaxText = consts.FeeDecrMaxV3
		incrMaxText = consts.FeeIncrMaxV3
	}

	if d.dynamicMax != nil {
		// 出租车动调盒子外面需要替换icon
		replaceIcon := d.replaceDynamicIcon(ctx)
		if replaceIcon != "" {
			d.dynamicMax.Icon = replaceIcon
		}
		res = append(res, d.dynamicMax)
	}

	if output := d.renderByFeeNum(ctx, incrMaxText, d.incrGroup, d.incrMax); output != nil && len(res) < 2 {
		res = append(res, output)
	}

	if output := d.renderByFeeNum(ctx, businessMaxText, d.businessGroup, d.businessMax); output != nil && len(res) < 2 {
		res = append(res, output)
	}

	if output := d.renderByFeeNum(ctx, decrMaxText, d.decrGroup, d.decrMax); output != nil && len(res) < 2 {
		res = append(res, output)
	}

	return res
}

func (d *DynamicFeeDescList) renderOutput(ctx context.Context, key string, path string, tag map[string]string) *engine_model.FeeOutput {
	if conf := dcmp.GetJSONMap(ctx, key, path); len(conf) > 0 {
		return &engine_model.FeeOutput{
			BorderColor:    conf["border_color"].String(),
			TextColor:      conf["text_color"].String(),
			HighLightColor: conf["highlight_color"].String(),
			Content:        util.ReplaceTag(ctx, conf["content"].String(), tag),
			Icon:           conf["icon"].String(),
		}
	}

	return nil
}

func (d *DynamicFeeDescList) compareAndSet(origin *engine_model.FeeOutput, target *engine_model.FeeOutput) *engine_model.FeeOutput {
	if target == nil {
		return origin
	}

	if origin.Fee.Amount > target.Fee.Amount {
		target = origin
	}

	return target
}

func (d *DynamicFeeDescList) renderByFeeNum(ctx context.Context, path string, feeGroup []*engine_model.FeeOutput, maxFee *engine_model.FeeOutput) *engine_model.FeeOutput {
	return d.renderByFeeNumWithPrecision(ctx, path, feeGroup, maxFee, 2)
}

func (d *DynamicFeeDescList) renderByFeeNumWithPrecision(ctx context.Context, path string, feeGroup []*engine_model.FeeOutput, maxFee *engine_model.FeeOutput, precision int) *engine_model.FeeOutput {
	if path == "" || maxFee == nil {
		return nil
	}

	if d.isSingleCheck {
		return &engine_model.FeeOutput{
			BorderColor:    maxFee.BorderColor,
			TextColor:      maxFee.TextColor,
			HighLightColor: maxFee.HighLightColor,
			Content:        maxFee.Content,
			Icon:           maxFee.Icon,
		}
	}

	if len(feeGroup) <= 0 {
		return nil
	}

	var key = consts.DynamicBoxFeeDetailInfo
	if d.data.GetRequest().GetFormStyleExp() != 0 {
		key = consts.DynamicBoxFeeDetailInfoNewStyle
	}

	return d.renderOutput(ctx, key, path, map[string]string{
		"num": util.FormatPriceWithoutZero(maxFee.Fee.Amount, precision),
	})
}

// replaceDynamicIcon 是否需要替换动调icon
func (d *DynamicFeeDescList) replaceDynamicIcon(ctx context.Context) string {
	if len(d.data.GetRequest().ProductList) <= 1 {
		return ""
	}
	isUniTaxi := false
	for _, v := range d.data.GetQuotations() {
		if v.GetProductId() == 11 {
			isUniTaxi = true
			break
		}
	}
	if !isUniTaxi {
		return ""
	}
	key := consts.DynamicBoxFeeDetailInfo
	if d.data.GetRequest().GetFormStyleExp() != 0 {
		key = consts.DynamicBoxFeeDetailInfoNewStyle
	}
	dcmpConf := dcmp.GetJSONMap(ctx, key, consts.FeeDynamicMaxAnyCar)
	if dcmpConf == nil {
		return ""
	}
	return dcmpConf["icon"].String()
}

func (d *productFull) GetApolloParams(func(full *biz_runtime.ProductInfoFull) string, ...func(full *biz_runtime.ProductInfoFull) (string, string)) (string, map[string]string) {
	key := fmt.Sprintf("%d", d.GetUID())
	ret := map[string]string{
		"city":             fmt.Sprintf("%d", d.GetCityID()),
		"phone":            d.GetUserPhone(),
		"uid":              fmt.Sprintf("%d", d.GetUID()),
		"pid":              fmt.Sprintf("%d", d.GetPID()),
		"city_id":          fmt.Sprintf("%d", d.GetCityID()),
		"lang":             d.GetLang(),
		"access_key_id":    fmt.Sprintf("%d", d.GetAccessKeyId()),
		"app_version":      d.GetAppVersion(),
		"product_category": strconv.FormatInt(d.GetProductCategory(), 10),
		"menu_id":          d.GetMenuId(),
	}

	return key, ret
}

//func (d *productFull) GetApolloParamsV2(key apollo.UniqID) (string, map[string]string) {
//	ret := map[string]string{
//		"is_cross_city":    cast.ToString(cast.ToInt64(d.GetCityID() != d.GetToCityID())),
//		"county":           cast.ToString(d.GetFromCounty()),
//		"city":             fmt.Sprintf("%d", d.GetCityID()),
//		"phone":            d.GetUserPhone(),
//		"uid":              fmt.Sprintf("%d", d.GetUID()),
//		"pid":              fmt.Sprintf("%d", d.GetPID()),
//		"city_id":          fmt.Sprintf("%d", d.GetCityID()),
//		"lang":             d.GetLang(),
//		"access_key_id":    fmt.Sprintf("%d", d.GetAccessKeyId()),
//		"app_version":      d.GetAppVersion(),
//		"product_category": strconv.FormatInt(d.GetProductCategory(), 10),
//		"menu_id":          d.GetMenuId(),
//	}
//
//	if key == apollo.KeyUid {
//		return fmt.Sprintf("%d", d.GetUID()), ret
//	} else if key == apollo.KeyPid {
//		return fmt.Sprintf("%d", d.GetPID()), ret
//	} else {
//		return fmt.Sprintf("%d", d.GetPID()), ret
//	}
//}

func (d *productFull) ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string) {

	if d._pc == nil {
		d._pc = &apollo_model.ParamsConnector{
			//"is_cross_city":    cast.ToString(cast.ToInt64(d.GetCityID() != d.GetToCityID())),
			//"county":           cast.ToString(d.GetFromCounty()),
			City:  fmt.Sprintf("%d", d.GetCityID()),
			Phone: d.GetUserPhone(),
			UID:   fmt.Sprintf("%d", d.GetUID()),
			PID:   fmt.Sprintf("%d", d.GetPID()),
			//"city_id":          fmt.Sprintf("%d", d.GetCityID()),
			Lang:        d.GetLang(),
			AccessKeyID: fmt.Sprintf("%d", d.GetAccessKeyId()),
			AppVersion:  d.GetAppVersion(),
		}

		d._pc.SetCounty(cast.ToString(d.GetFromCounty())).SetIsCrossCity(cast.ToString(cast.ToInt64(d.GetCityID() != d.GetToCityID()))).
			SetMenuID(d.GetMenuId()).SetProductCategory(strconv.FormatInt(d.GetProductCategory(), 10))
	}

	return d._pc.ApolloParamsGen(keyFunc, paramsFunc...)
}

func (d *productFull) IsHitDynamicIconAb(ctx context.Context) bool {
	ret := map[string]string{
		"city":             fmt.Sprintf("%d", d.GetCityID()),
		"phone":            d.GetUserPhone(),
		"uid":              fmt.Sprintf("%d", d.GetUID()),
		"pid":              fmt.Sprintf("%d", d.GetPID()),
		"city_id":          fmt.Sprintf("%d", d.GetCityID()),
		"lang":             d.GetLang(),
		"access_key_id":    fmt.Sprintf("%d", d.GetAccessKeyId()),
		"app_version":      d.GetAppVersion(),
		"product_category": strconv.FormatInt(d.GetProductCategory(), 10),
		"menu_id":          d.GetMenuId(),
		"page_type":        "0",
		"to_city":          cast.ToString(d.GetToCityID()),
		"font_scale_type":  cast.ToString(d.GetFontStyleType()),
	}

	return ab.IsHitDynamicIconAb(ctx, fmt.Sprintf("%d", d.GetPID()), ret)
}
