package multi_point_estimate

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/multi_point_estimate/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

// BuildPointDetail ...
func BuildPointDetail(ctx context.Context, req *proto.MultiPointEstimateRequest, data *model.CommonData, full []*biz_runtime.ProductInfoFull) []*proto.PointDetail {
	pointDetail := make([]*proto.PointDetail, 0)

	for _, product := range full {
		if product == nil {
			continue
		}

		pointDetail = append(pointDetail, &proto.PointDetail{
			EstimateId:  product.GetEstimateID(),
			EstimateFee: product.GetEstimateFee(),
		})
	}

	return pointDetail
}
