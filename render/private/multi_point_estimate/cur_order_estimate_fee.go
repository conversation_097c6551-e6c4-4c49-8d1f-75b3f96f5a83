package multi_point_estimate

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/logic/multi_point_estimate/model"
)

// GetCurOrderEstimateFee ...
func GetCurOrderEstimateFee(ctx context.Context, data *model.CommonData) *float64 {
	if data == nil || data.OrderInfo == nil {
		return nil
	}

	if data.OrderInfo.EstimatePcId == nil {
		return nil
	}

	for _, product := range data.OrderInfo.ExtendFeatureParsed.MultiRequiredProduct {
		if strconv.Itoa(product.ProductCategory) == *data.OrderInfo.EstimatePcId {
			return &product.EstimateFee
		}
	}

	return nil
}
