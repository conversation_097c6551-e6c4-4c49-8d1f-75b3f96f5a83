package bargain_side

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"

	Geofence "git.xiaojukeji.com/dirpc/dirpc-go-thrift-Geofence"
	"git.xiaojukeji.com/dirpc/dirpc-go-thrift-itsThrift/didi/locsvr"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/fence"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

type TopCommunicateDataProvider interface {
	GetBillDriverMetre() int64
	GetPid() string
	GetFromLat() float64
	GetFromLng() float64
	GetMapType() string
}
type topCommunicateConfig struct {
	FenceId int64  `json:"fence_id"`
	ImgUrl  string `json:"img_url"`
	Text    string `json:"text"`
}

var ErrNilConfig = errors.New("nil config")

// 构建司乘议价预估页面顶部沟通信息
func GetTopCommunicateData(ctx context.Context, product TopCommunicateDataProvider) (data *proto.TopCommunicateData) {
	const (
		BargainValueDeliveryText = "bargain-value_delivery_text" //DCMP 文案：场景和文案对应关系
		Default                  = "default"                     //DCMP 文案：兜底文案
		ImgURL                   = "imgUrl"                      //DCMP 地址：背景图片地址
		GroupId                  = "groupId"                     //DCMP 地址：背景图片地址
	)
	data = new(proto.TopCommunicateData)
	//兜底默认文案
	data.ImgUrl = dcmp.GetJSONContentWithPath(ctx, BargainValueDeliveryText, nil, ImgURL)
	data.Text = dcmp.GetJSONContentWithPath(ctx, BargainValueDeliveryText, nil, Default)
	//获取起点命中的围栏
	cd := []*Geofence.Coordinate{{
		Lat: product.GetFromLat(),
		Lng: product.GetFromLng(),
	}}
	mapType := MapType2Int(product.GetMapType())
	groupIdStr := dcmp.GetJSONContentWithPath(ctx, BargainValueDeliveryText, nil, GroupId)
	groupId, e := strconv.Atoi(groupIdStr)
	if e != nil {
		return data
	}
	ret, err := fence.MultiInFence(ctx, cd, []int32{int32(groupId)}, mapType)
	if err != nil || len(ret) == 0 {
		return data
	}
	ids := make([]int64, 0)
	for _, m := range ret[0] {
		ids = append(ids, m.FenceIds...)
	}
	//根据围栏id获取对应的文案信息
	config, err := GetTopCommunicateConfig(ctx, ids)
	if err == nil {
		data.Text = config.Text
		data.ImgUrl = config.ImgUrl
	}
	return
}
func MapType2Int(str string) int {
	var ret locsvr.CoordType
	if str == "soso" {
		ret = locsvr.CoordType_SOSOGCJ
	} else if str == "baidu" {
		ret = locsvr.CoordType_BAIDU
	} else if str == "wgs84" {
		ret = locsvr.CoordType_WGS84
	} else {
		ret = locsvr.CoordType_SOSOGCJ
	}
	return int(ret)
}

func GetTopCommunicateConfig(ctx context.Context, hitFenceIds []int64) (*topCommunicateConfig, error) {
	var (
		_communicateConfig []*topCommunicateConfig
		ok                 bool
		s                  string
		err                error
	)
	m := apollo.GetConfig(ctx, "bargain_estimate_cpm_config", "top_communicate_config")
	if m == nil {
		return nil, ErrNilConfig
	}
	if s, ok = m["communicate"]; !ok {
		return nil, ErrNilConfig
	}
	if err = json.Unmarshal([]byte(s), &_communicateConfig); err != nil {
		return nil, err
	}
	//取命中围栏和配置围栏的交集的第一个作为配置文案
	for _, item := range _communicateConfig {
		for _, hitFenceId := range hitFenceIds {
			if item.FenceId == hitFenceId {
				return item, nil
			}
		}
	}
	return nil, ErrNilConfig
}
