package bargain_estimate

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

type IntroMsgProvider interface {
	GetDynamicTotalFee() float64
	GetBillDriverMetre() int64
	render.ApolloProvider
}

func GetIntroMsg(ctx context.Context, product IntroMsgProvider) string {
	estimateFee := util.RoundAbs(product.GetDynamicTotalFee(), 1) // 券前价
	driveMetre := product.GetBillDriverMetre()
	metre := strconv.FormatFloat(float64(driveMetre)/1000, 'f', 1, 64)
	tag := map[string]string{"drive_metre": metre, "amount": util.FormatPriceForRound(estimateFee, 1)}
	apolloKey, apolloParams := product.GetApolloParams(biz_runtime.WithPIDKey)
	pre := GetChangeBracketsPre(ctx, apolloKey, apolloParams)
	if apollo.FeatureToggle(ctx, "indriver_unsupport_taxi_texi", apolloKey, apolloParams) {
		// 屏蔽"出租车"文案
		return dcmp.GetJSONContentWithPath(ctx, "bargain-intro_msg", tag, pre+"new_price_with_metre_without_taxi")
	}
	return dcmp.GetJSONContentWithPath(ctx, "bargain-intro_msg", tag, pre+"new_price_with_metre")
}

// 获取[]->{}的前缀标识
func GetChangeBracketsPre(ctx context.Context, key string, params map[string]string) string {
	if apollo.FeatureToggle(ctx, "bargain_change_brackets_switch", key, params) {
		return "hit_"
	}
	return ""
}
