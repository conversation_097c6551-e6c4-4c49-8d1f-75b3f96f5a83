package bargain_estimate

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

type EstimateTextProvider interface {
	GetBillDriverMetre() int64
	render.ApolloProvider
}

func GetEstimateText(ctx context.Context, product EstimateTextProvider) string {
	metre := strconv.FormatFloat(float64(product.GetBillDriverMetre())/1000, 'f', 1, 64)
	tag := map[string]string{"drive_metre": metre}
	apolloKey, apolloParams := product.GetApolloParams(biz_runtime.WithPIDKey)

	if apollo.FeatureToggle(ctx, "indriver_unsupport_taxi_texi", apolloKey, apolloParams) {
		// 屏蔽"出租车"文案
		return dcmp.GetJSONContentWithPath(ctx, "bargain-estimate_text", tag, "estimate_text_without_taxi")
	}

	return dcmp.GetJSONContentWithPath(ctx, "bargain-estimate_text", tag, "estimate_text")
}
