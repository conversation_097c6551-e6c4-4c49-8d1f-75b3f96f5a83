package bargain_estimate

import (
	"context"
	"strconv"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	Bargain "git.xiaojukeji.com/gulfstream/mamba/common/biz_util/bargain"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/tidwall/gjson"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

type RecommendDataProvider interface {
	GetBizInfo() *models.PrivateBizInfo
	GetCommonBizInfo() models.CommonBizInfo
	GetCouponInfo() *PriceApi.EstimateNewFormCouponInfo
	GetDynamicTotalFee() float64
	GetUserInfo() *models.PassengerInfo
	GetCity() int32
	GetBillExtraMap() map[string]interface{}
	render.ApolloProvider
}

func GetRecommendData(ctx context.Context, product RecommendDataProvider) []*proto.BargainRecommendData {
	var (
		recData *proto.BargainRecommendData
		ret     = make([]*proto.BargainRecommendData, 0)
	)

	dcmpRecommendInfo := dcmp.GetDcmpPlainContent(ctx, "bargain-recommend_info")
	if len(dcmpRecommendInfo) == 0 {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "getRecommendInfo dcmp error filter")
		return nil
	}
	info := Bargain.GetRecommendInfo(product.GetBillExtraMap())
	recommendPriceList := make([]*Bargain.RecommendPrice, 0)
	if info != nil && info.BubbleInfo != nil && info.BubbleInfo.RecommendPrice != nil {
		recommendPriceList = info.BubbleInfo.RecommendPrice
	}
	var (
		recommendFee  float64
		recommendText string
		rightIcon     string
		rightText     string
	)

	for index, value := range recommendPriceList {
		if value == nil {
			continue
		}
		recommendFee = util.RoundAbs(value.Price, 1)
		estimateFee := util.RoundAbs(product.GetDynamicTotalFee(), 1)
		recommendText, rightIcon, rightText = getRecommendInfoByDiffAmount(ctx, product, recommendFee, estimateFee, dcmpRecommendInfo, index)
		recData = &proto.BargainRecommendData{
			RecommendPrice:     strconv.FormatFloat(recommendFee, 'f', 1, 64),
			RecommendText:      recommendText,
			RecommendRightIcon: rightIcon,
			RecommendStyle:     rightText,
		}
		ret = append(ret, recData)
	}
	//兜底使用锚定价作为推荐价
	if len(ret) == 0 {
		recommendFee = util.RoundAbs(product.GetDynamicTotalFee(), 1)
		estimateFee := util.RoundAbs(product.GetDynamicTotalFee(), 1)
		recommendText, rightIcon, rightText = getRecommendInfoByDiffAmount(ctx, product, recommendFee, estimateFee, dcmpRecommendInfo, 0)
		recData = &proto.BargainRecommendData{
			RecommendPrice:     strconv.FormatFloat(recommendFee, 'f', 1, 64),
			RecommendText:      recommendText,
			RecommendRightIcon: rightIcon,
			RecommendStyle:     rightText,
		}
		ret = append(ret, recData)
	}

	return ret
}

/*
*
根据推荐价和预估价差值获取对应的文案信息
*/
func getRecommendInfoByDiffAmount(ctx context.Context, product RecommendDataProvider, recommendFee float64, estimateFee float64, dcmpRecommendInfo string, index int) (string, string, string) {
	var (
		recommendText           string
		rightIcon               string
		recommendStyle          string
		recommendHighText0      = "recommend_high_text_0"      //DCMP 文案：推荐价高于预估价文案,首个按钮
		recommendHighText1      = "recommend_high_text_1"      //DCMP 文案：推荐价高于预估价文案，第二个按钮
		recommendLowText        = "recommend_low_text"         //DCMP 文案：推荐价低于预估价文案
		recommendEqualText      = "recommend_equal_text"       //DCMP 文案：推荐价等于预估价文案
		recommendRightHighIcon  = "recommend_right_high_icon"  //DCMP 文案：推荐价高于预估价右icon
		recommendRightLowIcon   = "recommend_right_low_icon"   //DCMP 文案：推荐价低于预估价右icon
		recommendRightEqualIcon = "recommend_right_equal_icon" //DCMP 文案：推荐价等于预估价右icon
		recommendRightHighStyle = "recommend_right_high_style" //DCMP 文案：推荐价高于预估价样式
		recommendRightLowStyle  = "recommend_right_low_style"  //DCMP 文案：推荐价低于预估价样式
	)
	getRecommendTextAndIcon := func(dcmpRecommendInfo string, textKey string, rightIconKey string, index int, recommendFee float64, differenceValue float64) (string, string) {
		var (
			recommendText string
			rightIcon     string
		)
		tag := map[string]string{"amount": strconv.FormatFloat(util.RoundAbs(recommendFee, 1), 'f', 1, 64)}
		if differenceValue != 0 {
			tag["diff_amount"] = strconv.FormatFloat(util.RoundAbs(differenceValue, 1), 'f', 1, 64)
		}
		recommendText = dcmp.TranslateTemplate(gjson.Get(dcmpRecommendInfo, textKey).String(), tag)
		//只有首个价格才会展示右icon信息
		if index == 0 {
			rightIcon = gjson.Get(dcmpRecommendInfo, rightIconKey).String()
		}
		return recommendText, rightIcon
	}
	apolloKey, apolloParams := product.GetApolloParams(biz_runtime.WithPIDKey)
	recommendLowText = GetChangeBracketsPre(ctx, apolloKey, apolloParams) + recommendLowText
	//根据推荐价和预估价的差值展示不同的样式
	differenceValue := estimateFee - recommendFee
	if differenceValue > 0 {
		//(预估价-推荐价价格)>0
		recommendText, rightIcon = getRecommendTextAndIcon(dcmpRecommendInfo, recommendLowText, recommendRightLowIcon, index, recommendFee, differenceValue)
		recommendStyle = gjson.Get(dcmpRecommendInfo, recommendRightLowStyle).String()
	} else if differenceValue < 0 {
		//(预估价-推荐价价格)<0
		switch index {
		//不同的位置展示不同的文案信息
		case 0:
			recommendText, rightIcon = getRecommendTextAndIcon(dcmpRecommendInfo, recommendHighText0, recommendRightHighIcon, index, recommendFee, 0)
		case 1:
			recommendText, rightIcon = getRecommendTextAndIcon(dcmpRecommendInfo, recommendHighText1, recommendRightHighIcon, index, recommendFee, 0)
		}
		recommendStyle = gjson.Get(dcmpRecommendInfo, recommendRightHighStyle).String()
	} else {
		//(预估价-推荐价价格)=0
		recommendText, rightIcon = getRecommendTextAndIcon(dcmpRecommendInfo, recommendEqualText, recommendRightEqualIcon, index, recommendFee, 0)
	}
	return recommendText, rightIcon, recommendStyle
}

func GetRateAndPlusByCity(ctx context.Context, phone string, cityID int32) (rate float64, plus float64) {
	rate = 1
	plus = 0
	user := ApolloModel.NewUser(phone).
		With("city", strconv.FormatInt(int64(cityID), 10)).
		With("phone", phone)
	toggle, err := ApolloSDK.FeatureToggle("bargain_recommend_fee_rate", user)
	if err != nil {
		return
	}
	if !toggle.IsAllow() {
		return
	}
	rateStr := toggle.GetAssignment().GetParameter("bargain_price-rate", "1")
	plusStr := toggle.GetAssignment().GetParameter("bargain_price-add", "0")
	if rateStr != "" {
		rateTmp := util.String2float64(ctx, rateStr)
		if rateTmp != float64(0) {
			rate = rateTmp
		}
	}
	if plusStr != "" {
		plusTmp := util.String2float64(ctx, plusStr)
		if plusTmp != float64(0) {
			plus = plusTmp
		}
	}
	return

}
func GetAnycarRecommendData(product RecommendDataProvider) []*proto.AnycarRecommendData {
	var (
		ret = make([]*proto.AnycarRecommendData, 0)
	)
	info := Bargain.GetRecommendInfo(product.GetBillExtraMap())
	if info != nil && info.WaitRepayInfo != nil && info.WaitRepayInfo.RecommendPrice != nil {
		for _, value := range info.WaitRepayInfo.RecommendPrice {
			if value == nil {
				continue
			}
			recData := &proto.AnycarRecommendData{}
			recData.RecommendPrice = util.FormatPriceForRound(value.Price, 1)
			ret = append(ret, recData)
		}
	}
	//兜底使用锚定价作为推荐价
	if len(ret) == 0 {
		recData := &proto.AnycarRecommendData{}
		recData.RecommendPrice = util.FormatPriceForRound(product.GetDynamicTotalFee(), 1)
		ret = append(ret, recData)
	}
	return ret
}
