package bargain_estimate

import (
	"context"
	"encoding/json"
	"runtime/debug"

	Bargain "git.xiaojukeji.com/gulfstream/mamba/common/biz_util/bargain"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/trace"
)

type FeeMarginProvider interface {
	GetCity() int32
	GetEstimateFee() float64
	GetDynamicTotalFee() float64
	GetBillDriverMetre() int64
	GetStartPrice() float64
	GetBillExtraMap() map[string]interface{}
	GetCommonBizInfo() models.CommonBizInfo
}

// http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=805083518
func GetFeeMargin(ctx context.Context, product FeeMarginProvider) (feeMargin *proto.BargainFeeMargin) {
	//司乘议价2.0使用定价引擎的上下限
	feeMargin = new(proto.BargainFeeMargin)
	feeMargin.Ceil = Bargain.NewCalcCeil(ctx, product.GetBillExtraMap())
	feeMargin.Floor1, feeMargin.Floor2 = Bargain.NewCalcFloor(ctx, product.GetBillExtraMap())
	// 设置ufs cache
	go func(ctx context.Context, feeMarginCopy *proto.BargainFeeMargin) {
		defer func() {
			if r := recover(); r != nil {
				log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: PANIC=%v \n %v", r, string(debug.Stack()))
			}
		}()
		params := map[string]string{"trace_id": util.GetTraceIDFromCtxWithoutCheck(ctx)}
		marginFee := map[string]string{"ceil": feeMarginCopy.Ceil.Amount, "floor1": feeMarginCopy.Floor1.Amount, "floor2": feeMarginCopy.Floor2.Amount}
		marginFeeString, err2 := json.Marshal(marginFee)
		if err2 != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "setFeature: %v", err2)
			return
		}
		kv := map[string]string{"bargain_estimate.margin_fee": string(marginFeeString)}
		if _, err2 = ufs.SetFeature(ctx, "passenger", params, kv); err2 != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "setFeature: %v", err2)
		}
	}(ctx, feeMargin)

	return
}

func GetAnyCarFeeMargin(ctx context.Context, product FeeMarginProvider) (feeMargin *proto.AnycarBargainFeeMargin) {
	feeMargin = new(proto.AnycarBargainFeeMargin)
	feeMargin.Ceil = Bargain.CalcAnycarCeil(product.GetBillExtraMap())
	feeMargin.Floor = Bargain.CalcAnyCarFloor(product.GetBillExtraMap())
	feeMargin.LowerFloor = Bargain.CalcAnyCarLowerFloor(product.GetBillExtraMap())
	return
}
