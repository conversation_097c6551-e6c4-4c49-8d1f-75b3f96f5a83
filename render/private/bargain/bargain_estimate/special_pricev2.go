package bargain_estimate

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

type SpecialPriceProvider interface {
	GetHaveTollStation() bool
	GetHighwayFee() float64
	render.ApolloProvider
}

func GetSpecialPriceTextV2(ctx context.Context, provider SpecialPriceProvider) (spt *proto.BargainSpecialPriceTextV2) {
	init := func() *proto.BargainSpecialPriceTextV2 {
		spt = new(proto.BargainSpecialPriceTextV2)
		var ruleInfo []*proto.SpecialPriceRuleInfo
		spt.SetRuleInfo(ruleInfo)
		return spt
	}
	init()
	//构建春节服务费在前，高速费在后，前端展示依赖此顺序
	buildFestivalFeeTextV2(ctx, provider, spt)
	buildTollFeeTextV2(ctx, provider, spt)
	return spt
}

// 构建高速费沟通文案
func buildTollFeeTextV2(ctx context.Context, provider SpecialPriceProvider, spt *proto.BargainSpecialPriceTextV2) {
	var ruleType []int32
	apolloKey, apolloParams := provider.GetApolloParams(biz_runtime.WithPIDKey)
	//高速费单独沟通开关来控制使用新老哪一个高速费规则
	if apollo.FeatureToggle(ctx, "bargain-toll_fee_communicate_switch", apolloKey, apolloParams) {
		if provider.GetHaveTollStation() {
			//新规则
			ruleType = append(ruleType, consts.RuleTypeExtraFeeV2)
		}
	} else {
		//老规则
		if provider.GetHighwayFee() > 0 {
			ruleType = append(ruleType, consts.RuleTypeExtraFee)
		}
	}
	if len(ruleType) == 0 {
		return
	}
	var ruleInfo = new(proto.SpecialPriceRuleInfo)
	if len(ruleType) == 1 {
		switch ruleType[0] {
		case consts.RuleTypeExtraFee:
			ruleInfo.Text = dcmp.GetJSONContentWithPath(ctx, "bargain-special_price_text", nil, strconv.Itoa(consts.RuleTypeExtraFee))
		case consts.RuleTypeExtraFeeV2:
			ruleInfo.Text = dcmp.GetJSONContentWithPath(ctx, "bargain-special_price_text", nil, strconv.Itoa(consts.RuleTypeExtraFeeV2))
		}
	}
	ruleInfo.RuleType = ruleType
	spt.RuleInfo = append(spt.RuleInfo, ruleInfo)
	return
}

// 构建春节服务费沟通文案
func buildFestivalFeeTextV2(ctx context.Context, provider SpecialPriceProvider, spt *proto.BargainSpecialPriceTextV2) {
	apolloKey, apolloParams := provider.GetApolloParams(biz_runtime.WithPIDKey)
	if apollo.FeatureToggle(ctx, "bargain-festival_fee_communicate_switch", apolloKey, apolloParams) {
		var ruleInfo = new(proto.SpecialPriceRuleInfo)
		var ruleType []int32
		ruleType = append(ruleType, consts.RuleTypeFestivalFee)
		ruleInfo.RuleType = ruleType
		ruleInfo.Text = dcmp.GetJSONContentWithPath(ctx, "bargain-special_price_text", nil, strconv.Itoa(consts.RuleTypeFestivalFee))
		spt.RuleInfo = append(spt.RuleInfo, ruleInfo)
	}
}
