package carpool_invitation

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"github.com/tidwall/gjson"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

type ButtonProvider interface {
	IsRouteMatch() bool
	GetUserPID() int64
	GetInviterOid() string
	IsEstimateByCache() bool
	CanEstimateByCache(ctx context.Context) bool
	GetOrderType() int16
	GetMatchDepartureRange() string
	ApolloParam() *ApolloModel.User
}

func BuildButtonList(ctx context.Context, prov ButtonProvider) []*proto.ButtonItem {
	res := make([]*proto.ButtonItem, 0)
	config := dcmp.GetJSONMap(ctx, DcmpKeyPinchecheInvitation, DcmpPathButtonInfo)

	if prov.IsRouteMatch() {
		// "加入行程"
		joinButton := buildButtonItem(ctx, config["join"].Map())
		joinButton.Params = map[string]interface{}{
			"type":            prov.GetOrderType(),
			"departure_range": prov.GetMatchDepartureRange(),
		}
		res = append(res, joinButton)
		//  "不加入，自己呼叫"
		res = append(res, buildButtonItem(ctx, config["refuse"].Map()))
		return res
	}

	// "呼叫特价拼车"  不顺路
	res = append(res, buildButtonItem(ctx, config["order_alone"].Map()))
	// "返回继续加入拼友行程"
	if prov.CanEstimateByCache(ctx) && !prov.IsEstimateByCache() && showReJoinButton(ctx, prov) {
		rejoinButton := buildButtonItem(ctx, config["rejoin"].Map())
		rejoinButton.Params = map[string]interface{}{"estimate_type": 1}
		res = append(res, rejoinButton)
	}

	return res
}

func BuildOmegaInfo(ctx context.Context, prov ButtonProvider) map[string]interface{} {
	// 是否顺路 && 是否可返回顺路
	res := make(map[string]interface{})
	if prov.IsRouteMatch() {
		res["match_code"] = 1
	} else {
		res["match_code"] = 0
		if prov.CanEstimateByCache(ctx) && !prov.IsEstimateByCache() {
			res["re_join"] = 1
		}
	}

	return res
}

func buildButtonItem(ctx context.Context, infoMap map[string]gjson.Result) *proto.ButtonItem {
	res := proto.ButtonItem{
		Type:       int32(dcmp.GetNumContentByPath(infoMap, "type")),
		Icon:       *dcmp.GetStringDcmpContentByPath(infoMap, "icon"),
		Content:    *dcmp.GetStringDcmpContentByPath(infoMap, "content"),
		StartColor: *dcmp.GetStringDcmpContentByPath(infoMap, "start_color"),
		EndColor:   *dcmp.GetStringDcmpContentByPath(infoMap, "end_color"),
	}

	return &res
}

func showReJoinButton(ctx context.Context, prov ButtonProvider) bool {
	toggle, err := apollo.FeatureToggle("pincheche_invite_rejoin_button_fix", prov.ApolloParam())
	if err != nil || toggle == nil {
		return false
	}

	return toggle.IsAllow()
}
