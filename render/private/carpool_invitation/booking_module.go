package carpool_invitation

import (
	"context"
	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_fission/after_dds_fission"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/pincheche"
	"time"
)

type BookingModuleProvider interface {
	GetDepartureTime() time.Time
	GetTimeSpan() []*EstimateDecision.TimeSpanV2
	IsRouteMatch() bool
}

func BuildBookingModule(ctx context.Context, prov BookingModuleProvider) *proto.InvitationBookingModule {
	timeSpans := prov.GetTimeSpan()
	if len(timeSpans) == 0 {
		return nil
	}

	var departureTime = prov.GetDepartureTime().Unix()
	config := dcmp.GetJSONMap(ctx, pincheche.DcmpKeyPinchecheV2, pincheche.DcmpPathBookingModule)

	res := &proto.InvitationBookingModule{
		Title:      *dcmp.GetStringDcmpContentByPath(config, "title"),
		Subtitle:   *dcmp.GetStringDcmpContentByPath(config, "sub_title"),
		ButtonText: *dcmp.GetStringDcmpContentByPath(config, "button_text"),
	}

	_, timeSpanNew := after_dds_fission.ConvertTimeSpan(timeSpans, 0, departureTime)

	res.TimeSpan = timeSpanNew

	if prov.IsRouteMatch() {
		res.BottomText = dcmp.GetJSONContentWithPath(ctx, DcmpKeyPinchecheInvitation, nil, "booking_module_bottom_text")
	}

	return res
}
