package estimate_v4

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_category"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/combo_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

type ExtraMapProvider interface {
	render.ProductProvider
	GetTabId() string
	GetRouteType() int64
	GetPrivateBizInfo() *models.PrivateBizInfo
}

func GetExtraMap(ctx context.Context, prov ExtraMapProvider) *proto.ExtraMapV4 {
	extraMap := &proto.ExtraMapV4{}
	extraMap.SetProductId(prov.GetProductId())
	extraMap.SetBusinessId(prov.GetBusinessID())
	extraMap.SetComboType(prov.GetComboType())
	extraMap.SetRequireLevel(util.ToInt32(prov.GetRequireLevel()))
	extraMap.SetLevelType(prov.GetLevelType())
	extraMap.SetComboId(prov.GetPrivateBizInfo().ComboID)
	extraMap.SetRouteType(prov.GetRouteType())
	extraMap.SetIsSpecialPrice(util.ToInt32(prov.IsSpecialPrice()))
	extraMap.SetCountPriceType(prov.GetCountPriceType())

	// 司乘议价&单勾导流场景下，将导流来源标识给到发单
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdBargain && !prov.GetPrivateBizInfo().BargainIsMultiSelect {
		extraMap.SetBargainFromType(1)
	}

	// 深港车口岸控制
	if prov.GetComboType() == combo_type.ComboTypeShenGangFlatRate {
		// todo 后续补充
	}

	needAuth, exist := prov.GetPrivateBizInfo().UfsTripCloudAuthBusinessID[prov.GetBusinessID()]
	if exist && needAuth {
		extraMap.SetIsDefaultAuth(1)
	}
	// 补天出租车需要授权的话
	if prov.GetPrivateBizInfo().IsButianNeedAuth && prov.GetProductCategory() == product_category.ProductCategoryFastTaxi {
		extraMap.SetIsDefaultAuth(1)
	}

	// 三方聚合表单is_default_auth = 0
	if tab.IsClassifyTab(prov.GetTabId()) {
		extraMap.SetIsDefaultAuth(0)
	}

	if carpool.TypeMiniBus == prov.GetCarpoolType() {
		if prov.GetPrivateBizInfo().MiniBusPreMatch != nil &&
			prov.GetPrivateBizInfo().MiniBusPreMatch.EtpInfo != nil {
			minibusEtp := prov.GetPrivateBizInfo().MiniBusPreMatch.EtpInfo.EtpTimeDuration
			extraMap.SetEtp(int32(minibusEtp))
		}
	}

	return extraMap
}
