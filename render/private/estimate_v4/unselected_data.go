package estimate_v4

import (
	"context"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/category_carpool"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"github.com/tidwall/gjson"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

type UnselectedProvider interface {
	render.ProductProvider
	render.ActivityInfoProvider
	render.ApolloProvider
	GetCarpoolVCard(ctx context.Context) (string, int32)
	GetPageType() int32
	fee_info_render.PriceInfoProvider
	fee_info_render.PriceInfoDescListProvider
}

func GetUnselectedData(ctx context.Context, prov UnselectedProvider) *proto.NewFormUnselectedData {
	var (
		unselectedData  = &proto.NewFormUnselectedData{}
		multiPriceList  = make([]*proto.NewFormV4MultiPrice, 0)
		multiPriceLeft  = &proto.NewFormV4MultiPrice{}
		multiPriceRight = &proto.NewFormV4MultiPrice{}
	)

	if !hitUnselected(ctx, prov) {
		return nil
	}

	dcmpInfo := dcmp.GetDcmpPlainContent(ctx, "estimate_form_v3-carpool_unselected")
	if len(dcmpInfo) == 0 {
		return nil
	}

	multiPriceLeft.SetFeeIconUrl(dcmp.TranslateTemplate(gjson.Get(dcmpInfo, "fee_icon_url").String(), nil))
	feeMsg := dcmp.TranslateTemplate(gjson.Get(dcmpInfo, "fee_msg").String(), map[string]string{
		"num": util.RemoveSuffixZero(util.FormatPrice(fee_info_render.GetCarpoolFeeAmount(ctx, prov), 2)),
	})
	multiPriceRight.SetFeeMsg(feeMsg)

	multiPriceList = []*proto.NewFormV4MultiPrice{
		multiPriceLeft,
		multiPriceRight,
	}

	unselectedData.SetMultiPriceList(multiPriceList)
	unselectedData.SetFeeDescList(category_carpool.GetCarpoolFeeDescList(ctx, prov, prov.GetPageType()))
	return unselectedData
}

func hitUnselected(ctx context.Context, prov UnselectedProvider) bool {
	if prov.GetProductCategory() != estimate_pc_id.EstimatePcIdCarpoolStation {
		return false
	}

	if source, payStatus := prov.GetCarpoolVCard(ctx); source == "" && payStatus == -1 {
		return false
	}

	if prov.IsCarpoolUnSuccessFlatPriceShowAsCapPrice(ctx) {
		return false
	}

	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	isAllow, assignment := apollo.FeatureExp(ctx, "carpool_station_unselected_new_style_ab", pidKey, params)
	if isAllow && assignment.GetGroupName() == "treatment_group" {
		return true
	}

	return false
}
