package bargain_range_estimate

import (
	"context"
	"encoding/json"
	"math"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/spf13/cast"
)

type BargainRangeProvider interface {
	GetPriceLimitUpper() int32
	GetPriceLimitLower() int32
	GetRecommendPriceUpper() int32
	GetRecommendPriceLower() int32
	GetWaitReplyPriceUpper() int32
	GetAnswerRate() int32
	GetFastCarEstimateFee() float64
	GetSpFastCarEstimateFee() float64
	GetHighWayFee() float64
	GetQuotation() *biz_runtime.Quotation
}

func BuildView(ctx context.Context, viewAdapter BargainRangeProvider, data *proto.BargainRangeEstimateData) {
	var (
		dcmpMap  map[string]string
		ruleType []int32
	)
	template := dcmp.GetDcmpPlainContent(ctx, "bargain_range-estimate_info")
	if err := json.Unmarshal([]byte(template), &dcmpMap); err != nil {
		return
	}

	data.BackButtonText = dcmpMap["back_button_text"]
	data.BackgroundImg = dcmpMap["background_img"]
	if viewAdapter.GetAnswerRate() > 0 {
		data.AnswerRateText = util.ReplaceTag(ctx, dcmpMap["answer_rate_text"], map[string]string{
			"num": cast.ToString(viewAdapter.GetAnswerRate()),
		})
		//不同应答率展示不同颜色
		data.AnswerRateColor = GetAnswerRateColor(dcmpMap, viewAdapter.GetAnswerRate())
	}
	data.LowPriceBubbleText = dcmpMap["low_price_bubble_text"]
	data.HighPriceBubbleText = dcmpMap["high_price_bubble_text"]
	data.PriceLimitUpperText = dcmpMap["price_limit_upper_text"]
	data.SubTitle = dcmpMap["sub_title"]
	if viewAdapter.GetHighWayFee() > 0 {
		specialPriceRuleInfo := new(proto.BargainRangeSpecialPriceRuleInfo)
		ruleType = append(ruleType, consts.RuleTypeExtraFee)
		specialPriceRuleInfo.RuleType = ruleType
		specialPriceRuleInfo.Text = dcmpMap["special_price_rule_text"]
		specialPriceRuleInfo.EstimateId = viewAdapter.GetQuotation().EstimateId
		specialPriceRuleInfo.Event = "special_rule"
		data.SpecialPriceRuleInfo = specialPriceRuleInfo
	}
	data.BtnText = dcmpMap["btn_text"]
	stepSize := cast.ToInt32(math.Ceil(float64(viewAdapter.GetPriceLimitUpper()-viewAdapter.GetPriceLimitLower()) / 50))
	//若stepSize为空，兜底步长为1
	if stepSize <= 0 {
		stepSize = 1
	}
	data.StepSize = stepSize
}

func GetAnswerRateColor(dcmpMap map[string]string, answerRate int32) string {
	//x、y默认值：x=80,y=40
	answerRateX := cast.ToInt32(dcmpMap["answer_rate_x"])
	answerRateY := cast.ToInt32(dcmpMap["answer_rate_y"])
	if answerRate > answerRateX {
		return dcmpMap["answer_rate_color_over_x"]
	} else if answerRateY <= answerRate && answerRate <= answerRateX {
		return dcmpMap["answer_rate_color_range_xy"]
	} else {
		return dcmpMap["answer_rate_color_less_y"]
	}
}
