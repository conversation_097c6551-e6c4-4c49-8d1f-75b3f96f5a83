package bargain_range_estimate

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"
)

func BuildData(ctx context.Context, viewAdapter BargainRangeProvider, data *proto.BargainRangeEstimateData) {
	data.PriceLimitUpper = viewAdapter.GetPriceLimitUpper()
	data.PriceLimitLower = viewAdapter.GetPriceLimitLower()
	data.RecommendPriceUpper = viewAdapter.GetRecommendPriceUpper()
	data.RecommendPriceLower = viewAdapter.GetRecommendPriceLower()
	data.WaitReplyPriceUpper = viewAdapter.GetWaitReplyPriceUpper()
	data.FastCarEstimateFee = viewAdapter.GetFastCarEstimateFee()
	data.SpFastCarEstimateFee = viewAdapter.GetSpFastCarEstimateFee()
	//构建发单参数
	quotation := viewAdapter.GetQuotation()
	paramMap := make(map[string]interface{})
	paramMap["estimate_id"] = quotation.EstimateId
	paramMap["pay_type"] = quotation.GetDefaultPayType()
	paramMap["product_category"] = quotation.ProductCategory
	paramMap["product_id"] = quotation.GetProductId()
	paramMap["business_id"] = quotation.GetBusinessId()
	paramMap["combo_type"] = quotation.GetComboType()
	paramMap["require_level"] = cast.ToInt32(quotation.GetRequireLevel())
	paramMap["level_type"] = quotation.GetLevelType()
	paramMap["route_type"] = quotation.GetRouteType()
	paramMap["is_special_price"] = cast.ToInt32(quotation.GetIsSpecialPrice())
	paramList := make([]map[string]interface{}, 0)
	paramList = append(paramList, paramMap)
	paramListStr, err := json.Marshal(paramList)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "bargain range send order params marshal err %v", err)
	}
	data.MultiRequireProduct = string(paramListStr)
}
