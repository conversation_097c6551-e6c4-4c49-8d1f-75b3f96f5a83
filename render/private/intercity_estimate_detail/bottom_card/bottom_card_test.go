package bottom_card

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	. "github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/tidwall/gjson"
	"testing"
)

func TestBuildRefundAndChangeTerms(t *testing.T) {
	ctx := context.Background()
	product := &biz_runtime.ProductInfoFull{
		Product: &models.Product{
			ShiftID: "123",
		},
		BaseReqData: &models.BaseReqData{
			PassengerInfo: models.PassengerInfo{
				Phone: "1234567890",
			},
			AreaInfo: models.AreaInfo{
				Area: 1,
			},
			CommonInfo: models.CommonInfo{
				AccessKeyID: 1,
				AppVersion:  "1.0.0",
			},
			CommonBizInfo: models.CommonBizInfo{
				ComboId: 123,
			},
		},
	}

	text := map[string]gjson.Result{
		"refund_and_change_terms": {
			Type: gjson.JSON,
			Raw:  `{"refund_term" : "退款条款: [url]","card_term":"出行卡条款: [url]","third_party_term":"第三方条款: [url]","desc_text":"描述文本","subtitle_text":"副标题","popup":{"title":"popupTitle"}}`,
		},
	}
	bc := &bottomCard{
		product: product,
		text:    text,
	}

	PatchConvey("should handle normal case", t, func() {
		Mock(dcmp.GetDcmpContent).Return(`{"refund_click_url":"https://refund.url"}`).Build()
		Mock(apollo.FeatureToggle).Return(true).Build()

		terms := bc.buildRefundAndChangeTerms(ctx)
		So(terms, ShouldNotBeNil)
		So(terms.HasCheckBox, ShouldEqual, 1)
		So(terms.Desc, ShouldContainSubstring, "退款条款")
		So(terms.Popup.SubTitle, ShouldContainSubstring, "副标题")
	})

	PatchConvey("should return nil when refund click url is empty", t, func() {
		Mock(dcmp.GetDcmpContent).Return(`{"refund_click_url":""}`).Build()

		terms := bc.buildRefundAndChangeTerms(ctx)
		So(terms, ShouldBeNil)
	})

	PatchConvey("should handle case without bus card", t, func() {
		Mock((*biz_runtime.ProductInfoFull).GetBusCard).Return(nil).Build()
		Mock(dcmp.GetDcmpContent).Return(`{"refund_click_url":"https://refund.url"}`).Build()
		Mock(apollo.FeatureToggle).Return(false).Build()

		terms := bc.buildRefundAndChangeTerms(ctx)
		So(terms, ShouldNotBeNil)
		So(terms.HasCheckBox, ShouldEqual, 0)
	})
}
