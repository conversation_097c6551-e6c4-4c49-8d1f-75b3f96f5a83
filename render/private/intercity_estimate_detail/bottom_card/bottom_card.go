package bottom_card

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_estimate_detail/travel_card_info"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	DetailConst "git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	intercity_sku2 "git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_sku"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"github.com/shopspring/decimal"
	"github.com/tidwall/gjson"
)

type bottomCard struct {
	product      *biz_runtime.ProductInfoFull
	text         map[string]gjson.Result
	mode         int32
	isNewVersion bool
}

// NewBottomCard ...
func NewBottomCard(product *biz_runtime.ProductInfoFull, text map[string]gjson.Result, mode int32, isNewVersion bool) *bottomCard {
	return &bottomCard{
		product:      product,
		text:         text,
		mode:         mode,
		isNewVersion: isNewVersion,
	}
}

func (bc *bottomCard) Render(ctx context.Context) *proto.BottomCard {
	return bc.getBottomCard(ctx)
}

// getBottomCard ...
func (bc *bottomCard) getBottomCard(ctx context.Context) *proto.BottomCard {
	if bc.text == nil {
		return nil
	}

	if bc.product == nil || bc.product.BaseReqData == nil {
		return nil
	}

	return &proto.BottomCard{
		EstimateInfo: bc.buildEstimateInfo(ctx),
		TermsList:    bc.buildTermsList(ctx),
	}
}

// buildTermsList ...
func (bc *bottomCard) buildTermsList(ctx context.Context) []*proto.BottomTerms {
	if bc.text == nil || bc.product == nil || bc.product.BaseReqData == nil {
		return nil
	}
	termsList := make([]*proto.BottomTerms, 0)
	terms := bc.buildRefundAndChangeTerms(ctx)
	if terms != nil {
		termsList = append(termsList, terms)
	}
	if len(termsList) == 0 {
		return nil
	}
	return termsList
}

func (bc *bottomCard) buildRefundAndChangeTerms(ctx context.Context) *proto.BottomTerms {
	termsListStr := bc.text["refund_and_change_terms"].String()
	terms := new(proto.BottomTerms)
	err := json.Unmarshal([]byte(termsListStr), &terms)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "buildTermsList fail||err:%v", err)
		return nil
	}
	//替换跳转url
	shiftID := bc.product.Product.ShiftID
	chargeRuleStr := dcmp.GetDcmpContent(ctx, intercity.ChargeRuleEntranceDcmpKey, nil)
	if chargeRuleStr == "" || gjson.Get(chargeRuleStr, "refund_click_url").String() == "" {
		return nil
	}
	refundClickUrl := gjson.Get(chargeRuleStr, "refund_click_url").String()
	// 退改签规则跳转链接
	linkUrl := util.ReplaceTag(ctx, refundClickUrl, map[string]string{
		"bus_service_shift_id": shiftID,
		"rules_type":           util.Int32String(intercity_estimate_detail.RulesTypeRefund),
		"product_id":           util.Int642String(bc.product.GetProductId()),
	})
	totalTermStr := ""
	refundUrl := util.ReplaceTag(ctx, bc.text["refund_and_change_terms"].Map()["refund_term"].String(), map[string]string{"url": linkUrl})
	totalTermStr += refundUrl
	// 出行卡购买的跳转链接
	if bc.product.GetBusCard() != nil && bc.product.GetBusCard().BatchId > 0 {
		param := travel_card_info.BuildTermCardByPageParam(bc.product)
		cardUrl := util.ReplaceTag(ctx, bc.text["refund_and_change_terms"].Map()["card_term"].String(), param)
		totalTermStr += cardUrl
	}
	// 第三方协议的跳转链接
	totalTermStr += util.ReplaceTag(ctx, bc.text["refund_and_change_terms"].Map()["third_party_term"].String(), map[string]string{"product_id": cast.ToString(bc.product.GetProductId())})
	terms.Desc = bc.text["refund_and_change_terms"].Map()["desc_text"].String() + totalTermStr
	terms.Popup.SubTitle = bc.text["refund_and_change_terms"].Map()["subtitle_text"].String() + totalTermStr

	// 命中路线id则设置勾选框
	apolloParams := map[string]string{
		"phone":         bc.product.BaseReqData.PassengerInfo.Phone,
		"city":          cast.ToString(bc.product.BaseReqData.AreaInfo.Area),
		"access_key_id": cast.ToString(bc.product.BaseReqData.CommonInfo.AccessKeyID),
		"app_version":   cast.ToString(bc.product.BaseReqData.CommonInfo.AppVersion),
		"route_id":      cast.ToString(bc.product.GetComboID()),
		"product_id":    cast.ToString(bc.product.GetProductId()),
	}
	if apollo.FeatureToggle(ctx, "station_bus_terms_check_box_toggle", bc.product.BaseReqData.PassengerInfo.Phone, apolloParams) {
		terms.HasCheckBox = 1
	}
	return terms
}

// buildEstimateInfo ...
func (bc *bottomCard) buildEstimateInfo(ctx context.Context) *proto.EstimateInfo {
	var (
		feeAmount string
		feeMsg    string
	)
	if bc.text == nil || bc.product == nil || bc.product.BaseReqData == nil ||
		((bc.product.BaseReqData.CommonBizInfo.AgentType == DetailConst.QuickScanCode ||
			bc.product.BaseReqData.CommonBizInfo.AgentType == DetailConst.FormatScanCode) && !bc.product.BaseReqData.CommonBizInfo.NeedVisitPrice) {
		return nil
	}

	disable, disableToast := bc.buildBottomCardDisable(ctx)
	if disable == intercity_estimate_detail.Ban {
		return &proto.EstimateInfo{
			FeeMsg:            bc.text["ban_fee_msg"].String(),
			ConfirmButtonText: bc.text["confirm_button"].Map()["text"].String(),
			Disable:           disable,
			DisableToast:      disableToast,
		}
	}
	if biz_runtime.IsHitBusCardTyingSale(ctx, bc.product.BaseReqData, bc.product) {
		feeAmount = decimal.NewFromFloat(bc.product.GetCombineEstimateFee()).Round(2).String()
		feeMsg = fee_info_render.BusDetailFeeMsg(ctx, bc.product)
	} else {
		feeAmount = decimal.NewFromFloat(bc.product.GetEstimateFee()).Round(2).String()
		feeMsg = fee_info_render.FeeMsgV2(ctx, bc.product)
	}
	return &proto.EstimateInfo{
		EstimateId:  bc.product.GetEstimateID(),
		FeeAmount:   feeAmount,
		FeeMsg:      feeMsg,
		FeeDescList: intercity_sku2.FeeDescList(ctx, bc.product, bc.product),
		FeeDetailUrl: dcmp.GetDcmpContent(ctx, "intercity_station-fee_detail_url_v4", map[string]string{
			"city_id": strconv.Itoa(int(bc.product.BaseReqData.AreaInfo.Area)),
		}),
		ExtraMap:          bc.buildExtraMap(ctx),
		ConfirmButtonText: bc.text["confirm_button"].Map()["text"].String(),
	}
}

// buildExtraMap ...
func (bc *bottomCard) buildExtraMap(ctx context.Context) *proto.IntercityNewOrderParam {
	if bc.product == nil || bc.product.GetBizInfo() == nil {
		return nil
	}

	return &proto.IntercityNewOrderParam{
		ProductCategory: bc.product.GetProductCategory(),
		ComboType:       bc.product.GetComboType(),
		ComboId:         bc.product.GetBizInfo().ComboID,
		RequireLevel:    bc.product.Product.RequireLevelInt,
		BusinessId:      bc.product.GetBusinessID(),
		PageType:        bc.product.GetPageType(),
		RouteType:       int32(bc.product.GetRouteType()),
		CarpoolSeatNum:  &bc.product.GetBizInfo().CarpoolSeatNum,
	}
}

// buildBottomCardDisable ...
func (bc *bottomCard) buildBottomCardDisable(ctx context.Context) (int32, *string) {
	if bc.mode == models.RealName.ToInt32() {
		if bc.product.BaseReqData.CommonBizInfo.PassengerDetailInfo == nil {
			return intercity_estimate_detail.Ban, util.String2PtrString(bc.text["ban_no_passenger_info_toast"].String())
		}

		if bc.product.BaseReqData.CommonBizInfo.PassengerDetailInfo != nil && len(bc.product.BaseReqData.CommonBizInfo.PassengerDetailInfo.PassengerList) <= 0 {
			return intercity_estimate_detail.Ban, util.String2PtrString(bc.text["ban_no_check_people_toast"].String())
		}

		if bc.product.GetBizInfo() != nil && bc.product.GetBizInfo().CarpoolSeatNum <= 0 && bc.product.GetBizInfo().CarryChildrenNum <= 0 {
			return intercity_estimate_detail.Ban, util.String2PtrString(bc.text["ban_no_people_toast"].String())
		}

		if bc.product.GetBizInfo() != nil && bc.product.GetBizInfo().CarpoolSeatNum <= 0 && bc.product.GetBizInfo().CarryChildrenNum >= 0 {
			return intercity_estimate_detail.Ban, util.String2PtrString(bc.text["ban_only_carry_children_toast"].String())
		}
	} else if bc.mode == models.NoRealName.ToInt32() {
		if bc.product.GetBizInfo() != nil && bc.product.GetBizInfo().CarpoolSeatNum <= 0 && bc.product.GetBizInfo().CarryChildrenNum <= 0 {
			return intercity_estimate_detail.Ban, util.String2PtrString(bc.text["ban_no_people_toast"].String())
		}

		if bc.product.GetBizInfo() != nil && bc.product.GetBizInfo().CarpoolSeatNum <= 0 && bc.product.GetBizInfo().CarryChildrenNum >= 0 {
			return intercity_estimate_detail.Ban, util.String2PtrString(bc.text["ban_only_carry_children_toast"].String())
		}
	}

	return intercity_estimate_detail.NotBan, nil
}
