package order_params

import (
	"context"
	"github.com/spf13/cast"

	DetailConst "git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"

	"github.com/tidwall/gjson"
)

const FenceStation = 1

type orderParams struct {
	product                *biz_runtime.ProductInfoFull
	text                   map[string]gjson.Result
	req                    *proto.IntercityEstimateDetailRequest
	isNewVersionAnyCarFlow bool
}

// NewOrderParams ...
func NewOrderParams(product *biz_runtime.ProductInfoFull, text map[string]gjson.Result, req *proto.IntercityEstimateDetailRequest, isNewVersionAnyCarFlow bool) *orderParams {
	return &orderParams{
		product:                product,
		text:                   text,
		req:                    req,
		isNewVersionAnyCarFlow: isNewVersionAnyCarFlow,
	}
}

// Render ...
func (rc *orderParams) Render(ctx context.Context) *proto.OrderParams {
	return rc.getOrderParams(ctx)
}

// getOrderParams ...
func (rc *orderParams) getOrderParams(ctx context.Context) *proto.OrderParams {
	if rc.product == nil || rc.product.Product == nil || rc.product.GetBizInfo() == nil {
		return nil
	}

	intercityData := rc.product.Product.BizInfo.IntercityData

	res := &proto.OrderParams{
		BusServiceShiftId: rc.product.Product.ShiftID,
		DepartureTime:     util.Int642String(rc.product.GetBizInfo().DepartureTime),
		Type:              int32(rc.product.Product.OrderType),
		ComboId:           int32(rc.product.GetBizInfo().ComboID),
		FromStationId:     int32(intercityData.StartStationID),
		ToStationId:       int32(intercityData.DestStationID),
		FromName:          &rc.product.BaseReqData.AreaInfo.FromName,
		ToName:            &rc.product.BaseReqData.AreaInfo.ToName,
	}
	if rc.product.GetAreaInfo() != nil {
		if rc.product.GetCommonBizInfo().StartStation != nil && rc.product.GetCommonBizInfo().StartStation.StationSceneType == FenceStation && len(rc.req.GetStartLat()) != 0 && len(rc.req.GetStartLng()) != 0 {
			res.Flng = util.StringPtr(cast.ToString(rc.req.GetStartLng()))
			res.Flat = util.StringPtr(cast.ToString(rc.req.GetStartLat()))
		} else {
			res.Flat = util.StringPtr(cast.ToString(rc.product.GetAreaInfo().FromLat))
			res.Flng = util.StringPtr(cast.ToString(rc.product.GetAreaInfo().FromLng))
		}
		if rc.product.GetCommonBizInfo().EndStation != nil && rc.product.GetCommonBizInfo().EndStation.StationSceneType == FenceStation && len(rc.req.GetEndLat()) != 0 && len(rc.req.GetEndLng()) != 0 {
			res.Tlng = util.StringPtr(cast.ToString(rc.req.GetEndLng()))
			res.Tlat = util.StringPtr(cast.ToString(rc.req.GetEndLat()))
		} else {
			res.Tlng = util.StringPtr(cast.ToString(rc.product.GetAreaInfo().ToLng))
			res.Tlat = util.StringPtr(cast.ToString(rc.product.GetAreaInfo().ToLat))
		}
		res.FromName = util.StringPtr(rc.product.GetCommonBizInfo().StationInfo.StartPoiName)
		res.ToName = util.StringPtr(rc.product.GetCommonBizInfo().StationInfo.EndPoiName)
		res.StartingPoiId = util.StringPtr(rc.product.GetCommonBizInfo().StationInfo.StartPoiId)
		res.DestPoiId = util.StringPtr(rc.product.GetCommonBizInfo().StationInfo.EndPoiId)
	}
	if rc.product.BaseReqData.CommonBizInfo.AgentType == DetailConst.FormatScanCode ||
		rc.product.BaseReqData.CommonBizInfo.AgentType == DetailConst.QuickScanCode {
		res.AgentType = rc.product.BaseReqData.CommonBizInfo.AgentType
	}

	rc.RenderNewParams(res, rc.req)
	return res
}

func (rc *orderParams) RenderNewParams(params *proto.OrderParams, req *proto.IntercityEstimateDetailRequest) {
	if params == nil || !rc.isNewVersionAnyCarFlow || req.IsBestShift == nil || *req.IsBestShift != 1 {
		return
	}

	params.StartingPoiId = &req.StartPoiId
	params.FromName = &req.StartPoiName
	poiCityStart := cast.ToInt32(req.StartPoiCity)
	params.Area = &poiCityStart
	params.Flat = &req.StartLat
	params.Flng = &req.StartLng

	params.DestPoiId = &req.EndPoiId
	params.ToName = &req.EndPoiName
	poiCityEnd := cast.ToInt32(req.EndPoiCity)
	params.ToArea = &poiCityEnd
	params.Tlat = &req.EndLat
	params.Tlng = &req.EndLng
}
