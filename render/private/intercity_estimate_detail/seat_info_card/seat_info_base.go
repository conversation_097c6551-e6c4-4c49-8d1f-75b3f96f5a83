package seat_info_card

import (
	"context"

	Dirpc_SDK_Brick "git.xiaojukeji.com/dirpc/dirpc-go-http-Brick"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_estimate_detail/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

type seatInfoBase struct {
	product *biz_runtime.ProductInfoFull
	text    map[string]gjson.Result

	supportTicketTypes map[int32]bool
	hasDiscountSupport map[int32]*model.IdentityDiscountInfo // 必须有优惠金额
}

func newSeatInfoBase(product *biz_runtime.ProductInfoFull, text map[string]gjson.Result) *seatInfoBase {
	return &seatInfoBase{
		product: product,
		text:    text,
	}
}

// buildSupportTicketType 获取当前班次支持的票类型
func (sib *seatInfoBase) buildSupportTicketType(ctx context.Context) {
	supportTicketTypes := make(map[int32]bool)

	// 兜底成人
	supportTicketTypes[seat_selection_consts.Adult.ToInt32()] = true

	if sib.product == nil || sib.product.BaseReqData == nil || sib.product.BaseReqData.CommonBizInfo.IdentityPageInfo == nil {
		sib.supportTicketTypes = supportTicketTypes
		return
	}

	if sib.product.BaseReqData.CommonBizInfo.IdentityPageInfo != nil {
		ruleInfo := sib.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo
		if ruleInfo != nil && len(ruleInfo.SpecialSeatRules) > 0 {
			for _, rules := range ruleInfo.SpecialSeatRules {
				if rules == nil {
					continue
				}

				if rules.RuleName == seat_selection_consts.ChildTicket && rules.IsSupport == 1 {
					supportTicketTypes[seat_selection_consts.Children.ToInt32()] = true

					// 兼容老携童票逻辑
					for _, childTicket := range rules.GetTypePercent() {
						if childTicket.GetType() == seat_selection_consts.CarryChildren.ToInt64() {
							supportTicketTypes[seat_selection_consts.CarryChildren.ToInt32()] = true
							break
						}
					}
				}

				// 携童票新逻辑
				if rules.RuleName == seat_selection_consts.CarryChildTicket && rules.IsSupport == 1 {
					supportTicketTypes[seat_selection_consts.CarryChildren.ToInt32()] = true
				}

				// 优待票逻辑
				if rules.RuleName == seat_selection_consts.CouponTicket && rules.IsSupport == 1 {
					supportTicketTypes[seat_selection_consts.PreferentialPeople.ToInt32()] = true
				}

				if rules.RuleName == seat_selection_consts.OwnerTicket && rules.IsSupport == 1 {
					supportTicketTypes[seat_selection_consts.HomeOwnerAdult.ToInt32()] = true
					supportTicketTypes[seat_selection_consts.HomeOwnerChildren.ToInt32()] = true
					supportTicketTypes[seat_selection_consts.HomeOwnerOldMan.ToInt32()] = true
				}
			}
		}
	}

	sib.supportTicketTypes = supportTicketTypes
}

func (sib *seatInfoBase) getIdentityDiscountMap(ctx context.Context) {
	hasDiscountSupport := make(map[int32]*model.IdentityDiscountInfo)
	sib.hasDiscountSupport = hasDiscountSupport

	if sib.product == nil || sib.product.BaseReqData == nil || sib.product.BaseReqData.CommonBizInfo.IdentityPageInfo == nil {
		return
	}

	if sib.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo == nil ||
		len(sib.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo.SpecialSeatRules) == 0 {
		return
	}

	specialSeatRuleDate := sib.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo.SpecialSeatRules
	for _, rules := range specialSeatRuleDate {
		if rules == nil {
			continue
		}

		// 儿童票
		if rules.RuleName == seat_selection_consts.ChildTicket && rules.IsSupport == 1 {
			hasDiscountStatus := sib.getDiscountStatus(seat_selection_consts.Children.ToInt64(), rules.TypePercent)
			hasDiscountSupport[seat_selection_consts.Children.ToInt32()] = &model.IdentityDiscountInfo{
				HasDiscountSupport: hasDiscountStatus,
				DiscountRatio:      sib.getDiscountRatio(hasDiscountStatus, seat_selection_consts.Children.ToInt64(), rules.TypePercent),
			}
			continue
		}

		// 优待票逻辑
		if rules.RuleName == seat_selection_consts.CouponTicket && rules.IsSupport == 1 {
			hasDiscountStatus := sib.getDiscountStatus(seat_selection_consts.PreferentialPeople.ToInt64(), rules.TypePercent)
			hasDiscountSupport[seat_selection_consts.PreferentialPeople.ToInt32()] = &model.IdentityDiscountInfo{
				HasDiscountSupport: hasDiscountStatus,
				DiscountRatio:      sib.getDiscountRatio(hasDiscountStatus, seat_selection_consts.PreferentialPeople.ToInt64(), rules.TypePercent),
			}
		}
	}
	sib.hasDiscountSupport = hasDiscountSupport
}

func (sib *seatInfoBase) getDiscountRatio(hasDiscountSupport bool, identityType int64, typePercentList []*Dirpc_SDK_Brick.TypePercentList) float64 {
	if !hasDiscountSupport {
		return intercity_estimate_detail.ZeroRule
	}
	for _, percent := range typePercentList {
		if percent.Type == identityType {
			return cast.ToFloat64(percent.Percent) / intercity_estimate_detail.TenRatioPricing
		}
	}
	return intercity_estimate_detail.ZeroRule
}

func (sib *seatInfoBase) getDiscountStatus(identityType int64, typePercentList []*Dirpc_SDK_Brick.TypePercentList) bool {
	if len(typePercentList) == 0 {
		return false
	}

	for _, percent := range typePercentList {
		if percent == nil || percent.Type != identityType {
			continue
		}
		percentage := percent.Percent
		if percentage <= 0 || percentage == 100 {
			return false
		}
	}
	return true
}

// buildCarryChildrenLimit ...
func (sib *seatInfoBase) buildCarryChildrenLimit(ctx context.Context) *proto.RuleLimit {
	if _, support := sib.supportTicketTypes[seat_selection_consts.CarryChildren.ToInt32()]; !support {
		return nil
	}

	if sib.text == nil || sib.product == nil || sib.product.BaseReqData == nil {
		return nil
	}

	// 携童无上限或者小于0
	if sib.product.BaseReqData.CommonBizInfo.CarryChildrenMaxInventory == seat_selection_consts.NoLimit ||
		sib.product.BaseReqData.CommonBizInfo.CarryChildrenMaxInventory < 0 ||
		sib.product.BaseReqData.CommonBizInfo.CarryChildrenIsOccupySeat == 1 {
		return nil
	}

	toastTemplate := sib.text["carry_children_limit_toast"].String()
	if sib.product.BaseReqData.CommonBizInfo.CarryChildrenMaxInventory == 0 {
		toastTemplate = sib.text["carry_children_zero_limit_toast"].String()
	}

	return &proto.RuleLimit{
		MaxInventory: sib.product.BaseReqData.CommonBizInfo.CarryChildrenMaxInventory,
		Toast: util.ReplaceTag(ctx, toastTemplate, map[string]string{
			"num": util.Int32String(sib.product.BaseReqData.CommonBizInfo.CarryChildrenMaxInventory),
		}),
		TicketTypes: sib.buildCarryChildrenTicketTypes(ctx),
	}
}

// buildPassengerLimit ...
func (sib *seatInfoBase) buildPassengerLimit(ctx context.Context) *proto.RuleLimit {
	if sib.text == nil || sib.product == nil || sib.product.BaseReqData == nil {
		return nil
	}

	return &proto.RuleLimit{
		MaxInventory: sib.product.BaseReqData.CommonBizInfo.MaxInventory,
		Toast: util.ReplaceTag(ctx, sib.text["passenger_limit_toast"].String(), map[string]string{
			"num": util.Int32String(sib.product.BaseReqData.CommonBizInfo.MaxInventory),
		}),
		TicketTypes: sib.buildPassengerTicketTypes(ctx),
	}
}

// buildCarryChildrenTicketTypes ...
func (sib *seatInfoBase) buildCarryChildrenTicketTypes(ctx context.Context) []int32 {
	ticketTypes := make([]int32, 0)

	// 携童有上限
	ticketTypes = append(ticketTypes, seat_selection_consts.CarryChildren.ToInt32())

	return ticketTypes
}

// buildPassengerTicketTypes
func (sib *seatInfoBase) buildPassengerTicketTypes(ctx context.Context) []int32 {
	ticketTypes := make([]int32, 0)

	for ticketType, _ := range sib.supportTicketTypes {
		// 携童不占座情况，愚蠢的逻辑
		if ticketType == seat_selection_consts.CarryChildren.ToInt32() && sib.product.BaseReqData.CommonBizInfo.CarryChildrenIsOccupySeat != 1 {
			continue
		}

		ticketTypes = append(ticketTypes, ticketType)
	}

	return ticketTypes
}

// BuildChildOccupyLabel 构建携童是否占座标签
func BuildChildOccupyLabel(conf map[string]gjson.Result) *proto.NewFormFeeDesc {
	return &proto.NewFormFeeDesc{
		TextColor:      util.StringPtr(conf["text_color"].String()),
		BorderColor:    conf["border_color"].String(),
		Content:        conf["content"].String(),
		Icon:           conf["icon"].String(),
		BgColor:        util.StringPtr(conf["bg_color"].String()),
		HighlightColor: util.StringPtr(conf["highlight_color"].String()),
	}
}
