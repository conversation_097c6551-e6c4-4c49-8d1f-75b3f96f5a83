package station_pop_up

import (
	"context"
	"encoding/json"
	"strconv"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/tidwall/gjson"
)

const (
	StartStationType = "1"
	EndStationType   = "2"
)

type stationPopUp struct {
	product *biz_runtime.ProductInfoFull
	text    map[string]gjson.Result
}

// NewpriceCard ...
func NewstationPopUp(product *biz_runtime.ProductInfoFull, text map[string]gjson.Result) *stationPopUp {
	return &stationPopUp{
		product: product,
		text:    text,
	}
}

func (hc *stationPopUp) Render(ctx context.Context) *proto.StationPopUp {
	return hc.getStationPopup(ctx)
}

func (hc *stationPopUp) getStationPopup(ctx context.Context) *proto.StationPopUp {
	if hc.product == nil || hc.product.GetBizInfo() == nil || hc.product.BaseReqData == nil ||
		!hc.product.BaseReqData.CommonBizInfo.StationInfo.SupportCorrectStationId {
		return nil
	}

	res := &proto.StationPopUp{}

	routeDetail := hc.product.GetBizInfo().RouteDetailV2
	if routeDetail == nil || routeDetail.RouteBasicInfo == nil || len(routeDetail.RouteBasicInfo.StationList) <= 0 {
		return nil
	}

	stationList := routeDetail.RouteBasicInfo.StationList
	StartStationId := int32(hc.product.BaseReqData.CommonBizInfo.StartStationId)
	EndStationId := int32(hc.product.BaseReqData.CommonBizInfo.EndStationId)
	for _, tmp := range stationList {
		if tmp.StationType == StartStationType {
			res.StartStations = append(res.StartStations, buildStation(tmp, StartStationId))
		} else if tmp.StationType == EndStationType {
			res.EndStations = append(res.EndStations, buildStation(tmp, EndStationId))
		}
	}
	res.FeeParams = getFeeParams(hc.product)
	res.ButtonText = hc.text["botton_text"].String()
	res.StartTitle = hc.text["start_title"].String()
	res.EndTitle = hc.text["end_title"].String()
	return res
}

func getFeeParams(product *biz_runtime.ProductInfoFull) *proto.FeeParams {
	return &proto.FeeParams{
		District:          product.BaseReqData.AreaInfo.District,
		BusServiceShiftId: product.BaseReqData.CommonBizInfo.BusServiceShiftId,
		RouteId:           strconv.FormatInt(int64(product.BaseReqData.CommonBizInfo.RouteId), 10),
		NtupleInfo:        getNTuple(product),
		FromStationId:     int32(product.BaseReqData.CommonBizInfo.StartStationId),
		ToStationId:       int32(product.BaseReqData.CommonBizInfo.EndStationId),
	}
}

func getNTuple(product *biz_runtime.ProductInfoFull) string {
	tmp := &proto.NTuple{
		ProductId: product.Product.ProductID,
		ComboType: product.Product.ComboType,
		CarLevel:  product.Product.RequireLevel,
	}
	if res, err := json.Marshal(tmp); err == nil {
		return string(res)
	}
	return ""
}

// buildStation ...
func buildStation(data *Prfs.StationInfo, id int32) *proto.IntercityStationDetail {
	res := &proto.IntercityStationDetail{
		Name:        data.StationName,
		DisplayName: data.StationName,
		Id:          data.StationId,
	}
	if data.StationId == id {
		res.IsSelected = 1
	}
	return res
}
