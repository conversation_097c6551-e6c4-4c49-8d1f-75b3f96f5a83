package intercity_estimate_detail

import (
	"context"

	bizCommonConsts "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_estimate_detail/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_estimate_detail/bottom_card"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_estimate_detail/bus_shift_card"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_estimate_detail/head_card"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_estimate_detail/omage_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_estimate_detail/order_params"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_estimate_detail/price_card"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_estimate_detail/rule_card"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_estimate_detail/seat_info_card"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_estimate_detail/station_pop_up"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_estimate_detail/store_passenger_info_card"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_estimate_detail/travel_card_info"
	"git.xiaojukeji.com/gulfstream/passenger-common/util"
	trace "git.xiaojukeji.com/lego/context-go"
	"github.com/tidwall/gjson"
)

type render struct {
}

// NewRender ...
func NewRender() *render {
	return &render{}
}

// Do ...
func (r *render) Do(ctx context.Context, productsFull []*biz_runtime.ProductInfoFull, req *proto.IntercityEstimateDetailRequest, commonData *model.CommonData) (*proto.IntercityEstimateDetailData, error) {
	product := r.getTargetProduct(ctx, productsFull)
	text := r.getText(ctx)

	data := &proto.IntercityEstimateDetailData{
		EstimateTraceId: trace.GetTrace(ctx).GetTraceId(),
	}
	isNewVersionAnyCarFlow := IsNewVersionAnyCarFlow(req)

	data.Mode = r.buildMode(ctx, product)
	supportInfo := getSupportTicket(product)
	newPageSwitch := product.BaseReqData.CommonBizInfo.IsNewPageUpgrade
	data.HeadCard = head_card.NewHeadCard(product, gjson.Get(text, intercity_estimate_detail.HeadCard).Map(), isNewVersionAnyCarFlow, newPageSwitch).Render(ctx)
	data.RuleCard = rule_card.NewRuleCard(product, gjson.Get(text, intercity_estimate_detail.RuleCard).Map(), supportInfo, newPageSwitch).Render(ctx)
	if data.Mode == models.RealNameNoHistory.ToInt32() {
		data.StorePassengerInfoCard = store_passenger_info_card.NewStorePassengerInfoCard(product, gjson.Get(text, intercity_estimate_detail.StorePassengerInfoCard).Map(), supportInfo, newPageSwitch).Render(ctx)
	} else if data.Mode == models.RealName.ToInt32() {
		data.RealNameSeatInfoCard = seat_info_card.NewRealNameSeatInfoCard(product, gjson.Get(text, intercity_estimate_detail.RealNameSeatInfoCard).Map(), newPageSwitch, req).Render(ctx)
		data.BottomCard = bottom_card.NewBottomCard(product, gjson.Get(text, intercity_estimate_detail.BottomCard).Map(), data.Mode, isNewVersionAnyCarFlow).Render(ctx)
		data.OrderParams = order_params.NewOrderParams(product, nil, req, isNewVersionAnyCarFlow).Render(ctx)
	} else if data.Mode == models.NoRealName.ToInt32() {
		data.SeatInfoCard = seat_info_card.NewSeatInfoCard(product, gjson.Get(text, intercity_estimate_detail.SeatInfoCard).Map(), supportInfo, newPageSwitch).Render(ctx)
		data.BottomCard = bottom_card.NewBottomCard(product, gjson.Get(text, intercity_estimate_detail.BottomCard).Map(), data.Mode, isNewVersionAnyCarFlow).Render(ctx)
		data.OrderParams = order_params.NewOrderParams(product, nil, req, isNewVersionAnyCarFlow).Render(ctx)
	} else {
		log.Trace.Warnf(ctx, "render", "unknown mode")
	}

	data.BusShiftCard = bus_shift_card.NewBusShiftCard(product, commonData, isNewVersionAnyCarFlow).Render(ctx, req)
	data.IsBestShift = &product.BaseReqData.CommonBizInfo.IsBestShift
	data.TravelCardInfo = travel_card_info.NewTravelCardInfo(product, gjson.Get(text, intercity_estimate_detail.TravelCardInfo).Map()).Render(ctx)
	data.PriceCard = price_card.NewPriceCard(product, gjson.Get(text, intercity_estimate_detail.PriceCard).Map()).Render(ctx)
	data.StationPopup = station_pop_up.NewstationPopUp(product, gjson.Get(text, intercity_estimate_detail.StationPopUp).Map()).Render(ctx)
	data.OmegaInfo = omage_info.NewOmageInfo(product).Render(ctx)
	return data, nil
}

func InitJumpUrl(ctx context.Context, resp *proto.IntercityEstimateDetailResponse) {
	text := dcmp.GetDcmpContent(ctx, "intercity_estimate-confirm_order", nil)
	url := gjson.Get(text, intercity_estimate_detail.ExtraInfo).Map()["jump_url"].String()
	resp.Data = &proto.IntercityEstimateDetailData{
		ExtraInfo: &proto.ExtraInfo{
			JumpUrl: &url,
		},
	}
}

// getTargetProduct ...
func (r *render) getTargetProduct(ctx context.Context, productsFull []*biz_runtime.ProductInfoFull) *biz_runtime.ProductInfoFull {
	var targetProduct *biz_runtime.ProductInfoFull

	for _, product := range productsFull {
		if product == nil {
			continue
		}

		if carpool.IsIntercityStation(ctx, int(product.GetCarpoolType())) {
			targetProduct = product
			break
		}
	}

	return targetProduct
}

// buildMode 模式
func (r *render) buildMode(ctx context.Context, product *biz_runtime.ProductInfoFull) int32 {
	if product == nil || product.GetBizInfo() == nil || product.GetBizInfo().RouteDetailV2 == nil {
		return models.Unknown.ToInt32()
	}

	// 需要实名制
	if product.GetBizInfo().RouteDetailV2.IsNeedVerified() {
		if product.BaseReqData != nil &&
			product.BaseReqData.CommonBizInfo.IdentityPageInfo != nil &&
			len(product.BaseReqData.CommonBizInfo.IdentityPageInfo.IdentityHistoryInfo) > 0 {
			return models.RealName.ToInt32()
		} else {
			return models.RealNameNoHistory.ToInt32()
		}
	} else {
		return models.NoRealName.ToInt32()
	}
}

// getText 获取全局文案
func (r *render) getText(ctx context.Context) string {
	raw := dcmp.GetDcmpContent(ctx, "intercity_estimate-confirm_order", nil)

	return raw
}

// getSupportTicket ...
func getSupportTicket(product *biz_runtime.ProductInfoFull) *intercity_estimate_detail.SupportInfo {
	var supportItem = intercity_estimate_detail.SupportInfo{}

	if product == nil ||
		product.BaseReqData == nil ||
		product.BaseReqData.CommonBizInfo.IdentityPageInfo == nil ||
		product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo == nil {
		return &supportItem
	}

	routeDetail := product.GetBizInfo().RouteDetailV2

	for _, rule := range product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo.SpecialSeatRules {
		if rule.RuleName == seat_selection_consts.ChildTicket && rule.IsSupport == 1 {
			supportItem.SupportChildrenTicket = true
			// 兼容老携童票逻辑
			for _, childTicket := range rule.GetTypePercent() {
				if childTicket.GetType() == seat_selection_consts.CarryChildren.ToInt64() {
					supportItem.SupportCarryChildrenTicket = true
					break
				}
			}

			if supportItem.SupportChildrenTicket && routeDetail != nil {
				supportItem.CarryChildrenOccupySeat = routeDetail.IsChildOccupy()
			}
		}

		if rule.RuleName == seat_selection_consts.OwnerTicket && rule.IsSupport == 1 {
			supportItem.SupportHomeOwnerTicket = true
		}

		if rule.RuleName == seat_selection_consts.CouponTicket && rule.IsSupport == 1 {
			supportItem.SupportCouponTicket = true
		}

		// 新携童票逻辑
		if rule.RuleName == seat_selection_consts.CarryChildTicket && rule.IsSupport == 1 {
			supportItem.SupportCarryChildrenTicket = true
			if rule.OccupySeat == 1 {
				supportItem.CarryChildrenOccupySeat = true
			}
		}
	}

	return &supportItem
}

// IsNewVersionAnyCarFlow (端 >= 6.9.18; 小程序 >= 6.9.80)
func IsNewVersionAnyCarFlow(req *proto.IntercityEstimateDetailRequest) bool {
	if req.AccessKeyId == bizCommonConsts.AccessKeyIDDiDiIos || req.AccessKeyId == bizCommonConsts.AccessKeyIDDiDiAndroid {
		return util.VersionCompare(req.AppVersion, "6.9.18", ">=")
	} else if req.AccessKeyId == bizCommonConsts.AccessKeyIDDiDiWechatMini || req.AccessKeyId == bizCommonConsts.AccessKeyIDDiDiAlipayMini {
		return util.VersionCompare(req.AppVersion, "6.9.80", ">=")
	}
	return false
}
