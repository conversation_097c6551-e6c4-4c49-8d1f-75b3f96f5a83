package travel_card_info

import (
	"context"
	"encoding/json"
	Dirpc_SDK_Vcard "git.xiaojukeji.com/dirpc/dirpc-go-http-Vcard"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
	"sort"
)

const (
	BusCardBizId = "100008"

	BusEstimateDetailCardChannel = "39"
)

type TravelCardInfo struct {
	product *biz_runtime.ProductInfoFull
	text    map[string]gjson.Result
}

// NewTravelCardInfo ...
func NewTravelCardInfo(product *biz_runtime.ProductInfoFull, text map[string]gjson.Result) *TravelCardInfo {
	return &TravelCardInfo{
		product: product,
		text:    text,
	}
}

func (tc *TravelCardInfo) Render(ctx context.Context) *proto.TravelCardInfo {
	return tc.getCardInfo(ctx)
}
func (tc *TravelCardInfo) getCardInfo(ctx context.Context) *proto.TravelCardInfo {
	if tc.product == nil || tc.text == nil || tc.product.Product.BizInfo.BusCardListData == nil || len(tc.product.Product.BizInfo.BusCardListData.CardList) <= 0 {
		return nil
	}
	cardInfo := &proto.TravelCardInfo{
		BgImg:     tc.text["bg_img"].String(),
		TitleImg:  tc.text["title_img"].String(),
		TitleDesc: tc.BuildTitleDesc(ctx),
		SubTitle:  tc.BuildSubTitle(ctx),
		CardList:  tc.getCardList(ctx),
		CardTip:   tc.getCardTip(ctx),
		OmegaInfo: tc.getOmegaInfo(),
	}
	return cardInfo
}
func (tc *TravelCardInfo) BuildTitleDesc(ctx context.Context) string {
	res := ""
	if tc.getSavingFee() > 0 {
		res = util.ReplaceTag(ctx, tc.text["title_desc"].String(), map[string]string{
			"saving_fee": cast.ToString(tc.getSavingFee()),
		})
	}
	return res
}
func (tc *TravelCardInfo) BuildSubTitle(ctx context.Context) string {
	res := ""
	if tc.product.GetBizInfo().BusCardSelectedBatchId != 0 {
		if tc.product.GetBusCardCheaperFee() > 0 {
			res = util.ReplaceTag(ctx, tc.text["selected_sub_title"].String(), map[string]string{
				"saving_fee": cast.ToString(tc.product.GetBusCardCheaperFee()),
				"times":      cast.ToString(tc.product.GetBusCard().DiscountTimes),
			})
		}
	} else {
		res = util.ReplaceTag(ctx, tc.text["sub_title"].String(), BuildCardByPageParam(tc.product))
	}
	return res
}
func BuildTermCardByPageParam(product *biz_runtime.ProductInfoFull) map[string]string {
	param := map[string]string{
		"card_biz_id":  BusCardBizId,
		"card_channel": BusEstimateDetailCardChannel,
		"batch_id":     cast.ToString(product.GetBusCard().BatchId),
	}
	return param
}
func BuildCardByPageParam(product *biz_runtime.ProductInfoFull) map[string]string {
	param := map[string]string{
		"card_biz_id":  BusCardBizId,
		"card_channel": BusEstimateDetailCardChannel,
		"batch_id":     cast.ToString(getShowBatchId(product)),
	}
	return param
}

func getShowBatchId(product *biz_runtime.ProductInfoFull) int32 {
	for _, v := range product.Product.BizInfo.BusCardListData.CardList {
		if product.GetBizInfo().BusCardSelectedBatchId == v.BatchId {
			return v.BatchId
		}
	}
	cardList := product.Product.BizInfo.BusCardListData.CardList
	sortCardList(cardList)
	return cardList[0].BatchId
}
func (tc *TravelCardInfo) getOmegaInfo() *proto.BusDetailOmegaInfo {
	omegaInfo := new(proto.BusDetailOmegaInfo)
	omegaShow := new(proto.BusDetailOmegaShow)
	omegaParams := new(proto.BusDetailOmegaParams)
	itemList := make([]*map[string]interface{}, 0)
	cards := tc.product.GetBizInfo().BusCardListData.CardList
	sortCardList(cards)
	for _, v := range cards {
		item := &map[string]interface{}{
			"product_id": v.ProductId,
			"batch_id":   v.BatchId,
		}
		itemList = append(itemList, item)
	}
	itemListByte, err := json.Marshal(itemList)
	if err != nil {
		return nil
	}
	omegaParams.CardList = string(itemListByte)
	omegaParams.AccessKeyId = tc.product.GetAccessKeyId()
	omegaShow.Key = "wyc_kqstation_ticketpage_travelcard_sw"
	omegaShow.Params = omegaParams
	omegaInfo.Show = omegaShow
	return omegaInfo
}
func (tc *TravelCardInfo) getCardList(ctx context.Context) []*proto.BusCardItem {
	cardList := make([]*proto.BusCardItem, 0)
	cards := tc.product.GetBizInfo().BusCardListData.CardList
	//卡次数依次按照从少到多、卡批次id从小到大排序
	sortCardList(cards)

	cardSize := len(cards)
	for _, v := range cards {
		cardIterm := new(proto.BusCardItem)
		cardIterm = &proto.BusCardItem{
			TopLeftCornerTag: tc.getTopLeftCornerTag(ctx, v),
			CardDesc: util.ReplaceTag(ctx, tc.text["card_item"].Map()["card_desc"].String(), map[string]string{
				"unit_price": cast.ToString(float64(v.EachFee) / 100.0),
				"times":      cast.ToString(v.TotalTimes),
			}),
			PriceDesc: util.ReplaceTag(ctx, tc.text["card_item"].Map()["price_desc"].String(), map[string]string{
				"price": cast.ToString(float64(v.CardFee) / 100.0),
			}),
			IsSelect:  tc.getIsSelected(v),
			BatchId:   v.BatchId,
			ProductId: v.ProductId,
		}
		//单个卡片不同处理
		if cardSize == 1 {
			cardIterm.BgImg = tc.text["card_item"].Map()["single_bg_img"].String()
			cardIterm.CardDesc = util.ReplaceTag(ctx, tc.text["card_item"].Map()["single_card_desc"].String(), map[string]string{
				"unit_price": cast.ToString(float64(v.EachFee) / 100.0),
				"times":      cast.ToString(v.TotalTimes),
			})
			cardIterm.RuleDesc = util.ReplaceTag(ctx, tc.text["card_item"].Map()["single_rule_desc"].String(), map[string]string{
				"day":    cast.ToString(v.AvailableDay),
				"routes": cast.ToString(v.UsableRoutes),
			})
		} else {
			cardIterm.BgImg = tc.text["card_item"].Map()["multi_bg_img"].String()
			cardIterm.CardDesc = util.ReplaceTag(ctx, tc.text["card_item"].Map()["multi_card_desc"].String(), map[string]string{
				"unit_price": cast.ToString(float64(v.EachFee) / 100.0),
				"times":      cast.ToString(v.TotalTimes),
			})
			cardIterm.RuleDesc = util.ReplaceTag(ctx, tc.text["card_item"].Map()["multi_rule_desc"].String(), map[string]string{
				"day": cast.ToString(v.AvailableDay),
			})
		}
		cardList = append(cardList, cardIterm)
	}
	return cardList

}

func sortCardList(cards []*Dirpc_SDK_Vcard.BatchCardInfo) {
	sort.Slice(cards, func(i, j int) bool {
		if cards[i].TotalTimes == cards[j].TotalTimes {
			return cards[i].BatchId < cards[j].BatchId
		}
		return cards[i].TotalTimes < cards[j].TotalTimes
	})
}

func (tc *TravelCardInfo) getIsSelected(card *Dirpc_SDK_Vcard.BatchCardInfo) int32 {
	//用户选择的batchId需要和当前卡片、优惠保持一致。否则不设置为勾选状态
	if tc.product.GetBusCard() != nil && tc.product.GetBusCard().SelectedBatchId == card.BatchId && card.BatchId == tc.product.GetBizInfo().BusCardSelectedBatchId {
		return 1
	}
	return 0
}

func (tc *TravelCardInfo) getTopLeftCornerTag(ctx context.Context, card *Dirpc_SDK_Vcard.BatchCardInfo) *proto.TopLeftCornerTag {

	if tc.getIsSelected(card) == 0 {
		return nil
	}
	fee := tc.product.GetBusCardCheaperFee()
	if fee > 0 {
		tag := &proto.TopLeftCornerTag{
			Desc: util.ReplaceTag(ctx, tc.text["card_item"].Map()["tag_desc"].String(), map[string]string{
				"fee": cast.ToString(fee),
			}),
			TextColor: tc.text["card_item"].Map()["tag_text_color"].String(),
			BgColor:   tc.text["card_item"].Map()["tag_bg_color"].String(),
		}
		return tag
	} else {
		return nil
	}
}
func (tc *TravelCardInfo) getCardTip(ctx context.Context) *proto.CardTip {
	bgGradients := make([]string, 0)
	if val, ok := tc.text["card_tip"].Map()["bg_gradients"]; ok {
		err := json.Unmarshal([]byte(val.Raw), &bgGradients)
		if err != nil {
			log.Trace.Warnf(ctx, "card tip", "selected_bg_gradients unmarshal fail, err:%v", err)
		}
	}
	cardTip := &proto.CardTip{
		BgGradients: bgGradients,
		Title:       tc.text["card_tip"].Map()["title"].String(),
		RightImg:    tc.text["card_tip"].Map()["right_img"].String(),
	}
	if tc.getSavingFee() > 0 {
		cardTip.SubTitle = util.ReplaceTag(ctx, tc.text["card_tip"].Map()["sub_title"].String(), map[string]string{
			"saving_fee": cast.ToString(tc.getSavingFee()),
		})
	}
	return cardTip
}

func (tc *TravelCardInfo) getSavingFee() float64 {
	return float64(tc.product.Product.BizInfo.BusCardListData.SavingFee) / 100.0
}
