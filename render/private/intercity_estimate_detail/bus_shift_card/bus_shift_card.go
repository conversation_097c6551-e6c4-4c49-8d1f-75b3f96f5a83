package bus_shift_card

import (
	"context"
	"encoding/json"
	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	CarpoolOpenApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolOpenApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_estimate_detail/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_multi_station"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/distance_render"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/models/apolloconf/bus_shift"
	trace "git.xiaojukeji.com/lego/context-go"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
	"strings"
	"time"
)

const (
	dcmpBusShiftCardKey        = "intercity_station-bus_shift_card"
	SwitchNewMainCardApolloKey = "gs_switch_new_main_card"
	FenceStation               = 1
	NormalStation              = 0

	StationToStationRoute = 0
	StationToFenceRoute   = 1
)

type BusShiftCard struct {
	product                *biz_runtime.ProductInfoFull
	commonData             *model.CommonData
	isNewVersionAnyCarFlow bool
	startStation           *Prfs.StationInfo
	endStation             *Prfs.StationInfo
}

func NewBusShiftCard(product *biz_runtime.ProductInfoFull, commonData *model.CommonData, isNewVersionAnyCarFlow bool) *BusShiftCard {
	card := &BusShiftCard{
		product:                product,
		commonData:             commonData,
		isNewVersionAnyCarFlow: isNewVersionAnyCarFlow,
	}
	if product.GetCommonBizInfo().StartStation != nil {
		card.startStation = product.GetCommonBizInfo().StartStation
	} else {
		card.startStation = &Prfs.StationInfo{}
	}
	if product.GetCommonBizInfo().EndStation != nil {
		card.endStation = product.GetCommonBizInfo().EndStation
	} else {
		card.endStation = &Prfs.StationInfo{}
	}
	return card
}

// Render 渲染
func (b *BusShiftCard) Render(ctx context.Context, req *proto.IntercityEstimateDetailRequest) *proto.BusShiftCard {
	if !b.isNewVersionAnyCarFlow || req.IsBestShift == nil || *req.IsBestShift != 1 {
		return nil
	}
	config := dcmp.GetDcmpContent(ctx, "intercity_station-stationinfo", nil)
	busShiftCardConfig := dcmp.GetDcmpContent(ctx, dcmpBusShiftCardKey, nil)
	startStation, endStation := b.buildStation(req)
	res := &proto.BusShiftCard{
		TimeMsg: b.buildTimeMsg(busShiftCardConfig),
		StationInfo: &proto.BusShiftCardStationInfo{
			Start: startStation,
			End:   endStation,
			Detail: &proto.BusShiftCardDetail{
				Title: b.buildDetailTitle(ctx, config, b.startStation.StationId, b.endStation.StationId),
			},
		},
		SubTagList: b.buildSubTagList(ctx),
		JumpButton: b.buildJumpButton(ctx, busShiftCardConfig, req),
		OmegaInfo: &proto.BusShiftCardOmegaInfo{
			CommonParams: &proto.BusShiftCardOmegaParam{
				IsBestShift:  b.product.BaseReqData.CommonBizInfo.IsBestShift,
				GuideTraceId: b.product.BaseReqData.CommonInfo.GuideTraceId,
			},
		},
	}
	b.buildDistanceTag(ctx, startStation, endStation, config, req)
	return res
}

func (b *BusShiftCard) buildStation(req *proto.IntercityEstimateDetailRequest) (start *proto.BusShiftCardStation, end *proto.BusShiftCardStation) {
	if b.startStation.StationSceneType == FenceStation {
		start = &proto.BusShiftCardStation{
			Name: req.StartPoiName,
			Lat:  req.StartLat,
			Lng:  req.StartLng,
		}
	} else {
		start = &proto.BusShiftCardStation{
			Name:      b.startStation.StationName,
			StationId: b.startStation.StationId,
			Lat:       b.startStation.StationLat,
			Lng:       b.startStation.StationLng,
			Tag:       &proto.BusShiftCardTag{},
		}
	}

	if b.endStation.StationSceneType == FenceStation {
		end = &proto.BusShiftCardStation{
			Name: req.EndPoiName,
			Lat:  req.EndLat,
			Lng:  req.EndLng,
		}
	} else {
		end = &proto.BusShiftCardStation{
			Name:      b.endStation.StationName,
			StationId: b.endStation.StationId,
			Lat:       b.endStation.StationLat,
			Lng:       b.endStation.StationLng,
			Tag:       &proto.BusShiftCardTag{},
		}
	}
	return start, end
}

// buildTimeMsg 构造出发时间
func (b *BusShiftCard) buildTimeMsg(config string) string {
	departureTime := b.product.BaseReqData.CommonInfo.DepartureTime
	t := time.Unix(departureTime, 0)
	timeStr := t.Format("15:04")
	dateStr := ""
	if !util.IsToday(departureTime) {
		dateStr = t.Format("01月02日")
	}
	return strings.TrimSpace(dcmp.TranslateTemplate(gjson.Get(config, "time_msg").String(), map[string]string{"time": timeStr, "date": dateStr}))
}

// buildSubTagList 构建标签列表
func (b *BusShiftCard) buildSubTagList(ctx context.Context) []*proto.BusShiftCardSubMsg {
	res := make([]*proto.BusShiftCardSubMsg, 0)
	subMsgTemplate := dcmp.GetDcmpPlainContent(ctx, "intercity_station-suffix")
	if suffix := b.product.GetBizInfo().BusMode; suffix != "" {
		res = append(res, &proto.BusShiftCardSubMsg{
			Color:   gjson.Get(subMsgTemplate, "color").String(),
			Content: intercity_multi_station.GongJiaoModel,
		})
	}
	if b.product.GetCommonBizInfo().BusShiftInventoryRobinData == nil || b.commonData.Quotation == nil || b.commonData.Quotation.ShiftId == nil {
		return res
	}
	if shiftInfo, ok := b.product.GetCommonBizInfo().BusShiftInventoryRobinData[*b.commonData.Quotation.ShiftId]; ok {
		tagName := bus_shift.GetTagNameByTagId(ctx, shiftInfo.ShiftTagId)
		if len(tagName) == 0 {
			return res
		}
		// 通过shiftTagId转换为match的tagName
		subMsgTemplate = dcmp.GetDcmpPlainContent(ctx, "intercity_station-suffix")

		res = append(res, &proto.BusShiftCardSubMsg{
			Color:   gjson.Get(subMsgTemplate, "color").String(),
			Content: tagName,
		})
	}
	return res
}

// buildJumpButton 构建跳转按钮
func (b *BusShiftCard) buildJumpButton(ctx context.Context, config string, req *proto.IntercityEstimateDetailRequest) *proto.JumpButton {
	url := ""
	// 新老链接公参
	params := map[string]string{
		"client_type": cast.ToString(b.product.BaseReqData.CommonInfo.ClientType),
		"page_type":   cast.ToString(b.product.BaseReqData.CommonInfo.PageType),
		"channel":     cast.ToString(b.product.BaseReqData.CommonInfo.Channel),
		"day_time":    cast.ToString(util.GetDayTime(b.product.BaseReqData.CommonInfo.DepartureTime)),
	}
	if SwitchToNewMainCardEstimate(ctx, b.product.BaseReqData.PassengerInfo.Phone, cast.ToString(b.product.BaseReqData.PassengerInfo.PID),
		cast.ToString(b.product.BaseReqData.CommonInfo.AccessKeyID), b.product.BaseReqData.CommonInfo.AppVersion, cast.ToString(b.startStation.City)) {
		// 新版用poi作为参数
		url = gjson.Get(config, "jump_button").Map()["url_v2"].String()
		params["start_poi_id"] = req.StartPoiId
		params["start_poi_name"] = req.StartPoiName
		params["start_poi_city"] = cast.ToString(req.StartPoiCity)
		params["start_poi_city_name"] = req.StartPoiCityName
		params["start_lat"] = req.StartLat
		params["start_lng"] = req.StartLng
		params["end_poi_id"] = req.EndPoiId
		params["end_poi_name"] = req.EndPoiName
		params["end_poi_city"] = cast.ToString(req.EndPoiCity)
		params["end_poi_city_name"] = req.EndPoiCityName
		params["end_lat"] = req.EndLat
		params["end_lng"] = req.EndLng
		if b.product.GetBizInfo() != nil && b.product.GetBizInfo().RouteDetailV2 != nil && b.product.GetBizInfo().RouteDetailV2.RouteBasicInfo != nil &&
			b.product.GetBizInfo().RouteDetailV2.RouteBasicInfo.RouteSceneType == StationToFenceRoute {
			params["start_station_id"] = cast.ToString(b.product.GetCommonBizInfo().StartStationId)
			params["end_station_id"] = cast.ToString(b.product.GetCommonBizInfo().EndStationId)
		} else {
			// 给前端的参数留空
			params["start_station_id"] = ""
			params["end_station_id"] = ""
		}
	} else {
		// 旧版用起终站点作为参数
		url = gjson.Get(config, "jump_button").Map()["url"].String()
		params["start_station_id"] = cast.ToString(b.product.GetCommonBizInfo().StartStationId)
		params["end_station_id"] = cast.ToString(b.product.GetCommonBizInfo().EndStationId)
		params["start_county_id"] = cast.ToString(b.startStation.County)
		params["end_county_id"] = cast.ToString(b.endStation.County)
		params["start_city"] = cast.ToString(b.startStation.City)
		params["end_city"] = cast.ToString(b.endStation.City)
	}
	transData := make(map[string]string)
	transDataStr := ""
	transData["guide_trace_id"] = b.product.BaseReqData.CommonInfo.GuideTraceId
	transDataBytes, err := json.Marshal(transData)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "BusShiftCard buildJumpButton jsonMarshal error:%v", err)
	} else {
		transDataStr = string(transDataBytes)
	}
	omegaData := make(map[string]string)
	omegaDataStr := ""
	omegaData["guide_trace_id"] = b.product.BaseReqData.CommonInfo.GuideTraceId
	omegaData["is_best_shift"] = cast.ToString(b.product.BaseReqData.CommonBizInfo.IsBestShift)
	omegaDataBytes, err1 := json.Marshal(omegaData)
	if err1 != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "BusShiftCard buildJumpButton jsonMarshal error:%v", err1)
	} else {
		omegaDataStr = string(omegaDataBytes)
	}
	params["trans_data"] = transDataStr
	params["omg_data"] = omegaDataStr
	// 要区分新旧版链接，入参不一样，新版传poi，旧版传站点
	return &proto.JumpButton{
		Url:  util.String2PtrString(dcmp.TranslateTemplate(url, params)),
		Text: gjson.Get(config, "jump_button").Map()["text"].String(),
	}
}

func (b *BusShiftCard) buildDetailTitle(ctx context.Context, config string, startStationId int32, endStationId int32) string {
	startTime := int64(0)
	endTime := int64(0)
	minRes := ""
	stations := make([]*CarpoolOpenApi.BusStationDetail, 0)
	if b.product.GetCommonBizInfo().ShiftInfo != nil && b.product.GetCommonBizInfo().ShiftInfo.StationDetails != nil {
		for _, stationDetail := range b.product.GetCommonBizInfo().ShiftInfo.StationDetails {
			if stationDetail.StationID == cast.ToInt64(startStationId) && stationDetail.DepartureTime != nil {
				startTime = *stationDetail.DepartureTime
				stations = append(stations, stationDetail)
				continue
			}
			// type = 0 代表站点停售
			if stationDetail.Type != 0 && len(stations) > 0 {
				stations = append(stations, stationDetail)
			}
			if stationDetail.StationID == cast.ToInt64(endStationId) && stationDetail.DepartureTime != nil {
				endTime = *stationDetail.DepartureTime
			}
		}
	}
	totalTime := int64(0)
	if startTime > 0 && endTime > 0 {
		totalTime = (endTime - startTime) / 60
	}
	hour := totalTime / 60
	minute := totalTime % 60
	if hour != 0 {
		minRes = cast.ToString(hour) + gjson.Get(config, "hour").String()
	}
	if minute != 0 {
		minRes = minRes + cast.ToString(minute) + gjson.Get(config, "min").String()
	}
	n := 0
	if len(stations) > 2 {
		n = len(stations) - 2
	}
	return dcmp.TranslateTemplate(gjson.Get(config, "title").String(),
		map[string]string{"n": cast.ToString(n)}) +
		" " +
		intercity_multi_station.DetailTitle(ctx, minRes)
}

// buildDistanceTag 构造距离提示文案，用户戳点距离和推荐班次起终点距离
func (b *BusShiftCard) buildDistanceTag(ctx context.Context, startTag *proto.BusShiftCardStation, endTag *proto.BusShiftCardStation, config string, req *proto.IntercityEstimateDetailRequest) {

	startDistance := util.EarthDistance(cast.ToFloat64(req.StartLat), cast.ToFloat64(req.StartLng),
		cast.ToFloat64(b.startStation.StationLat), cast.ToFloat64(b.startStation.StationLng))
	if !util.LessOrEqualThanZero(startDistance) && startTag.Tag != nil {
		startStr, dcmpKey := distance_render.GetDistanceInfo(int64(startDistance))
		if dcmpKey == "distance" {
			startTag.Tag.Icon = gjson.Get(config, intercity_multi_station.START+"_"+intercity_multi_station.PORTRAITICON).String()
		}
		startTag.Tag.Content = dcmp.TranslateTemplate(gjson.Get(config, intercity_multi_station.START+"_"+dcmpKey).String(), map[string]string{
			"distance": startStr,
		})
	}
	endDistance := util.EarthDistance(cast.ToFloat64(req.EndLat), cast.ToFloat64(req.EndLng),
		cast.ToFloat64(b.endStation.StationLat), cast.ToFloat64(b.endStation.StationLng))
	if !util.LessOrEqualThanZero(endDistance) && endTag.Tag != nil {
		endStr, dcmpKey := distance_render.GetDistanceInfo(int64(endDistance))
		if dcmpKey == "distance" {
			endTag.Tag.Icon = gjson.Get(config, intercity_multi_station.END+"_"+intercity_multi_station.PORTRAITICON).String()
		}
		endTag.Tag.Content = dcmp.TranslateTemplate(gjson.Get(config, intercity_multi_station.END+"_"+dcmpKey).String(), map[string]string{
			"distance": endStr,
		})
	}
}

// SwitchToNewMainCardEstimate 切新预估开关
func SwitchToNewMainCardEstimate(ctx context.Context, phone string, pid string, accessKeyId string, appVersion string, cityId string) bool {
	params := map[string]string{
		"phone":         phone,
		"pid":           pid,
		"access_key_id": accessKeyId,
		"app_version":   appVersion,
		"city":          cityId,
	}
	return apollo.FeatureToggle(ctx, SwitchNewMainCardApolloKey, pid, params)
}
