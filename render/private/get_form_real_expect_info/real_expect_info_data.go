package get_form_real_expect_info

import (
	"context"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_business_form_real_data/model"
)

func BuildExpectInfo(ctx context.Context, info *model.BaseReq, totalExpectInfo *AthenaApiv3.TotalExpectInfo) *proto.TotalExpectInfo {
	if info == nil || totalExpectInfo == nil {
		return nil
	}

	resTotalExpectInfo := &proto.TotalExpectInfo{}
	resTotalExpectInfo.GlobalExpectInfo = formatGloatExpectInfo(totalExpectInfo.GetGlobalExpectInfo())
	resTotalExpectInfo.ProductExpectInfo = formatProductExpectInfo(totalExpectInfo.GetProductExpectInfo())

	return resTotalExpectInfo
}

func formatProductExpectInfo(info *AthenaApiv3.ProductExpectInfo) *proto.ProductExpectInfo {
	if info == nil {
		return nil
	}

	return &proto.ProductExpectInfo{
		ProductInfos: formatProductInfos(info.GetProductInfos()),
		ExtraInfo:    info.GetExtraInfo(),
	}
}

func formatProductInfos(productExpectInfoList []*AthenaApiv3.ProductExpectInfoItem) []*proto.ProductExpectInfoItem {
	if len(productExpectInfoList) <= 0 {
		return nil
	}

	productInfoList := make([]*proto.ProductExpectInfoItem, 0)

	for _, productExpect := range productExpectInfoList {
		if productExpect == nil {
			continue
		}

		productInfoList = append(productInfoList, &proto.ProductExpectInfoItem{
			ProductCategory: productExpect.ProductCategory,
			ShowType:        productExpect.ShowType,
			ExpectInfo:      formatExpectInfo(productExpect.ExpectInfo),
			SceneFlag:       productExpect.SceneFlag,
			QueueInfo:       formatQueueInfo(productExpect.QueueInfo),
			ExtraInfo:       productExpect.ExtraInfo,
		})
	}

	return productInfoList
}

func formatGloatExpectInfo(info *AthenaApiv3.GlobalExpectInfo) *proto.GlobalExpectInfo {
	if info == nil {
		return nil
	}

	return &proto.GlobalExpectInfo{
		ShowType:   info.GetShowType(),
		ExpectInfo: formatExpectInfo(info.GetExpectInfo()),
		ExtraInfo:  info.GetExtraInfo(),
	}
}

func formatExpectInfo(info *AthenaApiv3.ExpectInfo) *proto.ExpectInfo {
	if info == nil {
		return nil
	}

	return &proto.ExpectInfo{
		QueueInfo: formatQueueInfo(info.GetQueueInfo()),
		Etp:       info.Etp,
		Ets:       info.Ets,
		RespRate:  info.AnswerRate,
		ExtraInfo: info.ExtraInfo,
	}
}

func formatQueueInfo(info *AthenaApiv3.ExpectQueueInfo) *proto.ExpectQueueInfo {
	if info == nil {
		return nil
	}

	return &proto.ExpectQueueInfo{
		QueueId:       info.QueueID,
		QueueType:     info.QueueType,
		QueueOpenFlag: info.QueueOpenFlag,
		QueueLen:      info.QueueLen,
		Etq:           info.Etq,
		Rank:          info.Rank,
		QueueStatus:   info.QueueStatus,
		GroupKey:      info.GroupKey,
		ExtraInfo:     info.ExtraInfo,
	}
}
