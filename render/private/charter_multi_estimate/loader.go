package charter_multi_estimate

import (
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/s3e/x-engine/condition"
)

const (
	CharterEstimatePcIdSortConfNS   = "charter_render_matarial"
	CharterEstimatePcIdSortConfName = "charter_pc_sort"

	CharterEstimatePcMaterialConfNS   = "charter_render_matarial"
	CharterEstimatePcMaterialConfName = "luxury_car_material"
)

type pcIdSortParam struct {
	City        int `json:"city"`
	ProductId   int `json:"product_id"`
	SourceId    int `json:"source_id"`
	PageType    int `json:"page_type"`
	AccessKeyId int `json:"access_key_id"`
}

type pcIdSortMaterial struct {
	EstimatePcId int `json:"estimate_pc_id"`
}

type pcMaterialParam struct {
	pcIdSortParam // 复用一下

	EstimatePcId int `json:"estimate_pc_id"`
}

type pcMaterial struct {
	CarName         string `json:"car_name"`
	CarIcon         string `json:"car_icon"`
	SubTitleDescStr string `json:"sub_title_desc"`
	SubTitleTagStr  string `json:"sub_title_tag"`
}

func (r *Render) loadPcIdSort() ([]int, error) {
	param := &pcIdSortParam{
		City:        int(r.pg.BaseReqData.AreaInfo.Area),
		ProductId:   r.curProductId,
		SourceId:    util.ToInt(r.req.SourceId),
		PageType:    util.ToInt(r.req.PageType),
		AccessKeyId: util.ToInt(r.req.AccessKeyId),
	}

	res, err := condition.Check(r.ctx, CharterEstimatePcIdSortConfNS, param, condition.WithConfName(CharterEstimatePcIdSortConfName))
	if res == nil || err != nil {
		log.Trace.Warnf(r.ctx, LogTag, "pcIdSort x-engine check failed, params=%+v, res=%+v, error=%s", param, res, err.Error())
		return nil, err
	}

	if !res.IsAllow {
		log.Trace.Warnf(r.ctx, LogTag, "pcIdSort x-engine check not allowed, params=%+v， reason=%s", param, res.Reason)
		return nil, fmt.Errorf("not allowed")
	}

	var material []*pcIdSortMaterial
	err = res.GetMaterial(&material)
	if err != nil {
		log.Trace.Warnf(r.ctx, LogTag, "pcIdSort x-engine get material failed, material=%+v, err=%s, res=%+v", res.Material, err.Error(), res)
		return nil, err
	}

	ret := make([]int, 0, len(material))
	for _, pc := range material {
		if pc == nil {
			continue
		}

		ret = append(ret, pc.EstimatePcId)
	}

	return ret, nil
}

func (r *Render) loadPcMaterial() (map[int]*pcMaterial, error) {
	ret := make(map[int]*pcMaterial, len(r.products))

	for _, product := range r.products {
		param := &pcMaterialParam{
			pcIdSortParam: pcIdSortParam{
				City:        int(r.pg.BaseReqData.AreaInfo.Area),
				ProductId:   r.curProductId,
				SourceId:    util.ToInt(r.req.SourceId),
				PageType:    util.ToInt(r.req.PageType),
				AccessKeyId: util.ToInt(r.req.AccessKeyId),
			},
			EstimatePcId: int(product.GetProductCategory()),
		}

		res, err := condition.Check(r.ctx, CharterEstimatePcMaterialConfNS, param, condition.WithConfName(CharterEstimatePcMaterialConfName))
		if res == nil || err != nil {
			log.Trace.Warnf(r.ctx, LogTag, "pcMaterial x-engine check failed, params=%+v, res=%+v, error=%s", param, res, err.Error())
			return nil, err
		}

		if !res.IsAllow {
			log.Trace.Warnf(r.ctx, LogTag, "pcMaterial x-engine check not allowed, params=%+v， reason=%s", param, res.Reason)
			return nil, fmt.Errorf("not allowed")
		}

		material := &pcMaterial{}
		err = res.GetMaterial(material)
		if err != nil {
			log.Trace.Warnf(r.ctx, LogTag, "pcMaterial x-engine get material failed, material=%+v, err=%s, res=%+v", res.Material, err.Error(), res)
			return nil, err
		}

		ret[int(product.GetProductCategory())] = material
	}

	return ret, nil
}
