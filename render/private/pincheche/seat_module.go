package pincheche

import (
	"context"
	"strconv"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

type CarpoolSeatModuleProvider interface {
	GetCarpoolSeatMaxNum() int
	GetCarpoolSeatCurrentNum() int
	GetDepartureTime() time.Time
	ApolloParamProvider
}

func CarpoolSeatModule(ctx context.Context, prov CarpoolSeatModuleProvider) *proto.CarpoolSeatOptionModule {
	opts := make([]*proto.CarpoolSeatOption, 0, prov.GetCarpoolSeatMaxNum())
	labelTemplate := dcmp.GetJSONContentWithPath(ctx, "carpool_tab_pincheche-carpool_seat_module", nil, "label_template")
	for i := 1; i <= prov.GetCarpoolSeatMaxNum(); i++ {
		tmp := &proto.CarpoolSeatOption{
			Label:    dcmp.TranslateTemplate(labelTemplate, map[string]string{"num": strconv.Itoa(i)}),
			Value:    int32(i),
			Selected: false,
			Disable:  false,
		}

		if i == prov.GetCarpoolSeatCurrentNum() {
			tmp.Selected = true
		}

		if GetNightCarpoolStateApollo(prov.ApolloParam()) == "1" && IsNight(prov.GetDepartureTime()) {
			if i == 1 {
				tmp.Selected = true
			}
			if i == 2 {
				tmp.Selected = false
				tmp.Disable = true
			}
		}

		opts = append(opts, tmp)
	}
	return &proto.CarpoolSeatOptionModule{
		Title:      dcmp.GetJSONContentWithPath(ctx, "carpool_tab_pincheche-carpool_seat_module", nil, "title"),
		OptionList: opts,
	}
}
