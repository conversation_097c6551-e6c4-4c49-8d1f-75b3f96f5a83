package pincheche

import (
	StdURL "net/url"
	"strconv"
	"time"

	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

// Deprecated: 不带精度的小数格式化, 不是好格式化
func formatPrice(price float64) string {
	return strconv.FormatFloat(price, 'f', 1, 64)
}

func urlJoinParam(url string, param map[string]string) string {
	uri, err := StdURL.Parse(url)
	if err != nil {
		return ""
	}
	if uri.Scheme == "" || uri.Host == "" {
		return ""
	}
	query := StdURL.Values{}
	for k, v := range param {
		query.Add(k, v)
	}
	uri.RawQuery = query.Encode()
	return uri.String()
}

func IsNight(depTime time.Time) bool {
	year, month, date := depTime.Date()
	begin := time.Date(year, month, date, 23, 0, 0, 0, time.Now().Location())
	end := time.Date(year, month, date, 6, 0, 0, 0, time.Now().Location())
	if (depTime.After(begin) || depTime.Equal(begin)) || depTime.Before(end) {
		return true
	}

	return false
}

func GetNightCarpoolStateApollo(user *ApolloModel.User) string {
	user.With("source", "mamba")
	toggle, err := ApolloSDK.FeatureToggle("pincheche_open_state_toggle", user)
	if err == nil && toggle.IsAllow() {
		if ass := toggle.GetAssignment(); ass != nil {
			return ass.GetParameter("state", "")
		}
	}
	return ""
}
