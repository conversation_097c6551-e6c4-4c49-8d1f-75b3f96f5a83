package pincheche

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

func BuildCarpoolSceneInfo(ctx context.Context, prod render.BaseProvider, multiPriceProv []PriceDescProvider, introTagProv ExtraIntroTagProvider, priceTagProv []ExtraPriceTagProvider, isBooking bool) *proto.CarpoolLowPriceSceneInfo {
	res := &proto.CarpoolLowPriceSceneInfo{}
	res.SceneInfoSelected = &proto.CarpoolLowPriceSceneItem{
		MultiPriceDesc: MultiPriceDescV2(ctx, prod, isBooking, false, multiPriceProv...),
		ExtraIntroTag:  ExtraIntroTag(ctx, introTagProv, false),
	}

	res.SceneInfoUnselected = &proto.CarpoolLowPriceSceneItem{
		MultiPriceDesc: MultiPriceDescV2(ctx, prod, isBooking, true, multiPriceProv...),
		ExtraIntroTag:  ExtraIntroTag(ctx, introTagProv, true),
	}

	if !isBooking {
		res.SceneInfoSelected.ExtraPriceTag = ExtraPriceTag(ctx, priceTagProv...)
	}

	return res
}
