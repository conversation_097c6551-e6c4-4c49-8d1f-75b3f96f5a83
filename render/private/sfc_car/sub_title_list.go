// Package sfc_car
package sfc_car

import (
	"context"

	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

// SubTitleListProvider 副标题
type SubTitleListProvider interface {
	GetProductCategory() int64
}

// GetSubTitle 获取副标题
func GetSubTitle(ctx context.Context, prov SubTitleListProvider, dcmpData proto.SFCEstimateDcmp) string {
	var (
		subTitle string
	)

	productID := strconv.FormatInt(prov.GetProductCategory(), 10)
	subTitle = dcmpData.PriceInfo[productID].SubTitle

	return subTitle
}
