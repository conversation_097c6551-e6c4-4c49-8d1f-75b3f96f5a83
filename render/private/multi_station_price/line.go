package multi_station_price

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/spf13/cast"
	"sort"
)

const (
	FromTimeTypeRegular     = 1     //
	CheckTicketMethodQRCode = "二维码" //携程提供的枚举
)

func BuildLine(ctx context.Context, products []*biz_runtime.ProductInfoFull) (line *proto.LineBasic, firstStationSumMinutes int32) {
	//构建路线基本信息
	lineBasic := new(proto.LineBasic)
	routeDetail := products[0].GetBizInfo().RouteDetailV2
	lineBasic.Id = cast.ToString(routeDetail.RouteId)
	lineBasic.Name = routeDetail.RouteName
	lineBasic.FromTimeType = FromTimeTypeRegular //固定时间发车
	checkTicketMethods := make([]string, 0)
	checkTicketMethods = append(checkTicketMethods, CheckTicketMethodQRCode)
	lineBasic.CheckTicketMethods = checkTicketMethods
	if routeDetail != nil && routeDetail.RouteExtendInfo != nil && routeDetail.RouteExtendInfo.SeatLimit != nil {
		lineBasic.LimitTicketCntMax = routeDetail.RouteExtendInfo.SeatLimit.MaxPassengerCount
	}
	timeExpendMinutes := int32(0)
	stationBasicList := make([]*proto.StationBasic, 0)
	isContainUpPoint := false
	isContainDownPoint := false
	stationIndex := 0
	firstStationSumMinutes = 0
	for index, station := range routeDetail.RouteBasicInfo.StationList {
		stationBasic := new(proto.StationBasic)
		stationBasic.Sequence = int32(index + 1)
		stationBasic.Code = cast.ToString(station.StationId)
		stationBasic.ShuttleService = false
		if cityInfo, err := util.GetCityInfo(ctx, station.City); err == nil {
			stationBasic.CityName = cityInfo.CityDesc
		}
		if len(station.DisplayName) > 0 {
			stationBasic.Name = station.DisplayName
			stationBasic.Address = &station.DisplayName
		} else {
			stationBasic.Name = station.StationName
			stationBasic.Address = &station.StationName
		}
		lng := cast.ToString(station.StationLng)
		stationBasic.Longitude = &lng
		lat := cast.ToString(station.StationLat)
		stationBasic.Latitude = &lat
		stationBasic.BoardingType = cast.ToInt32(station.StationType)
		timeExpendMinutes += station.Eta
		stationBasic.OffsetMinutes = timeExpendMinutes
		//返回的路线，判断上车点是否匹配城市、区县
		if station.StationType == "1" || station.StationType == "3" {
			if products[0].GetCommonBizInfo().StationInfo.StartCity != station.City {
				continue
			}
			if products[0].GetCommonBizInfo().StationInfo.StartCountyId != 0 {
				if products[0].GetCommonBizInfo().StationInfo.StartCountyId != station.County {
					continue
				}
			}
			isContainUpPoint = true
		}
		//判断下车点是否匹配城市、区县
		if station.StationType == "2" || station.StationType == "3" {
			if products[0].GetCommonBizInfo().StationInfo.EndCity != station.City {
				continue
			}
			if products[0].GetCommonBizInfo().StationInfo.EndCountyId != 0 {
				if products[0].GetCommonBizInfo().StationInfo.EndCountyId != station.County {
					continue
				}
			}
			isContainDownPoint = true
		}
		stationBasicList = append(stationBasicList, stationBasic)
		stationIndex = stationIndex + 1
		if stationIndex == 1 { //第一个可用的站点，记录路线首发站到该站点的时间&清空累计的时间
			firstStationSumMinutes = timeExpendMinutes
			timeExpendMinutes = 0
		}
	}
	sort.Slice(stationBasicList, func(i, j int) bool {
		return stationBasicList[i].Sequence < stationBasicList[j].Sequence
	})
	if !isContainUpPoint || !isContainDownPoint {
		return nil, 0
	}
	//路程耗时使用终点站的距离始发点耗时
	lineBasic.TimeExpendMinutes = stationBasicList[len(stationBasicList)-1].OffsetMinutes
	lineBasic.Stations = stationBasicList
	return lineBasic, firstStationSumMinutes
}
