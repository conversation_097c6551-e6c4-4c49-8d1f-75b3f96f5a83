package consts

const (
	CommonPriceExtraInfoHK      = "common-price_extra_info_hk"
	FeeDetailDesc               = "simple_estimate-fee_detail_desc"
	FeeDescMemberCoupon         = "simple_estimate-fee_desc_member_coupon"
	CarpoolPriceInfo            = "simple_estimate-carpool_price_info"
	CarpoolFeeDesc              = "simple_estimate-carpool_fee_desc"
	GsCarpoolCommuteCardOpen    = "gs_carpool_commute_card_open"
	ConfigProductMap            = "config_product_map"
	EstimatePriceMsg            = "simple_estimate-estimate_price_msg"
	EstimateFeeInfo             = "simple_estimate-estimate_fee_info"
	TaxiSpecialNotShowPriceText = "taxi_special_not_show_price_text"
	VipDiscountStyleJisuCarpool = "vip_discount_style_jisucarpool"
	TaxiPeekFee                 = "taxi_peak_fee"
	ConfigCurrency              = "simple_estimate-config_currency"

	FeeNoInteractive          = "no_interactive"
	FeeMsgMixPersonal         = "mix_personal"
	FeeMsgUpFrontFare         = "upfront_fare"
	FeeMsgFail                = "fail"
	FeeMsgNormalWithPrefix    = "normal_with_prefix"
	FeeFlatRate               = "flat_rate"
	FeeMsgCarpoolTwoPrice     = "carpool_two_price"
	FeeMsgSubConfirmSucc      = "confirm_succ"
	FeeMsgSubSucc             = "succ"
	FeeMsgSubFail             = "fail"
	FeeMsgSubSucc2            = "succ_v2"
	FeeMsgSubFail2            = "fail_v2"
	FeeMsgFlatRate            = "flat_rate"
	FeeMsgFlatRateLeast       = "flat_rate_least"
	FeeMsgCarpoolSuccessPrice = "carpool_success_price"
	FeeMsgCarpoolFailPrice    = "carpool_fail_price"

	FeeMsgTaxiNonCarpoolEstimate      = "taxi_noncarpool_estimate"
	FeeMsgHKCapPrice                  = "hk_cap_taxi"
	FeeMsgTaxiSpecialNotShowPriceText = "taxi_special_not_show_price_text"
	FeeMsgTaxiChaozhiFeeEstimate      = "taxi_chaozhi_fee_estimate"
	FeeMsgTaxiOnlineFeeMsgV3          = "taxi_online_fee_msg_v3"
	FeeMsgTaxiMeter                   = "taxi_meter_fee_msg_v3"

	SpecPriceDesc = "price_desc" // 自由宝价格描述

	MaxFeeNum       = -1 // 不限制费用项数量
	DecrementMaxNum = 2  // 减价项最大数量

	VCardSourceSend     = "send"
	VCardSourceSendLoss = "send_loss"

	SendFirst   = "commute_card_send_first"  // 首次赠卡
	SendLoss    = "commute_card_send_loss"   // 赠卡流失
	SendNoFirst = "commute_card_send"        // 非首次赠卡
	UsableGive  = "commute_card_usable_give" // 买赠卡
	CardNew     = "commute_card_new"         // 付费卡

	PremiumPaidMember = "premium_paid_member" // 专车付费会员

	ControlGroup   = "control_group"   // 默认组
	TreatmentGroup = "treatment_group" // 对照组

	CustomTagMemberV3 = "member_v3"
	ProductMap        = "product_map"

	ShowMetredFareFiled      = "show_metered_fare"
	BelongsProductGroupFiled = "belongs_product_group"

	Currency = "元"

	Unione = 5

	TypeOrderNow     = 0 //实时单
	TypeOrderBooking = 1 //预约单

	FormNoInterActive = 0 // 无出口
	FormInterActive   = 1 // 无出口
)

type (
	// CustomTagMap CustomTagMap
	CustomTagMap struct {
		Content         string `json:"content"`
		Icon            string `json:"icon"`
		ProductCategory []int  `json:"product_category"`
		MaxDeduction    int    `json:"max_deduction"`
		BorderColor     string `json:"border_color"` //边框RGB色值, 如#000000, 空串标识无边框
	}

	// ConfigProduct config_product结构体映射
	ConfigProduct struct {
		ShowMeteredFare int   `json:"show_metered_fare"`
		BelongsCarGroup []int `json:"belongs_car_group"`
	}

	ConfigTaxiPeek struct {
		OpenStatus string `json:"open_status"`
	}
)
