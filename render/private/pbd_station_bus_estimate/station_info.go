package pbd_station_bus_estimate_render

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pbd_station_bus_estimate"
)

const PbdMultiType = "multi"
const PbdDetailType = "detail"

func StationInfo(ctx context.Context, prov *pbd_station_bus_estimate.AdapterPbdStationBus, estimateType string) *proto.PbdStationBusStationInfo {
	res := proto.PbdStationBusStationInfo{
		Start: &proto.PbdStationBusStationItem{
			Name:        prov.GetSelectInfo().FromStationInfo.StationName,
			DisplayName: prov.GetSelectInfo().FromStationInfo.DisplayName,
			Id:          int64(prov.GetSelectInfo().FromStationInfo.StationId),
			Lat:         prov.GetSelectInfo().FromStationInfo.StationLat,
			Lng:         prov.GetSelectInfo().FromStationInfo.StationLng,
		},
		End: &proto.PbdStationBusStationItem{
			Name:        prov.GetSelectInfo().DestStationInfo.StationName,
			DisplayName: prov.GetSelectInfo().DestStationInfo.DisplayName,
			Id:          int64(prov.GetSelectInfo().DestStationInfo.StationId),
			Lat:         prov.GetSelectInfo().DestStationInfo.StationLat,
			Lng:         prov.GetSelectInfo().DestStationInfo.StationLng,
		},
		Detail: &proto.PbdStationBusStationDetail{
			StationList: []*proto.PbdStationBusStationDetailItem{},
			RouteId:     prov.GetSelectInfo().RouteId,
		},
	}
	//批量预估需要库存信息
	if estimateType == PbdMultiType {
		inventory := prov.GetMaxInventory()
		res.Detail.Inventory = &inventory
	}
	dt := prov.GetDepartureTime()
	if len(prov.GetAllStationInfo()) != 0 {
		totalMin := 0
		for k, v := range prov.GetAllStationInfo() {
			stationType := int32(0)
			if v.StationType == "1" {
				stationType = 1
			} else if v.StationType == "2" {
				stationType = 2
			} else if v.StationType == "3" {
				// 类型3的站点先不返回
				continue
			}
			if k != 0 {
				totalMin = totalMin + int(v.Eta)
			}
			detail := proto.PbdStationBusStationDetailItem{
				Name:        v.StationName,
				Lat:         v.StationLat,
				Lng:         v.StationLng,
				Id:          int64(v.StationId),
				Type:        int64(stationType),
				DisplayName: v.DisplayName,
			}
			if stationType == 1 {
				if k != 0 {
					dt = dt + int64(v.Eta)*60
				}
				detail.DepartureTime = dt
			}
			res.Detail.StationList = append(res.Detail.StationList, &detail)

		}
	}
	return &res
}
