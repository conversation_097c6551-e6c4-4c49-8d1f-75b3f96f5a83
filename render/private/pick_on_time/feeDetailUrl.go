package pick_on_time

import (
	"context"
	"strings"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type FeeDetailUrProvider interface {
}

func GetFeeDetailUrl(ctx context.Context, products []*biz_runtime.ProductInfoFull) string {
	feeDetailUrl := dcmp.GetDcmpPlainContent(ctx, "common-fee_detail_url_v4") + "?estimate_ids="
	arr := make([]string, 0, 0)
	for _, product := range products {
		arr = append(arr, product.GetEstimateID())
	}
	return feeDetailUrl + strings.Join(arr, ",")
}
