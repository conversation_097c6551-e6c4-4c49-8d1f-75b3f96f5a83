package pick_on_time

import (
	"context"
	"encoding/json"
	"strconv"

	"git.xiaojukeji.com/gobiz/metrics"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	legoTrace "git.xiaojukeji.com/lego/context-go"
)

type EstimateFeeProvider interface {
}

func EstimateFee(ctx context.Context, products []*biz_runtime.ProductInfoFull) (float64, int) {

	hitTaxiProductCategoryList := getHitTaxiProduct(ctx, products)
	noTaxiProducts := make([]*biz_runtime.ProductInfoFull, 0)
	//1、获取非出租车的品类
	for _, product := range products {
		if isHitTaxiProduct(hitTaxiProductCategoryList, product) {
			continue
		}
		noTaxiProducts = append(noTaxiProducts, product)
	}
	//2、拦截必有车只有出租车的情况
	if len(noTaxiProducts) == 0 {
		return 0, consts.ErrnoGetPriceEstimateRsp
	}
	//3、校验非出租车品类价格是否一致
	estimateFee := noTaxiProducts[0].GetEstimateFee()
	for _, product := range noTaxiProducts {
		if product.GetEstimateFee() != estimateFee {
			log.Trace.Errorf(ctx, legoTrace.DLTagUndefined, "estimte fee is not equal: %v")
			metrics.Add("pick_on_time_estimate_fee_error ", 1)
			return 0, consts.ErrnoGetPriceEstimateRsp
		}
	}
	return estimateFee, consts.NoErr
}

/*
*
获取配置中命中的出租车品类
*/
func getHitTaxiProduct(ctx context.Context, products []*biz_runtime.ProductInfoFull) []int64 {
	hitTaxiProductCategoryList := make([]int64, 0)
	apolloParams := map[string]string{}
	hitTaxiProductCategoryListStr := ""
	if ok, assign := apollo.FeatureExp(ctx, "pick_on_time_taxi_list", strconv.FormatInt(products[0].BaseReqData.PassengerInfo.PID, 10), apolloParams); ok {
		hitTaxiProductCategoryListStr = assign.GetParameter("hit_taxi_product_category_list", "")
	}
	if len(hitTaxiProductCategoryListStr) == 0 {
		return hitTaxiProductCategoryList
	}
	err := json.Unmarshal([]byte(hitTaxiProductCategoryListStr), &hitTaxiProductCategoryList)
	if err != nil {
		log.Trace.Errorf(ctx, legoTrace.DLTagUndefined, "hitTaxiProductCategoryList unmarshal is error: %v")
	}
	return hitTaxiProductCategoryList
}

/*
*
是否命中了出租车的品类
*/
func isHitTaxiProduct(hitTaxiProductCategoryList []int64, product *biz_runtime.ProductInfoFull) bool {
	if hitTaxiProductCategoryList == nil || len(hitTaxiProductCategoryList) == 0 {
		return false
	}
	for _, hitTaxiProductCategory := range hitTaxiProductCategoryList {
		if product.GetProductCategory() == hitTaxiProductCategory {
			return true
		}
	}
	return false
}
