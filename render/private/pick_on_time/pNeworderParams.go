package pick_on_time

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	context2 "git.xiaojukeji.com/lego/context-go"
)

const (
	PickOnTime = "pick_on_time_exit"
)

type NewOrderParamsProvider interface {
}

func GetNewOrderParams(ctx context.Context, gen *biz_runtime.ProductsGenerator, products []*biz_runtime.ProductInfoFull) *proto.PNewOrderParams {
	nilStringToEmptyString := func(s *string) string {
		if s == nil {
			return ""
		}
		return *s
	}
	param := new(proto.PNewOrderParams)
	param.AgentType = PickOnTime
	param.Type = gen.BaseReqData.CommonInfo.OrderType //订单类型，0实时
	param.PageType = gen.BaseReqData.CommonInfo.PageType
	param.IsSupportMultiSelection = 1 //支持多勾
	param.StopoverPoints = nilStringToEmptyString(gen.BaseReqData.CommonInfo.StopoverPoints)
	multiRequireProduct := make([]*proto.RequireProduct, 0)
	for _, product := range products {
		prov := &PickOnTimeAdapter{ProductInfoFull: product}
		p := new(proto.RequireProduct)
		p.BusinessId = prov.GetBusinessID()
		p.RequireLevel = prov.GetRequireLevel()
		p.ComboType = prov.GetComboType()
		p.EstimateId = prov.GetEstimateID()
		p.ProductCategory = prov.GetProductCategory()
		if prov.GetPaymentInfo() != nil {
			p.PayType = prov.GetPaymentInfo().DefaultPayType
		}
		p.LevelType = prov.GetLevelType()
		p.CountPriceType = prov.GetCountPriceType()
		multiRequireProduct = append(multiRequireProduct, p)
	}
	param.MultiRequireProduct = multiRequireProduct
	param.EstimateTraceId = context2.GetTrace(ctx).GetTraceId()
	return param
}
