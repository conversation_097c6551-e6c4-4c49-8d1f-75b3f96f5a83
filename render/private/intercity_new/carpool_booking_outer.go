package intercity_new

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

type CarpoolBookingOuterData interface {
	GetDepartureRangeStr() string
}

func CarpoolBookingOuter(ctx context.Context, prov CarpoolBookingOuterData, carpoolBooking *proto.InterCityCarpoolBookingModule) *proto.InterCityCarpoolBookingSku {

	if carpoolBooking == nil {
		return nil
	}
	if len(carpoolBooking.TimeSpan) == 0 {
		return nil
	}
	bottomText := dcmp.GetDcmpPlainContent(ctx, "intercity_new-bottom_text")
	timeSpan := GetTimeSpan(carpoolBooking, prov.GetDepartureRangeStr())

	return &proto.InterCityCarpoolBookingSku{
		BottomText: bottomText,
		TimeSpan:   timeSpan,
	}
}

func isCurrentTimeSpan(span string, departureRangeStr string) bool {
	if span == departureRangeStr {
		return true
	}
	return false
}

func toOuterTimeSpan(timeRange []*TimeSpanWithDay) []*TimeSpanWithDay {
	res := []*TimeSpanWithDay{}
	for _, v := range timeRange {
		res = append(res, &TimeSpanWithDay{
			date: v.date,
			timeRange: &proto.InterCityTimeRange{
				Value:     v.timeRange.Value,
				Msg:       v.timeRange.Msg,
				OuterMsg:  v.timeRange.OuterMsg,
				OrderType: v.timeRange.OrderType,
				SkuDesc:   v.timeRange.SkuDesc,
				Icon:      v.timeRange.Icon,
				Available: v.timeRange.Available,
			},
		})
	}
	return res
}

type TimeSpanWithDay struct {
	timeRange *proto.InterCityTimeRange
	date      string
}

func GetTimeSpan(carpoolBooking *proto.InterCityCarpoolBookingModule, departureRange string) []*proto.InterCityTimeRange {

	// 当前时间片
	var now *TimeSpanWithDay
	var firstSpan *TimeSpanWithDay
	var res []*TimeSpanWithDay
	for _, v := range carpoolBooking.TimeSpan {
		if v == nil || len(v.Range) == 0 {
			continue
		}
		for _, span := range v.Range {
			if firstSpan == nil {
				firstSpan = &TimeSpanWithDay{
					timeRange: span,
					date:      v.Date,
				}
			}
			if isCurrentTimeSpan(span.Value, departureRange) {
				now = &TimeSpanWithDay{
					timeRange: span,
					date:      v.Date,
				}
			}
		}
	}
	if now != nil {
		res = []*TimeSpanWithDay{now}
	} else {
		res = []*TimeSpanWithDay{firstSpan}
	}

	return processTimeSpan(res)
}

func processTimeSpan(timeSpan []*TimeSpanWithDay) []*proto.InterCityTimeRange {
	res := []*proto.InterCityTimeRange{}
	for _, v := range timeSpan {
		res = append(res, &proto.InterCityTimeRange{
			Value:     v.timeRange.Value,
			Msg:       v.date + " " + v.timeRange.Msg,
			OuterMsg:  v.timeRange.OuterMsg,
			OrderType: v.timeRange.OrderType,
			SkuDesc:   v.timeRange.SkuDesc,
			Icon:      v.timeRange.Icon,
			Available: v.timeRange.Available,
		})
	}
	return res
}
