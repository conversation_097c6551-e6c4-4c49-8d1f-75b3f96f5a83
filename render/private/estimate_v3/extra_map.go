package estimate_v3

import (
	"context"
	"fmt"

	"git.xiaojukeji.com/nuwa/trace"
	jsoniter "github.com/json-iterator/go"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	consts2 "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/combo_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

type ExtraMapProvider interface {
	render.ProductProvider
	GetTabId() string
	GetRouteType() int64
	GetPrivateBizInfo() *models.PrivateBizInfo
	GetBaseReqData() *models.BaseReqData
	ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string)
}

const lanKeBaoServiceId = 113

func GetExtraMap(ctx context.Context, prov ExtraMapProvider) *proto.ExtraMapV3 {
	extraMap := &proto.ExtraMapV3{}
	extraMap.SetProductId(prov.GetProductId())
	extraMap.SetBusinessId(prov.GetBusinessID())
	extraMap.SetComboType(prov.GetComboType())
	extraMap.SetRequireLevel(util.ToInt32(prov.GetRequireLevel()))
	extraMap.SetLevelType(prov.GetLevelType())
	extraMap.SetComboId(prov.GetPrivateBizInfo().ComboID)
	extraMap.SetRouteType(prov.GetRouteType())
	extraMap.SetIsSpecialPrice(util.ToInt32(prov.IsSpecialPrice()))
	extraMap.SetCountPriceType(prov.GetCountPriceType())

	// 司乘议价&单勾导流场景下，将导流来源标识给到发单
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdBargain && !prov.GetPrivateBizInfo().BargainIsMultiSelect {
		extraMap.SetBargainFromType(1)
	}

	// 深港车口岸控制
	if prov.GetComboType() == combo_type.ComboTypeShenGangFlatRate {
		cityId := prov.GetBaseReqData().AreaInfo.Area
		toCityId := prov.GetBaseReqData().AreaInfo.ToArea
		key := fmt.Sprintf("%v_%v", cityId, toCityId)
		conf := dcmp.GetJSONResult(ctx, "estimate_form_v3-yuegang_port_type", nil)
		if conf != nil {
			if conf[key].Exists() {
				extraMap.SetPortType(conf[key].String())
			} else {
				extraMap.SetPortType(conf["default"].String())
			}
		}
	}

	if prov.GetPrivateBizInfo().IsNeedDefaultAuth == true {
		extraMap.SetIsDefaultAuth(1)
	}

	extraMap.SetNeedAuthList(prov.GetPrivateBizInfo().NeedAuthList)

	// 三方聚合表单is_default_auth = 0
	if tab.IsClassifyTab(prov.GetTabId()) {
		key, params := prov.ApolloParamsGen(apollo_model.WithPIDKey)
		toggle := apollo.FeatureToggle(ctx, "tripcloud_form_style_controller", key, params)
		if !toggle {
			extraMap.SetIsDefaultAuth(0)
		}
	}

	if carpool.TypeMiniBus == prov.GetCarpoolType() {
		if prov.GetPrivateBizInfo().MiniBusPreMatch != nil &&
			prov.GetPrivateBizInfo().MiniBusPreMatch.EtpInfo != nil {
			minibusEtp := prov.GetPrivateBizInfo().MiniBusPreMatch.EtpInfo.EtpTimeDuration
			extraMap.SetEtp(int32(minibusEtp))
		}
	}

	// 揽客宝与一车双价同时出现时，勾选品类等于默认勾选首选司机
	extraMap.SetExtraCustomFeature(buildExtraCustomFeature(ctx, prov))

	if prov.GetPrivateBizInfo().IsIntercitySurpriseAlone {
		extraMap.SetIsIntercitySurpriseAlone("1")
		extraMap.SetCarpoolSeatNum(1)
		extraMap.SetDepartureRange(prov.GetPrivateBizInfo().DepartureRangeStr)
	}

	return extraMap
}

func buildExtraCustomFeature(ctx context.Context, prov ExtraMapProvider) string {
	isShow := false
	customFeatureList := prov.GetPrivateBizInfo().CustomFeatureList
	for _, customFeature := range customFeatureList {
		if customFeature.ServiceId == lanKeBaoServiceId {
			isShow = true
			break
		}
	}

	if !isShow {
		return ""
	}

	if prov.GetSubGroupId() == consts2.SubGroupIdTaxiPricingBox {
		bytes, err := jsoniter.Marshal(map[string]int{
			"id":    lanKeBaoServiceId,
			"count": 1,
		})
		if err != nil {
			log.Trace.Infof(ctx, trace.DLTagUndefined, "buildExtraCustomFeature json marshal error: %v", err)
			return ""
		}

		return string(bytes)
	}

	return ""
}
