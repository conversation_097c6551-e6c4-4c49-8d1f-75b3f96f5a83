package estimate_v3

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/carpool_type"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/level_type"
	BizConsts "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/product"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/order"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/taxi"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/spacious_car/spacious_common"
	util2 "git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/util"
	carpool3 "git.xiaojukeji.com/gulfstream/passenger-common/biz/carpool"
	"git.xiaojukeji.com/gulfstream/passenger-common/model/price"
	carpool2 "git.xiaojukeji.com/s3e/pts/carpool"
	"github.com/spf13/cast"
)

type PriceDescProvider interface {
	render.BaseProvider
	render.BillInfoProvider
	render.ProductProvider
	IsCarpoolV3Merge(context.Context) bool
	GetPaymentInfo() *PriceApi.EstimateNewFormPaymentInfo
	GetMixedDeductPrice() float64
	GetVCard() *PriceApi.EstimateNewFormVCardInfo
	IsBusinessPay() bool
	GetExtendList() []*PriceApi.EstimateNewFormExtend
	GetCarpoolVCard(ctx context.Context) (string, int32)
	GetCarpoolFailEstimateFee() (float64, bool)
	GetIsSpecialPrice() bool

	ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string)
	render.ApolloProvider
}

type PriceInfoProvider interface {
	PriceDescProvider
	render.BargainInfoProvider
	render.CarpoolInfoProvider

	GetPageType() int32
	GetBaseReqData() *models.BaseReqData
	GetPeakFee() *models.PeakFee
}

type FeeInfoResp struct {
	FeeMsgTemplate string
	FeeAmount      string
	// FeeRangeTemplate string
}

const PriceMsgDCMPKey = "estimate_form_v3_new-estimate_price_msg"

func GetEstimateFeeInfo(ctx context.Context, prov PriceInfoProvider) *FeeInfoResp {
	var (
		feeMsgTemplate string
		feeAmount      string
		feeInfoResp    = new(FeeInfoResp)
	)

	defer func() {
		feeInfoResp.FeeAmount = util.RemoveSuffixZero(feeInfoResp.FeeAmount)
		feeInfoResp.FeeMsgTemplate = getCapPriceFeeMsgReplace(ctx, prov, feeInfoResp.FeeMsgTemplate)
	}()

	// 究极特殊品类，后面修改修改
	if product.IsHongKongProduct(BizConsts.ProductID(prov.GetProductId())) {
		feeInfoResp.FeeMsgTemplate, feeInfoResp.FeeAmount = getHKFeeInfo(ctx, prov)
		return feeInfoResp
	}

	// 1. 特殊前缀 -> apollo、非一口价、预估等常规文案
	feeMsgTemplate = getSpecialFeeMsgTemplate(ctx, prov)
	if feeMsgTemplate == "" {
		feeMsgTemplate = getDefaultFeeMsgTemplate(ctx, prov)
	}

	// 2. 构建feeAmount
	feeAmount = getFeeAmount(ctx, prov)
	// 非企业付
	if !prov.IsBusinessPay() {
		price.CheckSingle(ctx, "GetEstimateFeeInfo", "getFeeAmount", fmt.Sprintf("pc_id: %v feeAmount", prov.GetProductCategory()), util.ToFloat64(feeAmount), price.WithCheckBelowZero())
	}

	// if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdHuiXuanCar {
	// 	feeInfoResp.FeeRangeTemplate = dcmp.GetDcmpContent(ctx, "bargain_range-fee_range_template", nil)
	// }

	// 返回
	feeInfoResp.FeeMsgTemplate, feeInfoResp.FeeAmount = feeMsgTemplate, feeAmount
	return feeInfoResp
}

// 根据不同价格类型选择合适的费用文案模板
func getDefaultFeeMsgTemplate(ctx context.Context, prov PriceInfoProvider) string {
	if prov.GetMixedDeductPrice() > 0 {
		return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.mix_personal")
	}

	if prov.GetCapPrice() > 0 {
		return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.cap_price")
	}

	return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.normal_with_prefix")
}

func getSpecialFeeMsgTemplate(ctx context.Context, prov PriceInfoProvider) string {
	// apollo最优先
	feeMsgTemplate := util2.GetGeneralFeeMsgTemplateNew(ctx, prov.GetCityID(), int(prov.GetProductCategory()), prov.GetLang())
	if feeMsgTemplate != "" {
		return feeMsgTemplate
	}

	// 司乘议价
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdBargain {
		return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.bargain")
	}

	// 惠选车
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdHuiXuanCar {
		return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.bargain_range")
	}

	// 站点巴士
	if prov.GetCarpoolType() == consts.CarPoolTypeInterCityStation {
		return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.station_bus")
	}

	// 宠物出行
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdPetFastCar {
		return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.normal_with_prefix_and_suffix")
	}

	// 代驾
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdDaiJia {
		return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.normal_with_prefix_and_suffix")
	}

	// 拼车类型
	if carpool.IsCarpool(prov.GetCarpoolType()) && prov.GetProductCategory() != estimate_pc_id.EstimatePcIdTaxiCarpool {
		return getCarpoolFeeTemplate(ctx, prov)
	}

	if order.IsSpecialRateV2(prov, 0) {
		// 特惠快车
		return getSpecialRateFeeMsgTemplate(ctx, prov)
	}

	// 出租车类型
	if prov.GetProductId() == product_id.ProductIdUniOne && prov.GetProductCategory() != estimate_pc_id.EstimatePcIdTaxiCarpool {
		feeMsgTemplate, _ = GetUniOneFeeMsgTemplateAndFeeType(ctx, prov)
		// prov.GetLanKeBao().UpdateIsShowEstimatePrice(feeType)
		return feeMsgTemplate
	}

	// 车大
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdSpaciousCar {
		return getSpaciousFeeMsgTemplate(ctx, prov)
	}

	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolSFCar ||
		prov.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolCrossSFCar {
		return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.sfc_carpool")
	}

	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdTrainTicket {
		return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.train")
	}

	return getNormalFeeMsgTemplate(ctx, prov)
}

func getFeeAmount(ctx context.Context, prov PriceInfoProvider) string {
	// 拼车
	if carpool.IsCarpool(prov.GetCarpoolType()) {
		return GetCarpoolFeeAmount(ctx, prov)
	}

	// 特惠快车
	if order.IsSpecialRateV2(prov, 0) {
		return getSpecialRateFeeAmount(ctx, prov)
	}

	// 出租车
	if prov.GetProductId() == product_id.ProductIdUniOne {
		return getTaxiFeeAmount(ctx, prov, true)
	}

	// 车大
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdSpaciousCar {
		return getSpaciousFeeAmount(ctx, prov)
	}

	// 司乘议价
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdBargain {
		return getBargainFeeAmount(ctx, prov)
	}

	// 顺风车
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolSFCar ||
		prov.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolCrossSFCar {
		return getSFCFeeAmount(ctx, prov)
	}

	// 火车票
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdTrainTicket {
		return getTrainFeeAmount(ctx, prov)
	}

	return getNormalFeeAmount(ctx, prov)
}

func getCarpoolFeeTemplate(ctx context.Context, prov PriceInfoProvider) string {
	var (
		feeMsgTemplate string
	)

	if carpool2.IsLowPriceCarpoolV2(ctx, util.ToInt(prov.GetRequireLevel()), int(prov.GetComboType()), int(prov.GetProductId()), int(prov.GetCarpoolType()), int(prov.GetCarpoolPriceType())) {
		feeMsgTemplate = dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.flat_rate_least")
	} else if carpool.IsInterCityCarpool(prov.GetCarpoolType(), prov.GetComboType()) {
		feeMsgTemplate = dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.normal_with_prefix_and_suffix")
		apolloKey, apolloParams := prov.GetApolloParams(biz_runtime.WithPIDKey, biz_runtime.WithRouteGroup, biz_runtime.WithComboID, biz_runtime.WithProductID)
		hit, _ := PreferentialStatus(ctx, apolloKey, apolloParams)
		if hit {
			feeMsgTemplate = dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.flat_rate_least")
		}
	} else if carpool2.IsMiniBusCarpool(ctx, int(prov.GetCarpoolType())) {
		feeMsgTemplate = dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.cap_price")
	} else if carpool.IsCarpoolUnSuccessFlatPrice(prov) && prov.IsCarpoolUnSuccessFlatPriceShowAsCapPrice(ctx) {
		feeMsgTemplate = dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.cap_price")
		if prov.IsHaveCarpoolVCard(ctx) {
			feeMsgTemplate = dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.confirm_succ")
		}
	} else if prov.GetCarpoolType() == carpool_type.CarpoolTypeInterCityStation {
		feeMsgTemplate = dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.station_bus")
	} else if carpool.IsSmartBus(int(prov.GetCarpoolType())) {
		feeMsgTemplate = dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.cap_price")
	}

	if prov.GetMixedDeductPrice() > 0 {
		feeMsgTemplate = dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.mix_personal")
	}

	return feeMsgTemplate
}

func GetUniOneFeeMsgTemplateAndFeeType(ctx context.Context, prov PriceInfoProvider) (string, int) {
	var (
		feeMsgTemplate string
		feeType        int
	)

	showEstimateFee := isShowEstimateFee(ctx, prov)
	if !showEstimateFee {
		prov.GetPeakFee().UpdateIsShowEstimatePrice(false)
	}

	if page_type.PageTypeLankeBao == prov.GetPageType() {
		return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.metered_fare"), consts.FeeTypeTable
	}

	// 计价盒子
	if prov.GetSubGroupId() == consts.SubGroupIdTaxiPricingBox {
		feeMsgTemplate, feeType = getPricingBoxFeeMsgTemplateAndFeeType(ctx, prov)
		if len(feeMsgTemplate) > 0 {
			return feeMsgTemplate, feeType
		}
	}

	if isSpecialPrice(ctx, prov) {
		return dcmp.GetJSONResultWithPath(ctx, PriceMsgDCMPKey, nil,
			fmt.Sprintf("fee_msg_template.%s", getSpecialPriceTextKey(ctx, prov))).String(), consts.FeeTypeCapPrice
	}

	// 在线计价 预估 -> 此次改成预估
	if isShowOnlineEstimateFee(ctx, prov) {
		return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.normal_with_prefix"), consts.FeeTypeDefault
	}

	if showEstimateFee {
		return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.taxi_normal"), consts.FeeTypeDefault
	}

	return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.metered_fare"), consts.FeeTypeTable
}

func isShowOnlineEstimateFee(ctx context.Context, prov PriceInfoProvider) bool {
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey, biz_runtime.WithProductCategory)
	params["level_type"] = util.ToString(prov.GetLevelType())
	params["estimate_metre"] = util.ToString(prov.GetBillDriverMetre())

	return apollo.FeatureToggle(ctx, "taxi_online_estimate_fee_controller", pidKey, params)
}

func isShowEstimateFee(ctx context.Context, prov PriceInfoProvider) bool {
	isShow := false
	pidKey, params := getTaxiApolloParams(ctx, prov)
	if checkForCounty(ctx, pidKey, params) || checkForCity(ctx, pidKey, params) || checkForCityAB(ctx, pidKey, params) {
		isShow = true
	}

	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdCountyTaxi {
		isShow = true
	}

	if isShow && apollo.FeatureToggle(ctx, "taxi_estimate_price_show_threshold", pidKey, params) {
		isShow = false
	}

	return isShow
}

func getTaxiApolloParams(ctx context.Context, prov PriceInfoProvider) (string, map[string]string) {
	key, params := prov.GetApolloParams(biz_runtime.WithPIDKey, biz_runtime.WithCarLevel, biz_runtime.WithIsCrossCity, biz_runtime.WithProductID, biz_runtime.WithCarLevel,
		biz_runtime.WithRequireLevel, biz_runtime.WithProductCategory, biz_runtime.WithMenuID, biz_runtime.WithComboType, biz_runtime.WithIsSpecialPrice,
		biz_runtime.WithIsCrossCity, biz_runtime.WithFromCounty)
	// 比较特殊，不放进去了
	params["estimate_metre"] = util.ToString(prov.GetBillDriverMetre())
	return key, params
}

func checkForCounty(ctx context.Context, userPhoneKey string, params map[string]string) bool {
	return apollo.FeatureToggle(ctx, "estimate_form_unione_show_estimate_fee_county", userPhoneKey, params)
}

func checkForCity(ctx context.Context, userPhoneKey string, params map[string]string) bool {
	return apollo.FeatureToggle(ctx, "estimate_form_unione_show_estimate_fee", userPhoneKey, params)
}

func checkForCityAB(ctx context.Context, userPhoneKey string, params map[string]string) bool {
	ok, assign := apollo.FeatureExp(ctx, "unione_show_estimate_fee_ab", userPhoneKey, params)
	return ok && "treatment_group" == assign.GetGroupName()
}

func getSpaciousFeeMsgTemplate(ctx context.Context, prov PriceInfoProvider) string {
	if prov.GetCapPrice() > 0 {
		return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.cap_price")
	}
	return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.fee_msg_single")
}

func getHKFeeInfo(ctx context.Context, prov PriceDescProvider) (string, string) {
	dcmpPath := "hk_taxi-price_desc"
	key, param := prov.ApolloParamsGen(apollo_model.WithPIDKey)
	feeAmount := util2.PriceFormat(ctx, param, cast.ToString(key), prov.GetEstimateFee(), consts.FeeTypeDefault)
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdHKTaxi {
		dcmpPath = "hk_taxi-price_desc_without_price"
		feeMsgTemplate := dcmp.GetJSONResult(ctx, PriceMsgDCMPKey, nil)["fee_msg_template"].Get(dcmpPath).String()
		return feeMsgTemplate, feeAmount
	}

	// 特殊处理
	result := dcmp.GetJSONResult(ctx, PriceMsgDCMPKey, nil)
	feeMsgTemplate := result["fee_msg_template"].Get(dcmpPath).String()
	// 特殊处理，没有前缀，拼成一个字段给端
	return feeMsgTemplate, feeAmount
}

func getSpecialRateFeeMsgTemplate(ctx context.Context, prov PriceInfoProvider) string {
	if prov.IsBusinessPay() {
		return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.mix_personal")
	}

	if prov.GetCapPrice() > 0 {
		return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.cap_price")
	}

	return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.normal_with_prefix")
}

func getNormalFeeMsgTemplate(ctx context.Context, prov PriceInfoProvider) string {
	key, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	if prov.GetProductId() == product_id.ProductIdAutoDriving {
		params["func_type"] = "1"
		if !apollo.FeatureToggle(ctx, "auto_driving_fee_msg_open_city", key, params) {
			return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.fail")
		}
	} else if prov.GetBillDetail() == nil {
		return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.fail")
	}

	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdTaxiCarpool && prov.GetPageType() == page_type.PageTypeLankeBao {
		return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.cap_price")
	}

	return ""
}

func getPricingBoxFeeMsgTemplateAndFeeType(ctx context.Context, prov PriceInfoProvider) (string, int) {
	if prov.GetLevelType() != level_type.LevelTypeTaxiMarketisation && prov.GetProductCategory() != estimate_pc_id.EstimatePcIdFastTaxi {
		if usingNewBox(ctx, prov) {
			// 打表·预估
			return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.taxi_normal"), consts.FeeTypeDefault
		}

		// 显示打表计价
		return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.metered_fare"), consts.FeeTypeTable
	}

	return dcmp.GetJSONContentWithPath(ctx, PriceMsgDCMPKey, nil, "fee_msg_template.normal_with_prefix"), consts.FeeTypeDefault
}

func usingNewBox(ctx context.Context, prov PriceInfoProvider) bool {
	if prov.GetBaseReqData() == nil {
		return false
	}

	if prov.GetBaseReqData().PassengerInfo.UID == 0 {
		return false
	}

	if prov.GetOrderType() != order.OrderTypeNow {
		return false
	}

	key, params := prov.ApolloParamsGen(apollo_model.WithUIDKey)
	params["county_id"] = util.ToString(prov.GetBaseReqData().AreaInfo.FromCounty)
	params["order_type"] = util.ToString(prov.GetOrderType())
	params["lang"] = prov.GetBaseReqData().CommonInfo.Lang
	params["tab_id"] = prov.GetBaseReqData().CommonInfo.TabId

	return taxi.UsingNewBox(ctx, key, params)
}

func GetCarpoolFeeAmount(ctx context.Context, prov PriceInfoProvider) string {
	key, params := prov.ApolloParamsGen(apollo_model.WithPIDKey)
	estimateFee := prov.GetEstimateFee()
	if carpool.IsPinCheCheV2(prov.GetProductCategory(), prov.GetCarpoolPriceType()) {
		if prov.IsBusinessPay() {
			return "0.0"
		}

		priceList := make([]float64, 0)
		sceneList := prov.GetSceneEstimatePrice()
		for _, scene := range sceneList {
			priceList = append(priceList, scene.GetFee())
		}

		if len(priceList) > 0 {
			sort.Float64s(priceList)
			estimateFee = priceList[0]
		}

		estimateFee = carpool3.FormatPrice(estimateFee, carpool3.PageDefault, util.ToString(prov.GetCityID()), key, util.RoundAbs(estimateFee, 1)).FloatVal
	} else if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolStation {
		if prov.IsBusinessPay() {
			return "0.0"
		}
		estimateFee = carpool3.FormatPrice(estimateFee, carpool3.PageDefault, util.ToString(prov.GetCityID()), key, util.RoundAbs(math.Ceil(estimateFee*10)/10, 1)).FloatVal
	}

	deductFee := prov.GetMixedDeductPrice()
	if deductFee > 0 {
		estimateFee = math.Max(estimateFee-deductFee, 0.0)
	}

	finalEstimateFee := util2.PriceFormat(ctx, params, key, estimateFee, consts.FeeTypeTwoPrice)
	return finalEstimateFee
}

func getSpecialRateFeeAmount(ctx context.Context, prov PriceDescProvider) string {
	var (
		feeType int
	)

	// 企业支付
	if prov.IsBusinessPay() {
		feeType = consts.FeeTypeBusinessPay
	} else if prov.GetCapPrice() > 0 {
		feeType = consts.FeeTypeCapPrice
	} else {
		feeType = consts.FeeTypeDefault
	}

	key, params := prov.ApolloParamsGen(apollo_model.WithPIDKey)
	feeAmount := util2.PriceFormat(ctx, params, key, prov.GetPersonalEstimateFee(), feeType)

	return feeAmount
}

func getTaxiFeeAmount(ctx context.Context, prov PriceInfoProvider, digitExp bool) string {
	_, feeType := GetUniOneFeeMsgTemplateAndFeeType(ctx, prov)
	key, param := prov.ApolloParamsGen(apollo_model.WithPIDKey)
	feeAmount := util2.PriceFormat(ctx, param, cast.ToString(key), prov.GetEstimateFee(), feeType)
	return feeAmount
}

func getSpaciousFeeAmount(ctx context.Context, prov PriceInfoProvider) string {
	var (
		feeAmount string
	)

	key, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	if spacious_common.HitSingle(ctx, key, params) {
		if prov.GetCapPrice() > 0 {
			feeAmount = util2.PriceFormat(ctx, params, key, prov.GetPersonalEstimateFee(), consts.FeeTypeCapPrice)
		} else {
			feeAmount = util2.PriceFormat(ctx, params, key, prov.GetPersonalEstimateFee(), consts.FeeTypeDefault)
		}
	} else {
		feeDetailInfo := prov.GetBillFeeDetailInfo()
		_, _, selectionFee := spacious_common.GetSpaciousCarInfo(feeDetailInfo)
		feeAmount = util2.PriceFormat(ctx, params, key, selectionFee, consts.FeeTypeDefault)
	}
	return feeAmount
}

func getBargainFeeAmount(ctx context.Context, prov PriceInfoProvider) string {
	var (
		feeAmount float64
	)
	_, params := prov.GetApolloParams(biz_runtime.WithUIDKey)
	if apollo.FeatureToggle(ctx, "bargain_recommend_v2_switch", "", params) {
		// 命中端上推荐二期新版本，展示券后价
		feeAmount = util.RoundAbs(prov.GetEstimateFee(), 2)
	} else {
		// 司乘议价表单输入框展示
		recommendPrice := getBargainRecommendInfo(ctx, prov)
		if recommendPrice > 0 {
			feeAmount = util.RoundAbs(recommendPrice, 2)
		} else {
			feeAmount = util.RoundAbs(prov.GetDynamicTotalFee(), 2)
		}
	}

	return util.ToString(feeAmount)
}

func getSFCFeeAmount(ctx context.Context, prov PriceInfoProvider) string {
	var (
		feeAmount string
	)

	key, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	if prov.GetCapPrice() > 0 {
		feeAmount = util2.PriceFormat(ctx, params, key, prov.GetEstimateFee(), consts.FeeTypeCapPrice)
	} else {
		feeAmount = util2.PriceFormat(ctx, params, key, prov.GetEstimateFee(), consts.FeeTypeDefault)
	}
	return feeAmount
}

func getTrainFeeAmount(ctx context.Context, prov PriceInfoProvider) string {
	var (
		feeAmount string
	)

	key, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	feeAmount = util2.PriceFormat(ctx, params, key, prov.GetEstimateFee(), consts.FeeTypeDefault)
	return feeAmount
}

func getNormalFeeAmount(ctx context.Context, prov PriceInfoProvider) string {
	var (
		feeAmount string
		feeType   int
	)

	key, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	if prov.GetProductId() == product_id.ProductIdAutoDriving {
		params["func_type"] = "1"
		if !apollo.FeatureToggle(ctx, "auto_driving_fee_msg_open_city", key, params) {
			return ""
		}
	} else if prov.GetBillDetail() == nil {
		return ""
	}

	estimateFee := prov.GetEstimateFee()
	if prov.GetMixedDeductPrice() > 0 {
		feeType = consts.FeeTypeBusinessPay
		estimateFee = math.Max(0, estimateFee-prov.GetMixedDeductPrice())
	} else {
		feeType = getNormalFeeType(ctx, prov)
	}

	feeAmount = util2.PriceFormat(ctx, params, key, estimateFee, feeType)
	return feeAmount
}

func getNormalFeeType(ctx context.Context, prov PriceInfoProvider) int {
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdTaxiCarpool && prov.GetPageType() == page_type.PageTypeLankeBao {
		return consts.FeeTypeTable
	}

	if prov.GetCapPrice() > 0 {
		return consts.FeeTypeCapPrice
	}

	return consts.FeeTypeDefault
}

func getSpecialPriceTextKey(ctx context.Context, prov PriceDescProvider) string {
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey, biz_runtime.WithIsSpecialPrice, biz_runtime.WithIsCrossCity, biz_runtime.WithFromCounty, biz_runtime.WithMenuID, biz_runtime.WithProductCategory)
	params["car_level"] = prov.GetRequireLevel()
	params["require_level"] = prov.GetRequireLevel()
	params["combo_type"] = strconv.FormatInt(prov.GetComboType(), 10)

	if apollo.FeatureToggle(ctx, "taxi_special_not_show_price_text", pidKey, params) {
		return "normal_with_prefix"
	} else {
		if apollo.FeatureToggle(ctx, "fee_msg_template_cap_price_switch", pidKey, params) {
			return "cap_price_v2"
		}

		return "cap_price"
	}
}

func isSpecialPrice(ctx context.Context, prov PriceDescProvider) bool {
	var isShowMetredFare int64
	if result, err := product_id.GetConfigByProductIDAndPath(prov.GetProductId(), "show_metered_fare"); err != nil || !result.Exists() {
		return false
	} else {
		isShowMetredFare = result.Int()
	}

	return isShowMetredFare == 1 &&
		prov.IsSpecialPrice() &&
		util.InArrayInt64(prov.GetProductId(), []int64{consts.ProductIDUNITAXI, consts.ProductIDBusinessTaixCar})
}

func getBargainRecommendInfo(ctx context.Context, prov PriceInfoProvider) float64 {
	bubbleInfo := prov.GetBargainRecommendInfo()
	if bubbleInfo != nil && bubbleInfo.RecommendPrice != nil && bubbleInfo.RecommendPrice[0] != nil {
		return bubbleInfo.RecommendPrice[0].Price
	}

	return 0.0
}

func PreferentialStatus(ctx context.Context, key string, params map[string]string) (bool, int) {
	if !apollo.FeatureToggle(ctx, "intercity_carpool_subscribe_preferential_toggle", key, params) {
		return false, 0
	}

	isAllow, assignment := apollo.FeatureExp(ctx, "yuantu_reservation_discount", key, params)
	if isAllow && assignment.GetGroupName() == "treatment_group" {
		timeInterval, ok := assignment.GetParameters()["time_interval"]
		if !ok {
			timeInterval = "0"
		}

		return true, util.ToInt(timeInterval) * 60
	}

	return false, 0
}

func getCapPriceFeeMsgReplace(ctx context.Context, prov PriceInfoProvider, feeMsg string) string {
	// product_category * city
	key, params := prov.GetApolloParams(biz_runtime.WithPIDKey, biz_runtime.WithProductCategory)
	hit, assignParams := apollo.GetParameters("fee_msg_template_cap_price_switch", key, params)
	if !hit {
		return feeMsg
	}

	if src, ok := assignParams["replace_src"]; ok && strings.Contains(feeMsg, src) {
		return strings.Replace(feeMsg, src, assignParams["replace_dst"], -1)
	}

	return feeMsg
}
