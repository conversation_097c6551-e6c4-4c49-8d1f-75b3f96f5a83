package anycar_v3

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	consts2 "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/category_carpool"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/common_logic"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"

	Compensation "git.xiaojukeji.com/dirpc/dirpc-go-http-Compensation"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"github.com/spf13/cast"
)

const (
	PeakSeason = "peak_season"

	ReductionCoupon = 0
	DiscountCoupon  = 1
)

type ExtraInfoProvider interface {
	GetNoAnswerCompensationData() []*Compensation.GetNoAnswerCompensationTackData
	car_info.CarNormalNameProvider
	car_info.CarNormalIconProvider
	GetOpenCitySourceId() int32
	ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string)
}

// GetExtraInfoData GetExtraInfoData
func GetExtraInfoData(ctx context.Context, p ExtraInfoProvider, feeType int) map[string]string {
	if p == nil {
		return nil
	}

	extraInfo := make(map[string]string)

	extraInfo["fee_type"] = strconv.Itoa(feeType)
	extraInfo["origin_name"] = car_info.GetNormalCarName(ctx, p)
	extraInfo["origin_icon"] = car_info.GetCarNormalIcon(ctx, p)

	if data := p.GetNoAnswerCompensationData(); data != nil {
		for _, data := range p.GetNoAnswerCompensationData() {
			if data.PrivilegeSource != nil && *data.PrivilegeSource == PeakSeason && util.InArrayInt32(int32(p.GetProductCategory()), data.ProductCategoryList) {
				buildCompensationMetaInfo(extraInfo, data)
			}
		}
	}

	if p.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolStation {
		//sort exp
		if common_logic.DualPriceOrderedOnAsc(p, page_type.PageTypeGuideAnyCar) {
			extraInfo[consts2.DualPriceSort] = consts2.ASC
		}

		//价格字号
		succFontSize, failFontSize := category_carpool.DualPriceFontSize(ctx, p)

		//极速拼车扩展数据
		fontSizeExt := map[string]string{
			"suc_font_size":  cast.ToString(succFontSize), //拼成价格字号，一口价字号
			"fail_font_size": cast.ToString(failFontSize), //未拼成价格字号
		}

		extraInfo["dual_font_size_ext"] = util.JustJsonEncode(fontSizeExt)
	}

	return extraInfo
}

func buildCompensationMetaInfo(extraInfo map[string]string, data *Compensation.GetNoAnswerCompensationTackData) {
	if data == nil {
		return
	}

	extraInfo["activity_product_category_list"] = util.JustJsonEncode(data.ProductCategoryList)
	if data.WaitTime != nil {
		extraInfo["time"] = strconv.Itoa(int(*data.WaitTime))
	}

	if data.WaitTimeUnit != nil {
		extraInfo["time_unit"] = *data.WaitTimeUnit
	}

	if data.CouponInfo != nil {
		if data.CouponInfo.CouponType == ReductionCoupon {
			extraInfo["cost"] = strconv.Itoa(int(data.CouponInfo.Amount))
		} else if data.CouponInfo.CouponType == DiscountCoupon {
			extraInfo["cost"] = strconv.Itoa(int(data.CouponInfo.Discount))
		}

		extraInfo["cost_unit"] = cast.ToString(data.CouponInfo.CouponType)
	}

	if data.ProductCategoryNum != nil {
		extraInfo["product_category_num"] = strconv.Itoa(int(*data.ProductCategoryNum))
	}
}
