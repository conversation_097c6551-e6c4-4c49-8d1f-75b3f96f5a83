package anycar_v3

import (
	"context"
	"errors"
	Compensation "git.xiaojukeji.com/dirpc/dirpc-go-http-Compensation"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/compensation"
	"git.xiaojukeji.com/nuwa/golibs/zerolog/ddlog"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"

	"git.xiaojukeji.com/gulfstream/bronze-door-sdk-go/common/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

// 简单的Mock实现，只包含供应场景测试所需的方法
type mockSupplySceneProvider struct {
	biz_runtime.ProductInfoFull
	memberLevel        int32
	isShowFemaleDriver int64
}

func (m mockSupplySceneProvider) GetMemberLevel() int32 {
	return m.memberLevel
}

func (m mockSupplySceneProvider) GetIsShowFemaleDriver() int64 {
	return m.isShowFemaleDriver
}

// 测试buildSupplySceneRightSubTitle函数
func TestBuildSupplySceneRightSubTitle(t *testing.T) {
	// 测试前清理所有mock
	mockey.UnPatchAll()

	// 测试上下文
	ctx := context.Background()

	// 测试用例
	tests := []struct {
		name          string
		from          string
		setupProvider func() *mockSupplySceneProvider
		mockDcmp      func() []*mockey.Mocker
		expectNil     bool
		expectIconUrl string
	}{
		{
			name: "供需场景信息不存在时返回nil",
			from: "",
			setupProvider: func() *mockSupplySceneProvider {
				return &mockSupplySceneProvider{
					biz_runtime.ProductInfoFull{
						BaseReqData: &models.BaseReqData{
							CommonBizInfo: models.CommonBizInfo{
								// SupplySceneInfo为nil
							},
						},
						Product: &models.Product{ProductCategory: 1001},
					},
					1,
					1,
				}
			},
			mockDcmp:  func() []*mockey.Mocker { return nil },
			expectNil: true,
		},
		{
			name: "不需要展示供需标签时返回nil",
			from: "",
			setupProvider: func() *mockSupplySceneProvider {
				return &mockSupplySceneProvider{
					biz_runtime.ProductInfoFull{
						BaseReqData: &models.BaseReqData{
							CommonBizInfo: models.CommonBizInfo{
								AthenaResult: models.AthenaResult{
									SupplySceneInfo: &AthenaApiv3.SupplySceneInfo{
										IsDisplaySupplyLabel: util.BoolPtr(false),
									},
								},
							},
						},
						Product: &models.Product{ProductCategory: 1001},
					},
					1,
					1,
				}
			},
			mockDcmp:  func() []*mockey.Mocker { return nil },
			expectNil: true,
		},
		{
			name: "当车型不在展示列表中时返回nil",
			from: "",
			setupProvider: func() *mockSupplySceneProvider {
				return &mockSupplySceneProvider{
					biz_runtime.ProductInfoFull{
						BaseReqData: &models.BaseReqData{
							CommonBizInfo: models.CommonBizInfo{
								AthenaResult: models.AthenaResult{
									SupplySceneInfo: &AthenaApiv3.SupplySceneInfo{
										IsDisplaySupplyLabel:   util.BoolPtr(true),
										SupplyLabelProductList: []int32{1002, 1003}, // 不包含测试用的1001
									},
								},
							},
						},
						Product: &models.Product{ProductCategory: 1001},
					},
					1,
					1,
				}
			},
			mockDcmp:  func() []*mockey.Mocker { return nil },
			expectNil: true,
		},
		{
			name: "供需不匹配场景返回带图标的标签",
			from: "",
			setupProvider: func() *mockSupplySceneProvider {
				return &mockSupplySceneProvider{
					biz_runtime.ProductInfoFull{
						BaseReqData: &models.BaseReqData{
							CommonBizInfo: models.CommonBizInfo{
								AthenaResult: models.AthenaResult{
									SupplySceneInfo: &AthenaApiv3.SupplySceneInfo{
										IsDisplaySupplyLabel:   util.BoolPtr(true),
										SupplyLabelProductList: []int32{1001},
										SupplySceneType:        util.Int32Ptr(SupplyDemandNotMatch),
									},
								},
							},
						},
						Product: &models.Product{ProductCategory: 1001},
					},
					1,
					1,
				}
			},
			mockDcmp: func() []*mockey.Mocker {
				// 使用单个mock和To方法，根据参数路径返回不同值
				mock := mockey.Mock(dcmp.GetJSONContentWithPath).To(func(ctx context.Context, configPath string, p map[string]string, path string) string {
					if path == "icon_url_1" {
						return "mock_icon_url"
					}
					return ""
				}).Build()
				return []*mockey.Mocker{mock}
			},
			expectNil:     false,
			expectIconUrl: "mock_icon_url",
		},
		{
			name: "低需求场景返回带背景和内容的标签",
			from: "",
			setupProvider: func() *mockSupplySceneProvider {
				return &mockSupplySceneProvider{
					biz_runtime.ProductInfoFull{
						BaseReqData: &models.BaseReqData{
							CommonBizInfo: models.CommonBizInfo{
								AthenaResult: models.AthenaResult{
									SupplySceneInfo: &AthenaApiv3.SupplySceneInfo{
										IsDisplaySupplyLabel:   util.BoolPtr(true),
										SupplyLabelProductList: []int32{1001},
										SupplySceneType:        util.Int32Ptr(LowDemand),
									},
								},
							},
						},
						Product: &models.Product{
							BizInfo: &models.PrivateBizInfo{
								OrderReceiveCnt: 5,
							},
						},
					},
					1,
					1,
				}
			},
			mockDcmp: func() []*mockey.Mocker {
				// 使用单个mock和To方法，根据参数路径返回不同值
				mock := mockey.Mock(dcmp.GetJSONContentWithPath).To(func(ctx context.Context, configPath string, p map[string]string, path string) string {
					if path == "bg_url" {
						return "mock_bg_url"
					} else if path == "content" {
						return "附近已有5人叫到车"
					}
					return ""
				}).Build()
				return []*mockey.Mocker{mock}
			},
			expectNil: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 准备数据
			provider := tt.setupProvider()
			productInfoFull := provider.ProductInfoFull
			// 设置dcmp的Mock
			var dcmpMockers []*mockey.Mocker
			if tt.mockDcmp != nil {
				dcmpMockers = tt.mockDcmp()
				for _, m := range dcmpMockers {
					defer m.UnPatch()
				}
			}

			// 设置GetCommonBizInfo的Mock
			mockGetCommonBizInfo := mockey.Mock((*biz_runtime.ProductInfoFull).GetCommonBizInfo).Return(productInfoFull.BaseReqData.CommonBizInfo).Build()
			defer mockGetCommonBizInfo.UnPatch()

			// 执行测试方法
			result := buildSupplySceneRightSubTitle(ctx, &productInfoFull, provider, tt.from)

			// 验证结果
			if tt.expectNil {
				assert.Nil(t, result, "应该返回nil")
				return
			}

			assert.NotNil(t, result, "不应该返回nil")

			if tt.expectIconUrl != "" {
				assert.Equal(t, tt.expectIconUrl, result.IconUrl, "图标URL应该匹配")
			}

		})
	}
}

// 测试buildCompensationRightSubTitle函数
func TestBuildCompensationRightSubTitle(t *testing.T) {
	// 测试前清理所有mock
	mockey.UnPatchAll()

	// 测试上下文
	ctx := context.Background()

	// 测试用例
	tests := []struct {
		name          string
		from          string
		setupProvider func() *mockSupplySceneProvider
		mockDcmp      func() []*mockey.Mocker
		mockJson      func() []*mockey.Mocker
		mockTrace     func() []*mockey.Mocker
		expectNil     bool
		expectContent string
	}{
		{
			name: "当PrivateBizInfo为nil时返回nil",
			from: "",
			setupProvider: func() *mockSupplySceneProvider {
				return &mockSupplySceneProvider{
					biz_runtime.ProductInfoFull{
						Product: &models.Product{BizInfo: nil},
					},
					1,
					1,
				}
			},
			mockDcmp:  func() []*mockey.Mocker { return nil },
			mockJson:  func() []*mockey.Mocker { return nil },
			mockTrace: func() []*mockey.Mocker { return nil },
			expectNil: true,
		},
		{
			name: "当CompensationInfo为空时返回nil",
			from: "",
			setupProvider: func() *mockSupplySceneProvider {
				return &mockSupplySceneProvider{
					biz_runtime.ProductInfoFull{
						Product: &models.Product{BizInfo: &models.PrivateBizInfo{
							CompensationInfo: map[string]*Compensation.CompensationAbilityResult{},
						}},
					},
					1,
					1,
				}
			},
			mockDcmp:  func() []*mockey.Mocker { return nil },
			mockJson:  func() []*mockey.Mocker { return nil },
			mockTrace: func() []*mockey.Mocker { return nil },
			expectNil: true,
		},
		{
			name: "当NormalNoAnswerCompensation决策不存在时返回nil",
			from: "",
			setupProvider: func() *mockSupplySceneProvider {
				return &mockSupplySceneProvider{
					biz_runtime.ProductInfoFull{
						Product: &models.Product{BizInfo: &models.PrivateBizInfo{
							CompensationInfo: map[string]*Compensation.CompensationAbilityResult{
								"other_compensation": {
									Decision: 1,
								},
							},
						}},
					},
					1,
					1,
				}
			},
			mockDcmp:  func() []*mockey.Mocker { return nil },
			mockJson:  func() []*mockey.Mocker { return nil },
			mockTrace: func() []*mockey.Mocker { return nil },
			expectNil: true,
		},
		{
			name: "当NormalNoAnswerCompensation决策为0时返回nil",
			from: "",
			setupProvider: func() *mockSupplySceneProvider {
				return &mockSupplySceneProvider{
					biz_runtime.ProductInfoFull{
						Product: &models.Product{BizInfo: &models.PrivateBizInfo{
							CompensationInfo: map[string]*Compensation.CompensationAbilityResult{
								compensation.NormalNoAnswerCompensation: {
									Decision: 0,
								},
							},
						}},
					},
					1,
					1,
				}
			},
			mockDcmp:  func() []*mockey.Mocker { return nil },
			mockJson:  func() []*mockey.Mocker { return nil },
			mockTrace: func() []*mockey.Mocker { return nil },
			expectNil: true,
		},
		{
			name: "无车赔场景返回正确的标题",
			from: "",
			setupProvider: func() *mockSupplySceneProvider {
				return &mockSupplySceneProvider{
					biz_runtime.ProductInfoFull{
						Product: &models.Product{BizInfo: &models.PrivateBizInfo{
							CompensationInfo: map[string]*Compensation.CompensationAbilityResult{
								compensation.NormalNoAnswerCompensation: {
									Decision: 1,
									Extra: map[string]interface{}{
										"biz_name": "无车赔偿",
									},
								},
							},
						}},
					},
					1,
					1,
				}
			},
			mockDcmp: func() []*mockey.Mocker {
				// 模拟dcmp返回标题配置
				mock := mockey.Mock(dcmp.GetJSONContentWithPath).To(func(ctx context.Context, configPath string, p map[string]string, path string) string {
					if configPath == "config_text-normal_compensation" && path == "right_sub_title" {
						return `{"content":"无车赔偿","icon_url":"compensation_icon","bg_url":"compensation_bg","disabled":true}`
					}
					return ""
				}).Build()
				return []*mockey.Mocker{mock}
			},
			mockJson: func() []*mockey.Mocker {
				// 模拟JSON解析
				unmarshalMock := mockey.Mock(json.Unmarshal).To(func(data []byte, v interface{}) error {
					if s, ok := v.(*proto.GroupSubTitle); ok {
						s.Content = "无车赔偿"
						s.IconUrl = "compensation_icon"
						s.BgUrl = util.StringPtr("compensation_bg")
						s.Disabled = util.BoolPtr(true)
					}
					return nil
				}).Build()
				return []*mockey.Mocker{unmarshalMock}
			},
			mockTrace: func() []*mockey.Mocker {
				// 模拟Trace记录
				if log.Trace == nil {
					log.Trace = &ddlog.DiLogHandle{}
				}
				mock := mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build()
				return []*mockey.Mocker{mock}
			},
			expectNil:     false,
			expectContent: "无车赔偿",
		},
		{
			name: "JSON解析失败时返回nil",
			from: "",
			setupProvider: func() *mockSupplySceneProvider {
				return &mockSupplySceneProvider{
					biz_runtime.ProductInfoFull{
						Product: &models.Product{BizInfo: &models.PrivateBizInfo{
							CompensationInfo: map[string]*Compensation.CompensationAbilityResult{
								compensation.NormalNoAnswerCompensation: {
									Decision: 1,
									Extra: map[string]interface{}{
										"biz_name": "无车赔偿",
									},
								},
							},
						}},
					},
					1,
					1,
				}
			},
			mockDcmp: func() []*mockey.Mocker {
				// 模拟dcmp返回标题配置
				mock := mockey.Mock(dcmp.GetJSONContentWithPath).To(func(ctx context.Context, configPath string, p map[string]string, path string) string {
					if configPath == "config_text-normal_compensation" && path == "right_sub_title" {
						return `{invalid json}`
					}
					return ""
				}).Build()
				return []*mockey.Mocker{mock}
			},
			mockJson: func() []*mockey.Mocker {
				// 模拟JSON解析失败
				errMock := mockey.Mock(json.Unmarshal).To(func(data []byte, v interface{}) error {
					return errors.New("validation error")
				}).Build()
				return []*mockey.Mocker{errMock}
			},
			mockTrace: func() []*mockey.Mocker {
				// 模拟Trace记录
				if log.Trace == nil {
					log.Trace = &ddlog.DiLogHandle{}
				}
				mock := mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build()
				return []*mockey.Mocker{mock}
			},
			expectNil: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			provider := tt.setupProvider()
			productInfoFull := provider.ProductInfoFull
			// 设置mock
			var dcmpMockers []*mockey.Mocker
			if tt.mockDcmp != nil {
				dcmpMockers = tt.mockDcmp()
				for _, m := range dcmpMockers {
					defer m.UnPatch()
				}
			}

			var jsonMockers []*mockey.Mocker
			if tt.mockJson != nil {
				jsonMockers = tt.mockJson()
				for _, m := range jsonMockers {
					defer m.UnPatch()
				}
			}

			var traceMockers []*mockey.Mocker
			if tt.mockTrace != nil {
				traceMockers = tt.mockTrace()
				for _, m := range traceMockers {
					defer m.UnPatch()
				}
			}

			// 执行测试方法
			result := buildCompensationRightSubTitle(ctx, &productInfoFull, provider, tt.from)

			// 验证结果
			if tt.expectNil {
				assert.Nil(t, result, "应该返回nil")
				return
			}

			assert.NotNil(t, result, "不应该返回nil")
			if tt.expectContent != "" {
				assert.Equal(t, tt.expectContent, result.Content, "内容应该匹配")
			}
		})
	}
}

// 测试buildRightSubTitle函数
func TestBuildRightSubTitle(t *testing.T) {
	// 测试前清理所有mock
	mockey.UnPatchAll()

	// 测试上下文
	ctx := context.Background()

	// 测试用例
	tests := []struct {
		name                     string
		from                     string
		setupProductInfoFull     func() *biz_runtime.ProductInfoFull
		setupProvider            func() *mockSupplySceneProvider
		mockCompensationSubTitle func() []*mockey.Mocker
		mockSupplySceneSubTitle  func() []*mockey.Mocker
		expectNil                bool
		expectCompensation       bool
		expectSupplyScene        bool
	}{
		{
			name: "当from是pAnycarEstimateV4且FontScaleType不为0时返回nil",
			from: "pAnycarEstimateV4",
			setupProductInfoFull: func() *biz_runtime.ProductInfoFull {
				return &biz_runtime.ProductInfoFull{
					BaseReqData: &models.BaseReqData{
						CommonInfo: models.CommonInfo{
							FontScaleType: 1,
						},
					},
				}
			},
			setupProvider: func() *mockSupplySceneProvider {
				return &mockSupplySceneProvider{}
			},
			mockCompensationSubTitle: func() []*mockey.Mocker { return nil },
			mockSupplySceneSubTitle:  func() []*mockey.Mocker { return nil },
			expectNil:                true,
		},
		{
			name: "当无车赔返回非nil时，应该返回无车赔内容",
			from: "",
			setupProductInfoFull: func() *biz_runtime.ProductInfoFull {
				return &biz_runtime.ProductInfoFull{}
			},
			setupProvider: func() *mockSupplySceneProvider {
				return &mockSupplySceneProvider{}
			},
			mockCompensationSubTitle: func() []*mockey.Mocker {
				// 模拟buildCompensationRightSubTitle返回结果
				mockCompensation := mockey.Mock(buildCompensationRightSubTitle).Return(&proto.GroupSubTitle{
					Content: "无车赔偿",
				}).Build()
				return []*mockey.Mocker{mockCompensation}
			},
			mockSupplySceneSubTitle: func() []*mockey.Mocker { return nil },
			expectNil:               false,
			expectCompensation:      true,
		},
		{
			name: "当无车赔返回nil且供需场景返回非nil时，应该返回供需场景内容",
			from: "",
			setupProductInfoFull: func() *biz_runtime.ProductInfoFull {
				return &biz_runtime.ProductInfoFull{}
			},
			setupProvider: func() *mockSupplySceneProvider {
				return &mockSupplySceneProvider{}
			},
			mockCompensationSubTitle: func() []*mockey.Mocker {
				// 模拟buildCompensationRightSubTitle返回nil
				mockCompensation := mockey.Mock(buildCompensationRightSubTitle).Return(nil).Build()
				return []*mockey.Mocker{mockCompensation}
			},
			mockSupplySceneSubTitle: func() []*mockey.Mocker {
				// 模拟buildSupplySceneRightSubTitle返回结果
				mockSupplyScene := mockey.Mock(buildSupplySceneRightSubTitle).Return(&proto.GroupSubTitle{
					Content: "供需场景",
				}).Build()
				return []*mockey.Mocker{mockSupplyScene}
			},
			expectNil:         false,
			expectSupplyScene: true,
		},
		{
			name: "当无车赔和供需场景都返回nil时，应该返回nil",
			from: "",
			setupProductInfoFull: func() *biz_runtime.ProductInfoFull {
				return &biz_runtime.ProductInfoFull{}
			},
			setupProvider: func() *mockSupplySceneProvider {
				return &mockSupplySceneProvider{}
			},
			mockCompensationSubTitle: func() []*mockey.Mocker {
				// 模拟buildCompensationRightSubTitle返回nil
				mockCompensation := mockey.Mock(buildCompensationRightSubTitle).Return(nil).Build()
				return []*mockey.Mocker{mockCompensation}
			},
			mockSupplySceneSubTitle: func() []*mockey.Mocker {
				// 模拟buildSupplySceneRightSubTitle返回nil
				mockSupplyScene := mockey.Mock(buildSupplySceneRightSubTitle).Return(nil).Build()
				return []*mockey.Mocker{mockSupplyScene}
			},
			expectNil: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 准备数据
			productInfoFull := tt.setupProductInfoFull()
			provider := tt.setupProvider()

			// 设置mock
			var compensationMockers []*mockey.Mocker
			if tt.mockCompensationSubTitle != nil {
				compensationMockers = tt.mockCompensationSubTitle()
				for _, m := range compensationMockers {
					defer m.UnPatch()
				}
			}

			var supplySceneMockers []*mockey.Mocker
			if tt.mockSupplySceneSubTitle != nil {
				supplySceneMockers = tt.mockSupplySceneSubTitle()
				for _, m := range supplySceneMockers {
					defer m.UnPatch()
				}
			}

			// 执行测试方法
			result := buildRightSubTitle(ctx, productInfoFull, provider, tt.from)

			// 验证结果
			if tt.expectNil {
				assert.Nil(t, result, "应该返回nil")
				return
			}

			assert.NotNil(t, result, "不应该返回nil")

			if tt.expectCompensation {
				assert.Equal(t, "无车赔偿", result.Content, "应该返回无车赔偿内容")
			}

			if tt.expectSupplyScene {
				assert.Equal(t, "供需场景", result.Content, "应该返回供需场景内容")
			}
		})
	}
}
