package anycar_v3

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/render"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/biz-lib-go/utils/tagreplace"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/bitly/go-simplejson"
)

type SeatNumProvider interface {
	render.BaseProvider
	render.ProductProvider
	render.RequestProvider
	GetPaymentInfo() *PriceApi.EstimateNewFormPaymentInfo
}

func GetCarpoolSeatNum(ctx context.Context, prov SeatNumProvider) []*proto.NewFormCarpoolSeatOption {
	if !carpool.IsCarpool(prov.GetCarpoolType()) {
		return nil
	}
	// 小巴不下发carpoolSeatNum
	if carpool.IsMiniBus(int(prov.GetCarpoolType())) {
		return nil
	}
	// 智能小巴不下发carpoolSeatNum
	if carpool.IsMiniBus(int(prov.GetCarpoolType())) {
		return nil
	}

	var carpoolSeatNum int32
	if prov.GetCarpoolSeatNum() != 0 {
		carpoolSeatNum = prov.GetCarpoolSeatNum()
	}
	if prov.GetPassengerCount() != nil && carpoolSeatNum == 0 &&
		carpool.IsLowPriceCarpoolByInfos(prov.GetComboType(), prov.GetProductId(), prov.GetCarpoolType(), prov.GetRequireLevel()) {
		carpoolSeatNum = *prov.GetPassengerCount()
	}
	carpoolSeats, err := buildCarpoolSeats(ctx, carpoolSeatNum)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "err:%v", err)
	}
	return carpoolSeats
}

func buildCarpoolSeats(ctx context.Context, carpoolSeatNum int32) ([]*proto.NewFormCarpoolSeatOption, error) {
	config := dcmp.GetDcmpContent(ctx, "config_text-pget_order_match_info", nil)
	jsonConfig, err := simplejson.NewJson([]byte(config))
	if err != nil {
		return nil, err
	}
	textModel, err := jsonConfig.Get("guide_carpool_confirm_seat_num").String()
	if err != nil {
		return nil, err
	}
	tr := tagreplace.NewDefaultTagReplacer()
	oneSeatText := tr.ReplaceTag(textModel, map[string]string{"num": "1"})
	var oneIsSelected bool
	var twoIsSelected bool
	if 1 == carpoolSeatNum {
		oneIsSelected = true
	}
	twoSeatText := tr.ReplaceTag(textModel, map[string]string{"num": "2"})
	if 2 == carpoolSeatNum {
		twoIsSelected = true
	}

	res := []*proto.NewFormCarpoolSeatOption{{
		Label:    oneSeatText,
		Value:    1,
		Selected: oneIsSelected,
	}, {
		Label:    twoSeatText,
		Value:    2,
		Selected: twoIsSelected,
	}}
	return res, nil
}
