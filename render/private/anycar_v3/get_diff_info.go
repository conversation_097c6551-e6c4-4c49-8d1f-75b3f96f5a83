package anycar_v3

import (
	"context"
	"fmt"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	ProductCategory "git.xiaojukeji.com/gulfstream/biz-common-go/v6/category"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	trace "git.xiaojukeji.com/lego/context-go"
	"strconv"
)

type BaseFeeInfoBody struct {
	FeeMsg           string
	FeeAmount        string
	FeeType          int
	FeeTemplate      string
	MinFeeAmount     string
	FeeRangeTemplate string
	SendCarMaxPrice  float64
	Product          *biz_runtime.ProductInfoFull
	MultiPriceList   []*proto.NewFormMultiPrice
}

func GetDiffInfo(ctx context.Context, baseFeeInfoBody *BaseFeeInfoBody) (string, string) {

	if baseFeeInfoBody == nil {
		return "", ""
	}
	product := baseFeeInfoBody.Product
	enablePriceDiff, _ := getDiffPriceExam(ctx, baseFeeInfoBody.Product)
	if !enablePriceDiff {
		return "", ""
	}
	if product.GetProductCategory() == estimate_pc_id.EstimatePcIdHuiXuanCar {
		return "", ""
	}
	// 出租车
	if product.GetProductId() == product_id.ProductIdUniOne {
		return dealTaxiDiffFeeInfo(ctx, baseFeeInfoBody)
	}
	// 极速拼车
	if product.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolStation {
		return dealCarpoolStationDiffFeeInfo(ctx, baseFeeInfoBody)
	}
	return dealDiffFeeInfo(ctx, baseFeeInfoBody.SendCarMaxPrice, baseFeeInfoBody.FeeAmount)
}

const (
	NORMAL_DIFF_PRICE           = "再加{{{num}}}元"
	CAR_POOL_FAIL_DIFF_PRICE    = "未拼成{{{num}}}元"
	CAR_POOL_SUCCESS_DIFF_PRICE = "拼成{{{num}}}元"
	CAR_POOL_PREFIX             = "拼成·"
	DefaultHighLightColor       = "#999999"
)

func GetDiffPriceInfoDescList(ctx context.Context, baseFeeInfoBody *BaseFeeInfoBody) []*proto.NewFormFeeDesc {
	if baseFeeInfoBody == nil || baseFeeInfoBody.Product == nil {
		return nil
	}
	var (
		feeMsg         string
		highlightColor string
	)
	product := baseFeeInfoBody.Product
	enablePriceDiff, showAbsolutePrice := getDiffPriceExam(ctx, product)
	if !enablePriceDiff {
		return nil
	}
	if !showAbsolutePrice && product.GetProductCategory() != estimate_pc_id.EstimatePcIdCarpoolStation {
		return nil
	}
	feeDesc := make([]*proto.NewFormFeeDesc, 0)
	feeMsg = baseFeeInfoBody.FeeMsg
	// 极速拼车
	if product.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolStation {
		if baseFeeInfoBody.MultiPriceList != nil && len(baseFeeInfoBody.MultiPriceList) > 0 {
			if len(baseFeeInfoBody.MultiPriceList) == 1 {
				feeMsg = baseFeeInfoBody.MultiPriceList[0].FeeMsg
			} else {
				var (
					failFeeMsg    string
					successFeeMsg string
				)

				if conf1 := dcmp.GetJSONContentWithPath(ctx, "simple_estimate-diff_price_msg", nil, "car_pool_fail_diff_price"); len(conf1) > 0 {
					failFeeMsg = util.ReplaceTag(ctx, conf1, map[string]string{
						"num": strconv.FormatFloat(baseFeeInfoBody.MultiPriceList[0].FeeAmount, 'f', 1, 64),
					})

				} else {
					failFeeMsg = util.ReplaceTag(ctx, CAR_POOL_FAIL_DIFF_PRICE, map[string]string{
						"num": strconv.FormatFloat(baseFeeInfoBody.MultiPriceList[0].FeeAmount, 'f', 1, 64),
					})
				}

				if conf2 := dcmp.GetJSONContentWithPath(ctx, "simple_estimate-diff_price_msg", nil, "car_pool_success_diff_price"); len(conf2) > 0 {
					successFeeMsg = util.ReplaceTag(ctx, conf2, map[string]string{
						"num": strconv.FormatFloat(baseFeeInfoBody.MultiPriceList[1].FeeAmount, 'f', 1, 64),
					})

				} else {
					successFeeMsg = util.ReplaceTag(ctx, CAR_POOL_SUCCESS_DIFF_PRICE, map[string]string{
						"num": strconv.FormatFloat(baseFeeInfoBody.MultiPriceList[1].FeeAmount, 'f', 1, 64),
					})
				}
				feeMsg = fmt.Sprintf("%s|%s", failFeeMsg, successFeeMsg)
			}
		}
	}

	// 出租车打表计价
	if product.GetProductId() == product_id.ProductIdUniOne && baseFeeInfoBody.FeeType == consts.FeeTypeTable {
		feeMsg = ""
	}

	// 惠选车
	if estimate_pc_id.EstimatePcIdHuiXuanCar == product.GetProductCategory() {
		feeMsg = ""
	}

	// 高亮颜色
	if confColor := dcmp.GetJSONContentWithPath(ctx, "simple_estimate-diff_price_msg", nil, "desc_list_num_color"); len(confColor) > 0 {
		highlightColor = confColor

	} else {
		highlightColor = DefaultHighLightColor
	}

	item := &proto.NewFormFeeDesc{
		Content:        feeMsg,
		HighlightColor: util.StringPtr(highlightColor),
	}
	feeDesc = append(feeDesc, item)
	return feeDesc
}

func dealTaxiDiffFeeInfo(ctx context.Context, baseFeeInfoBody *BaseFeeInfoBody) (string, string) {
	if baseFeeInfoBody == nil {
		return "", ""
	}
	// 打表计价
	if baseFeeInfoBody.FeeType == consts.FeeTypeTable {
		productPriceAmount, err := strconv.ParseFloat(baseFeeInfoBody.FeeAmount, 64)
		if err != nil {
			return "", ""
		}
		var diffFeeAmount string
		referPriceAmountV2 := baseFeeInfoBody.SendCarMaxPrice * 100
		productPriceAmountV2 := productPriceAmount * 100
		if productPriceAmountV2 <= referPriceAmountV2 {
			diffFeeAmount = "0"
		} else {
			diffAmount := (productPriceAmountV2 - referPriceAmountV2) / 100
			diffFeeAmount = strconv.FormatFloat(diffAmount, 'f', 1, 64)
		}
		return diffFeeAmount, baseFeeInfoBody.FeeMsg
	}
	diffFee, diffFeeMsg := dealDiffFeeInfo(ctx, baseFeeInfoBody.SendCarMaxPrice, baseFeeInfoBody.FeeAmount)
	return diffFee, diffFeeMsg

}

func dealCarpoolStationDiffFeeInfo(ctx context.Context, baseFeeInfoBody *BaseFeeInfoBody) (string, string) {
	if baseFeeInfoBody == nil {
		return "", ""
	}
	var feeAmount string
	multiPriceList := baseFeeInfoBody.MultiPriceList

	if multiPriceList == nil || len(multiPriceList) < 1 {
		feeAmount = baseFeeInfoBody.FeeAmount
	} else if len(multiPriceList) == 1 {
		feeAmount = fmt.Sprintf("%f", multiPriceList[0].FeeAmount)
	} else {
		feeAmount = fmt.Sprintf("%f", multiPriceList[1].FeeAmount)
	}

	diffFee, diffFeeMsg := dealDiffFeeInfo(ctx, baseFeeInfoBody.SendCarMaxPrice, feeAmount)

	// 一口价
	if multiPriceList == nil || len(multiPriceList) < 2 {
		return diffFee, diffFeeMsg
	} else {
		// 极速拼车两口价加前缀
		successDiffFeeMsg := getCarPoolPrefix(ctx) + diffFeeMsg
		return diffFee, successDiffFeeMsg
	}

}

func dealDiffFeeInfo(ctx context.Context, sendCarMaxPrice float64, fee string) (string, string) {
	productPriceAmount, err := strconv.ParseFloat(fee, 64)
	var (
		diffFeeAmount string
		diffFeeMsg    string
	)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "transform string to float64 failed||err=%v", err)
		return "", ""
	}
	// 转换成分
	referPriceAmountV2 := sendCarMaxPrice * 100
	productPriceAmountV2 := productPriceAmount * 100
	if productPriceAmountV2 <= referPriceAmountV2 {
		diffFeeAmount = "0"
	} else {
		diffAmount := (productPriceAmountV2 - referPriceAmountV2) / 100
		diffFeeAmount = strconv.FormatFloat(diffAmount, 'f', 1, 64)
	}

	if conf := dcmp.GetJSONContentWithPath(ctx, "simple_estimate-diff_price_msg", nil, "normal_diff_price"); len(conf) > 0 {
		diffFeeMsg = util.ReplaceTag(ctx, conf, map[string]string{
			"num": diffFeeAmount,
		})
	} else {
		diffFeeMsg = util.ReplaceTag(ctx, NORMAL_DIFF_PRICE, map[string]string{
			"num": diffFeeAmount,
		})
	}

	return diffFeeAmount, diffFeeMsg
}

func GetDiffMultiPriceList(ctx context.Context, baseFeeInfoBody *BaseFeeInfoBody) []*proto.NewFormMultiPrice {
	if baseFeeInfoBody == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "baseFeeInfoBody is nil")
		return nil
	}
	multiPriceList := baseFeeInfoBody.MultiPriceList
	product := baseFeeInfoBody.Product
	if multiPriceList == nil || len(multiPriceList) < 1 {
		return multiPriceList
	}
	if product.Product.ProductCategory != ProductCategory.ProductCategoryCarpoolStation {
		return multiPriceList
	}

	enablePriceDiff, _ := getDiffPriceExam(ctx, product)
	if !enablePriceDiff {
		return multiPriceList
	}

	newFormMultiPrice := make([]*proto.NewFormMultiPrice, 0)

	// 一口价
	failPriceItem := multiPriceList[0]
	failFee := fmt.Sprintf("%f", failPriceItem.FeeAmount)
	failFeeDiffAmount, failFeeDiffMsg := dealDiffFeeInfo(ctx, baseFeeInfoBody.SendCarMaxPrice, failFee)
	finaFailFeeDiffAmount, _ := strconv.ParseFloat(failFeeDiffAmount, 64)
	newFormMultiPrice = append(newFormMultiPrice, &proto.NewFormMultiPrice{
		FeeMsg:        failPriceItem.FeeMsg,
		FeeAmount:     failPriceItem.FeeAmount,
		FeeDesc:       failPriceItem.FeeDesc,
		FeeDiffMsg:    &failFeeDiffMsg,
		FeeDiffAmount: &finaFailFeeDiffAmount,
		IsLargeFont:   failPriceItem.IsLargeFont,
	})

	// 两口价
	if len(multiPriceList) > 1 {
		successPriceItem := multiPriceList[1]
		successFee := fmt.Sprintf("%f", successPriceItem.FeeAmount)
		successFeeDiffAmount, successFeeDiffMsg := dealDiffFeeInfo(ctx, baseFeeInfoBody.SendCarMaxPrice, successFee)
		finalSuccessFeeDiffAmount, _ := strconv.ParseFloat(successFeeDiffAmount, 64)
		finalSuccessFeeDiffMsg := getCarPoolPrefix(ctx) + successFeeDiffMsg
		newFormMultiPrice = append(newFormMultiPrice, &proto.NewFormMultiPrice{
			FeeMsg:        successPriceItem.FeeMsg,
			FeeAmount:     successPriceItem.FeeAmount,
			FeeDesc:       successPriceItem.FeeDesc,
			FeeDiffMsg:    &finalSuccessFeeDiffMsg,
			FeeDiffAmount: &finalSuccessFeeDiffAmount,
			IsLargeFont:   successPriceItem.IsLargeFont,
		})
	}

	return newFormMultiPrice

}

func getDiffPriceExam(ctx context.Context, product *biz_runtime.ProductInfoFull) (bool, bool) {
	var (
		enablePriceDiff  bool
		showAbsolutPrice bool
	)
	if product == nil {
		log.Trace.Info(ctx, trace.DLTagUndefined, "product is nil")
		return false, false
	}
	apolloKey, params := product.GetApolloParams(biz_runtime.WithPIDKey)
	toggleAllow, toggleAssignment := apollo.FeatureExp(ctx, "ab_waitingpage_jiacha", apolloKey, params)
	if !toggleAllow {
		log.Trace.Info(ctx, trace.DLTagUndefined, "not hit jiacha experiment")
		return false, false
	}
	if toggleAssignment.GetParameter("enable_price_diff", "0") == "1" {
		enablePriceDiff = true
	}

	if toggleAssignment.GetParameter("show_absolute_price", "0") == "1" {
		showAbsolutPrice = true
	}

	return enablePriceDiff, showAbsolutPrice

}
func getCarPoolPrefix(ctx context.Context) string {
	if conf := dcmp.GetJSONContentWithPath(ctx, "simple_estimate-diff_price_msg", nil, "car_pool_prefix"); len(conf) > 0 {
		return conf
	}
	return CAR_POOL_PREFIX
}
