package common_logic

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/spf13/cast"
)

const (
	RedPacketFee    = "red_packet"
	CouponCustomTag = "anycar_v3-coupon_custom_tag"

	TaxiPeakFeeKey                        = "taxi_peak_fee"      // 峰期加价
	TaxiPeakDiscount                      = "taxi_peak_discount" // 峰期折扣
	PRICE_TYPE_REVOLVING_ACCOUNT_DISCOUNT = 9                    // 内循环账户折扣
	PRICE_TYPE_REVOLVING_ACCOUNT_REBATE   = 10                   // 内循环账户返利
)

// DecrementFunc 定义减价项方法
type DecrementFunc func(ctx context.Context, full *biz_runtime.ProductInfoFull) *PriceDesc

type PriceDesc struct {
	Amount   float64
	Content  string
	LeftIcon string
	Type     int32
}

type customTagMap struct {
	Content         string `json:"content"`
	Icon            string `json:"icon"`
	ProductCategory []int  `json:"product_category"`
	MaxDeduction    int    `json:"max_deduction"`
}

// GetIncrementItems 构建加价项
func GetIncrementItems(ctx context.Context, full *biz_runtime.ProductInfoFull, num int) []*PriceDesc {
	var (
		decItems []*PriceDesc
	)

	apolloParam := map[string]string{
		"city":          strconv.Itoa(full.GetCityID()),
		"phone":         full.GetUserPhone(),
		"app_version":   full.GetAppVersion(),
		"access_key_id": strconv.Itoa(int(full.GetAccessKeyId())),
	}

	//red_packet 展示加开关
	if apollo.FeatureToggle(ctx, "gs_holiday_fee_fee_desc", strconv.Itoa(int(full.GetUID())), apolloParam) {
		if redPacketFeePriceDesc := getRedPacketFee(ctx, full); redPacketFeePriceDesc != nil && len(redPacketFeePriceDesc.Content) > 0 {
			decItems = append(decItems, redPacketFeePriceDesc)
		}
	}

	if num >= len(decItems) {
		//暂时未处理加价项过多  返回数量需要小于等于 num int
		return decItems
	}

	return decItems
}

// GetDecrementItems 构建减价项
func GetDecrementItems(ctx context.Context, full *biz_runtime.ProductInfoFull, num int, functionList ...DecrementFunc) []PriceDesc {
	var (
		decItems    []PriceDesc
		totalAmount float64
	)

	for _, function := range functionList {
		// 遍历需要执行的减价项
		if priceDesc := function(ctx, full); priceDesc != nil && priceDesc.Content != "" {
			totalAmount = totalAmount + priceDesc.Amount
			decItems = append(decItems, *priceDesc)
		}
	}

	if num >= len(decItems) {
		return decItems
	}

	// 优惠共抵
	if totalDiscountDesc := buildTotalDiscount(ctx, full, totalAmount); totalDiscountDesc != nil {
		return []PriceDesc{*totalDiscountDesc}
	}

	return decItems
}

// GetTaxiPeakDiscountDescAndIcon 峰期折扣
func GetTaxiPeakDiscountDescAndIcon(ctx context.Context, full *biz_runtime.ProductInfoFull) (priceDesc *PriceDesc) {
	// 峰期折扣，绑定在峰期信息费的优惠费项，如果有则是个负数
	peakDisCount := GetValueFromDisplayLines(full, TaxiPeakDiscount)
	peakDisCount = math.Abs(cast.ToFloat64(peakDisCount))
	if peakDisCount == 0 {
		return
	}

	// 文案渲染
	priceDesc = &PriceDesc{
		Amount: peakDisCount,
	}
	if content := dcmp.GetSubContent(ctx, "common-price_extra_info", TaxiPeakDiscount); content != "" {
		tag := map[string]string{
			"num": util.FormatPrice(peakDisCount, -1),
		}
		priceDesc.Content, priceDesc.LeftIcon = util.ReplaceTag(ctx, content, tag), ""
	}

	return
}

func GetCouponDescAndIcon(ctx context.Context, full *biz_runtime.ProductInfoFull) (priceDesc *PriceDesc) {
	var (
		couponAmount float64
		hitCustomTag bool
		customTag    string
	)

	if full.GetCouponInfo() == nil {
		return
	}

	couponAmount, err := strconv.ParseFloat(full.GetCouponInfo().Amount, 64)
	if err != nil || couponAmount == 0 {
		return
	}
	couponAmount = couponAmount / 100

	if len(full.GetCouponInfo().CustomTag) > 0 {
		customTag = full.GetCouponInfo().CustomTag
		hitCustomTag = true
	}

	priceDesc = &PriceDesc{
		Amount: couponAmount,
	}
	if page_type.PageTypeGuideAnyCar == full.BaseReqData.CommonInfo.PageType {
		if content := dcmp.GetSubContent(ctx, "common-price_extra_info", "coupon_desc"); content != "" {
			tag := map[string]string{
				"amount": util.FormatPrice(couponAmount, -1),
			}
			priceDesc.Content, priceDesc.LeftIcon = util.ReplaceTag(ctx, content, tag), ""
		}

		if hitCustomTag {
			// 命中custom_tag，如果有对应dcmp，则替换。
			var tagMap customTagMap
			config := dcmp.GetJSONContentWithPath(ctx, "common-price_extra_info_custom_tag", nil, customTag)
			err := json.Unmarshal([]byte(config), &tagMap)
			if err != nil {
				return
			}

			content, icon, pcList, maxDeduction := tagMap.Content, tagMap.Icon, tagMap.ProductCategory, tagMap.MaxDeduction
			pcMap := make(map[int]string)
			for _, value := range pcList {
				pcMap[value] = ""
			}

			if len(pcList) > 0 {
				// 限制特定车型展示
				if _, ok := pcMap[int(full.Product.ProductCategory)]; ok {
					if (maxDeduction == 0) || (maxDeduction != 0 && couponAmount < float64(maxDeduction)) {
						// 目前只有特快新客用到maxDeduction
						tag := map[string]string{
							"amount": util.FormatPrice(couponAmount, -1),
						}
						priceDesc.Content, priceDesc.LeftIcon = util.ReplaceTag(ctx, content, tag), icon
					}
				}
			} else {
				tag := map[string]string{
					"amount": util.FormatPrice(couponAmount, -1),
				}
				priceDesc.Content, priceDesc.LeftIcon = util.ReplaceTag(ctx, content, tag), icon
			}
		}
	}

	return
}

func GetRewardsDescAndIcon(ctx context.Context, full *biz_runtime.ProductInfoFull) (priceDesc *PriceDesc) {
	var (
		rewardsAmount float64
	)

	if full.GetBonus() == nil {
		return
	}

	rewardsAmount, err := strconv.ParseFloat(full.GetBonus().Amount, 64)
	if err != nil || rewardsAmount == 0 {
		return
	}
	rewardsAmount = rewardsAmount / 100

	priceDesc = &PriceDesc{
		Amount: rewardsAmount,
	}

	if page_type.PageTypeGuideAnyCar == full.BaseReqData.CommonInfo.PageType {
		if content := dcmp.GetSubContent(ctx, "common-price_extra_info", "rewards_title"); content != "" {
			unit, symbol := util.GetCurrencyUnitAndSymbol(ctx, full.Product.ProductID, full.GetBillInfoCurrency())
			tag := map[string]string{
				"currency_symbol": symbol,
				"num":             util.FormatPrice(rewardsAmount, -1),
				"currency_unit":   unit,
			}
			priceDesc.Content, priceDesc.LeftIcon = util.ReplaceTag(ctx, content, tag), ""
		}
	}

	return
}

// 获取内循环账户折扣信息
func GetRevolvingAccountDiscountDescAndIcon(ctx context.Context, full *biz_runtime.ProductInfoFull) (priceDesc *PriceDesc) {
	var (
		discountAmount float64
	)

	if full.GetRevolvingAccountDiscount() == nil {
		return
	}

	discountAmount, err := strconv.ParseFloat(full.GetRevolvingAccountDiscount().Amount, 64)
	if err != nil || discountAmount == 0 {
		return
	}
	discountAmount = discountAmount / 100

	priceDesc = &PriceDesc{
		Amount: discountAmount,
		Type:   PRICE_TYPE_REVOLVING_ACCOUNT_DISCOUNT,
	}
	if content := dcmp.GetSubContent(ctx, "common-price_extra_info", "revolving_account_discount_title"); content != "" {
		unit, symbol := util.GetCurrencyUnitAndSymbol(ctx, full.Product.ProductID, full.GetBillInfoCurrency())
		tag := map[string]string{
			"currency_symbol": symbol,
			"num":             util.FormatPrice(discountAmount, -1),
			"currency_unit":   unit,
		}
		icon := dcmp.GetSubContent(ctx, "common-price_extra_info", "revolving_account_discount_icon")
		priceDesc.Content, priceDesc.LeftIcon = util.ReplaceTag(ctx, content, tag), icon
	}

	return
}

// 获取内循环账户返利信息
func GetRevolvingAccountRebateDescAndIcon(ctx context.Context, full *biz_runtime.ProductInfoFull) (priceDesc *PriceDesc) {
	var (
		rebateAmount float64
	)

	if full.GetRevolvingAccountRebate() == nil {
		return
	}

	rebateAmount, err := strconv.ParseFloat(full.GetRevolvingAccountRebate().Amount, 64)
	if err != nil || rebateAmount == 0 {
		return
	}
	rebateAmount = rebateAmount / 100

	priceDesc = &PriceDesc{
		Amount: rebateAmount,
		Type:   PRICE_TYPE_REVOLVING_ACCOUNT_REBATE,
	}
	if content := dcmp.GetSubContent(ctx, "common-price_extra_info", "revolving_account_rebate_title"); content != "" {
		unit, symbol := util.GetCurrencyUnitAndSymbol(ctx, full.Product.ProductID, full.GetBillInfoCurrency())
		tag := map[string]string{
			"currency_symbol": symbol,
			"num":             util.FormatPrice(rebateAmount, -1),
			"currency_unit":   unit,
		}
		icon := dcmp.GetSubContent(ctx, "common-price_extra_info", "revolving_account_rebate_icon")
		priceDesc.Content, priceDesc.LeftIcon = util.ReplaceTag(ctx, content, tag), icon
	}

	return
}

func GetPricePrivilegeDescLite(ctx context.Context, full *biz_runtime.ProductInfoFull) (priceDesc *PriceDesc) {

	if full.GetBillInfo() == nil ||
		full.GetBillInfo().PricePrivilegeType == nil ||
		biz_runtime.PricePrivilegeTypeFastUpDefault != *full.GetBillInfo().PricePrivilegeType {
		return nil
	}
	baseReq := full.BaseReqData
	product := full.Product
	params := map[string]string{
		"key":           strconv.FormatInt(baseReq.PassengerInfo.PID, 10),
		"passenger_id":  strconv.FormatInt(baseReq.PassengerInfo.PID, 10),
		"city":          strconv.FormatInt(int64(baseReq.AreaInfo.City), 10),
		"access_key_id": strconv.FormatInt(int64(baseReq.CommonInfo.AccessKeyID), 10),
		"lang":          baseReq.CommonInfo.Lang,
		"require_level": product.RequireLevel,
		"product_id":    strconv.FormatInt(product.ProductID, 10),
		"type":          strconv.FormatInt(int64(product.OrderType), 10),
		"app_version":   baseReq.CommonInfo.AppVersion,
		"from":          "pre_sale_price_info_desc",
	}
	// 是否展示打专车快车价
	if !apollo.FeatureToggle(ctx, biz_runtime.ApolloShowSwitch, strconv.FormatInt(baseReq.PassengerInfo.UID, 10), params) {
		return nil
	}

	priceDesc = &PriceDesc{}
	if content := dcmp.GetSubContent(ctx, "common-price_extra_info", "price_privilege_desc"); content != "" {
		priceDesc.Content, priceDesc.LeftIcon = content, ""
		return
	}
	return nil
}

func buildTotalDiscount(ctx context.Context, full *biz_runtime.ProductInfoFull, totalAmount float64) (priceDesc *PriceDesc) {
	if content := dcmp.GetSubContent(ctx, "common-price_extra_info", "discount_title"); content != "" && full.GetBillInfo() != nil {
		priceDesc = &PriceDesc{}
		unit, symbol := util.GetCurrencyUnitAndSymbol(ctx, full.Product.ProductID, full.GetBillInfoCurrency())
		tag := map[string]string{
			"currency_symbol": symbol,
			"num":             util.FormatPrice(totalAmount, -1),
			"currency_unit":   unit,
		}
		priceDesc.Content, priceDesc.LeftIcon = util.ReplaceTag(ctx, content, tag), ""
	}
	return
}

// getRedPacketFee 加价项:春节服务费
func getRedPacketFee(ctx context.Context, full *biz_runtime.ProductInfoFull) *PriceDesc {
	if redPacketFee := GetValueFromDisplayLines(full, RedPacketFee); redPacketFee > 0 {
		if content := dcmp.GetSubContent(ctx, "common-price_extra_info", "red_packet_desc"); content != "" {
			tag := map[string]string{
				"amount": util.FormatPrice(redPacketFee, -1),
			}
			return &PriceDesc{
				Amount:   redPacketFee,
				Content:  util.ReplaceTag(ctx, content, tag),
				LeftIcon: "",
			}
		}
	}
	return nil
}

// GetFeeByFeeDetailInfo 加价项:春节服务费
func GetFeeByFeeDetailInfo(ctx context.Context, feeDetailInfo map[string]float64) *PriceDesc {
	if len(feeDetailInfo) < 1 {
		return nil
	}
	if redPacketFee, ok := feeDetailInfo[RedPacketFee]; ok && redPacketFee > 0 {
		if content := dcmp.GetSubContent(ctx, "common-price_extra_info", "red_packet_desc"); content != "" {
			tag := map[string]string{
				"amount": util.FormatPrice(redPacketFee, -1),
			}
			return &PriceDesc{
				Amount:   redPacketFee,
				Content:  util.ReplaceTag(ctx, content, tag),
				LeftIcon: "",
			}
		}
	}
	return nil
}

// GetValueFromDisplayLines 根据key获取display_lines的value
func GetValueFromDisplayLines(full *biz_runtime.ProductInfoFull, key string) float64 {
	if len(full.GetBillDisplayLines()) > 0 {
		for _, displayLine := range full.GetBillDisplayLines() {
			if key == displayLine.Name {
				return displayLine.Value
			}
		}
	}
	return 0
}

func GetHKCouponDescAndIcon(ctx context.Context, full *biz_runtime.ProductInfoFull) (priceDesc *PriceDesc) {
	var (
		couponAmount float64
	)

	if full.GetCouponInfo() == nil {
		return
	}

	couponAmount, err := strconv.ParseFloat(full.GetCouponInfo().Amount, 64)
	if err != nil || couponAmount == 0 {
		return
	}

	priceDesc = &PriceDesc{
		Amount: couponAmount,
	}
	couponAmount = couponAmount / 100

	if content := dcmp.GetSubContent(ctx, "common-price_extra_info_hk", "coupon_desc"); content != "" {
		tag := map[string]string{
			"amount": util.FormatPrice(couponAmount, -1),
		}
		icon := dcmp.GetSubContent(ctx, "common-price_extra_info_hk", "coupon_icon")
		priceDesc.Content, priceDesc.LeftIcon = util.ReplaceTag(ctx, content, tag), icon
	}

	return
}

func GetHKBonusDescAndIcon(ctx context.Context, full *biz_runtime.ProductInfoFull) (priceDesc *PriceDesc) {

	hkBonus := full.GetHkBonus()
	if hkBonus == nil {
		return
	}

	hkBonusAmount, err := strconv.ParseFloat(full.GetHkBonus().Amount, 64)
	if err != nil || hkBonusAmount == 0 {
		return
	}
	hkBonusAmount = hkBonusAmount / 100
	priceDesc = &PriceDesc{
		Amount: hkBonusAmount,
	}

	if content := dcmp.GetSubContent(ctx, "common-price_extra_info_hk", "bonus_title"); content != "" {
		unit, symbol := util.GetCurrencyUnitAndSymbol(ctx, full.Product.ProductID, full.GetBillInfoCurrency())
		tag := map[string]string{
			"currency_symbol": symbol,
			"num":             util.FormatPrice(hkBonusAmount, -1),
			"currency_unit":   unit,
		}
		icon := dcmp.GetSubContent(ctx, "common-price_extra_info_hk", "bonus_icon")
		priceDesc.Content, priceDesc.LeftIcon = util.ReplaceTag(ctx, content, tag), icon
	}

	return
}

// GetCarPoolCoupon 获取拼车券 未拼成券-X元/拼成券-Y元
func GetCarPoolCoupon(ctx context.Context, prov *biz_runtime.ProductInfoFull) *proto.NewFormFeeDesc {
	// 一口价
	if prov.IsCarpoolV3Merge(ctx) {
		if couponInfo := prov.GetCouponInfo(); couponInfo != nil {
			couponAmount, err := strconv.ParseFloat(couponInfo.Amount, 64)
			if err != nil || couponAmount == 0 {
				return nil
			}
			couponAmount = couponAmount / 100

			if config := dcmp.GetJSONMap(ctx, CouponCustomTag, "default"); len(config) > 0 {
				tag := map[string]string{
					"amount": util.FormatPrice(couponAmount, -1),
				}
				return &proto.NewFormFeeDesc{
					BorderColor: config["border_color"].String(),
					Content:     util.ReplaceTag(ctx, config["content"].String(), tag),
					Icon:        config["icon"].String(),
					Amount:      couponAmount,
				}
			}
		}
	} else {
		succCoupon := prov.GetCouponInfo()
		failCoupon := prov.GetCarpoolFailedCoupon()
		if succCoupon != nil && failCoupon != nil {
			succAmount := util.String2float64(ctx, succCoupon.Amount) / 100
			failAmount := util.String2float64(ctx, failCoupon.Amount) / 100

			if succAmount > 0 && failAmount > 0 {
				if conf := dcmp.GetJSONMap(ctx, CouponCustomTag, "merge_coupon"); len(conf) > 0 {
					return &proto.NewFormFeeDesc{
						BorderColor: conf["border_color"].String(),
						Content: fmt.Sprintf("%s%s",
							util.ReplaceTag(ctx, conf["content"].String(), map[string]string{
								"amount": util.FormatPrice(failAmount, -1),
							}),
							util.ReplaceTag(ctx, conf["suffix"].String(), map[string]string{
								"amount": util.FormatPrice(succAmount, -1),
							}),
						),
						Icon: conf["icon"].String(),
						Type: 3,
					}
				}
			}
		}
	}

	return nil
}
