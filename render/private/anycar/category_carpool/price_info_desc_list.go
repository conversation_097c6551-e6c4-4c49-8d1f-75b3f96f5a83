package category_carpool

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/gulfstream/mamba/render/private/anycar"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/anycar/common_logic"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type dcmpConf struct {
	FailV2 string `json:"fail_v_2"`
	FailV3 string `json:"fail_v_3"`
}

type DcmpVcard struct {
	VcardText map[string]DcmpItem
}

type DcmpItem struct {
	LeftIcon string `json:"left_icon"`
	Text     string `json:"text"`
}

func GetPriceInfoDescList(ctx context.Context, full *biz_runtime.ProductInfoFull, prov anycar.PriceInfoDescListProvider) []*proto.AnyCarPriceDescInfo {
	var (
		priceInfoDescList  []*proto.AnyCarPriceDescInfo
		increPriceInfoDesc *proto.AnyCarPriceDescInfo //加价项(一个位置,在未拼成价格后)
	)

	// 需要有拼成和未拼成价格 如果只有一个说明响应错误
	_, ok := prov.GetCarpoolFailEstimateFee()
	if !ok {
		return priceInfoDescList
	}

	priceInfo, err := getCarpoolFail(ctx, prov)
	if err == nil && priceInfo != nil {
		priceInfoDescList = append(priceInfoDescList, priceInfo)
	}

	if priceDescList := common_logic.GetIncrementItems(ctx, full, 1); len(priceDescList) > 0 {
		increPriceInfoDesc = &proto.AnyCarPriceDescInfo{
			Content:  priceDescList[0].Content,
			LeftIcon: priceDescList[0].LeftIcon,
		}
		priceInfoDescList = append(priceInfoDescList, increPriceInfoDesc)
	}

	if len(priceInfoDescList) < 2 {
		if VcardTag := getVcardTag(ctx, prov); VcardTag != nil {
			priceInfoDescList = append(priceInfoDescList, VcardTag)
		}
	}

	return priceInfoDescList
}

// getVcardTag 判断是否有省钱卡赠卡或者买卡的情况，有则渲染赠卡标签
func getVcardTag(ctx context.Context, prov anycar.PriceInfoDescListProvider) *proto.AnyCarPriceDescInfo {
	//判断是否有省钱卡赠卡或者买卡的情况，有则渲染赠卡标签
	vcardResult := prov.GetVCard()
	if vcardResult == nil {
		return nil
	}

	//vcardResult := vcard.GetVCard()
	dcmpVcard := &DcmpVcard{}
	vcardText := &dcmpVcard.VcardText
	// 读取极速拼车-新增赠卡相关文案
	config := dcmp.GetDcmpContent(ctx, "anycar_estimate-new_fee_card", nil)
	err := json.Unmarshal([]byte(config), vcardText)
	if err != nil {
		return nil
	}

	var priceInfo *proto.AnyCarPriceDescInfo
	if vcardResult != nil {
		if carpool.SendNew == vcardResult.Source {
			priceInfo = &proto.AnyCarPriceDescInfo{
				LeftIcon: (*vcardText)["0"].LeftIcon,
				Content:  (*vcardText)["0"].Text,
			}
		} else if carpool.SendLoss == vcardResult.Source {
			priceInfo = &proto.AnyCarPriceDescInfo{
				LeftIcon: (*vcardText)["1"].LeftIcon,
				Content:  (*vcardText)["1"].Text,
			}
		} else if carpool.Usable == vcardResult.Source && carpool.PayStatus == vcardResult.PayStatus {
			priceInfo = &proto.AnyCarPriceDescInfo{
				LeftIcon: (*vcardText)["2"].LeftIcon,
				Content:  (*vcardText)["2"].Text,
			}
		} else if carpool.Usable == vcardResult.Source && carpool.PayStatus != vcardResult.PayStatus {
			priceInfo = &proto.AnyCarPriceDescInfo{
				LeftIcon: (*vcardText)["3"].LeftIcon,
				Content:  (*vcardText)["3"].Text,
			}
		} else if carpool.UsableGive == vcardResult.Source {
			priceInfo = &proto.AnyCarPriceDescInfo{
				LeftIcon: (*vcardText)["4"].LeftIcon,
				Content:  (*vcardText)["4"].Text,
			}
		}
	}
	return priceInfo
}

// getCarpoolFail 未拼成价格描述
func getCarpoolFail(ctx context.Context, prov anycar.PriceInfoDescListProvider) (
	priceInfo *proto.AnyCarPriceDescInfo, err error) {

	var carpoolFailMsg string

	//如果命中了拼成/未拼成 价格合并 则不展示未拼成价
	if prov.IsCarpoolV3Merge(ctx) {
		return
	}

	_dcmpConf, err := loadDcmpConf(ctx)
	if err != nil || _dcmpConf == nil {
		return
	}

	if carpool.IsCarpoolDualPriceV2(prov.GetCarpoolType(), prov.IsDualCarpoolPrice(), prov.GetCarpoolPriceType()) {
		carpoolFailMsg = _dcmpConf.FailV2
	} else {
		fee, _ := prov.GetCarpoolFailEstimateFee()
		tag := map[string]string{
			"carpool_fail_price": util.Float64ToString(fee),
		}
		carpoolFailMsg = util.ReplaceTag(ctx, _dcmpConf.FailV3, tag)
	}

	priceInfo = &proto.AnyCarPriceDescInfo{
		Content: carpoolFailMsg,
	}
	return
}

func loadDcmpConf(ctx context.Context) (conf *dcmpConf, err error) {
	conf = new(dcmpConf)
	dcmpStr := dcmp.GetDcmpContent(ctx, "carpool_config-dual_price", nil)
	err = json.Unmarshal([]byte(dcmpStr), conf)
	return
}
