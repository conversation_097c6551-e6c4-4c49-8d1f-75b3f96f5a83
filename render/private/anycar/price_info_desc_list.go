package anycar

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/taxi"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/anycar/common_logic"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type PriceInfoDescListProvider interface {
	render.ProductProvider
	render.BillInfoProvider
	render.ActivityInfoProvider
	render.PaymentInfoProvider
	render.MemberInfoProvider
	render.BaseProvider
	render.TaxiInfoProvider
	render.DynamicIconABProvider

	GetPricingBoxData() *taxi.PricingBoxData
	IsCarpoolV3Merge(context.Context) bool
	render.ApolloProvider
	ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string)
	GetFastCarPrice() float64
}

const (
	maxCountLimit = 2 // 价格描述最多两个位置
)

func GetPriceInfoDescList(ctx context.Context, prov PriceInfoDescListProvider) []*proto.AnyCarPriceDescInfo {
	ret := make([]*proto.AnyCarPriceDescInfo, 0)
	env := fee_desc_engine.NewEnv(consts.AnyCarForm).SetDcmpKey(consts.AnyCarFeeDetailDesc).SetApolloParams(prov)
	resp := fee_desc_engine.NewFeeEngine(input.BuildNormalFeeInput(ctx, prov, consts.AnyCarForm), env).SetProductCategory(prov.GetProductCategory()).Do(ctx)
	if resp == nil {
		return nil
	}

	for _, output := range resp {
		if output == nil {
			continue
		}

		desc := &proto.AnyCarPriceDescInfo{
			LeftIcon: output.Icon,
			Content:  output.Content,
			Type:     output.Type,
		}
		if output.Fee != nil {
			desc.Amount = output.Fee.Amount
		}

		ret = append(ret, desc)
	}

	return ret
}

//func GetDescListNormal(ctx context.Context, prov PriceInfoDescListProvider, descListResult []*proto.AnyCarPriceDescInfo,
//) []*proto.AnyCarPriceDescInfo {
//
//	full := prov.GetFullProduct()
//	if priceDescList := common_logic.GetIncrementItems(ctx, full, 2); len(priceDescList) > 0 {
//		for _, priceDesc := range priceDescList {
//			priceDescInfo := &proto.AnyCarPriceDescInfo{
//				Content:  priceDesc.Content,
//				LeftIcon: priceDesc.LeftIcon,
//			}
//			descListResult = append(descListResult, priceDescInfo)
//		}
//	}
//
//	if len(descListResult) >= maxCountLimit {
//		// 加价项数量超过限制, 不再展示减价项
//		return descListResult
//	}
//
//	leftCount := maxCountLimit - len(descListResult) // 加价项没超过限制 计算剩余位置的数量
//
//	decrementFunctions := getDecrementFunctions(full)
//
//	if priceDescList := common_logic.GetDecrementItems(ctx, full, leftCount, decrementFunctions...); len(priceDescList) > 0 {
//		for _, desc := range priceDescList {
//			priceDescInfo := &proto.AnyCarPriceDescInfo{
//				Content:  desc.Content,
//				LeftIcon: desc.LeftIcon,
//			}
//			descListResult = append(descListResult, priceDescInfo)
//		}
//	}
//	if len(descListResult) >= maxCountLimit {
//		//价格标签项超过限制
//		return descListResult
//	}
//
//	if rebateDesc := common_logic.GetRevolvingAccountRebateDescAndIcon(ctx, full); rebateDesc != nil {
//		priceDescInfo := &proto.AnyCarPriceDescInfo{
//			Content:  rebateDesc.Content,
//			LeftIcon: rebateDesc.LeftIcon,
//			Amount:   rebateDesc.Amount,
//			Type:     rebateDesc.Type,
//		}
//		descListResult = append(descListResult, priceDescInfo)
//	}
//
//	if len(descListResult) >= maxCountLimit {
//		//价格标签项超过限制
//		return descListResult
//	}
//
//	if discountDescAndIcon := common_logic.GetRevolvingAccountDiscountDescAndIcon(ctx, full); discountDescAndIcon != nil {
//		priceDescInfo := &proto.AnyCarPriceDescInfo{
//			Content:  discountDescAndIcon.Content,
//			LeftIcon: discountDescAndIcon.LeftIcon,
//			Amount:   discountDescAndIcon.Amount,
//			Type:     discountDescAndIcon.Type,
//		}
//		descListResult = append(descListResult, priceDescInfo)
//	}
//
//	return descListResult
//}

func getDecrementFunctions(full *biz_runtime.ProductInfoFull) []common_logic.DecrementFunc {
	var decrementFunctions = []common_logic.DecrementFunc{
		common_logic.GetCouponDescAndIcon,
		common_logic.GetRewardsDescAndIcon,
	}

	if full.Product.ProductCategory == estimate_pc_id.EstimatePcIdPremiumComfort {
		decrementFunctions = append(decrementFunctions, common_logic.GetPricePrivilegeDescLite)
	}
	return decrementFunctions
}
