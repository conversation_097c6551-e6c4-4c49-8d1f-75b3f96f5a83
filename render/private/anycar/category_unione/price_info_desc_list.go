package category_unione

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/category_unione"
	"strconv"

	ProductCategory "git.xiaojukeji.com/gulfstream/biz-common-go/v6/category"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/anycar"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/anycar/common_logic"
	trace "git.xiaojukeji.com/lego/context-go"
	"github.com/spf13/cast"
)

const (
	// TaxiHolidayPriceFee 节假日附加费
	TaxiHolidayPriceFee = "taxi_holiday_price"
	// TaxiPeakFeePrice 峰期加价账单标识
	TaxiPeakFeePrice = "taxi_peak_price"
	// TaxiPeakFeeKey 峰期加价DCMP文案
	TaxiPeakFeeKey = "taxi_peak_fee"
)

func GetPriceInfoDescList(ctx context.Context, full *biz_runtime.ProductInfoFull, prov anycar.PriceInfoDescListProvider) []*proto.AnyCarPriceDescInfo {
	descList := make([]*proto.AnyCarPriceDescInfo, 0)
	env := fee_desc_engine.NewEnv(consts.AnyCarForm).SetApolloParams(full).SetDcmpKey(consts.AnyCarFeeDetailDesc)
	resp := fee_desc_engine.NewFeeEngine(input.BuildTaxiFeeInput(ctx, full, consts.AnyCarForm), env).SetProductCategory(prov.GetProductCategory()).Do(ctx)

	for _, output := range resp {
		if output == nil {
			continue
		}

		desc := &proto.AnyCarPriceDescInfo{
			LeftIcon: output.Icon,
			Content:  output.Content,
		}

		descList = append(descList, desc)
	}

	if prov.GetProductCategory() == ProductCategory.ProductCategoryUnione && full.BaseReqData.CommonInfo.PricingBoxData == nil {
		//若命中展示预估价，则将打表计价挪到第二行 [针对于普通打表出租车]
		params := map[string]string{
			"phone":            full.BaseReqData.PassengerInfo.Phone,
			"city":             strconv.FormatInt(int64(full.BaseReqData.AreaInfo.City), 10),
			"menu_id":          full.BaseReqData.CommonInfo.MenuID,
			"access_key_id":    strconv.FormatInt(int64(full.BaseReqData.CommonInfo.AccessKeyID), 10),
			"is_special_price": util.Bool2string(full.Product.IsSpecialPrice),
			"is_cross_city":    util.Bool2string(full.BaseReqData.AreaInfo.City != full.BaseReqData.AreaInfo.ToArea),
			"combo_type":       strconv.FormatInt(full.Product.ComboType, 10),
			"car_level":        full.Product.RequireLevel,
			"require_level":    full.Product.RequireLevel,
			"product_category": strconv.FormatInt(full.Product.ProductCategory, 10),
			"county":           cast.ToString(full.GetAreaInfo().FromCounty),
		}
		if category_unione.IsTaxiShowEstimateFee(ctx, full.Product.ProductCategory,
			cast.ToString(full.BaseReqData.PassengerInfo.UID), full.BaseReqData.PassengerInfo.Phone, params, full.GetBillDriverMetre()) {
			priceDesc := dcmp.GetDcmpContent(ctx, "unione-price_desc_without_price", nil)
			if len(descList) <= 0 {
				priceDescInfo := &proto.AnyCarPriceDescInfo{Content: priceDesc}
				descList = append(descList, priceDescInfo)
			} else {
				descList[0].Content = priceDesc + ", " + descList[0].Content
			}
		}
	}

	return descList
}

// GetIncrementItems 构建加价项
func GetIncrementItems(ctx context.Context, full *biz_runtime.ProductInfoFull, num int) []*common_logic.PriceDesc {
	var (
		decItems []*common_logic.PriceDesc
	)

	// 此处目前只有两个加费项，如果需要添加多个的话，建议转到Apollo搞一个配置来控制。
	// 峰期加价
	if taxiPeakPriceDesc := getTaxiPeakFeeItem(ctx, full); taxiPeakPriceDesc != nil && len(taxiPeakPriceDesc.Content) > 0 {
		decItems = append(decItems, taxiPeakPriceDesc)
	}

	// 节假日附加费
	if taxiHolidayPriceDesc := getTaxiHolidayFeeItem(ctx, full); taxiHolidayPriceDesc != nil && len(taxiHolidayPriceDesc.Content) > 0 {
		decItems = append(decItems, taxiHolidayPriceDesc)
	}

	if num >= len(decItems) {
		//暂时未处理加价项过多  返回数量需要小于等于 num int
		return decItems
	}

	return decItems
}

// getTaxiHolidayFeeItem 节假日附加费
func getTaxiHolidayFeeItem(ctx context.Context, full *biz_runtime.ProductInfoFull) *common_logic.PriceDesc {
	if holidayFee := GetValueFromFeeDetailInfo(full, TaxiHolidayPriceFee); holidayFee > 0 {
		if content := dcmp.GetSubContent(ctx, "common-price_extra_info", TaxiHolidayPriceFee); content != "" {
			tag := map[string]string{
				"amount": cast.ToString(holidayFee),
			}
			return &common_logic.PriceDesc{
				Amount:   holidayFee,
				Content:  util.ReplaceTag(ctx, content, tag),
				LeftIcon: "",
			}
		}
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "common-price_extra_info taxi_holiday_price not found")
	}
	return nil
}

// getTaxiPeakFeeItem 峰期加价
func getTaxiPeakFeeItem(ctx context.Context, full *biz_runtime.ProductInfoFull) *common_logic.PriceDesc {
	if peakFee := GetValueFromFeeDetailInfo(full, TaxiPeakFeePrice); peakFee > 0 {
		if content := dcmp.GetSubContent(ctx, "common-price_extra_info", TaxiPeakFeeKey); content != "" {
			tag := map[string]string{
				"num": util.FormatPrice(peakFee, -1),
			}
			return &common_logic.PriceDesc{
				Amount:   peakFee,
				Content:  util.ReplaceTag(ctx, content, tag),
				LeftIcon: "",
			}
		}
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "common-price_extra_info taxi_peak_fee not found")
	}
	return nil
}

// GetValueFromFeeDetailInfo 根据key获取fee_detail_info的value
func GetValueFromFeeDetailInfo(full *biz_runtime.ProductInfoFull, key string) float64 {
	if len(full.GetBillInfo().FeeDetailInfo) > 0 {
		for k, v := range full.GetBillInfo().FeeDetailInfo {
			if key == k {
				return v
			}
		}
	}
	return 0
}
