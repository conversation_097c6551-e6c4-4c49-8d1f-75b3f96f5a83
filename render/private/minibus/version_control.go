package minibus

import (
	"strconv"

	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

func JudgeNewMinibus(accessKeyId int32, appVersion string) bool {
	res := false
	param := ApolloModel.NewUser("").
		With("access_key_id", strconv.FormatInt(int64(accessKeyId), 10)).
		With("app_version", appVersion)

	toggle, err := ApolloSDK.FeatureToggle("minibus_update", param)
	if err == nil && toggle.IsAllow() {
		res = true
	}
	return res
}
