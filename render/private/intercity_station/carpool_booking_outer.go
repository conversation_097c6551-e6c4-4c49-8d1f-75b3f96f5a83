package intercity_station

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"github.com/tidwall/gjson"
)

const (
	OpenAppointmentRange = 13
)

type CarpoolBookingOuterData interface {
	GetDepartureTime() int64
	GetAppointmentRange() int
}

func CarpoolBookingOuter(ctx context.Context, prov CarpoolBookingOuterData, carpoolBooking *proto.InterCityCarpoolBookingModule, remainSeatsMap map[string]int32) *proto.InterCityCarpoolBookingSku {

	if carpoolBooking == nil {
		return nil
	}
	if len(carpoolBooking.TimeSpan) == 0 {
		return nil
	}
	departureTime := strconv.Itoa(int(prov.GetDepartureTime()))
	stationTemplate := dcmp.GetDcmpPlainContent(ctx, "intercity_station-carpool_booking")
	timeSpan, isTop3 := GetTimeSpan(carpoolBooking, departureTime, stationTemplate)
	signalText := ""
	if len(timeSpan) != 0 && !isTop3 {
		signalText = gjson.Get(stationTemplate, "signal_text").String()
	}

	if len(timeSpan) == 1 && isCurrentTimeSpan(departureTime, timeSpan[0].Value) {
		signalText = ""
	}

	res := &proto.InterCityCarpoolBookingSku{
		SignalText: signalText,
		TimeSpan:   timeSpan,
	}

	if carpoolBooking.GetNoDisplayBookingSelect() {
		res.BottomText = ""
	} else if prov.GetAppointmentRange() == OpenAppointmentRange {
		res.BottomText = gjson.Get(stationTemplate, "open_appointment_bottom_text").String()
	} else {
		res.BottomText = gjson.Get(stationTemplate, "bottom_text").String()
	}

	return res
}

func isCurrentTimeSpan(span string, departureRangeStr string) bool {
	if span == departureRangeStr {
		return true
	}
	return false
}

func toOuterTimeSpan(timeRange []*TimeSpanWithDay) []*TimeSpanWithDay {
	res := []*TimeSpanWithDay{}
	for _, v := range timeRange {
		res = append(res, &TimeSpanWithDay{
			date: v.date,
			timeRange: &proto.InterCityTimeRange{
				Value:             v.timeRange.Value,
				Msg:               v.timeRange.Msg,
				OuterMsg:          v.timeRange.OuterMsg,
				OrderType:         v.timeRange.OrderType,
				SkuDesc:           v.timeRange.SkuDesc,
				Available:         v.timeRange.Available,
				BusServiceShiftId: v.timeRange.BusServiceShiftId,
			},
		})
	}
	return res
}

type TimeSpanWithDay struct {
	timeRange *proto.InterCityTimeRange
	date      string
}

func getDate(tmp *proto.InterCityTimeSpan) string {
	if tmp.Date == "" {
		return tmp.Day
	}
	return tmp.Date
}

func GetTimeSpan(carpoolBooking *proto.InterCityCarpoolBookingModule, departureRange string, skuTemplate string) ([]*proto.InterCityTimeRange, bool) {
	// 前三个时间片
	timeSpan := []*TimeSpanWithDay{}
	// 推荐时间片
	recommendTimeSpan := []*TimeSpanWithDay{}
	// 当前时间片
	var now *TimeSpanWithDay
	// 前三个时间片是否有库存
	top3HaveSku := false
	// 前三个是否有库存
	isTop3 := true
	var res []*TimeSpanWithDay
	for _, v := range carpoolBooking.TimeSpan {
		if v == nil || len(v.Range) == 0 {
			continue
		}
		for _, span := range v.Range {
			// 当前时间片是否有库存
			isHaveSku := span.Available
			if isCurrentTimeSpan(span.Value, departureRange) {
				now = &TimeSpanWithDay{
					timeRange: span,
					date:      getDate(v),
				}
			}

			if len(timeSpan) < 3 {
				top3HaveSku = top3HaveSku || isHaveSku
				timeSpan = append(timeSpan, &TimeSpanWithDay{
					timeRange: span,
					date:      getDate(v),
				})
				continue
			}

			if len(recommendTimeSpan) < 2 && isHaveSku {
				recommendTimeSpan = append(recommendTimeSpan, &TimeSpanWithDay{
					timeRange: span,
					date:      getDate(v),
				})
			}
			if len(recommendTimeSpan) == 2 && len(timeSpan) == 3 && now != nil {
				break
			}
		}
	}

	if len(timeSpan) != 0 && top3HaveSku {
		// 出前三个
		res = toOuterTimeSpan(timeSpan)
	} else if len(recommendTimeSpan) != 0 {
		// 出推荐
		isTop3 = false
		res = toOuterTimeSpan(recommendTimeSpan)
	} else {
		// todo 处理 异常情况
		return nil, isTop3
	}
	return processTimeSpan(res, isTop3, now, skuTemplate), isTop3
}

func processTimeSpan(timeSpan []*TimeSpanWithDay, isTop3 bool, now *TimeSpanWithDay, skuTemplate string) []*proto.InterCityTimeRange {
	res := []*proto.InterCityTimeRange{}
	haveCurrentTime := false
	for _, v := range timeSpan {
		v.timeRange.Msg = v.date + " " + v.timeRange.Msg
		if !isTop3 {
			v.timeRange.Icon = gjson.Get(skuTemplate, "rec_icon").String()
		}
		res = append(res, v.timeRange)
		if now == nil || now.timeRange.Value == v.timeRange.Value || !now.timeRange.Available {
			haveCurrentTime = true
		}
	}
	if !haveCurrentTime {
		nowRe := &proto.InterCityTimeRange{
			Value:             now.timeRange.Value,
			Msg:               now.date + " " + now.timeRange.Msg,
			OuterMsg:          now.timeRange.OuterMsg,
			OrderType:         now.timeRange.OrderType,
			SkuDesc:           now.timeRange.SkuDesc,
			Icon:              now.timeRange.Icon,
			Available:         now.timeRange.Available,
			BusServiceShiftId: now.timeRange.BusServiceShiftId,
		}
		return []*proto.InterCityTimeRange{nowRe}
	}
	return res
}
