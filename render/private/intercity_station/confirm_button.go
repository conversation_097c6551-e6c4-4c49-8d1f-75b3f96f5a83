package intercity_station

import (
	"context"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
)

type ConfirmButtonProv interface {
	GetDepartureTime() int64
	IsStationScan() bool
	ValidScanCode() bool
}

func ConfirmButton(ctx context.Context, prov ConfirmButtonProv) string {
	if prov.IsStationScan() && !prov.ValidScanCode() {
		return dcmp.GetDcmpContent(ctx, "intercity_station-scan_fail_confirm_button", nil)
	}

	return dcmp.TranslateTemplate(dcmp.GetDcmpContent(ctx, "intercity_station-new_order_button", nil), map[string]string{
		"time": time.Unix(prov.GetDepartureTime(), 0).Format("01.02 15:04"),
	})
}
