package intercity_station

import (
	"context"
	"fmt"
	"strconv"
	"time"

	CarpoolOpenApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolOpenApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/tidwall/gjson"
)

// 时间常量
const (
	SecondOfOneDay                           = 24 * 60 * 60
	AheadOfTimeOrderWithVersionControlReveal = "ahead_of_time_order_with_version_control_reveal"
)

type CarpoolBookingData interface {
	GetStationInventory() []*CarpoolOpenApi.StationInventory
	IsStationScan() bool
	ValidScanCode() bool
	GetAppointmentRange() int
	GetPageType() int32
	GetApolloParams(func(full *biz_runtime.ProductInfoFull) string, ...func(full *biz_runtime.ProductInfoFull) (string, string)) (string, map[string]string)
	GetScene() string
	GetPreBusServiceShiftId() string
}

func CarpoolBooking(ctx context.Context, prov CarpoolBookingData) (*proto.InterCityCarpoolBookingModule, map[string]int32) {
	config := dcmp.GetDcmpContent(ctx, "intercity_station-carpool_booking", nil)
	res := &proto.InterCityCarpoolBookingModule{
		Title:                  gjson.Get(config, "title").String(),
		Icon:                   gjson.Get(config, "icon").String(),
		FirstSpanFit:           false,
		ButtonText:             gjson.Get(config, "button_text").String(),
		TimeSpan:               []*proto.InterCityTimeSpan{},
		NoDisplayBookingSelect: prov.ValidScanCode(),
	}

	apolloKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	params["page_type"] = fmt.Sprint(prov.GetPageType())
	if apollo.FeatureToggle(ctx, AheadOfTimeOrderWithVersionControlReveal, apolloKey, params) {
		res.FirstSpanFit = true
	}

	if len(prov.GetStationInventory()) == 0 {
		return nil, nil
	}
	skuDescConfig := gjson.Get(config, "sku_desc").String()
	msgConfig := gjson.Get(config, "msg").String()
	interCityTimeSpan, remainSeatsMap := initTimeSpan(ctx, prov.GetStationInventory(), prov.IsStationScan(), prov.GetAppointmentRange())
	for _, item := range interCityTimeSpan {
		for _, v := range item.Range {
			v.Msg = dcmp.TranslateTemplate(msgConfig, map[string]string{
				"time": v.OuterMsg,
			})
			remainSeat := int(remainSeatsMap[v.Value])
			if remainSeat >= 10 {
				v.SkuDesc = gjson.Get(config, "sku_desc_enough").String()
			} else {
				v.SkuDesc = dcmp.TranslateTemplate(skuDescConfig, map[string]string{
					"seat": strconv.Itoa(remainSeat),
				})
			}
		}
	}
	if len(interCityTimeSpan) > 0 {
		if len(interCityTimeSpan[0].Range) == 0 {
			interCityTimeSpan = interCityTimeSpan[1:]
		}
	}
	res.TimeSpan = interCityTimeSpan
	return res, remainSeatsMap
}

func initTimeSpan(ctx context.Context, stationInventory []*CarpoolOpenApi.StationInventory, isStationScan bool, openAppointmentDays int) ([]*proto.InterCityTimeSpan, map[string]int32) {
	dayTime := time.Now()
	interCityTimeSpan := []*proto.InterCityTimeSpan{}
	// 三天
	day := map[int]string{
		0: "今天",
		1: "明天",
		2: "后天",
	}
	for i := 0; i <= openAppointmentDays; i++ {
		interCityTimeSpan = append(interCityTimeSpan, &proto.InterCityTimeSpan{
			Range: []*proto.InterCityTimeRange{},
			Day:   dayTime.Format("1月2日"),
		})
		if i < len(day) {
			interCityTimeSpan[i].Date = day[i]
		}
		dayTime = time.Unix(dayTime.Unix()+SecondOfOneDay, 0)
	}
	remainSeatsMap := map[string]int32{}
	dayTimeStamp := time.Now().Unix()
	nowLastTimeStamp := util.GetNowLastTimeStamp()
	for i := 0; i <= openAppointmentDays; i++ {
		for _, v := range stationInventory {
			if v == nil {
				continue
			}
			if v.DepartureTime < nowLastTimeStamp && (isStationScan || v.DepartureTime >= dayTimeStamp) {
				available := false
				if v.RemainSeats > 0 && v.Status > 0 {
					available = true
				}
				remainSeatsMap[strconv.Itoa(int(v.DepartureTime))] = v.RemainSeats
				interCityTimeSpan[i].Range = append(interCityTimeSpan[i].Range, &proto.InterCityTimeRange{
					Value:             strconv.Itoa(int(v.DepartureTime)),
					OuterMsg:          time.Unix(v.DepartureTime, 0).Format("15:04"),
					OrderType:         1,
					Available:         available,
					BusServiceShiftId: v.BusServiceShiftID, // 大巴车发单前展示退票规则需要绑定班次id，所以需要在这里加上
				})
				isStationScan = false
			}
		}
		dayTimeStamp = nowLastTimeStamp
		nowLastTimeStamp += SecondOfOneDay

	}

	// 过滤空时间片
	interCityTimeSpanRes := []*proto.InterCityTimeSpan{}
	for i := 0; i < len(interCityTimeSpan); i++ {
		if len(interCityTimeSpan[i].Range) > 0 {
			interCityTimeSpanRes = append(interCityTimeSpanRes, interCityTimeSpan[i])
		}
	}

	return interCityTimeSpanRes, remainSeatsMap
}
