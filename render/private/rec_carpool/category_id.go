package rec_carpool

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
)

type CategoryIDProvider interface {
	GetSeatNumString() string
	GetProductCategoryString() string
}

func CategoryID(ctx context.Context, prov CategoryIDProvider) int32 {
	idString := dcmp.GetJSONContentWithPath(
		ctx,
		"rec_carpool-category_id",
		nil,
		prov.GetProductCategoryString()+"_"+prov.GetSeatNumString())

	id, _ := strconv.Atoi(idString)
	return int32(id)
}
