package rec_carpool

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
)

type InterMsgProvider interface {
	GetSeatNumString() string
	GetProductCategoryString() string
}

func IntroMsg(ctx context.Context, prov InterMsgProvider, isNewStyle bool) string {
	key := "rec_carpool-intro_msg"
	if isNewStyle {
		key = "rec_carpool-intro_msg_new"
	}

	return dcmp.GetJSONContentWithPath(
		ctx,
		key,
		nil,
		prov.GetProductCategoryString()+"_"+prov.GetSeatNumString())
}
