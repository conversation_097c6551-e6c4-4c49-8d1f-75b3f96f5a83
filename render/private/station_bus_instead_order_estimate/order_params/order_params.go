package order_params

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"

	"github.com/tidwall/gjson"
)

type orderParams struct {
	product *biz_runtime.ProductInfoFull
	text    map[string]gjson.Result
}

// NewOrderParams ...
func NewOrderParams(product *biz_runtime.ProductInfoFull, text map[string]gjson.Result) *orderParams {
	return &orderParams{
		product: product,
		text:    text,
	}
}

// Render ...
func (rc *orderParams) Render(ctx context.Context) *proto.BusOrderParams {
	return rc.getOrderParams(ctx)
}

// getOrderParams ...
func (rc *orderParams) getOrderParams(ctx context.Context) *proto.BusOrderParams {
	if rc.product == nil || rc.product.Product == nil || rc.product.GetBizInfo() == nil {
		return nil
	}

	return &proto.BusOrderParams{
		BusServiceShiftId: rc.product.Product.ShiftID,
		DepartureTime:     util.Int642String(rc.product.GetBizInfo().DepartureTime),
		ComboId:           int32(rc.product.GetBizInfo().ComboID),
	}
}
