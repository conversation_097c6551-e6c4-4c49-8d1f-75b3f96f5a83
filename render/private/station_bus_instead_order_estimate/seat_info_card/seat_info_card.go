package seat_info_card

import (
	"context"

	Dirpc_SDK_Brick "git.xiaojukeji.com/dirpc/dirpc-go-http-Brick"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"

	"github.com/tidwall/gjson"
)

type seatInfoCard struct {
	*seatInfoBase

	ruleInfoMap map[int64]*Dirpc_SDK_Brick.TypePercentList
}

// NewSeatInfoCard ...
func NewSeatInfoCard(product *biz_runtime.ProductInfoFull, text map[string]gjson.Result) *seatInfoCard {
	return &seatInfoCard{
		seatInfoBase: newSeatInfoBase(product, text),
	}
}

func (sic *seatInfoCard) Render(ctx context.Context) *proto.BusSeatInfoCard {
	return sic.getSeatInfoCard(ctx)
}

// getSeatInfoCard ...
func (sic *seatInfoCard) getSeatInfoCard(ctx context.Context) *proto.BusSeatInfoCard {
	if sic.text == nil {
		return nil
	}

	sic.buildSupportTicketType(ctx)
	sic.buildSeatRules(ctx)
	return sic.buildSeatInfoCard(ctx)
}

// buildSeatRules ...
func (sic *seatInfoCard) buildSeatRules(ctx context.Context) {
	if sic.product.BaseReqData == nil || sic.product.BaseReqData.CommonBizInfo.IdentityPageInfo == nil {
		return
	}

	var (
		ruleInfoMap = make(map[int64]*Dirpc_SDK_Brick.TypePercentList)
	)

	ruleInfo := sic.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo
	if ruleInfo == nil {
		return
	}

	for _, rule := range ruleInfo.SpecialSeatRules {
		if rule == nil {
			continue
		}

		if len(rule.TypePercent) > 0 {
			for _, typePercent := range rule.TypePercent {
				if typePercent == nil {
					continue
				}

				ruleInfoMap[typePercent.Type] = typePercent
			}
		}
	}

	sic.ruleInfoMap = ruleInfoMap
}

// buildSeatLimit ...
func (sic *seatInfoCard) buildSeatInfoCard(ctx context.Context) *proto.BusSeatInfoCard {

	return &proto.BusSeatInfoCard{
		PassengerList:              sic.getBusPassengerInfo(ctx),
		SupportTicketTypeInfo:      sic.getSupportTicketTypeInfo(),
		MaxInventory:               sic.getMaxInventory(),
		CarryChildrenMaxInventory:  sic.getCarryChildMaxInventory(),
		CarryChildrenLimit:         sic.buildCarryChildrenLimit(ctx),
		PassengerLimit:             sic.buildPassengerLimit(ctx),
		RealNameCarryChildrenTitle: sic.buildRealNameCarryChildrenTitle(ctx),
		CouponTicketBubbleText:     util.String2PtrString(sic.text["coupon_ticket_bubble_text"].String()),
	}
}
