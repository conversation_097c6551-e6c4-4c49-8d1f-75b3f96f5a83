package seat_info_card

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

// buildCarryChildrenLimit ... 库存限制
func (sib *seatInfoBase) buildCarryChildrenLimit(ctx context.Context) *proto.BusRuleLimit {
	if _, support := sib.supportTicketTypes[seat_selection_consts.CarryChildren.ToInt32()]; !support {
		return nil
	}

	if sib.text == nil || sib.product == nil || sib.product.BaseReqData == nil {
		return nil
	}

	// 携童无上限或者小于0
	if sib.getCarryChildMaxInventory() == seat_selection_consts.NoLimit ||
		sib.getCarryChildMaxInventory() < 0 {
		return nil
	}

	toastTemplate := sib.text["seat_info"].Map()["carry_children_limit_toast"].String()
	if sib.getCarryChildMaxInventory() == 0 {
		toastTemplate = sib.text["seat_info"].Map()["carry_children_zero_limit_toast"].String()
	}

	return &proto.BusRuleLimit{
		MaxInventory: sib.getCarryChildMaxInventory(),
		Toast: util.ReplaceTag(ctx, toastTemplate, map[string]string{
			"num": util.Int32String(sib.getCarryChildMaxInventory()),
		}),
		TicketTypes: sib.buildCarryChildrenTicketTypes(ctx),
	}
}

// buildPassengerLimit ... 库存限制
func (sib *seatInfoBase) buildPassengerLimit(ctx context.Context) *proto.BusRuleLimit {
	if sib.text == nil || sib.product == nil || sib.product.BaseReqData == nil {
		return nil
	}
	return &proto.BusRuleLimit{
		MaxInventory: sib.getMaxInventory(),
		Toast: util.ReplaceTag(ctx, sib.text["seat_info"].Map()["passenger_limit_toast"].String(), map[string]string{
			"num": util.Int32String(sib.getMaxInventory()),
		}),
		TicketTypes: sib.buildPassengerTicketTypes(ctx),
	}
}
func (sib *seatInfoBase) getCarryChildMaxInventory() int32 {
	bizInfo := sib.product.GetBizInfo()
	if bizInfo == nil {
		return 0
	}

	if bizInfo.StationInventoryInfo != nil {
		return bizInfo.StationInventoryInfo.SelectInfo.CarryChildrenMaxInventory
	}

	return 0
}

func (sib *seatInfoBase) getMaxInventory() int32 {
	bizInfo := sib.product.GetBizInfo()
	if bizInfo == nil {
		return 0
	}
	return sib.product.Product.BizInfo.MaxCarpoolSeatNum
}

// 获取票类型是否支持
func (sib *seatInfoBase) getSupportTicketTypeInfo() *proto.SupportTicketTypeInfo {
	supportTicketTypeInfo := new(proto.SupportTicketTypeInfo)
	if _, support := sib.supportTicketTypes[seat_selection_consts.Adult.ToInt32()]; support {
		supportTicketTypeInfo.Adult = 1
	}
	if _, support := sib.supportTicketTypes[seat_selection_consts.Children.ToInt32()]; support {
		supportTicketTypeInfo.Children = 1
	}
	if _, support := sib.supportTicketTypes[seat_selection_consts.CarryChildren.ToInt32()]; support {
		supportTicketTypeInfo.CarryChildren = 1
	}
	if _, support := sib.supportTicketTypes[seat_selection_consts.PreferentialPeople.ToInt32()]; support {
		supportTicketTypeInfo.CouponTicket = 1
	}
	return supportTicketTypeInfo
}

// buildCarryChildrenTicketTypes ...
func (sib *seatInfoBase) buildCarryChildrenTicketTypes(ctx context.Context) []int32 {
	ticketTypes := make([]int32, 0)

	// 携童有上限
	ticketTypes = append(ticketTypes, seat_selection_consts.CarryChildren.ToInt32())

	return ticketTypes
}

// buildPassengerTicketTypes
func (sib *seatInfoBase) buildPassengerTicketTypes(ctx context.Context) []int32 {
	ticketTypes := make([]int32, 0)

	for ticketType, _ := range sib.supportTicketTypes {
		// 携童不占座情况，愚蠢的逻辑
		if ticketType == seat_selection_consts.CarryChildren.ToInt32() && sib.product.GetBizInfo().CarryChildrenIsOccupySeat != 1 {
			continue
		}

		ticketTypes = append(ticketTypes, ticketType)
	}

	return ticketTypes
}
