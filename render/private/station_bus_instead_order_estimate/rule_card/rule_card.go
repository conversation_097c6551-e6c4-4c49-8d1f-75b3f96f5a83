package rule_card

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/tidwall/gjson"
)

type ruleCard struct {
	product *biz_runtime.ProductInfoFull
	text    map[string]gjson.Result
}
type supportInfo struct {
	supportChildrenTicket      bool
	supportHomeOwnerTicket     bool
	supportCarryChildrenTicket bool
	CarryChildrenOccupySeat    bool
	supportCouponTicket        bool // 是否支持优待票
}

// NewRuleCard ...
func NewRuleCard(product *biz_runtime.ProductInfoFull, text map[string]gjson.Result) *ruleCard {
	return &ruleCard{
		product: product,
		text:    text,
	}
}

func (rc *ruleCard) Render(ctx context.Context) *proto.BusRuleCard {
	return rc.getRuleCard(ctx)
}

// GetHeadCard ...
func (rc *ruleCard) getRuleCard(ctx context.Context) *proto.BusRuleCard {
	if rc.text == nil {
		return nil
	}

	return &proto.BusRuleCard{RuleList: rc.buildRuleList(ctx)}
}

// buildRuleList ...
func (rc *ruleCard) buildRuleList(ctx context.Context) []string {
	if rc.text == nil || rc.product == nil || rc.product.BaseReqData == nil || rc.product.GetBizInfo() == nil || rc.product.GetBizInfo().RouteDetailV2 == nil {
		return nil
	}

	var (
		conf map[string]gjson.Result
		key  string
	)

	routeDetail := rc.product.GetBizInfo().RouteDetailV2
	if routeDetail.IsNeedVerified() {
		conf = rc.text["real_name_rule_list"].Map()
	} else {
		conf = rc.text["no_real_name_rule_list"].Map()
	}

	supportItem := rc.getSupportTicket()

	if supportItem.supportChildrenTicket {
		key += intercity_estimate_detail.SupportChildTicket
		// 有无携童票 是否占座
		if supportItem.supportCarryChildrenTicket {
			key += intercity_estimate_detail.SupportCarryChildTicket
			if supportItem.CarryChildrenOccupySeat {
				key += intercity_estimate_detail.WithOccupy
			} else {
				key += intercity_estimate_detail.WithoutOccupy
			}
		} else {
			key += intercity_estimate_detail.NoSupportCarryChildTicket
		}
	} else {
		key += intercity_estimate_detail.NoSupportChildTicket
	}
	// 支持优待票
	if supportItem.supportCouponTicket {
		key += intercity_estimate_detail.SupportCouponTicket
	}

	ruleList := conf[key].Array()
	resRuleList := make([]string, 0)

	for _, ruleItem := range ruleList {
		if ruleItem.Exists() {
			resRuleList = append(resRuleList, ruleItem.String())
		}
	}

	return resRuleList
}

// getSupportTicket ...
func (rc *ruleCard) getSupportTicket() *supportInfo {
	var supportItem = supportInfo{}

	if rc.product == nil ||
		rc.product.BaseReqData == nil ||
		rc.product.BaseReqData.CommonBizInfo.IdentityPageInfo == nil ||
		rc.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo == nil {
		return &supportItem
	}

	routeDetail := rc.product.GetBizInfo().RouteDetailV2

	for _, rule := range rc.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo.SpecialSeatRules {
		if rule.RuleName == seat_selection_consts.ChildTicket && rule.IsSupport == 1 {
			supportItem.supportChildrenTicket = true
			// 兼容老携童票逻辑
			for _, childTicket := range rule.GetTypePercent() {
				if childTicket.GetType() == seat_selection_consts.CarryChildren.ToInt64() {
					supportItem.supportCarryChildrenTicket = true
					break
				}
			}

			if supportItem.supportChildrenTicket && routeDetail != nil {
				supportItem.CarryChildrenOccupySeat = routeDetail.IsChildOccupy()
			}
		}

		// 新携童票逻辑
		if rule.RuleName == seat_selection_consts.CarryChildTicket && rule.IsSupport == 1 {
			supportItem.supportCarryChildrenTicket = true
			if rule.OccupySeat == 1 {
				supportItem.CarryChildrenOccupySeat = true
			}
		}
		// 支持优待票
		if rule.RuleName == seat_selection_consts.CouponTicket && rule.IsSupport == 1 {
			supportItem.supportCouponTicket = true
		}
	}

	return &supportItem
}
