package intercity_multi_station

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/tidwall/gjson"
)

const (
	DefaultPageTitle = "滴滴站点巴士"
)

type ScreenDataText struct {
	AllTime     string   `json:"all_time"`
	DayMap      []string `json:"day_map"`
	DayName     []string `json:"day_name"`
	WeekName    []string `json:"week_name"`
	StationName string   `json:"station_name"`
}

func Title(cbi models.CommonBizInfo) string {
	return cbi.StationListV2.FromName + "-" + cbi.StationListV2.ToName
}

// 页面标题，不可或缺
func PageTitle(ctx context.Context) string {
	pageInfoConfig := dcmp.GetDcmpPlainContent(ctx, "intercity_station-page_info")
	if len(pageInfoConfig) == 0 {
		return DefaultPageTitle
	}
	pageInfoMap := make(map[string]string)
	err := json.Unmarshal([]byte(pageInfoConfig), &pageInfoMap)
	if err != nil {
		return DefaultPageTitle
	}
	return pageInfoMap["page_title"]
}

func ScreenData(ctx context.Context, cbi models.CommonBizInfo, isEstimateV2 bool) *proto.ScreenData {
	res := &proto.ScreenData{}
	stationInfo := cbi.StationInfo
	var selectTime int64
	var screenDataText *ScreenDataText
	if err := json.Unmarshal([]byte(dcmp.GetDcmpPlainContent(ctx, "intercity_station-screen_data")), &screenDataText); err != nil {
		log.Trace.Warnf(ctx, "dcmp intercity_station-screen_data", "unmarshalerror: %v", err)
		return nil
	}
	res.Day, selectTime = initDayTime(ctx, stationInfo.DayTime, screenDataText, cbi.DatesCouponInfo, isEstimateV2)
	// todo 发车时段  选上车站  选下车站
	res.BeginStationV2 = initStationListV2(cbi.StationListV2.FromCounties, stationInfo.StartCountyId, stationInfo.StartStationId, screenDataText, cbi.StationListV2.FromCityName)
	res.EndStationV2 = initStationListV2(cbi.StationListV2.ToCounties, stationInfo.EndCountyId, stationInfo.EndStationId, screenDataText, cbi.StationListV2.ToCityName)
	res.StationTimeV2 = initStationTimeV2(stationInfo.StartTime, stationInfo.EndTime, selectTime, screenDataText)
	return res
}

func initStationTimeV2(s int32, e int32, n int64, screenDataText *ScreenDataText) *proto.StationTime {

	res := &proto.StationTime{
		CheckedId: 0,
		TimeList:  []*proto.IntercityTime{},
	}
	len := int64(6 * 60 * 60)
	res.TimeList = append(res.TimeList, &proto.IntercityTime{
		Name:      screenDataText.AllTime,
		StartTime: 0,
		EndTime:   4 * len,
	})
	for i := 0; i < 4; i++ {
		res.TimeList = append(res.TimeList, &proto.IntercityTime{
			Name:      screenDataText.DayMap[i],
			StartTime: int64(i) * len,
			EndTime:   int64(i)*len + len,
		})
		if int64(s) == n && int64(e) == n+len {
			res.CheckedId = int32(i) + 1
		}
		n = n + len
	}
	return res
}

func initDayTime(ctx context.Context, dayTime int32, screenDataText *ScreenDataText, couponInfo map[int64]bool, isEstimateV2 bool) (*proto.Day, int64) {

	res := &proto.Day{
		DayList:      []*proto.DayList{},
		CheckedIndex: 0,
	}
	baseTime := util.GetNowEarlyTimeStamp()
	resTime := baseTime
	discountConf := dcmp.GetDcmpPlainContent(ctx, "intercity_station-discount")
	for i := 0; i < 14; i++ {
		name := screenDataText.WeekName[int(time.Unix(baseTime, 0).Weekday())]
		if len(screenDataText.DayName) > i && screenDataText.DayName[i] != "" {
			name = screenDataText.DayName[i]
		}
		if baseTime == int64(dayTime) {
			res.CheckedIndex = int32(i)
			resTime = int64(dayTime)
		}
		dItem := &proto.DayList{
			Name: name,
			Time: baseTime,
			Date: time.Unix(baseTime, 0).Format("1.2"),
		}
		if !isEstimateV2 {
			if couponInfo != nil && couponInfo[baseTime] {
				dItem.Text = discountConf
			}
		}
		res.DayList = append(res.DayList, dItem)
		baseTime = baseTime + 24*60*60
	}
	return res, resTime
}

func initStationListV2(countyInfos []*Prfs.County, countyID int32, stationId int64, screenDataText *ScreenDataText, cityName string) *proto.StationDataV2 {
	res := proto.StationDataV2{
		Counties:        []*proto.IntercityCounty{},
		CheckedId:       int32(stationId),
		CountyCheckedId: countyID,
		CityName:        cityName,
	}
	baseStation := &proto.IntercityStation{
		Name: screenDataText.StationName,
	}
	res.Counties = append(res.Counties, &proto.IntercityCounty{
		Name:     dcmp.GetDcmpPlainContent(context.Background(), "intercity_estimate-all_counties"),
		Stations: []*proto.IntercityStation{baseStation},
	})
	for _, v := range countyInfos {
		county := &proto.IntercityCounty{
			Name:     v.Name, // 区县名
			CountyId: int64(v.Countyid),
			Stations: []*proto.IntercityStation{
				{
					Name: v.Name + screenDataText.StationName,
				},
			},
		}
		var selectableStations []*proto.IntercityStation
		for _, w := range v.Stations {
			if util.InArrayInt32(w.Status, []int32{0, 1}) {
				continue
			} else {
				selectableStations = append(selectableStations, &proto.IntercityStation{
					Name:      w.Name,
					StationId: int64(w.Stationid),
					Lat:       strconv.FormatFloat(w.Lat, 'f', 1, 64),
					Lng:       strconv.FormatFloat(w.Lng, 'f', 1, 64),
					ImgUrl:    generateImgUrl(context.Background(), w.ImageUrls),
				})
			}
		}
		if len(selectableStations) == 0 {
			continue
		}
		county.Stations = append(county.Stations, selectableStations...)
		res.Counties = append(res.Counties, county)
	}

	return &res
}

func DisabledInfo(ctx context.Context, products []*biz_runtime.ProductInfoFull) *proto.DisabledInfo {

	if len(products) == 0 {
		disabledInfoTemplate := dcmp.GetDcmpPlainContent(ctx, "intercity_station-disabledinfo")
		return &proto.DisabledInfo{
			Icon:    gjson.Get(disabledInfoTemplate, "icon").String(),
			Content: gjson.Get(disabledInfoTemplate, "content").String(),
		}
	}

	return nil
}
func generateImgUrl(ctx context.Context, imgUrls []string) string {
	config := dcmp.GetDcmpContent(ctx, "intercity_station-img_url", nil)
	imgUrl := gjson.Get(config, "base_url").String()
	factor := gjson.Get(config, "resize_factor_w").Int()
	if len(imgUrls) > 0 {
		imgUrl = imgUrls[0]
	}

	if factor == 0 {
		return imgUrl
	}
	return fmt.Sprintf("%s?x-s3-process=image/resize,w_%d", imgUrl, factor)
}
