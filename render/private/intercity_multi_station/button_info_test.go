package intercity_multi_station

import (
	"context"
	"encoding/json"
	"fmt"
	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/conf"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/station_bus_data"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/ability/page_navigation/dfa"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"path/filepath"
	"runtime"
	"testing"
)

func TestMain(m *testing.M) {
	_, filename, _, ok := runtime.Caller(0)
	if !ok {
		fmt.Println("无法获取当前文件路径")
		return
	}
	dir := filepath.Dir(filepath.Dir(filepath.Dir(filename)))
	confPath := filepath.Join(dir, "../conf/app_dev.toml")
	fmt.Printf("confPath is %s\n", confPath)
	conf.InitConf(confPath)
	log.Init()
	m.Run()
}

type MockMyInterface interface {
	GetIsDRN() bool
}

type Prov struct {
}

func (t *Prov) GetCarryChildMaxInventory(ctx context.Context) int32 {
	return 1
}

func (t *Prov) GetSelectInfo() *models.StationInventorySelectInfo {
	//TODO implement me
	panic("implement me")
}

func (t *Prov) GetAccessKeyID() int {
	return 1
}

func (t *Prov) GetEstimateID() string {
	return "12345"
}

func (t *Prov) GetDchn() string {
	return "xD86F"
}

func (t *Prov) GetCarpoolSeatMaxNum() int32 {
	return 5
}

func (t *Prov) GetAppVersion() string {
	return "7.0.0"
}

func (t *Prov) GetStationInfo() models.StationInfo {
	//TODO implement me
	panic("implement me")
}

func (t *Prov) GetIsDRN() bool {
	return false
}

func TestGetJumpLinkUrl(t *testing.T) {
	mockey.PatchConvey("hit new process", t, func() {
		pid := 17596678416188
		req := &proto.IntercityMultiEstimateRequest{
			AccessKeyId: 1,
			AppVersion:  "7.0.0",
			StartCity:   1,
		}

		selectInfo := &models.StationInventorySelectInfo{
			RouteId:  123,
			SrcCost:  nil,
			DestCost: nil,
			FromStationInfo: &Prfs.StationInfo{
				FenceId: "1",
				GroupId: "1",
			},
			DestStationInfo: &Prfs.StationInfo{
				FenceId: "1",
				GroupId: "1",
			},
			FromStationId: 1,
			DestStationId: 2,
			ShiftID:       "123",
		}

		stationInfo := &models.StationInfo{
			StartStationId: 1,
			StartCity:      1,
			EndStationId:   2,
			EndCity:        2,
		}

		stationList := &models.StationListV2{
			FromCityName: "北京",
			ToCityName:   "北京",
		}

		mockey.Mock(getJumpDivertMark).Return(true).Build()

		mockey.Mock(dcmp.GetDcmpPlainContent).Return(nil).Build()

		tmp := Prov{}

		mockey.Mock(tmp.GetIsDRN).Return(false).Build()

		test := getJumpLinkUrl(nil, int64(pid), req, selectInfo, "", &tmp, *stationInfo, *stationList, true)

		res := &station_bus_data.JumpInfo{
			NewJumpMark: true,
			LinkType:    0,
			LinkUrl:     "",
			LinkParams: map[string]string{
				"bus_service_shift_id": "123", "dchn": "xD86F", "end_fence_id": "1", "end_group_id": "1", "end_poi_city": "2", "end_poi_city_name": "%E5%8C%97%E4%BA%AC", "estimate_id": "12345", "from_page_type": "1", "origin_end_station_id": "2", "origin_start_station_id": "1", "route_id": "123", "selected_end_station_id": "2", "selected_start_station_id": "1", "start_city_id": "1", "start_fence_id": "1", "start_group_id": "1", "start_poi_city": "1", "start_poi_city_name": "%E5%8C%97%E4%BA%AC", "support_divert": "1",
			},
		}

		convey.So(res, convey.ShouldEqual, test)
	})

	mockey.PatchConvey("hit old process", t, func() {
		pid := 17596678416188
		req := &proto.IntercityMultiEstimateRequest{
			AccessKeyId: 1,
			AppVersion:  "7.0.0",
			StartCity:   1,
		}

		selectInfo := &models.StationInventorySelectInfo{
			RouteId:  123,
			SrcCost:  nil,
			DestCost: nil,
			FromStationInfo: &Prfs.StationInfo{
				FenceId: "1",
				GroupId: "1",
			},
			DestStationInfo: &Prfs.StationInfo{
				FenceId: "1",
				GroupId: "1",
			},
			FromStationId: 1,
			DestStationId: 2,
			ShiftID:       "123",
		}

		stationInfo := &models.StationInfo{
			StartStationId: 1,
			StartCity:      1,
			EndStationId:   2,
			EndCity:        2,
		}

		stationList := &models.StationListV2{
			FromCityName: "北京",
			ToCityName:   "北京",
		}

		mockey.Mock(getJumpDivertMark).Return(false).Build()

		mockey.Mock(dcmp.GetDcmpPlainContent).Return(nil).Build()

		tmp := Prov{}

		mockey.Mock(tmp.GetIsDRN).Return(false).Build()

		test := getJumpLinkUrl(nil, int64(pid), req, selectInfo, "", &tmp, *stationInfo, *stationList, true)

		res := &station_bus_data.JumpInfo{
			NewJumpMark: false,
			LinkType:    2,
			LinkUrl:     "",
			LinkParams:  nil,
		}

		convey.So(res, convey.ShouldEqual, test)
	})

	mockey.PatchConvey("hit dfa error", t, func() {
		pid := 17596678416188
		req := &proto.IntercityMultiEstimateRequest{
			AccessKeyId: 1,
			AppVersion:  "7.0.0",
			StartCity:   1,
		}

		selectInfo := &models.StationInventorySelectInfo{
			RouteId:  123,
			SrcCost:  nil,
			DestCost: nil,
			FromStationInfo: &Prfs.StationInfo{
				FenceId: "1",
				GroupId: "1",
			},
			DestStationInfo: &Prfs.StationInfo{
				FenceId: "1",
				GroupId: "1",
			},
			FromStationId: 1,
			DestStationId: 2,
			ShiftID:       "123",
		}

		stationInfo := &models.StationInfo{
			StartStationId: 1,
			StartCity:      1,
			EndStationId:   2,
			EndCity:        2,
		}

		stationList := &models.StationListV2{
			FromCityName: "北京",
			ToCityName:   "北京",
		}

		mockey.Mock(getJumpDivertMark).Return(true).Build()

		mockey.Mock(dcmp.GetDcmpPlainContent).Return(nil).Build()

		mockey.Mock(dfa.DFA).Return(nil).Build()

		mockey.Mock(json.Unmarshal).Return(nil).Build()

		tmp := Prov{}

		mockey.Mock(tmp.GetIsDRN).Return(false).Build()

		test := getJumpLinkUrl(nil, int64(pid), req, selectInfo, "", &tmp, *stationInfo, *stationList, true)

		res := &station_bus_data.JumpInfo{
			NewJumpMark: false,
			LinkType:    2,
			LinkUrl:     "",
			LinkParams:  nil,
		}

		convey.So(res, convey.ShouldEqual, test)
	})

	mockey.PatchConvey("hit getJumpDivertMark", t, func() {

		mockey.Mock(apollo.FeatureToggle).Return(true).Build()
		req := &proto.IntercityMultiEstimateRequest{
			AccessKeyId: 1,
			StartCity:   1,
		}
		pre := getJumpDivertMark(nil, 12345, req)
		convey.So(pre, convey.ShouldEqual, true)
	})

	mockey.PatchConvey("hit getJumpDivertMark", t, func() {

		mockey.Mock(apollo.FeatureToggle).Return(false).Build()
		req := &proto.IntercityMultiEstimateRequest{
			AccessKeyId: 1,
			StartCity:   1,
		}
		pre := getJumpDivertMark(nil, 12345, req)
		convey.So(pre, convey.ShouldEqual, false)
	})

}
