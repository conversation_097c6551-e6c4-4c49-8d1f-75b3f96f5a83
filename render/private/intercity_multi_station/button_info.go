package intercity_multi_station

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/constants/page"
	"strconv"

	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/station_bus_data"
	"git.xiaojukeji.com/gulfstream/passenger-common/constant/carpoolstation"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/ability/drn"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/ability/page_navigation/dfa"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/ability/page_navigation/jump_link"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/dto"

	"net/url"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"github.com/tidwall/gjson"
)

const (
	FenceToDoor     = 1
	InvalidLatOrLng = 0
	InvalidStation  = 0
	SupportDivert   = 1
)

type ButtonInfoProv interface {
	GetSelectInfo() *models.StationInventorySelectInfo
	GetAccessKeyID() int
	GetEstimateID() string
	GetDchn() string
	GetCarpoolSeatMaxNum() int32
	GetCarryChildMaxInventory(context.Context) int32
	GetAppVersion() string
	GetStationInfo() models.StationInfo
	GetIsDRN() bool
}

func ButtonInfo(ctx context.Context, prov ButtonInfoProv, stationInfo models.StationInfo, passengerID int64, isRecommend bool, req *proto.IntercityMultiEstimateRequest, stationListV2 models.StationListV2) *proto.ButtonInfo {
	buttonInfoTemplate := dcmp.GetDcmpPlainContent(ctx, "intercity_station-buttoninfo")
	selectInfo := prov.GetSelectInfo()
	if selectInfo == nil || selectInfo.FromStationInfo == nil || selectInfo.DestStationInfo == nil {
		return nil
	}
	typeInt := int32(2)
	if selectInfo.RemainSeats > 0 {
		typeInt = 1
	}
	// 获取跳转链接
	jumpInfo := getButtonLinkUrlAndType(ctx, buttonInfoTemplate, prov, stationInfo, passengerID, isRecommend, req, stationListV2)
	return &proto.ButtonInfo{
		Text:        gjson.Get(buttonInfoTemplate, "text").String(),
		LinkType:    int32(jumpInfo.LinkType),
		Type:        typeInt,
		LinkUrl:     jumpInfo.LinkUrl,
		NewJumpMark: jumpInfo.NewJumpMark,
		LinkParams:  jumpInfo.LinkParams,
	}
}

// getButtonLinkUrlAndType 获取按钮点击后跳转链接
func getButtonLinkUrlAndType(ctx context.Context, buttonInfoTemplate string, prov ButtonInfoProv, stationInfo models.StationInfo, passengerID int64, isRecommend bool, req *proto.IntercityMultiEstimateRequest, stationListV2 models.StationListV2) *station_bus_data.JumpInfo {
	selectInfo := prov.GetSelectInfo()

	// 获取guide_trace_id (导流位trace id透传)
	transData := station_bus_data.BusTransData{}
	var guideTraceID string
	if req.TransData != nil {
		err := json.Unmarshal([]byte(*req.TransData), &transData)
		if err == nil {
			guideTraceID = transData.GuideTraceId
		}
	}

	res := getJumpLinkUrl(ctx, passengerID, req, selectInfo, buttonInfoTemplate, prov, stationInfo, stationListV2, isRecommend)
	// 如果有guide_trace_id 拼接
	if guideTraceID != "" {
		res.LinkUrl = res.LinkUrl + "&guide_trace_id=" + guideTraceID
	}
	return res
}

func getOldJumpLinkUrl(ctx context.Context, passengerID int64, buttonInfoTemplate string, prov ButtonInfoProv, stationListV2 models.StationListV2, selectInfo *models.StationInventorySelectInfo, stationInfo models.StationInfo, req *proto.IntercityMultiEstimateRequest, isRecommend bool) (string, int) {
	var linkUrl string
	var linkType int
	if hitFenceToDoor(selectInfo) {
		if fenceToDoorJumpPurchase(selectInfo, stationInfo, req) {
			linkUrl, linkType = genFencePurchaseLink(ctx, buttonInfoTemplate, prov, linkUrl, stationInfo)
		} else {
			linkUrl, linkType = genFenceBusSelectPointLink(linkUrl, buttonInfoTemplate, selectInfo, stationInfo, prov, req, stationListV2, linkType)
		}
		return linkUrl, linkType
	}
	if (isRecommend || stationInfo.StartStationId == 0 || stationInfo.EndStationId == 0) && isHitSelectStationPointPageSwitch(ctx, passengerID, prov, stationInfo) {
		linkUrl, linkType = genSelectPointLink(linkUrl, buttonInfoTemplate, selectInfo, stationInfo, prov, req, stationListV2, linkType)
	} else {
		linkUrl, linkType = genPurchaseLink(ctx, buttonInfoTemplate, prov, linkUrl, selectInfo, stationInfo)
	}
	return linkUrl, linkType
}

func genAllStationInfo(selectInfo *models.StationInventorySelectInfo, stationInfo *models.StationInfo) *dto.AllStationInfo {
	allStationInfo := &dto.AllStationInfo{}

	// duse推荐出来的班次
	if selectInfo != nil {
		allStationInfo.FromStationID = selectInfo.FromStationId
		allStationInfo.ToStationID = selectInfo.DestStationId
		allStationInfo.FromFenceID = cast.ToInt(selectInfo.FromStationInfo.FenceId)
		allStationInfo.FromGroupID = cast.ToInt(selectInfo.FromStationInfo.GroupId)
		allStationInfo.ToFenceID = cast.ToInt(selectInfo.DestStationInfo.FenceId)
		allStationInfo.ToGroupID = cast.ToInt(selectInfo.DestStationInfo.GroupId)
		allStationInfo.ShiftID = selectInfo.ShiftID
	}

	if selectInfo != nil && stationInfo != nil {
		if stationInfo.StartStationId != int64(selectInfo.FromStationId) {
			allStationInfo.FromStationID = InvalidStation
		}
		if stationInfo.EndStationId != int64(selectInfo.DestStationId) {
			allStationInfo.ToStationID = InvalidStation
		}
		if selectInfo.SrcCost != nil && selectInfo.SrcCost.Dist == 0 {
			allStationInfo.FromLng = stationInfo.FromLng
			allStationInfo.FromLat = stationInfo.FromLat
		}
		if selectInfo.DestCost != nil && selectInfo.DestCost.Dist == 0 {
			allStationInfo.ToLng = stationInfo.ToLng
			allStationInfo.ToLat = stationInfo.ToLat
		}
	}

	return allStationInfo
}

func getNewJumpLinkUrl(ctx context.Context, selectInfo *models.StationInventorySelectInfo, stationInfo *models.StationInfo, prov ButtonInfoProv, req *proto.IntercityMultiEstimateRequest) (string, int, *dto.AllStationInfo) {
	var (
		linkUrl  string
		linkType int
		urlSet   *dto.NavigationLink
	)
	allStationInfo := genAllStationInfo(selectInfo, stationInfo)

	linkUrlConfigStr := dcmp.GetDcmpPlainContent(ctx, "intercity_station-page_navigation_link")
	if err := json.Unmarshal([]byte(linkUrlConfigStr), &urlSet); err != nil {
		log.Trace.Warnf(ctx, "genPurchaseLink", "_msg=unmarshal linkUrlConfigStr fail||err=%v", err)
		return "", 0, allStationInfo
	}

	urlKey := drn.ConvertUrlKey(cast.ToString(req.GetAccessKeyId()), prov.GetIsDRN())
	pageName := dfa.DFA(page.CheckShiftInfo, allStationInfo)
	if len(pageName) == 0 {
		log.Trace.Warnf(ctx, "genNextJumpLink", "_msg=gen todo task len is %v", len(pageName))
		return "", 0, allStationInfo
	}

	baseUrl := jump_link.GetNextJumpLink(urlKey, pageName, urlSet)
	linkType = cast.ToInt(drn.GetLinkTypeByPage(pageName, page.CheckShiftInfo, urlKey))
	linkUrl = baseUrl

	return linkUrl, linkType, allStationInfo
}

func genLinkParams(prov ButtonInfoProv, stationInfo models.StationInfo, selectInfo *models.StationInventorySelectInfo, stationListV2 models.StationListV2, allStationInfo *dto.AllStationInfo) map[string]string {
	var params = make(map[string]string)

	if selectInfo != nil {
		if len(selectInfo.ShiftID) > 0 {
			params["bus_service_shift_id"] = selectInfo.ShiftID
		}
		if selectInfo.SrcCost != nil {
			if selectInfo.SrcCost.Dist == 0 && len(stationInfo.StartPoiName) > 0 {
				params["start_poi_name"] = stationInfo.StartPoiName
			}
		}

		if selectInfo.DestCost != nil {
			if selectInfo.DestCost.Dist == 0 && len(stationInfo.EndPoiName) > 0 {
				params["end_poi_name"] = stationInfo.EndPoiName
			}
		}

		if selectInfo.FromStationId != 0 {
			if stationInfo.StartStationId != 0 || len(selectInfo.FromStationInfo.FenceId) > 0 || len(stationInfo.StartPoiName) > 0 {
				params["selected_start_station_id"] = strconv.Itoa(selectInfo.FromStationId)
			}
		}

		if selectInfo.DestStationId != 0 {
			if stationInfo.EndStationId != 0 || len(selectInfo.DestStationInfo.FenceId) > 0 || len(stationInfo.EndPoiName) > 0 {
				params["selected_end_station_id"] = strconv.Itoa(selectInfo.DestStationId)
			}
		}
	}

	if len(prov.GetEstimateID()) > 0 {
		params["estimate_id"] = prov.GetEstimateID()
	}

	if len(prov.GetDchn()) > 0 {
		params["dchn"] = prov.GetDchn()
	}

	if allStationInfo != nil {
		if allStationInfo.FromLat != 0 {
			params["start_lat"] = cast.ToString(allStationInfo.FromLat)
		}

		if allStationInfo.FromLng != 0 {
			params["start_lng"] = cast.ToString(allStationInfo.FromLng)
		}

		if allStationInfo.ToLat != 0 {
			params["end_lat"] = cast.ToString(allStationInfo.ToLat)
		}

		if allStationInfo.ToLng != 0 {
			params["end_lng"] = cast.ToString(allStationInfo.ToLng)
		}

		params["origin_start_station_id"] = cast.ToString(stationInfo.StartStationId)
		params["origin_end_station_id"] = cast.ToString(stationInfo.EndStationId)
	}

	if selectInfo.RouteId != 0 {
		params["route_id"] = util.Int642String(selectInfo.RouteId)
	}

	if stationInfo.StartCity != 0 {
		params["start_city_id"] = util.Int32String(stationInfo.StartCity)
	}

	if len(selectInfo.FromStationInfo.FenceId) > 0 {
		params["start_fence_id"] = selectInfo.FromStationInfo.FenceId
	}

	if len(selectInfo.DestStationInfo.FenceId) > 0 {
		params["end_fence_id"] = selectInfo.DestStationInfo.FenceId
	}

	if len(selectInfo.FromStationInfo.GroupId) > 0 {
		params["start_group_id"] = selectInfo.FromStationInfo.GroupId
	}

	if len(selectInfo.DestStationInfo.GroupId) > 0 {
		params["end_group_id"] = selectInfo.DestStationInfo.GroupId
	}

	if stationInfo.StartCity != 0 {
		params["start_poi_city"] = util.Int32String(stationInfo.StartCity)
	}

	if stationInfo.EndCity != 0 {
		params["end_poi_city"] = util.Int32String(stationInfo.EndCity)
	}

	if len(stationListV2.FromCityName) > 0 {
		params["start_poi_city_name"] = url.PathEscape(stationListV2.FromCityName)
	}

	if len(stationListV2.ToCityName) > 0 {
		params["end_poi_city_name"] = url.PathEscape(stationListV2.ToCityName)
	}

	params["from_page_type"] = util.Int2String(carpoolstation.FromPageTypeSelectStationShift)

	params["support_divert"] = util.Int2String(SupportDivert)

	return params
}

func getJumpLinkUrl(ctx context.Context, pid int64, req *proto.IntercityMultiEstimateRequest, selectInfo *models.StationInventorySelectInfo, buttonInfoTemplate string, prov ButtonInfoProv, stationInfo models.StationInfo, stationListV2 models.StationListV2, isRecommend bool) *station_bus_data.JumpInfo {
	var (
		linkUrl        string
		linkType       int
		allStationInfo = &dto.AllStationInfo{}
	)
	res := &station_bus_data.JumpInfo{
		NewJumpMark: getJumpDivertMark(ctx, pid, req),
	}
	if !res.NewJumpMark {
		linkUrl, linkType = getOldJumpLinkUrl(ctx, pid, buttonInfoTemplate, prov, stationListV2, selectInfo, stationInfo, req, isRecommend)
		res.LinkUrl = linkUrl
		res.LinkType = linkType
		return res
	}
	linkUrl, linkType, allStationInfo = getNewJumpLinkUrl(ctx, selectInfo, &stationInfo, prov, req)
	res.LinkUrl = linkUrl
	res.LinkType = linkType
	res.LinkParams = genLinkParams(prov, stationInfo, selectInfo, stationListV2, allStationInfo)
	return res
}

func getJumpDivertMark(ctx context.Context, passengerID int64, req *proto.IntercityMultiEstimateRequest) bool {
	params := map[string]string{
		"access_key_id": cast.ToString(req.AccessKeyId),
		"app_version":   req.AppVersion,
		"city":          cast.ToString(req.StartCity),
		"scene":         consts.IntercityMutliEstimatePriceCaller,
		"pid":           cast.ToString(passengerID),
	}

	if apollo.FeatureToggle(ctx, "gs_intercity_page_navigation_divert_toggle", strconv.FormatInt(passengerID, 10), params) {
		return true
	}
	return false
}

func splicing(tmp string, additionalParams map[string]string) string {
	for k, v := range additionalParams {
		tmp += "&" + k + "=" + v
	}
	return tmp
}

func genFencePurchaseLink(ctx context.Context, buttonInfoTemplate string, prov ButtonInfoProv, linkUrl string, stationInfo models.StationInfo) (string, int) {
	var linkType int
	linkUrlConfigStr := gjson.Get(buttonInfoTemplate, "fence_link_url").String()
	linkUrlConfig := make(map[string]string, 0)
	err := json.Unmarshal([]byte(linkUrlConfigStr), &linkUrlConfig)
	if err != nil {
		log.Trace.Warnf(ctx, "genPurchaseLink", "_msg=unmarshal linkUrlConfigStr fail||err=%v", err)
	}
	linkUrl, linkType = drn.GetLinkUrlAndLinkType(cast.ToString(prov.GetAccessKeyID()), linkUrlConfig, prov.GetIsDRN())
	params := map[string]string{
		"estimate_id":                  prov.GetEstimateID(),
		"max_inventory":                strconv.Itoa(int(prov.GetCarpoolSeatMaxNum())),
		"carry_children_max_inventory": util.Int32String(prov.GetCarryChildMaxInventory(ctx)),
		"dchn":                         prov.GetDchn(),
		"title":                        url.PathEscape(gjson.Get(buttonInfoTemplate, "title").String()),
		"start_poi_name":               url.PathEscape(stationInfo.StartPoiName),
		"end_poi_name":                 url.PathEscape(stationInfo.EndPoiName),
	}
	additionalParams := make(map[string]string)
	if len(prov.GetStationInfo().StartPoiName) > 0 {
		additionalParams["start_poi_name"] = url.PathEscape(prov.GetStationInfo().StartPoiName)
	}
	if len(prov.GetStationInfo().EndPoiName) > 0 {
		additionalParams["end_poi_name"] = url.PathEscape(prov.GetStationInfo().EndPoiName)
	}
	if prov.GetStationInfo().FromLat > 0 {
		additionalParams["start_lat"] = cast.ToString(prov.GetStationInfo().FromLat)
	}
	if prov.GetStationInfo().FromLng > 0 {
		additionalParams["start_lng"] = cast.ToString(prov.GetStationInfo().FromLng)
	}
	if prov.GetStationInfo().ToLat > 0 {
		additionalParams["end_lat"] = cast.ToString(prov.GetStationInfo().ToLat)
	}
	if prov.GetStationInfo().ToLng > 0 {
		additionalParams["end_lng"] = cast.ToString(prov.GetStationInfo().ToLng)
	}
	linkUrl = splicing(dcmp.TranslateTemplate(linkUrl, params), additionalParams)
	return linkUrl, linkType
}

func genPurchaseLink(ctx context.Context, buttonInfoTemplate string, prov ButtonInfoProv, linkUrl string, selectInfo *models.StationInventorySelectInfo, stationInfo models.StationInfo) (string, int) {
	var linkType int
	linkUrlConfigStr := gjson.Get(buttonInfoTemplate, "link_url").String()
	linkUrlConfig := make(map[string]string, 0)
	err := json.Unmarshal([]byte(linkUrlConfigStr), &linkUrlConfig)
	if err != nil {
		log.Trace.Warnf(ctx, "genPurchaseLink", "_msg=unmarshal linkUrlConfigStr fail||err=%v", err)
	}

	linkUrl, linkType = drn.GetLinkUrlAndLinkType(cast.ToString(prov.GetAccessKeyID()), linkUrlConfig, prov.GetIsDRN())
	linkUrl = linkUrl + gjson.Get(buttonInfoTemplate, "poi_params").String()
	params := map[string]string{
		"estimate_id":                  prov.GetEstimateID(),
		"max_inventory":                strconv.Itoa(int(prov.GetCarpoolSeatMaxNum())),
		"carry_children_max_inventory": util.Int32String(prov.GetCarryChildMaxInventory(ctx)),
		"dchn":                         prov.GetDchn(),
		"slat":                         selectInfo.FromStationInfo.StationLat,
		"slng":                         selectInfo.FromStationInfo.StationLng,
		"dlat":                         selectInfo.DestStationInfo.StationLat,
		"dlng":                         selectInfo.DestStationInfo.StationLng,
		"title":                        url.PathEscape(gjson.Get(buttonInfoTemplate, "title").String()),
		"start_poi_name":               url.PathEscape(stationInfo.StartPoiName),
		"end_poi_name":                 url.PathEscape(stationInfo.EndPoiName),
	}
	linkUrl = dcmp.TranslateTemplate(linkUrl, params)
	return linkUrl, linkType
}

func genFenceBusSelectPointLink(linkUrl string, buttonInfoTemplate string, selectInfo *models.StationInventorySelectInfo, stationInfo models.StationInfo, prov ButtonInfoProv, req *proto.IntercityMultiEstimateRequest, stationListV2 models.StationListV2, linkType int) (string, int) {
	linkUrl = gjson.Get(buttonInfoTemplate, "fence_select_station_page_url").String()
	params := map[string]string{
		"route_id":            util.Int642String(selectInfo.RouteId),
		"start_city_id":       util.Int32String(stationInfo.StartCity),
		"estimate_id":         prov.GetEstimateID(),
		"dchn":                prov.GetDchn(),
		"from_page_type":      util.Int2String(carpoolstation.FromPageTypeSelectStationShift),
		"start_fence_id":      selectInfo.FromStationInfo.FenceId,
		"start_group_id":      selectInfo.FromStationInfo.GroupId,
		"end_fence_id":        selectInfo.DestStationInfo.FenceId,
		"end_group_id":        selectInfo.DestStationInfo.GroupId,
		"start_poi_city":      util.Int32String(stationInfo.StartCity),
		"end_poi_city":        util.Int32String(stationInfo.EndCity),
		"start_poi_city_name": url.PathEscape(stationListV2.FromCityName),
		"end_poi_city_name":   url.PathEscape(stationListV2.ToCityName),
	}
	additionalParams := make(map[string]string)
	if stationInfo.StartStationId != NoSelectStation || len(stationInfo.StartPoiName) != 0 {
		if stationInfo.StartStationId == int64(prov.GetSelectInfo().FromStationId) {
			// 这个站点是用户选择的，不是推荐的
			additionalParams["selected_start_station_id"] = util.Int642String(stationInfo.StartStationId)
			additionalParams["start_is_recommend"] = "0"
		} else {
			// 班次卡片的起点站点是根据用户选择的起点、poi来推荐的
			additionalParams["selected_start_station_id"] = util.Int2String(prov.GetSelectInfo().FromStationId)
			additionalParams["start_is_recommend"] = "1"
		}
	} else {
		if selectInfo.FromStationInfo.StationSceneType == FenceStation {
			additionalParams["selected_start_station_id"] = util.Int642String(int64(selectInfo.FromStationInfo.StationId))
		}
	}
	if req.GetFromLat() != InvalidLatOrLng {
		additionalParams["start_lat"] = cast.ToString(req.GetFromLat())
	}
	if req.GetFromLng() != InvalidLatOrLng {
		additionalParams["start_lng"] = cast.ToString(req.GetFromLng())
	}
	if len(req.GetStartPoiName()) > 0 {
		additionalParams["start_poi_name"] = url.PathEscape(req.GetStartPoiName())
	}
	if stationInfo.EndStationId != NoSelectStation || len(stationInfo.EndPoiName) != 0 {
		if stationInfo.EndStationId == int64(prov.GetSelectInfo().DestStationId) {
			// 这个站点是用户选择的，不是推荐的
			additionalParams["selected_end_station_id"] = util.Int642String(stationInfo.EndStationId)
			additionalParams["end_is_recommend"] = "0"
		} else {
			// 班次卡片的终点站点是根据用户选择的终点、poi来推荐的
			additionalParams["selected_end_station_id"] = util.Int2String(prov.GetSelectInfo().DestStationId)
			additionalParams["end_is_recommend"] = "1"
		}
	} else {
		if selectInfo.DestStationInfo.StationSceneType == FenceStation {
			additionalParams["selected_end_station_id"] = util.Int642String(int64(selectInfo.DestStationInfo.StationId))
		}
	}
	if req.GetToLat() != InvalidLatOrLng {
		additionalParams["end_lat"] = cast.ToString(req.GetToLat())
	}
	if req.GetToLng() != InvalidLatOrLng {
		additionalParams["end_lng"] = cast.ToString(req.GetToLng())
	}
	if len(req.GetEndPoiName()) > 0 {
		additionalParams["end_poi_name"] = url.PathEscape(req.GetEndPoiName())
	}
	linkUrl = splicing(dcmp.TranslateTemplate(linkUrl, params), additionalParams)
	linkType = 3 // 1小程序 2端 3 h5
	return linkUrl, linkType
}

func genSelectPointLink(linkUrl string, buttonInfoTemplate string, selectInfo *models.StationInventorySelectInfo, stationInfo models.StationInfo, prov ButtonInfoProv, req *proto.IntercityMultiEstimateRequest, stationListV2 models.StationListV2, linkType int) (string, int) {
	linkUrl = gjson.Get(buttonInfoTemplate, "select_station_page_url").String() + gjson.Get(buttonInfoTemplate, "fence_params").String()
	params := map[string]string{
		"route_id":            util.Int642String(selectInfo.RouteId),
		"start_city_id":       util.Int32String(stationInfo.StartCity),
		"estimate_id":         prov.GetEstimateID(),
		"dchn":                prov.GetDchn(),
		"from_page_type":      util.Int2String(carpoolstation.FromPageTypeSelectStationShift),
		"start_fence_id":      selectInfo.FromStationInfo.FenceId,
		"start_group_id":      selectInfo.FromStationInfo.GroupId,
		"start_lat":           cast.ToString(req.GetFromLat()),
		"start_lng":           cast.ToString(req.GetFromLng()),
		"end_fence_id":        selectInfo.DestStationInfo.FenceId,
		"end_lat":             cast.ToString(req.GetToLat()),
		"end_lng":             cast.ToString(req.GetToLng()),
		"end_group_id":        selectInfo.DestStationInfo.GroupId,
		"start_poi_city":      util.Int32String(stationInfo.StartCity),
		"end_poi_city":        util.Int32String(stationInfo.EndCity),
		"start_poi_city_name": url.PathEscape(stationListV2.FromCityName),
		"end_poi_city_name":   url.PathEscape(stationListV2.ToCityName),
	}
	// 只要入参不是全部站点，就传默勾参数，无论是否是推荐班次
	if stationInfo.StartStationId != NoSelectStation || len(stationInfo.StartPoiName) != 0 || (stationInfo.FromLat != 0 && stationInfo.FromLng != 0) {
		params["selected_start_station_id"] = cast.ToString(prov.GetSelectInfo().FromStationId)
	}
	if stationInfo.EndStationId != NoSelectStation || len(stationInfo.EndPoiName) != 0 || (stationInfo.ToLat != 0 && stationInfo.ToLng != 0) {
		params["selected_end_station_id"] = cast.ToString(prov.GetSelectInfo().DestStationId)
	}
	linkUrl = dcmp.TranslateTemplate(linkUrl, params)
	linkType = 3 // 1小程序 2端 3 h5
	return linkUrl, linkType
}

// isHitSelectStationPointPageSwitch 大巴新增"选站点页面需求放量"
func isHitSelectStationPointPageSwitch(ctx context.Context, passengerID int64, prov ButtonInfoProv, stationInfo models.StationInfo) bool {
	return apollo.FeatureToggle(
		ctx,
		"gs_select_station_point_page_switch",
		cast.ToString(passengerID),
		map[string]string{
			"pid":           cast.ToString(passengerID),
			"access_key_id": cast.ToString(prov.GetAccessKeyID()),
			"app_version":   prov.GetAppVersion(),
			"city_id":       cast.ToString(stationInfo.StartCity),
		})
}

func hitFenceToDoor(selectInfo *models.StationInventorySelectInfo) bool {
	if selectInfo == nil {
		return false
	}
	if selectInfo.FromStationInfo != nil && selectInfo.FromStationInfo.StationSceneType == FenceToDoor {
		return true
	}
	if selectInfo.DestStationInfo != nil && selectInfo.DestStationInfo.StationSceneType == FenceToDoor {
		return true
	}
	return false
}

func fenceToDoorJumpPurchase(selectInfo *models.StationInventorySelectInfo, stationInfo models.StationInfo, req *proto.IntercityMultiEstimateRequest) bool {
	if selectInfo == nil {
		return false
	}

	if stationInfo.StartStationId == NoSelectStation && selectInfo.FromStationInfo != nil && selectInfo.FromStationInfo.StationSceneType != FenceToDoor {
		return false
	}
	if stationInfo.EndStationId == NoSelectStation && selectInfo.DestStationInfo != nil && selectInfo.DestStationInfo.StationSceneType != FenceToDoor {
		return false
	}
	if selectInfo.FromStationInfo != nil && selectInfo.FromStationInfo.StationSceneType == FenceToDoor && selectInfo.SrcCost != nil &&
		(selectInfo.SrcCost.Dist == 0 && req.GetFromLat() != InvalidLatOrLng && req.GetFromLng() != InvalidLatOrLng) {
		return true
	}
	if selectInfo.DestStationInfo != nil && selectInfo.DestStationInfo.StationSceneType == FenceToDoor && selectInfo.DestCost != nil &&
		(selectInfo.DestCost.Dist == 0 && req.GetToLat() != InvalidLatOrLng && req.GetToLng() != InvalidLatOrLng) {
		return true
	}
	return false
}
