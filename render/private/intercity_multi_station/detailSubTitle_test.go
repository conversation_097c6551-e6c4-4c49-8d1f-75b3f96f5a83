package intercity_multi_station

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	. "github.com/bytedance/mockey"              // nolint
	. "github.com/smartystreets/goconvey/convey" //nolint
	"github.com/tidwall/gjson"
	"testing"
)

func Test_detailSubTitle(t *testing.T) {
	PatchConvey("Test_detailSubTitle", t, func() {
		<PERSON>vey("Case1: GetUrl", func() {
			mockOpenapiUrl := Mock(gjson.Get).Return(nil).Build()
			defer mockOpenapiUrl.UnPatch()
			//dcmp.TranslateTemplate

			mockTranslateTemplate := Mock(dcmp.TranslateTemplate).Return(nil).Build()
			defer mockTranslateTemplate.UnPatch()
			detailSubTitle(context.Background(), "50")
		})

	})
}
