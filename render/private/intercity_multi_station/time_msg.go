package intercity_multi_station

import (
	"context"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/door_and_station"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

type TimeMsgProv interface {
	GetDepartureTime() int64
	GetSelectInfo() *models.StationInventorySelectInfo
	GetStationEarlyPickUpTimeEta() int32
	GetProductFull() *biz_runtime.ProductInfoFull
}

func HitDoorToStation(station *models.StationInventorySelectInfo) bool {
	if station == nil {
		return false
	}
	if station.FromStationInfo != nil && station.FromStationInfo.StationSceneType == FenceStation {
		return true
	}
	return false
}

func HitFenceStation(station *models.StationInventorySelectInfo) bool {
	if station == nil {
		return false
	}
	if station.FromStationInfo != nil && station.FromStationInfo.StationSceneType == FenceStation {
		return true
	}
	if station.DestStationInfo != nil && station.DestStationInfo.StationSceneType == FenceStation {
		return true
	}
	return false
}

func TimeMsg(ctx context.Context, prov TimeMsgProv, isEstimateV2 bool) string {
	if !isEstimateV2 {
		return time.Unix(prov.GetDepartureTime(), 0).Format("15:04")
	}
	var timeTempelete string
	if door_and_station.HitNewStyle(ctx, prov.GetProductFull()) {
		text := dcmp.GetDcmpPlainContent(ctx, "intercity_station-door_to_station_time_msg")
		timeTempelete = gjson.Get(text, "time_msg").String()
	} else if HitFenceStation(prov.GetSelectInfo()) {
		timeTempelete = dcmp.GetDcmpPlainContent(ctx, "intercity_station-fence_time_msg")
	} else {
		timeTempelete = dcmp.GetDcmpPlainContent(ctx, "intercity_station-time_msg")
	}

	return dcmp.TranslateTemplate(
		timeTempelete,
		map[string]string{"time": time.Unix(prov.GetDepartureTime(), 0).Format("15:04")},
	)
}

func TimeMsgDescription(ctx context.Context, prov TimeMsgProv, isEstimateV2 bool) string {
	if !isEstimateV2 {
		return ""
	}
	var res string
	if HitDoorToStation(prov.GetSelectInfo()) && door_and_station.HitNewStyle(ctx, prov.GetProductFull()) && prov.GetStationEarlyPickUpTimeEta() > 0 {
		text := dcmp.GetDcmpPlainContent(ctx, "intercity_station-door_to_station_time_msg")
		timeTemplate := gjson.Get(text, "time_msg_description").String()
		res = dcmp.TranslateTemplate(timeTemplate, map[string]string{"minute": cast.ToString(prov.GetStationEarlyPickUpTimeEta())})
	}
	return res
}
