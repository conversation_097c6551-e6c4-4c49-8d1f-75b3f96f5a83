package ab

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/terminal/access_key_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/passenger-common/util"
	"git.xiaojukeji.com/nuwa/golibs/knife"
	"github.com/spf13/cast"
)

const DynamicIconAb = "dynamic_icon_ab"

var VERSION_REQUIREMENTS = map[int]string{
	access_key_id.AccessKeyIdDidiIosPassengerApp:     "7.0.16",
	access_key_id.AccessKeyIdDidiAndroidPassengerApp: "7.0.16",
	access_key_id.AccessKeyIdDidiWeChatMiniProgram:   "6.10.85",
	access_key_id.AccessKeyIdDidiAlipayMiniProgram:   "6.10.85",
}

func IsHitDynamicIconAb(ctx context.Context, userId string, params map[string]string) bool {

	v := knife.Get(ctx, DynamicIconAb)
	if v != nil {
		return v.(bool)
	}

	if v, ok := params["page_type"]; !ok || (!util.InArrayString(
		v,
		[]string{
			cast.ToString(page_type.PageTypeUndefined),
			cast.ToString(page_type.PageTypeGuideAnyCar),
		})) {
		knife.Set(ctx, DynamicIconAb, false)
		return false
	}

	// 版本号限制
	var accessKeyID int
	var appVersion string
	if accessKeyIDS, ok := params["access_key_id"]; ok {
		accessKeyID = cast.ToInt(accessKeyIDS)
	}

	if appVersionS, ok := params["app_version"]; ok {
		appVersion = appVersionS
	}

	if _, ok := VERSION_REQUIREMENTS[accessKeyID]; !ok || util.CompareAppVersion(appVersion, VERSION_REQUIREMENTS[accessKeyID]) < 0 {
		knife.Set(ctx, DynamicIconAb, false)
		return false
	}

	//toggleRes := apollo.GetHitToggleByNamespace(ctx, userId, "dynamic_price_show_icon", params)
	//if toggleRes != nil && toggleRes.IsAllow() &&
	//	toggleRes.GetAssignment().GetParameter("hit_dynamic_icon", "0") == "1" {
	//	knife.Set(ctx, DynamicIconAb, true)
	//	return true
	//}

	if apollo.FeatureToggle(ctx, "dynamic_price_show_icon", userId, params) {
		knife.Set(ctx, DynamicIconAb, true)
		return true
	}

	knife.Set(ctx, DynamicIconAb, false)
	return false
}
