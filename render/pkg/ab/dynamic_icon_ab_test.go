package ab

import (
	"context"
	"testing"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/terminal/access_key_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/passenger-common/util"
	"git.xiaojukeji.com/nuwa/golibs/knife"
	"github.com/bytedance/mockey"
	"github.com/spf13/cast"
	"github.com/stretchr/testify/assert"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

func TestIsHitDynamicIconAb(t *testing.T) {
	t.Run("命中缓存场景", func(t *testing.T) {
		// 设置mockey
		defer mockey.UnPatchAll()

		// 准备测试数据
		ctx := context.Background()
		ctx = knife.New(ctx)
		knife.Set(ctx, DynamicIconAb, true)
		userId := "12345"
		params := map[string]string{}

		// 执行测试
		result := IsHitDynamicIconAb(ctx, userId, params)

		// 断言结果
		assert.True(t, result, "当context中已缓存了DynamicIconAb为true时，应返回true")
	})

	t.Run("页面类型不符合要求场景", func(t *testing.T) {
		// 设置mockey
		defer mockey.UnPatchAll()

		// 准备测试数据
		ctx := context.Background()
		ctx = knife.New(ctx)
		userId := "12345"
		params := map[string]string{
			"page_type": "999", // 不在指定的页面类型列表中
		}

		// 执行测试
		result := IsHitDynamicIconAb(ctx, userId, params)

		// 断言结果
		assert.False(t, result, "当页面类型不满足要求时，应返回false")
		cachedValue := knife.Get(ctx, DynamicIconAb)
		assert.Equal(t, false, cachedValue, "缓存值应为false")
	})

	t.Run("页面类型未定义场景", func(t *testing.T) {
		// 设置mockey
		defer mockey.UnPatchAll()

		// 准备测试数据
		ctx := context.Background()
		ctx = knife.New(ctx)
		userId := "12345"
		params := map[string]string{
			"page_type":     cast.ToString(page_type.PageTypeUndefined),
			"access_key_id": cast.ToString(access_key_id.AccessKeyIdDidiIosPassengerApp),
			"app_version":   "7.0.16",
		}

		// 模拟依赖
		mockey.Mock(util.CompareAppVersion).Return(0).Build() // 版本匹配

		// Mock Apollo相关
		mockey.Mock(apollo.GetHitToggleByNamespace).Return(&model.ToggleResult{}).Build()
		mockey.Mock((*model.ToggleResult).IsAllow).Return(true).Build()
		mockey.Mock((*model.ToggleResult).GetAssignment).Return(&model.Assignment{}).Build()
		mockey.Mock((*model.Assignment).GetParameter).Return("0").Build()
		mockey.Mock(apollo.FeatureToggle).Return(false).Build()

		// 执行测试
		result := IsHitDynamicIconAb(ctx, userId, params)

		// 断言结果
		assert.False(t, result, "当Apollo返回hit_dynamic_icon为0时，应返回false")
	})

	t.Run("版本号不满足要求场景", func(t *testing.T) {
		// 设置mockey
		defer mockey.UnPatchAll()

		// 准备测试数据
		ctx := context.Background()
		ctx = knife.New(ctx)
		userId := "12345"
		params := map[string]string{
			"page_type":     cast.ToString(page_type.PageTypeGuideAnyCar),
			"access_key_id": cast.ToString(access_key_id.AccessKeyIdDidiIosPassengerApp),
			"app_version":   "7.0.15", // 版本低于要求
		}

		// 模拟依赖
		mockey.Mock(util.CompareAppVersion).Return(-1).Build() // 版本低于要求

		// 执行测试
		result := IsHitDynamicIconAb(ctx, userId, params)

		// 断言结果
		assert.False(t, result, "当应用版本低于要求时，应返回false")
		cachedValue := knife.Get(ctx, DynamicIconAb)
		assert.Equal(t, false, cachedValue, "缓存值应为false")
	})

	t.Run("不支持的AccessKeyId场景", func(t *testing.T) {
		// 设置mockey
		defer mockey.UnPatchAll()

		// 准备测试数据
		ctx := context.Background()
		ctx = knife.New(ctx)
		userId := "12345"
		params := map[string]string{
			"page_type":     cast.ToString(page_type.PageTypeGuideAnyCar),
			"access_key_id": "999", // 不支持的AccessKeyId
			"app_version":   "7.0.16",
		}

		// 执行测试
		result := IsHitDynamicIconAb(ctx, userId, params)

		// 断言结果
		assert.False(t, result, "当AccessKeyId不被支持时，应返回false")
	})

	t.Run("Apollo允许且hit_dynamic_icon为1场景", func(t *testing.T) {
		// 设置mockey
		defer mockey.UnPatchAll()

		// 准备测试数据
		ctx := context.Background()
		ctx = knife.New(ctx)
		userId := "12345"
		params := map[string]string{
			"page_type":     cast.ToString(page_type.PageTypeGuideAnyCar),
			"access_key_id": cast.ToString(access_key_id.AccessKeyIdDidiIosPassengerApp),
			"app_version":   "7.0.16", // 版本满足要求
		}

		// 模拟依赖
		mockey.Mock(util.CompareAppVersion).Return(0).Build() // 版本匹配

		// Mock Apollo相关
		mockey.Mock(apollo.GetHitToggleByNamespace).Return(&model.ToggleResult{}).Build()
		mockey.Mock((*model.ToggleResult).IsAllow).Return(true).Build()
		mockey.Mock((*model.ToggleResult).GetAssignment).Return(&model.Assignment{}).Build()
		mockey.Mock((*model.Assignment).GetParameter).Return("1").Build()
		mockey.Mock(apollo.FeatureToggle).Return(true).Build()

		// 执行测试
		result := IsHitDynamicIconAb(ctx, userId, params)

		// 断言结果
		assert.True(t, result, "当Apollo返回允许且hit_dynamic_icon为1时，应返回true")
		cachedValue := knife.Get(ctx, DynamicIconAb)
		assert.Equal(t, true, cachedValue, "缓存值应为true")
	})

	t.Run("Apollo允许但hit_dynamic_icon不为1场景", func(t *testing.T) {
		// 设置mockey
		defer mockey.UnPatchAll()

		// 准备测试数据
		ctx := context.Background()
		ctx = knife.New(ctx)
		userId := "12345"
		params := map[string]string{
			"page_type":     cast.ToString(page_type.PageTypeGuideAnyCar),
			"access_key_id": cast.ToString(access_key_id.AccessKeyIdDidiIosPassengerApp),
			"app_version":   "7.0.16", // 版本满足要求
		}

		// 模拟依赖
		mockey.Mock(util.CompareAppVersion).Return(0).Build() // 版本匹配

		// Mock Apollo相关
		mockey.Mock(apollo.GetHitToggleByNamespace).Return(&model.ToggleResult{}).Build()
		mockey.Mock((*model.ToggleResult).IsAllow).Return(true).Build()
		mockey.Mock((*model.ToggleResult).GetAssignment).Return(&model.Assignment{}).Build()
		mockey.Mock((*model.Assignment).GetParameter).Return("0").Build()
		mockey.Mock(apollo.FeatureToggle).Return(false).Build()

		// 执行测试
		result := IsHitDynamicIconAb(ctx, userId, params)

		// 断言结果
		assert.False(t, result, "当Apollo返回允许但hit_dynamic_icon不为1时，应返回false")
		cachedValue := knife.Get(ctx, DynamicIconAb)
		assert.Equal(t, false, cachedValue, "缓存值应为false")
	})

	t.Run("Apollo不允许场景", func(t *testing.T) {
		// 设置mockey
		defer mockey.UnPatchAll()

		// 准备测试数据
		ctx := context.Background()
		ctx = knife.New(ctx)
		userId := "12345"
		params := map[string]string{
			"page_type":     cast.ToString(page_type.PageTypeGuideAnyCar),
			"access_key_id": cast.ToString(access_key_id.AccessKeyIdDidiIosPassengerApp),
			"app_version":   "7.0.16", // 版本满足要求
		}

		// 模拟依赖
		mockey.Mock(util.CompareAppVersion).Return(0).Build() // 版本匹配

		// Mock Apollo相关
		mockey.Mock(apollo.GetHitToggleByNamespace).Return(&model.ToggleResult{}).Build()
		mockey.Mock((*model.ToggleResult).IsAllow).Return(false).Build()
		mockey.Mock(apollo.FeatureToggle).Return(false).Build()

		// 执行测试
		result := IsHitDynamicIconAb(ctx, userId, params)

		// 断言结果
		assert.False(t, result, "当Apollo返回不允许时，即使hit_dynamic_icon为1，也应返回false")
		cachedValue := knife.Get(ctx, DynamicIconAb)
		assert.Equal(t, false, cachedValue, "缓存值应为false")
	})
}
