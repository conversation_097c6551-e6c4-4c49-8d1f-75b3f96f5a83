package handler

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
)

func (h *handler) exclusion(ctx context.Context) *handler {
	memberProtect, existMemberProtect := h.formatInput[consts.FeeMemberProtect]
	dynamicDiffPrice, existDynamicDiffPrice := h.formatInput[consts.DynamicDiffPrice]

	// 溢价保护和动调互斥
	if (existMemberProtect && memberProtect != nil && memberProtect.Amount != 0) &&
		(existDynamicDiffPrice && dynamicDiffPrice != nil && dynamicDiffPrice.Amount != 0) {
		delete(h.formatInput, consts.DynamicDiffPrice)
	}

	pickGuideUpFree, existPickGuideUpFree := h.formatInput[consts.FeePickUpGuideFree]
	pickStationGuide, existStationGuide := h.formatInput[consts.FeeStationGuide]
	if (existPickGuideUpFree && pickGuideUpFree.Amount != 0) &&
		(existStationGuide && pickStationGuide.Amount != 0) {
		delete(h.formatInput, consts.FeeStationGuide)
	}

	h.comparePricingByMeterAndPricingByOnlineExclusion(ctx).
		comparePricingByMeterAndCouponExclusion(ctx).
		comparePricingByMeterAndSpecialPrice(ctx)

	return h
}

// comparePricingByMeterAndPricingByOnlineExclusion 比打表省和在线计价标签互斥
func (h *handler) comparePricingByMeterAndPricingByOnlineExclusion(ctx context.Context) *handler {
	comparePricingByMeter, existComparePricingByMeter := h.formatInput[consts.FeeComparePricingByMeter]
	taxiPricingByOnline, existTaxiPricingByOnline := h.formatInput[consts.FeeTaxiPricingByOnline]
	if existComparePricingByMeter && existTaxiPricingByOnline && comparePricingByMeter != nil && taxiPricingByOnline != nil {
		delete(h.formatInput, consts.FeeTaxiPricingByOnline)
	}

	return h
}

// comparePricingByMeterAndCouponExclusion 打表计价和券互斥
func (h *handler) comparePricingByMeterAndCouponExclusion(ctx context.Context) *handler {
	comparePricingByMeter, existComparePricingByMeter := h.formatInput[consts.FeeComparePricingByMeter]
	coupon, existCoupon := h.formatInput[consts.FeeCoupon]
	if existComparePricingByMeter && existCoupon && comparePricingByMeter != nil && coupon != nil && comparePricingByMeter.Amount > 0 && coupon.Amount > 0 {
		delete(h.formatInput, consts.FeeCoupon)
	}

	return h
}

// comparePricingByMeterAndSpecialPrice 打表计价和定价折扣互斥
func (h *handler) comparePricingByMeterAndSpecialPrice(ctx context.Context) *handler {
	comparePricingByMeter, existComparePricingByMeter := h.formatInput[consts.FeeComparePricingByMeter]
	specialPrice, existSpecialPrice := h.formatInput[consts.FeeSpecialPrice]
	if existComparePricingByMeter && existSpecialPrice && comparePricingByMeter != nil && specialPrice != nil && comparePricingByMeter.Amount > 0 && specialPrice.Amount > 0 {
		delete(h.formatInput, consts.FeeSpecialPrice)
	}

	return h
}
