package handler

import (
	"context"

	model2 "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
)

type handler struct {
	input           *model2.FeeInput
	env             *model2.Env
	productCategory int64

	confInstance  *model2.ConfInstance         // 配置
	formatInput   map[string]*model2.FeeDetail // 格式化后输入
	groups        []*model2.FeeGroup           // 分组
	residueCap    int                          // 剩余容量
	defaultRender model2.RenderCallback        // 默认渲染回调
	defaultFilter model2.FilterCallback        // 过滤回调
}

func NewHandler(input *model2.FeeInput, env *model2.Env) *handler {
	return &handler{
		input:      input,
		env:        env,
		residueCap: env.ShareCap,
	}
}

func (h *handler) Do(ctx context.Context) []*model2.FeeOutput {
	return h.load(ctx).
		exclusion(ctx).
		filter(ctx).
		grouping(ctx).
		merge(ctx).
		output(ctx)
}

func (h *handler) SetProductCategory(productCategory int64) *handler {
	h.productCategory = productCategory

	return h
}
