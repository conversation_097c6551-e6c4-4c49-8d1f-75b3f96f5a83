package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"testing"

	model2 "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"github.com/tidwall/gjson"
)

func newInput(ctx context.Context) *model2.FeeInput {
	res := &model2.FeeInput{
		BusinessPayDeduction: util.Float64Ptr(10.01),
		DynamicProtect:       util.Float64Ptr(11.01),
		TaxiSpecial:          util.Float64Ptr(12.01),

		CouponInfo: &model2.Coupon{
			CustomTag: "aaa",
			Amount:    13.01,
		},
		Ziyoubao: &model2.Ziyoubao{
			Amount: 14.01,
			Mile:   15.01,
		},
		DiscountInfo: map[string]float64{
			"bonus":    16.01,
			"hk_bonus": 17.01,
		},
		FeeDetailInfo: map[string]float64{
			"red_packet":               18.01,
			"dynamic_diff_price":       19.01,
			"energy_consume_fee":       20.01,
			"designated_driver_fee":    21.01,
			"cross_city_fee":           22.01,
			"taxi_peak_price":          23.01,
			"taxi_holiday_price":       24.01,
			"sps_barrier_free_car_fee": 25.01,
			"sps_pet_fee":              26.01,
			"sps_pet_platform_fee":     27.01,
			"sps_pick_up_guide_fee":    28.01,
		},
		//RenderCallback: map[string]func(groupName string, fee float64, feeType int32) *model.FeeOutput{
		//	"default":  RenderCallbackDefault(ctx),
		//	"coupon":   RenderCallbackCoupon(ctx, "price_diff"),
		//	"ziyoubao": RenderCallbackZiyoubao(ctx, 29.01),
		//},
	}
	return res
}

func TestEngine(t *testing.T) {
	var ctx = context.Background()
	h := NewHandler(newInput(ctx), &model2.Env{
		IsShareCap: false,
	})
	h.confInstance = getConf()
	h.mount(ctx)
	h.exclusion(ctx)
	h.grouping(ctx)
	h.merge(ctx)
	fmt.Println(util.JustJsonEncode(h.groups))
	output := h.output(ctx)
	fmt.Println(util.JustJsonEncode(output))
}

func getConf() *model2.ConfInstance {
	var instance *model2.ConfInstance
	conf := `{"page_type":"33","groups":[{"group_name":"incr_fee_group","merge_text":"normal_increase","fee_list":[{"key":"red_packet"},{"key":"dynamic_diff_price"},{"key":"energy_consume_fee"},{"key":"taxi_peak_price"},{"key":"taxi_holiday_price"}],"cap":7,"fee_type":1},{"group_name":"customized_service_group","merge_text":"normal_increase","cap":1,"fee_list":[{"key":"designated_driver_fee"},{"key":"sps_pet_fee"},{"key":"sps_barrier_free_car_fee"},{"key":"sps_pet_platform_fee"},{"key":"sps_pick_up_guide_fee"},{"key":"cross_city_fee"}],"fee_type":1},{"group_name":"business_pay_group","merge_text":"","fee_list":[{"key":"business_pay_deduction"}],"cap":1,"fee_type":4},{"group_name":"spec_ziyoubao_group","merge_text":"","fee_list":[{"key":"ziyoubao"}],"cap":1,"fee_type":3},{"group_name":"taxi_special_group","merge_text":"","fee_list":[{"key":"taxi_special"}],"cap":1,"fee_type":3},{"group_name":"decr_fee_grpup","merge_text":"normal_discount","fee_list":[{"key":"member_protect"},{"key":"coupon"},{"key":"bonus"},{"key":"right"}],"cap":1,"fee_type":3}]}`

	_ = json.Unmarshal([]byte(conf), &instance)

	return instance
}

func RenderCallbackDefault(ctx context.Context) func(path string, fee float64, feeType int32) *model2.FeeOutput {
	return func(path string, fee float64, feeType int32) *model2.FeeOutput {
		// key := consts.FeeDetailDesc

		if conf := gjson.Get(mockDcmp, path).Map(); len(conf) > 0 {
			tag := map[string]string{
				"num": util.FormatPrice(fee, -1),
			}
			return &model2.FeeOutput{
				BorderColor: conf["border_color"].String(),
				Content:     util.ReplaceTag(ctx, conf["content"].String(), tag),
				Icon:        conf["icon"].String(),
				Type:        feeType,
				Fee: &model2.FeeDetail{
					Amount: fee,
				},
			}
		}

		return nil
	}
}

// 自由宝
func RenderCallbackZiyoubao(ctx context.Context, deductionMile float64) func(path string, fee float64, feeType int32) *model2.FeeOutput {
	return func(path string, fee float64, feeType int32) *model2.FeeOutput {
		// var key string // todo
		if conf := gjson.Get(mockDcmp, path).Map(); len(conf) > 0 {
			return &model2.FeeOutput{
				BorderColor: conf["border_color"].String(),
				Content: util.ReplaceTag(ctx, conf["content"].String(), map[string]string{
					"mile": util.FormatPrice(deductionMile, 2),
					"num":  util.FormatPrice(fee, 2),
				}),
				Icon: conf["icon"].String(),
				Type: feeType,
				Fee: &model2.FeeDetail{
					Amount: fee,
				},
			}
		}

		return nil
	}
}

func RenderCallbackCoupon(ctx context.Context, customTag string) func(path string, fee float64, feeType int32) *model2.FeeOutput {
	return func(_ string, fee float64, feeType int32) *model2.FeeOutput {
		var (
			conf = make(map[string]gjson.Result)
		)

		if strings.Contains(customTag, "") {
			if cf := gjson.Get(mockDcmp, customTag).Map(); len(cf) > 0 {
				conf = cf
			}
		} else {
			if cf := gjson.Get(mockDcmp, customTag).Map(); len(cf) > 0 {
				conf = cf
			} else if cf = gjson.Get(mockDcmp, "coupon").Map(); len(cf) > 0 {
				conf = cf
			} else {
				return nil
			}
		}

		tag := map[string]string{
			"num": util.FormatPrice(fee, -1),
		}

		return &model2.FeeOutput{
			BorderColor: conf["border_color"].String(),
			Content:     util.ReplaceTag(ctx, conf["content"].String(), tag),
			Icon:        conf["icon"].String(),
			Type:        feeType,
			Fee: &model2.FeeDetail{
				Amount: fee,
			},
		}
	}
}

var mockDcmp = `{
  "normal_discount": {
    "content": "{-{{num}}元}",
    "icon": "https://pt-starimg.didistatic.com/static/starimg/img/koYaLjhSQm1638954388327.png",
    "border_color": "#FFC2BF",
    "title": "汇总优惠"
  },
  "coupon": {
    "content": "{-{{num}}元}",
    "icon": "https://pt-starimg.didistatic.com/static/starimg/img/ydBUiytPMj1638954388837.png",
    "border_color": "#FFC2BF",
    "title": "券"
  },
  "bonus": {
    "content": "{-{{num}}元}",
    "icon": "https://pt-starimg.didistatic.com/static/starimg/img/ZrEaaEvzGq1638954388487.png",
    "border_color": "#FFC2BF",
    "title": "福利金"
  },
  "right": {
    "content": "{-{{num}}元}",
    "icon": "https://dpubstatic.udache.com/static/dpubimg/28b98e96-9cbc-4f72-99b1-46195134224e.png",
    "border_color": "#FFC2BF",
    "title": "权益"
  },
  "card": {
    "content": "{-{{num}}元}",
    "icon": "https://dpubstatic.udache.com/static/dpubimg/28b98e96-9cbc-4f72-99b1-46195134224e.png",
    "border_color": "#FFC2BF",
    "title": "畅行卡"
  },
  "ziyoubao": {
    "title": "打车自由宝",
    "icon": "https://dpubstatic.udache.com/static/dpubimg/eacb338a-e9a7-4eb9-8d5d-bc23b6e57ad5.png",
    "content": "预计抵{{{mile}}公里}, 共{{{num}}元}"
  },
  "red_packet": {
    "content": "含节假日司机服务费{{{num}}}元",
    "icon": "",
    "border_color": "",
    "title": "节假日服务费"
  },
  "business_pay_deduction": {
    "content": "{-{{num}}元}",
    "icon": "https://pt-starimg.didistatic.com/static/starimg/img/TJcl9iFeku1638954388682.png",
    "border_color": "#AFBCD8",
    "title": "企业支付"
  },
  "price_privilege": {
    "content": "{只要快车价}",
    "icon": "",
    "border_color": "#FFC2BF",
    "title": "升舱卡"
  },
  "dynamic_diff_price": {
    "content": "{+{{num}}}倍",
    "icon": "https://pt-starimg.didistatic.com/static/starimg/img/p5Ws5xXpuK1638955579921.png",
    "border_color": "#FFC5A2",
    "title": "动调费用"
  },
  "member_protect": {
    "content": "{免溢价已抵{{num}}元}",
    "icon": "https://pt-starimg.didistatic.com/static/starimg/img/VUaufE267x1638954388536.png",
    "border_color": "#D5CAAB",
    "title": "会员保护"
  },
  "paid_member": {
    "icon": "https://pt-starimg.didistatic.com/static/starimg/img/03Vn2POiUP1650943554904.png",
    "title": "超级会员(平台乘客付费会员)",
    "content": "{超级会员-{{num}}元}"
  },
  "normal_increase": {
    "content": "{+{{num}}元}",
    "icon": "https://pt-starimg.didistatic.com/static/starimg/img/0D1hRxe6ue1638954388354.png",
    "border_color": "#FFC2BF",
    "title": "加价汇总"
  },
  "carpool_diff_fast": {
    "content": "比快车{省{{num}}元}",
    "suffix": "{/省{{num}}元}",
    "icon": "",
    "border_color": "#FFC2BF",
    "title": "拼车合并"
  },
  "carpool_paid_member": {
    "content": "{会员-{{num}}元}",
    "suffix": "{/-{{num}}元}",
    "icon": "",
    "border_color": "#FFC2BF",
    "title": "拼车接入会员标识"
  },
  "limittime": {
    "content": "司机愿减{{{num}}}元",
    "icon": "",
    "title": ""
  },
  "pay_return_reduction": {
    "content": "{下车可返{{num}}元券}",
    "icon": "https://dpubstatic.udache.com/static/dpubimg/29876c5c-8334-49d6-addc-57d9734d7b71.png",
    "title": "下车返立减券",
    "border_color": "#FFC2BF"
  },
  "pay_return_discount": {
    "content": "{下车可返{{num}}折券}",
    "icon": "https://dpubstatic.udache.com/static/dpubimg/29876c5c-8334-49d6-addc-57d9734d7b71.png",
    "title": "下车返折扣券",
    "border_color": "#FFC2BF"
  },
  "newloss": {
    "content": "{重逢专属-{{num}}元}",
    "icon": "",
    "title": "新流券",
    "border_color": "#FFC2BF"
  },
  "newloss_develop_task": {
    "content": "{重逢专属-{{num}}元}",
    "icon": "",
    "title": "新流任务券",
    "border_color": "#FFC2BF"
  },
  "member_coupon": {
    "content": "{会员-{{num}}元}",
    "border_color": "#FFC2BF"
  },
  "premium_paid_member": {
    "content": "{-{{num}}元}",
    "title": "专车付费会员免溢价",
    "icon": "https://pt-starimg.didistatic.com/static/starimg/img/kLAvPs1x0C1659679447379.png",
    "border_color": "#FFC2BF"
  },
  "special_price_taxi_dis_desc": {
    "content": "已优惠{{{num}}}元",
    "icon": "",
    "title": ""
  },
  "MPT_EXAM": {
    "content": "{特快暖心助考价}",
    "icon": "",
    "title": "高考特快免溢价",
    "border_color": "#FFC2BF"
  },
  "RESUME_WORK_SUBSIDY": {
    "content": "{复工补贴-{{num}}元}",
    "icon": "",
    "border_color": "#FFC2BF",
    "title": ""
  },
  "luxury_paid_member": {
    "content": "{-{{num}}元}",
    "icon": "https://pt-starimg.didistatic.com/static/starimg/img/XRY7x81YFA1653879379954.png",
    "border_color": "#FFC2BF",
    "title": ""
  },
  "hk_cap_taxi_coupon": {
    "content": "{已抵{{num}}HKD}",
    "icon": "https://pt-starimg.didistatic.com/static/starimg/img/ydBUiytPMj1638954388837.png",
    "border_color": "#FFC2BF",
    "title": "券"
  },
  "hk_cap_taxi_bonus": {
    "content": "{已抵{{num}}HKD}",
    "icon": "https://dpubstatic.udache.com/static/dpubimg/KqqrHwyL27MDlHrhyyiC7.png",
    "border_color": "#FFC2BF",
    "title": "福利金"
  },
  "ZHAO_9_WAN_5": {
    "content": "{朝九晚五-{{num}}元}",
    "icon": "",
    "border_color": "#FFC2BF",
    "title": "券"
  },
  "ZHAO_9_WAN_6": {
    "content": "{朝九晚六-{{num}}元}",
    "icon": "",
    "border_color": "#FFC2BF",
    "title": "券"
  },
  "SALE_WEEKEND": {
    "content": "{周末大惠-{{num}}元}",
    "icon": "",
    "border_color": "#FFC2BF",
    "title": "券"
  },
  "SPECIAL_RATE_66": {
    "content": "{特惠66折-{{num}}元}",
    "icon": "",
    "border_color": "#FFC2BF",
    "title": "券"
  },
  "WEEKEND_567": {
    "content": "{周末567-{{num}}元}",
    "icon": "",
    "border_color": "#FFC2BF",
    "title": "券"
  },
  "MPT_NEW_CATEGORY": {
    "content": "{-{{num}}元}",
    "icon": "https://dpubstatic.udache.com/static/dpubimg/077c6d36-5483-4825-b0a3-4d87cba49894.png",
    "title": "新客券"
  },
  "price_diff": {
    "content": "{价准宝券-{{num}}元}",
    "icon": "",
    "border_color": "#FFC2BF",
    "title": "价准宝券"
  },
  "student_coupon_tag": {
    "content": "{学生券-{{num}}元}",
    "icon": "",
    "border_color": "#FFC2BF",
    "title": ""
  },
  "CHAO_ZHI_WEEKEND": {
    "content": "{超值周末-{{num}}元}",
    "icon": "",
    "border_color": "#FFC2BF",
    "title": ""
  },
  "hilda_commute_cap": {
    "content": "{通勤封顶-{{num}}元}",
    "icon": "",
    "border_color": "#FFC2BF",
    "title": ""
  },
  "taxi_holiday_price": {
    "content": "含节假日附加费{{{num}}}元",
    "icon": "",
    "border_color": "#FFC2BF",
    "title": "出租车节假日附加费"
  },
  "taxi_peak_price": {
    "content": "含信息费{{num}}元",
    "icon": "",
    "border_color": "#999999",
    "title": "出租车峰期无出口"
  },
  "taxi_special": {
    "content": "已优惠{{{num}}}元",
    "icon": "",
    "title": ""
  },
  "weekend_member": {
    "content": "{周末畅玩卡-{{num}}元}",
    "icon": "",
    "border_color": "#FFC2BF",
    "title": "周末畅玩卡"
  }
}`
