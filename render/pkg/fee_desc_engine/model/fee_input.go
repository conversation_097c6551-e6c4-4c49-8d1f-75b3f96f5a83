package model

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
)

const (
	ZiyoubaoType = "spec_ziyoubao"

	Reward = "reward"

	CouponDefault    = "coupon"
	CouponStudent    = "student"
	CouponMonth      = "month"
	CouponPaidMember = "paid_member"
	CouponCityCard   = "citycard"

	MemberDiscountCard = "member_discount_card"
	Right              = "economical_card_right"

	AlipayCoupon = "alipay_coupon"

	RevolvingAccountDiscount = "revolvingAccountDiscount"
	RevolvingAccountRebate   = "revolvingAccountRebate"
)

type FeeInput struct {
	BusinessPayDeduction *float64            // 企业付
	DynamicProtect       *float64            // 溢价保护
	TaxiSpecial          *float64            // 出租车特惠
	CouponInfo           *Coupon             // 券
	Ziyoubao             *Ziyoubao           // 自由宝
	DiscountInfo         map[string]float64  // 减价项汇总，打车金, 香港打车金等等
	FeeDetailInfo        map[string]float64  // 加价项汇总
	FeeExtraInfo         map[string]float64  // 账单补充信息
	NeutralInfo          map[string]float64  // 中性项
	CheaperThanFastCar   *CheaperThanFastCar //比快车省

	FilterCallback FilterCallback
	RenderCallback RenderCallback

	TCDiscountFee *float64 // 网开台三方优惠
}

func NewFeeInput() *FeeInput {
	return &FeeInput{}
}

func (f *FeeInput) RegisterRenderCallBack(key string, callBack func(key string, path string, fee float64, feeType int32) *FeeOutput) *FeeInput {
	if f.RenderCallback == nil {
		f.RenderCallback = RenderCallback{}
	}

	f.RenderCallback[key] = callBack

	return f
}

func (f *FeeInput) RegisterFilterCallBack(key string, callBack func() bool) *FeeInput {
	if f.FilterCallback == nil {
		f.FilterCallback = FilterCallback{}
	}

	f.FilterCallback[key] = callBack

	return f
}

func (f *FeeInput) SetBusinessPayByQuotation(ctx context.Context, prov render.QuotationInfoProvider) *FeeInput {
	if prov.IsBusinessPay() {
		if payInfo := prov.GetMixedPaymentInfo(ctx); payInfo != nil && payInfo.DeductFee > 0 {
			f.BusinessPayDeduction = util.Float64Ptr(payInfo.DeductFee)
		} else if estimateFee := prov.GetEstimateFee(); estimateFee > 0 {
			f.BusinessPayDeduction = util.Float64Ptr(estimateFee)
		}
	}

	return f
}

func (f *FeeInput) SetCouponInfoByQuotation(ctx context.Context, prov render.QuotationInfoProvider) *FeeInput {
	if coupon := prov.GetDiscountDescByTypes(ctx, []string{CouponDefault, CouponStudent, CouponMonth, CouponPaidMember, CouponCityCard}); coupon != nil {
		f.CouponInfo = &Coupon{
			CustomTag: coupon.CustomTag,
			Amount:    coupon.Amount,
		}
	}

	return f
}

func (f *FeeInput) SetZiYouBaoByQuotation(ctx context.Context, prov render.QuotationInfoProvider) *FeeInput {
	if ziyoubao := prov.GetDiscountDescByTypes(ctx, []string{ZiyoubaoType}); ziyoubao != nil {
		if mile, err := strconv.ParseFloat(ziyoubao.DeductionMile, 64); err == nil && mile > 0 {
			f.Ziyoubao = &Ziyoubao{
				Amount: ziyoubao.Amount,
				Mile:   mile,
			}

		} else if err != nil {
			log.Trace.Warnf(ctx, "getZiYouBaoByQuotation", "ziyoubao parse float fail, data:%v", ziyoubao.DeductionMile)
		}
	}

	return f
}

func (f *FeeInput) SetTCDiscountFeeByQuotation(ctx context.Context, prov render.QuotationInfoProvider) *FeeInput {
	tcDiscountFee := prov.GetTCDiscountFee()
	if tcDiscountFee != 0 {
		f.TCDiscountFee = util.Float64Ptr(tcDiscountFee)
	}

	return f
}

func (f *FeeInput) SetFeeDetailInfoByQuotation(ctx context.Context, prov render.QuotationInfoProvider) *FeeInput {
	f.FeeDetailInfo = prov.GetFeeDetailInfo()

	return f
}

func (f *FeeInput) SetDiscountInfoByQuotation(ctx context.Context, prov render.QuotationInfoProvider) *FeeInput {
	ret := make(map[string]float64)

	if bonus := prov.GetDiscountDescByTypes(ctx, []string{Reward}); bonus != nil {
		ret[consts.FeeBonus] = bonus.Amount
	}

	if revolvingDiscount := prov.GetDiscountDescByTypes(ctx, []string{RevolvingAccountDiscount}); revolvingDiscount != nil {
		ret[consts.FeeRevolvingDiscount] = revolvingDiscount.Amount
	}

	if revolvingRebate := prov.GetDiscountDescByTypes(ctx, []string{RevolvingAccountRebate}); revolvingRebate != nil {
		ret[consts.FeeRevolvingRebate] = revolvingRebate.Amount
	}

	if memberDiscountCard := prov.GetDiscountDescByTypes(ctx, []string{MemberDiscountCard}); memberDiscountCard != nil {
		ret[consts.FeeMemberDiscountCard] = memberDiscountCard.Amount
	}

	if alipayCoupon := prov.GetAlipayCoupon(); alipayCoupon != nil {
		alipayCouponAmount, err := strconv.ParseFloat(alipayCoupon.Amount, 64)
		if err == nil && alipayCouponAmount > 0 {
			ret[consts.FeeAlipayCoupon] = alipayCouponAmount / 100
		}
	}

	f.DiscountInfo = ret

	return f
}

func (f *FeeInput) SetRenderCallBack(callBack RenderCallback) *FeeInput {
	if len(f.RenderCallback) <= 0 {
		f.RenderCallback = callBack
	} else {
		for k, v := range callBack {
			f.RenderCallback[k] = v
		}
	}

	return f
}

func (f *FeeInput) SetFilterCallBack(callBack FilterCallback) *FeeInput {
	if len(f.FilterCallback) <= 0 {
		f.FilterCallback = callBack
	} else {
		for k, v := range callBack {
			f.FilterCallback[k] = v
		}
	}

	return f
}

func (f *FeeInput) SetTaxiSpecialByQuotation(ctx context.Context, prov FeeDescByQuotationEngineInputProvider) *FeeInput {
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdUnioneSpecialPrice {
		if prov.GetPreTotalFee() > 0 && prov.GetCapPrice() > 0 {
			preTotalFee := prov.GetPreTotalFee() * 100
			capPrice := prov.GetCapPrice() * 100
			specialFee := (preTotalFee - capPrice) / 100
			f.TaxiSpecial = util.ValidFloat64(specialFee)
		}
	}
	return f
}

func (f *FeeInput) DeleteFeeDetailInfo(key string) *FeeInput {
	if _, ok := f.FeeDetailInfo[key]; ok {
		delete(f.FeeDetailInfo, key)
	}
	return f
}
