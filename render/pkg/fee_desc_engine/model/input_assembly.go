package model

import (
	"context"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"

	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
)

func checkAmount(amount float64) bool {
	if amount <= 0 {
		return false
	}

	return true
}

func (f *FeeInput) WithDynamicProtect(ctx context.Context, amount float64) *FeeInput {
	if !checkAmount(amount) {
		return f
	}

	f.DynamicProtect = &amount

	return f
}

func (f *FeeInput) WithTaxiSpecial(ctx context.Context, amount float64) *FeeInput {
	if !checkAmount(amount) {
		return f
	}

	f.TaxiSpecial = &amount

	return f
}

func (f *FeeInput) WithCoupon(ctx context.Context, amount float64, customTag string) *FeeInput {
	if !checkAmount(amount) {
		return f
	}

	f.CouponInfo = &Coupon{
		CustomTag: customTag,
		Amount:    amount,
	}

	return f
}

func (f *FeeInput) WithZ<PERSON><PERSON>ubao(ctx context.Context, amount float64, mile float64) *FeeInput {
	if !checkAmount(amount) {
		return f
	}

	f.Ziyoubao = &Ziyoubao{
		Amount: amount,
		Mile:   mile,
	}

	return f
}

func (f *FeeInput) WithDiscountInfo(ctx context.Context, discountInfo map[string]float64) *FeeInput {
	if len(discountInfo) <= 0 {
		return f
	}

	f.DiscountInfo = discountInfo

	return f
}

func (f *FeeInput) WithBonus(ctx context.Context, amount float64) *FeeInput {
	if !checkAmount(amount) {
		return f
	}

	if f.DiscountInfo == nil {
		f.DiscountInfo = make(map[string]float64)
	}

	f.DiscountInfo[consts.FeeBonus] = amount

	return f
}

func (f *FeeInput) WithRevolvingAccountRebate(ctx context.Context, amount float64, activityType int32, multiple int32) *FeeInput {
	if !checkAmount(amount) {
		return f
	}

	if f.DiscountInfo == nil {
		f.DiscountInfo = make(map[string]float64)
	}

	f.DiscountInfo[consts.FeeRevolvingRebate] = amount

	return f
}

func (f *FeeInput) WithRevolvingAccountDiscount(ctx context.Context, amount float64) *FeeInput {
	if !checkAmount(amount) {
		return f
	}

	if f.DiscountInfo == nil {
		f.DiscountInfo = make(map[string]float64)
	}

	f.DiscountInfo[consts.FeeRevolvingDiscount] = amount

	return f
}

func (f *FeeInput) WithFeeDetailInfo(ctx context.Context, feeDetailInfo map[string]float64) *FeeInput {
	f.FeeDetailInfo = feeDetailInfo

	return f
}

func (f *FeeInput) WithCarpoolChildTicketFee(ctx context.Context, displayLines map[string]*PriceApi.DisplayLine) *FeeInput {
	if displayLine, ok := displayLines[consts.FeeCarpoolChildTicketFee]; ok {
		if f.FeeDetailInfo == nil {
			f.FeeDetailInfo = make(map[string]float64)
		}

		f.FeeDetailInfo[consts.FeeCarpoolChildTicketFee] = displayLine.Value
	}

	return f
}

func (f *FeeInput) WithCarpoolInfantsTicketFee(ctx context.Context, displayLines map[string]*PriceApi.DisplayLine) *FeeInput {
	if displayLine, ok := displayLines[consts.FeeCarpoolInfantsTicketFee]; ok {
		if f.FeeDetailInfo == nil {
			f.FeeDetailInfo = make(map[string]float64)
		}

		f.FeeDetailInfo[consts.FeeCarpoolInfantsTicketFee] = displayLine.Value
	}

	return f
}
