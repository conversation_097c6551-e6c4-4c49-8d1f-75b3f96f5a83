package input

import (
	"context"
	"testing"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"github.com/stretchr/testify/assert"
)

// mock provider for FeeDescByQuotationEngineInputProvider
type mockQuotationProvider struct {
	tunnelFees   map[string]float64
	dynamicTimes float64
	couponAmount float64
}

func (m *mockQuotationProvider) GetEstimateFee() float64 {
	//TODO implement me
	panic("implement me")
}

func (m *mockQuotationProvider) GetPreTotalFee() float64 {
	//TODO implement me
	panic("implement me")
}

func (m *mockQuotationProvider) GetCapPrice() float64 {
	//TODO implement me
	panic("implement me")
}

func (m *mockQuotationProvider) GetOrderType() int16 {
	//TODO implement me
	panic("implement me")
}

func (m *mockQuotationProvider) GetAlipayCoupon() *PriceApi.CouponInfoV2 {
	//TODO implement me
	panic("implement me")
}

func (m *mockQuotationProvider) GetMixedPaymentInfo(ctx context.Context) *price_api.MixedPaymentInfo {
	//TODO implement me
	panic("implement me")
}

func (m *mockQuotationProvider) IsBusinessPay() bool {
	//TODO implement me
	panic("implement me")
}

func (m *mockQuotationProvider) GetProductCategory() int64 {
	//TODO implement me
	panic("implement me")
}

func (m *mockQuotationProvider) GetCityID() int {
	//TODO implement me
	panic("implement me")
}

func (m *mockQuotationProvider) GetProductId() int64 {
	//TODO implement me
	panic("implement me")
}

func (m *mockQuotationProvider) GetTunnelFeeDetail() map[string]float64 {
	return m.tunnelFees
}
func (m *mockQuotationProvider) GetDynamicTimes() float64 {
	return m.dynamicTimes
}
func (m *mockQuotationProvider) GetDiscountDescByTypes(ctx context.Context, strings []string) *price_api.DiscountItem {
	return &price_api.DiscountItem{Amount: m.couponAmount}
}

// 其它接口用不到，dummy实现
func (m *mockQuotationProvider) GetLevelID() int64                                    { return 0 }
func (m *mockQuotationProvider) IsHitDynamicIconAb(_ context.Context) bool            { return false }
func (m *mockQuotationProvider) GetTCDiscountFee() float64                            { return 0 }
func (m *mockQuotationProvider) GetFeeDetailInfo() map[string]float64                 { return nil }
func (m *mockQuotationProvider) GetFeeExtraInfo(_ context.Context) map[string]float64 { return nil }

func Test_getHkFeeExtraInfoByQuotation(t *testing.T) {
	ctx := context.Background()
	prov := &mockQuotationProvider{tunnelFees: map[string]float64{"a": 10.5, "b": 20.3}}
	ret := getHkFeeExtraInfoByQuotation(ctx, prov)
	assert.Equal(t, 30.8, ret[consts.FeeHkTunnelFee])

	prov = &mockQuotationProvider{tunnelFees: map[string]float64{}}
	ret = getHkFeeExtraInfoByQuotation(ctx, prov)
	assert.Empty(t, ret)
}

func Test_getHkDiscountInfoByQuotation(t *testing.T) {
	ctx := context.Background()
	prov := &mockQuotationProvider{couponAmount: 12.345}
	ret := getHkDiscountInfoByQuotation(ctx, prov)
	assert.Equal(t, 12.34, ret[consts.FeeHkCoupon])

	prov = &mockQuotationProvider{couponAmount: -1}
	ret = getHkDiscountInfoByQuotation(ctx, prov)
	assert.Empty(t, ret)

	prov = &mockQuotationProvider{couponAmount: -999} // nil coupon
	ret = getHkDiscountInfoByQuotation(ctx, prov)
	assert.Empty(t, ret)
}

func Test_getHkFeeDetailInfoByQuotation(t *testing.T) {
	ctx := context.Background()
	prov := &mockQuotationProvider{dynamicTimes: 1.5}
	ret := getHkFeeDetailInfoByQuotation(ctx, prov)
	assert.Equal(t, 1.5, ret[consts.HkDynamicPrice])

	prov = &mockQuotationProvider{dynamicTimes: 0}
	ret = getHkFeeDetailInfoByQuotation(ctx, prov)
	assert.Empty(t, ret)
}
