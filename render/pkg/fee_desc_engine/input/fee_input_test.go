package input

import (
	"context"
	"testing"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type EstimateV3Adapter struct {
	*biz_runtime.ProductInfoFull
}

func (a *EstimateV3Adapter) GetBonus() float64 {
	fee := a.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}
	amount := fee.GetFeeDetail().GetBonusAmount()
	if amount == nil {
		return 0
	}
	return *amount
}

func (a *EstimateV3Adapter) GetFastEstimatePrice() float64 {
	return a.BaseReqData.CommonBizInfo.FastCarPrice
}

func TestBuildHKTaxiFeeInput(t *testing.T) {
	hu := &PriceApi.EstimateNewFormData{
		BillInfo: &PriceApi.EstimateNewFormBillInfo{
			DynamicTimes: 0.1,
		},
	}
	product := &biz_runtime.ProductInfoFull{
		Product: &models.Product{
			ProductCategory: 12200,
		},
	}
	product.SetHu(hu)
	prov := &EstimateV3Adapter{ProductInfoFull: product}
	ctx := context.TODO()
	HkFeeInput := BuildHkProductFeeInput(ctx, prov)
	// 动调数据是否存在
	if HkFeeInput.FeeDetailInfo == nil {
		t.Error("FeeDetailInfo is nil")
	}
}
