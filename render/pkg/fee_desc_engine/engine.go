package fee_desc_engine

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/handler"
	model2 "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
)

type Engine struct {
	input *model2.FeeInput
	env   *model2.Env

	productCategory int64
}

func NewDefaultEnv() *model2.Env {
	return &model2.Env{
		Form:       consts.DefaultForm,
		IsShareCap: true,
		ShareCap:   2,

		DcmpKey: consts.FeeDetailDesc,
	}
}

func NewEnv(form int32) *model2.Env {
	return &model2.Env{
		Form:       form,
		IsShareCap: true,
		ShareCap:   2,
		DcmpKey:    consts.AnyCarV3FeeDesc,
	}
}

func NewFeeEngine(input *model2.FeeInput, env *model2.Env) *Engine {
	if env == nil {
		env = NewDefaultEnv()
	}
	return &Engine{
		input: input,
		env:   env,
	}
}

func (e *Engine) Do(ctx context.Context) []*model2.FeeOutput {
	return handler.NewHandler(e.input, e.env).SetProductCategory(e.productCategory).Do(ctx)
}

func (e *Engine) SetProductCategory(productCategory int64) *Engine {
	e.productCategory = productCategory

	return e
}
