package fee_desc_carpool_render

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	// "git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
)

// GetRedPacketFee 加价项:春节服务费
func (c *CarpoolFeeDescBase) GetRedPacketFee(ctx context.Context) (map[string]float64, bool) {

	uidKey, params := c.prov.ApolloParamsGen(apollo_model.WithUIDKey)

	if !apollo.FeatureToggle(ctx, "gs_holiday_fee_fee_desc", uidKey, params) {
		return nil, false
	}

	res := make(map[string]float64)
	for k, v := range c.feeMap {
		if v == nil {
			return nil, false
		}
		if c.feeDetaillInfo == nil {
			return nil, false
		}
		if c.feeDetaillInfo[k] == nil {
			return nil, false
		}
		if c.feeDetaillInfo[k][FeeRedPacket] <= 0 {
			return nil, false
		}
		res[k] = c.feeDetaillInfo[k][FeeRedPacket]
	}

	return res, true

}

// GetBusinessPayDeduction 减价项： 企业支付
func (c *CarpoolFeeDescBase) GetBusinessPayDeduction(ctx context.Context) (map[string]float64, bool) {
	if !c.prov.IsBusinessPay() {
		return nil, false
	}
	res := make(map[string]float64)
	for k, v := range c.feeMap {
		if v == nil {
			return nil, false
		}
		res[k] = v.GetFee()
	}

	return res, false

}

// GetMemberDiscountCard 折上折
func (c *CarpoolFeeDescBase) GetMemberDiscountCard(ctx context.Context) (map[string]float64, bool) {
	res := make(map[string]float64)
	for k, v := range c.feeMap {
		if v == nil {
			return nil, false
		}
		if v.GetFeeDetail() == nil {
			return nil, false
		}
		if v.GetFeeDetail().GetDiscountCard() == nil || v.GetFeeDetail().GetDiscountCard().Amount <= 0 {
			return nil, false
		}
		fee := v.GetRawBill().DynamicTotalFee - v.GetFee()
		if fee < 0 {
			fee = 0
		}
		if c.feeDetaillInfo != nil && c.feeDetaillInfo[k] != nil {
			if c.feeDetaillInfo[k]["sps_carpool_discount_surprised_two"] != 0 {
				fee -= c.feeDetaillInfo[k]["sps_carpool_discount_surprised_two"]
			}
			if c.feeDetaillInfo[k]["sps_carpool_discount_surprised"] != 0 {
				fee -= c.feeDetaillInfo[k]["sps_carpool_discount_surprised"]
			}
		}

		res[k] = fee
	}

	return res, false
}

// GetNormalDiscount 多减价项
func (c *CarpoolFeeDescBase) GetNormalDiscount(ctx context.Context) (map[string]float64, bool) {
	res := make(map[string]float64)
	for k, v := range c.feeMap {
		if v == nil {
			return nil, false
		}
		fee := float64(0)

		fee = v.GetRawBill().DynamicTotalFee - v.GetFee()
		if fee < 0 {
			fee = 0
		}
		if c.feeDetaillInfo != nil && c.feeDetaillInfo[k] != nil {
			if c.feeDetaillInfo[k]["sps_carpool_discount_surprised_two"] != 0 {
				fee -= c.feeDetaillInfo[k]["sps_carpool_discount_surprised_two"]
			}
			if c.feeDetaillInfo[k]["sps_carpool_discount_surprised"] != 0 {
				fee -= c.feeDetaillInfo[k]["sps_carpool_discount_surprised"]
			}
		}
		res[k] = fee
	}
	return res, false
}

// GetCoupon 获取会员券展示
func (c *CarpoolFeeDescBase) GetCoupon(ctx context.Context) (map[string]float64, bool) {
	res := make(map[string]float64)
	for k, v := range c.feeMap {
		if v == nil {
			return nil, false
		}

		if v.GetFeeDetail() == nil {
			return nil, false
		}
		if v.GetFeeDetail().GetCoupon() != nil && v.GetFeeDetail().GetCoupon().Amount >= 0 && v.GetFeeDetail().GetCoupon().Tag == "member_v3" {
			res[k] = v.GetFeeDetail().GetCoupon().Amount
		}
	}
	return res, false
}
