package toast_tip

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_category"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/nuwa/trace"
	"strings"
)

const (
	PayTypeForFriend       = 100
	BusinessPayByPersonNew = 1
	EnterprisePayType      = 21
)

type ToastTipProvider interface {
	GetPageType() int32
	GetPaymentsType() int32
	GetMultiRequireProduct() []models.RequireProduct
	GetStopoverPoints() *string
	GetCallCarType() int32
	GetDepartureTime() int64

	render.ApolloProvider
}

// GetToastTip 构建提示信息
func GetToastTip(ctx context.Context, prov ToastTipProvider, products []*biz_runtime.ProductInfoFull, estimateDataList map[int64]*proto.V3EstimateData, userPayInfo *proto.PaymentOptionModule) string {
	// 构建用户选中方式与勾选表单不匹配信息
	if content := buildPayTypeDisableToast(ctx, prov, estimateDataList, userPayInfo); content != "" {
		return content
	}

	// 构建小巴二次预估无车提示
	if content := buildDisabledToast(ctx, prov, estimateDataList); content != "" {
		return content
	}

	// 构建支付方式降级提示
	if content := buildPayTypeDegrade(ctx, prov, estimateDataList, userPayInfo); content != "" {
		return content
	}

	// Toast 亲友代付仅支持线上结算
	if content := buildPayTypeForFriend(ctx, prov, estimateDataList, userPayInfo); content != "" {
		return content
	}

	// 同起同终toast
	if content := buildNearStartDestinationToast(ctx, products); content != "" {
		return content
	}

	return ""
}

type PayTypeDisabledToast struct {
	EnumCarLevelMsg     string `json:"enum_car_level_msg"`
	ShowPartCarLevelMsg string `json:"show_part_car_level_msg"`
	DefaultMsg          string `json:"default_msg"`
}

func buildPayTypeDisableToast(ctx context.Context, prov ToastTipProvider, estimateDataList map[int64]*proto.V3EstimateData, userPayInfo *proto.PaymentOptionModule) string {
	var config *PayTypeDisabledToast
	paymentType := prov.GetPaymentsType()
	if paymentType <= 0 || paymentType == BusinessPayByPersonNew {
		return ""
	}

	// 获取选中列表中不支持的车型信息
	multiCarLevelNotSupport := false
	var notSupportCarLevelMsg []string
	for _, estimateData := range estimateDataList {
		if estimateData.GetIsSelected() == 1 &&
			paymentType != util.ToInt32(estimateData.GetUserPayInfo().GetPaymentId()) {
			notSupportCarLevelMsg = append(notSupportCarLevelMsg, estimateData.GetCarTitle())
			if len(notSupportCarLevelMsg) >= 2 {
				multiCarLevelNotSupport = true
				break
			}
		}
	}

	if len(notSupportCarLevelMsg) == 0 {
		return ""
	}

	// 获取支付方式文案
	paymentTypeText := ""
	if userPayInfo != nil {
		for _, payment := range userPayInfo.PaymentList {
			if paymentType == payment.Tag {
				paymentTypeText = payment.Msg
				break
			}
		}
	}

	dcmpContent := dcmp.GetDcmpContent(ctx, "estimate_dache_anycar-pay_type_not_support", map[string]string{
		"car_level_msg": strings.Join(notSupportCarLevelMsg, "、"),
		"pay_type_msg":  paymentTypeText,
	})
	err := json.Unmarshal([]byte(dcmpContent), &config)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "payTypeDisabled unmarshal json fail, %s", err.Error())
		return ""
	}

	disableMsg := config.EnumCarLevelMsg
	if multiCarLevelNotSupport {
		disableMsg = config.ShowPartCarLevelMsg
	}

	if paymentTypeText == "" {
		disableMsg = config.DefaultMsg
	}

	return disableMsg
}

type MiniBusToast struct {
	ProductCategoryS []int64 `json:"productcategorys"`
	ToastTip         string  `json:"toast_tip"`
}

// buildDisabledToast 构建小巴二次预估无车提示
func buildDisabledToast(ctx context.Context, prov ToastTipProvider, estimateDataList map[int64]*proto.V3EstimateData) string {
	var miniBusToast *MiniBusToast
	content := dcmp.GetDcmpContent(ctx, "minibus-toast", nil)
	if content == "" {
		return ""
	}

	err := json.Unmarshal([]byte(content), &miniBusToast)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "miniBusToast json unmarshal err: %s", err.Error())
		return ""
	}

	nowAppear := false
	preAppear := false
	for _, requireProduct := range prov.GetMultiRequireProduct() {
		if util.InArrayInt64(requireProduct.ProductCategory, miniBusToast.ProductCategoryS) {
			preAppear = true
		}
	}

	for _, estimateData := range estimateDataList {
		if util.InArrayInt64(estimateData.ProductCategory, miniBusToast.ProductCategoryS) {
			nowAppear = true
		}
	}

	if preAppear && !nowAppear && (prov.GetStopoverPoints() == nil || *prov.GetStopoverPoints() == "") &&
		prov.GetCallCarType() == 0 && prov.GetDepartureTime() == 0 {
		return miniBusToast.ToastTip
	}

	return ""
}

// buildPayTypeDegrade 构建支付方式降级提示
func buildPayTypeDegrade(ctx context.Context, prov ToastTipProvider, estimateDataList map[int64]*proto.V3EstimateData, userPayInfo *proto.PaymentOptionModule) string {
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	toggle := apollo.FeatureToggle(ctx, "gs_pay_type_degrade_toast", pidKey, params)
	if !toggle {
		return ""
	}

	isExistEnterprisePayType := false
	isPreExistEnterprisePayType := false

	// 检查当前预估数据中是否有企业支付
	for _, estimateData := range estimateDataList {
		if util.ToInt32(estimateData.UserPayInfo.GetPaymentId()) == EnterprisePayType {
			isExistEnterprisePayType = true
			break
		}
	}

	// 检查用户多需求产品中是否有企业支付
	multiRequireProduct := prov.GetMultiRequireProduct()
	for _, requireProduct := range multiRequireProduct {
		if requireProduct.PayType == EnterprisePayType {
			isPreExistEnterprisePayType = true
			break
		}
	}

	userSelectPaymentType := prov.GetPaymentsType()
	payDegrade := dcmp.GetJSONContentWithPath(ctx, "estimate_form_v3-toast", nil, "pay_degrade")
	if userSelectPaymentType == EnterprisePayType {
		if !isExistEnterprisePayType {
			return payDegrade
		}
	} else if userSelectPaymentType == -1 {
		if isPreExistEnterprisePayType && !isExistEnterprisePayType {
			return payDegrade
		}
	}

	return ""
}

// buildPayTypeForFriend 构建亲友代付提示
func buildPayTypeForFriend(ctx context.Context, prov ToastTipProvider, estimateList map[int64]*proto.V3EstimateData, userPayInfo *proto.PaymentOptionModule) string {
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	toggle := apollo.FeatureToggle(ctx, "gs_pay_for_friend_toast", pidKey, params)
	if !toggle {
		return ""
	}

	if userPayInfo == nil {
		return ""
	}

	toast := dcmp.GetJSONContentWithPath(ctx, "estimate_form_v3-pay_type_for_friend", nil, "toast")
	for _, payInfo := range userPayInfo.PaymentList {
		if payInfo.Tag == PayTypeForFriend && payInfo.IsSelected == 1 {
			return toast
		}
	}

	return ""
}

type NearStartDestinationToast struct {
	Toast       string `json:"toast"`
	MaxDistance int64  `json:"max_distance"`
	MinDistance int64  `json:"min_distance"`
}

// buildNearStartDestinationToast 起终点相近提示
func buildNearStartDestinationToast(ctx context.Context, estimateProducts []*biz_runtime.ProductInfoFull) string {
	var (
		toast *NearStartDestinationToast
		metre *int64
	)

	// 查找快车的距离信息
	for _, estimateData := range estimateProducts {
		if estimateData.Product != nil && estimateData.Product.ProductCategory == product_category.ProductCategoryFastCarNormal {
			metre = util.IntToInt64Ptr(int(estimateData.GetBillDriverMetre()))
			break
		}
	}

	content := dcmp.GetDcmpContent(ctx, "estimate_form_v3-near_start_destination_toast", nil)
	if content == "" {
		return ""
	}

	err := json.Unmarshal([]byte(content), &toast)
	if err != nil {
		log.Trace.Warnf(ctx, "nearStartDestinationToast json Unmarshal err: %s", err.Error())
		return ""
	}

	if metre != nil && *metre <= toast.MaxDistance && *metre >= toast.MinDistance {
		return toast.Toast
	}

	return ""
}

type ToastInfoConfig struct {
	TextColor   string `json:"text_color"`
	CarIcon     string `json:"car_icon"`
	BgColor     string `json:"bg_color"`
	BorderColor string `json:"border_color"`
	Text        string `json:"text"`
	ArrowIcon   string `json:"arrow_icon"`
}

func GetMoreToastTip(ctx context.Context, prov ToastTipProvider) *proto.MoreToastTipData {
	if prov.GetPageType() == page_type.PageTypeLankeBao {
		return nil
	}

	toastInfoConfig := &ToastInfoConfig{}
	dcmpContent := dcmp.GetDcmpContent(ctx, "estimate_form_v3-toast_info", nil)
	err := json.Unmarshal([]byte(dcmpContent), &toastInfoConfig)
	if err != nil {
		log.Trace.Warnf(ctx, "GetMoreToastTip json Unmarshal err: %s", err.Error())
		return nil
	}

	return &proto.MoreToastTipData{
		TextColor:   toastInfoConfig.TextColor,
		CarIcon:     toastInfoConfig.CarIcon,
		BgColor:     toastInfoConfig.BgColor,
		BorderColor: toastInfoConfig.BorderColor,
		Text:        toastInfoConfig.Text,
		ArrowIcon:   toastInfoConfig.ArrowIcon,
	}
}
