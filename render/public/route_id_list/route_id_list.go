package route_id_list

import (
	"context"
	CommonConsts "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/product"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	consts2 "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/render"
)

const EN_US = "en-US"

type RouteIdListProvider interface {
	GetLang() string
	GetAppVersion() string
	GetAccessKeyId() int32
	GetBillRouteIdList() []string

	render.ProductProvider
	render.ApolloProvider
}

func GetRouteIdList(ctx context.Context, prov RouteIdListProvider) (routeIdList []string) {

	if prov.GetOrderType() == 1 && util.CompareAppVersion(prov.GetAppVersion(), "6.1.4") < 0 {
		return nil
	}

	// 英文端 需要 版本大于6.1.4 || 9， 22无限制
	if EN_US == prov.GetLang() && util.CompareAppVersion(prov.GetAppVersion(), "6.1.4") < 0 && !util.InArrayInt32(prov.GetAccessKeyId(), []int32{9, 22}) {
		return nil
	}

	// 如果是香港品类
	if prov.GetProductId() == consts2.ProductIDHKTaixCar || product.IsHongKongThird(CommonConsts.ProductID(prov.GetProductId())) {
		if enableHkRoute(ctx, prov) {
			return prov.GetBillRouteIdList()
		} else {
			return nil
		}
	}

	if util.InArrayInt64(prov.GetProductId(), []int64{consts2.ProductIDUNITAXI, consts2.ProductIDBusinessTaixCar, consts2.ProductIDHKTaixCar}) &&
		!taxiNotAllowRoute(ctx, prov) {
		return nil
	}

	if carpool.IsCarpool(prov.GetCarpoolType()) {
		if prov.GetOrderType() == consts2.BookOrder {
			return nil
		}

		if carpool.IsCarpoolFlatRate(prov.GetCarpoolType()) {
			return nil
		}

		//过滤出租车拼车
		if consts2.ProductIDUNITAXI == prov.GetProductId() {
			return nil
		}
	}

	return prov.GetBillRouteIdList()
}

// 客户端，小程序 某些版本 直接不允许透出 或者直接允许透出
func enableHkRoute(ctx context.Context, prov RouteIdListProvider) bool {
	return (util.InArrayInt32(prov.GetAccessKeyId(), []int32{CommonConsts.AccessKeyIDDiDiAndroid, CommonConsts.AccessKeyIDDiDiIos}) && util.CompareAppVersion(prov.GetAppVersion(), "6.9.20") >= 0) ||
		(util.InArrayInt32(prov.GetAccessKeyId(), []int32{CommonConsts.AccessKeyIDDiDiAlipayMini, CommonConsts.AccessKeyIDDiDiWechatMini}) && util.CompareAppVersion(prov.GetAppVersion(), "6.9.90") >= 0) ||
		(util.InArrayInt32(prov.GetAccessKeyId(), []int32{CommonConsts.AccessKeyIDAlipayHKWebapp}) && util.CompareAppVersion(prov.GetAppVersion(), "6.9.66") >= 0)
}

// taxiNotAllowRoute 出租车选路ab实验
func taxiNotAllowRoute(ctx context.Context, prov RouteIdListProvider) bool {
	apolloKey, apolloParams := prov.GetApolloParams(biz_runtime.WithUIDKey)
	apolloParams["product_id"] = cast.ToString(prov.GetProductId())
	apolloParams["require_level"] = cast.ToString(prov.GetRequireLevel())
	apolloParams["combo_type"] = cast.ToString(prov.GetComboType())
	apolloParams["carpool_type"] = cast.ToString(prov.GetCarpoolType())
	apolloParams["level_type"] = cast.ToString(prov.GetLevelType())
	apolloParams["is_special_price"] = cast.ToString(util.ToInt32(prov.IsSpecialPrice()))
	toggleAllow, toggleAssignment := apollo.FeatureExp(ctx, "taxi_access_map_multi_route", apolloKey, apolloParams)

	if toggleAllow && "treatment_group" == toggleAssignment.GetGroupName() {
		return true
	}

	return false
}
