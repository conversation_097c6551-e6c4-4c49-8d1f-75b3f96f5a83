package fee_msg_template

import (
	"context"
	Currency "git.xiaojukeji.com/gulfstream/biz-common-go/v6/currency"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

func GetFeeMsgTemplate(ctx context.Context, estimateProducts []*biz_runtime.ProductInfoFull) string {
	currency := ""
	for _, estimateProduct := range estimateProducts {
		if estimateProduct.GetPriceInfo() == nil || estimateProduct.GetBillInfo() == nil {
			continue
		}

		if estimateProduct.GetBillInfo().Currency != "" {
			currency = estimateProduct.GetBillInfo().Currency
			break
		}
	}

	if currency != "" && currency != "CNY" {
		currencyUnit, _ := Currency.GetCurrencyUnit(ctx, currency)
		feeMsgTemplate := dcmp.GetJSONContentWithPath(ctx, "config_currency-fee_msg_template", nil, "estimate_operation_button_fee_tpl")
		if feeMsgTemplate != "" {
			return dcmp.TranslateTemplate(feeMsgTemplate, map[string]string{
				"unit": currencyUnit,
			})
		}
	}

	return ""
}
