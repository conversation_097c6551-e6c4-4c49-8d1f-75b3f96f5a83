package order_button_info

import (
	"context"
	"encoding/json"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v4_multi/experiment"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"math"
)

const (
	ShowTypeETS        = "ets"         // 预估到达时间
	ShowTypeETP        = "etp"         // 预估取车时间
	ShowTypeAnswerRate = "answer_rate" // 应答率

	DCMPKeyOrderButtonConfig = "estimate_new_form-order_button_info"

	ApolloKeyBlankObjSwitch   = "gs_order_button_blank_obj_switch"
	ApolloKeyFilterEtsFeature = "gs_filter_ets_feature_delete"
	ApolloKeyAthenaNewField   = "api_athena_expect_new_field_switch"

	TimeThresholdMinute = 60 // 60秒转分钟
	MaxEtsMinutes       = 30 // ETS最大分钟数限制
)

type OrderButtonInfoProvider interface {
	GetTabId() string
	GetOrderType() int16
	GetExpectInfo() *AthenaApiv3.AthenaBubbleExpectInfoResp
	GetEstimateStripInfo() *AthenaApiv3.EstimateStripInfo
	GetBaseReqData() *models.BaseReqData

	render.ApolloProvider
}

type OrderButtonInfoConfig struct {
	AnswerRateClassifyButton string               `json:"answer_rate_classify_button"`
	EtsMinuteClassifyButton  string               `json:"ets_minute_classify_button"`
	EtsSecClassifyButton     string               `json:"ets_sec_classify_button"`
	EtpMinuteClassifyButton  string               `json:"etp_minute_classify_button"`
	EtpSecClassifyButton     string               `json:"etp_sec_classify_button"`
	LeftButtonInfo           *proto.NavigationBar `json:"left_button_info"`
	SendOrderButtonTitle0    string               `json:"sendorder_button_title_0"`
	SendOrderButtonTitle1    string               `json:"sendorder_button_title_1"`
}

func GetOrderButtonInfo(ctx context.Context, prov OrderButtonInfoProvider) *proto.OrderButtonInfo {
	var (
		buttonInfo       *proto.OrderButtonInfo
		isReturnBlankObj bool // 检查是否需要返回空数组的灰度结果
	)

	orderButtonInfoConfig := &OrderButtonInfoConfig{}
	dcmpContent := dcmp.GetDcmpContent(ctx, DCMPKeyOrderButtonConfig, nil)
	_ = json.Unmarshal([]byte(dcmpContent), &orderButtonInfoConfig)

	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	allow := apollo.FeatureToggle(ctx, ApolloKeyFilterEtsFeature, pidKey, params)
	if allow || tab.IsClassifyTab(prov.GetTabId()) {
		if apollo.FeatureToggle(ctx, ApolloKeyAthenaNewField, pidKey, params) {
			buttonInfo = buildClassifyTabButtonInfoV2(ctx, prov.GetExpectInfo(), orderButtonInfoConfig)
		} else {
			buttonInfo = buildClassifyTabButtonInfo(ctx, prov.GetEstimateStripInfo(), orderButtonInfoConfig)
		}
	}

	formABParam := experiment.GetNewStyleFormABParamFromBaseReqData(ctx, prov.GetBaseReqData())
	if formABParam.IsHitNewOrderButtonTitle() {
		buttonInfo.SendorderButtonTitle = util.StringPtr(orderButtonInfoConfig.SendOrderButtonTitle0)
		if prov.GetOrderType() == consts.BookOrder {
			buttonInfo.SendorderButtonTitle = util.StringPtr(orderButtonInfoConfig.SendOrderButtonTitle1)
		}
	}

	if formABParam.IsHitNewNavigationBarNewSort() {
		buttonInfo.LeftButton = orderButtonInfoConfig.LeftButtonInfo
	}

	if apollo.FeatureToggle(ctx, ApolloKeyBlankObjSwitch, pidKey, params) {
		isReturnBlankObj = true
	}

	// 检查数据是否为空
	if isReturnBlankObj && buttonInfo == nil {
		return &proto.OrderButtonInfo{}
	}

	return buttonInfo
}

func buildClassifyTabButtonInfoV2(ctx context.Context, expectInfo *AthenaApiv3.AthenaBubbleExpectInfoResp, orderButtonInfoConfig *OrderButtonInfoConfig) *proto.OrderButtonInfo {
	if expectInfo == nil || expectInfo.GlobalSceneExpect == nil || expectInfo.GlobalSceneExpect.ExpectInfo == nil {
		return nil
	}

	showType := expectInfo.GlobalSceneExpect.ShowType
	expectData := expectInfo.GlobalSceneExpect.ExpectInfo
	switch showType {
	case ShowTypeETS:
		if expectData.GetEts() <= 0 {
			return nil
		}

		if expectData.GetEts() >= TimeThresholdMinute {
			newEts := int32(math.Ceil(float64(expectData.GetEts()) / TimeThresholdMinute))
			if newEts > MaxEtsMinutes {
				return nil
			} else {
				return &proto.OrderButtonInfo{
					ExpectInfoText: dcmp.TranslateTemplate(orderButtonInfoConfig.EtsMinuteClassifyButton, map[string]string{
						"num": util.ToString(math.Ceil(float64(expectData.GetEts()) / TimeThresholdMinute)),
					}),
				}
			}
		}

		return &proto.OrderButtonInfo{
			ExpectInfoText: dcmp.TranslateTemplate(orderButtonInfoConfig.EtsSecClassifyButton, map[string]string{
				"num": util.ToString(expectData.GetEts()),
			}),
		}
	case ShowTypeETP:
		if expectData.GetEtp() <= 0 {
			return nil
		}

		if expectData.GetEtp() >= TimeThresholdMinute {
			return &proto.OrderButtonInfo{
				ExpectInfoText: dcmp.TranslateTemplate(orderButtonInfoConfig.EtpMinuteClassifyButton, map[string]string{
					"num": util.ToString(math.Ceil(float64(expectData.GetEtp()) / TimeThresholdMinute)),
				}),
			}
		} else {
			return &proto.OrderButtonInfo{
				ExpectInfoText: dcmp.TranslateTemplate(orderButtonInfoConfig.EtpSecClassifyButton, map[string]string{
					"num": util.ToString(expectData.GetEtp()),
				}),
			}
		}
	case ShowTypeAnswerRate:
		if expectData.GetAnswerRate() <= 0 {
			return nil
		}

		answerRate := int32(math.Round(expectData.GetAnswerRate() * 100))
		return &proto.OrderButtonInfo{
			ExpectInfoText: dcmp.TranslateTemplate(orderButtonInfoConfig.AnswerRateClassifyButton, map[string]string{
				"num": util.ToString(answerRate),
			}),
		}
	default:
		return nil
	}
}

func buildClassifyTabButtonInfo(ctx context.Context, estimateStripInfo *AthenaApiv3.EstimateStripInfo, orderButtonInfoConfig *OrderButtonInfoConfig) *proto.OrderButtonInfo {
	if estimateStripInfo == nil || estimateStripInfo.GetExtraInfo() == nil {
		return nil
	}

	showType, ok := estimateStripInfo.ExtraInfo["button_show_type"]
	if !ok {
		return nil
	}

	switch showType {
	case ShowTypeETS:
		if estimateStripInfo.GetEts() <= 0 {
			return nil
		}

		if estimateStripInfo.GetEts() >= TimeThresholdMinute {
			return &proto.OrderButtonInfo{
				ExpectInfoText: dcmp.TranslateTemplate(orderButtonInfoConfig.EtsMinuteClassifyButton, map[string]string{
					"num": util.ToString(math.Ceil(float64(estimateStripInfo.GetEts()) / TimeThresholdMinute)),
				}),
			}
		} else {
			return &proto.OrderButtonInfo{
				ExpectInfoText: dcmp.TranslateTemplate(orderButtonInfoConfig.EtsSecClassifyButton, map[string]string{
					"num": util.ToString(math.Ceil(float64(estimateStripInfo.GetEts()) / TimeThresholdMinute)),
				}),
			}
		}
	case ShowTypeETP:
		if estimateStripInfo.GetEtp() <= 0 {
			return nil
		}

		if estimateStripInfo.GetEtp() >= TimeThresholdMinute {
			return &proto.OrderButtonInfo{
				ExpectInfoText: dcmp.TranslateTemplate(orderButtonInfoConfig.EtpMinuteClassifyButton, map[string]string{
					"num": util.ToString(math.Ceil(float64(estimateStripInfo.GetEtp()) / TimeThresholdMinute)),
				}),
			}
		} else {
			return &proto.OrderButtonInfo{
				ExpectInfoText: dcmp.TranslateTemplate(orderButtonInfoConfig.EtpSecClassifyButton, map[string]string{
					"num": util.ToString(estimateStripInfo.GetEtp()),
				}),
			}
		}
	case ShowTypeAnswerRate:
		if estimateStripInfo.GetAnswerRate() <= 0 {
			return nil
		}

		return &proto.OrderButtonInfo{
			ExpectInfoText: dcmp.TranslateTemplate(orderButtonInfoConfig.AnswerRateClassifyButton, map[string]string{
				"num": util.ToString(estimateStripInfo.GetAnswerRate()),
			}),
		}
	default:
		return nil
	}
}
