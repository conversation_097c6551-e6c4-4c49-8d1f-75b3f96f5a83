package carpool_seat_module_render

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

type InterCityCarpoolSeatModule interface {
	GetMaxSeatNum() int32
	GetUserSelectSeatNum() int32
}

func InterCityCarpool(ctx context.Context, module InterCityCarpoolSeatModule) *proto.InterCityCarpoolSeatModule {
	var (
		resp = new(proto.InterCityCarpoolSeatModule)
	)

	resp.Title = dcmp.GetSubContent(ctx, "inter_city_carpool-carpool_seat_module", "title")
	resp.MaxCount = module.GetMaxSeatNum()
	resp.SelectCount = module.GetUserSelectSeatNum()
	toastTpl := dcmp.GetSubContent(ctx, "inter_city_carpool-carpool_seat_module", "seats_exceed_toast")
	resp.SeatsExceedToast = dcmp.TranslateTemplate(toastTpl, map[string]string{"max_num": strconv.FormatInt(int64(module.GetMaxSeatNum()), 10)})
	return resp
}
