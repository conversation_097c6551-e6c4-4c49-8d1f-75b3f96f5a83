package charter_combo

import (
	"context"
	"fmt"
	plutusSerivce "git.xiaojukeji.com/dirpc/dirpc-go-http-Plutus"
	plutus "git.xiaojukeji.com/gulfstream/mamba/dao/rpc/plutus"
)

func getComboStrategy(ctx context.Context, productIds []int, district string, timestamp int64) (map[int]map[int]*ComboPackage, error) {
	multiReqs := make([]*plutusSerivce.GetStrategiesRequest, 0, len(productIds))
	for _, productId := range productIds {
		comboType, ok := productId2ComboType[productId]
		if !ok {
			continue
		}

		r := &plutusSerivce.GetStrategiesRequest{
			District:      district,
			ProductId:     int32(productId),
			ComboType:     int64(comboType),
			Role:          2,
			DepartureTime: timestamp,
			Sign:          true,
		}

		multiReqs = append(multiReqs, r)
	}

	if len(multiReqs) == 0 {
		return nil, fmt.Errorf("no invalid charter productIds")
	}

	req := &plutusSerivce.GetMultiStrategiesRequest{MultiRequest: multiReqs}
	resp, err := plutus.GetMultiStrategies(ctx, req)
	if err != nil {
		return nil, err
	}

	res := make(map[int]map[int]*ComboPackage, len(resp.Data.MultiResponse))

	for _, item := range resp.Data.MultiResponse {
		if item == nil || item.Strategies == nil || len(item.Strategies) == 0 {
			continue
		}

		var productId int
		packages := make([]*ComboPackage, 0, len(item.Strategies))

		for _, strategy := range item.Strategies {
			if strategy == nil {
				continue
			}

			if productId == 0 {
				productId = int(strategy.ProductId)
			} else if productId != int(strategy.ProductId) { // 正常不会出现这种情况
				continue
			}

			pack := &ComboPackage{
				ComboId:  int(strategy.ComboId),
				Amount:   strategy.Package,
				Distance: strategy.PackageDistance,
				Hour:     int(strategy.PackageTime / 60), // 分钟 -> 小时
				Time:     strategy.PackageTime,
			}

			packages = append(packages, pack)
		}

		res[productId] = mergeComboStrategies(packages)
	}

	return res, nil
}

// 将每条产品线下的所有套餐，聚合为combo_id维度
func mergeComboStrategies(packages []*ComboPackage) map[int]*ComboPackage {
	res := map[int]*ComboPackage{}
	for _, cur := range packages {
		combo, ok := res[cur.ComboId]
		if !ok || combo == nil {
			res[cur.ComboId] = cur
		}

		combo, _ = res[cur.ComboId]
		if cur.Amount < combo.Amount {
			combo.Amount = cur.Amount
		}

		if cur.Distance < combo.Distance {
			combo.Distance = cur.Distance
		}
	}

	return res
}
