package charter_combo

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/s3e/x-engine/condition"
)

// loadTimeSliceConf load当前城市相关产品线下所有套餐的时间片信息（用缓存？）
func loadTimeSliceConf(ctx context.Context, productIds []int, area int, accessKeyId int, appVersion string) map[int]*TimeSliceInfo {
	ret := make(map[int]*TimeSliceInfo, len(productIds))

	for _, productId := range productIds {
		params := &comboConditionParam{ProductId: productId, City: area, AccessKeyId: accessKeyId, AppVersion: appVersion}
		res, err := condition.Check(ctx, CharterComboTimeSliceNS, params, condition.WithConfName(CharterComboTimeSliceConfName))
		if res == nil || err != nil {
			log.Trace.Warnf(ctx, LogTag, "comboTimeSlice x-engine check failed, params=%+v, res=%+v, error=%s", params, res, err.Error())
			return nil
		}

		if !res.IsAllow {
			log.Trace.Warnf(ctx, LogTag, "comboTimeSlice x-engine check not allowed, params=%+v， reason=%s", params, res.Reason)
			return nil
		}

		material := &comboTimeSliceMaterial{}
		err = res.GetMaterial(material)
		if err != nil {
			log.Trace.Warnf(ctx, LogTag, "comboTimeSlice x-engine get material failed, material=%+v, err=%s, res=%+v", res.Material, err.Error(), res)
			return nil
		}

		ts := map[int]*ComboTimeSlice{}
		if err := json.Unmarshal([]byte(material.TimeSliceConf), &ts); err != nil {
			log.Trace.Warnf(ctx, LogTag, "comboTimeSlice unmarshal ComboTimeSlice failed, material=%+v, err=%s", material, err.Error())
			return nil
		}

		ret[productId] = &TimeSliceInfo{
			TimeSliceConf:    ts,
			TimePageTitle:    material.TimePageTitle,
			TimePageSubTitle: material.TimePageSubTitle,
		}
	}

	return ret
}

func loadOpenComboIDs(ctx context.Context) map[int]map[int]struct{} {
	raw := dcmp.GetDcmpContent(ctx, CharterComboListDcmpKey, nil)
	if raw == "" {
		return nil
	}

	tmp := make(map[int][]int)
	err := json.Unmarshal([]byte(raw), &tmp)
	if err != nil {
		log.Trace.Warnf(ctx, LogTag, "unmarshal combo_list dcmp failed,raw=%s,err=%s", raw, err.Error())
		return nil
	}

	res := make(map[int]map[int]struct{}, len(tmp))
	for productId, comboIDs := range tmp {
		res[productId] = make(map[int]struct{}, len(comboIDs))
		for _, comboID := range comboIDs {
			res[productId][comboID] = struct{}{}
		}
	}

	return res
}
