package car_info

import (
	"context"
	"encoding/json"
	"strconv"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/s3e/x-engine/activity"
	s3e_model "git.xiaojukeji.com/s3e/x-engine/activity/model"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"

	TaxiUtil "git.xiaojukeji.com/gulfstream/mamba/common/biz_util/taxi"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

type CarIconProvider interface {
	GetProductCategory() int64
	GetSubGroupId() int32
	GetCityID() int
	GetUserPID() int64
	GetAppVersion() string
	GetAccessKeyId() int32
	GetProductId() int64
	GetLang() string
	GetPageType() int32

	GetPricingBoxData() *TaxiUtil.PricingBoxData
	GetPrivateBizInfo() *models.PrivateBizInfo

	render.ApolloProvider
}

type RegionalConfOuter struct {
	Configs []RegionalConf `json:"icon_regional_configs"`
}

func GetCarIcon(ctx context.Context, prov CarIconProvider) string {

	var carIcon string

	if estimate_pc_id.EstimatePcIdBargain == prov.GetProductCategory() {
		if prov.GetPrivateBizInfo() != nil && prov.GetPrivateBizInfo().BargainData.SenseConfig != nil {
			return prov.GetPrivateBizInfo().BargainData.SenseConfig.Icon
		}
	}

	// 从青铜门获取
	carIcon = getCarIconFromBronzeDoor(ctx, prov)
	if carIcon != "" {
		return carIcon
	}

	// 计价盒子
	if prov.GetSubGroupId() == consts.SubGroupIdTaxiPricingBox {
		icon := TaxiUtil.GetCarIconByPcId(ctx, prov.GetProductCategory())
		if len(icon) > 0 {
			return icon
		}
	}

	return GetCarNormalIcon(ctx, prov)
}

type CarNormalIconProvider interface {
	GetProductCategory() int64
	GetCityID() int
	render.ApolloProvider
}

func GetCarNormalIcon(ctx context.Context, prov CarNormalIconProvider) string {
	icon := getCarIconByFormdataMateriel(ctx, prov)
	if icon == "" {
		log.Trace.Infof(ctx, "_car_icon_miss",
			"car_icon miss for product_category %d", prov.GetProductCategory())
		return DefaultCarIcon
	}
	return icon
}

func getCarIconFromBronzeDoor(ctx context.Context, prov CarIconProvider) string {

	activityName := ActivityPrefix_ProductCategory + strconv.FormatInt(prov.GetProductCategory(), 10)

	param := ApolloModel.NewUser("").
		With("pid", strconv.FormatInt(prov.GetUserPID(), 10)).
		With("activity_name", activityName)
	toggle, err := ApolloSDK.FeatureToggle("gs_get_car_info_by_bronze_switch", param)

	if err != nil || !toggle.IsAllow() {
		return ""
	}

	commonRes := &s3e_model.CommonRequest{
		CityId:          prov.GetCityID(),
		AppVersion:      prov.GetAppVersion(),
		Pid:             strconv.FormatInt(prov.GetUserPID(), 10),
		AccessKeyId:     int(prov.GetAccessKeyId()),
		Lang:            prov.GetLang(),
		ProductCategory: int(prov.GetProductCategory()),
		TagCaller:       "mamba",
	}
	var management *activity.MaterialManagement
	switch prov.GetPageType() {
	case page_type.PageTypeUndefined:
		management = activity.NewMaterialManagement(activityName, Page, ComponentName, commonRes)
	case page_type.PageTypeGuideAnyCar:
		management = activity.NewMaterialManagement(activityName, MainFormPage, MainFormComponentName, commonRes)
	}

	if management == nil {
		return ""
	}

	// 添加额外参数
	extraErr := management.SetExtraParamV2(getIconExtraParams(prov))
	if extraErr != nil {
		return ""
	}

	material, err := management.Check(ctx)
	if err != nil || material == nil || material.Hit == false {
		return ""
	}

	bs, err := json.Marshal(material.Material)
	if err != nil {
		return ""
	}

	carData := &models.CarInfo{}
	err = json.Unmarshal(bs, carData)
	if err != nil || carData == nil {
		return ""
	}

	return carData.CarIcon
}

func getIconExtraParams(prov CarIconProvider) map[string]interface{} {
	extraParams := make(map[string]interface{})
	extraParams["product_id"] = prov.GetProductId()
	extraParams["tag_caller"] = "mamba"
	return extraParams
}
