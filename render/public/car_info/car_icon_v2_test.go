package car_info

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/combo_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"github.com/bytedance/mockey"
	"testing"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/carpool_type"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/stretchr/testify/assert"
)

type mockCarIconProvider struct {
	biz_runtime.ProductInfoFull
	tabId             string
	estimateStyleType int32
}

func (m *mockCarIconProvider) GetTabId() string {
	return m.tabId
}

func (m *mockCarIconProvider) GetEstimateStyleType() int32 {
	return m.estimateStyleType
}

// TestGetCarIconType 测试GetCarIconType函数
func TestGetCarIconType(t *testing.T) {
	tests := []struct {
		name     string
		provider *mockCarIconProvider
		expected int32
		allow    bool
	}{
		{
			name: "非三方表单应返回CarIconTypeNormal",
			provider: &mockCarIconProvider{
				tabId: "normal",
			},
			expected: CarIconTypeNormal,
		},
		{
			name: "非主表单应返回CarIconTypeNormal",
			provider: &mockCarIconProvider{
				ProductInfoFull: biz_runtime.ProductInfoFull{
					BaseReqData: &models.BaseReqData{
						CommonInfo: models.CommonInfo{
							PageType: 10,
						},
					},
				},
				tabId: ClassTab,
			},
			expected: CarIconTypeNormal,
		},
		{
			name: "城际非拼车应返回CarIconTypeNormal",
			provider: &mockCarIconProvider{
				ProductInfoFull: biz_runtime.ProductInfoFull{
					BaseReqData: &models.BaseReqData{
						CommonInfo: models.CommonInfo{
							PageType: page_type.PageTypeDefault,
						},
					},
					Product: &models.Product{
						ProductCategory: estimate_pc_id.EstimatePcIdFastCar,
						ComboType:       combo_type.ComboTypeInterCity,
					},
				},
				tabId: ClassTab,
			},
			expected: CarIconTypeNormal,
		},
		{
			name: "智能小巴应返回CarIconTypeNormal",
			provider: &mockCarIconProvider{
				ProductInfoFull: biz_runtime.ProductInfoFull{
					BaseReqData: &models.BaseReqData{
						CommonInfo: models.CommonInfo{
							PageType: page_type.PageTypeDefault,
						},
					},
					Product: &models.Product{
						CarpoolType: carpool_type.CarpoolTypeSmartCarpool,
					},
				},
				tabId: ClassTab,
			},
			expected: CarIconTypeNormal,
		},
		{
			name: "三方表单应返回CarIconTypeNew",
			provider: &mockCarIconProvider{
				ProductInfoFull: biz_runtime.ProductInfoFull{
					BaseReqData: &models.BaseReqData{
						CommonInfo: models.CommonInfo{
							PageType: page_type.PageTypeDefault,
						},
					},
					Product: &models.Product{
						IsTripcloud: func(s bool) *bool { return &s }(true),
					},
				},
				tabId: ClassTab,
			},
			expected: CarIconTypeNew,
			allow:    true,
		},
		{
			name: "默认情况应返回CarIconTypeNormal",
			provider: &mockCarIconProvider{
				ProductInfoFull: biz_runtime.ProductInfoFull{
					BaseReqData: &models.BaseReqData{
						CommonInfo: models.CommonInfo{
							PageType: page_type.PageTypeDefault,
						},
					},
					Product: &models.Product{
						ComboType: combo_type.ComboTypeDefault,
					},
				},
				tabId: ClassTab,
			},
			expected: CarIconTypeNormal,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			patch := mockey.Mock(apollo.FeatureToggle).Return(tt.allow).Build()
			defer patch.UnPatch()

			result := GetCarIconType(context.Background(), tt.provider)
			assert.Equal(t, tt.expected, result, "结果与预期不符")
		})
	}
}
