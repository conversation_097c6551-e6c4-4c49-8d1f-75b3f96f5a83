package car_info

import (
	"context"
	"github.com/spf13/cast"
	"strconv"

	"git.xiaojukeji.com/gobiz/logger"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/models/passenger_transport"
)

const (
	TripcloudFeatureConfig = "tripcloud_feature_config"
	TemplateElement        = "template_element"
)

type BusInessNameProvider interface {
	GetProductCategory() int64
	GetBusinessID() int64
	GetRequireLevel() string
	GetProductId() int64
}

func GetBusinessName(ctx context.Context, prov BusInessNameProvider) (ret string) {
	if passenger_transport.IsPassengerTransportByProductId(cast.ToString(prov.GetProductId())) {
		configResult := passenger_transport.GetTemplateElement(ctx, strconv.Itoa(int(prov.GetBusinessID())), prov.GetRequireLevel())
		if configResult != nil && configResult.BrandName != "" {
			ret = configResult.BrandName
		} else {
			logger.Warnf(ctx, logger.DLTagUndefined, "load fail: %v . %v", TripcloudFeatureConfig, TemplateElement)
			return
		}
	}
	return ret
}

func GetBusinessNameAndIcon(ctx context.Context, prov BusInessNameProvider) (name string, icon string) {
	configResult := passenger_transport.GetTemplateElement(ctx, strconv.Itoa(int(prov.GetBusinessID())), prov.GetRequireLevel())
	if configResult != nil {
		name = configResult.BrandName
		icon = configResult.WaitReplyIcon
	} else {
		logger.Warnf(ctx, logger.DLTagUndefined, "load fail: %v . %v", TripcloudFeatureConfig, TemplateElement)
		return
	}
	return name, icon
}

func GetEstimateNameAndIcon(ctx context.Context, prov BusInessNameProvider) (name string, icon string) {
	configResult := passenger_transport.GetTemplateElement(ctx, strconv.Itoa(int(prov.GetBusinessID())), prov.GetRequireLevel())
	if configResult != nil {
		name = configResult.BrandName
		icon = configResult.EstimateIcon
	} else {
		logger.Warnf(ctx, logger.DLTagUndefined, "load fail: %v . %v", TripcloudFeatureConfig, TemplateElement)
		return
	}
	return name, icon
}
