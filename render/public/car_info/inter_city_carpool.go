package car_info

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/render/public/plain_text_render"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

type ProductInfo interface {
	GetProductCategory() int64
}

func ExtraTag(ctx context.Context, info ProductInfo) *proto.TagWithIconAndBorder {
	contentStr := plain_text_render.DCMPSimpleStringByProduct(ctx, "inter_city_carpool-tab_extra_tag_content", info)
	if contentStr != "" {
		return &proto.TagWithIconAndBorder{
			Content: contentStr,
		}
	}
	return nil
}
