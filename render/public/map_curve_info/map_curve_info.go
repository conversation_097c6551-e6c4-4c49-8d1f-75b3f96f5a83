package map_curve_info

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/notice_info"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"
)

type MapCurveInfoProvider interface {
	GetCarpoolType() int64
	GetExtMap() map[string]string
	GetBaseReqData() *models.BaseReqData

	GetBillDriverMinute() int64
	ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string)

	render.ProductProvider
}

type DcmpConfig struct {
	JumpUrl    string `json:"jump_url"`
	BubbleText string `json:"bubble_text"`
}

func GetMapCurveInfo(ctx context.Context, prov MapCurveInfoProvider) *proto.MapCurveInfo {
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolStation {
		return BuildCarpoolStationMapCurveInfo(ctx, prov)
	}

	if carpool.IsMiniBus(int(prov.GetCarpoolType())) {
		return BuildMinibusMapCurveInfo(ctx, prov)
	}

	return nil
}

func BuildMinibusMapCurveInfo(ctx context.Context, prov MapCurveInfoProvider) *proto.MapCurveInfo {
	// 新版本控制开关
	toggle := apollo.FeatureToggle(ctx, "minibus_long_trip_open_city_switch", util.ToString(prov.GetBaseReqData().PassengerInfo.PID), map[string]string{
		"pid":           util.ToString(prov.GetBaseReqData().PassengerInfo.PID),
		"access_key_id": util.ToString(prov.GetBaseReqData().CommonInfo.AccessKeyID),
		"app_version":   prov.GetBaseReqData().CommonInfo.AppVersion,
		"page_type":     util.ToString(prov.GetBaseReqData().CommonInfo.PageType),
	})
	if !toggle {
		return nil
	}

	dcmpConf := dcmp.GetDcmpContent(ctx, "estimate_form_v3-long_trip_notice_info", nil)
	var dcmpInfo = &DcmpConfig{}
	err := json.Unmarshal([]byte(dcmpConf), dcmpInfo)
	if err != nil {
		return nil
	}

	mapCurveInfo := &proto.MapCurveInfo{
		//na 7.0.6以下，wx 6.10.25以下，继续用此字段不展示曲线
		IsDrawCurve: 0,
		// 新增的曲线类型，除快线外均展示曲线
		CurveType: proto.Int32Ptr(2),
	}
	extMap := prov.GetExtMap()
	if extMap != nil {
		if _, ok := extMap["long_trip_id"]; ok {
			mapCurveInfo.BubbleText = dcmpInfo.BubbleText
			mapCurveInfo.IsDrawCurve = 1
			mapCurveInfo.CurveType = proto.Int32Ptr(1)
			mapCurveInfo.JumpUrl = notice_info.UrlAppendParams(dcmpInfo.JumpUrl, map[string]string{
				"uid":           util.ToString(prov.GetBaseReqData().PassengerInfo.UID),
				"lang":          prov.GetBaseReqData().CommonInfo.Lang,
				"app_version":   prov.GetBaseReqData().CommonInfo.AppVersion,
				"access_key_id": util.ToString(prov.GetBaseReqData().CommonInfo.AccessKeyID),
				"city_id":       util.ToString(prov.GetBaseReqData().AreaInfo.City),
				"long_trip_id":  prov.GetExtMap()["long_trip_id"],
				"page_from":     "2", // 1:表单车型列表、2:表单地图、3:独立页沟通位、4:独立页地图、5:接驾页、6:行中页
			})
		}
	}

	return mapCurveInfo
}

func BuildCarpoolStationMapCurveInfo(ctx context.Context, prov MapCurveInfoProvider) *proto.MapCurveInfo {
	apolloKey, apolloParam := prov.ApolloParamsGen(apollo_model.WithPIDKey, apollo_model.WithProductCategory)
	isAllow := apollo.FeatureToggle(ctx, "map_curve_Info_switch", apolloKey, apolloParam)
	if !isAllow {
		return nil
	}

	dcmpConfig := dcmp.GetJSONResult(ctx, "carpool_config-map_curve_info", nil)
	text := dcmpConfig[cast.ToString(estimate_pc_id.EstimatePcIdCarpoolStation)].String()
	driverMinute := prov.GetBillDriverMinute()
	if text == "" || driverMinute == 0 {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "driverMinute or Config is enpty, driverMinute=%v,text=%v", driverMinute, text)
		return nil
	}

	var mapCurveInfo = &proto.MapCurveInfo{}
	bubbleText := util.ReplaceTag(ctx, text, map[string]string{"eta": cast.ToString(driverMinute)})
	mapCurveInfo.SetIsDrawCurve(1)
	mapCurveInfo.SetBubbleText(bubbleText)
	return mapCurveInfo
}
