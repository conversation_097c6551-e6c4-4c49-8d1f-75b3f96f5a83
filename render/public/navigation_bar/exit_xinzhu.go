package navigation_bar

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	lang2 "git.xiaojukeji.com/gulfstream/tripcloud-common-go/common/lang"
)

const (
	exitXinZhu = "exit_xinzhu"
)

type ExitXinZhu struct {
	Dependency *Dependency
	showType   string
}

func NewExitXinZhu(ctx context.Context, prov NavigationBarProvider, relays ...With) *ExitXinZhu {
	ret := &ExitXinZhu{
		Dependency: &Dependency{},
	}
	for _, relay := range relays {
		relay(ret.Dependency)
	}

	ret.Init(ctx, prov)
	return ret
}

func (e *ExitXinZhu) Init(ctx context.Context, prov NavigationBarProvider) {
}

func (e *ExitXinZhu) Allow(ctx context.Context, prov NavigationBarProvider) bool {
	if prov.GetLang() != lang2.LanguageZhCN {
		return false
	}

	if prov.GetPageType() != page_type.PageTypePageTypeXinZhu {
		return false
	}

	if e.Dependency.ComponentConfig == nil {
		return false
	}

	return true
}

func (e *ExitXinZhu) Build(ctx context.Context, prov NavigationBarProvider) []*proto.NavigationBar {
	cfg := e.Dependency.ComponentConfig
	component := &proto.NavigationBar{}
	component.Key = exitXinZhu
	component.Title = util.StringPtr(cfg.Title)
	component.Icon = util.StringPtr(cfg.Icon)
	return []*proto.NavigationBar{component}
}
