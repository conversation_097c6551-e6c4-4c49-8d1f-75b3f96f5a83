package navigation_bar

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/nuwa/golibs/knife"
)

const travelForecast = "travel_forecast"

type TravelForecast struct {
	Dependency *Dependency
}

func NewTravelForecast(ctx context.Context, prov NavigationBarProvider, relays ...With) *TravelForecast {
	ret := &TravelForecast{
		Dependency: &Dependency{},
	}

	for _, relay := range relays {
		relay(ret.Dependency)
	}
	ret.Init(ctx, prov)
	return ret
}

func (t *TravelForecast) Init(ctx context.Context, prov NavigationBarProvider) {
	return
}

func (t *TravelForecast) Allow(ctx context.Context, prov NavigationBarProvider) bool {
	stopOverPoints := prov.GetStopoverPoints()
	if stopOverPoints != nil && *stopOverPoints != "" {
		return false
	}

	if prov.GetCityID() != prov.GetToCityID() {
		return false
	}

	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	params["estimate_style_type"] = util.ToString(prov.GetEstimateStyleType())
	hit, parameter := apollo.GetParameters("xingchengyuce_1", pidKey, params)
	if !hit {
		return false
	}

	isShow, ok := parameter["is_show"]
	if !ok || isShow == "0" {
		return false
	}

	toggle := apollo.FeatureToggle(ctx, "xingchengyuce_degrade", pidKey, params)
	if toggle {
		return false
	}

	return true
}

func (t *TravelForecast) Build(ctx context.Context, prov NavigationBarProvider) []*proto.NavigationBar {
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	params["estimate_style_type"] = util.ToString(prov.GetEstimateStyleType())
	_, parameter := apollo.GetParameters("xingchengyuce_1", pidKey, params)

	title, ok1 := parameter["title"]
	link, ok2 := parameter["link"]
	if !ok1 || !ok2 {
		return nil
	}

	component := &proto.NavigationBar{
		Key:   travelForecast,
		Title: util.StringPtr(title),
		Link:  util.StringPtr(link),
	}

	result := []*proto.NavigationBar{component}
	knife.Set(ctx, consts.TravelForecastKey, result)
	return result
}
