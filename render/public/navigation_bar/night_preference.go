package navigation_bar

import (
	"context"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"net/url"
)

const (
	nightPreference                = "night_preference"
	nightPreferenceFromType        = 11
	CustomServiceFemaleDriverFirst = 114
)

type NightPreference struct {
	Dependency *Dependency
	IsShow     bool
}

func NewNightPreference(ctx context.Context, prov NavigationBarProvider, relays ...With) *NightPreference {
	ret := &NightPreference{
		Dependency: &Dependency{},
	}

	for _, relay := range relays {
		relay(ret.Dependency)
	}
	ret.Init(ctx, prov)
	return ret
}

func (n *NightPreference) Init(ctx context.Context, prov NavigationBarProvider) {
	n.IsShow = util.InArrayInt64(CustomServiceFemaleDriverFirst, prov.GetAvailableServiceList())
}

func (n *NightPreference) Allow(ctx context.Context, prov NavigationBarProvider) bool {
	if !n.IsShow {
		return false
	}

	if n.Dependency.ComponentConfig == nil {
		return false
	}

	return true
}

func (n *NightPreference) Build(ctx context.Context, prov NavigationBarProvider) []*proto.NavigationBar {
	cfg := n.Dependency.ComponentConfig
	if cfg == nil {
		return nil
	}

	component := &proto.NavigationBar{
		Key:            nightPreference,
		Title:          util.StringPtr(cfg.Title),
		Icon:           util.StringPtr(cfg.Icon),
		HighlightColor: util.StringPtr(cfg.HighlightColor),
		IsHighlight:    util.Int32Ptr(0),
	}

	if util.IsMini(prov.GetAccessKeyId()) {
		component.Link = nil
	} else {
		component.Link = util.StringPtr(n.buildLinkWithParams(prov, cfg.Link))
	}

	if prov.GetIsFemaleDriverFirst() == 1 {
		component.Title = util.StringPtr(cfg.FemaleFirst)
		if n.Dependency.ShowHighLight {
			component.IsHighlight = util.Int32Ptr(1)
			component.Icon = util.StringPtr(cfg.HighlightIcon)
		}
	}

	return []*proto.NavigationBar{component}
}

func (n *NightPreference) buildLinkWithParams(prov NavigationBarProvider, link string) string {
	if link == "" {
		return ""
	}

	params := url.Values{}
	params.Set("is_female_driver_first", fmt.Sprintf("%d", prov.GetIsFemaleDriverFirst()))
	params.Set("from_type", fmt.Sprintf("%d", nightPreferenceFromType))
	params.Set("city_id", fmt.Sprintf("%d", prov.GetCityID()))

	return fmt.Sprintf(link, params.Encode())
}
