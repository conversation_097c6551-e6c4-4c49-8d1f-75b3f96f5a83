package navigation_bar

import (
	"context"
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

type NavigationBarProvider interface {
	GetFormStyleExp() int32
	GetAvailableServiceList() []int64
	GetCallCarType() int32
	GetPageType() int32
	GetTabId() string
	GetUserChangeTime() bool
	GetOneStopVersion() string
	GetEstimateStyleType() int32
	GetIsFemaleDriverFirst() int32
	GetUserUID() int64
	GetLat() float64
	GetLng() float64
	GetStopoverPoints() *string
	GetToCityID() int
	GetPaymentsType() int32
	GetPaymentList() []*PriceApi.PaymentElem
	GetAreaInfo() *models.AreaInfo
	GetUserPhone() string
	GetBaseReqData() *models.BaseReqData

	render.BaseProvider
	render.ProductProvider
	render.ApolloProvider
}

type Base interface {
	Init(ctx context.Context, prov NavigationBarProvider)
	Allow(ctx context.Context, prov NavigationBarProvider) bool
	Build(ctx context.Context, prov NavigationBarProvider) []*proto.NavigationBar
}

type ComponentConfig struct {
	Key            string              `json:"key"`
	Title          string              `json:"title"`
	Icon           string              `json:"icon"`
	Link           string              `json:"link"`
	Animation      Animation           `json:"animation"`
	HighlightColor string              `json:"highlight_color"`
	IsHighlight    int32               `json:"is_highlight"`
	Params         NavigationBarParams `json:"params"`
	Popup          map[string]string   `json:"popup"`
	HighlightIcon  string              `json:"highlight_icon"`
	FemaleFirst    string              `json:"female_first"`
	Text           string              `json:"text"`
	Img            string              `json:"img"`
	SubText        string              `json:"sub_text"`
	LeftButton     string              `json:"left_button"`
	RightButton    string              `json:"right_button"`
}

type Animation struct {
	Title *string `json:"title"`
	Icon  *string `json:"icon"`
}

type NavigationBarParams struct {
	TabID    *string `json:"tab_id"`
	PageType *int32  `json:"page_type"`
}

type NavigationBarConfig struct {
	ComponentConfigMap map[string]*ComponentConfig `json:"component_config_map"`
	SortOrder          []string                    `json:"__sort_order"`
	XinzhuSortOrder    []string                    `json:"__xinzhu_sort_order"`
}

type Dependency struct {
	ShowHighLight      bool
	ComponentConfig    *ComponentConfig
	ComponentConfigMap map[string]*ComponentConfig
	UserPayInfo        *proto.PaymentOptionModule
}

type With func(p *Dependency)

func WithShowHighLight(ShowHighLight bool) With {
	return func(p *Dependency) {
		p.ShowHighLight = ShowHighLight
	}
}

func WithComponentConfig(ComponentConfig *ComponentConfig) With {
	return func(p *Dependency) {
		p.ComponentConfig = ComponentConfig
	}
}

func WithAllComponentConfig(ComponentConfigMap map[string]*ComponentConfig) With {
	return func(p *Dependency) {
		p.ComponentConfigMap = ComponentConfigMap
	}
}

func WithUserPayInfo(UserPayInfo *proto.PaymentOptionModule) With {
	return func(p *Dependency) {
		p.UserPayInfo = UserPayInfo
	}
}
