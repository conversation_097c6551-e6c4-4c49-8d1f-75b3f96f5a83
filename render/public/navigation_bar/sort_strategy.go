package navigation_bar

import (
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"sort"
)

type SortStrategy interface {
	Sort(components []*proto.NavigationBar) []*proto.NavigationBar
}

type DefaultSortStrategy struct {
	sortOrder []string
}

func NewDefaultSortStrategy(sortOrder []string) *DefaultSortStrategy {
	return &DefaultSortStrategy{
		sortOrder: sortOrder,
	}
}

func (s *DefaultSortStrategy) Sort(components []*proto.NavigationBar) []*proto.NavigationBar {
	if len(s.sortOrder) == 0 {
		return components
	}

	orderMap := make(map[string]int)
	for i, key := range s.sortOrder {
		orderMap[key] = i
	}

	result := make([]*proto.NavigationBar, len(components))
	copy(result, components)
	sort.Slice(result, func(i, j int) bool {
		keyI := result[i].Key
		keyJ := result[j].Key

		orderI := 1000
		if pos, exists := orderMap[keyI]; exists {
			orderI = pos
		}

		orderJ := 1000
		if pos, exists := orderMap[keyJ]; exists {
			orderJ = pos
		}

		return orderI < orderJ
	})

	return result
}

type NewFormSortStrategy struct{}

func NewNewFormSortStrategy() *NewFormSortStrategy {
	return &NewFormSortStrategy{}
}

var sortRules = [][]string{
	{callCar},
	{nightPreference, riderPreference, travelForecast},
	{cashbackXinZhu, riderPreference, travelForecast},
	{payments, riderPreference, travelForecast},
	{riderPreference},
	{travelForecast},
}

func (s *NewFormSortStrategy) Sort(components []*proto.NavigationBar) []*proto.NavigationBar {
	var sortedComponents []*proto.NavigationBar
	usedKeys := make(map[string]bool)

	componentMap := make(map[string]*proto.NavigationBar)
	for _, component := range components {
		componentMap[component.Key] = component
	}

	for _, candidates := range sortRules {
		for _, candidate := range candidates {
			if component, exists := componentMap[candidate]; exists && !usedKeys[candidate] {
				sortedComponents = append(sortedComponents, component)
				usedKeys[candidate] = true
				break
			}
		}
	}

	for _, component := range components {
		if !usedKeys[component.Key] {
			sortedComponents = append(sortedComponents, component)
		}
	}

	return sortedComponents
}

type SortStrategyFactory struct{}

func (f *SortStrategyFactory) CreateDefaultStrategy(sortOrder []string) SortStrategy {
	return NewDefaultSortStrategy(sortOrder)
}

func (f *SortStrategyFactory) CreateNewFormStrategy() SortStrategy {
	return NewNewFormSortStrategy()
}

func GetSortStrategy(useNewSort bool, sortOrder []string) SortStrategy {
	factory := &SortStrategyFactory{}

	if useNewSort {
		return factory.CreateNewFormStrategy()
	}

	return factory.CreateDefaultStrategy(sortOrder)
}
