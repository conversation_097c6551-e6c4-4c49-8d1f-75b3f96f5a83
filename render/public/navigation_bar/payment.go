package navigation_bar

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

const (
	payments             = "payments"
	paymentTagIndividual = 2
)

type Payment struct {
	Dependency *Dependency
}

func NewPayment(ctx context.Context, prov NavigationBarProvider, relays ...With) *Payment {
	ret := &Payment{
		Dependency: &Dependency{},
	}
	for _, relay := range relays {
		relay(ret.Dependency)
	}

	ret.Init(ctx, prov)
	return ret
}

func (p *Payment) Init(ctx context.Context, prov NavigationBarProvider) {
	return
}

func (p *Payment) Allow(ctx context.Context, prov NavigationBarProvider) bool {
	if p.Dependency.UserPayInfo == nil || p.Dependency.UserPayInfo.PaymentList == nil {
		return false
	}

	paymentList := p.Dependency.UserPayInfo.PaymentList
	if len(paymentList) == 0 || (len(paymentList) == 1 && paymentList[0] != nil && paymentList[0].Tag == paymentTagIndividual) {
		return false
	}

	if p.Dependency.ComponentConfig == nil {
		return false
	}

	return true
}

func (p *Payment) Build(ctx context.Context, prov NavigationBarProvider) []*proto.NavigationBar {
	var paymentTypeList []int32
	cfg := p.Dependency.ComponentConfig
	component := &proto.NavigationBar{
		Key:            payments,
		Icon:           util.StringPtr(cfg.Icon),
		HighlightColor: util.StringPtr(cfg.HighlightColor),
		IsHighlight:    util.Int32Ptr(0),
	}

	// 构建下可用支付方式
	for _, payment := range prov.GetPaymentList() {
		if payment == nil {
			continue
		}

		if payment.Disabled != nil && *payment.Disabled == 1 {
			continue
		}

		paymentTypeList = append(paymentTypeList, payment.PayType)
	}

	// 根据支付方式高亮显示
	if p.Dependency.ShowHighLight && util.InArrayInt32(prov.GetPaymentsType(), paymentTypeList) {
		component.IsHighlight = util.Int32Ptr(1)
		component.Icon = util.StringPtr(cfg.HighlightIcon)
	}

	return []*proto.NavigationBar{component}
}
