package navigation_bar

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

const callCar = "call_car"

type CallCar struct {
	Dependency       *Dependency
	IsDisableCallCar int32
}

func NewCallCar(ctx context.Context, prov NavigationBarProvider, relays ...With) *CallCar {
	ret := &CallCar{
		Dependency: &Dependency{},
	}
	for _, relay := range relays {
		relay(ret.Dependency)
	}

	ret.Init(ctx, prov)
	return ret
}

func (h *CallCar) Init(ctx context.Context, prov NavigationBarProvider) {
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	if apollo.FeatureToggle(ctx, "gs_disable_call_car", pid<PERSON><PERSON>, params) {
		h.IsDisableCallCar = 1
	}
}

func (h *CallCar) Allow(ctx context.Context, prov NavigationBarProvider) bool {
	if h.IsDisableCallCar == 1 {
		return false
	}

	if h.Dependency.ComponentConfig == nil {
		return false
	}

	return true
}

func (h *CallCar) Build(ctx context.Context, prov NavigationBarProvider) []*proto.NavigationBar {
	cfg := h.Dependency.ComponentConfig
	callCarComponent := &proto.NavigationBar{
		IsHighlight:    util.Int32Ptr(0),
		Key:            callCar,
		HighlightColor: util.StringPtr(cfg.HighlightColor),
		Icon:           util.StringPtr(cfg.Icon),
	}

	if h.Dependency.ShowHighLight && prov.GetCallCarType() != 0 {
		callCarComponent.IsHighlight = util.Int32Ptr(1)
		callCarComponent.Icon = util.StringPtr(h.Dependency.ComponentConfig.HighlightIcon)
	}

	return []*proto.NavigationBar{callCarComponent}
}
