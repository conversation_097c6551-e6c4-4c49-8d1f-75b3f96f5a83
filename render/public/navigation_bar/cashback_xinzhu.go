package navigation_bar

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/tag_service"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	lang2 "git.xiaojukeji.com/gulfstream/tripcloud-common-go/common/lang"
)

const (
	cashbackXinZhu = "cashback_xinzhu"
	xinZhuTag      = "438863,707234,639718,776493,776491"
)

type CashbackXinZhu struct {
	Dependency *Dependency
}

func NewCashbackXinZhu(ctx context.Context, prov NavigationBarProvider, relays ...With) *CashbackXinZhu {
	ret := &CashbackXinZhu{
		Dependency: &Dependency{},
	}
	for _, relay := range relays {
		relay(ret.Dependency)
	}

	ret.Init(ctx, prov)
	return ret
}

func (c *CashbackXinZhu) Init(ctx context.Context, prov NavigationBarProvider) {
}

func (c *CashbackXinZhu) Allow(ctx context.Context, prov NavigationBarProvider) bool {
	if prov.GetLang() != lang2.LanguageZhCN {
		return false
	}

	if prov.GetPageType() != page_type.PageTypeDefault ||
		!util.InArrayStr(prov.GetTabId(), []string{tab.TabIdClassify, tab.TabIdNormal}) {
		return false
	}

	if prov.GetEstimateStyleType() == 4 || prov.GetOneStopVersion() == "9.0" {
		return false
	}

	if prov.GetUserChangeTime() == true {
		return false
	}

	if c.Dependency.ComponentConfig == nil {
		return false
	}

	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	params["caller"] = "mamba"
	toggle := apollo.FeatureToggle(ctx, "cashback_xinzhu_switch", pidKey, params)
	if !toggle {
		return false
	}

	allow, assignment := apollo.FeatureExp(ctx, "xinzhu_v1_002", pidKey, params)
	if !allow {
		return false
	}

	hitGroup := assignment.GetParameter("hit_group", "")
	switch hitGroup {
	case "hit_1":
		return true
	case "hit_2":
		userTag := xinZhuTag
		if tag := assignment.GetParameter("user_tag", ""); tag != "" {
			userTag = tag
			if tag_service.IsHitTags(ctx, prov.GetTabId(), userTag) {
				return true
			}
		}
	default:
		return false
	}

	return false
}

func (c *CashbackXinZhu) Build(ctx context.Context, prov NavigationBarProvider) []*proto.NavigationBar {
	cfg := c.Dependency.ComponentConfig
	component := &proto.NavigationBar{
		Key:   cashbackXinZhu,
		Title: util.StringPtr(cfg.Title),
		Icon:  util.StringPtr(cfg.Icon),
		Params: &proto.NavigationBarParams{
			TabId:    cfg.Params.TabID,
			PageType: cfg.Params.PageType,
		},
		Popup: cfg.Popup,
	}

	return []*proto.NavigationBar{component}
}
