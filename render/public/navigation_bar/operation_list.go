package navigation_bar

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/operation_list"
	"git.xiaojukeji.com/nuwa/golibs/json"
)

const riderPreference = "ride_preference"

type OperationList struct {
	Dependency              *Dependency
	IsDisableRidePreference bool
}

func NewOperationList(ctx context.Context, prov NavigationBarProvider, relays ...With) *OperationList {
	ret := &OperationList{
		Dependency: &Dependency{},
	}

	for _, relay := range relays {
		relay(ret.Dependency)
	}
	ret.Init(ctx, prov)
	return ret
}

func (o *OperationList) Init(ctx context.Context, prov NavigationBarProvider) {
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	o.IsDisableRidePreference = apollo.FeatureToggle(ctx, "gs_disable_ride_preference", pidKey, params)
}

func (o *OperationList) Allow(ctx context.Context, prov NavigationBarProvider) bool {
	if o.IsDisableRidePreference {
		return false
	}

	operationList := operation_list.GetOperationListFormCache(ctx, prov)
	if len(operationList) == 0 {
		return false
	}

	return true
}

func (o *OperationList) Build(ctx context.Context, prov NavigationBarProvider) []*proto.NavigationBar {
	operationList := operation_list.GetOperationListFormCache(ctx, prov)
	if len(operationList) == 0 {
		return nil
	}

	cfgMap := o.Dependency.ComponentConfigMap
	var result []*proto.NavigationBar
	for _, operationItem := range operationList {
		if operationItem == nil || operationItem.Key == "" {
			continue
		}

		if cfgMap[operationItem.Key] == nil {
			continue
		}

		bytes, _ := json.Marshal(cfgMap[operationItem.Key])
		component := &proto.NavigationBar{}
		err := json.Unmarshal(bytes, &component)
		if err != nil {
			continue
		}

		result = append(result, component)
	}

	return result
}
