package navigation_bar

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/nuwa/trace"
)

const (
	ufsKey = "new_nav_bar.frequency_control"
)

type UFSFrequencyControl struct{}

func NewUFSFrequencyControl() *UFSFrequencyControl {
	return &UFSFrequencyControl{}
}

func (u *UFSFrequencyControl) GetShowTimes(ctx context.Context, prov NavigationBarProvider) (bool, map[string]int) {
	params := map[string]string{
		"passenger_id": util.ToString(prov.GetUserPID()),
	}

	feature, err := ufs.GetFeatureV2(ctx, ufs.DomainPassenger, ufsKey, params)
	if err != nil || feature == "" {
		return false, nil
	}

	frequency := make(map[string]int)
	_ = json.Unmarshal([]byte(feature), &frequency)
	return true, frequency
}

func (u *UFSFrequencyControl) SetShowTimes(ctx context.Context, prov NavigationBarProvider, times map[string]int) {
	timesJson, err := json.Marshal(times)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "failed to marshal times: %v", err)
		return
	}

	params := map[string]string{
		"passenger_id": util.ToString(prov.GetUserPID()),
	}
	features := map[string]string{
		ufsKey: string(timesJson),
	}
	_, err = ufs.SetFeature(ctx, ufs.DomainPassenger, params, features)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "failed to set features: %v", err)
		return
	}

	return
}
