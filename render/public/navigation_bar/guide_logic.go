package navigation_bar

//
//import (
//	"context"
//	"encoding/json"
//	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
//	"git.xiaojukeji.com/gulfstream/mamba/common/util"
//	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
//	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
//	x_engine "git.xiaojukeji.com/s3e/x-engine/condition"
//)
//
//const (
//	guideNs = "navigation_bar_guide"
//)
//
//// GuideLogic 导流逻辑处理
//type GuideLogic struct {
//	ufsFrequencyControl *UFSFrequencyControl
//}
//
//// NewGuideLogic 创建导流逻辑实例
//func NewGuideLogic() *GuideLogic {
//	return &GuideLogic{
//		ufsFrequencyControl: NewUFSFrequencyControl(),
//	}
//}
//
//// GetGuideInfo 获取导流信息
//func (g *GuideLogic) GetGuideInfo(ctx context.Context, prov NavigationBarProvider, components []*proto.NavigationBar) []*proto.NavigationBar {
//	ok, frequency := g.ufsFrequencyControl.GetShowTimes(ctx, prov)
//	if !ok {
//		return components
//	}
//
//	var ret *x_engine.Result
//	pos := -1
//	// 遍历所有组件
//	for curPos, component := range components {
//		key := component.Key
//		_, engineReq := prov.GetApolloParams(biz_runtime.WithPIDKey)
//		engineReq["tab_id"] = prov.GetTabId()
//		engineReq["caller"] = "mamba"
//		sUfsKey := "__auto__" + key
//		engineReq["ufs_frequency"] = util.ToString(frequency[sUfsKey])
//		curRet, err := x_engine.Check(ctx, guideNs, engineReq)
//		if err == nil && curRet.IsAllow == true {
//			pos = curPos
//			ret = curRet
//			frequency[sUfsKey]++
//		}
//	}
//
//	if ret == nil {
//		return components
//	}
//
//	if ret.Material == nil {
//		return components
//	}
//
//	materialMap := make(map[string]string)
//	err := json.Unmarshal([]byte(util.ToString(ret.Material)), &materialMap)
//	if err != nil {
//		log.Trace.Warnf(ctx, "json.Unmarshal material err: %s", err.Error())
//		return components
//	}
//
//
//	return components
//}
