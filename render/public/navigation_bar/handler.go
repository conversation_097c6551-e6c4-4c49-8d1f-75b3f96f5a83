package navigation_bar

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/nuwa/golibs/json"
	"git.xiaojukeji.com/nuwa/trace"
)

type Handler struct {
	IsShowHighLight     bool
	NavigationBarConfig *NavigationBarConfig
	Components          []Base
	SortOrder           []string
	ExtraData
}

type ExtraData struct {
	UserPayInfo *proto.PaymentOptionModule
}

func (h *Handler) init(ctx context.Context, prov NavigationBarProvider) {
	switch prov.GetPageType() {
	case page_type.PageTypePageTypeXinZhu:
		h.initXinZhu(ctx, prov)
	default:
		h.initDefault(ctx, prov)
	}
}

func (h *Handler) build(ctx context.Context, prov NavigationBarProvider) []*proto.NavigationBar {
	result := make([]*proto.NavigationBar, 0)
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	params["page_type"] = util.ToString(prov.GetPageType())
	if !apollo.FeatureToggle(ctx, "navigation_bar_switch", pidKey, params) {
		return result
	}

	for _, component := range h.Components {
		component.Init(ctx, prov)
		if !component.Allow(ctx, prov) {
			continue
		}

		res := component.Build(ctx, prov)
		if res != nil {
			result = append(result, res...)
		}
	}

	result = h.filterValidComponents(result)
	useNewSort := h.shouldUseNewSort(ctx, prov)
	strategy := GetSortStrategy(useNewSort, h.SortOrder)
	result = strategy.Sort(result)

	// todo: GuideLogic确认是否真没了
	return result
}

func (h *Handler) filterValidComponents(components []*proto.NavigationBar) []*proto.NavigationBar {
	filtered := make([]*proto.NavigationBar, 0)
	for _, component := range components {
		if component != nil && component.Key != "" {
			filtered = append(filtered, component)
		}
	}
	return filtered
}

func (h *Handler) shouldUseNewSort(ctx context.Context, prov NavigationBarProvider) bool {
	return prov.GetOrderType() == 0 && prov.GetFormStyleExp() == 2
}

func (h *Handler) getGuideInfo(ctx context.Context, prov NavigationBarProvider, components []*proto.NavigationBar) []*proto.NavigationBar {
	return components
}

func GetNavigationBar(ctx context.Context, prov NavigationBarProvider, userPayInfo *proto.PaymentOptionModule) []*proto.NavigationBar {
	var navigationBarConfig *NavigationBarConfig
	content := dcmp.GetDcmpContent(ctx, "config_text-navigation_bar_new", nil)
	err := json.Unmarshal([]byte(content), &navigationBarConfig)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "json unmarshal navigation_bar config err: %v", err)
		return nil
	}

	handler := &Handler{
		IsShowHighLight:     buildIsShowHighLight(ctx, prov),
		NavigationBarConfig: navigationBarConfig,
	}

	handler.init(ctx, prov)
	return handler.build(ctx, prov)
}

func buildIsShowHighLight(ctx context.Context, prov NavigationBarProvider) bool {
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	return apollo.FeatureToggle(ctx, "navigation_highlight_controller", pidKey, params)
}

func (h *Handler) initXinZhu(ctx context.Context, prov NavigationBarProvider) {
	componentConfigMap := h.NavigationBarConfig.ComponentConfigMap
	userPayInfo := h.UserPayInfo
	h.SortOrder = h.NavigationBarConfig.XinzhuSortOrder
	h.Components = []Base{
		NewPayment(ctx, prov, WithShowHighLight(h.IsShowHighLight), WithComponentConfig(componentConfigMap[payments]), WithUserPayInfo(userPayInfo)),
		NewExitXinZhu(ctx, prov, WithComponentConfig(componentConfigMap[exitXinZhu])),
	}
}

func (h *Handler) initDefault(ctx context.Context, prov NavigationBarProvider) {
	componentConfigMap := h.NavigationBarConfig.ComponentConfigMap
	userPayInfo := h.UserPayInfo
	h.SortOrder = h.NavigationBarConfig.SortOrder
	h.Components = []Base{
		NewCallCar(ctx, prov, WithShowHighLight(h.IsShowHighLight), WithComponentConfig(componentConfigMap[callCar])),
		NewTimeSelector(ctx, prov, WithShowHighLight(h.IsShowHighLight), WithComponentConfig(componentConfigMap[timeSelector])),
		NewPayment(ctx, prov, WithShowHighLight(h.IsShowHighLight), WithComponentConfig(componentConfigMap[payments]), WithUserPayInfo(userPayInfo)),
		NewTravelForecast(ctx, prov),
		NewOperationList(ctx, prov, WithAllComponentConfig(componentConfigMap)),
		NewNightPreference(ctx, prov, WithComponentConfig(componentConfigMap[nightPreference])),
		NewCashbackXinZhu(ctx, prov, WithComponentConfig(componentConfigMap[cashbackXinZhu])),
	}
}
