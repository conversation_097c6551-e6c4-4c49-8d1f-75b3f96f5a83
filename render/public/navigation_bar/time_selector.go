package navigation_bar

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v4_multi/experiment"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

const timeSelector = "time_selector"

type TimeSelector struct {
	Dependency          *Dependency
	IsDisableTimeSelect bool
}

func NewTimeSelector(ctx context.Context, prov NavigationBarProvider, relays ...With) *TimeSelector {
	ret := &TimeSelector{
		Dependency: &Dependency{},
	}
	for _, relay := range relays {
		relay(ret.Dependency)
	}
	ret.Init(ctx, prov)
	return ret
}

func (t *TimeSelector) Init(ctx context.Context, prov NavigationBarProvider) {
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	t.IsDisableTimeSelect = apollo.FeatureToggle(ctx, "gs_disable_time_selector", pidKey, params)
}

func (t *TimeSelector) Allow(ctx context.Context, prov NavigationBarProvider) bool {
	if t.IsDisableTimeSelect {
		return false
	}

	if experiment.GetNewStyleFormABParamFromBaseReqData(ctx, prov.GetBaseReqData()).IsHitNewNavigationBarNewSort() && prov.GetOrderType() == consts.RealTimeOrder {
		return false
	}

	if t.Dependency.ComponentConfig == nil {
		return false
	}

	return true
}

func (t *TimeSelector) Build(ctx context.Context, prov NavigationBarProvider) []*proto.NavigationBar {
	cfg := t.Dependency.ComponentConfig
	component := &proto.NavigationBar{
		Key:            timeSelector,
		Icon:           util.StringPtr(cfg.Icon),
		HighlightColor: util.StringPtr(cfg.HighlightColor),
		IsHighlight:    util.Int32Ptr(0),
	}

	if t.Dependency.ShowHighLight && prov.GetUserChangeTime() {
		component.IsHighlight = util.Int32Ptr(1)
		component.Icon = util.StringPtr(cfg.HighlightIcon)
	}

	return []*proto.NavigationBar{component}
}
