package prefer_data

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/redis"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

const optionServiceDcmpKey = "option_service-hk_taxi"
const PHkTaxiTipInfo = "p_hk_taxi_tip_info"
const PHkTaxiCommentInfo = "p_hk_taxi_comment_info"

type PreferDataTagListRsp struct {
	IsSelect bool                          `json:"is_select"`
	TagList  []*proto.NewFormPreferDataTag `json:"tag_list"`
}

type OptionServiceDcmpInfo struct {
	Title             string            `json:"title"`
	SubTitle          string            `json:"sub_title"`
	HeadImg           string            `json:"head_img"`
	StartBgColor      string            `json:"start_bg_color"`
	EndBgColor        string            `json:"end_bg_color"`
	EstimateTraceId   string            `json:"estimate_trace_id"`
	DefaultSelectTab  int               `json:"default_select_tab"`
	Theme             int               `json:"theme"`
	Disable           int               `json:"disable"`
	ShowTab           int               `json:"show_tab"`
	SubTitleLink      string            `json:"sub_title_link"`
	PreferDisplayTags *PreferDisplayTag `json:"prefer_display_tags"`
	DispatchFee       *DispatchFee      `json:"dispatch_fee"`
	Comment           *Comment          `json:"comment"`
}

type PreferDisplayTag struct {
	DispatchFee   *DisplayInfo `json:"dispatch_fee"`
	CommentOption *DisplayInfo `json:"comment_option"`
}

type DispatchFee struct {
	Head      string `json:"head"`
	SubHead   string `json:"sub_head"`
	TipOption []*Tip `json:"tip_option"`
}

type Comment struct {
	Head          string    `json:"head"`
	SubHead       string    `json:"sub_head"`
	CommentOption []*Option `json:"comment_option"`
}

type Option struct {
	OptionId int    `json:"option_id"`
	Text     string `json:"text"`
}

type Tip struct {
	OptionId int    `json:"option_id"`
	Currency string `json:"currency"`
	Unit     string `json:"unit"`
	Text     string `json:"text"`
	Content  string `json:"content"`
	Icon     string `json:"icon"`
}

type DisplayInfo struct {
	DisplayIcon  string   `json:"display_icon"`
	DisplayNames []string `json:"display_names"`
	DisplayCount int      `json:"display_count"`
}

func GetPreferDataOfHkTaxiFeature(ctx context.Context, prov PreferDataProvider) *proto.V3PreferData {
	preferDataRsp := getPreferDataTagList(ctx, prov)

	var selectDesc string
	if !preferDataRsp.IsSelect {
		selectDesc = dcmp.GetDcmpContent(ctx, canSelectDcmpKey, nil)
	} else {
		selectDesc = dcmp.GetDcmpContent(ctx, selectedDcmpKey, nil)
	}

	return &proto.V3PreferData{
		Desc:    selectDesc,
		TagList: preferDataRsp.TagList,
	}

}

func getPreferDataTagList(ctx context.Context, prov PreferDataProvider) *PreferDataTagListRsp {
	var preferDataTagList = &PreferDataTagListRsp{}
	var tagList = make([]*proto.NewFormPreferDataTag, 0)
	var optionService = &OptionServiceDcmpInfo{}

	rawConfig := dcmp.GetDcmpContent(ctx, optionServiceDcmpKey, nil)

	err := json.Unmarshal([]byte(rawConfig), optionService)
	if err != nil || optionService.PreferDisplayTags == nil {
		return nil
	}

	preferDataTagList.IsSelect = false
	tags := optionService.PreferDisplayTags

	// 获取红包信息
	tag, flag := getDispatchFeeTag(ctx, prov, optionService, tags)
	if tag != nil {
		tagList = append(tagList, tag)
	}

	preferDataTagList.IsSelect = flag

	// 获取捎话信息
	tag, flag = getCommentTag(ctx, prov, optionService, tags)
	if tag != nil {
		tagList = append(tagList, tag)
	}

	preferDataTagList.IsSelect = flag
	preferDataTagList.TagList = tagList

	return preferDataTagList

}

func getDispatchFeeTag(ctx context.Context, prov PreferDataProvider, optionService *OptionServiceDcmpInfo, tags *PreferDisplayTag) (*proto.NewFormPreferDataTag, bool) {
	redisDB := redis.GetMultiEstimateClient()
	isSelect := false
	if optionService.DispatchFee != nil && optionService.DispatchFee.TipOption != nil &&
		tags.DispatchFee != nil {
		var dispatchFeeDisplayNames string
		// 从Redis获取红包信息
		tipKey := PHkTaxiTipInfo + prov.GetPassengerId() + strconv.FormatInt(prov.GetProductId(), 10)
		tipRes, err := redisDB.Get(ctx, tipKey)
		if err == nil {
			dispatchRes := &Option{}
			err = json.Unmarshal([]byte(tipRes), dispatchRes)
			if err != nil {
				return nil, false
			}

			if dispatchRes.OptionId > 0 {
				for _, tip := range optionService.DispatchFee.TipOption {
					if tip.OptionId == dispatchRes.OptionId {
						dispatchFeeDisplayNames = tip.Content
						break
					}
				}
			}
		}

		if "" == dispatchFeeDisplayNames && tags.DispatchFee.DisplayNames != nil && len(tags.DispatchFee.DisplayNames) > 0 {
			dispatchFeeDisplayNames = tags.DispatchFee.DisplayNames[0]
		} else {
			isSelect = true
		}

		return &proto.NewFormPreferDataTag{
			Content: getTagContent(dispatchFeeDisplayNames, tags.DispatchFee.DisplayCount),
			Icon:    tags.DispatchFee.DisplayIcon,
		}, isSelect

	}
	return nil, false
}

func getCommentTag(ctx context.Context, prov PreferDataProvider, optionService *OptionServiceDcmpInfo, tags *PreferDisplayTag) (*proto.NewFormPreferDataTag, bool) {
	redisDB := redis.GetMultiEstimateClient()
	isSelect := false

	if optionService.Comment != nil && optionService.Comment.CommentOption != nil &&
		tags.CommentOption != nil {
		commentDisplayName := make([]string, 0)
		// 从Redis获取捎话信息
		commentKey := PHkTaxiCommentInfo + prov.GetPassengerId() + strconv.FormatInt(prov.GetProductId(), 10)
		commentRes, err := redisDB.Get(ctx, commentKey)
		if err == nil {
			var commRes []*Option
			err = json.Unmarshal([]byte(commentRes), &commRes)
			if err == nil {
				commentHistory := make([]int, 0)
				for _, re := range commRes {
					commentHistory = append(commentHistory, re.OptionId)
				}

				for _, option := range optionService.Comment.CommentOption {
					if option.OptionId > 0 && util.InArrayInt(option.OptionId, commentHistory) {
						commentDisplayName = append(commentDisplayName, option.Text)
					}
				}
			}
		}

		displayName := strings.Join(commentDisplayName, "/")

		if "" == displayName && tags.CommentOption.DisplayNames != nil && len(tags.CommentOption.DisplayNames) > 0 {
			displayName = tags.CommentOption.DisplayNames[0]
		} else {
			isSelect = true
		}

		return &proto.NewFormPreferDataTag{
			Content: getTagContent(displayName, tags.CommentOption.DisplayCount),
			Icon:    tags.CommentOption.DisplayIcon,
		}, isSelect

	}

	return nil, false
}

func getTagContent(name string, count int) string {
	if count < 1 {
		return name
	}
	return fmt.Sprintf("%s x %d", name, count)
}
