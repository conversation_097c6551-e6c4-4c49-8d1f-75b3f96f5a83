package prefer_data

import (
	"context"
	"encoding/json"
	hundunClient "git.xiaojukeji.com/dirpc/dirpc-go-http-Hundun"
	Sps "git.xiaojukeji.com/dirpc/dirpc-go-http-Sps"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_category"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/terminal/access_key_id"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	lang2 "git.xiaojukeji.com/gulfstream/tripcloud-common-go/common/lang"
	"strconv"
	"strings"

	"git.xiaojukeji.com/nuwa/trace"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/hestia_charge"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

const HKCityId = 357
const customServiceGuideNew = 102
const optionOpenControl = "pre_sale_option_open_control"
const optionServiceConfig = "option_service_config"
const cityServiceConfig = "city_support_service_config"
const selectedDcmpKey = "prefer_info-prefer_desc_select"
const canSelectDcmpKey = "prefer_info-prefer_desc"
const priorityFeatureDcmpKey = "prefer_info-priority_feature"
const preferUrlToggle = "gs_prefer_url"
const firstClassAdditionalServiceControl = "first_class_additional_service_controller"
const ENUS = "en-US"
const ZHHK = "zh-HK"
const customFeatureUfsKey = "tailor.custom_feature"
const guideNewDefaultCheck = "gs_cs_guide_new_default_check_switch"

type PreferDataProvider interface {
	GetLang() string
	GetCityID() int
	GetToCityID() int
	GetCountyID() int
	GetPageType() int32
	GetPassengerId() string
	GetDesignatedDriver() string
	GetAccessKeyId() int32
	GetAppVersion() string
	GetEstimateId() string
	GetTaxiSpsData() *hestia_charge.TaxiSpsData
	GetSpsFeeMap() map[int64]*Sps.FeeItem
	GetPcId2CustomFeature() map[int64][]dos.CustomFeatureStruct
	GetBillTaxiPeakPrice() float64
	GetBillTaxiPeakDiscount() float64
	GetIntelTaxiPeakFee() int64
	IsSwitchTaxiPeakIntel() bool
	GetMultiRequireProduct() []models.RequireProduct
	GetOrderType() int16
	GetDepartureTime() int64
	GetServiceList() []*hundunClient.PcServiceData
	GetAthenaRecommend() int32
	GetBasicTotalFee() float64
	GetDynamicTotalFee() float64
	GetPeakFee() *models.PeakFee
	GetUserPID() int64
	render.ProductProvider
	render.ApolloProvider
}

type PreferOptionRsp struct {
	Title          string         `json:"title"`
	SetOptionTitle string         `json:"set_option_title"`
	Icon           string         `json:"icon"`
	ServiceList    map[int]string `json:"service_list"`
	ImContact      string         `json:"im_contact"`
}

type PreferOptionFullConfig struct {
	Title            *LangCfg         `json:"title"`
	SetOptionTitle   *LangCfg         `json:"set_option_title"`
	ServiceList      []*OptionService `json:"service_list"`
	Head             *LangCfg         `json:"head"`
	HeadLink         string           `json:"head_link"`
	HeadLinkCity     string           `json:"head_link_city"`
	IsSupportTitle   int              `json:"is_support_title"`
	IsSupportRemark  int              `json:"is_support_remark"`
	IsImDirectSend   int              `json:"is_im_direct_send"`
	HeadTitle        *LangCfg         `json:"head_title"`
	HeadTitleCity    *LangCfg         `json:"head_title_city"`
	HeadSubTitle     *LangCfg         `json:"head_sub_title"`
	HeadSubTitleCity *LangCfg         `json:"head_sub_title_city"`
	HeadImg          string           `json:"head_img"`
	HeadImgCity      string           `json:"head_img_city"`
	StartBgColor     string           `json:"start_bg_color"`
	EndBgColor       string           `json:"end_bg_color"`
	Boardcast        string           `json:"boardcast"`
	ImContact        string           `json:"im_contact"`
	ImContactEn      string           `json:"im_contact_en"`
	Icon             string           `json:"icon"`
}

type OptionService struct {
	OptionId int      `json:"option_id"`
	Title    *LangCfg `json:"title"`
}

type LangCfg struct {
	Chinese  string `json:"zh-CN"`
	English  string `json:"en-US"`
	Hongkong string `json:"zh-HK"`
}

type PreferOptionCityConfig struct {
	City          string   `json:"city"`
	OptionService []string `json:"option_service"`
}

type PriorityDcmpInfo struct {
	PriorityServiceId []int  `json:"priority_service_id"`
	Conjunction       string `json:"conjunction"`
}

func GetPreferData(ctx context.Context, prov PreferDataProvider, estimateData *proto.V3EstimateData) *proto.V3PreferData {
	// 专豪个性化服务不透出
	if isNotDisplayCustomService(prov) {
		return nil
	}

	if product_id.ProductIdDefault == prov.GetProductId() {
		return GetPreferDataOfCustomService(ctx, prov)
	}

	// todo: redis
	if product_id.ProductIdHLTaxiCar == prov.GetProductId() {
		return GetPreferDataOfHkTaxiFeature(ctx, prov)
	}

	if product_id.ProductIdFirstClassCar == prov.GetProductId() {
		return GetPreferDataOfFirstClass(ctx, prov)
	}

	if product_id.ProductIdUniOne == prov.GetProductId() {
		return GetPreferDataOfUniTaxiFeature(ctx, prov, estimateData)
	}

	return nil
}

// isNotDisplayCustomService 香港支付宝滴小在内地打车是否在勾选的时候展示个性化服务
func isNotDisplayCustomService(prov PreferDataProvider) bool {
	if prov.GetAccessKeyId() == access_key_id.AccessKeyIdHKAlipayWebapp && prov.GetLang() == lang2.LanguageZhHK &&
		prov.GetCityID() != HKCityId && prov.GetToCityID() != HKCityId {
		return true
	}

	return false
}

func GetPreferDataOfCustomService(ctx context.Context, prov PreferDataProvider) *proto.V3PreferData {
	s3eServiceMap := getAllAvailableCustomService(ctx, prov)

	// city config service
	preferOption := getPreferOptionConfig(ctx, prov)
	serviceConfig := getServiceConfigByLang(preferOption, prov.GetLang())

	// empty return nil
	if len(s3eServiceMap) == 0 && (serviceConfig == nil || serviceConfig.ServiceList == nil || len(serviceConfig.ServiceList) == 0) {
		return nil
	}

	// custom_features
	userCustomFeatureMap := getUserCustomFeature(ctx, prov)

	// option_settings
	chosenOptions := getOptionSettings(prov)
	chosenPreferOption := getUserChosenPreferOption(ctx, prov, chosenOptions, serviceConfig.ServiceList)

	// getSelectDesc 可选服务 已选服务
	selectDesc := dcmp.GetDcmpContent(ctx, canSelectDcmpKey, nil)
	if len(userCustomFeatureMap) > 0 || (chosenOptions != nil && len(chosenPreferOption) > 0) {
		selectDesc = dcmp.GetDcmpContent(ctx, selectedDcmpKey, nil)
	}

	// tryGetPreferContent
	preferDataTag := getTagList(ctx, s3eServiceMap, userCustomFeatureMap, chosenPreferOption, serviceConfig)

	// $oFeatureToggle => jump url
	jumpUrl := ""
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey, biz_runtime.WithProductID)
	allow, parameter := apollo.GetParameters(preferUrlToggle, pidKey, params)
	if allow && parameter != nil && len(parameter) > 0 {
		jumpUrl = parameter["jump_url"]
	}

	return &proto.V3PreferData{
		Desc:    selectDesc,
		TagList: []*proto.NewFormPreferDataTag{preferDataTag},
		JumpUrl: &jumpUrl,
	}
}

func getUserCustomFeature(ctx context.Context, prov PreferDataProvider) map[int]int {
	userCustomFeatureMap := make(map[int]int)
	chosenFeatures := getCustomFeatures(ctx, prov)
	if chosenFeatures != nil && len(chosenFeatures) > 0 {
		for _, feature := range chosenFeatures {
			userCustomFeatureMap[int(feature.ID)] = int(feature.Count)
		}
	}

	// 默认勾选机场助理
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	toggle := apollo.FeatureToggle(ctx, guideNewDefaultCheck, pidKey, params)
	if toggle && defaultCheckGuideNew(ctx, prov) {
		userCustomFeatureMap[customServiceGuideNew] = 1
	}

	return userCustomFeatureMap
}

func defaultCheckGuideNew(ctx context.Context, prov PreferDataProvider) bool {
	var zeroFlag bool
	serviceList := prov.GetServiceList()
	if serviceList == nil || len(serviceList) == 0 {
		return false
	}
	for _, service := range serviceList {
		if service.ServiceId == customServiceGuideNew {
			if service.FeeInfo != nil && service.FeeInfo.DiscountPrice == 0 {
				zeroFlag = true
			}
			break
		}
	}

	if !zeroFlag {
		return false
	}

	if util.IsNA(prov.GetAccessKeyId()) && util.CompareAppVersion(prov.GetAppVersion(), "7.0.10") >= 0 ||
		util.IsMini(prov.GetAccessKeyId()) && util.CompareAppVersion(prov.GetAppVersion(), "6.10.50") >= 0 {
		return defaultCheckGuideNewByUfs(ctx, prov)
	} else {
		return defaultCheckGuideNewOld(ctx, prov)
	}
}

func defaultCheckGuideNewByUfs(ctx context.Context, prov PreferDataProvider) bool {
	multiRequireProduct := prov.GetMultiRequireProduct()
	if multiRequireProduct == nil || len(multiRequireProduct) == 0 {
		return true
	}

	feature, err := ufs.GetFeature(ctx, ufs.DomainPassenger, []string{customFeatureUfsKey}, map[string]string{
		"passenger_id": prov.GetPassengerId(),
	}, "")
	if err != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "prefer_data getFeatureErr=%v", err)
		return false
	}

	if feature == nil || feature[customFeatureUfsKey] == nil || feature[customFeatureUfsKey].Value == nil {
		return false
	}

	var customFeatureList []dos.CustomFeatureStruct
	err = json.Unmarshal([]byte(*feature[customFeatureUfsKey].Value), &customFeatureList)
	if err != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "prefer_data unmarshal customFeatureErr=%v", err)
		return false
	}

	for _, customFeature := range customFeatureList {
		if customFeature.Id == customServiceGuideNew {
			if customFeature.Count == 0 {
				return true
			} else {
				return false
			}

		}
	}

	return false
}

func defaultCheckGuideNewOld(ctx context.Context, prov PreferDataProvider) bool {
	multiRequireProduct := prov.GetMultiRequireProduct()
	if multiRequireProduct == nil || len(multiRequireProduct) == 0 {
		return true
	}

	notSelected := true
	for _, requireProduct := range multiRequireProduct {
		if requireProduct.ProductCategory == product_category.ProductCategoryLuxuryAny || requireProduct.ProductCategory == product_category.ProductCategoryLuxurySixSeatAny && requireProduct.IsSelected == 1 {
			notSelected = false
			break
		}
	}

	if notSelected {
		return true
	}

	return false
}

func getTagList(ctx context.Context, customServiceMap map[int]*hundunClient.PcServiceData, userChosenFeature map[int]int, chosenPreferOption map[int]string, serviceConfig *PreferOptionRsp) *proto.NewFormPreferDataTag {
	var chosenContent = make([]string, 0)
	for serviceId, service := range customServiceMap {
		if _, ok := userChosenFeature[serviceId]; !ok {
			continue
		}

		if userChosenFeature[serviceId] > 0 {
			chosenContent = append(chosenContent, service.Title)
		}
	}

	if len(chosenPreferOption) > 0 {
		for _, optionTitle := range chosenPreferOption {
			chosenContent = append(chosenContent, optionTitle)
		}
	}

	if len(chosenContent) > 0 {
		return &proto.NewFormPreferDataTag{
			Content: strings.Join(chosenContent, "/"),
			Icon:    serviceConfig.Icon,
		}
	}

	return &proto.NewFormPreferDataTag{
		Content: getMaxPriorityServiceTitle(ctx, customServiceMap) + serviceConfig.Title,
		Icon:    serviceConfig.Icon,
	}

}

func getUserChosenPreferOption(ctx context.Context, prov PreferDataProvider, chosenOption *models.OptionSetting, serviceList map[int]string) map[int]string {
	if chosenOption == nil || len(chosenOption.Options) == 0 {
		pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey, biz_runtime.WithProductID)
		toggle := apollo.FeatureToggle(ctx, "gs_option_setting_from_ufs", pidKey, params)
		if toggle {
			chosenOption = getUserChosenPreferOptionByUfs(ctx, prov)
		}
	}

	if chosenOption == nil || len(chosenOption.Options) == 0 {
		return nil
	}

	var chosenPreferOption = make(map[int]string)
	for _, option := range chosenOption.Options {
		if len(serviceList[int(option.OptionId)]) <= 0 || option.Count <= 0 {
			continue
		}

		chosenPreferOption[int(option.OptionId)] = serviceList[int(option.OptionId)]
	}

	return chosenPreferOption
}

func getUserChosenPreferOptionByUfs(ctx context.Context, prov PreferDataProvider) *models.OptionSetting {
	chosenOption := &models.OptionSetting{}
	conditions := map[string]string{"passenger_id": prov.GetPassengerId(), "require_level": prov.GetRequireLevel()}
	ret, err := ufs.GetFeature(ctx, "passenger.option_data", []string{Info}, conditions, "")
	if err != nil || ret[Info] == nil || ret[Info].Errno != 0 || ret[Info].Value == nil || *ret[Info].Value == "" {
		return nil
	}

	err = json.Unmarshal([]byte(*ret[Info].Value), &chosenOption)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "json unmarshal option_data ufs error : %s \n %v", *ret[Info].Value, err.Error())
		return nil
	}

	return chosenOption

}

func getAllAvailableCustomService(ctx context.Context, prov PreferDataProvider) map[int]*hundunClient.PcServiceData {
	customServiceMap := make(map[int]*hundunClient.PcServiceData)
	customServiceList := prov.GetServiceList()
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	for _, service := range customServiceList {
		params["service_id"] = util.ToString(service.ServiceId)
		if apollo.FeatureToggle(ctx, firstClassAdditionalServiceControl, pidKey, params) {
			customServiceMap[int(service.ServiceId)] = service
		}
	}

	return customServiceMap
}

func getServiceConfigByLang(config *PreferOptionFullConfig, lang string) *PreferOptionRsp {
	var rsp = &PreferOptionRsp{
		ServiceList: make(map[int]string),
	}
	if config == nil {
		return rsp
	}

	switch lang {
	case ZHHK:
		rsp.Title = config.Title.Hongkong
		rsp.SetOptionTitle = config.SetOptionTitle.Hongkong
		for _, service := range config.ServiceList {
			rsp.ServiceList[service.OptionId] = service.Title.Hongkong
		}

	case ENUS:
		rsp.Title = config.Title.English
		rsp.SetOptionTitle = config.SetOptionTitle.English
		for _, service := range config.ServiceList {
			rsp.ServiceList[service.OptionId] = service.Title.English
		}

	default:
		rsp.Title = config.Title.Chinese
		rsp.SetOptionTitle = config.SetOptionTitle.Chinese
		for _, service := range config.ServiceList {
			rsp.ServiceList[service.OptionId] = service.Title.Chinese
		}
	}

	rsp.Icon = config.Icon

	if "en-US" == lang {
		rsp.ImContact = config.ImContactEn
	}

	return rsp
}

func getPreferOptionConfig(ctx context.Context, prov PreferDataProvider) *PreferOptionFullConfig {
	// 城市灰度
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey, biz_runtime.WithProductID)
	if !apollo.FeatureToggle(ctx, optionOpenControl, pidKey, params) {
		return nil
	}

	// get full config
	var configInfofull = &PreferOptionFullConfig{}
	var fullConfigs []PreferOptionFullConfig
	condition := model.NewCondition(map[string]string{"product_id": strconv.Itoa(int(prov.GetProductId()))})
	if rawFullCfg := getConfig(ctx, optionServiceConfig, condition); rawFullCfg != nil && len(rawFullCfg) > 0 {
		if err := json.Unmarshal(rawFullCfg, &fullConfigs); err == nil {
			configInfofull = &fullConfigs[0]
		}
	}

	if fullConfigs == nil || len(fullConfigs) == 0 {
		return nil
	}

	// get city config
	var configInfoCity = &PreferOptionCityConfig{}
	var cityConfigs []PreferOptionCityConfig
	condition = model.NewCondition(map[string]string{"city_id": strconv.Itoa(prov.GetCityID())})
	if rawCityCfg := getConfig(ctx, cityServiceConfig, condition); rawCityCfg != nil && len(rawCityCfg) > 0 {
		if err := json.Unmarshal(rawCityCfg, &cityConfigs); err == nil {
			configInfoCity = &cityConfigs[0]
		}
	}

	if cityConfigs == nil || len(cityConfigs) == 0 ||
		configInfoCity.OptionService == nil || len(configInfoCity.OptionService) == 0 {
		return configInfofull
	}

	// get intersection of full and city
	newServiceList := make([]*OptionService, 0)
	for _, service := range configInfofull.ServiceList {
		if util.InArrayStr(strconv.Itoa(service.OptionId), configInfoCity.OptionService) {
			newServiceList = append(newServiceList, service)
		}
	}

	configInfofull.ServiceList = newServiceList
	return configInfofull
}

func getConfig(ctx context.Context, configName string, condition *model.Condition) []byte {

	configs, err := apollo.GetConfigsByNamespaceAndConditions(ctx, configName, condition)

	if configs == nil || err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "apollo config read err with %v and conf: %v", err, configName)
		return nil
	}

	return configs
}

func getMaxPriorityServiceTitle(ctx context.Context, availableCustomService map[int]*hundunClient.PcServiceData) string {
	var selectTile string
	if len(availableCustomService) <= 0 {
		return ""
	}

	rawCfg := dcmp.GetDcmpContent(ctx, priorityFeatureDcmpKey, nil)

	dcmpInfo := &PriorityDcmpInfo{}
	err := json.Unmarshal([]byte(rawCfg), dcmpInfo)
	if err != nil {
		return ""
	}

	for _, serviceId := range dcmpInfo.PriorityServiceId {
		if service, ok := availableCustomService[serviceId]; ok {
			selectTile = service.Title
		}
	}

	if selectTile != "" {
		selectTile = selectTile + dcmpInfo.Conjunction
	}

	return selectTile
}

func getOptionSettings(prov PreferDataProvider) *models.OptionSetting {
	options := &models.OptionSetting{}
	requireProducts := prov.GetMultiRequireProduct()
	if requireProducts != nil && len(requireProducts) > 0 {
		for _, product := range requireProducts {
			if prov.GetProductCategory() == product.ProductCategory {
				_ = json.Unmarshal([]byte(product.OptionSettings), &options)
				return options

			}
		}
	}
	return nil
}

func getCustomFeatures(ctx context.Context, prov PreferDataProvider) []*models.FeatureItem {
	features := make([]*models.FeatureItem, 0)
	requireProducts := prov.GetMultiRequireProduct()
	if requireProducts != nil && len(requireProducts) > 0 {
		for _, product := range requireProducts {
			if prov.GetProductCategory() == product.ProductCategory {
				_ = json.Unmarshal([]byte(product.CustomFeature), &features)
				return features
			}
		}
	}
	return features
}
