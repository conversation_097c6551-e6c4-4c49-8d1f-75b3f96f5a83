package prefer_data

import (
	"context"
	"fmt"
	hundunClient "git.xiaojukeji.com/dirpc/dirpc-go-http-Hundun"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	ApolloSDK "git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/tag_service"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/passenger-common/constant"
	"git.xiaojukeji.com/nuwa/trace"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"github.com/syyongx/php2go"
	"github.com/tidwall/gjson"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
	"strconv"
)

const (
	customServiceLanKeBao = 113
	customServiceTaxiPeak = 107
	taxiPeakFeeConfig     = "taxi_peak_fee"
	taxiPeakFeeConfigV2   = "taxi_peak_fee-peak_passenger_config"
	lanKeBaoConfigV2      = "taxi-lankebao_new_style"

	lanKeBaoVersion    = "lankebao_version_controller"
	lanKeBaoPickUpShow = "lankebao_pick_up_show"
	taxiPreferControl  = "taxi_prefer_data_control"

	tagServiceNS = "tag_service"
	tagName      = "new_user"

	PeakFeeConfigNameSpace            = "taxi_peak_fee"
	PeakFeeSelectNewStrategy          = "taxi_peak_fee_select_new_strategy"
	PreferDataControl                 = "taxi_prefer_data_control"
	InteractiveDefaultTextKeyNewStyle = "taxi_peak_fee-way_out_conf_new_style"
	HolidayTextNewStyle               = "taxi_peak_fee-way_out_conf_holiday_new_style"
	PeakFeeSelection                  = "unione_taxi.peak_fee_selection"
)

type InteractiveDcmpInfo struct {
	NoInteractive                *InteractiveItem `json:"no_interactive"`
	NoInteractiveWithoutEstimate *InteractiveItem `json:"no_interactive_without_estimate"`
	Interactive                  *InteractiveItem `json:"interactive"`
	InteractiveWithoutEstimate   *InteractiveItem `json:"interactive_without_estimate"`
	InteractiveWithDiscount      *InteractiveItem `json:"interactive_with_discount"`
}

type InteractiveItem struct {
	Title       string `json:"title"`
	Content     string `json:"content"`
	BorderColor string `json:"border_color"`
	InfoUrl     string `json:"info_url"`
	Icon        string `json:"icon"`
}

func GetPreferDataOfUniTaxiFeature(ctx context.Context, prov PreferDataProvider, estimateData *proto.V3EstimateData) *proto.V3PreferData {
	if prov.GetPageType() == page_type.PageTypeLankeBao {
		return nil
	}

	taxiPeakFeePreferData := GetPreferDataOfTaxiPeakFee(ctx, prov)
	if taxiPeakFeePreferData != nil {
		return taxiPeakFeePreferData
	}

	return nil
}

func GetPreferDataOfTaxiPeakFee(ctx context.Context, prov PreferDataProvider) *proto.V3PreferData {
	if prov.GetProductId() != product_id.ProductIdUnitaxi {
		return nil
	}

	// 如果是是空说明s3e没开城
	taxiPeakFeeService := getSSSEData(prov, customServiceTaxiPeak)
	if taxiPeakFeeService == nil {
		return nil
	}

	// 出拼NA端旧版本 不支持下挂 不下发prefer_data
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey, biz_runtime.WithProductID)
	params["estimate_id"] = prov.GetEstimateId()
	toggle := ApolloSDK.FeatureToggle(ctx, taxiPreferControl, pidKey, params)
	if !toggle {
		return nil
	}

	// 预约单
	if prov.GetOrderType() == consts.BookOrder {
		return nil
	}

	// 未开城
	taxiPeakFee := prov.GetPeakFee()
	taxiPeakFee.Status = check(ctx, prov)
	if !taxiPeakFee.Status {
		return nil
	}

	// 出租车盒子不展示下挂
	if prov.GetSubGroupId() == consts.SubGroupIdUnitaxi {
		return nil
	}

	if taxiPeakFee.GetIsInterActive() {
		return buildPreferDataForTaxiPeakFee(ctx, prov)
	}

	return nil
}

func getSSSEData(prov PreferDataProvider, serviceId int64) *hundunClient.PcServiceData {
	customServiceList := prov.GetServiceList()
	for _, customService := range customServiceList {
		if serviceId == customService.ServiceId {
			return customService
		}
	}

	return nil
}

func check(ctx context.Context, prov PreferDataProvider) bool {
	// 加载Apollo配置
	if !loadApolloData(ctx, prov) {
		return false
	}

	// 检查开城规则
	if !checkOpenStatus(prov) || !checkAccessKeyId(prov) || !checkCounty(prov) || !checkToggle(ctx, prov) {
		return false
	}

	// 检查语言
	if !checkLang(prov) {
		return false
	}

	// 构建用户选择数据
	buildCustomFeature(ctx, prov)

	// 检查定价服务
	if !checkSpsData(ctx, prov) {
		return false
	}

	// 加载文案配置
	loadDCMP(ctx, prov)

	// 构建峰期加价标识
	buildTaxiPeakFeeFlag(prov)
	return true
}

func loadApolloData(ctx context.Context, prov PreferDataProvider) bool {
	taxiPeakFee := prov.GetPeakFee()
	condition := model.NewCondition(map[string]string{"product_category": util.ToString(prov.GetProductCategory()), "city_id": "0"})
	rawConfigs, err := ApolloSDK.GetConfigsByNamespaceAndConditions(ctx, PeakFeeConfigNameSpace, condition)
	if rawConfigs == nil || err != nil || len(rawConfigs) <= 0 {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "apollo config read err with %v and conf: %v", err, PeakFeeConfigNameSpace)
		return false
	}

	configs := make([]*models.PeakFeeConfig, 0)
	if err = jsoniter.Unmarshal(rawConfigs, &configs); err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "parse travelType err with %v and conf %s", err, string(rawConfigs))
		return false
	}
	if len(configs) <= 0 {
		return false
	}

	taxiPeakFee.PeakFeeConfig = configs[0]
	if taxiPeakFee.PeakFeeConfig == nil {
		return false
	}

	if taxiPeakFee.PeakFeeConfig.OpenStatus == "" || taxiPeakFee.PeakFeeConfig.OpenStatus == "0" {
		return false
	}

	return true
}

// 检查开城开关
func checkOpenStatus(prov PreferDataProvider) bool {
	return util.ToInt64(prov.GetPeakFee().PeakFeeConfig.OpenStatus) == 1
}

// 检查端来源
func checkAccessKeyId(prov PreferDataProvider) bool {
	allowAccessKeyIds := prov.GetPeakFee().PeakFeeConfig.OpenRules.AccessKeyID
	if len(allowAccessKeyIds) == 0 {
		return true
	}

	if util.InArrayStr(util.ToString(prov.GetAccessKeyId()), allowAccessKeyIds) {
		return true
	}

	return false
}

// 检查区县
func checkCounty(prov PreferDataProvider) bool {
	countyConf := prov.GetPeakFee().PeakFeeConfig.OpenRules.CountyConf
	switch countyConf.SupportType {
	case 1:
		return util.InArrayInt(prov.GetCountyID(), countyConf.CountyList)
	case 2:
		// todo: 这里之前写的就是一样的，看看有没有问题
		return prov.GetCountyID() == prov.GetCountyID() && util.InArrayInt(prov.GetCountyID(), countyConf.CountyList)
	default:
		return true
	}
}

func checkToggle(ctx context.Context, prov PreferDataProvider) bool {
	toggleName := prov.GetPeakFee().PeakFeeConfig.OpenRules.LaunchApolloName
	if toggleName == "" {
		return true
	}

	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	return ApolloSDK.FeatureToggle(ctx, toggleName, pidKey, params)
}

// 检查语言
func checkLang(prov PreferDataProvider) bool {
	allowLanguage := prov.GetPeakFee().PeakFeeConfig.OpenRules.Lang
	if len(allowLanguage) == 0 {
		allowLanguage = []string{constant.LanguageZhCN}
	}

	return util.InArrayStr(prov.GetLang(), allowLanguage)
}

// 构建用户选择特征
func buildCustomFeature(ctx context.Context, prov PreferDataProvider) {
	t := prov.GetPeakFee()
	multiRequireProduct := prov.GetMultiRequireProduct()
	if multiRequireProduct == nil || len(multiRequireProduct) == 0 {
		t.IsFirstEstimate = true
	}

	if len(multiRequireProduct) > 0 {
		t.IsFirstEstimate = false
	}

	if !t.IsFirstEstimate {
		// 出拼NA端旧版本 不支持下挂 不下发prefer_data
		pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
		toggle := ApolloSDK.FeatureToggle(ctx, PreferDataControl, pidKey, params)
		if !toggle {
			t.IsUserSelected = true
		}

		pcId2CustomFeature := prov.GetPcId2CustomFeature()
		if pcId2CustomFeature == nil || len(pcId2CustomFeature) == 0 {
			return
		}

		for _, feature := range pcId2CustomFeature[prov.GetProductCategory()] {
			if feature.Id == customServiceTaxiPeak && feature.Count > 0 {
				t.IsUserSelected = true
			}
		}

	} else {
		t.IsUserSelected = isFirstSelect(ctx, prov)
	}

}

// 首次是否选择
func isFirstSelect(ctx context.Context, prov PreferDataProvider) bool {
	if isNewUser(ctx, prov) {
		return true
	}

	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	toggle := ApolloSDK.FeatureToggle(ctx, PeakFeeSelectNewStrategy, pidKey, params)
	if !toggle {
		return true
	}

	return getLastSelect(ctx, prov)
}

func isNewUser(ctx context.Context, prov PreferDataProvider) bool {
	condition := model.NewCondition(map[string]string{"tag_name": "new_user"})
	config, err := ApolloSDK.GetConfigsByNamespaceAndConditions(ctx, "tag_service", condition)
	if err != nil {
		return false
	}
	tagMap := make(map[string]string)
	err = jsoniter.Unmarshal(config, &tagMap)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "unmarshal new_user config err:%v", err)
		return false
	}

	tagNumber := tagMap["tag_number"]
	pid := prov.GetUserPID()
	if tagNumber == "" || pid == 0 {
		return false
	}

	hitTagList, err := tag_service.GetHitTags(ctx, util.ToString(pid), []string{tagNumber})
	if err != nil {
		return false
	}

	if len(hitTagList) > 0 && util.InArrayStr(tagNumber, hitTagList) {
		return true
	}

	return false
}

func getLastSelect(ctx context.Context, prov PreferDataProvider) bool {
	params := map[string]string{
		"passenger_id": strconv.FormatInt(prov.GetUserPID(), 10),
	}

	feature, err := ufs.GetFeatureV2(ctx, ufs.DomainPassenger, PeakFeeSelection, params)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "get ufs feature, key = %v | err = %v", PeakFeeSelection, err)
		return false
	}

	return feature == "1"
}

// 检查SPS数据
func checkSpsData(ctx context.Context, prov PreferDataProvider) bool {
	spsFeeMap := prov.GetSpsFeeMap()
	if spsFeeMap == nil || len(spsFeeMap) == 0 {
		return false
	}

	// 品类无揽客宝数据
	spsFee := spsFeeMap[customServiceTaxiPeak]
	if spsFee == nil {
		return false
	}

	// todo check下逻辑是否返回为true
	if spsFee.ExtraInfo == nil {
		return true
	}

	var spsData map[string]interface{}
	_ = jsoniter.Unmarshal([]byte(*spsFee.ExtraInfo), &spsData)
	t := prov.GetPeakFee()
	t.SpsData = &models.SpsData{
		PassengerPrice:    cast.ToInt(spsData["passenger_price"]),
		DriverPrice:       cast.ToInt(spsData["driver_price"]),
		CanSelect:         cast.ToInt(spsData["can_select"]),
		PassengerDiscount: 0,
		IsHoliday:         cast.ToInt(spsData["holiday"]) == 1,
	}
	return true
}

// 加载DCMP配置
func loadDCMP(ctx context.Context, prov PreferDataProvider) {
	t := prov.GetPeakFee()
	dcmpKey := InteractiveDefaultTextKeyNewStyle
	if t.IsHoliday {
		dcmpKey = HolidayTextNewStyle
	}

	dcmpContent := dcmp.GetJSONResult(ctx, dcmpKey, nil)
	t.TextConfig = dcmpContent
}

// 构建峰期加价标识
func buildTaxiPeakFeeFlag(prov PreferDataProvider) {
	t := prov.GetPeakFee()
	switch {
	case t.GetIsInterActive() && t.GetIsUserSelect():
		t.IsTaxiPeakFeeFlag = true
	case t.GetIsInterActive() && t.GetIsFirstEstimate():
		t.IsTaxiPeakFeeFlag = true
	case !t.GetIsInterActive():
		t.IsTaxiPeakFeeFlag = true
	default:
		t.IsTaxiPeakFeeFlag = false
	}
}

func buildPreferDataForTaxiPeakFee(ctx context.Context, prov PreferDataProvider) *proto.V3PreferData {
	var feeAmount string
	taxiPeakFee := prov.GetPeakFee()
	isSelect := taxiPeakFee.GetIsUserSelect()
	infoUrl := taxiPeakFee.TextConfig["interactive"].Get("info_url").String()
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	toggle := ApolloSDK.FeatureToggle(ctx, "taxi_peak_fee_rule_propaganda", pidKey, params)
	if toggle {
		infoUrl = dcmp.GetJSONResult(ctx, taxiPeakFeeConfigV2, nil)["h5_url"].String()
		infoUrl = fmt.Sprintf(infoUrl, php2go.HTTPBuildQuery(map[string][]string{
			"departure_time":   {util.ToString(prov.GetDepartureTime())},
			"product_category": {util.ToString(prov.GetProductCategory())},
			"product_id":       {util.ToString(prov.GetProductId())},
			"estimate_id":      {prov.GetEstimateId()},
			"county_id":        {util.ToString(prov.GetCountyID())},
			"trip_cityid":      {util.ToString(prov.GetCityID())},
		}))
	}
	feeAmount = getPeakPriceAmount(ctx, prov)
	wayOutConf := GetWayOutConf(taxiPeakFee)
	feeMsgTemplate := wayOutConf.Get("content").String()
	feeMsg := fmt.Sprintf(feeMsgTemplate, feeAmount)

	return &proto.V3PreferData{
		Desc:       wayOutConf.Get("title").String(),
		FeeMsg:     feeMsg,
		InfoUrl:    infoUrl,
		Id:         util.Int32Ptr(customServiceTaxiPeak),
		Count:      1,
		IsSelected: util.ToInt32(isSelect),
		TagList:    make([]*proto.NewFormPreferDataTag, 0),
	}
}

func getPeakPriceAmount(ctx context.Context, prov PreferDataProvider) string {
	taxiPeakFee := prov.GetPeakFee()
	taxiPeakPrice := prov.GetBillTaxiPeakPrice()
	taxiPeakDiscount := prov.GetBillTaxiPeakDiscount()
	if taxiPeakPrice == 0 {
		taxiPeakPrice = util.ToFloat64(taxiPeakFee.SpsData.PassengerPrice)
	}

	if taxiPeakDiscount == 0 {
		taxiPeakDiscount = util.ToFloat64(taxiPeakFee.SpsData.PassengerDiscount)
	}

	if taxiPeakPrice != 0 && taxiPeakDiscount != 0 {
		taxiPeakPrice = taxiPeakPrice + taxiPeakDiscount
	}

	if taxiPeakDiscount != 0 {
		taxiPeakFee.HasDiscount = true
	}

	return GetStringPrice(util.ToInt(taxiPeakPrice), taxiPeakFee)
}

func GetStringPrice(price int, t *models.PeakFee) string {
	if price <= 0 {
		if t.SpsData == nil || t.SpsData.PassengerPrice <= 0 {
			return "0"
		}
		price = t.SpsData.PassengerPrice
	}

	if price%100 == 0 {
		return strconv.Itoa(price / 100)
	}

	return fmt.Sprintf("%.2f", float64(price)/100.0)
}

// GetWayOutConf 构建出口文案
func GetWayOutConf(t *models.PeakFee) gjson.Result {
	textConf := t.TextConfig
	// 无出口
	if !t.GetIsInterActive() {
		if t.GetIsShowEstimatePrice() {
			return textConf["no_interactive"]
		} else {
			return textConf["no_interactive_without_estimate"]
		}
	} else {
		// 有出口
		if t.HasDiscount && !t.IsHoliday {
			return textConf["interactive_with_discount"]
		} else {
			return textConf["interactive"]
		}
	}
}
