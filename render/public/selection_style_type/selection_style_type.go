package selection_style_type

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
)

const (
	PreOrderShowOld = 0
	PreOrderShowNew = 1
)

type SelectionStyleTypeProvider interface {
	GetOrderType() int16
}

func GetSelectionStyleType(ctx context.Context, prov SelectionStyleTypeProvider) *int32 {
	if prov.GetOrderType() != consts.BookOrder {
		return util.Int32Ptr(PreOrderShowOld)
	}

	return util.Int32Ptr(PreOrderShowNew)
}
