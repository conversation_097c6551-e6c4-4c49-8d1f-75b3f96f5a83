package consts

import "git.xiaojukeji.com/gulfstream/mamba/idl/proto"

const (

	// SingleGroupType 普通单车型
	SingleGroupType = 1
	// SpaciousGroupType 车大
	SpaciousGroupType = 1
	// ShortDistanceGroupType 三方盒子  / 三方主端二期特惠联盟
	ShortDistanceGroupType = 2
	// TaxiGroupType 出租车盒子
	TaxiGroupType = 3
	// PeakGroupType 高峰盒子
	PeakGroupType = 4
	// TpGroupType TP盒子
	TpGroupType = 5
	// TaxiPricingBoxGroupType 出租车计价盒子
	TaxiPricingBoxGroupType = 6
	// GuideGroupType 导流
	FarMustCheapBoxGroupType = 7
	GuideGroupType           = 99
	// GuideGroupType2 带主题的导流
	GuideGroupType2 = 100
	// GuideGroupType3 带主题的导流（支持司乘议价改价功能）
	GuideGroupType3 = 101

	// TopArea 置顶区域
	TopArea = 1
	// RecommendArea 推荐区域
	RecommendArea = 2
	// OtherArea 更多车型区域
	OtherArea = 3

	// CHECK 勾选
	CHECK = 1
	// UNCHECK 不勾选
	UNCHECK = 0

	// RecPosTopArea 置顶
	RecPosTopArea = 1
	// RecPosRecommendArea 推荐区
	RecPosRecommendArea = 2
	// RecPosOtherArea 非推荐区
	RecPosOtherArea = 0

	// AppendCarNamespace 追加车型命名空间
	AppendCarNamespace = "AnycarEstimateConf_6_5"
	// PeakGroupBoxNamespace PeekGroupBox命名空间
	PeakGroupBoxNamespace = "peak_group_box"
	// NewGroupBox 配置name
	NewGroupBox = "new_group_box"
	// WYCBubbleRank 网约车冒泡排序命名空间
	WYCBubbleRank = "wyc_bubble_rank"
	// SortListConfNameSpace 排序配置命名空间
	SortListConfNameSpace = "products_sort"

	// CarSortNamespace 排序配置名称
	CarSortNamespace = "car_sort"

	// TreatmentGroup 对照组
	TreatmentGroup = "treatment_group"
	// ControlGroup 控制组
	ControlGroup = "control_group"

	// TaxiBoxType 出租车盒子类型
	TaxiBoxType = "taxi"

	// DefaultThemeType 默认主题
	DefaultThemeType = 1
	// BargainThemeType 司乘议价主题（头部包框右边有个大icon）
	BargainThemeType = 4

	// HighWeight 万最高优先级
	FiftyThousandWeight = 50000
	HighWeight          = 30000
	// MidWeight  万中优先级
	MidWeight = 20000
	// LowWeight 万低优先级
	LowWeight = 10000
	// HighThousandWeight 千高优先级
	HighThousandWeight = 3000
	// MidThousandWeight 千中优先级
	MidThousandWeight = 2000
	// LowThousandWeight 千低优先级
	LowThousandWeight = 1000
	// NegativeThousandWeight 千负优先级 用于区域内置底
	NegativeThousandWeight = -7000
	// HighHundredWeight 百高优先级
	HighHundredWeight = 300
	// MidTHundredWeight 百中优先级
	MidTHundredWeight = 200
	// LowHundredWeight 百低优先级
	LowHundredWeight = 100
)

type (
	// AppendCarBox 追加车型盒子配置
	AppendCarBox struct {
		BoxType          string  `json:"box_type"`
		ProductCategorys []int64 `json:"product_categorys"`
	}

	CarSortConf struct {
		ProductsSort []CarSortItem `json:"products_sort"`
	}

	CarSortItem struct {
		ProductCategory int `json:"product_category"`
	}

	// PeakGroupBox 高峰期盒子配置
	PeakGroupBox struct {
		ThemeKey            string       `json:"theme_key"`
		Title               string       `json:"title"`
		SubTitle            string       `json:"sub_title"`
		TitleIcon           string       `json:"title_icon"`
		RightImage          string       `json:"right_image"`
		TextureImage        string       `json:"texture_image"`
		ThemeColor          string       `json:"theme_color"`
		SelectedBgGradients *BgGradients `json:"selected_bg_gradients"`
		OutBgGradients      *BgGradients `json:"out_bg_gradients"`
	}

	// BgGradients 渐变色
	BgGradients struct {
		StartColor string `json:"start_color"`
		EndColor   string `json:"end_color"`
	}
)

type SimpleEstimateData struct {
	FeeDescList []*proto.NewFormFeeDesc
}
