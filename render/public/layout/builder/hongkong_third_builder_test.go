package builder

import (
	"context"
	"testing"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	feedescutil "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/util"
	LayoutUtil "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/builder/util"
	layoutConsts "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

func TestHongKongThirtyBuilder_getFeeDescList(t *testing.T) {
	ctx := context.Background()
	// 构造estimateData
	feeDesc := &proto.NewFormFeeDesc{Type: 1, Content: "desc1"}
	estimateData := map[int64]*layoutConsts.SimpleEstimateData{
		100: {FeeDescList: []*proto.NewFormFeeDesc{feeDesc}},
	}
	builder := &HongKongThirtyBuilder{
		baseBuilder: baseBuilder{
			estimateData: estimateData,
			reqData:      &models.BaseReqData{CommonInfo: models.CommonInfo{Lang: ""}},
		},
	}
	// mock feedescutil.BattleHarbourFeeDesc
	patch := mockey.Mock(feedescutil.BattleHarbourFeeDesc).Return([]*proto.NewFormFeeDesc{feeDesc}).Build()
	defer patch.UnPatch()

	res := builder.getFeeDescList(ctx, []int64{100}, []int32{})
	assert.Len(t, res, 1)
	assert.Equal(t, "desc1", res[0].Content)
}

func TestHongKongThirtyBuilder_BuildGroups(t *testing.T) {
	ctx := context.Background()
	// 构造estimateData
	feeDesc := &proto.NewFormFeeDesc{Type: 1, Content: "desc1"}
	estimateData := map[int64]*layoutConsts.SimpleEstimateData{
		100: {FeeDescList: []*proto.NewFormFeeDesc{feeDesc}},
	}
	// 构造subProjects
	subProjects := map[int64]*biz_runtime.ProductInfoFull{
		100: {
			Product: &models.Product{
				BizInfo: &models.PrivateBizInfo{CheckStatus: layoutConsts.CHECK},
			},
		},
	}
	// 构造reqData
	reqData := &models.BaseReqData{
		CommonInfo: models.CommonInfo{Lang: "", AccessKeyID: 1, AppVersion: "8.0.0"},
		CommonBizInfo: models.CommonBizInfo{
			AthenaResult: models.AthenaResult{SubGroup2RecPos: map[int32]int32{1: layoutConsts.RecPosRecommendArea}},
		},
	}
	// mock dcmp.GetJSONContentWithPath
	mockConf := `{"Products":["100"],"Type":1,"GroupId":"1_1","IsSelected":1}`
	patch := mockey.Mock(dcmp.GetJSONContentWithPath).Return(mockConf).Build()
	defer patch.UnPatch()
	// mock LayoutUtil.SortByValue
	patchSort := mockey.Mock(LayoutUtil.SortByValue).Return([]string{"100"}).Build()
	defer patchSort.UnPatch()
	// mock feedescutil.BattleHarbourFeeDesc
	patchFee := mockey.Mock(feedescutil.BattleHarbourFeeDesc).Return([]*proto.NewFormFeeDesc{feeDesc}).Build()
	defer patchFee.UnPatch()

	builder := &HongKongThirtyBuilder{
		baseBuilder: baseBuilder{
			estimateData: estimateData,
			reqData:      reqData,
		},
		subProjects: subProjects,
		subGroupId:  1,
	}
	builder.setBaseData(ctx)
	groups := builder.BuildGroups(ctx)
	assert.Len(t, groups, 1)
	assert.Len(t, groups[0].FeeDescList, 1)
	assert.Equal(t, "desc1", groups[0].FeeDescList[0].Content)
}

func TestHongKongThirtyBuilder_setBaseData(t *testing.T) {
	ctx := context.Background()
	subProjects := map[int64]*biz_runtime.ProductInfoFull{
		100: {
			Product: &models.Product{
				BizInfo: &models.PrivateBizInfo{CheckStatus: layoutConsts.CHECK},
			},
		},
		200: {
			Product: &models.Product{
				BizInfo: &models.PrivateBizInfo{CheckStatus: 0},
			},
		},
	}
	reqData := &models.BaseReqData{
		CommonInfo: models.CommonInfo{Lang: "", AccessKeyID: 1, AppVersion: "8.0.0"},
	}
	patch := mockey.Mock(dcmp.GetJSONContentWithPath).Return(`{"mock":"conf"}`).Build()
	defer patch.UnPatch()
	patchSort := mockey.Mock(LayoutUtil.SortByValue).Return([]string{"100", "200"}).Build()
	defer patchSort.UnPatch()

	builder := &HongKongThirtyBuilder{
		baseBuilder: baseBuilder{
			reqData: reqData,
		},
		subProjects: subProjects,
		subGroupId:  1,
	}
	builder.setBaseData(ctx)
	assert.Contains(t, builder.allPcIds, int64(100))
	assert.Contains(t, builder.allPcIds, int64(200))
	assert.Contains(t, builder.checkPcIds, int64(100))
	assert.Equal(t, int32(layoutConsts.CHECK), builder.isSelected)
	assert.Equal(t, []string{"100", "200"}, builder.pcIds)
}
