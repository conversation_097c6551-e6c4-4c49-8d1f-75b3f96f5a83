package builder

import (
	"context"
	"strconv"

	commonConsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	common_consts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	fee_consts "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/anycar_v3/layout/consts"
	LayoutConsts "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
)

const UniTaxiBoxLayoutKeyDcmpKey = "anycar_estimate-unitaxi_box_layout_city"

type UniTaxiBoxBuilder struct {
	baseBuilder
	subProjects       map[int64]*biz_runtime.ProductInfoFull
	pcIDs             []string
	checkPCIDs        []int64
	isSelected        int32 // 是否存在被选中
	recPos            int32
	topSubGroupIdList []int64
}

func NewUniTaxiBoxBuilder(subProjects map[int64]*biz_runtime.ProductInfoFull, estimateData map[int64]*LayoutConsts.SimpleEstimateData, data *models.BaseReqData) IBuilder {
	return &UniTaxiBoxBuilder{
		baseBuilder: baseBuilder{
			reqData:      data,
			estimateData: estimateData,
		},
		subProjects: subProjects,
	}
}

func (u *UniTaxiBoxBuilder) GetName() string {
	return "UniTaxiBoxBuilder"
}

func (u *UniTaxiBoxBuilder) Access() bool {
	if len(u.subProjects) == 0 {
		return false
	}

	return true
}

func (u *UniTaxiBoxBuilder) PreBuild(ctx context.Context) {
	u.setPCIDs()
	u.setRecPos()
}

func (u *UniTaxiBoxBuilder) BuildGroups(ctx context.Context) []*proto.NewFormGroup {
	var (
		groups []*proto.NewFormGroup
	)

	//盒子的展示信息，支持by城市配置，所以优选取城市的配置，取不到就用默认的
	boxLayoutConfig := dcmp.GetJSONMap(ctx, UniTaxiBoxLayoutKeyDcmpKey, strconv.FormatInt(int64(u.reqData.AreaInfo.City), 10))
	if len(boxLayoutConfig) == 0 {
		boxLayoutConfig = dcmp.GetJSONMap(ctx, UniTaxiBoxLayoutKeyDcmpKey, "default")
	}

	group := &proto.NewFormGroup{
		Products:           u.pcIDs,
		Type:               int32(consts.ShortDistanceGroupType),
		GroupId:            u.BuildGroupID(commonConsts.ShortDistanceType, commonConsts.SubGroupIdUnitaxi),
		CarIcon:            *dcmp.GetStringDcmpContentByPath(boxLayoutConfig, "car_icon"),
		CarTitle:           *dcmp.GetStringDcmpContentByPath(boxLayoutConfig, "car_title"),
		PopupTitle:         dcmp.GetStringDcmpContentByPath(boxLayoutConfig, "popup_title"),
		UnselectPopupTitle: dcmp.GetStringDcmpContentByPath(boxLayoutConfig, "unselect_popup_title"),
		PopupSubTitle:      dcmp.GetStringDcmpContentByPath(boxLayoutConfig, "popup_sub_title"),
		IsSelected:         u.isSelected,
		FeeDescList:        u.getBoxFeeDescList(ctx),
	}

	groups = append(groups, group)
	u.groups = groups
	return u.groups
}

func (u *UniTaxiBoxBuilder) BuildFormShowType(ctx context.Context) int32 {
	u.formShowType = u.athenaRecommendAreaToFormShowType(u.recPos)
	return u.formShowType
}

func (u *UniTaxiBoxBuilder) BuildTheme(ctx context.Context) *proto.NewFormThemeData {
	return nil
}

func (u *UniTaxiBoxBuilder) BuildWeight(ctx context.Context) int64 {
	u.buildCommonWeight(ctx)
	return u.weight
}

func (u *UniTaxiBoxBuilder) setPCIDs() {
	var (
		pcIDs        []string
		checkedPcIDs []int64
	)

	for pcID, product := range u.subProjects {
		pcIDs = append(pcIDs, strconv.FormatInt(pcID, 10))

		if consts.CHECK == product.Product.BizInfo.CheckStatus {
			u.isSelected = consts.CHECK
			checkedPcIDs = append(checkedPcIDs, pcID)
		}
	}

	u.pcIDs = pcIDs
	u.checkPCIDs = checkedPcIDs
	if u.reqData != nil && u.reqData.CommonBizInfo.TopData != nil {
		u.topSubGroupIdList = u.reqData.CommonBizInfo.TopData.TopSubGroupIdList
	}
}

func (u *UniTaxiBoxBuilder) setRecPos() {
	// 置顶
	if util.InArrayInt64(common_consts.SubGroupIdUnitaxi, u.topSubGroupIdList) {
		u.recPos = consts.RecPosTopArea
	} else if item, ok := u.reqData.CommonBizInfo.SubGroup2RecPos[common_consts.SubGroupIdUnitaxi]; ok {
		u.recPos = item
	} else {
		// 兜底推荐区域
		u.recPos = consts.RecPosRecommendArea
	}
}

func (u *UniTaxiBoxBuilder) getBoxFeeDescList(ctx context.Context) []*proto.NewFormFeeDesc {
	if len(u.checkPCIDs) > 0 {
		return u.getFeeDescList(ctx, u.checkPCIDs, []int32{fee_consts.TypeIncrement, fee_consts.TypeNeutral, fee_consts.TypeDecrement})
	} else {
		pcIDs := make([]int64, 0, len(u.pcIDs))
		for _, pcID := range u.pcIDs {
			pcIDs = append(pcIDs, util.ToInt64(pcID))
		}
		return u.getFeeDescList(ctx, pcIDs, []int32{fee_consts.TypeIncrement, fee_consts.TypeNeutral, fee_consts.TypeDecrement})
	}
}
