package builder

import (
	"context"
	"encoding/json"
	"strconv"

	CommonConsts "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	mambaconsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	feedescutil "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/util"
	LayoutUtil "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/builder/util"
	layoutConsts "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
	"github.com/spf13/cast"
)

var (
	feeMsgTemplateControlMap = map[int]string{
		CommonConsts.AccessKeyIDDiDiIos: "7.0.4",
	}
)

type HongKongThirtyBuilder struct {
	baseBuilder
	subGroupId  int
	subProjects map[int64]*biz_runtime.ProductInfoFull

	recPos     int32
	dcmpConf   string
	isSelected int32
	pcIds      []string
	allPcIds   []int64
	checkPcIds []int64
}

func NewHongKongThirtyBuilder(subGroupId int, subProjects map[int64]*biz_runtime.ProductInfoFull, estimateData map[int64]*layoutConsts.SimpleEstimateData, data *models.BaseReqData) IBuilder {
	return &HongKongThirtyBuilder{
		baseBuilder: baseBuilder{
			reqData:      data,
			estimateData: estimateData,
		},
		subProjects: subProjects,
		subGroupId:  subGroupId,
	}
}

func (h *HongKongThirtyBuilder) GetName() string {
	return "HongKongThirtyBuilder"
}

func (h *HongKongThirtyBuilder) Access() bool {
	if len(h.subProjects) == 0 {
		return false
	}

	return true
}

func (h *HongKongThirtyBuilder) PreBuild(ctx context.Context) {
	h.setBaseData(ctx)
	h.setRecPos()
}

func (h *HongKongThirtyBuilder) BuildGroups(ctx context.Context) []*proto.NewFormGroup {
	group := &proto.NewFormGroup{
		Products:   h.pcIds,
		Type:       int32(layoutConsts.ShortDistanceGroupType),
		GroupId:    h.BuildGroupID(layoutConsts.ShortDistanceGroupType, int64(h.subGroupId)),
		IsSelected: h.isSelected,
	}

	err := json.Unmarshal([]byte(h.dcmpConf), group)
	if err != nil {
		return nil
	}

	if len(h.checkPcIds) != 0 {
		group.FeeDescList = h.getFeeDescList(ctx, h.checkPcIds, []int32{})
	} else {
		group.FeeDescList = h.getFeeDescList(ctx, h.allPcIds, []int32{})
	}
	group.FeeMsgTemplate = h.rebuildFeeMsgTemplate(ctx, group)

	h.groups = []*proto.NewFormGroup{group}
	return h.groups
}

func (h *HongKongThirtyBuilder) getFeeDescList(ctx context.Context, pcIDs []int64, feeTypes []int32) []*proto.NewFormFeeDesc {
	var pcIdToDescs = make(map[int64][]*proto.NewFormFeeDesc)
	for _, id := range pcIDs {
		if data, ok := h.estimateData[id]; ok && data != nil {
			pcIdToDescs[id] = data.FeeDescList
		}
	}

	cnt := 2
	if h.reqData.CommonInfo.Lang == mambaconsts.LangEnUS {
		cnt = 1 // 如果是英文版，则只取一个
	}
	return feedescutil.BattleHarbourFeeDesc(ctx, pcIdToDescs, cnt)
}

func (h *HongKongThirtyBuilder) rebuildFeeMsgTemplate(ctx context.Context, group *proto.NewFormGroup) *string {
	if h.reqData == nil {
		return group.FeeMsgTemplate
	}

	// 如果是老版本 则不下发
	if value, ok := feeMsgTemplateControlMap[cast.ToInt(h.reqData.CommonInfo.AccessKeyID)]; ok && util.CompareAppVersion(h.reqData.CommonInfo.AppVersion, value) < 0 {
		return nil
	}

	return group.FeeMsgTemplate
}

func (h *HongKongThirtyBuilder) BuildFormShowType(context.Context) int32 {
	h.formShowType = h.athenaRecommendAreaToFormShowType(h.recPos)
	return h.formShowType
}

func (h *HongKongThirtyBuilder) BuildTheme(context.Context) *proto.NewFormThemeData {
	return nil
}

func (h *HongKongThirtyBuilder) setRecPos() {
	if item, ok := h.reqData.CommonBizInfo.SubGroup2RecPos[int32(h.subGroupId)]; ok {
		h.recPos = item
	} else {
		h.recPos = layoutConsts.RecPosRecommendArea // 兜底推荐区域
	}
}

func (h *HongKongThirtyBuilder) setBaseData(ctx context.Context) {
	h.dcmpConf = dcmp.GetJSONContentWithPath(ctx, "hk_product-hongkong_third_box_info", nil, cast.ToString(h.subGroupId)+".info")
	h.pcIds = []string{}
	h.checkPcIds = []int64{}

	pcIdToFee := map[string]float64{}
	for pcId, product := range h.subProjects {
		h.allPcIds = append(h.allPcIds, pcId)
		pcIdToFee[strconv.FormatInt(pcId, 10)] = product.GetEstimateFee()

		if layoutConsts.CHECK == product.Product.BizInfo.CheckStatus {
			h.checkPcIds = append(h.checkPcIds, pcId)
			h.isSelected = layoutConsts.CHECK
		}
	}

	h.pcIds = LayoutUtil.SortByValue(pcIdToFee)
}

func (h *HongKongThirtyBuilder) BuildWeight(ctx context.Context) int64 {
	h.buildCommonWeight(ctx)
	return h.weight
}
