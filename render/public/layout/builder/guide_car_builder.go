package builder

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/carpool_type"
	"net/url"
	"strconv"
	"strings"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	constsCommon "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	EstimateV4 "git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v4/data"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/estimate_v4"
	LayoutConsts "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
	"git.xiaojukeji.com/gulfstream/tripcloud-common-go/models/product"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

const (
	NA           = "na-v3"
	webapp_path  = "webapp_path"
	DefaultPCID  = "default"
	GuidePathKey = "estimate_form_v3-guide_path"
)

type GuideCarBuilder struct {
	baseBuilder
	guideCarPcId int32
	guideCarItem *biz_runtime.ProductInfoFull
	pcIDs        []string
	recPos       int32
}

func NewGuideCarBuilder(guideCarPcId int32, guideCarMap *biz_runtime.ProductInfoFull,
	estimateDate map[int64]*LayoutConsts.SimpleEstimateData, data *models.BaseReqData, themeData *proto.NewFormThemeData) IBuilder {
	return &GuideCarBuilder{
		baseBuilder: baseBuilder{
			reqData:      data,
			estimateData: estimateDate,
			themeData:    themeData,
		},
		guideCarPcId: guideCarPcId,
		guideCarItem: guideCarMap,
	}
}

func (g *GuideCarBuilder) Access() bool {
	if g.guideCarItem != nil {
		return true
	}
	return false
}

func (g *GuideCarBuilder) GetName() string {
	return "GuideCarBuilder"
}

func (g *GuideCarBuilder) PreBuild(ctx context.Context) {
	g.setBaseData(ctx)
	g.setRecPos()
}

func (g *GuideCarBuilder) setBaseData(ctx context.Context) {
	var (
		pcIDs = make([]string, 0)
	)
	pcIDs = append(pcIDs, util.ToString(g.guideCarPcId))
	g.pcIDs = pcIDs
}

func (g *GuideCarBuilder) setRecPos() {
	g.recPos = consts.RecPosTopArea
}

func (g *GuideCarBuilder) BuildGroups(ctx context.Context) []*proto.NewFormGroup {
	var (
		groups []*proto.NewFormGroup
	)

	buttonText := g.GetButtonText(ctx)
	actionType, guideType, guidePath, guideParams := g.getGuidePathActionType(ctx)
	disableShadow := util.ToInt32(g.GetDisableShadow(ctx))
	group := &proto.NewFormGroup{
		Products:      g.pcIDs,
		Type:          consts.GuideGroupType,
		GroupId:       g.BuildGroupID(consts.SingleType, int64(g.guideCarPcId)),
		ButtonText:    &buttonText,
		ButtonStyle:   g.getButtonStyle(ctx),
		IsSelected:    0,
		ActionType:    &actionType,
		GuidePath:     &guidePath,
		GuideStyle:    &guideType,
		DisableShadow: &disableShadow,
	}
	if guideParams != "" {
		group.GuideParams = util.StringPtr(guideParams)
	}
	groups = append(groups, group)
	g.groups = groups
	return g.groups
}

func (g *GuideCarBuilder) BuildFormShowType(ctx context.Context) int32 {
	g.formShowType = g.athenaRecommendAreaToFormShowType(g.recPos)
	return g.formShowType
}

func (g *GuideCarBuilder) BuildTheme(ctx context.Context) *proto.NewFormThemeData {
	return g.GetThemeDataSingleCar(ctx, int64(g.guideCarPcId))
}

func (g *GuideCarBuilder) BuildWeight(ctx context.Context) int64 {
	g.buildCommonWeight(ctx)
	return g.weight
}

func (g *GuideCarBuilder) GetButtonText(ctx context.Context) string {
	var (
		buttonText string
	)

	buttonText = dcmp.GetJSONContentWithPath(ctx, "estimate_form_v3-guide_show_type_text", nil, "button_text")
	if g.guideCarPcId == estimate_pc_id.EstimatePcIdCarpoolInter {
		text := g.getIntercityExp(ctx)
		if text != "" {
			buttonText = text
		}
	}

	if g.guideCarPcId == estimate_pc_id.EstimatePcIdAutoDriving {
		buttonText = g.getSelfDrivingText(ctx)
	}

	return buttonText
}

func (g *GuideCarBuilder) getIntercityExp(ctx context.Context) string {
	apolloKey, apolloParam := g.guideCarItem.GetApolloParams(biz_runtime.WithUIDKey, biz_runtime.WithRouteGroup, biz_runtime.WithComboID)
	isAllow, assignment := apollo.FeatureExp(ctx, "bubble_ganzhi_jining", apolloKey, apolloParam)
	if isAllow {
		return assignment.GetParameter("button_text", "")
	}

	return ""
}

func (g *GuideCarBuilder) getSelfDrivingText(ctx context.Context) string {
	apolloKey, apolloParam := g.guideCarItem.GetApolloParams(biz_runtime.WithPIDKey)
	apolloParam["func_type"] = "2"
	isAllow, params := apollo.GetParameters("auto_driving_fee_msg_open_city", apolloKey, apolloParam)
	if !isAllow {
		return dcmp.GetDcmpPlainContent(ctx, "estimate_form_v3-self_drive_guide_button_text")
	}

	if buttonText, ok := params["button_text"]; ok {
		return buttonText
	} else {
		return ""
	}
}

func (g *GuideCarBuilder) getButtonStyle(ctx context.Context) *proto.ButtonStyle {
	var (
		buttonStyle *proto.ButtonStyle
	)

	isIntercityTC, _ := product.IsTCIntercityCarpoolByProductID(util.ToString(g.guideCarItem.GetProductId()))
	if g.guideCarPcId == estimate_pc_id.EstimatePcIdCarpoolInter || isIntercityTC {
		style := g.getStrongRecButtonStyleIntercity(ctx)
		if style != nil {
			buttonStyle = style
		}
	}

	if g.guideCarPcId == estimate_pc_id.EstimatePcIdCarpoolStation && g.hitButtonStyleExp(ctx) {
		styleConf := dcmp.GetDcmpPlainContent(ctx, "estimate_form_v3-guide_info_button_style")
		if styleConf != "" {
			_ = json.Unmarshal([]byte(styleConf), buttonStyle)
		}
	}

	return buttonStyle
}

func (g *GuideCarBuilder) getStrongRecButtonStyleIntercity(ctx context.Context) *proto.ButtonStyle {
	apolloKey, apolloParam := g.guideCarItem.GetApolloParams(biz_runtime.WithUIDKey)
	isAllow, assignment := apollo.FeatureExp(ctx, "intercity_tupo_1", apolloKey, apolloParam)
	if !isAllow || assignment.GetGroupName() != "control_group" {
		return nil
	}

	borderColor := assignment.GetParameter("border_color", "")
	fontColor := assignment.GetParameter("font_color", "")
	bgGradients := assignment.GetParameter("bg_gradients", "")
	bgGradientsArr := strings.Split(bgGradients, ",")

	return &proto.ButtonStyle{
		BorderColor: borderColor,
		FontColor:   fontColor,
		BgGradients: bgGradientsArr,
	}
}

func (g *GuideCarBuilder) hitButtonStyleExp(ctx context.Context) bool {
	topRec := g.reqData.CommonBizInfo.TopRec
	if topRec == nil || len(topRec.TopRecList) < 1 {
		return false
	}

	topRecItem := topRec.TopRecList[0]
	if topRecItem.ExtraInfo != nil {
		buttonStyle, ok := topRecItem.ExtraInfo["button_style"]
		if ok && buttonStyle == "1" {
			return true
		}
	}

	return false
}

func GetGuidePath(ctx context.Context, guidePathKey string, group string, pcID int32) string {
	dcmpPath := group + "." + util.ToString(pcID)
	guidePath := dcmp.GetJSONContentWithPath(ctx, guidePathKey, nil, dcmpPath)
	if len(guidePath) == 0 {
		dcmpPath := group + "." + DefaultPCID
		tmpGuidePath := dcmp.GetJSONContentWithPath(ctx, guidePathKey, nil, dcmpPath)
		guidePath = util.ReplaceTag(ctx, tmpGuidePath, map[string]string{"product_category": util.ToString(pcID)})
	}
	return guidePath
}

func (g *GuideCarBuilder) getGuidePathActionType(ctx context.Context) (actionType int32, guideStyle int32, guidePath string, guideParams string) {
	if g.reqData.CommonInfo.AccessKeyID == constsCommon.AccessKeyIDDiDiIos ||
		g.reqData.CommonInfo.AccessKeyID == constsCommon.AccessKeyIDDiDiAndroid {
		guidePath = GetGuidePath(ctx, GuidePathKey, NA, g.guideCarPcId)
		if g.guideCarPcId == estimate_pc_id.EstimatePcIdAutoDriving {
			adapter := &EstimateV4.EstimateV4Adapter{
				ProductInfoFull: g.guideCarItem,
			}
			autoAddressInfo := estimate_v4.GetAutoDrivingAddressInfo(ctx, adapter)
			strAddressInfo, err := json.Marshal(autoAddressInfo)
			if err != nil {
				guidePath = guidePath + "&auto_driving_address_info=" + url.QueryEscape(string(strAddressInfo))
			}
		}
	} else {
		guidePath = GetGuidePath(ctx, GuidePathKey, webapp_path, g.guideCarPcId)
	}

	tabList := tab.GetTabList(g.reqData.CommonInfo.TabList)
	oneTab := len(tabList) < 2                             // 是否为单tab
	is8Form := g.reqData.CommonInfo.EstimateStyleType == 4 // 是否是8.0
	if g.isHitCarpoolNewJump(ctx) && ((oneTab && !is8Form && g.isHitCarpoolBubbleExp(ctx)) || g.isGuideJumpCarpoolTab(ctx)) {
		actionType = 1
		guideStyle = 0
		guidePath = "carpool"
		status, params := g.getGuideParams(ctx)
		if status {
			guideParams = params
		}
	}

	return
}

func (g *GuideCarBuilder) isHitCarpoolBubbleExp(ctx context.Context) bool {
	apolloKey, apolloParam := g.guideCarItem.GetApolloParams(biz_runtime.WithPIDKey)
	isAllow, assignment := apollo.FeatureExp(ctx, "ab_test_carpool_guide_back_button", apolloKey, apolloParam)
	if isAllow && assignment.GetParameter("hit_guide_carpool", "0") == "1" {
		return true
	}
	return false
}

func (g *GuideCarBuilder) isHitCarpoolNewJump(ctx context.Context) bool {
	apolloKey, apolloParam := g.guideCarItem.GetApolloParams(biz_runtime.WithPIDKey)
	apolloParam["carpool_type"] = strconv.FormatInt(g.guideCarItem.GetCarpoolType(), 10)
	return apollo.FeatureToggle(ctx, "gs_guidebar_jump_carpool", apolloKey, apolloParam)
}

func (g *GuideCarBuilder) isGuideJumpCarpoolTab(ctx context.Context) bool {
	apolloKey, apolloParam := g.guideCarItem.GetApolloParams(biz_runtime.WithPIDKey)
	return apollo.FeatureToggle(ctx, "carpool_offline_old_page_switch", apolloKey, apolloParam)
}

func (g *GuideCarBuilder) getGuideParams(ctx context.Context) (bool, string) {
	if g.guideCarItem == nil {
		return false, ""
	}

	if util.InArrayInt64(g.guideCarItem.GetProductCategory(), []int64{estimate_pc_id.EstimatePcIdTrainTicket,
		estimate_pc_id.EstimatePcIdCarpoolSFCar, estimate_pc_id.EstimatePcIdCarpoolCrossSFCar}) {
		return false, ""
	}

	if carpool_type.CarpoolTypeInterCityStation == g.guideCarItem.GetCarpoolType() {
		return false, ""
	}

	apolloKey, apolloParam := g.guideCarItem.GetApolloParams(biz_runtime.WithPIDKey)
	allow, params := apollo.GetParameters("gs_guide_trans_data", apolloKey, apolloParam)
	if !allow {
		return false, ""
	}

	if data, ok := params["trans_data"]; ok && data != "" {
		return true, util.ReplaceTag(ctx, data, map[string]string{
			"traceId": util.GetTraceIDFromCtxWithoutCheck(ctx),
		})
	}
	return false, ""
}
