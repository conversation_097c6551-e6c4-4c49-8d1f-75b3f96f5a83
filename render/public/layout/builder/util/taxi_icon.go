package util

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"
	jsoniter "github.com/json-iterator/go"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

type taxiGroupPos string

const (
	TaxiCarAllianceLayout  = taxiGroupPos("TaxiCarAllianceLayout")
	TaxiPricingBoxLayout   = taxiGroupPos("TaxiPricingBoxLayout")
	TaxiTimePriceLayout    = taxiGroupPos("TaxiTimePriceLayout")
	TaxiGroupIconApolloKey = "taxi_group_car_icon"
)

type taxiGroupCarIcon struct {
	Pos     string `json:"pos"`
	City    string `json:"city"`
	CarIcon string `json:"car_icon"`
}

func GetTaxiGroupIcon(ctx context.Context, pos taxiGroupPos, city int32, originalIcon string) string {
	rawConfigs, err := apollo.GetConfigsByNamespaceAndConditions(ctx, TaxiGroupIconApolloKey, model.NewCondition(map[string]string{
		"pos":  string(pos),
		"city": strconv.FormatInt(int64(city), 10),
	}))
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "parse taxi_group_car_icon err with %v and conf %s", err, string(rawConfigs))
		return originalIcon
	}
	if len(rawConfigs) == 0 {
		return originalIcon
	}

	configs := make([]taxiGroupCarIcon, 0)
	if err = jsoniter.Unmarshal(rawConfigs, &configs); err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "parse taxi_group_car_icon err with %v and conf %s", err, string(rawConfigs))
		return ""
	}
	if len(configs) < 1 {
		return originalIcon
	}
	return configs[0].CarIcon
}
