package sort_strategy

import (
	"context"
	"sort"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	PriceCheck "git.xiaojukeji.com/gulfstream/passenger-common/model/price"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"github.com/spf13/cast"

	CommonConsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout/builder"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
)

type PriceSort struct {
	productSort *AthenaApiv3.ProductSortInfo
	weightMap   map[string]int64 // map[pcid]weight

	allProductsMap map[int64]*biz_runtime.ProductInfoFull

	SortStrategyCal
	topBoxLayoutIndex int
}

func NewPriceSort(ctx context.Context, baseReq *models.BaseReqData, allProductsMap map[int64]*biz_runtime.ProductInfoFull) *PriceSort {

	weightMap := buildPriceWeight(ctx, allProductsMap)

	return &PriceSort{
		productSort:       baseReq.CommonBizInfo.ProductSortInfo,
		weightMap:         weightMap,
		SortStrategyCal:   new(AbstractSort),
		topBoxLayoutIndex: -1,
		allProductsMap:    allProductsMap,
	}
}

func buildPriceWeight(ctx context.Context, productsMap map[int64]*biz_runtime.ProductInfoFull) map[string]int64 {
	type productItem struct {
		productCategory int64
		price           float64
		reference       float64
	}

	var (
		weightMap = make(map[string]int64, 0)
		priceList []*productItem
	)

	if len(productsMap) == 0 {
		return weightMap
	}

	for pcid, product := range productsMap {
		item := &productItem{
			productCategory: pcid,
			price:           product.GetEstimateFee(),
			reference:       product.GetEstimateFee(),
		}
		// 惠选车使用推荐价左边界参与排序
		if estimate_pc_id.EstimatePcIdHuiXuanCar == pcid {
			item.price = util.String2float64(ctx, product.GetFastRangeRecommendInfo(CommonConsts.RecommendPriceLower))
			item.reference = item.price
		}

		if shouldUsePersonalEstimateFee(ctx, product) {
			item.price = product.GetPersonalEstimateFee()
		}

		priceList = append(priceList, item)
	}

	sort.SliceStable(priceList, func(i, j int) bool {
		if priceList[i].price > priceList[j].price {
			return true
		} else if priceList[i].price < priceList[j].price {
			return false
		} else {
			return priceList[i].reference > priceList[j].reference
		}
	})

	for index, item := range priceList {
		weightMap[util.ToString(item.productCategory)] = int64(index)
	}

	return weightMap
}

func (s *PriceSort) BuildLayoutSort(ctx context.Context, builderList []builder.IBuilder) []*proto.NewFormLayout {
	var (
		layouts []*proto.NewFormLayout
	)
	// builder构建
	for _, iBuilder := range builderList {
		if !iBuilder.Access() {
			continue
		}
		iBuilder.PreBuild(ctx)
		layoutItem := &proto.NewFormLayout{
			ThemeData:    iBuilder.BuildTheme(ctx),
			Groups:       iBuilder.BuildGroups(ctx),
			FormShowType: iBuilder.BuildFormShowType(ctx),
		}
		targetPcID, targetPrice := s.buildFPrice(ctx, layoutItem.Groups[0].Products)
		layoutItem.Price = targetPrice
		layoutItem.Weight = s.BuildLayoutWeight(s.productSort, layoutItem, s.getLayoutWeightByPcId(targetPcID))
		layouts = s.appendLayout(layouts, layoutItem)
	}

	return s.LayoutBubbleSort(layouts)
}

func (s *PriceSort) appendLayout(layouts []*proto.NewFormLayout, singleLayout *proto.NewFormLayout) []*proto.NewFormLayout {
	// 置顶 但是不是 导流位  且不是自选车勾选态=> 合并
	isBargainCheck := false //司乘议价勾选态
	products := singleLayout.Groups[0].Products
	if len(products) > 0 && cast.ToInt(products[0]) == estimate_pc_id.EstimatePcIdBargain && consts.SingleGroupType == singleLayout.Groups[0].Type {
		isBargainCheck = true
	}
	if singleLayout.FormShowType == consts.TopArea && singleLayout.Groups[0].Type != consts.GuideGroupType && singleLayout.Groups[0].Type != consts.GuideGroupType3 && !isBargainCheck {
		if s.topBoxLayoutIndex != -1 {
			for _, group := range singleLayout.Groups {
				layouts[s.topBoxLayoutIndex].Groups = append(layouts[s.topBoxLayoutIndex].Groups, group)
			}
		} else {
			layouts = append(layouts, singleLayout)
			s.topBoxLayoutIndex = len(layouts) - 1
		}
	} else {
		layouts = append(layouts, singleLayout)
	}

	return layouts
}

func (s *PriceSort) buildFPrice(ctx context.Context, productList []string) (int, float64) {
	var (
		maxPrice   float64
		minPrice   float64
		maxPcID    int
		minPcID    int
		firstPcID  int
		firstPrice float64
		flag       bool
	)

	if len(productList) == 0 {
		return 0, 0
	}

	minPrice = 1000000

	for _, pcID := range productList {
		productItem := s.allProductsMap[util.ToInt64(pcID)]
		if productItem == nil {
			continue
		}

		var price float64

		if shouldUsePersonalEstimateFee(ctx, productItem) {
			price = productItem.GetPersonalEstimateFee()
			PriceCheck.CheckSingle(ctx, "PriceSort", "GetPersonalEstimateFee", "PersonalEstimateFee", price)
		} else {
			price = productItem.GetEstimateFee()
			PriceCheck.CheckSingle(ctx, "PriceSort", "GetEstimateFee", "EstimateFee", price)
		}

		if !flag {
			firstPrice = price
			firstPcID = util.ToInt(pcID)
			flag = true
		}

		if maxPrice < price {
			maxPcID = util.ToInt(pcID)
			maxPrice = price
		}

		if minPrice > price {
			minPcID = util.ToInt(pcID)
			minPrice = price
		}
	}

	if s.productSort.RankSort != nil && *s.productSort.RankSort == CommonConsts.RankSortMinProduct {
		return minPcID, minPrice
	} else if s.productSort.RankSort != nil && *s.productSort.RankSort == CommonConsts.RankSortMaxProduct {
		return maxPcID, maxPrice
	} else {
		return firstPcID, firstPrice
	}
}

func (s *PriceSort) LayoutBubbleSort(layouts []*proto.NewFormLayout) []*proto.NewFormLayout {
	// 排序
	sort.SliceStable(layouts, func(i, j int) bool {
		return layouts[i].Weight > layouts[j].Weight
	})

	return layouts
}

func (s *PriceSort) getLayoutWeightByPcId(targetPcID int) int64 {
	if targetPcID != 0 {
		if w, ok := s.weightMap[cast.ToString(targetPcID)]; ok {
			return w
		}
	}
	return 0
}

func shouldUsePersonalEstimateFee(ctx context.Context, product *biz_runtime.ProductInfoFull) bool {
	return product.GetDefaultPayType() == CommonConsts.BusinessPaymentType || product.GetDefaultPayType() == CommonConsts.BusinessPayByTeam
}
