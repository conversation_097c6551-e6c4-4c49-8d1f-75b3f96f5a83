package fee_prefix_icon

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

type FeeMsgPrefixProvider interface {
	render.DynamicIconABProvider
	render.BillInfoProvider
}

func GetFeeMsgPrefixIcon(ctx context.Context, prov FeeMsgPrefixProvider) *string {
	var (
		normalDynamicTimes float64
		tcDynamicTimes     float64
	)

	bill := prov.GetBillDetail()
	if bill.DynamicDiffPrice > 0 {
		normalDynamicTimes = bill.DynamicTimes + 1
	}
	if bill.DupsDynamicRaise > 0 {
		tcDynamicTimes = bill.DupsDynamicTimes + 1
	}

	if normalDynamicTimes <= 0 && tcDynamicTimes <= 0 {
		return nil
	}

	if prov.IsHitDynamicIconAb(ctx) {
		icon := dcmp.GetDcmpPlainContent(ctx, "estimate_form_v3-dynamic_icon")
		return &icon
	}
	return nil
}
