package operation_list

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	lang2 "git.xiaojukeji.com/gulfstream/tripcloud-common-go/common/lang"
	"git.xiaojukeji.com/nuwa/golibs/knife"
	"git.xiaojukeji.com/nuwa/trace"
)

const (
	RidePreference = "ride_preference"
	RideType       = "1"
	FilterType     = "2"
	RouteType      = "3"
)

type OperationListProvider interface {
	GetTabId() string
	GetUserUID() int64
	GetLat() float64
	GetLng() float64

	render.BaseProvider
	render.ApolloProvider
}

type OperationListConf struct {
	Key    string            `json:"key"`
	Title  string            `json:"title"`
	Link   map[string]string `json:"link"`
	Apollo string            `json:"apollo"`
}

func GetOperationListFormCache(ctx context.Context, prov OperationListProvider) []*proto.OperationItem {
	if v := knife.Get(ctx, consts.OperationListKey); v != nil {
		return v.([]*proto.OperationItem)
	}

	result := GetOperationList(ctx, prov)
	knife.Set(ctx, consts.OperationListKey, result)
	return result
}

func GetOperationList(ctx context.Context, prov OperationListProvider) []*proto.OperationItem {
	var (
		operationList         []*proto.OperationItem
		operationListConfList []*OperationListConf
		rawDrnUrl             string
	)

	// 屏蔽英文
	if prov.GetLang() == lang2.LanguageEnUS {
		return nil
	}

	content := dcmp.GetDcmpContent(ctx, "estimate_form_v3-operation_list", nil)
	if len(content) > 0 {
		err := json.Unmarshal([]byte(content), &operationListConfList)
		if err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "json unmarshal operation_list conf err: %s", err.Error())
		}
	}

	uidKey, params := prov.GetApolloParams(biz_runtime.WithUIDKey)
	params["feature"] = "operation"
	params["caller"] = "mamba"
	if apollo.FeatureToggle(ctx, "ride_preference_use_drn_4", uidKey, params) {
		content = dcmp.GetDcmpContent(ctx, "estimate_form_v3-operation_list_7014up_2", nil)
		if len(content) > 0 {
			// todo: 这块儿逻辑follow V3，但是有点怪
			err := json.Unmarshal([]byte(content), &operationListConfList)
			if err != nil || len(operationListConfList) == 0 {
				log.Trace.Warnf(ctx, trace.DLTagUndefined, "json unmarshal operation_list conf err")
			} else {
				rawDrnUrl = operationListConfList[0].Link["1"]
				if rawDrnUrl == "" {
					rawDrnUrl = operationListConfList[0].Link["2"]
				}

				if rawDrnUrl != "" {
					operationListConfList = []*OperationListConf{operationListConfList[0]}
				}
			}
		}
	}

	operationList = GetOperationListWithExp(ctx, prov, operationListConfList, rawDrnUrl)
	return operationList

}

//
//func GetOperationListWithBaseLine(ctx context.Context, prov OperationListProvider, operationListConfList []*OperationListConf, rawDrnUrl string) []*proto.OperationItem {
//	if operationListConfList == nil || len(operationListConfList) == 0 {
//		return nil
//	}
//
//	operationList := make([]*proto.OperationItem, 0)
//	for _, operationItem := range operationListConfList {
//		accessKeyId := prov.GetAccessKeyId()
//		if rawDrnUrl != "" && (accessKeyId == 1 || accessKeyId == 2) {
//			operationItem.Link[util.ToString(accessKeyId)] = dcmp.TranslateTemplate(rawDrnUrl, map[string]string{
//				"estimate_trace_id": util.GetTraceIDFromCtxWithoutCheck(ctx),
//				"preference_type":   RideType + "_" + FilterType + "_" + RouteType,
//				"latReplace":        util.ToString(prov.GetLat()),
//				"lngReplace":        util.ToString(prov.GetLng()),
//				"cityReplace":       util.ToString(prov.GetCityID()),
//			})
//		}
//		operationList = append(operationList, &proto.OperationItem{
//			Key:   operationItem.Key,
//			Title: operationItem.Title,
//			Link:  operationItem.Link[util.ToString(accessKeyId)],
//		})
//	}
//
//	return operationList
//}

func GetOperationListWithExp(ctx context.Context, prov OperationListProvider, operationListConfList []*OperationListConf, rawDrnUrl string) []*proto.OperationItem {
	if operationListConfList == nil || len(operationListConfList) == 0 {
		return nil
	}

	operationList := make([]*proto.OperationItem, 0)
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	filterInfo := apollo.FeatureToggle(ctx, "gs_filter_ets_feature_delete", pidKey, params)
	for _, operationItem := range operationListConfList {
		flag := false
		if operationItem.Apollo != "" {
			if apollo.FeatureToggle(ctx, operationItem.Apollo, pidKey, params) {
				flag = true
			}
		} else {
			flag = true
		}

		if prov.GetTabId() == tab.TabIdClassify || filterInfo {
			flag = true
		}

		accessKeyId := prov.GetAccessKeyId()
		if flag {
			if rawDrnUrl != "" && (accessKeyId == 1 || accessKeyId == 2) {
				operationItem.Link[util.ToString(accessKeyId)] = dcmp.TranslateTemplate(rawDrnUrl, map[string]string{
					"estimate_trace_id": util.GetTraceIDFromCtxWithoutCheck(ctx),
					"preference_type":   RideType + "_" + FilterType + "_" + RouteType,
					"latReplace":        util.ToString(prov.GetLat()),
					"lngReplace":        util.ToString(prov.GetLng()),
					"cityReplace":       util.ToString(prov.GetCityID()),
				})
			}
		}
		operationList = append(operationList, &proto.OperationItem{
			Key:   operationItem.Key,
			Title: operationItem.Title,
			Link:  operationItem.Link[util.ToString(accessKeyId)],
		})
	}

	return operationList
}
