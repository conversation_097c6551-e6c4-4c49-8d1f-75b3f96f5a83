package seat_module

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

const miniBusSeatModuleDcmpKey = "minibus-carpool_seat_module"

type SeatModuleProvider interface {
	GetCarpoolType() int64
	GetCarpoolSeatNum() int32
	GetPassengerCountOption() []int32
}

func GetSeatModule(ctx context.Context, prov SeatModuleProvider) *proto.NewCarpoolSeatModule {
	var seatModule = &proto.NewCarpoolSeatModule{}
	// 只有小巴才支持该渲染
	if !carpool.IsMiniBus(int(prov.GetCarpoolType())) {
		return nil
	}

	dcmpConf := dcmp.GetDcmpContent(ctx, miniBusSeatModuleDcmpKey, nil)

	err := json.Unmarshal([]byte(dcmpConf), &seatModule)
	if err != nil {
		return nil
	}

	seatModule.SelectCount = 1
	if prov.GetCarpoolSeatNum() > 0 {
		seatModule.SelectCount = prov.GetCarpoolSeatNum()
	}

	seatOption := prov.GetPassengerCountOption()
	if i := len(seatOption); i > 0 {
		seatModule.MaxCount = seatOption[i-1]
	}

	return seatModule
}
