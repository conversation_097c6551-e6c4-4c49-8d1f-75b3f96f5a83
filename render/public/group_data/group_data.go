package group_data

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/golibs/knife"
)

type GroupData struct {
	groupData map[string]*proto.NewFormGroup
}

func NewGroupData(ctx context.Context) *GroupData {
	// 从context缓存中获取
	if v := knife.Get(ctx, consts.GroupData); v != nil {
		return v.(*GroupData)
	}
	g := &GroupData{groupData: make(map[string]*proto.NewFormGroup)}
	knife.Set(ctx, consts.GroupData, g)
	return g
}

func (g *GroupData) AddGroupData(key string, value *proto.NewFormGroup) {
	g.groupData[key] = value
}

func (g *GroupData) GetGroupData() map[string]*proto.NewFormGroup {
	return g.groupData
}
