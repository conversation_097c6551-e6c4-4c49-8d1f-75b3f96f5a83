package objective_remain_num

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type ObjectiveRemainNum struct {
	product         *biz_runtime.ProductInfoFull
	carpoolSeatData *proto.CarpoolSeatData
}

func NewObjectiveRemainNum(product *biz_runtime.ProductInfoFull, carpoolSeatData *proto.CarpoolSeatData) *ObjectiveRemainNum {
	return &ObjectiveRemainNum{
		product:         product,
		carpoolSeatData: carpoolSeatData,
	}
}

func (s *ObjectiveRemainNum) GetObjectiveRemainNum(ctx context.Context) *int32 {
	if s.carpoolSeatData == nil {
		return nil
	}

	objectiveRemainNum := int32(len(s.carpoolSeatData.SeatConfig))

	return &objectiveRemainNum
}
