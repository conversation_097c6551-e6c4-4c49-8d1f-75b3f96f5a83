package multi_route_tips_data

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/golibs/json"
	"git.xiaojukeji.com/nuwa/trace"
)

const MultiRouteTipsDataDCMPKey = "config_text-multi_route_tips_data"

func GetMultiRouteTipsData(ctx context.Context) map[string]string {
	res := make(map[string]string)
	content := dcmp.GetDcmpContent(ctx, MultiRouteTipsDataDCMPKey, nil)
	if content == "" {
		return res
	}

	err := json.Unmarshal([]byte(content), &res)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "multiRouteTipsData json unmarshal err: %s", err.Error())
	}

	return res
}
