package travel_fore_cast

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/nuwa/golibs/knife"
	"git.xiaojukeji.com/nuwa/trace"
)

type TravelForecastProvider interface {
	GetBaseReqData() *models.BaseReqData
	GetFormStyleExp() int32
	GetAvailableServiceList() []int64
	render.ProductProvider
	render.ApolloProvider
}

func GetTravelForeCastFromCache(ctx context.Context, prov TravelForecastProvider) map[string]string {
	var travelForecast map[string]string
	if v := knife.Get(ctx, consts.TravelForecastKey); v != nil {
		bytes, _ := json.Marshal(v)
		err := json.Unmarshal(bytes, &travelForecast)
		if err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "json unmarshal travelForcast error: %v", err)
			return nil
		}
	}

	return travelForecast
}
