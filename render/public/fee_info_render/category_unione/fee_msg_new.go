package category_unione

//
//import (
//	"context"
//
//	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
//	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/level_type"
//	"git.xiaojukeji.com/gulfstream/passenger-common/model/price"
//	"github.com/spf13/cast"
//
//	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
//	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/taxi"
//	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
//	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
//	"git.xiaojukeji.com/gulfstream/mamba/common/util"
//	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
//	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
//	util2 "git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/util"
//)
//
//func GetTaxiFeeMsg(ctx context.Context, prov PriceDescProvider, digitExp bool) string {
//	var (
//		priceDesc string
//		feeAmount string
//		feeMsg    string
//	)
//
//	key, param := prov.ApolloParamsGen(apollo_model.WithPIDKey)
//	price.CheckSingle(ctx, "getTaxiFeeInfo", "TaxiFeeInfo", "estimateFee", prov.GetEstimateFee())
//	if carpool.IsTaxiCarpool(prov.GetProductCategory()) {
//		template := dcmp.GetJSONResultWithPath(ctx, "estimate_form_v3-estimate_price_msg", nil, "taxi_carpool").String()
//		fee := util2.PriceFormat(ctx, param, cast.ToString(key), prov.GetEstimateFee(), consts.FeeTypeDefault)
//		return dcmp.TranslateTemplate(template, map[string]string{
//			"num": fee,
//		})
//	}
//
//	priceDesc = dcmp.GetDcmpContent(ctx, "unione-price_desc_without_price", nil)
//
//	if digitExp {
//		//小数点位数实验
//		estimateFee := prov.GetEstimateFee()
//		feeAmount = util2.PriceFormat(ctx, param, cast.ToString(key), estimateFee, consts.FeeTypeDefault)
//	} else {
//		//不需要过小数点位数实验
//		feeAmount = util.Float64ToString(prov.GetEstimateFee())
//	}
//
//	// 计价盒子
//	if prov.GetSubGroupId() == consts.SubGroupIdTaxiPricingBox {
//		feeMsg = GetPricingBoxFeeMsgNew(ctx, prov, priceDesc)
//
//		if len(feeMsg) > 0 {
//			priceDesc = feeMsg
//			return priceDesc
//		}
//	}
//
//	if CheckTaxiShowEstimateFee(ctx, prov) {
//		unit, symbol := util.GetCurrencyUnitAndSymbol(ctx, prov.GetProductId(), prov.GetBillInfoCurrency())
//		tag := map[string]string{
//			"currency_symbol": symbol,
//			"total_fee":       feeAmount,
//			"currency_unit":   unit,
//		}
//		priceDesc = dcmp.GetDcmpContent(ctx, "common-price_desc", tag)
//
//		// 预估价前缀逻辑
//		if prefix := GetPrefixTextNew(ctx, prov); prefix != "" {
//			priceDesc = prefix + priceDesc
//		}
//	}
//	if isSpecialPrice(ctx, prov) {
//		var textKey string
//		tag := map[string]string{
//			"total_fee": feeAmount,
//		}
//		textKey, _ = getSpecialPriceTextKey(ctx, prov)
//		priceDesc = dcmp.GetJSONContentWithPath(ctx, "unione-special_price_taxi_price_desc", tag, textKey)
//	}
//
//	return priceDesc
//}
//
//func GetPricingBoxFeeMsgNew(ctx context.Context, prov PriceDescProvider, priceDesc string) string {
//	uidKey, params := prov.GetApolloParams(biz_runtime.WithUIDKey)
//	useNew := taxi.UsingNewBox(ctx, cast.ToString(uidKey), params)
//	price.CheckSingle(ctx, "getPricingBoxFeeMsg", "PricingBoxFeeMsg", "estimateFee", prov.GetEstimateFee(), price.WithCheckBelowZero())
//	if (prov.GetLevelType() != level_type.LevelTypeTaxiMarketisation && prov.GetLevelType() != level_type.LevelTypeTaxiBuTian) && !useNew {
//		// 不走实验 保持原逻辑 显示打表计价
//		return priceDesc
//	} else {
//		// 在线计价 或者 走实验的打表计价 显示预估价格
//		unit, symbol := util.GetCurrencyUnitAndSymbol(ctx, prov.GetProductId(), prov.GetBillInfoCurrency())
//		key, param := prov.ApolloParamsGen(apollo_model.WithPIDKey)
//		feeAmount := util2.PriceFormat(ctx, param, cast.ToString(key), prov.GetEstimateFee(), consts.FeeTypeDefault)
//
//		tag := map[string]string{
//			"currency_symbol": symbol,
//			"total_fee":       feeAmount,
//			"currency_unit":   unit,
//		}
//		priceDesc = dcmp.GetDcmpContent(ctx, "common-price_desc", tag)
//		if prefix := GetPrefixTextNew(ctx, prov); prefix != "" && useNew {
//			priceDesc = prefix + priceDesc
//		}
//		return priceDesc
//	}
//}
//
//// GetPrefixTextNew 获取前缀文案
//func GetPrefixTextNew(ctx context.Context, prov PriceDescProvider) string {
//	// 一车两价出租车，在预估价前加上"在线计价·" 文案展示
//	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdTaxiMarketisationPutong ||
//		prov.GetProductCategory() == estimate_pc_id.EstimatePcIdTaxiMarketisationYouxuan ||
//		prov.GetProductCategory() == estimate_pc_id.EstimatePcIdFastTaxi {
//		return dcmp.GetDcmpContent(ctx, "config-taxi_price_online_text", nil)
//	}
//
//	// 打表出租车，在预估价前加上"打表·" 文案展示
//	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdUnione ||
//		prov.GetProductCategory() == estimate_pc_id.EstimatePcIdYouxuanTaxi ||
//		prov.GetProductCategory() == estimate_pc_id.EstimatePcIdCountyTaxi {
//		return dcmp.GetDcmpContent(ctx, "config-taxi_price_unione_text", nil)
//	}
//	return ""
//}
