package category_unione

import (
	"context"
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	mambaconsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input"
	feedescutil "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/util"
)

type PriceInfoDescListProvider interface {
	render.ProductProvider
	render.BaseProvider
	render.BillInfoProvider
	render.ActivityInfoProvider
	render.PaymentInfoProvider
	render.MemberInfoProvider
	render.TaxiInfoProvider
	render.ApolloProvider
	render.CarpoolInfoProvider
	render.DynamicIconABProvider
	// render.ApolloProvider
	ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string)

	IsCarpoolV3Merge(context.Context) bool
	GetBonus() float64
	GetFastEstimatePrice() float64
	GetDiscountCard() *PriceApi.DiscountCard
	GetExactEstimateFee() float64
	GetCarpoolFailExactEstimateFee() float64

	GetMixedDeductPrice() float64
	GetSceneEstimatePrice() []biz_runtime.SceneEstimateFeeViewer
	GetFastCarPrice() float64
	GetOpenCitySourceId() int32
	GetBaseReqData() *models.BaseReqData
}

// GetPriceInfoDescListHarbour 获取香港descList；v3/v4接口使用
func GetPriceInfoDescListHarbour(ctx context.Context, prov PriceInfoDescListProvider) []*proto.NewFormFeeDesc {
	cnt := 2 // 最大展示标签
	if prov.GetLang() == mambaconsts.LangEnUS {
		cnt = 1
	}
	env := fee_desc_engine.NewEnv(consts.HarbourForm).SetApolloParams(prov).SetDcmpKey(consts.DcmpKeyHarbourFeeDesc).SetCap(cnt)
	resp := fee_desc_engine.NewFeeEngine(input.BuildHkProductFeeInput(ctx, prov), env).SetProductCategory(prov.GetProductCategory()).Do(ctx)
	if resp == nil {
		return nil
	}

	return feedescutil.ConvertFeeOutputToDesc(ctx, resp)
}

// GetPriceInfoDescListHarbourV4 8.5表单预估使用
func GetPriceInfoDescListHarbourV4(ctx context.Context, prov PriceInfoDescListProvider) []*proto.NewFormFeeDesc {
	cnt := 2 // 最大展示标签
	if prov.GetLang() == mambaconsts.LangEnUS {
		cnt = 1
	}
	env := fee_desc_engine.NewEnv(consts.HarbourForm).SetApolloParams(prov).SetDcmpKey(consts.DcmpKeyHarbourFeeDesc).SetCap(cnt)
	resp := fee_desc_engine.NewFeeEngine(input.BuildHkProductFeeInput(ctx, prov), env).SetProductCategory(prov.GetProductCategory()).Do(ctx)
	if resp == nil {
		return nil
	}

	return feedescutil.ConvertFeeOutputToDesc(ctx, resp)
}
