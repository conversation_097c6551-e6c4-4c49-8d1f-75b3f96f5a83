package category_unione

import (
	"context"
	"testing"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"github.com/agiledragon/gomonkey/v2"
	. "github.com/smartystreets/goconvey/convey"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

func TestCheckForCityAB(t *testing.T) {
	Convey("TestCheckForCityAB", t, func() {
		p1 := gomonkey.ApplyFunc(apollo.FeatureExp, func(context.Context, string, string, map[string]string) (bool, *model.Assignment) {
			return false, nil
		})
		defer p1.Reset()
		checkForCityAB(context.TODO(), "1", nil)
	})
	Convey("TestCheckForCityAB", t, func() {
		p2 := gomonkey.ApplyFunc(apollo.FeatureExp, func(context.Context, string, string, map[string]string) (bool, *model.Assignment) {
			return true, nil
		})
		defer p2.Reset()
		checkForCityAB(context.TODO(), "1", nil)
	})
	<PERSON>vey("TestCheckForCityAB", t, func() {
		assign := model.NewAssignment("estimate_price_normal_taxi", "", "", map[string]interface{}{
			"show_estimate_price": "1"})
		p3 := gomonkey.ApplyFunc(apollo.FeatureExp, func(context.Context, string, string, map[string]string) (bool, *model.Assignment) {
			return false, assign
		})
		defer p3.Reset()
		checkForCityAB(context.TODO(), "1", nil)
	})
}

//func TestIsTaxiShowEstimateFee(t *testing.T) {
//	Convey("TestIsTaxiShowEstimateFee", t, func() {
//		p1 := gomonkey.ApplyFunc(checkForCityAB, func(context.Context, string, map[string]string) bool {
//			return true
//		})
//		defer p1.Reset()
//		IsTaxiShowEstimateFee(context.Background(), 7, "1", "1", nil, 1)
//	})
//}
