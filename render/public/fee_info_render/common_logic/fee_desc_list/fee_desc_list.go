package fee_desc_list

import (
	"context"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	BizConsts "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/product"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/category_bargain"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/category_carpool"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/category_unione"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/spacious_car"
)

// GetEstimateFeeDescList 拉齐预估v3的fee_desc_list
func GetEstimateFeeDescList(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider, pageType int32) []*proto.NewFormFeeDesc {
	if carpool.IsCarpool(prov.GetCarpoolType()) {
		// 拼车
		return category_carpool.GetCarpoolFeeDescList(ctx, prov, pageType)
	}

	if prov.GetProductId() == product_id.ProductIdUniOne {
		// 出租车业务线
		return category_unione.GetTaxiPriceInfoDescList(ctx, prov)
	}

	if product.IsHongKongProduct(BizConsts.ProductID(prov.GetProductId())) {
		return category_unione.GetPriceInfoDescListHarbourV4(ctx, prov)
	}

	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdSpaciousCar {
		// 车大联盟
		return spacious_car.GetSpaciousCarFeeDescList(ctx, prov)
	}

	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdBargain {
		// 司乘议价
		return category_bargain.GetBargainFeeDescList(ctx, prov)
	}

	return fee_info_render.GetPriceInfoDescListV4(ctx, prov)
}

// GetEstimateFeeDescListV3 预估v3的fee_desc_list
func GetEstimateFeeDescListV3(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider, pageType int32) []*proto.NewFormFeeDesc {
	// 拼车
	if carpool.IsCarpool(prov.GetCarpoolType()) && prov.GetProductCategory() != estimate_pc_id.EstimatePcIdTaxiCarpool {
		return category_carpool.GetCarpoolFeeDescListV3(ctx, prov, pageType)
	}
	feeInput := buildFeeInput(ctx, prov)
	if feeInput == nil {
		return nil
	}
	return fee_info_render.GetPriceInfoDescListV3(ctx, prov, feeInput)
}

func buildFeeInput(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider) *model.FeeInput {
	// 出租车
	if prov.GetProductId() == product_id.ProductIdUniOne && prov.GetProductCategory() != estimate_pc_id.EstimatePcIdTaxiCarpool {
		return input.BuildTaxiFeeInput4MainForm(ctx, prov, consts.DefaultForm)
	}

	// 香港地区品类（自营的士+合作的士+网约车）
	if product.IsHongKongProduct(BizConsts.ProductID(prov.GetProductId())) {
		return input.BuildHkProductFeeInput(ctx, prov)
	}

	// 车大
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdSpaciousCar {
		return input.BuildSpaciousCarFeeInput4MainForm(ctx, prov, consts.DefaultForm)
	}

	// 司乘议价
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdBargain {
		return input.BuildBargainFeeInput(ctx, prov)
	}

	// 顺风车
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolSFCar ||
		prov.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolCrossSFCar {
		return input.BuildSFCFeeInput(ctx, prov)
	}

	// 限时特价车，惊喜特价
	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdEstimatePcIdTimeLimitSpecialRate {
		return input.BuildSpecialRate(ctx, prov, consts.DefaultForm)
	}

	return input.BuildNormalFeeInput4MainForm(ctx, prov, consts.DefaultForm)
}
