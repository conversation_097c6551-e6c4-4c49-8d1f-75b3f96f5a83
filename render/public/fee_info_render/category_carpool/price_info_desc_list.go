package category_carpool

import (
	"context"
	"encoding/json"
	"math"
	"strings"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/carpool_type"
	"git.xiaojukeji.com/gulfstream/passenger-common/model/apolloconfig"
	"git.xiaojukeji.com/gulfstream/passenger-common/model/price"
	"github.com/shopspring/decimal"

	"github.com/spf13/cast"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/nuwa/trace/v2"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_carpool_render"

	"github.com/tidwall/gjson"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	FeeConsts "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/common_logic"
)

type Item struct {
	Amount   float64 `json:"amount"`
	Template string  `json:"template"`
	Type     int32   `json:"type"`
}

func GetPriceInfoDescList(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider, pageType int32) []*proto.NewFormFeeDesc {
	var (
		priceInfoDescList []*proto.NewFormFeeDesc
	)

	incrementList := []common_logic.FeeDescHandlerFunc{
		common_logic.GetRedPacketFee, // 节假日服务费
	}
	if priceDescList := common_logic.GetIncrementItems(ctx, prov, 1, incrementList, pageType); len(priceDescList) > 0 {
		priceInfoDescList = append(priceInfoDescList, priceDescList...)
	}
	// 拼车有节假日服务费的时候不展示其他优惠
	if len(priceInfoDescList) > 0 {
		return priceInfoDescList
	}

	// 企业付
	if prov.IsBusinessPay() {
		conf := getCarpool3DcmpConfig(ctx)
		if conf != nil {

			fee, b := prov.GetCarpoolFailEstimateFee()
			if len(conf.FeeDesc) > 0 && conf.FeeDesc["multi_business_pay"] != nil && b {
				priceInfo := &proto.NewFormFeeDesc{
					BorderColor: conf.FeeDesc["multi_business_pay"].BorderColor,
					Icon:        conf.FeeDesc["multi_business_pay"].Icon,
					Content: util.ReplaceTag(ctx, conf.FeeDesc["multi_business_pay"].Content, map[string]string{
						"num": util.FormatPrice(fee, -1),
					}) + util.ReplaceTag(ctx, conf.FeeDesc["multi_business_pay"].Suffix, map[string]string{
						"num": util.FormatPrice(prov.GetEstimateFee(), -1),
					}),
					Amount: prov.GetEstimateFee(),
				}

				if sortContent := common_logic.GetDualPricetContentWithSort(ctx, prov.GetEstimateFee(), fee, "estimate_form_v3-dual_price_fee_desc", "business_pay_deduction", common_logic.DualPriceOrderedOnAsc(prov, pageType)); sortContent != "" {
					priceInfo.Content = sortContent
				}

				priceInfoDescList = append(priceInfoDescList, priceInfo)
				return priceInfoDescList
			}
		}
	}

	decrementList := []common_logic.FeeDescHandlerFunc{
		common_logic.GetVcardDesc,            // vcard服务返回的省钱卡
		common_logic.GetMemberDoubleDiscount, //折上折
		common_logic.GetDiscountInfo,         //优惠【惠-、券-】
		common_logic.GetFastPrice,            //比快车省
	}
	if priceDescList := common_logic.BuildDecrementItems(ctx, prov, decrementList, pageType); len(priceDescList) > 0 {
		priceInfoDescList = append(priceInfoDescList, priceDescList...)
	}

	return priceInfoDescList
}

func GetPriceInfoDescListPincheche(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider, pageType int32) []*proto.NewFormFeeDesc {
	priceInfoDescList := []*proto.NewFormFeeDesc{}
	b := fee_desc_carpool_render.NewCarpoolFeeDescBase(ctx, prov, "anycar_v3-carpool_fee_detail_desc", "carpool_config-sort_price_desc", "carpool_config-price_select")
	res := b.RegisterFee(ctx, fee_desc_carpool_render.FeeRedPacket).RegisterFee(ctx, fee_desc_carpool_render.FeeBusinessPayDeduction).RegisterFee(ctx, fee_desc_carpool_render.FeeMemberDiscountCard).RegisterFee(ctx, fee_desc_carpool_render.FeeNormalDiscount).RegisterFee(ctx, fee_desc_carpool_render.FeeCoupon).BuildResp(ctx)
	if res != nil {
		r := &proto.NewFormFeeDesc{
			Amount:      res.Amount,
			Content:     res.Content,
			Icon:        res.Icon,
			BorderColor: res.BorderColor,
		}
		if res.TextColor != "" {
			r.TextColor = &res.TextColor
		}
		return append(priceInfoDescList, r)
	}
	return nil
}

// 拉齐主预估v3
func GetDualCarpoolFeeDescList(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider, pageType int32) []*proto.NewFormFeeDesc {
	var (
		priceInfoDescList []*proto.NewFormFeeDesc
	)

	incrementList := []common_logic.FeeDescHandlerFunc{
		common_logic.GetRedPacketFee, // 节假日服务费
	}
	if priceDescList := common_logic.GetIncrementItems(ctx, prov, 1, incrementList, pageType); len(priceDescList) > 0 {
		priceInfoDescList = append(priceInfoDescList, priceDescList...)
	}
	// 拼车有节假日服务费的时候不展示其他优惠
	if len(priceInfoDescList) > 0 {
		return priceInfoDescList
	}

	// 企业付
	if prov.IsBusinessPay() {
		ascOrder := common_logic.DualPriceOrderedOnAsc(prov, pageType)
		priceInfo := common_logic.GetDualPriceBusinessPayFeeDesc(ctx, prov, ascOrder, consts.EstimateV3FeeDesc)
		if priceInfo != nil {
			priceInfoDescList = append(priceInfoDescList, priceInfo)
			return priceInfoDescList
		}
	}
	// 8.5表单
	decrementList := []common_logic.FeeDescHandlerFunc{
		common_logic.GetVcardDesc, // vcard服务返回的省钱卡
		GetCarpoolCouponDesc,
		common_logic.GetMemberDoubleDiscount,
		common_logic.GetFastPrice,
	}
	if priceDescList := common_logic.BuildDecrementItems(ctx, prov, decrementList, pageType); len(priceDescList) > 0 {
		priceInfoDescList = append(priceInfoDescList, priceDescList...)
	}

	return priceInfoDescList
}

// 主预估v3两口价费项标签，本次下线了 省钱卡、专享券、通用活动、低碳一小时
func GetDualCarpoolFeeDescListV3(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider, dcmpKey string, isBigFontSize bool) []*proto.NewFormFeeDesc {
	var priceInfoDescList []*proto.NewFormFeeDesc
	// 企业付
	if prov.IsBusinessPay() {
		ascOrder := common_logic.DualPriceOrderedOnAscV3(prov)
		if isBigFontSize {
			ascOrder = isBigFontSize
		}
		priceInfo := common_logic.GetDualPriceBusinessPayFeeDesc(ctx, prov, ascOrder, dcmpKey)
		if priceInfo != nil {
			return append(priceInfoDescList, priceInfo)
		}
	}
	// 会员券
	desc := getCarpoolMemberCouponDesc(ctx, prov, dcmpKey, isBigFontSize)
	if desc != nil {
		return append(priceInfoDescList, desc)
	}
	// 折上折
	desc = getMemberDoubleDiscount(ctx, prov, dcmpKey, isBigFontSize)
	if desc != nil {
		return append(priceInfoDescList, desc)
	}
	// 7.0表单兜底优惠
	desc = getDualDiscount4Classify(ctx, prov, dcmpKey)
	if desc != nil {
		return append(priceInfoDescList, desc)
	}
	return priceInfoDescList
}

func GetMiniBusFeeDescListV3(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider) []*proto.NewFormFeeDesc {
	return fee_info_render.GetPriceInfoDescListV3(ctx, prov, input.BuildNormalFeeInput4MainForm(ctx, prov, FeeConsts.DefaultForm))
}

func GetMiniBusFeeDescList(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider) []*proto.NewFormFeeDesc {
	return fee_info_render.GetPriceInfoDescListV4(ctx, prov)
}

func GetInterCityFeeDescList(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider) []*proto.NewFormFeeDesc {
	var (
		priceInfoDescList []*proto.NewFormFeeDesc
	)

	if prov.IsBusinessPay() {
		if conf := dcmp.GetJSONMap(ctx, "estimate_form_v3-fee_desc", "business_pay_deduction"); len(conf) > 0 {
			deductFee := prov.GetMixedDeductPrice()
			price.CheckSingle(ctx, "GetInterCityFeeDescList", "InterCityFeeDescList", "MixedDeductPrice", deductFee)
			priceInfoDescList = append(priceInfoDescList, &proto.NewFormFeeDesc{
				BorderColor: conf["border_color"].String(),
				Icon:        conf["icon"].String(),
				Amount:      deductFee,
				Content:     dcmp.TranslateTemplate(conf["content"].String(), map[string]string{"num": util.FormatPriceWithoutZero(deductFee, 2)}),
			})
			return priceInfoDescList
		}
	}

	if discountFeeDesc := GetInterCityDiscountFeeDesc(ctx, prov); discountFeeDesc != nil && len(discountFeeDesc) > 0 {
		// 预约更优惠mock渲染感知
		discountFeeDesc = intercityPreferential(ctx, discountFeeDesc, prov)
		return discountFeeDesc
	}

	routeModel := apolloconfig.NewInterCarpoolRouteConfModel().GetConfigByComboId(int32(prov.GetComboID()))
	if routeModel != nil {
		fastCarPrice := routeModel.FastCarPrice
		diffPrice := util.ToFloat64(fastCarPrice) - prov.GetEstimateFee()
		if diffPrice > 0 {
			if conf := dcmp.GetJSONMap(ctx, "estimate_form_v3-fee_desc", "normal_discount"); len(conf) > 0 {
				priceInfoDescList = append(priceInfoDescList, &proto.NewFormFeeDesc{
					BorderColor: conf["border_color"].String(),
					Icon:        conf["icon"].String(),
					Amount:      diffPrice,
					Content:     dcmp.TranslateTemplate(conf["content"].String(), map[string]string{"num": util.FormatPriceWithoutZero(diffPrice, 2)}),
				})
				return priceInfoDescList
			}
		}
	}

	return nil
}

func GetInterCityFeeDescListV3(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider) []*proto.NewFormFeeDesc {
	var (
		priceInfoDescList []*proto.NewFormFeeDesc
	)

	if prov.IsBusinessPay() {
		deductFee := prov.GetMixedDeductPrice()
		priceInfoDescList = append(priceInfoDescList, buildBusinessPay(ctx, deductFee))
		return priceInfoDescList
	}

	if discountFeeDesc := GetInterCityDiscountFeeDesc(ctx, prov); discountFeeDesc != nil && len(discountFeeDesc) > 0 {
		return discountFeeDesc
	}

	routeModel := apolloconfig.NewInterCarpoolRouteConfModel().GetConfigByComboId(int32(prov.GetComboID()))
	if routeModel != nil {
		fastCarPrice := routeModel.FastCarPrice
		diffPrice := util.ToFloat64(fastCarPrice) - prov.GetEstimateFee()
		if diffPrice > 0 {
			amount := util.FormatPriceWithoutZero(diffPrice, 2)
			if conf := dcmp.GetJSONMap(ctx, FeeConsts.EstimateV3FeeDesc, "normal_discount"); len(conf) > 0 {
				priceInfoDescList = append(priceInfoDescList, &proto.NewFormFeeDesc{
					BorderColor:    "",
					Icon:           conf["icon"].String(),
					TextColor:      util.String2PtrString(conf["text_color"].String()),
					HighlightColor: util.String2PtrString(conf["highlight_color"].String()),
					Amount:         util.ToFloat64(amount),
					Content:        dcmp.TranslateTemplate(conf["content"].String(), map[string]string{"num": util.FormatPriceWithoutZero(diffPrice, 2)}),
				})
				return priceInfoDescList
			}
		}
	}

	return nil
}

func GetInterCityDiscountFeeDesc(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider) []*proto.NewFormFeeDesc {
	ret := make([]*proto.NewFormFeeDesc, 0)
	env := fee_desc_engine.NewEnv(FeeConsts.DefaultForm).SetApolloParams(prov).SetDcmpKey(FeeConsts.EstimateV3FeeDesc)
	resp := fee_desc_engine.NewFeeEngine(input.BuildInterCityCommonEstimateInput(ctx, prov, FeeConsts.DefaultForm), env).SetProductCategory(prov.GetProductCategory()).Do(ctx)
	if resp == nil {
		return nil
	}

	for _, output := range resp {
		if output == nil {
			continue
		}

		desc := &proto.NewFormFeeDesc{
			BorderColor:    "",
			Icon:           output.Icon,
			Content:        output.Content,
			Type:           output.Type,
			TextColor:      util.StringPtr(output.TextColor),
			HighlightColor: util.StringPtr(output.HighLightColor),
		}

		if output.Fee != nil {
			desc.Amount = output.Fee.Amount
		}

		ret = append(ret, desc)
	}

	return ret
}

// 获取被预约更优惠二次处理过的感知
func intercityPreferential(ctx context.Context, disCountInfo []*proto.NewFormFeeDesc, prov fee_info_render.PriceInfoDescListProvider) []*proto.NewFormFeeDesc {
	if disCountInfo == nil || len(disCountInfo) == 0 {
		return disCountInfo
	}

	if couponInfo := prov.GetCouponInfo(); couponInfo != nil && couponInfo.CouponSource == "pope" {
		return disCountInfo
	}

	var (
		priceInfoDescList []*proto.NewFormFeeDesc
	)

	apolloKey, apolloParam := prov.GetApolloParams(biz_runtime.WithPIDKey, biz_runtime.WithProductID, biz_runtime.WithComboID, biz_runtime.WithRouteGroup)
	status, _ := fee_info_render.PreferentialStatus(ctx, apolloKey, apolloParam)
	dcmpConf := dcmp.GetDcmpPlainContent(ctx, "estimate_form_v3-intercity_preferential_fee_desc")
	if status && len(dcmpConf) > 0 {
		tag := map[string]string{
			"num": util.ToString(disCountInfo[0].Amount),
		}
		textColor := gjson.Get(dcmpConf, "text_color").String()
		priceInfoDescList = append(priceInfoDescList, &proto.NewFormFeeDesc{
			BorderColor: gjson.Get(dcmpConf, "border_color").String(),
			Icon:        gjson.Get(dcmpConf, "icon").String(),
			TextColor:   &textColor,
			Amount:      disCountInfo[0].Amount,
			Content:     dcmp.TranslateTemplate(gjson.Get(dcmpConf, "content").String(), tag),
		})
		return priceInfoDescList
	}

	return disCountInfo
}

func GetPinchecheV1FeeDescListV3(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider) []*proto.NewFormFeeDesc {
	var priceInfoDescList []*proto.NewFormFeeDesc

	if prov.IsBusinessPay() {
		deductFee := prov.GetMixedDeductPrice()
		buss := buildBusinessPay(ctx, deductFee)
		if buss != nil {
			return append(priceInfoDescList, buss)
		}
	}
	baseFee := prov.GetDynamicTotalFee() - prov.GetEstimateFee()
	if util.RoundAbs(baseFee, 1) > 0.0 {
		amount := util.FormatPriceWithoutZero(baseFee, 2)
		if normalConf := dcmp.GetJSONMap(ctx, FeeConsts.EstimateV3FeeDesc, "normal_discount"); len(normalConf) > 0 {
			priceInfoDescList = append(priceInfoDescList, &proto.NewFormFeeDesc{
				BorderColor:    normalConf["border_color"].String(),
				Icon:           normalConf["icon"].String(),
				TextColor:      util.String2PtrString(normalConf["text_color"].String()),
				HighlightColor: util.String2PtrString(normalConf["highlight_color"].String()),
				Amount:         util.ToFloat64(amount),
				Content:        dcmp.TranslateTemplate(normalConf["content"].String(), map[string]string{"num": amount}),
			})
		}
	}
	return priceInfoDescList
}

func GetPinchecheV2FeeDescList(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider) []*proto.NewFormFeeDesc {
	var (
		priceInfoDescList []*proto.NewFormFeeDesc
		priceInfoDesc     *proto.NewFormFeeDesc
	)
	dcmpKey := FeeConsts.EstimateV3FeeDesc
	poolNumPrice := getPinchecheV2Price(ctx, prov)

	if prov.IsBusinessPay() && poolNumPrice != nil && len(poolNumPrice) == 2 && poolNumPrice[0] != nil && poolNumPrice[0].GetFee() > 0.0 {
		priceInfoDescList = append(priceInfoDescList, buildBusinessPay(ctx, poolNumPrice[0].GetFee()))
		return priceInfoDescList
	}

	// 拼成乐的会员券只有拼成券或者未拼成券，不存在拼成1人/拼成2人券, 拼成乐会员pre-sale没有拼成/拼不成优惠, 原key carpool_paid_member会合并两个优惠展示最高优惠，这里没有两个优惠，直接使用normal_discount
	couponData := GetPinchecheV2MemberCoupon(ctx, prov)
	if couponData != nil {
		if conf := dcmp.GetJSONMap(ctx, dcmpKey, "normal_discount"); len(conf) > 0 {
			// couponData.Amount是分
			amount := decimal.NewFromInt(cast.ToInt64(couponData.Amount)).DivRound(decimal.New(1, 2), 4).InexactFloat64()
			price.CheckSingle(ctx, "GetPinchecheV2FeeDescList", "isPaidCoupon", "couponData_amount", amount)
			priceInfoDescList = append(priceInfoDescList, &proto.NewFormFeeDesc{
				BorderColor:    conf["border_color"].String(),
				Icon:           conf["icon"].String(),
				Amount:         amount,
				TextColor:      util.String2PtrString(conf["text_color"].String()),
				HighlightColor: util.String2PtrString(conf["high_light_color"].String()),
				Content:        dcmp.TranslateTemplate(conf["content"].String(), map[string]string{"num": util.ToString(amount)}),
			})
			return priceInfoDescList
		}
	}

	if poolNumPrice != nil && len(poolNumPrice) == 2 && poolNumPrice[1] != nil {
		maxPoolNumPrice := poolNumPrice[1]
		diffPrice := getCarpoolLowPriceV2DiscountAll(maxPoolNumPrice)
		if util.RoundAbs(diffPrice, 1) > 0.0 {
			amount := util.FormatPriceWithoutZero(diffPrice, 2)
			if normalConf := dcmp.GetJSONMap(ctx, dcmpKey, "normal_discount"); len(normalConf) > 0 {
				priceInfoDesc = &proto.NewFormFeeDesc{
					BorderColor:    normalConf["border_color"].String(),
					Icon:           normalConf["icon"].String(),
					TextColor:      util.String2PtrString(normalConf["text_color"].String()),
					HighlightColor: util.String2PtrString(normalConf["high_light_color"].String()),
					Amount:         util.ToFloat64(amount),
					Content:        dcmp.TranslateTemplate(normalConf["content"].String(), map[string]string{"num": amount}),
				}
			}

			// 折上折
			if maxPoolNumPrice.GetFeeDetail().GetDiscountCard() != nil && maxPoolNumPrice.GetFeeDetail().GetDiscountCard().Amount > 0 {
				memberLevel := prov.GetLevelID()
				memberConf := dcmp.GetJSONMap(ctx, dcmpKey, "member_v3_coupon_level_"+util.ToString(memberLevel))
				if len(memberConf) > 0 && len(memberConf) > 0 {
					priceInfoDesc = &proto.NewFormFeeDesc{
						BorderColor:    memberConf["border_color"].String(),
						Icon:           memberConf["icon"].String(),
						TextColor:      util.String2PtrString(memberConf["text_color"].String()),
						HighlightColor: util.String2PtrString(memberConf["high_light_color"].String()),
						Amount:         util.ToFloat64(amount),
						Content:        dcmp.TranslateTemplate(memberConf["content"].String(), map[string]string{"num": amount}),
					}
				}
			}

			priceInfoDescList = append(priceInfoDescList, priceInfoDesc)
			return priceInfoDescList
		}
	}
	return nil
}

func GetPinchecheV2FeeDescListV3(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider) []*proto.NewFormFeeDesc {
	var priceInfoDescList []*proto.NewFormFeeDesc
	poolNumPrice := getPinchecheV2Price(ctx, prov)

	if prov.IsBusinessPay() && poolNumPrice != nil && len(poolNumPrice) == 2 && poolNumPrice[0] != nil && poolNumPrice[0].GetFee() > 0.0 {
		buss := buildBusinessPay(ctx, poolNumPrice[0].GetFee())
		if buss != nil {
			return append(priceInfoDescList, buss)
		}
	}

	// 拼成乐的会员券只有拼成券或者未拼成券，不存在拼成1人/拼成2人券, 拼成乐会员pre-sale没有拼成/拼不成优惠, 原key carpool_paid_member会合并两个优惠展示最高优惠，这里没有两个优惠，直接使用normal_discount
	priceInfoDesc := buildPinchecheV2MemberCouponDesc(ctx, prov)
	if priceInfoDesc != nil {
		return append(priceInfoDescList, priceInfoDesc)
	}

	if poolNumPrice != nil && len(poolNumPrice) == 2 && poolNumPrice[1] != nil {
		maxPoolNumPrice := poolNumPrice[1]
		diffPrice := getCarpoolLowPriceV2DiscountAll(maxPoolNumPrice)
		if util.RoundAbs(diffPrice, 1) > 0.0 {
			amount := util.FormatPriceWithoutZero(diffPrice, 2)
			// 折上折
			priceInfoDesc = buildPinchecheV2MemberDiscountDesc(ctx, prov, maxPoolNumPrice, amount)
			if priceInfoDesc != nil {
				return append(priceInfoDescList, priceInfoDesc)
			}
			// 兜底
			priceInfoDesc = buildPinchecheV2NormalDesc(ctx, amount)
			if priceInfoDesc != nil {
				return append(priceInfoDescList, priceInfoDesc)
			}
		}
	}
	return nil
}

func GetCarpoolFlatRateFeeDescListV3(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider) []*proto.NewFormFeeDesc {
	var priceInfoDescList []*proto.NewFormFeeDesc

	if prov.IsBusinessPay() {
		deductFee := prov.GetMixedDeductPrice()
		buss := buildBusinessPay(ctx, deductFee)
		if buss != nil {
			return append(priceInfoDescList, buss)
		}
	}
	return priceInfoDescList
}

func buildBusinessPay(ctx context.Context, fee float64) *proto.NewFormFeeDesc {
	if conf := dcmp.GetJSONMap(ctx, FeeConsts.EstimateV3FeeDesc, "business_pay_deduction"); len(conf) > 0 {
		price.CheckSingle(ctx, "GetPinchecheV2FeeDescList", "minPoolNumPrice", "minPoolNumPrice", fee)
		return &proto.NewFormFeeDesc{
			BorderColor:    conf["border_color"].String(),
			Icon:           conf["icon"].String(),
			Amount:         fee,
			Type:           FeeConsts.TypeBusinessPay,
			TextColor:      util.String2PtrString(conf["text_color"].String()),
			HighlightColor: util.String2PtrString(conf["highlight_color"].String()),
			Content:        dcmp.TranslateTemplate(conf["content"].String(), map[string]string{"num": util.FormatPriceWithoutZero(fee, 2)}),
		}
	}
	return nil
}

func getPinchecheV2Price(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider) []biz_runtime.SceneEstimateFeeViewer {
	if prov == nil {
		return nil
	}

	var (
		maxPoolNumPrice, minPoolNumPrice biz_runtime.SceneEstimateFeeViewer
		maxPoolNum, minPoolNum           int64
		poolNumPrice                     = make([]biz_runtime.SceneEstimateFeeViewer, 0)
	)
	maxPoolNum = 0
	minPoolNum = 10000

	priceList := prov.GetSceneEstimatePrice()
	if priceList == nil || len(priceList) == 0 {
		return poolNumPrice
	}
	for _, price := range priceList {
		poolNum := price.GetFeeAttributes().GetInt("pool_num")

		if poolNum != nil && *poolNum > maxPoolNum {
			maxPoolNumPrice = price
			maxPoolNum = *poolNum
		}

		if poolNum != nil && *poolNum < minPoolNum {
			minPoolNumPrice = price
			minPoolNum = *poolNum
		}
	}

	poolNumPrice = append(poolNumPrice, minPoolNumPrice, maxPoolNumPrice)
	return poolNumPrice
}

func buildPinchecheV2MemberCouponDesc(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider) *proto.NewFormFeeDesc {
	if prov == nil {
		return nil
	}
	coupon := prov.GetCouponInfo()
	if coupon != nil && coupon.Amount != "" && strings.Contains(coupon.CustomTag, "member_v3") {
		if conf := dcmp.GetJSONMap(ctx, FeeConsts.EstimateV3FeeDesc, "normal_discount"); len(conf) > 0 {
			// couponData.Amount是分
			amount := decimal.NewFromInt(cast.ToInt64(coupon.Amount)).DivRound(decimal.New(1, 2), 4).InexactFloat64()
			price.CheckSingle(ctx, "GetPinchecheV2FeeDescList", "isPaidCoupon", "couponData_amount", amount)
			return &proto.NewFormFeeDesc{
				BorderColor:    conf["border_color"].String(),
				Icon:           conf["icon"].String(),
				Amount:         amount,
				TextColor:      util.String2PtrString(conf["text_color"].String()),
				HighlightColor: util.String2PtrString(conf["highlight_color"].String()),
				Content:        dcmp.TranslateTemplate(conf["content"].String(), map[string]string{"num": util.ToString(amount)}),
			}
		}
	}
	return nil
}

func buildPinchecheV2MemberDiscountDesc(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider, maxPoolNumPrice biz_runtime.SceneEstimateFeeViewer, amount string) *proto.NewFormFeeDesc {
	if maxPoolNumPrice.GetFeeDetail().GetDiscountCard() != nil && maxPoolNumPrice.GetFeeDetail().GetDiscountCard().Amount > 0 {
		memberLevel := prov.GetLevelID()
		memberConf := dcmp.GetJSONMap(ctx, FeeConsts.EstimateV3FeeDesc, "member_v3_coupon_level_"+util.ToString(memberLevel))
		if len(memberConf) > 0 && len(memberConf) > 0 {
			return &proto.NewFormFeeDesc{
				BorderColor:    memberConf["border_color"].String(),
				Icon:           memberConf["icon"].String(),
				TextColor:      util.String2PtrString(memberConf["text_color"].String()),
				HighlightColor: util.String2PtrString(memberConf["highlight_color"].String()),
				Amount:         util.ToFloat64(amount),
				Content:        dcmp.TranslateTemplate(memberConf["content"].String(), map[string]string{"num": amount}),
			}
		}
	}
	return nil
}

func buildPinchecheV2NormalDesc(ctx context.Context, amount string) *proto.NewFormFeeDesc {
	if normalConf := dcmp.GetJSONMap(ctx, FeeConsts.EstimateV3FeeDesc, "normal_discount"); len(normalConf) > 0 {
		return &proto.NewFormFeeDesc{
			BorderColor:    normalConf["border_color"].String(),
			Icon:           normalConf["icon"].String(),
			TextColor:      util.String2PtrString(normalConf["text_color"].String()),
			HighlightColor: util.String2PtrString(normalConf["highlight_color"].String()),
			Amount:         util.ToFloat64(amount),
			Content:        dcmp.TranslateTemplate(normalConf["content"].String(), map[string]string{"num": amount}),
		}
	}
	return nil
}

func GetPinchecheV2MemberCoupon(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider) *PriceApi.EstimateNewFormCouponInfo {
	if prov == nil {
		return nil
	}
	coupon := prov.GetCouponInfo()
	if coupon != nil && coupon.Amount != "" && strings.Contains(coupon.CustomTag, "member_v3") {
		return coupon
	}

	return nil
}

func getCarpoolLowPriceV2DiscountAll(extendPrice biz_runtime.SceneEstimateFeeViewer) float64 {
	if extendPrice == nil {
		return 0
	}
	var fSpsDiscount float64
	feeDetailInfo := extendPrice.GetFeeDetail().GetFeeDetailInfo()
	if feeDetailInfo != nil {
		if spsDiscount, ok := feeDetailInfo["sps_carpool_discount_surprised_two"]; ok {
			fSpsDiscount = math.Abs(spsDiscount)
		}
	}

	// 实付优惠
	channelDiscount := extendPrice.GetFeeDetail().GetTotalFeeWithoutCouponSome() - extendPrice.GetFee()

	return fSpsDiscount + channelDiscount
}

// GetPriceInfoDescListV2 v2版本
func GetPriceInfoDescListV2(ctx context.Context, prov PriceInfoDescListProvider) []*proto.NewFormFeeDesc {
	var (
		priceInfoDescList []*proto.NewFormFeeDesc
	)

	redPacket := GetRedPacketFee(ctx, prov)
	if redPacket != nil {
		priceInfoDescList = append(priceInfoDescList, redPacket)
		return priceInfoDescList
	}

	businessPay := GetBusinessPay(ctx, prov)
	if businessPay != nil {
		priceInfoDescList = append(priceInfoDescList, businessPay)
		return priceInfoDescList
	}

	vCard := GetVcardDesc(ctx, prov)
	if vCard != nil {
		priceInfoDescList = append(priceInfoDescList, vCard)
		return priceInfoDescList
	}

	return priceInfoDescList
}

type MultiDcmpConf struct {
	SuccCarpoolDay string `json:"succ_carpool_day"`
	Succ           string `json:"succ"`
	CapPriceMerge  string `json:"cap_price_merge"`

	FailV2 string `json:"fail_v_2"`
	FailV3 string `json:"fail_v_3"`

	FeeDesc map[string]*FeeDescStruct `json:"fee_desc"`
}
type FeeDescStruct struct {
	Content     string `json:"content"`
	Suffix      string `json:"suffix"`
	Icon        string `json:"icon"`
	BorderColor string `json:"border_color"`
}

func getCarpool3DcmpConfig(ctx context.Context) *MultiDcmpConf {
	conf := new(MultiDcmpConf)
	dcmpStr := dcmp.GetDcmpContent(ctx, "carpool_config-dual_price", nil)
	if len(dcmpStr) == 0 {
		return nil
	}
	err := json.Unmarshal([]byte(dcmpStr), conf)
	if err != nil || conf == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "unmarshal dcmp string fail with %v", err)
		return nil
	}
	return conf
}

// 拉齐主预估v3
func GetCarpoolFeeDescList(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider, pageType int32) []*proto.NewFormFeeDesc {
	var (
		priceInfoDescList []*proto.NewFormFeeDesc
	)

	incrementList := []common_logic.FeeDescHandlerFunc{
		common_logic.GetRedPacketFee, // 节假日服务费
	}
	if priceDescList := common_logic.GetIncrementItems(ctx, prov, 1, incrementList, pageType); len(priceDescList) > 0 {
		priceInfoDescList = append(priceInfoDescList, priceDescList...)
	}
	// 拼车有节假日服务费的时候不展示其他优惠
	if len(priceInfoDescList) > 0 {
		return priceInfoDescList
	}

	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolStation {
		// 两口价
		return GetDualCarpoolFeeDescList(ctx, prov, pageType)
	} else if carpool.IsPinCheCheV2(prov.GetProductCategory(), prov.GetCarpoolPriceType()) {
		// 拼成乐v2
		return GetPinchecheV2FeeDescList(ctx, prov)
	} else if carpool.IsInterCityCarpool(prov.GetCarpoolType(), prov.GetComboType()) {
		// 城际拼车
		return GetInterCityFeeDescList(ctx, prov)
	} else if carpool.IsMiniBus(util.ToInt(prov.GetCarpoolType())) {
		// 小巴
		return GetMiniBusFeeDescList(ctx, prov)
	}

	return nil
}

// 主预估v3
func GetCarpoolFeeDescListV3(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider, pageType int32) []*proto.NewFormFeeDesc {
	var priceInfoDescList []*proto.NewFormFeeDesc

	incrementList := []common_logic.FeeDescHandlerFunc{
		common_logic.GetRedPacketFee, // 节假日服务费
	}
	if priceDescList := common_logic.GetIncrementItems(ctx, prov, 1, incrementList, pageType); len(priceDescList) > 0 {
		priceInfoDescList = append(priceInfoDescList, priceDescList...)
	}
	// 拼车有节假日服务费的时候不展示其他优惠
	if len(priceInfoDescList) > 0 {
		return priceInfoDescList
	}

	if carpool.IsPinCheChe(prov.GetProductCategory()) && !carpool.IsPinCheCheV2(prov.GetProductCategory(), prov.GetCarpoolPriceType()) {
		// 拼成乐v1
		return GetPinchecheV1FeeDescListV3(ctx, prov)
	} else if carpool.IsPinCheCheV2(prov.GetProductCategory(), prov.GetCarpoolPriceType()) {
		// 拼成乐v2
		return GetPinchecheV2FeeDescListV3(ctx, prov)
	} else if prov.GetCarpoolType() == carpool_type.CarpoolTypeFlatRate {
		// 区域渗透(司乘一口价)
		return GetCarpoolFlatRateFeeDescListV3(ctx, prov)
	} else if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolStation {
		// 两口价
		return GetDualCarpoolFeeDescListV3(ctx, prov, consts.EstimateV3FeeDesc, false)
	} else if carpool.IsInterCityCarpool(prov.GetCarpoolType(), prov.GetComboType()) {
		// 城际拼车
		return GetInterCityFeeDescListV3(ctx, prov)
	} else if carpool.IsMiniBus(util.ToInt(prov.GetCarpoolType())) {
		// 小巴
		return GetMiniBusFeeDescListV3(ctx, prov)
	}

	return nil
}
