package category_carpool

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strings"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"github.com/spf13/cast"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	cardRenderCore "git.xiaojukeji.com/gulfstream/carpool-communication-go/cardController"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/common_logic"
	"git.xiaojukeji.com/gulfstream/passenger-common/constant/communicate"
)

type PriceInfoDescListProvider interface {
	GetApolloParams(func(full *biz_runtime.ProductInfoFull) string, ...func(full *biz_runtime.ProductInfoFull) (string, string)) (key string, params map[string]string)
	GetFeeDetailInfo() map[string]float64
	GetVCard() *PriceApi.EstimateNewFormVCardInfo
	IsBusinessPay() bool
	IsCarpoolV3Merge(ctx context.Context) bool
	GetCarpoolFailEstimateFee() (float64, bool)
	GetEstimateFee() float64
}

// GetRedPacketFee 加价项:春节服务费
func GetRedPacketFee(ctx context.Context, prov PriceInfoDescListProvider) *proto.NewFormFeeDesc {

	uidKey, params := prov.GetApolloParams(biz_runtime.WithUIDKey)

	if !apollo.FeatureToggle(ctx, "gs_holiday_fee_fee_desc", uidKey, params) {
		return nil
	}

	return common_logic.GetHolidayFee(ctx, prov.GetFeeDetailInfo())
}

// GetBusinessPay 获取拼车企业付
func GetBusinessPay(ctx context.Context, prov PriceInfoDescListProvider) *proto.NewFormFeeDesc {
	if !prov.IsBusinessPay() {
		return nil
	}

	var (
		failFee float64
		succFee float64
	)

	succFee = prov.GetEstimateFee()

	if fee, ok := prov.GetCarpoolFailEstimateFee(); ok {
		failFee = fee
	}

	// 一口价
	if prov.IsCarpoolV3Merge(ctx) {
		template := dcmp.GetJSONMap(ctx, "anycar_v3-fee_detail_desc", "business_pay_deduction")
		if len(template) > 0 {
			return &proto.NewFormFeeDesc{
				BorderColor: template["border_color"].String(),
				Content: util.ReplaceTag(ctx, template["content"].String(), map[string]string{
					"num": fmt.Sprintf("%.2f", failFee),
				}),
				Icon:           template["icon"].String(),
				Amount:         0,
				Type:           2,
				TextColor:      util.StringPtr(template["text_color"].String()),
				BgColor:        util.StringPtr(template["bg_color"].String()),
				HighlightColor: util.StringPtr(template["high_light_color"].String()),
			}
		}
	} else {
		template := dcmp.GetJSONMap(ctx, "anycar_v3-fee_detail_desc", "multi_business_pay_deduction")
		if len(template) > 0 {
			return &proto.NewFormFeeDesc{
				BorderColor: template["border_color"].String(),
				Content: util.ReplaceTag(ctx, template["content"].String(), map[string]string{
					"num":  fmt.Sprintf("%.2f", failFee),
					"num2": fmt.Sprintf("%.2f", succFee),
				}),
				Icon:           template["icon"].String(),
				Amount:         0,
				Type:           2,
				TextColor:      util.StringPtr(template["text_color"].String()),
				BgColor:        util.StringPtr(template["bg_color"].String()),
				HighlightColor: util.StringPtr(template["high_light_color"].String()),
			}
		}
	}

	return nil
}

// GetVcardDesc 获取vcard
func GetVcardDesc(ctx context.Context, prov PriceInfoDescListProvider) *proto.NewFormFeeDesc {

	// 沟通体系: 高优走下面
	renderObj := cardRenderCore.NewCardRenderObj(log.Trace, communicate.BubbleAnycarV3Page, communicate.FeeDescComponent)
	if desc := renderObj.BuildFeeDesc(ctx, prov.GetVCard(), nil); desc != nil {
		return &proto.NewFormFeeDesc{
			BorderColor:    desc.BorderColor,
			Content:        desc.Content,
			Icon:           desc.Icon,
			TextColor:      desc.TextColor,
			BgColor:        desc.BgColor,
			HighlightColor: desc.HighlightColor,
		}
	}

	var (
		firstIndex  = "0"
		nextIndex   = "1"
		thirdIndex  = "2"
		fourthIndex = "3"
		fifthIndex  = "4"
	)

	dcmpVcard := &common_logic.DcmpVcard{}
	vcardText := &dcmpVcard.VcardText
	// 读取极速拼车-新增赠卡相关文案
	config := dcmp.GetDcmpContent(ctx, "anycar_v3-new_free_card", nil)
	if err := json.Unmarshal([]byte(config), vcardText); err != nil {
		return &proto.NewFormFeeDesc{}
	}

	//1、获取Vcard服务
	vcardResult := prov.GetVCard()
	if vcardResult == nil {
		return &proto.NewFormFeeDesc{}
	}

	var failDesc *proto.NewFormFeeDesc
	if carpool.SendNew == vcardResult.Source {
		textColor := (*vcardText)[firstIndex].TextColor
		bgColor := (*vcardText)[firstIndex].BgColor
		failDesc = &proto.NewFormFeeDesc{
			BorderColor: (*vcardText)[firstIndex].BorderColor,
			Content:     (*vcardText)[firstIndex].Text,
			Icon:        (*vcardText)[firstIndex].LeftIcon,
			Amount:      0.0,
			Type:        0,
			TextColor:   &textColor,
			BgColor:     &bgColor,
		}
	} else if carpool.SendLoss == vcardResult.Source {
		textColor := (*vcardText)[nextIndex].TextColor
		bgColor := (*vcardText)[nextIndex].BgColor
		failDesc = &proto.NewFormFeeDesc{
			BorderColor: (*vcardText)[nextIndex].BorderColor,
			Content:     (*vcardText)[nextIndex].Text,
			Icon:        (*vcardText)[nextIndex].LeftIcon,
			Amount:      0.0,
			Type:        0,
			TextColor:   &textColor,
			BgColor:     &bgColor,
		}
	} else if carpool.Usable == vcardResult.Source && carpool.PayStatus == vcardResult.PayStatus {
		textColor := (*vcardText)[thirdIndex].TextColor
		bgColor := (*vcardText)[thirdIndex].BgColor
		failDesc = &proto.NewFormFeeDesc{
			BorderColor: (*vcardText)[thirdIndex].BorderColor,
			Content:     (*vcardText)[thirdIndex].Text,
			Icon:        (*vcardText)[thirdIndex].LeftIcon,
			Amount:      0.0,
			Type:        0,
			TextColor:   &textColor,
			BgColor:     &bgColor,
		}
	} else if carpool.Usable == vcardResult.Source && carpool.PayStatus != vcardResult.PayStatus {
		textColor := (*vcardText)[fourthIndex].TextColor
		bgColor := (*vcardText)[fourthIndex].BgColor
		failDesc = &proto.NewFormFeeDesc{
			BorderColor: (*vcardText)[fourthIndex].BorderColor,
			Content:     (*vcardText)[fourthIndex].Text,
			Icon:        (*vcardText)[fourthIndex].LeftIcon,
			Amount:      0.0,
			Type:        0,
			TextColor:   &textColor,
			BgColor:     &bgColor,
		}
	} else if carpool.UsableGive == vcardResult.Source {
		textColor := (*vcardText)[fifthIndex].TextColor
		bgColor := (*vcardText)[fifthIndex].BgColor
		failDesc = &proto.NewFormFeeDesc{
			BorderColor: (*vcardText)[fifthIndex].BorderColor,
			Content:     (*vcardText)[fifthIndex].Text,
			Icon:        (*vcardText)[fifthIndex].LeftIcon,
			TextColor:   &textColor,
			BgColor:     &bgColor,
		}
	}
	return failDesc
}

func getCarpoolMemberCouponDesc(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider, dcmpKey string, isBigFontSize bool) *proto.NewFormFeeDesc {
	var (
		content       string
		successAmount float64
		failAmount    float64
	)

	if prov.GetCouponInfo() == nil || prov.GetCouponInfo().CustomTag == "" || !strings.Contains(prov.GetCouponInfo().CustomTag, "member_v3") {
		return nil
	}
	successAmount = cast.ToFloat64(util.FormatPriceWithoutZero(cast.ToFloat64(prov.GetCouponInfo().Amount)/100, 2))
	if prov.GetCarpoolFailCouponInfo() != nil && prov.GetCarpoolFailCouponInfo().CustomTag != "" && strings.Contains(prov.GetCarpoolFailCouponInfo().CustomTag, "member_v3") {
		failAmount = cast.ToFloat64(util.FormatPriceWithoutZero(cast.ToFloat64(prov.GetCarpoolFailCouponInfo().Amount)/100, 2))
	}
	if 0 == successAmount || 0 == failAmount {
		return nil
	}
	path := "carpool_paid_member_level_" + cast.ToString(prov.GetLevelID())
	conf := dcmp.GetJSONMap(ctx, dcmpKey, path)
	if conf == nil || len(conf) == 0 {
		return nil
	}
	content = util.ReplaceTag(ctx, conf["content"].String(), map[string]string{
		"num": cast.ToString(successAmount),
	})
	desc := &proto.NewFormFeeDesc{
		Icon:           conf["icon"].String(),
		BorderColor:    "",
		Content:        content,
		TextColor:      util.String2PtrString(conf["text_color"].String()),
		HighlightColor: util.String2PtrString(conf["highlight_color"].String()),
	}
	if prov.IsCarpoolUnSuccessFlatPriceShowAsCapPrice(ctx) {
		return desc
	} else {
		ascOrder := common_logic.DualPriceOrderedOnAscV3(prov)
		if isBigFontSize {
			ascOrder = isBigFontSize
		}
		desc.Content = common_logic.GetDualPricetContentWithSort(ctx, successAmount, failAmount, dcmpKey, path, ascOrder)
		return desc
	}
}

func getMemberDoubleDiscount(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider, dcmpKey string, isBigFontSize bool) *proto.NewFormFeeDesc {
	card := prov.GetDiscountCard()
	if card == nil || card.Amount <= 0 {
		return nil
	}

	path := "carpool_paid_member_level_" + cast.ToString(prov.GetLevelID())
	conf := dcmp.GetJSONMap(ctx, dcmpKey, path)
	if conf == nil || len(conf) == 0 {
		return nil
	}
	var failDiff float64
	totalFeeWithoutDiscount := prov.GetTotalFeeWithoutDiscount()
	succDiff := cast.ToFloat64(util.FormatPriceWithoutZero(totalFeeWithoutDiscount-prov.GetEstimateFee(), 2))
	if failFee, ok := prov.GetCarpoolFailEstimateFee(); ok && failFee > 0 && prov.GetEstimateFee() != failFee {
		failDiff = cast.ToFloat64(util.FormatPriceWithoutZero(totalFeeWithoutDiscount-failFee, 2))
	}
	if 0 == succDiff || 0 == failDiff {
		return nil
	}
	content := util.ReplaceTag(ctx, conf["content"].String(), map[string]string{
		"num": cast.ToString(succDiff),
	})
	desc := &proto.NewFormFeeDesc{
		BorderColor:    "",
		HighlightColor: util.StringPtr(conf["highlight_color"].String()),
		TextColor:      util.StringPtr(conf["text_color"].String()),
		Icon:           conf["icon"].String(),
		Content:        content,
	}
	if succDiff > 0 && failDiff > 0 && succDiff != failDiff { // 两口价
		ascOrder := common_logic.DualPriceOrderedOnAscV3(prov)
		if isBigFontSize {
			ascOrder = isBigFontSize
		}
		content = common_logic.GetDualPricetContentWithSort(ctx, succDiff, failDiff, dcmpKey, path, ascOrder)
		desc.Content = content
	}
	return desc
}

func getDualDiscount4Classify(ctx context.Context, prov fee_info_render.PriceInfoDescListProvider, dcmpKey string) *proto.NewFormFeeDesc {
	feeDetailInfo := prov.GetBillFeeDetailInfo()
	// 拼成价，券前价-预估价+sps
	successAmount := prov.GetDynamicTotalFee() - prov.GetEstimateFee()
	successAmount += math.Abs(feeDetailInfo["sps_carpool_discount_surprised"])
	successAmount += math.Abs(feeDetailInfo["sps_carpool_discount_surprised_two"])
	// 未拼成价，未拼成券前价-未拼成预估价
	failEstimateFee, _ := prov.GetCarpoolFailEstimateFee()
	failAmount := prov.GetCarpoolFailRawBill().DynamicTotalFee - failEstimateFee
	// 两者取最大，为最高优惠
	discountAmount := math.Max(successAmount, failAmount)
	discountAmount = cast.ToFloat64(util.FormatPriceWithoutZero(discountAmount, 2))
	if prov.IsCarpoolUnSuccessFlatPriceShowAsCapPrice(ctx) {
		conf := dcmp.GetJSONMap(ctx, dcmpKey, "normal_discount")
		if conf == nil || len(conf) == 0 || successAmount == 0 {
			return nil
		}
		return &proto.NewFormFeeDesc{
			Icon:           conf["icon"].String(),
			BorderColor:    "",
			Content:        util.ReplaceTag(ctx, conf["content"].String(), map[string]string{"num": util.FormatPriceWithoutZero(successAmount, 2)}),
			TextColor:      util.String2PtrString(conf["text_color"].String()),
			HighlightColor: util.String2PtrString(conf["highlight_color"].String()),
		}
	}
	conf := dcmp.GetJSONMap(ctx, dcmpKey, "dual_normal_discount")
	if conf == nil || len(conf) == 0 || discountAmount == 0 {
		return nil
	}
	return &proto.NewFormFeeDesc{
		Icon:           conf["icon"].String(),
		BorderColor:    "",
		Content:        util.ReplaceTag(ctx, conf["content"].String(), map[string]string{"num": cast.ToString(discountAmount)}),
		TextColor:      util.String2PtrString(conf["text_color"].String()),
		HighlightColor: util.String2PtrString(conf["highlight_color"].String()),
	}
}

func GetCarpoolCouponDesc(ctx context.Context, prov common_logic.PriceDescCommonProvider, pageType int32) *proto.NewFormFeeDesc {
	var (
		customTag string
		content   string
	)

	if prov.GetCouponInfo() == nil || prov.GetCouponInfo().CustomTag == "" {
		return nil
	}

	customTag = prov.GetCouponInfo().CustomTag
	conf := dcmp.GetJSONMap(ctx, "estimate_form_v3-carpool_fee_desc", customTag)
	if conf == nil || len(conf) == 0 {
		return nil
	}

	content = conf["content"].String()
	if customTag == "CARPOOL_RAISE" {
		content = getCarpoolRaiseTag(ctx, prov)
	}
	if content == "" {
		return nil
	}

	return &proto.NewFormFeeDesc{
		Icon:        conf["icon"].String(),
		BorderColor: conf["border_color"].String(),
		Content:     content,
	}
}

func getCarpoolRaiseTag(ctx context.Context, prov common_logic.PriceDescCommonProvider) string {
	apolloKey, apolloParam := prov.ApolloParamsGen(apollo_model.WithPIDKey)
	isAllow, assignment := apollo.FeatureExp(ctx, "carpool_raise_fee_desc_ab_2", apolloKey, apolloParam)
	if isAllow {
		if assignment.GetGroupName() != "control_group" && assignment.GetParameter("coupon_desc", "") != "" {
			return assignment.GetParameter("coupon_desc", "")
		}
	}
	return ""
}
