package util

import (
	"context"
	"errors"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"

	mambaApollo "git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
)

// 该测试函数由AI自动生成
func TestGetGeneralFeeMsgTemplate(t *testing.T) {
	// 设置测试上下文
	ctx := context.Background()

	t.Run("城市特定配置存在", func(t *testing.T) {
		// Mock Apollo配置获取
		mockConfigs := []*model.ConfResult{
			model.NewConfResult("fee_msg_conf", "test", map[string]string{"fee_msg_template": "test_fee_msg"}, nil, ""),
		}

		patchGetConfigsWithCity := mockey.Mock(mambaApollo.GetConfigsByNamespaceAndConditionsWithLang).Return(mockConfigs, nil).Build()
		defer patchGetConfigsWithCity.UnPatch()

		// 执行测试函数
		result := GetGeneralFeeMsgTemplateNew(ctx, 1, 10, "zh-CN")

		// 断言结果
		assert.Equal(t, "test_fee_msg", result, "应返回城市特定配置")
		assert.Equal(t, 1, patchGetConfigsWithCity.Times(), "应调用GetConfigsByNamespaceAndConditionsWithLang函数一次")
	})

	t.Run("两次获取配置都失败", func(t *testing.T) {
		// Mock Apollo配置获取，两次调用都返回nil
		patchGetConfigs := mockey.Mock(mambaApollo.GetConfigsByNamespaceAndConditionsWithLang).
			Return(nil, errors.New("config not found")).Build()
		defer patchGetConfigs.UnPatch()

		// 执行测试函数
		result := GetGeneralFeeMsgTemplateNew(ctx, 1, 10, "zh-CN")

		// 断言结果
		assert.Equal(t, "", result, "没有配置时应返回空字符串")
		assert.Equal(t, 2, patchGetConfigs.Times(), "应调用GetConfigsByNamespaceAndConditionsWithLang函数两次")
	})

	t.Run("获取配置成功但无fee_msg项", func(t *testing.T) {
		// Mock Apollo配置获取
		mockConfigs := []*model.ConfResult{
			model.NewConfResult("fee_msg_conf", "test", nil, nil, ""),
		}

		patchGetConfigs := mockey.Mock(mambaApollo.GetConfigsByNamespaceAndConditionsWithLang).
			Return(mockConfigs, nil).Build()
		defer patchGetConfigs.UnPatch()

		// 执行测试函数
		result := GetGeneralFeeMsgTemplateNew(ctx, 1, 10, "zh-CN")

		// 断言结果
		assert.Equal(t, "", result, "没有fee_msg项时应返回空字符串")
	})
}
