package tab_extra_data

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_category"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

const (
	timeUnitHour   = "小时"
	timeUnitMinute = "分钟"
)

func GetTabExtraData(ctx context.Context, estimateProducts []*biz_runtime.ProductInfoFull) *proto.TabExtraData {
	if estimateProducts == nil || len(estimateProducts) == 0 {
		return nil
	}

	var product *biz_runtime.ProductInfoFull
	for _, estimateProduct := range estimateProducts {
		if estimateProduct.Product != nil && estimateProduct.Product.ProductCategory == product_category.ProductCategoryFastCarNormal {
			product = estimateProduct
			break
		}
	}

	if product == nil {
		return nil
	}

	if product.GetBillInfo() == nil || product.GetBillDriverMinute() == 0 {
		return nil
	}

	return &proto.TabExtraData{
		Classify: &proto.Classify{
			TimeText: GetClassifyTimeText(ctx, product.GetBillDriverMinute()),
		},
	}
}

func GetClassifyTimeText(ctx context.Context, driverMinute int64) string {
	if driverMinute <= 0 {
		return ""
	}

	if driverMinute <= 60 {
		return util.ToString(driverMinute) + timeUnitMinute
	}

	hour := driverMinute / 60
	minute := driverMinute % 60
	if minute == 0 {
		return util.ToString(hour) + timeUnitHour
	}

	return util.ToString(hour) + timeUnitHour + util.ToString(minute)
}
