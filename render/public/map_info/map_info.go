package map_info

import (
	"context"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

type MapInfoProvider interface {
	GetCarpoolType() int64
	GetMapInfoCacheToken() string
	GetMapinfoStartCacheToken() string
	GetMapinfoDestCacheToken() string
	ShowMiniBusStation() bool
	GetExtMap() map[string]string
	GetBaseReqData() *models.BaseReqData
	GetProductCategory() int64
	GetBillExtraMap() map[string]interface{}
}

func GetMapInfo(ctx context.Context, prov MapInfoProvider) *proto.MapInfo {
	if carpool.IsMiniBus(int(prov.GetCarpoolType())) {
		return getMiniBusMapInfo(ctx, prov)
	}

	if prov.GetProductCategory() == estimate_pc_id.EstimatePcIdAutopilotByTC {
		return getAutopilotMapInfo(ctx, prov)
	}

	if carpool.IsSmartBus(int(prov.GetCarpoolType())) {
		return getSmartBusMapInfo(ctx, prov)
	}

	return nil
}

func getMiniBusMapInfo(ctx context.Context, prov MapInfoProvider) *proto.MapInfo {
	mapInfo := &proto.MapInfo{
		MapinfoCacheToken:      prov.GetMapInfoCacheToken(),
		ShowMiniBusStation:     prov.ShowMiniBusStation(),
		MapinfoStartCacheToken: prov.GetMapinfoStartCacheToken(),
		MapinfoDestCacheToken:  prov.GetMapinfoDestCacheToken(),
	}

	toggle := apollo.FeatureToggle(ctx, "minibus_long_trip_open_city_switch", util.ToString(prov.GetBaseReqData().PassengerInfo.PID), map[string]string{
		"pid":           util.ToString(prov.GetBaseReqData().PassengerInfo.PID),
		"access_key_id": util.ToString(prov.GetBaseReqData().CommonInfo.AccessKeyID),
		"app_version":   prov.GetBaseReqData().CommonInfo.AppVersion,
		"page_type":     util.ToString(prov.GetBaseReqData().CommonInfo.PageType),
	})
	if !toggle {
		return mapInfo
	}

	mapInfo.BestViewType = 1
	extMap := prov.GetExtMap()
	if extMap != nil {
		if _, ok := extMap["long_trip_id"]; ok {
			mapInfo.BestViewType = 2
		}
	}

	return mapInfo
}

func getAutopilotMapInfo(ctx context.Context, prov MapInfoProvider) *proto.MapInfo {
	historyExtraMap := prov.GetBillExtraMap()
	if historyExtraMap == nil {
		return nil
	}

	voyRouteId, ok := historyExtraMap["voy_route_id"]
	if !ok {
		return nil
	}

	toggle := apollo.FeatureToggle(ctx, "zidongjiashi_choose_cell_open", util.ToString(prov.GetBaseReqData().PassengerInfo.PID), map[string]string{
		"pid":           util.ToString(prov.GetBaseReqData().PassengerInfo.PID),
		"phone":         util.ToString(prov.GetBaseReqData().PassengerInfo.Phone),
		"access_key_id": util.ToString(prov.GetBaseReqData().CommonInfo.AccessKeyID),
		"app_version":   prov.GetBaseReqData().CommonInfo.AppVersion,
		"page_type":     util.ToString(prov.GetBaseReqData().CommonInfo.PageType),
		"tab_id":        prov.GetBaseReqData().CommonInfo.TabId,
	})
	if !toggle {
		return nil
	}

	return &proto.MapInfo{
		VoyRouteId: util.ToString(voyRouteId),
	}
}

func getSmartBusMapInfo(_ context.Context, prov MapInfoProvider) *proto.MapInfo {
	return &proto.MapInfo{
		MapinfoStartCacheToken: prov.GetMapinfoStartCacheToken(),
		MapinfoDestCacheToken:  prov.GetMapinfoDestCacheToken(),
		MinibusType:            1,
	}
}
