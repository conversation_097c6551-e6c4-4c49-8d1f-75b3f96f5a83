package estimate_extra

import (
	"context"

	commonConsts "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"

	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/tripcloud"
)

func GetIsTripcloud(ctx context.Context, product *biz_runtime.ProductInfoFull) bool {
	if tripcloud.IsTripcloudProductID(commonConsts.ProductID(product.Product.ProductID)) {
		return true
	}

	return false
}
