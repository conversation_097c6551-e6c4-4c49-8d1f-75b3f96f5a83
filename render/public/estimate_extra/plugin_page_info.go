package estimate_extra

import (
	"context"
	"strconv"
	"time"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/terminal/access_key_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/redis"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	trace "git.xiaojukeji.com/lego/context-go"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

const (
	// NativeControlDisplayType 端控制频率
	NativeControlDisplayType = 3
)

// GetPluginPageInfo 获取春节服务费拦截页
func GetPluginPageInfo(ctx context.Context, req *models.BaseReqData, holidayFee float64, control bool) *proto.PluginPageInfo {
	if holidayFee > 0 {
		params := map[string]string{
			"key":           strconv.FormatInt(req.PassengerInfo.PID, 10),
			"phone":         req.PassengerInfo.Phone,
			"city":          strconv.FormatInt(int64(req.AreaInfo.City), 10),
			"page_type":     strconv.FormatInt(int64(req.CommonInfo.PageType), 10),
			"access_key_id": strconv.FormatInt(int64(req.CommonInfo.AccessKeyID), 10),
			"app_version":   req.CommonInfo.AppVersion,
			"lang":          req.CommonInfo.Lang,
		}

		// 拦截页灰度
		isHit := apollo.FeatureToggle(ctx, "red_packet_intercept_page_switch", req.PassengerInfo.Phone, params)
		if !isHit {
			return nil
		}

		isShow := apollo.FeatureToggle(ctx, "gs_festival_service_fee_h5times_switch", req.PassengerInfo.Phone, params)
		if isShow {
			return getHolidayPage(ctx, holidayFee, req)
		}

		if control {
			// 控制频次
			redisKey := "p_red_packet_last_value" + strconv.FormatInt(req.PassengerInfo.PID, 10)
			get, err := redis.GetClient().Get(ctx, redisKey)
			if len(get) > 0 && err == nil {
				return nil
			}

			// 拦截弹窗超时时间 15天
			if _, err := redis.GetClient().SetEx(ctx, redisKey, time.Duration(15*24)*time.Hour, holidayFee); err != nil {
				log.Trace.Warnf(ctx, trace.DLTagUndefined, "set holiday page,redisKey=%s, err:%v", redisKey, err)
			}
		}

		return getHolidayPage(ctx, holidayFee, req)
	}
	return nil
}

func getHolidayPage(ctx context.Context, holidayFee float64, req *models.BaseReqData) *proto.PluginPageInfo {
	tag := map[string]string{
		"holiday_fee": util.Float64ToString(holidayFee),
		"flng":        util.ToString(req.AreaInfo.FromLng),
		"flat":        util.ToString(req.AreaInfo.FromLat),
	}
	urlString := dcmp.GetDcmpContent(ctx, "common-holiday_fee_url", tag)
	return &proto.PluginPageInfo{
		Type:   NativeControlDisplayType,
		ShowH5: urlString,
	}
}

// GetFullPluginPageInfo 获取拦截页 （动调、春节服务费）
func GetFullPluginPageInfo(ctx context.Context, estimateProducts []*biz_runtime.ProductInfoFull) *proto.PluginPageInfo {
	var (
		hitDynamicPage bool
		pluginPageInfo *proto.PluginPageInfo
		baseReqData    *models.BaseReqData
		dynamicH5      string
		holidayFee     float64
	)

	if len(estimateProducts) > 0 && estimateProducts[0] != nil {
		baseReqData = estimateProducts[0].BaseReqData
	}

	if baseReqData == nil {
		return nil
	}

	for _, product := range estimateProducts {
		if !product.GetIsUserUseDpa() {
			// 用户上一次在动调页选择过dpa，则这次预估不下发动调确认
			if product.GetNewDynamicDiffPrice() > 0 {
				hitDynamicPage = true
			}
		}

		if product.GetHolidayFee() > 0 {
			holidayFee = product.GetHolidayFee()
		}
	}

	// 拦截页优先级：春节服务费 > 动调
	holidayPage := GetPluginPageInfo(ctx, baseReqData, holidayFee, true)
	if holidayPage != nil {
		return holidayPage
	}

	if hitDynamicPage {
		if util.InArrayInt32(baseReqData.CommonInfo.AccessKeyID, []int32{
			access_key_id.AccessKeyIdDidiIosPassengerApp,
			access_key_id.AccessKeyIdDidiAndroidPassengerApp,
		}) {
			dynamicH5 = dcmp.GetJSONContentWithPath(ctx, "estimate_form_v3-plugin_page_info", nil, "dynamic_price_new_h5")
		} else if util.InArrayInt32(baseReqData.CommonInfo.AccessKeyID, []int32{
			access_key_id.AccessKeyIdDidiWeChatMiniProgram,
			access_key_id.AccessKeyIdDidiAlipayMiniProgram,
			access_key_id.AccessKeyIdStandardWebapp,
		}) {
			dynamicH5 = dcmp.GetJSONContentWithPath(ctx, "estimate_form_v3-plugin_page_info", nil, "dynamic_price_program")
		}
	}

	if dynamicH5 != "" {
		pluginPageInfo = &proto.PluginPageInfo{
			Type:      1,
			ConfirmH5: dynamicH5,
		}

		return pluginPageInfo
	}

	return nil
}
