package instance

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"
	"strconv"
)

type ClassifyNormalCategory struct {
}

func (c *ClassifyNormalCategory) IsDisplay(ctx context.Context, req *models.BaseReqData, productMap map[int64]*biz_runtime.ProductInfoFull, categoryIdList *util.Set[int32]) bool {
	if !tab.IsClassifyTab(req.CommonInfo.TabId) {
		return false
	}

	// 2. 大字版适配，不下发分框
	if req.CommonInfo.FontScaleType != 0 {
		return false
	}

	// 获取分栏数量
	// 根据开关决定是否下发
	apolloParams := map[string]string{
		"pid":               strconv.FormatInt(req.PassengerInfo.PID, 10),
		"city_id":           strconv.FormatInt(int64(req.AreaInfo.City), 10),
		"app_version":       req.CommonInfo.AppVersion,
		"access_key_id":     strconv.FormatInt(int64(req.CommonInfo.AccessKeyID), 10),
		"tab_product_count": strconv.Itoa(len(productMap)),
		"frame_count":       cast.ToString(categoryIdList.Size()),
		"order_type":        strconv.FormatInt(int64(req.CommonInfo.OrderType), 10),
		"lang":              req.CommonInfo.Lang,
		"form_style_exp":    strconv.FormatInt(int64(req.CommonBizInfo.FormStyleExp), 10),
	}

	// 检查Apollo开关
	if !apollo.FeatureToggle(ctx, "gs_category_info_show_switch", strconv.FormatInt(req.PassengerInfo.PID, 10), apolloParams) {
		return false
	}

	return true
}

func (c *ClassifyNormalCategory) Render(ctx context.Context, categoryConf []*models.CategoryItem) []*proto.CategoryData {

	selectedCategoryId := 1
	var categoryInfo []*proto.CategoryData
	for _, categoryItem := range categoryConf {
		categoryData := &proto.CategoryData{
			CategoryId:  categoryItem.CategoryID,
			Title:       categoryItem.Title,
			SubTitle:    categoryItem.SubTitle,
			Icon:        categoryItem.Icon,
			BgGradients: categoryItem.BgGradients,
			IsSelected:  util.Bool2I32(categoryItem.CategoryID == int32(selectedCategoryId)),
			//IsFold:          int32(classifyFold.GetIsFoldByCategoryId(categoryItem.CategoryID)),
			//ClickNeedExpand: int32(isClickNeedExpand),
		}

		//foldStatus := categoryFoldStatusMap[int32(categoryItem.CategoryID)]
		// foldText 优先取配置，否则取原始
		foldText := categoryItem.FoldText
		//if aConfig != nil {
		//	if foldConf, ok := aConfig[cast.ToString(foldStatus)]; ok {
		//		foldText = foldConf.FoldText
		//	}
		//}
		categoryData.FoldText = foldText

		categoryInfo = append(categoryInfo, categoryData)
	}

	return categoryInfo
}
