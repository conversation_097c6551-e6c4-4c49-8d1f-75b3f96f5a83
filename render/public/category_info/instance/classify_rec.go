package instance

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/category_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"sort"
)

type ClassifyRecCategory struct {
}

func (c *ClassifyRecCategory) IsDisplay(ctx context.Context, req *models.BaseReqData, productMap map[int64]*biz_runtime.ProductInfoFull, categoryIdList *util.Set[int32]) bool {
	return true
}

func (c *ClassifyRecCategory) Render(ctx context.Context, categoryConf []*models.CategoryItem) []*proto.CategoryData {

	categoryConf = c.Sort(ctx, categoryConf)
	selectedCategoryId := c.Check(categoryConf)

	//classifyFold := NewClassifyFoldLogic(ctx, c.req, c.productMap)
	//categoryFoldStatusMap := classifyFold.GetCategoryFoldStatusMap()

	//aConfig := c.dcmpConfig.Fold

	var categoryInfo []*proto.CategoryData
	for _, categoryItem := range categoryConf {
		categoryData := &proto.CategoryData{
			CategoryId:  categoryItem.CategoryID,
			Title:       categoryItem.Title,
			SubTitle:    categoryItem.SubTitle,
			Icon:        categoryItem.Icon,
			BgGradients: categoryItem.BgGradients,
			IsSelected:  util.Bool2I32(categoryItem.CategoryID == int32(selectedCategoryId)),
			//IsFold:          int32(classifyFold.GetIsFoldByCategoryId(categoryItem.CategoryID)),
			//ClickNeedExpand: int32(isClickNeedExpand),
		}

		//foldStatus := categoryFoldStatusMap[int32(categoryItem.CategoryID)]
		// foldText 优先取配置，否则取原始
		foldText := categoryItem.FoldText
		//if aConfig != nil {
		//	if foldConf, ok := aConfig[cast.ToString(foldStatus)]; ok {
		//		foldText = foldConf.FoldText
		//	}
		//}
		categoryData.FoldText = foldText

		categoryInfo = append(categoryInfo, categoryData)
	}

	return categoryInfo
}

func (c *ClassifyRecCategory) Sort(ctx context.Context, categoryConf []*models.CategoryItem) []*models.CategoryItem {
	//classifyFold := NewClassifyFoldLogic(ctx, req, productMap)
	//categoryFoldStatusMap := classifyFold.GetCategoryFoldStatusMap()
	// 临时使用空的折叠状态映射
	//categoryFoldStatusMap := make(map[int32]int)
	result := make([]*models.CategoryItem, len(categoryConf))
	copy(result, categoryConf)

	// 根据PHP代码的排序逻辑进行排序
	sort.SliceStable(result, func(i, j int) bool {
		a := result[i]
		b := result[j]
		//aStatus := categoryFoldStatusMap[a.CategoryID]
		//bStatus := categoryFoldStatusMap[b.CategoryID]

		// 送货品类排在最后
		if int(a.CategoryID) == category_id.GoodsCategoryID {
			return false // a在b后面
		}
		if int(b.CategoryID) == category_id.GoodsCategoryID {
			return true // b在a后面
		}

		// 全折叠的排在后面
		//if aStatus == FoldAll && bStatus != FoldAll {
		//	return false // a在b后面
		//}
		//if bStatus == FoldAll && aStatus != FoldAll {
		//	return true // b在a后面
		//}

		// 按rank排序，rank越大优先级越高
		return a.Rank > b.Rank
	})
	return result
}

func (c *ClassifyRecCategory) Check(categoryFinalConf []*models.CategoryItem) int {
	return category_id.RecFormCategory
}
