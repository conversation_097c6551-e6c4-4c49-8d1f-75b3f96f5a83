package instance

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type CategoryInfoStrategy interface {
	IsDisplay(ctx context.Context, req *models.BaseReqData, productMap map[int64]*biz_runtime.ProductInfoFull, categoryIdList *util.Set[int32]) bool
	Render(ctx context.Context, categoryConf []*models.CategoryItem) []*proto.CategoryData
}
