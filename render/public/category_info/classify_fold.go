package category_info

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/category_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/group_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/nuwa/golibs/knife"
	"slices"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"
)

const (
	FlowExposureFeature = "exposure_line"

	// 折叠类型常量
	FoldUnfold   = 0
	FoldOnlyFold = 1
	FoldIntoBox  = 2 // 流量盒子

	// 折叠状态常量
	FoldAll  = 1
	FoldSome = 2
	FoldZero = 3
)

// ClassifyFoldLogic 品类折叠逻辑
type ClassifyFoldLogic struct {
	req           *models.BaseReqData
	bizProductMap map[int64]*biz_runtime.ProductInfoFull

	group2FoldType map[string]int32

	// 流量盒子 分栏=>品类映射 map[category_id][]pcIds
	flowBoxCategoryIdToPcIDMap map[int32][]int64
	// 流量盒子 分栏=>盒子映射 map[category_id][]subGroupIds
	flowBoxCategoryIdToSubGroupIdMap map[int32][]int32

	// 记录最终进入盒子的盒子id map[category_id][sub_group_id]bool
	finalInBoxSubGroupIdSet *util.Set[string]
	// 记录最终进入盒子的盒子id,layout数据map map[sub_group_id]interface{}
	finalInBoxGroupIdLayoutMap map[string]interface{}

	// 分栏=>折叠 map[category_id]is_fold
	isFoldCategoryIdToFoldMap map[int32]int
	// 分栏=>折叠类型 map[category_id]fold_status
	categoryFoldStatusMap map[int32]int
}

func GetClassifyFold(ctx context.Context, req *models.BaseReqData, bizProductMap map[int64]*biz_runtime.ProductInfoFull) *ClassifyFoldLogic {
	//if !CheckCanFold(ctx, req) {
	//	return nil
	//}
	res, ok := knife.Get(ctx, consts.ClassifyFold).(*ClassifyFoldLogic)
	if ok && res != nil {
		return res
	}
	res = NewClassifyFoldLogic(ctx, req, bizProductMap)
	knife.Set(ctx, consts.ClassifyFold, res)
	return res
}

func NewClassifyFoldLogic(ctx context.Context, req *models.BaseReqData, bizProductMap map[int64]*biz_runtime.ProductInfoFull) *ClassifyFoldLogic {
	result := &ClassifyFoldLogic{
		req:                              req,
		bizProductMap:                    bizProductMap,
		group2FoldType:                   make(map[string]int32),
		flowBoxCategoryIdToPcIDMap:       make(map[int32][]int64),
		flowBoxCategoryIdToSubGroupIdMap: make(map[int32][]int32),
		finalInBoxSubGroupIdSet:          util.NewSet[string](),
		finalInBoxGroupIdLayoutMap:       make(map[string]interface{}),
		isFoldCategoryIdToFoldMap:        make(map[int32]int),
		categoryFoldStatusMap:            make(map[int32]int),
	}

	// 兜底，三方品类折叠
	if len(req.CommonBizInfo.AthenaResult.GroupId2FoldType) == 0 {
		result.dealDefault(ctx)
	}

	if req.CommonBizInfo.RecForm == 1 {
		// 清楚兜底结果
		result.dealRecTabFold(ctx)
	}
	return result
}

// dealRecTabFold 处理推荐tab 进盒子/折叠逻辑
func (c *ClassifyFoldLogic) dealRecTabFold(ctx context.Context) {
	athenaResult := c.req.CommonBizInfo.AthenaResult

	status := make(map[int32][]int)
	for pcId, pull := range c.bizProductMap {
		subGroupID := pull.GetSubGroupId()
		groupId := group_id.BuildGroupIdById(int64(subGroupID), pcId)
		categoryId := pull.GetCategoryId(ctx, int64(subGroupID), pcId)
		isSubGroup := subGroupID != 0
		if pull.GetBizInfo().IsRec {
			c.group2FoldType[groupId] = FoldOnlyFold

			c.handleFoldByType(
				util.GetOrDefault(athenaResult.GroupId2FoldType[groupId] != 0, int(athenaResult.GroupId2FoldType[groupId]), int(0)),
				category_id.RecFormCategory,
				util.GetOrDefault(isSubGroup, nil, &pcId),
				util.GetOrDefault(isSubGroup, &subGroupID, nil),
			)
		}

		status[categoryId] = append(status[categoryId], util.GetOrDefault(pull.GetBizInfo().IsRec, 1, -1))
	}

	c.categoryFoldStatusMap = c.mapStatusToFoldType(status)
}

// mapStatusToFoldType 将状态映射为折叠类型
func (c *ClassifyFoldLogic) mapStatusToFoldType(status map[int32][]int) map[int32]int {
	foldTypeMap := make(map[int32]int)
	for categoryId, items := range status {
		uniqueStatus := util.UniqueSlice(items)
		foldTypeMap[categoryId] = util.GetOrDefault(len(uniqueStatus) == 1,
			util.GetOrDefault(uniqueStatus[1] == 1, FoldAll, FoldZero),
			FoldSome,
		)
	}
	return foldTypeMap
}

// handleFoldByType 根据折叠类型处理
func (c *ClassifyFoldLogic) handleFoldByType(foldType int, categoryId int32, pcId *int64, subGroupID *int32) {
	if foldType == FoldIntoBox {
		if subGroupID != nil {
			subGroupIDs := c.flowBoxCategoryIdToSubGroupIdMap[categoryId]
			subGroupIDs = append(subGroupIDs, *subGroupID)
			// 去重
			uniqueSubGroupIDs := util.UniqueSlice(subGroupIDs)
			c.flowBoxCategoryIdToSubGroupIdMap[categoryId] = uniqueSubGroupIDs
		} else if pcId != nil {
			c.flowBoxCategoryIdToPcIDMap[categoryId] = append(c.flowBoxCategoryIdToPcIDMap[categoryId], *pcId)
		}
	}

	if foldType == FoldOnlyFold {
		c.isFoldCategoryIdToFoldMap[categoryId] = 1
	}
}

// CheckCanFold 检查是否可折叠/品类进盒子
func (c *ClassifyFoldLogic) CheckCanFold(ctx context.Context) bool {
	return NewCategoryInfo(ctx, c.req, c.bizProductMap).IsDisplay(ctx)
}

func (c *ClassifyFoldLogic) GetFoldTypeByGroupId(groupId string) int32 {
	return c.group2FoldType[groupId]
}

// GetFlowBoxCategoryIdToPcIDMap 获取流量盒子分栏到品类的映射
func (c *ClassifyFoldLogic) GetFlowBoxCategoryIdToPcIDMap() map[int32][]int64 {
	return c.flowBoxCategoryIdToPcIDMap
}

// GetFlowBoxCategoryIdToSubGroupIdMap 获取流量盒子分栏到子组的映射
func (c *ClassifyFoldLogic) GetFlowBoxCategoryIdToSubGroupIdMap() map[int32][]int32 {
	return c.flowBoxCategoryIdToSubGroupIdMap
}

// GetIsFoldByCategoryId 根据分栏ID获取是否折叠
func (c *ClassifyFoldLogic) GetIsFoldByCategoryId(categoryId int32) int {
	// 二次预估
	categoryInfo := c.req.CommonInfo.CategoryInfo
	if categoryInfo != nil {
		return int(categoryInfo[categoryId])
	}

	if len(c.categoryFoldStatusMap) > 0 {
		return util.GetOrDefault(
			slices.Contains([]int{FoldAll, FoldSome}, c.categoryFoldStatusMap[categoryId]),
			1,
			0,
		)
	}

	return util.GetOrDefault(c.isFoldCategoryIdToFoldMap[categoryId] == 1, 1, 0)
}

// GetCategoryFoldStatusMap 获取分栏折叠状态映射
func (c *ClassifyFoldLogic) GetCategoryFoldStatusMap() map[int32]int {
	return c.categoryFoldStatusMap
}

// GetFinalInBoxSubGroupIdSet 获取最终进入盒子的子组ID映射
func (c *ClassifyFoldLogic) GetFinalInBoxSubGroupIdSet() *util.Set[string] {
	return c.finalInBoxSubGroupIdSet
}

// SetFinalInBoxSubGroupIdMap 设置最终进入盒子的子组ID映射
func (c *ClassifyFoldLogic) AddFinalInBoxSubGroupIdSet(groudId string) {
	if c.finalInBoxSubGroupIdSet == nil {
		c.finalInBoxSubGroupIdSet = util.NewSet[string]()
	}
	c.finalInBoxSubGroupIdSet.Add(groudId)
}

// GetFinalInBoxLayoutByGroupId 根据组ID获取最终进入盒子的布局
func (c *ClassifyFoldLogic) GetFinalInBoxLayoutByGroupId(groupId string) interface{} {
	if val, ok := c.finalInBoxGroupIdLayoutMap[groupId]; ok {
		return val
	}
	return nil
}

// SetFinalInBoxGroupIdLayoutMap 设置最终进入盒子的组ID布局映射
func (c *ClassifyFoldLogic) SetFinalInBoxGroupIdLayoutMap(groupId string, layout interface{}) {
	c.finalInBoxGroupIdLayoutMap[groupId] = layout
}

// GetIsHitSplitBox 获取是否命中分割盒子
func GetIsHitSplitBox(ctx context.Context, apolloParam map[string]string) bool {
	tabId := apolloParam["tab_id"]
	if tabId == "" || !tab.IsClassifyTab(tabId) {
		return false
	}

	apolloParam["key"] = apolloParam["pid"]
	// 过版本限制
	toggleParams := make(map[string]string)
	for k, v := range apolloParam {
		toggleParams[k] = cast.ToString(v)
	}

	allow, params := apollo.GetParameters("farMustCheaper_into_xDiscount_box", cast.ToString(apolloParam["pid"]), toggleParams)
	if !allow && params["hit_split_box"] == "1" {
		return true
	}

	return false
}

// dealDefault 处理默认值，兜底athena的逻辑
func (c *ClassifyFoldLogic) dealDefault(ctx context.Context) {
	if len(c.bizProductMap) == 0 {
		return
	}

	for _, bizProduct := range c.bizProductMap {
		if bizProduct == nil {
			continue
		}
		//if bizProduct.Product.IsTripcloudProduct(ctx) {
		//	c.productFoldType[int32(bizProduct.GetProductCategory())] = FoldOnlyFold
		//} // TODO 陈晓
	}
}
