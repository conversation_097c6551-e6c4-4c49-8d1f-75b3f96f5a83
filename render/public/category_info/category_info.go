package category_info

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/category_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/category_info/instance"
	"git.xiaojukeji.com/nuwa/golibs/knife"
)

type CategoryInfo struct {
	req        *models.BaseReqData
	productMap map[int64]*biz_runtime.ProductInfoFull

	categoryConf []*models.CategoryItem

	categoryIdList  *util.Set[int32]
	renderStrategy  instance.CategoryInfoStrategy
	isBuild         bool
	categoryRank    map[int]int
	categoryInfoRes []*proto.CategoryData
}

func NewCategoryInfo(ctx context.Context, req *models.BaseReqData, productMap map[int64]*biz_runtime.ProductInfoFull) *CategoryInfo {
	res, ok := knife.Get(ctx, consts.BizCategoryInfo).(*CategoryInfo)
	if ok && res != nil {
		return res
	}

	c := &CategoryInfo{
		req:        req,
		productMap: productMap,
	}

	if req.CommonBizInfo.BaseCategoryConf != nil {
		c.categoryConf = req.CommonBizInfo.BaseCategoryConf.CategoryConf
	}

	c.categoryIdList = util.NewSet[int32]()

	for _, full := range productMap {
		categoryId := full.GetCategoryId(ctx, int64(full.GetSubGroupId()), full.GetProductCategory())
		c.categoryIdList.Add(categoryId)
	}
	if c.req.CommonBizInfo.RecForm == 1 {
		c.categoryIdList.Add(category_id.RecFormCategory)
	}
	c.renderStrategy = instance.GetCategoryInfoStrategy(req)

	var categoryFinalConf []*models.CategoryItem
	for _, categoryItem := range c.categoryConf {
		if !c.categoryIdList.Contains(categoryItem.CategoryID) {
			continue
		}
		categoryFinalConf = append(categoryFinalConf, categoryItem)
	}

	c.categoryConf = categoryFinalConf

	knife.Set(ctx, consts.BizCategoryInfo, c)
	return c
}

func (c *CategoryInfo) GetCategoryInfo(ctx context.Context) []*proto.CategoryData {
	if c.isBuild {
		return c.categoryInfoRes
	}

	if c.renderStrategy.IsDisplay(ctx, c.req, c.productMap, c.categoryIdList) {
		var categoryIdList []int
		c.categoryInfoRes = c.renderStrategy.Render(ctx, c.categoryConf)
		for _, categoryItem := range c.categoryInfoRes {
			categoryIdList = append(categoryIdList, int(categoryItem.CategoryId))
		}
		categoryRank := make(map[int]int)
		for i, id := range categoryIdList {
			categoryRank[id] = len(categoryIdList) - 1 - i
		}
		c.categoryRank = categoryRank
	}
	c.isBuild = true
	return c.categoryInfoRes
}

// GetCategoryRank 获取category排序 越大优先级越高
func (c *CategoryInfo) GetCategoryRank(categoryId int) int {
	c.GetCategoryInfo(context.Background())
	return c.categoryRank[categoryId]
}

func (c *CategoryInfo) IsDisplay(ctx context.Context) bool {
	return c.renderStrategy.IsDisplay(ctx, c.req, c.productMap, c.categoryIdList)
}
