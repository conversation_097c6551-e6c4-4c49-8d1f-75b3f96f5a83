package side_params

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/carpool_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type SideParamsProvider interface {
	GetRecForm() int32
	GetFormStyleExp() int32
	GetSelectInfo() *models.StationInventorySelectInfo
	GetAccessKeyId() int32
	GetAppVersion() string
}

func GetSideParams(ctx context.Context, prov SideParamsProvider, estimateProducts []*biz_runtime.ProductInfoFull) map[string]int32 {
	sideParams := make(map[string]int32)
	sideParams["rec_form"] = prov.GetRecForm()
	sideParams["form_style_exp"] = prov.GetFormStyleExp()
	for _, estimateProduct := range estimateProducts {
		productInfo := estimateProduct.Product
		productBizInfo := estimateProduct.GetBizInfo()
		if productInfo != nil && productInfo.GetCarpoolType() == carpool_type.CarpoolTypeInterCityStation {
			busShiftInfo := productBizInfo.StationInventoryInfo.SelectInfo.ExtraInfo
			if busShiftInfo["is_beat_shift"] == "1" && isStationBusGuideNewAppVersion(prov) {
				sideParams["best_shift_distance"] = util.ToInt32(busShiftInfo["optimal_shift_dist"])
				sideParams["best_shift_exp_group"] = util.ToInt32(busShiftInfo["exp_group"])
			}
		}
	}

	return sideParams
}

func isStationBusGuideNewAppVersion(prov SideParamsProvider) bool {
	if util.IsNA(prov.GetAccessKeyId()) && util.CompareAppVersion(prov.GetAppVersion(), "6.9.18") >= 0 {
		return true
	}

	if util.IsMini(prov.GetAccessKeyId()) && util.CompareAppVersion(prov.GetAppVersion(), "6.9.80") >= 0 {
		return true
	}

	return false
}
