package new_order_params

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"strconv"
	"strings"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/nuwa/trace"
)

const CustomServiceFemaleDriverFirst = 114

type NewOrderParamsProvider interface {
	GetPageType() int32
	GetAvailableServiceList() []int64
	GetIsFemaleDriverFirst() int32
	GetAdditionService() string
}

// GetNewOrderParams 构建发单参数-针对预估 (返回给端, 端透传给发单)
func GetNewOrderParams(ctx context.Context, prov NewOrderParamsProvider, estimateProducts []*biz_runtime.ProductInfoFull, estimateData map[int64]*proto.V3EstimateData) map[string]string {
	params := make(map[string]string)
	// 是否为陪伴出行
	if prov.GetPageType() == page_type.PageTypeCancelEstimate {
		params["is_accompany_order"] = util.ToString(1)
	}

	// 是否命中女司机优先服务
	if isFemaleDriverFirst := getFemaleDriverFirst(ctx, prov); isFemaleDriverFirst > 0 {
		params["is_female_driver_first"] = util.ToString(isFemaleDriverFirst)
	}

	// 宠物车附加服务处理
	if prov.GetPageType() == page_type.PageTypePetCar && prov.GetAdditionService() != "" {
		params["additional_service"] = prov.GetAdditionService()
	}

	// 获取 Minos 预付费优先产品
	if designatedPcIds := getMinosPrepayFirstProducts(ctx, estimateProducts); designatedPcIds != "" {
		params["designated_pc_ids"] = designatedPcIds
	}

	// 处理揽客宝额外信息
	if lanKeBaoExtra := getLanKeBaoExtra(ctx, estimateData); lanKeBaoExtra != "" {
		params["lankebao_extra"] = lanKeBaoExtra
	}

	return params
}

// getFemaleDriverFirst 获取女司机优先设置
// todo ufs?
func getFemaleDriverFirst(ctx context.Context, prov NewOrderParamsProvider) int32 {
	if util.InArrayInt64(CustomServiceFemaleDriverFirst, prov.GetAvailableServiceList()) {
		return prov.GetIsFemaleDriverFirst()
	}

	return 0
}

type MinosPrepayConfig struct {
	MinosPrepayFirstProducts []int64 `json:"minos_prepay_first_products"`
}

// getMinosPrepayFirstProducts 获取 Minos 预付费优先产品
func getMinosPrepayFirstProducts(ctx context.Context, estimateProducts []*biz_runtime.ProductInfoFull) string {
	content := dcmp.GetDcmpContent(ctx, "config_text-minos_prepay_first", nil)
	if content == "" {
		return ""
	}

	var config MinosPrepayConfig
	err := json.Unmarshal([]byte(content), &config)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "minos prepay config json unmarshal err: %s", err.Error())
		return ""
	}

	if len(config.MinosPrepayFirstProducts) == 0 {
		return ""
	}

	// 检查当前产品中哪些在配置列表中
	var result []string
	productCategories := make(map[int64]bool)
	for _, product := range estimateProducts {
		productCategories[product.Product.ProductCategory] = true
	}

	for _, targetPcID := range config.MinosPrepayFirstProducts {
		if productCategories[targetPcID] {
			result = append(result, strconv.FormatInt(targetPcID, 10))
		}
	}

	return strings.Join(result, ",")
}

// getLanKeBaoExtra 获取揽客宝额外信息
func getLanKeBaoExtra(ctx context.Context, estimateData map[int64]*proto.V3EstimateData) string {
	lanKeBaoExtra := make(map[int64]string)

	for _, product := range estimateData {
		if product.GetExtraMap() == nil || product.GetExtraMap().GetExtraCustomFeature() == "" {
			continue
		}

		lanKeBaoExtra[product.ProductCategory] = product.GetExtraMap().GetExtraCustomFeature()
	}

	if len(lanKeBaoExtra) == 0 {
		return ""
	}

	bytes, err := json.Marshal(lanKeBaoExtra)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "lankebao extra json marshal err: %s", err.Error())
		return ""
	}

	return string(bytes)
}
