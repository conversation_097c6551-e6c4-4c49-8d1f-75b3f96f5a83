package carpool_booking_time_module

import (
	"context"
	"encoding/json"
	"fmt"
	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/smartystreets/goconvey/convey"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
	"testing"
	"time"
)

func TestIntercityCarpoolBookingTimeAnycar_Success(t *testing.T) {
	convey.Convey("Test IntercityCarpoolBookingTimeAnycar success case", t, func() {
		ctx := context.Background()

		bookingTimeSpan := &mockInterCityCarpoolBookingTimeSpan{}

		patches := gomonkey.ApplyFunc(apollo.FeatureExp, func() (bool, *model.Assignment) {
			return false, nil
		})
		patches.ApplyFunc(dcmp.GetJSONContentWithPath, func(_ context.Context, _ string, _ map[string]string, path string) string {
			switch path {
			case "title":
				return "Time Booking Title"
			case "sub_title":
				return "Time Booking Subtitle"
			case "confirm_button":
				return "Confirm Button"
			default:
				return ""
			}
		})
		patches.ApplyFunc(time.Now, func() time.Time {
			return time.Date(2025, 6, 25, 0, 0, 0, 0, time.UTC)
		})
		defer patches.Reset()

		resp := IntercityCarpoolBookingTimeAnycar(ctx, bookingTimeSpan)
		jsonStr, _ := json.Marshal(resp)
		fmt.Println(string(jsonStr))
		convey.So(resp, convey.ShouldNotBeNil)
		convey.So(resp.Title, convey.ShouldEqual, "Time Booking Title")
		convey.So(resp.Subtitle, convey.ShouldEqual, "Time Booking Subtitle")
		convey.So(resp.ButtonText, convey.ShouldEqual, "Confirm Button")
		convey.So(resp.TimeSpan, convey.ShouldNotBeEmpty)

		firstSpan := resp.TimeSpan[0]
		convey.So(firstSpan.Date, convey.ShouldEqual, "今天")
		convey.So(firstSpan.Range, convey.ShouldNotBeEmpty)

		firstRange := firstSpan.Range[0]
		convey.So(firstRange.Value, convey.ShouldEqual, "[1750834800,1750836600]")
		convey.So(firstRange.Msg, convey.ShouldEqual, "{15:00~15:30} 接您")
		convey.So(firstRange.OuterMsg, convey.ShouldEqual, "15:00~15:30")
		//convey.So(firstRange.OrderType, convey.ShouldEqual, order.OrderTypeBooking)
		convey.So(firstRange.Available, convey.ShouldBeTrue)
		convey.So(firstRange.Icon, convey.ShouldEqual, "")
	})
}

// 模拟InterCityCarpoolBookingTimeSpan接口实现
type mockInterCityCarpoolBookingTimeSpan struct {
	timeSpan []*EstimateDecision.TimeSpanV2
	skuInfo  map[string]struct {
		Status int32
		Seat   int
	}
	productCategory int64
	productId       int64
	accessKeyID     int
	version         string
	agentType       string
}

func (m *mockInterCityCarpoolBookingTimeSpan) GetTimeSpan() []*EstimateDecision.TimeSpanV2 {
	return []*EstimateDecision.TimeSpanV2{
		{
			Title: "今天",
			Date:  "06月25日",
			Range: []*EstimateDecision.SpanItem{
				{
					Label: "15:00~15:30",
					Value: "[1750834800,1750836600]",
				},
			},
		},
	}
}

func (m *mockInterCityCarpoolBookingTimeSpan) GetSkuInfo() map[string]struct {
	Status int32
	Seat   int
} {
	return map[string]struct {
		Status int32
		Seat   int
	}{
		"[1750834800,1750836600]": {
			Status: 0,
			Seat:   0,
		},
	}
}

func (m *mockInterCityCarpoolBookingTimeSpan) GetVersion() string {
	return m.version
}

func (m *mockInterCityCarpoolBookingTimeSpan) GetProductCategory() int64 {
	return m.productCategory
}

func (m *mockInterCityCarpoolBookingTimeSpan) GetProductId() int64 {
	return m.productId
}

func (m *mockInterCityCarpoolBookingTimeSpan) GetAccessKeyID() int {
	return m.accessKeyID
}

func (m *mockInterCityCarpoolBookingTimeSpan) GetAgentType() string {
	return m.agentType
}

func (m *mockInterCityCarpoolBookingTimeSpan) GetApolloParams(func(full *biz_runtime.ProductInfoFull) string, ...func(full *biz_runtime.ProductInfoFull) (string, string)) (key string, params map[string]string) {
	return "", nil
}
