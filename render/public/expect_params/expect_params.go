package expect_params

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

const (
	EtsShowType        = "ets"
	AnswerRateShowType = "answer_rate"
)

type ExpectParamsProvider interface {
	GetTabId() string
	GetAthenaFilterInfo() *AthenaApiv3.BubbleFilterInfo
	GetExpectInfo() *AthenaApiv3.AthenaBubbleExpectInfoResp
	GetEstimateStripInfo() *AthenaApiv3.EstimateStripInfo

	render.ApolloProvider
}

func GetExpectParams(ctx context.Context, prov ExpectParamsProvider) map[string]int32 {
	if tab.IsNormalTab(prov.GetTabId()) {
		return GetNormalTabExpectParams(ctx, prov)
	}

	if tab.IsClassifyTab(prov.GetTabId()) {
		return GetClassifyTabExpectParams(ctx, prov)
	}

	return nil
}

func GetNormalTabExpectParams(ctx context.Context, prov ExpectParamsProvider) map[string]int32 {
	if prov.GetAthenaFilterInfo() == nil || prov.GetAthenaFilterInfo().ExtraInfo == nil {
		return nil
	}

	switch prov.GetAthenaFilterInfo().ExtraInfo["filter_show_type"] {
	case EtsShowType:
		if prov.GetAthenaFilterInfo().Ets == nil || *prov.GetAthenaFilterInfo().Ets <= 0 {
			return nil
		}
		return map[string]int32{
			"ets": *prov.GetAthenaFilterInfo().Ets,
		}
	case AnswerRateShowType:
		if prov.GetAthenaFilterInfo().AnswerRate == nil || *prov.GetAthenaFilterInfo().AnswerRate <= 0 {
			return map[string]int32{
				"answer_rate": *prov.GetAthenaFilterInfo().AnswerRate,
			}
		}
	}

	return nil
}

func GetClassifyTabExpectParams(ctx context.Context, prov ExpectParamsProvider) map[string]int32 {
	pidKey, params := prov.GetApolloParams(biz_runtime.WithPIDKey)
	params["feature"] = "button"
	params["tab_id"] = prov.GetTabId()
	if apollo.FeatureToggle(ctx, "api_athena_expect_new_field_switch", pidKey, params) {
		return GetClassifyTabExpectParamsInfoV2(ctx, prov)
	}

	return GetClassifyTabExpectParamsInfo(ctx, prov)
}

func GetClassifyTabExpectParamsInfoV2(ctx context.Context, prov ExpectParamsProvider) map[string]int32 {
	if prov.GetExpectInfo() == nil || prov.GetExpectInfo().GlobalSceneExpect == nil ||
		prov.GetExpectInfo().GlobalSceneExpect.ExpectInfo == nil {
		return nil
	}

	expectInfo := prov.GetExpectInfo().GlobalSceneExpect.ExpectInfo
	switch prov.GetExpectInfo().GlobalSceneExpect.ShowType {
	case EtsShowType:
		if expectInfo.Ets == nil || *expectInfo.Ets <= 0 || *expectInfo.Ets > 1800 {
			return nil
		}

		return map[string]int32{
			"ets": *expectInfo.Ets,
		}

	case AnswerRateShowType:
		if expectInfo.AnswerRate == nil || *expectInfo.AnswerRate <= 0 {
			return nil
		}

		answerRate := util.Round(*expectInfo.AnswerRate*100, 0)
		return map[string]int32{
			"answer_rate": int32(answerRate),
		}
	}

	return nil
}

func GetClassifyTabExpectParamsInfo(ctx context.Context, prov ExpectParamsProvider) map[string]int32 {
	if prov.GetEstimateStripInfo() == nil || prov.GetEstimateStripInfo().GetExtraInfo() == nil {
		return nil
	}

	estimateStripInfo := prov.GetEstimateStripInfo()
	switch estimateStripInfo.ExtraInfo["button_show_type"] {
	case EtsShowType:
		if estimateStripInfo.Ets == nil || *estimateStripInfo.Ets <= 0 {
			return nil
		}

		return map[string]int32{
			"ets": *estimateStripInfo.Ets,
		}

	case AnswerRateShowType:
		if estimateStripInfo.AnswerRate == nil || *estimateStripInfo.AnswerRate <= 0 {
			return nil
		}

		return map[string]int32{
			"answer_rate": *estimateStripInfo.AnswerRate,
		}
	}

	return nil
}
