package other_data

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/category_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

// FixedCategoryIdStrategy 算法推荐策略
type FixedCategoryIdStrategy struct{}

func NewFixedCategoryIdStrategyFactory() CategoryIdStrategy {
	return &FixedCategoryIdStrategy{}
}

func (s *FixedCategoryIdStrategy) GetStrategyType() CategoryIdStrategyType {
	return CategoryIdStrategyFixed
}

func (s *FixedCategoryIdStrategy) BuildCategoryId(ctx context.Context, g []*proto.NewFormGroup, baseReqData *models.BaseReqData, productMap map[int64]*biz_runtime.ProductInfoFull) int32 {
	return baseReqData.CommonBizInfo.GroupId2CategoryId[g[0].GroupId]
}

// RecAreaCategoryIdStrategy 算法推荐策略
type RecAreaCategoryIdStrategy struct{}

func NewRecAreaCategoryIdStrategy() CategoryIdStrategy {
	return &RecAreaCategoryIdStrategy{}
}

func (s *RecAreaCategoryIdStrategy) GetStrategyType() CategoryIdStrategyType {
	return CategoryIdStrategyRecArea
}

func (s *RecAreaCategoryIdStrategy) BuildCategoryId(ctx context.Context, g []*proto.NewFormGroup, baseReqData *models.BaseReqData, productMap map[int64]*biz_runtime.ProductInfoFull) int32 {
	return category_id.RecFormCategory
}

// CategoryIdStrategyFactory CategoryId策略工厂
type CategoryIdStrategyFactory struct{}

func NewCategoryIdStrategyFactory() *CategoryIdStrategyFactory {
	return &CategoryIdStrategyFactory{}
}

// SelectCategoryIdStrategy 根据group类型选择合适的FormShowType策略
func (f *CategoryIdStrategyFactory) SelectCategoryIdStrategy(baseReqData *models.BaseReqData, isRecArea bool) CategoryIdStrategy {
	if tab.IsClassifyTab(baseReqData.CommonInfo.TabId) {
		// 推荐表单推荐区
		if isRecArea {
			return NewRecAreaCategoryIdStrategy()
		}
		// 7.0非推荐区
		return NewFixedCategoryIdStrategyFactory()
	}
	return nil
}
