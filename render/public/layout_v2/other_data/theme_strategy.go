package other_data

import (
	"context"
	"encoding/json"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	layoutConst "git.xiaojukeji.com/gulfstream/mamba/render/private/anycar_v3/layout/consts"
	"time"
)

// AthenaRecommendThemeStrategy 算法推荐策略
type AthenaRecommendThemeStrategy struct {
}

func NewAthenaRecommendThemeStrategy() ThemeStrategy {
	return &AthenaRecommendThemeStrategy{}
}

func (s *AthenaRecommendThemeStrategy) GetStrategyType() ThemeStrategyType {
	return ThemeStrategyAlgorithm
}

func (s *AthenaRecommendThemeStrategy) BuildTheme(ctx context.Context, baseReqData *models.BaseReqData) *proto.NewFormThemeData {
	// 算法推荐策略：迁移自原layout.go的构建theme_data逻辑
	if baseReqData == nil || baseReqData.CommonBizInfo.TopRec == nil {
		return nil
	}

	var (
		themeData  *proto.NewFormThemeData
		themeStyle int32
		title      string
		rightText  string
	)

	themeStyle = baseReqData.CommonBizInfo.TopRec.Style
	title = baseReqData.CommonBizInfo.TopRec.Text
	if baseReqData.CommonBizInfo.TopRec.SubText != nil {
		rightText = *baseReqData.CommonBizInfo.TopRec.SubText
	}

	apolloParam := baseReqData.GetApolloParam()
	enableNormativeTheme := apollo.FeatureToggle(ctx, "enable_formative_theme", "", apolloParam)
	switch themeStyle {
	case consts.ThemeStyleNormative:
		themeType := int32(consts.ThemeTypeNormative)
		if enableNormativeTheme {
			themeData = &proto.NewFormThemeData{
				Title:     &title,
				RightText: &rightText,
				ThemeType: &themeType,
			}
		}
	case consts.ThemeStyleDefaultSelectedCompensation:
		themeData = s.buildThemeCompensation(ctx, baseReqData)
	default:
		themeData = s.buildThemeCommonOld(ctx, baseReqData)
	}

	if themeData != nil && enableNormativeTheme {
		themeData = s.normalizeTheme(ctx, themeData)
	}

	return themeData
}

// buildThemeCompensation 构建补偿主题数据
func (s *AthenaRecommendThemeStrategy) buildThemeCompensation(ctx context.Context, baseReqData *models.BaseReqData) *proto.NewFormThemeData {
	defaultSelectedCompensation := baseReqData.CommonBizInfo.DefaultSelectedCompensation
	if defaultSelectedCompensation == nil || defaultSelectedCompensation.Contents == nil {
		return nil
	}

	tags := map[string]string{
		"compensation_time": baseReqData.CommonBizInfo.DefaultSelectedCompensation.Contents.CompensationTimeStr,
		"amount":            util.FormatPriceFloor(float64(baseReqData.CommonBizInfo.DefaultSelectedCompensation.Contents.CouponAmount)/float64(100), 1),
	}

	return &proto.NewFormThemeData{
		Title:     util.StringPtr(dcmp.GetDcmpContent(ctx, "estimate_form_v3-compensation_theme_title", tags)),
		ThemeType: &baseReqData.CommonBizInfo.TopRec.Style,
	}
}

// normalizeTheme 标准化主题数据
func (s *AthenaRecommendThemeStrategy) normalizeTheme(ctx context.Context, themeData *proto.NewFormThemeData) *proto.NewFormThemeData {
	if *themeData.ThemeType == consts.ThemeTypeMarketing {
		return themeData
	}

	var normalizedThemeData = &proto.NewFormThemeData{}
	normalizedThemeData.Title = themeData.Title
	if *themeData.ThemeType == consts.ThemeTypeCountDown {
		normalizedThemeData.RightText = themeData.RightText
		normalizedThemeData.ExpireTime = themeData.ExpireTime
	}

	conf := dcmp.GetJSONMap(ctx, "estimate_form_v3-normative_theme", util.ToString(*themeData.ThemeType))
	if len(conf) <= 0 {
		return nil
	}

	var bgGradients []string
	themeType := util.ToInt32(consts.ThemeTypeNormative)
	textColor := conf["text_color"].String()
	borderColor := conf["border_color"].String()
	disableBg := util.ToInt64(conf["disable_selected_bg"].Int())
	for _, bgGradient := range conf["outer_bg_gradients"].Array() {
		bgGradients = append(bgGradients, bgGradient.String())
	}
	leftIcon := conf["left_icon"].String()

	normalizedThemeData.ThemeType = &themeType
	normalizedThemeData.TextColor = &textColor
	normalizedThemeData.BorderColor = &borderColor
	normalizedThemeData.DisableSelectedBg = &disableBg
	normalizedThemeData.LeftIcon = &leftIcon
	normalizedThemeData.OuterBgGradients = bgGradients
	return normalizedThemeData
}

// buildThemeCommonOld 构建通用旧版主题数据
func (s *AthenaRecommendThemeStrategy) buildThemeCommonOld(ctx context.Context, baseReqData *models.BaseReqData) *proto.NewFormThemeData {
	var (
		isBargainRec bool
	)
	if baseReqData.CommonBizInfo.TopRec.Style == 0 {
		return nil
	}

	bargainSceneRec, ok := baseReqData.CommonBizInfo.TopRec.ExtraInfo["bargain_scene_recommend"]
	if ok && bargainSceneRec == "1" {
		isBargainRec = true
	}

	if isBargainRec {
		return s.buildBargainTheme(ctx, baseReqData)
	}

	if util.InArrayInt32(baseReqData.CommonBizInfo.TopRec.Style, []int32{
		consts.ThemeStyleUnbalance,
		consts.ThemeStylePreferentialMarketing,
		consts.ThemeStylePreferentialRecommendMarketing,
	}) {
		themeData := s.buildThemeStyle(ctx, baseReqData)
		if themeData != nil {
			return themeData
		}
	}

	dialogID, ok := baseReqData.CommonBizInfo.TopRec.ExtraInfo["dialog_id"]
	if ok && dialogID != "" {
		themeData := s.buildThemeByDialogId(ctx, baseReqData)
		if themeData != nil {
			return themeData
		}
	}

	return s.buildThemeFinal(ctx, baseReqData)
}

// buildBargainTheme 构建司乘议价主题数据
func (s *AthenaRecommendThemeStrategy) buildBargainTheme(ctx context.Context, baseReqData *models.BaseReqData) *proto.NewFormThemeData {
	var (
		title       string
		icon        string
		rightIcon   string
		bgGradients = make([]string, 0)
		themeType   int32
	)

	themeType = consts.ThemeTypeBargain
	// 兜底使用dcmp中的文案
	if conf := dcmp.GetJSONMap(ctx, "bargain-estimate_form_guide_info_v2", "outer"); len(conf) > 0 {
		title = conf["title"].String()
		icon = conf["left_icon"].String()
		rightIcon = conf["right_icon"].String()
		for _, bgGradient := range conf["bg_gradients"].Array() {
			bgGradients = append(bgGradients, bgGradient.String())
		}
	}
	// 使用athena的文案进行覆盖
	topRec := baseReqData.CommonBizInfo.TopRec
	if topRec.Text != "" {
		title = topRec.Text
	}

	iconLeft, ok := topRec.ExtraInfo["icon_left"]
	if ok && iconLeft != "" {
		icon = iconLeft
	}

	iconRight, ok := topRec.ExtraInfo["icon_right"]
	if ok && iconRight != "" {
		rightIcon = iconRight
	}

	return &proto.NewFormThemeData{
		Title:            &title,
		Icon:             &icon,
		RightIcon:        &rightIcon,
		OuterBgGradients: bgGradients,
		ThemeType:        &themeType,
	}
}

// buildThemeStyle 构建样式主题数据
func (s *AthenaRecommendThemeStrategy) buildThemeStyle(ctx context.Context, baseReqData *models.BaseReqData) *proto.NewFormThemeData {
	var (
		style             int32
		dialogID          int32
		themeType         int32
		selectBgGradients []string
		outBgGradients    []string
		themeData         = &proto.NewFormThemeData{}
	)

	topRec := baseReqData.CommonBizInfo.TopRec
	style = topRec.Style
	dialogIDStr, ok := topRec.ExtraInfo["dialog_id"]
	if ok {
		dialogID = util.ToInt32(dialogIDStr)
	}

	confIndex := fmt.Sprintf("%d_%d", style, dialogID)
	conf := dcmp.GetJSONMap(ctx, "estimate_form_v3-dialog_theme", confIndex)
	if len(conf) == 0 {
		return nil
	}

	rightIcon := conf["right_icon"].String()
	themeColor := conf["theme_color"].String()
	for _, selectBgGradient := range conf["selected_bg_gradients"].Array() {
		selectBgGradients = append(selectBgGradients, selectBgGradient.String())
	}
	for _, outBgGradient := range conf["out_bg_gradients"].Array() {
		outBgGradients = append(outBgGradients, outBgGradient.String())
	}

	themeData = &proto.NewFormThemeData{
		Title:               &topRec.Text,
		Icon:                &topRec.Icon,
		RightIcon:           &rightIcon,
		ThemeColor:          &themeColor,
		SelectedBgGradients: selectBgGradients,
		OuterBgGradients:    outBgGradients,
	}

	switch style {
	case consts.ThemeStyleUnbalance:
	case consts.ThemeStylePreferentialMarketing:
		themeType = 1
		break
	case consts.ThemeStylePreferentialRecommendMarketing:
		themeType = consts.ThemeTypeMarketing
		break
	}

	// 营销主题屏蔽athena返回的默认text
	if themeType == consts.ThemeTypeMarketing {
		emptyText := ""
		themeData.Title = &emptyText
	}

	if themeType == 0 {
		return nil
	}

	themeData.ThemeType = &themeType

	return themeData
}

// buildThemeByDialogId 根据dialog_id构建主题数据
func (s *AthenaRecommendThemeStrategy) buildThemeByDialogId(ctx context.Context, baseReqData *models.BaseReqData) *proto.NewFormThemeData {
	if baseReqData.CommonBizInfo.TopRec == nil || baseReqData.CommonBizInfo.TopRec.ExtraInfo == nil {
		return nil
	}

	dialogId, ok := baseReqData.CommonBizInfo.TopRec.ExtraInfo["dialog_id"]
	if !ok {
		return nil
	}

	conf := dcmp.GetJSONMap(ctx, "estimate_form_v3-dialog_theme", dialogId)
	if len(conf) <= 0 {
		return nil
	}

	var (
		themeData         *proto.NewFormThemeData
		selectBgGradients []string
		outBgGradients    []string
	)
	topRec := baseReqData.CommonBizInfo.TopRec
	themeColor := conf["theme_color"].String()
	for _, selectBgGradient := range conf["selected_bg_gradients"].Array() {
		selectBgGradients = append(selectBgGradients, selectBgGradient.String())
	}
	for _, outBgGradient := range conf["out_bg_gradients"].Array() {
		outBgGradients = append(outBgGradients, outBgGradient.String())
	}
	switch topRec.Style {
	// 0是无样式  默认1  自选车2
	case 6: // 倒计时样式
		themeData = &proto.NewFormThemeData{
			Title:               &topRec.Text,
			Icon:                &topRec.Icon,
			ThemeColor:          &themeColor,
			SelectedBgGradients: selectBgGradients,
			OuterBgGradients:    outBgGradients,
			ThemeType:           &topRec.Style,
		}
		expireTime, ok1 := topRec.ExtraInfo["expire_time"]
		if ok1 && expireTime != "" && *topRec.SubText != "" {
			layout := "2006-01-02 15:04:05"
			parsedTime, err := time.ParseInLocation(layout, expireTime, time.Local)
			if err == nil {
				expireTmp := parsedTime.Unix()
				timeDiff := expireTmp - time.Now().Unix()
				if timeDiff > 0 {
					themeData.ExpireTime = &timeDiff
					themeData.RightText = topRec.SubText
				}
			}
		}
		return themeData
	default:
		return nil
	}
}

// buildThemeFinal 构建最终主题数据
func (s *AthenaRecommendThemeStrategy) buildThemeFinal(ctx context.Context, baseReqData *models.BaseReqData) *proto.NewFormThemeData {
	var (
		selectedBgGradients layoutConst.BgGradients
		outerBgGradients    layoutConst.BgGradients
		defaultThemeType    int32
		tag                 = "buildThemeCommon"
	)
	config := apollo.GetConfig(ctx, layoutConst.PeakGroupBoxNamespace, layoutConst.NewGroupBox)

	themeColor := config["theme_color"]
	if val, ok := config["selected_bg_gradients"]; ok {
		err := json.Unmarshal([]byte(val), &selectedBgGradients)
		if err != nil {
			log.Trace.Warnf(ctx, tag, "selected_bg_gradients unmarshal fail, err:%v", err)
		}
	}
	if val, ok := config["out_bg_gradients"]; ok {
		err := json.Unmarshal([]byte(val), &outerBgGradients)
		if err != nil {
			log.Trace.Warnf(ctx, tag, "out_bg_gradients unmarshal fail, err:%v", err)
		}
	}

	topRec := baseReqData.CommonBizInfo.TopRec

	defaultThemeType = layoutConst.DefaultThemeType

	return &proto.NewFormThemeData{
		Title:      &(topRec.Text),
		Icon:       &(topRec.Icon),
		RightText:  topRec.SubText,
		RightIcon:  topRec.SubIcon,
		ThemeColor: &themeColor,
		SelectedBgGradients: []string{
			selectedBgGradients.StartColor,
			selectedBgGradients.EndColor,
		},
		ThemeType: &defaultThemeType,
		OuterBgGradients: []string{
			outerBgGradients.StartColor,
			outerBgGradients.EndColor,
		},
	}
}

// ThemeStrategyFactory Theme策略工厂
type ThemeStrategyFactory struct {
}

func NewThemeStrategyFactory() *ThemeStrategyFactory {
	return &ThemeStrategyFactory{}
}

// SelectThemeStrategy 选择合适的Theme策略
func (f *ThemeStrategyFactory) SelectThemeStrategy(baseReqData *models.BaseReqData) ThemeStrategy {
	// 推荐表单
	if tab.IsClassifyTab(baseReqData.CommonInfo.TabId) && baseReqData.CommonBizInfo.RecForm == 1 {
		return nil
	}
	return NewAthenaRecommendThemeStrategy()
}
