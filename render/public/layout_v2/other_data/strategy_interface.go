package other_data

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

// FormShowTypeStrategy FormShowType策略接口
type FormShowTypeStrategy interface {
	BuildFormShowType(ctx context.Context, group []*proto.NewFormGroup, baseReqData *models.BaseReqData) int32
	GetStrategyType() FormShowTypeStrategyType
}

// ThemeStrategy Theme策略接口
type ThemeStrategy interface {
	BuildTheme(ctx context.Context, baseReqData *models.BaseReqData) *proto.NewFormThemeData
	GetStrategyType() ThemeStrategyType
}

// PriceStrategy Price策略接口
type PriceStrategy interface {
	BuildPrice(ctx context.Context, group []*proto.NewFormGroup, baseReqData *models.BaseReqData, productMap map[int64]*biz_runtime.ProductInfoFull) (int64, float64)
	GetStrategyType() PriceStrategyType
}

// WeightStrategy Weight策略接口
type WeightStrategy interface {
	BuildWeight(ctx context.Context, layout *proto.NewFormLayout, baseReqData *models.BaseReqData, productsMap map[int64]*biz_runtime.ProductInfoFull, targetPcId int64) int64
	GetStrategyType() WeightStrategyType
}

// FoldTypeStrategy FoldType策略接口
type FoldTypeStrategy interface {
	BuildFoldType(ctx context.Context, group []*proto.NewFormGroup, baseReqData *models.BaseReqData, productMap map[int64]*biz_runtime.ProductInfoFull) int32
	GetStrategyType() FoldTypeStrategyType
}

// CategoryIdStrategy CategoryId策略接口
type CategoryIdStrategy interface {
	BuildCategoryId(ctx context.Context, group []*proto.NewFormGroup, baseReqData *models.BaseReqData, productMap map[int64]*biz_runtime.ProductInfoFull) int32
	GetStrategyType() CategoryIdStrategyType
}

// SubCategoryIdStrategy SubCategoryId策略接口
type SubCategoryIdStrategy interface {
	BuildSubCategoryId(ctx context.Context, group []*proto.NewFormGroup, baseReqData *models.BaseReqData) int32
	GetStrategyType() SubCategoryIdStrategyType
}

// FormShowTypeStrategyType FormShowType策略类型
type FormShowTypeStrategyType int

const (
	FormShowTypeStrategyAlgorithm FormShowTypeStrategyType = 1 // athena推荐、API兜底
)

// ThemeStrategyType Theme策略类型
type ThemeStrategyType int

const (
	ThemeStrategyAlgorithm ThemeStrategyType = 1 // 算法推荐
)

// PriceStrategyType Price策略类型
type PriceStrategyType int

const (
	PriceStrategyFixed PriceStrategyType = 1 // 固定策略
)

// WeightStrategyType Weight策略类型
type WeightStrategyType int

const (
	WeightStrategyClassify  WeightStrategyType = 1 // 7.0 权重策略、分框优先
	WeightStrategyRecTab    WeightStrategyType = 2 // 推荐区权重策略
	WeightStrategyPrice     WeightStrategyType = 3 // 低价优先策略
	WeightStrategyLargeFont WeightStrategyType = 4 // 大字版策略
)

// FoldTypeStrategyType FoldType策略类型
type FoldTypeStrategyType int

const (
	FoldTypeStrategyFixed FoldTypeStrategyType = 1 // 固定规则
)

// CategoryIdStrategyType CategoryId策略类型
type CategoryIdStrategyType int

const (
	CategoryIdStrategyFixed   CategoryIdStrategyType = 1 // 固定规则、API配置+athena推荐
	CategoryIdStrategyRecArea CategoryIdStrategyType = 2 // 推荐区规则
)

// SubCategoryIdStrategyType SubCategoryId策略类型
type SubCategoryIdStrategyType int

const (
	SubCategoryIdStrategyFixed SubCategoryIdStrategyType = 1 // 固定规则
)

// StrategyContext 策略上下文
type StrategyContext struct {
	BaseReqData *models.BaseReqData
	ProductsMap map[int64]*biz_runtime.ProductInfoFull
	IsRecArea   bool
}
