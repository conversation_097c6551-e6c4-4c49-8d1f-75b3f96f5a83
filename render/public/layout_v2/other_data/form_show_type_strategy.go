package other_data

import (
	"context"
	CommonConsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

// AthenaRecommendFormShowTypeStrategy 算法推荐策略
type AthenaRecommendFormShowTypeStrategy struct{}

func NewAthenaRecommendFormShowTypeStrategy() FormShowTypeStrategy {
	return &AthenaRecommendFormShowTypeStrategy{}
}

func (s *AthenaRecommendFormShowTypeStrategy) GetStrategyType() FormShowTypeStrategyType {
	return FormShowTypeStrategyAlgorithm
}

func (s *AthenaRecommendFormShowTypeStrategy) BuildFormShowType(ctx context.Context, g []*proto.NewFormGroup, baseReqData *models.BaseReqData) int32 {
	recPos := baseReqData.CommonBizInfo.GroupId2RecPos[g[0].GroupId]
	return s.athenaRecommendAreaToFormShowType(recPos)
}

func (s *AthenaRecommendFormShowTypeStrategy) athenaRecommendAreaToFormShowType(recPos int32) int32 {
	if CommonConsts.RecPosRecommendArea == recPos {
		return int32(CommonConsts.RecommendArea)
	} else if CommonConsts.RecPosTopArea == recPos || CommonConsts.RecPosTopAreaNew == recPos {
		return int32(CommonConsts.TopArea)
	} else {
		return int32(CommonConsts.OtherArea)
	}
}

// FormShowTypeStrategyFactory FormShowType策略工厂
type FormShowTypeStrategyFactory struct{}

func NewFormShowTypeStrategyFactory() *FormShowTypeStrategyFactory {
	return &FormShowTypeStrategyFactory{}
}

// SelectFormShowTypeStrategy 根据group类型选择合适的FormShowType策略
func (f *FormShowTypeStrategyFactory) SelectFormShowTypeStrategy() FormShowTypeStrategy {
	return NewAthenaRecommendFormShowTypeStrategy()
}
