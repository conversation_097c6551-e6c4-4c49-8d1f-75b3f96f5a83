package other_data

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/group_id"
	CommonConsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/model"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/category_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout_v2/group"
	"github.com/spf13/cast"
	"sort"
)

// WeightStrategyFactory Weight策略工厂
type WeightStrategyFactory struct{}

func NewWeightStrategyFactory() *WeightStrategyFactory {
	return &WeightStrategyFactory{}
}

// SelectWeightStrategy 选择Weight策略
func (f *WeightStrategyFactory) SelectWeightStrategy(baseReq *models.BaseReqData, isRecArea bool, layout *proto.NewFormLayout) WeightStrategy {
	if isRecArea {
		return NewRecTabCategoryWeightStrategy()
	}
	if baseReq.CommonInfo.FontScaleType != CommonConsts.NormalFontSize {
		return NewLargeFontStrategy()
	}
	if layout.CategoryId != nil {
		return NewClassifyWeightStrategy()
	}
	return NewPriceMinStrategy()
}

// RecTabWeightStrategy 推荐表单推荐区权重策略
type RecTabWeightStrategy struct{}

func NewRecTabCategoryWeightStrategy() WeightStrategy {
	return &RecTabWeightStrategy{}
}

func (s *RecTabWeightStrategy) GetStrategyType() WeightStrategyType {
	return WeightStrategyRecTab
}

func (s *RecTabWeightStrategy) BuildWeight(ctx context.Context, layout *proto.NewFormLayout, baseReqData *models.BaseReqData, productsMap map[int64]*biz_runtime.ProductInfoFull, targetPcId int64) int64 {
	if layout == nil || baseReqData == nil {
		return 0
	}

	weight := int64(0)

	// 获取FlowBoxIsDown状态
	flowBoxIsDown := s.getFlowBoxIsDown(ctx, baseReqData.CommonBizInfo.RecFormFeatureModel)

	// 根据category排序
	category := category_info.NewCategoryInfo(ctx, baseReqData, productsMap)
	category.GetCategoryInfo(ctx)
	categoryRank := category.GetCategoryRank(cast.ToInt(*layout.CategoryId))
	weight += int64(categoryRank) * 100000

	// 大家常选
	if layout.SubCategoryType != nil {
		weight -= int64(*layout.SubCategoryType) * 6000
	}

	// 遍历groups计算权重
	for _, g := range layout.Groups {
		if g == nil {
			continue
		}

		// 导流类型权重
		if g.Type == group.GuideThemeType {
			weight += 4000
		}
		if g.Type == group.GuideType {
			weight += 3000
		}

		// 默勾权重
		if g.IsSelected == consts.CHECK && s.isDefaultSelectUp(baseReqData.CommonBizInfo.RecFormFeatureModel) {
			weight += 2000
		}

		// FlowBox置底权重
		if flowBoxIsDown {
			groupId := group_id.NewGroupId(g.GroupId)
			if groupId.GroupType == group_id.FLOWBOXType {
				weight -= 1000
			}
		}

		// 运营位权重
		if s.isOperationPos(g, baseReqData.CommonBizInfo.RecFormFeatureModel) {
			weight -= 5000
		}

		// 产品权重
		productWeight := buildClassifyPriceWeight(ctx, productsMap, baseReqData)
		if priceWeight, exists := productWeight[targetPcId]; exists {
			weight += priceWeight
		}
	}

	return weight
}

func (s *RecTabWeightStrategy) getFlowBoxIsDown(ctx context.Context, recFormModel *model.RecFormFeatureModel) bool {
	if recFormModel == nil {
		return true
	}
	if recFormModel.RecTabFoldSubGroupDown == nil {
		return true
	}
	return *recFormModel.RecTabFoldSubGroupDown == 1
}

func (s *RecTabWeightStrategy) isDefaultSelectUp(recFormModel *model.RecFormFeatureModel) bool {
	if recFormModel == nil {
		return false
	}
	sortInfo := recFormModel.RecTabProductSortInfo
	if sortInfo == nil || sortInfo.DefaultSelectUp == nil {
		return false
	}
	return *sortInfo.DefaultSelectUp == 1
}

func (s *RecTabWeightStrategy) isOperationPos(g *proto.NewFormGroup, recFormModel *model.RecFormFeatureModel) bool {
	if recFormModel == nil {
		return false
	}
	return recFormModel.RecGroup2tags[g.GroupId] != nil
}

// ClassifyWeightStrategy 7.0表单/推荐表单非推荐tab权重策略
type ClassifyWeightStrategy struct{}

func NewClassifyWeightStrategy() WeightStrategy {
	return &ClassifyWeightStrategy{}
}

func (s *ClassifyWeightStrategy) GetStrategyType() WeightStrategyType {
	return WeightStrategyClassify
}

func (s *ClassifyWeightStrategy) BuildWeight(ctx context.Context, layout *proto.NewFormLayout, baseReqData *models.BaseReqData, productsMap map[int64]*biz_runtime.ProductInfoFull, targetPcId int64) int64 {
	if layout == nil || baseReqData == nil {
		return 0
	}

	weight := int64(0)

	// 根据category排序
	category := category_info.NewCategoryInfo(ctx, baseReqData, productsMap)
	category.GetCategoryInfo(ctx)
	categoryRank := category.GetCategoryRank(cast.ToInt(*layout.CategoryId))
	weight += int64(categoryRank) * 100000

	switch layout.FormShowType {
	case int32(CommonConsts.TopArea):
		weight += 50000
	case int32(CommonConsts.RecommendArea):
		weight += 10000
	case int32(CommonConsts.OtherArea):
		weight += 10000
	default:
		weight += 10000
	}

	weight += s.getAdditionalScoreByProductLevel(baseReqData, layout)

	// 遍历groups计算权重
	for _, g := range layout.Groups {
		if g == nil {
			continue
		}
		// 导流类型权重
		if g.Type == group.GuideType {
			weight += 2000
		}

		// 产品权重
		productWeight := buildClassifyPriceWeight(ctx, productsMap, baseReqData)
		if priceWeight, exists := productWeight[targetPcId]; exists {
			weight += priceWeight
		}
	}

	return weight
}

func (s *ClassifyWeightStrategy) getAdditionalScoreByProductLevel(baseReqData *models.BaseReqData, layout *proto.NewFormLayout) int64 {
	if baseReqData.CommonBizInfo.RecForm == 1 {
		if layout.FoldType != nil && *layout.FoldType == category_info.FoldOnlyFold {
			return -60000
		}
		return 0
	}
	groupId := group_id.NewGroupId(layout.Groups[0].GroupId)
	if groupId.GroupType == group_id.AggregationBoxType && groupId.SubGroupID == group_id.SubGroupShortDistance {
		return -10000
	}
	for _ = range layout.Groups[0].Products {
		// TODO athena TopRecScore
	}
	if layout.FoldType != nil && *layout.FoldType == category_info.FoldOnlyFold {
		return -5000
	}
	return 0
}

// LargeFontStrategy 大字版权重策略
type LargeFontStrategy struct {
	productWeight map[int64]int32
}

func NewLargeFontStrategy() WeightStrategy {
	return &LargeFontStrategy{}
}

func (s *LargeFontStrategy) GetStrategyType() WeightStrategyType {
	return WeightStrategyLargeFont
}

func (s *LargeFontStrategy) BuildWeight(ctx context.Context, layout *proto.NewFormLayout, baseReqData *models.BaseReqData, productsMap map[int64]*biz_runtime.ProductInfoFull, targetPcId int64) int64 {
	if layout == nil || baseReqData == nil {
		return 0
	}

	weight := int64(0)

	// 根据category排序
	category := category_info.NewCategoryInfo(ctx, baseReqData, productsMap)
	category.GetCategoryInfo(ctx)
	categoryRank := category.GetCategoryRank(cast.ToInt(*layout.CategoryId))
	weight += int64(categoryRank) * 100000

	// 遍历groups计算权重
	for _, g := range layout.Groups {
		if g == nil {
			continue
		}
		// 导流类型权重
		if g.Type == group.GuideThemeType {
			weight += 30000
		}
		if g.Type == group.GuideType {
			weight += 20000
		}
		// 默勾权重
		if g.IsSelected == consts.CHECK {
			weight += 10000
		}

		// 产品权重
		productWeight := buildPriceWeight(ctx, productsMap)
		if priceWeight, exists := productWeight[targetPcId]; exists {
			weight += priceWeight
		}
	}

	return weight
}

// PriceMinStrategy 价格排序权重策略
type PriceMinStrategy struct {
	productWeight map[int64]int32
}

func NewPriceMinStrategy() WeightStrategy {
	return &PriceMinStrategy{}
}

func (s *PriceMinStrategy) GetStrategyType() WeightStrategyType {
	return WeightStrategyLargeFont
}

func (s *PriceMinStrategy) BuildWeight(ctx context.Context, layout *proto.NewFormLayout, baseReqData *models.BaseReqData, productsMap map[int64]*biz_runtime.ProductInfoFull, targetPcId int64) int64 {
	if layout == nil || baseReqData == nil {
		return 0
	}

	weight := int64(0)

	switch layout.FormShowType {
	case int32(CommonConsts.TopArea):
		weight += 30000
	case int32(CommonConsts.RecommendArea):
		weight += 20000
	case int32(CommonConsts.OtherArea):
		weight += 10000
	default:
		weight += 10000
	}

	if layout.ThemeData != nil && layout.ThemeData.ThemeType != nil && *layout.ThemeData.ThemeType == CommonConsts.ThemeTypeNormative {
		weight += 10000
	}

	// 遍历groups计算权重
	for _, g := range layout.Groups {
		if g == nil {
			continue
		}
		// 导流类型权重
		if g.Type == group.GuideType {
			if layout.FormShowType == CommonConsts.OtherArea {
				weight -= 2000 // other_area 区域需要置底，且要比默勾权重更低，保持置底
			} else {
				weight += 2000 // 目前导流位如果不在 other_area 区域，就在 top_area 需要置顶，保持优先级最高
			}
		}
		// 默勾权重
		if g.IsSelected == consts.CHECK && s.isDefaultSelectUp(baseReqData.CommonBizInfo.ProductSortInfo) {
			weight += 1000
		}

		// 产品权重
		productWeight := buildPriceWeight(ctx, productsMap)
		if priceWeight, exists := productWeight[targetPcId]; exists {
			weight += priceWeight
		}
	}

	return weight
}

func (s *PriceMinStrategy) isDefaultSelectUp(sortInfo *AthenaApiv3.ProductSortInfo) bool {
	if sortInfo == nil || sortInfo.DefaultSelectUp == nil {
		return false
	}
	return *sortInfo.DefaultSelectUp == 1
}

func buildPriceWeight(ctx context.Context, productsMap map[int64]*biz_runtime.ProductInfoFull) map[int64]int64 {
	type productItem struct {
		productCategory int64
		price           float64
		reference       float64
	}

	var (
		weightMap = make(map[int64]int64, 0)
		priceList []*productItem
	)

	if len(productsMap) == 0 {
		return weightMap
	}

	for pcId, product := range productsMap {
		item := &productItem{
			productCategory: pcId,
			price:           product.GetEstimateFee(),
			reference:       product.GetEstimateFee(),
		}
		if shouldUsePersonalEstimateFee(product) {
			item.price = product.GetPersonalEstimateFee()
		}

		priceList = append(priceList, item)
	}

	sort.SliceStable(priceList, func(i, j int) bool {
		if priceList[i].price > priceList[j].price {
			return true
		} else if priceList[i].price < priceList[j].price {
			return false
		} else {
			return priceList[i].reference > priceList[j].reference
		}
	})

	for index, item := range priceList {
		weightMap[item.productCategory] = int64(index)
	}

	return weightMap
}

func buildClassifyPriceWeight(ctx context.Context, productsMap map[int64]*biz_runtime.ProductInfoFull, baseReqData *models.BaseReqData) map[int64]int64 {
	type productItem struct {
		productCategory int64
		price           float64
		reference       float64
	}

	var (
		weightMap = make(map[int64]int64, 0)
		priceList []*productItem
	)

	if len(productsMap) == 0 {
		return weightMap
	}

	for pcId, product := range productsMap {
		item := &productItem{
			productCategory: pcId,
			price:           product.GetEstimateFee(),
			reference:       product.GetEstimateFee(),
		}
		// 惠选车使用推荐价左边界参与排序
		if estimate_pc_id.EstimatePcIdHuiXuanCar == pcId {
			item.price = util.String2float64(ctx, product.GetFastRangeRecommendInfo(CommonConsts.RecommendPriceLower))
			item.reference = item.price
		} else if shouldUsePersonalEstimateFee(product) {
			item.price = product.GetPersonalEstimateFee()
		}

		priceList = append(priceList, item)
	}

	foldLogic := category_info.GetClassifyFold(ctx, baseReqData, productsMap)
	if foldLogic != nil && foldLogic.CheckCanFold(ctx) {
		inFlowBox := foldLogic.GetFinalInBoxSubGroupIdSet()
		if inFlowBox != nil {
			inFlowBox.ForEach(func(groupId string) {
				buildPriceWeight(ctx, productsMap)
			})
		}
	}

	sort.SliceStable(priceList, func(i, j int) bool {
		if priceList[i].price > priceList[j].price {
			return true
		} else if priceList[i].price < priceList[j].price {
			return false
		} else {
			return priceList[i].reference > priceList[j].reference
		}
	})

	for index, item := range priceList {
		weightMap[item.productCategory] = int64(index)
	}

	return weightMap
}

func shouldUsePersonalEstimateFee(product *biz_runtime.ProductInfoFull) bool {
	return product.GetDefaultPayType() == CommonConsts.BusinessPaymentType || product.GetDefaultPayType() == CommonConsts.BusinessPayByTeam
}
