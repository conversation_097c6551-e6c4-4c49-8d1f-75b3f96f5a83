package other_data

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/category_info"
)

type FixedFoldTypeStrategyStrategy struct{}

func NewFixedFoldTypeStrategyStrategy() FoldTypeStrategy {
	return &FixedFoldTypeStrategyStrategy{}
}

func (s *FixedFoldTypeStrategyStrategy) BuildFoldType(ctx context.Context, g []*proto.NewFormGroup, baseReqData *models.BaseReqData, productMap map[int64]*biz_runtime.ProductInfoFull) int32 {
	fold := category_info.GetClassifyFold(ctx, baseReqData, productMap)
	if fold == nil || !fold.CheckCanFold(ctx) {
		return category_info.FoldUnfold
	}
	foldType := fold.GetFoldTypeByGroupId(g[0].GroupId)

	if foldType == category_info.FoldOnlyFold {
		return category_info.FoldOnlyFold
	} else {
		return category_info.FoldUnfold
	}
}

func (s *FixedFoldTypeStrategyStrategy) GetStrategyType() FoldTypeStrategyType {
	return FoldTypeStrategyFixed
}

// FoldTypeStrategyFactory FoldType策略工厂
type FoldTypeStrategyFactory struct{}

func NewFoldTypeStrategyFactory() *FoldTypeStrategyFactory {
	return &FoldTypeStrategyFactory{}
}

// SelectFoldTypeStrategy 根据group类型选择合适的FoldType策略
func (f *FoldTypeStrategyFactory) SelectFoldTypeStrategy(baseReqData *models.BaseReqData) FoldTypeStrategy {
	// 普通非推荐7.0
	if tab.IsClassifyTab(baseReqData.CommonInfo.TabId) && baseReqData.CommonBizInfo.RecForm == 0 {
		return NewFixedFoldTypeStrategyStrategy()
	}
	return nil
}
