package other_data

import (
	"context"
	CommonConsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	PriceCheck "git.xiaojukeji.com/gulfstream/passenger-common/model/price"
	"github.com/spf13/cast"
)

// PriceStrategyFactory Price策略工厂
type PriceStrategyFactory struct {
}

func NewPriceStrategyFactory() *PriceStrategyFactory {
	return &PriceStrategyFactory{}
}

// SelectPriceStrategy 选择Price策略
func (f *PriceStrategyFactory) SelectPriceStrategy() PriceStrategy {
	return NewFixedPriceStrategy()
}

// FixedPriceStrategy 固定价格策略
type FixedPriceStrategy struct{}

func NewFixedPriceStrategy() PriceStrategy {
	return &FixedPriceStrategy{}
}

func (s *FixedPriceStrategy) GetStrategyType() PriceStrategyType {
	return PriceStrategyFixed
}

func (s *FixedPriceStrategy) BuildPrice(ctx context.Context, group []*proto.NewFormGroup, baseReqData *models.BaseReqData, productMap map[int64]*biz_runtime.ProductInfoFull) (int64, float64) {
	return BuildPrice(ctx, group, baseReqData, productMap)
}

func BuildPrice(ctx context.Context, group []*proto.NewFormGroup, baseReqData *models.BaseReqData, productMap map[int64]*biz_runtime.ProductInfoFull) (int64, float64) {
	productList := group[0].Products
	var (
		maxPrice   float64
		minPrice   float64
		maxPcID    int64
		minPcID    int64
		firstPcID  int64
		firstPrice float64
		flag       bool
	)

	if len(productList) == 0 {
		return 0, 0
	}

	minPrice = 1000000

	for _, pcIdStr := range productList {
		pcID := cast.ToInt64(pcIdStr)
		productItem := productMap[pcID]
		if productItem == nil {
			continue
		}

		var price float64

		if ShouldUsePersonalEstimateFee(productItem) {
			price = productItem.GetPersonalEstimateFee()
			PriceCheck.CheckSingle(ctx, "PriceSort", "GetPersonalEstimateFee", "PersonalEstimateFee", price)
		} else {
			price = productItem.GetEstimateFee()
			PriceCheck.CheckSingle(ctx, "PriceSort", "GetEstimateFee", "EstimateFee", price)
		}

		if !flag {
			firstPrice = price
			firstPcID = pcID
			flag = true
		}

		if maxPrice < price {
			maxPcID = pcID
			maxPrice = price
		}

		if minPrice > price {
			minPcID = pcID
			minPrice = price
		}
	}
	rankSort := cast.ToInt32(CommonConsts.RankSortFirstProduct)
	if baseReqData.CommonBizInfo.ProductSortInfo != nil && baseReqData.CommonBizInfo.ProductSortInfo.RankSort != nil {
		rankSort = *baseReqData.CommonBizInfo.ProductSortInfo.RankSort
	}
	if rankSort == CommonConsts.RankSortMinProduct {
		return minPcID, minPrice
	} else if rankSort == CommonConsts.RankSortMaxProduct {
		return maxPcID, maxPrice
	} else {
		return firstPcID, firstPrice
	}
}

func ShouldUsePersonalEstimateFee(product *biz_runtime.ProductInfoFull) bool {
	return product.GetDefaultPayType() == CommonConsts.BusinessPaymentType || product.GetDefaultPayType() == CommonConsts.BusinessPayByTeam
}
