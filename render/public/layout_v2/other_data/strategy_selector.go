package other_data

// StrategySelector 策略选择器
type StrategySelector struct {
	FormShowTypeFactory   *FormShowTypeStrategyFactory
	ThemeFactory          *ThemeStrategyFactory
	FoldTypeStrategy      *FoldTypeStrategyFactory
	CategoryIdStrategy    *CategoryIdStrategyFactory
	SubCategoryIdStrategy *SubCategoryIdStrategyFactory
	PriceFactory          *PriceStrategyFactory
	WeightFactory         *WeightStrategyFactory
}

// DefaultStrategySelector 创建策略选择器
func DefaultStrategySelector() *StrategySelector {
	return &StrategySelector{
		FormShowTypeFactory: NewFormShowTypeStrategyFactory(),
		ThemeFactory:        NewThemeStrategyFactory(),
		FoldTypeStrategy:    NewFoldTypeStrategyFactory(),
		CategoryIdStrategy:  NewCategoryIdStrategyFactory(),
		PriceFactory:        NewPriceStrategyFactory(),
		WeightFactory:       NewWeightStrategyFactory(),
	}
}

// DefaultStrategySelector 创建策略选择器
func RecLayoutStrategySelector() *StrategySelector {
	return &StrategySelector{
		FormShowTypeFactory:   NewFormShowTypeStrategyFactory(),
		ThemeFactory:          NewThemeStrategyFactory(),
		FoldTypeStrategy:      NewFoldTypeStrategyFactory(),
		CategoryIdStrategy:    NewCategoryIdStrategyFactory(),
		SubCategoryIdStrategy: NewSubCategoryIdStrategyFactory(),
		PriceFactory:          NewPriceStrategyFactory(),
		WeightFactory:         NewWeightStrategyFactory(),
	}
}
