package other_data

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

// FixedSubCategoryIdStrategy 算法推荐策略
type FixedSubCategoryIdStrategy struct{}

func NewFixedSubCategoryIdStrategyFactory() SubCategoryIdStrategy {
	return &FixedSubCategoryIdStrategy{}
}

func (s *FixedSubCategoryIdStrategy) GetStrategyType() SubCategoryIdStrategyType {
	return SubCategoryIdStrategyFixed
}

func (s *FixedSubCategoryIdStrategy) BuildSubCategoryId(ctx context.Context, g []*proto.NewFormGroup, baseReqData *models.BaseReqData) int32 {

	return baseReqData.CommonBizInfo.RecFormFeatureModel.RecGroup2SubType[g[0].GroupId]
}

// SubCategoryIdStrategyFactory SubCategoryId策略工厂
type SubCategoryIdStrategyFactory struct{}

func NewSubCategoryIdStrategyFactory() *SubCategoryIdStrategyFactory {
	return &SubCategoryIdStrategyFactory{}
}

// SelectSubCategoryIdStrategy 根据group类型选择合适的FormShowType策略
func (f *SubCategoryIdStrategyFactory) SelectSubCategoryIdStrategy(baseReqData *models.BaseReqData) SubCategoryIdStrategy {
	// 推荐表单
	if tab.IsClassifyTab(baseReqData.CommonInfo.TabId) && baseReqData.CommonBizInfo.RecForm == 1 {
		return NewFixedSubCategoryIdStrategyFactory()
	}
	return nil
}
