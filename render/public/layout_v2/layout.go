package layout_v2

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	LayoutConsts "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout_v2/group/data"
)

type Layout struct {
	BaseReqData   *models.BaseReqData
	ProductsMap   map[int64]*biz_runtime.ProductInfoFull
	EstimateData  map[int64]*LayoutConsts.SimpleEstimateData
	boxId2PcIds   map[int32][]int64
	pcId2BoxId    map[int64]int32
	pcId2Selected map[int64]int32
	guidePcIds    []int32 // 导流车型
}

func NewLayout(b *models.BaseReqData, p map[int64]*biz_runtime.ProductInfoFull, e map[int64]*LayoutConsts.SimpleEstimateData) *Layout {
	r := &Layout{
		BaseReqData:   b,
		ProductsMap:   p,
		EstimateData:  e,
		boxId2PcIds:   make(map[int32][]int64),
		pcId2BoxId:    make(map[int64]int32),
		pcId2Selected: make(map[int64]int32),
	}

	for pcId, product := range r.ProductsMap {
		r.pcId2Selected[pcId] = int32(product.GetProductCheckStatus())
		if subGroupId := product.GetSubGroupId(); subGroupId > 0 {
			boxId := subGroupId
			r.pcId2BoxId[pcId] = boxId
			if _, exists := r.boxId2PcIds[boxId]; !exists {
				r.boxId2PcIds[boxId] = make([]int64, 0)
			}
			r.boxId2PcIds[boxId] = append(r.boxId2PcIds[boxId], pcId)
		}
	}

	for _, product := range r.ProductsMap {
		r.guidePcIds = product.BaseReqData.CommonBizInfo.GuidePcIds
		break
	}

	return r
}

func (l *Layout) BuildLayout(ctx context.Context) []*proto.NewFormLayout {
	var layouts []*proto.NewFormLayout

	// 1. 先构建所有的Groups
	groups := l.buildAllGroups(ctx, false)
	if len(groups) == 0 {
		return layouts
	}

	// 2. 创建Layout包装器
	wrapper := NewLayoutWrapper()

	// 3. 将Groups包装成NewFormLayouts
	layouts = wrapper.BuildLayoutFromGroups(ctx, groups, l.BaseReqData, l.ProductsMap, false)

	// 4. 排序Layout
	layouts = l.sortLayouts(ctx, layouts)

	return layouts
}

func (l *Layout) BuildRecLayout(ctx context.Context) []*proto.NewFormLayout {
	var layouts []*proto.NewFormLayout

	// 1. 先构建所有的Groups
	groups := l.buildAllGroups(ctx, true)
	if len(groups) == 0 {
		return layouts
	}

	// 2. 创建Layout包装器
	wrapper := NewRecLayoutWrapper()

	// 3. 将Groups包装成NewFormLayouts
	layouts = wrapper.BuildLayoutFromGroups(ctx, groups, l.BaseReqData, l.ProductsMap, true)

	// 4. 排序Layout
	layouts = l.sortLayouts(ctx, layouts)

	return layouts
}

// buildAllGroups 构建所有的Groups
func (l *Layout) buildAllGroups(ctx context.Context, isRecArea bool) [][]*proto.NewFormGroup {
	groupManager := data.NewGroupBuilderManager(ctx, l.pcId2BoxId, l.boxId2PcIds, l.guidePcIds, l.BaseReqData, l.EstimateData, l.ProductsMap, l.pcId2Selected, isRecArea)

	groups, err := groupManager.BuildGroups(ctx)
	if err != nil {
		return groups
	}
	return groups
}

// sortLayouts 排序Layouts
func (l *Layout) sortLayouts(ctx context.Context, layouts []*proto.NewFormLayout) []*proto.NewFormLayout {
	if len(layouts) <= 1 {
		return layouts
	}

	for i := 0; i < len(layouts)-1; i++ {
		for j := 0; j < len(layouts)-1-i; j++ {
			if layouts[j].Weight < layouts[j+1].Weight {
				layouts[j], layouts[j+1] = layouts[j+1], layouts[j]
			}
		}
	}

	return layouts
}
