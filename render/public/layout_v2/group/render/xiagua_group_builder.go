package render

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout_v2/group"
)

// XiaguaGroupBuilder 下挂车型Group构建器
type XiaguaGroupBuilder struct {
	data *group.GroupBuilderData
}

// NewXiaguaGroupBuilder 创建下挂车型Group构建器
func NewXiaguaGroupBuilder(data *group.GroupBuilderData) *XiaguaGroupBuilder {
	return &XiaguaGroupBuilder{
		data: data,
	}
}

// GetBuilderType 获取构建器类型
func (x *XiaguaGroupBuilder) GetBuilderType() group.GroupBuilderType {
	return group.GroupBuilderTypeXiagua
}

// BuildGroup 构建下挂车型Group
func (x *XiaguaGroupBuilder) BuildGroup(ctx context.Context) *proto.NewFormGroup {
	newGroup := &proto.NewFormGroup{
		Type:        x.data.Type,
		Products:    x.data.Products,
		IsSelected:  x.data.IsSelected,
		GroupId:     x.data.GroupId,
		CarIconType: x.data.CarIconType,
	}

	x.buildXiaguaInfo(newGroup)

	return newGroup
}

func (x *XiaguaGroupBuilder) buildXiaguaInfo(newGroup *proto.NewFormGroup) {
	if x.data.XiaguaData != nil {
		newGroup.LinkInfo = x.data.XiaguaData.LinkInfo
	}
	if x.data.RecAreaData != nil {
		newGroup.RecData = x.data.RecAreaData.RecTagData
	}
}
