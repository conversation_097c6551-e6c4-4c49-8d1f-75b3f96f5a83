package render

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout_v2/group"
	"git.xiaojukeji.com/gulfstream/passenger-common/util"
)

// BoxGroupBuilder 盒子车型Group构建器
type BoxGroupBuilder struct {
	data *group.GroupBuilderData
}

// NewBoxGroupBuilder 创建盒子车型Group构建器
func NewBoxGroupBuilder(data *group.GroupBuilderData) *BoxGroupBuilder {
	return &BoxGroupBuilder{
		data: data,
	}
}

// GetBuilderType 获取构建器类型
func (b *BoxGroupBuilder) GetBuilderType() group.GroupBuilderType {
	return group.GroupBuilderTypeBox
}

// BuildGroup 构建盒子车型Group
func (b *BoxGroupBuilder) BuildGroup(ctx context.Context) *proto.NewFormGroup {
	newGroup := &proto.NewFormGroup{
		Type:        b.data.Type,
		Products:    b.data.Products,
		IsSelected:  b.data.IsSelected,
		GroupId:     b.data.GroupId,
		CarIconType: b.data.CarIconType,
	}
	b.buildBoxFields(newGroup)
	return newGroup
}

func (b *BoxGroupBuilder) buildBoxFields(newGroup *proto.NewFormGroup) {
	if b.data.BoxData != nil {
		newGroup.CarIcon = b.data.BoxData.CarIcon
		newGroup.CarTitle = b.data.BoxData.CarTitle
		newGroup.PopupTitle = util.StrPtr(b.data.BoxData.PopupTitle)
		newGroup.PopupSubTitle = util.StrPtr(b.data.BoxData.PopupSubTitle)
		newGroup.FeeMsgTemplate = util.StrPtr(b.data.BoxData.FeeMsgTemplate)
		newGroup.BoxDesc = util.StrPtr(b.data.BoxData.BoxDesc)
		newGroup.PopupToast = b.data.BoxData.PopupToast
		newGroup.FeeMsg = b.data.BoxData.FeeMsg
		newGroup.FeeDetailUrl = b.data.BoxData.FeeDetailUrl
		newGroup.FeeDescList = b.data.BoxData.FeeDescList
		newGroup.UnselectPopupTitle = b.data.BoxData.UnselectPopupTitle
		newGroup.FeeDescIcon = b.data.BoxData.FeeDescIcon
		newGroup.IsSubNodisplayEtp = b.data.BoxData.IsSubNodisplayEtp
	}
	if b.data.RecAreaData != nil {
		newGroup.RecData = b.data.RecAreaData.RecTagData
	}
}
