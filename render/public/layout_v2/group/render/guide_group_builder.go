package render

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout_v2/group"
	"git.xiaojukeji.com/gulfstream/passenger-common/util"
)

// GuideGroupBuilder 导流车型Group构建器
type GuideGroupBuilder struct {
	data *group.GroupBuilderData
}

// NewGuideGroupBuilder 创建导流车型Group构建器
func NewGuideGroupBuilder(data *group.GroupBuilderData) *GuideGroupBuilder {
	return &GuideGroupBuilder{
		data: data,
	}
}

// GetBuilderType 获取构建器类型
func (g *GuideGroupBuilder) GetBuilderType() group.GroupBuilderType {
	return group.GroupBuilderTypeGuide
}

// BuildGroup 构建导流车型Group
func (g *GuideGroupBuilder) BuildGroup(ctx context.Context) *proto.NewFormGroup {
	newGroup := &proto.NewFormGroup{
		Type:        g.data.Type,
		Products:    g.data.Products,
		IsSelected:  0,
		GroupId:     g.data.GroupId,
		CarIconType: g.data.CarIconType,
	}

	g.buildGuideInfo(newGroup)

	return newGroup
}

func (g *GuideGroupBuilder) buildGuideInfo(newGroup *proto.NewFormGroup) {
	if g.data.GuideData != nil {
		newGroup.ButtonText = util.StrPtr(g.data.GuideData.ButtonText)
		newGroup.ActionType = util.Int32Ptr(g.data.GuideData.ActionType)
		newGroup.DisableShadow = g.data.GuideData.DisableShadow
		newGroup.JumpType = util.Int32Ptr(g.data.GuideData.JumpType)
		newGroup.GuidePath = util.StrPtr(g.data.GuideData.GuidePath)
		newGroup.FeeDetailUrl = g.data.GuideData.FeeDetailUrl
		newGroup.GuideStyle = g.data.GuideData.GuideStyle
		newGroup.GuideParams = g.data.GuideData.GuideParams
		newGroup.OmgData = g.data.GuideData.OmgData
		newGroup.ButtonParams = g.data.GuideData.ButtonParams
	}
	if g.data.RecAreaData != nil {
		newGroup.RecData = g.data.RecAreaData.RecTagData
	}
}
