package render

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout_v2/group"
)

// SingleGroupBuilder 单车型Group构建器
type SingleGroupBuilder struct {
	data *group.GroupBuilderData
}

// NewSingleGroupBuilder 创建单车型Group构建器
func NewSingleGroupBuilder(data *group.GroupBuilderData) *SingleGroupBuilder {
	return &SingleGroupBuilder{
		data: data,
	}
}

// GetBuilderType 获取构建器类型
func (s *SingleGroupBuilder) GetBuilderType() group.GroupBuilderType {
	return group.GroupBuilderTypeSingle
}

// BuildGroup 构建单车型Group
func (s *SingleGroupBuilder) BuildGroup(ctx context.Context) *proto.NewFormGroup {

	newGroup := &proto.NewFormGroup{
		Type:        s.data.Type,
		Products:    s.data.Products,
		IsSelected:  s.data.IsSelected,
		GroupId:     s.data.GroupId,
		CarIconType: s.data.CarIconType,
		StyleType:   nil, // TODO 下线？
	}
	s.buildSingleExtraInfo(newGroup)
	return newGroup
}

func (s *SingleGroupBuilder) buildSingleExtraInfo(newGroup *proto.NewFormGroup) {
	if s.data.SingleData != nil {
		newGroup.BargainPopup = s.data.SingleData.BargainPopup
		newGroup.BargainMsg = s.data.SingleData.BargainMsg
		newGroup.FeeDetailUrl = s.data.SingleData.FeeDetailUrl
	}
	if s.data.RecAreaData != nil {
		newGroup.RecData = s.data.RecAreaData.RecTagData
	}
}
