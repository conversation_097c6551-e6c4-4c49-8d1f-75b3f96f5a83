package data

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v4_multi/experiment"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	feeConsts "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	util2 "git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/util"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout_v2/group"
	"github.com/spf13/cast"
	"math"
)

func (m *GroupBuilderManager) buildXiaguaData(ctx context.Context, linkPcId int64, product *biz_runtime.ProductInfoFull) *group.XiaguaData {
	data := &group.XiaguaData{}
	dcmpKey := "estimate_new_form-spacious_car_alliance"
	tagIcon := dcmp.GetJSONContentWithPath(ctx, dcmpKey, nil, "tag_icon")
	feeMsgKey := "fee_msg"
	feeMsgCapPriceKey := "fee_msg_cap_price"
	if experiment.GetNewStyleFormABParamFromBaseReqData(ctx, m.baseReqData).IsHitNewStyleForm() {
		tagIcon = ""
		feeMsgKey = "fee_msg_new"
		feeMsgCapPriceKey = "fee_msg_new_cap_price"
	}
	linkInfo := &proto.NewFormLinkInfo{
		SelectStyle: 1,
		IsStrength:  1,
		LinkProduct: cast.ToString(linkPcId),
		IsSelected:  m.pcId2Selected[linkPcId],
		PreferData: &proto.NewFormPreferData{
			Desc: dcmp.GetJSONContentWithPath(ctx, dcmpKey, nil, "desc"),
			TagList: []*proto.NewFormPreferDataTag{
				{
					Icon:    tagIcon,
					Content: dcmp.GetJSONContentWithPath(ctx, dcmpKey, nil, "tag_content"),
				},
			},
		},
	}
	linkInfo.InfoUrl = m.replaceGuildPath(ctx, dcmp.GetJSONContentWithPath(ctx, dcmpKey, nil, "info_url"), product)
	estimateFee := product.GetEstimateFee()
	deductFee := product.GetMixedDeductPrice()
	if deductFee > 0 {
		estimateFee = math.Max(0, estimateFee-deductFee)
	}
	key, params := product.GetApolloParams(biz_runtime.WithPIDKey)
	if product.GetCapPrice() > 0 {
		estimateFeeFinal := util2.PriceFormat(ctx, params, key, estimateFee, consts.FeeTypeCapPrice)
		linkInfo.FeeMsg = util.ReplaceTag(ctx, dcmp.GetJSONContentWithPath(ctx, dcmpKey, nil, feeMsgCapPriceKey), map[string]string{
			"fee_amount": cast.ToString(estimateFeeFinal),
		})
	} else {
		estimateFeeFinal := util2.PriceFormat(ctx, params, key, estimateFee, consts.FeeTypeDefault)
		linkInfo.FeeMsg = util.ReplaceTag(ctx, dcmp.GetJSONContentWithPath(ctx, dcmpKey, nil, feeMsgKey), map[string]string{
			"fee_amount": cast.ToString(estimateFeeFinal),
		})
	}
	linkInfo.FeeDescList = m.getFeeDescList(ctx, []int64{linkPcId}, []int32{feeConsts.TypeDynamic, feeConsts.TypeIncrement, feeConsts.TypeDecrement, feeConsts.TypeBusinessPay, feeConsts.TypeZiyoubao})
	data.LinkInfo = linkInfo
	return data
}
