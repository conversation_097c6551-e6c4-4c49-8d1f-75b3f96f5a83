package data

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/category_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/group_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/level_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/order"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/taxi"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v4_multi/experiment"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	feeConsts "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	feeEngineConsts "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout_v2/group"
	"github.com/spf13/cast"
)

var BoxDataDcmpKey = map[int32]string{
	group_id.SubGroupShortDistance:      "estimate_new_form-short_distance_box",
	group_id.SubGroupIdTaxi:             "estimate_new_form-taxi_box",
	group_id.SubGroupIdTaxiPricing:      "estimate_new_form-taxi_pricing_box",
	group_id.SubGroupIdFastMustCheaper:  "estimate_new_form-fast_must_cheaper_box",
	group_id.SubGroupIdXDiscount:        "estimate_new_form-x_discount_box",
	group_id.SubGroupIdDiscountAlliance: "estimate_new_form-discount_alliance_box",
	group_id.SubGroupIdTaxiChaoZhiDa:    "estimate_new_form-chao_zhi_da_box",
}

var TaxiBoxCarTitleInCity = "estimate_new_form_taxi_box_car_title_city"

type BoxDcmpTemplate struct {
	CarIcon            string  `json:"car_icon"`
	CarTitle           string  `json:"car_title"`
	BoxDesc            string  `json:"box_desc"`
	FeeMsgTemplate     string  `json:"fee_msg_template"`
	PopupTitle         string  `json:"popup_title"`
	PopupSubTitle      string  `json:"popup_sub_title"`
	FeeMsg             *string `json:"fee_msg,omitempty"`
	UnselectPopupTitle *string `json:"unselect_popup_title,omitempty"`
	PopupToast         *string `json:"popup_toast,omitempty"`
	FontScaleBoxDesc   *string `json:"font_scale_box_desc,omitempty"`
	FeeDetailUrl       *string `json:"fee_detail_url,omitempty"`
	FeeDescIcon        *string `json:"fee_desc_icon,omitempty"`
	FeeDesc            *string `json:"fee_desc,omitempty"`
	RedPacket          *string `json:"red_packet,omitempty"`
}

func (m *GroupBuilderManager) buildBoxData(ctx context.Context, atheneType int, boxId int32) *group.BoxData {
	if atheneType == group_id.FLOWBOXType {
		return m.buildFlowBoxData(ctx, boxId)
	}
	data := &group.BoxData{}
	boxTemplate := &BoxDcmpTemplate{}
	if dcmpKey, ok := BoxDataDcmpKey[boxId]; ok {
		err := json.Unmarshal([]byte(dcmp.GetDcmpPlainContent(ctx, dcmpKey)), boxTemplate)
		if err != nil {
			data.CarTitle = boxTemplate.CarTitle
			data.CarIcon = boxTemplate.CarIcon
			data.PopupTitle = boxTemplate.PopupTitle
			data.BoxDesc = boxTemplate.BoxDesc
			data.FeeMsgTemplate = boxTemplate.FeeMsgTemplate
			data.UnselectPopupTitle = boxTemplate.UnselectPopupTitle
			data.FeeDescIcon = boxTemplate.FeeDescIcon
			data.PopupToast = boxTemplate.PopupToast
		}
	}
	count := len(m.boxId2PcId)
	data.PopupSubTitle = util.ReplaceTag(ctx, boxTemplate.FeeMsgTemplate, map[string]string{
		"num": cast.ToString(count),
	})
	data.BoxDesc = util.ReplaceTag(ctx, boxTemplate.BoxDesc, map[string]string{
		"num": cast.ToString(count),
	})
	data.FeeMsgTemplate = boxTemplate.FeeMsgTemplate
	if !experiment.GetNewStyleFormABParamFromBaseReqData(ctx, m.baseReqData).IsHitNewStyleForm() {
		data.FeeMsgTemplate = ""
	}
	m.buildBoxExtraData(ctx, boxId, boxTemplate, data)
	return data
}

func (m *GroupBuilderManager) buildFlowBoxData(ctx context.Context, categoryId int32) *group.BoxData {
	data := &group.BoxData{}
	flowBoxConfig := make(map[string]string)
	err := json.Unmarshal([]byte(dcmp.GetDcmpPlainContent(ctx, "estimate_new_form-flow_box")), &flowBoxConfig)
	if err != nil {
		data.CarTitle = flowBoxConfig["car_title_"+cast.ToString(categoryId)]
		data.CarIcon = flowBoxConfig["car_icon_"+cast.ToString(categoryId)]
		data.PopupTitle = flowBoxConfig["popup_title"]
		data.UnselectPopupTitle = util.StringPtr(flowBoxConfig["unselect_popup_title"])
		data.PopupSubTitle = flowBoxConfig["popup_sub_title"]
		data.BoxDesc = flowBoxConfig["box_desc_"+cast.ToString(categoryId)]
		data.FeeDescList = m.getFeeDescList(ctx, m.flowBoxAllPcIds[categoryId], []int32{feeConsts.TypeDynamic, feeConsts.TypeIncrement, feeConsts.TypeDecrement, feeConsts.TypeBusinessPay, feeConsts.TypeZiyoubao})
	}
	data.FeeMsgTemplate = flowBoxConfig["fee_msg_template"]
	if !experiment.GetNewStyleFormABParamFromBaseReqData(ctx, m.baseReqData).IsHitNewStyleForm() {
		data.FeeMsgTemplate = ""
	}
	if m.isTaxiInRecArea(m.flowBoxAllPcIds[category_id.RecFormCategory]) {
		data.CarIcon = flowBoxConfig["car_icon_taxi_in_rec_area"]
	}
	return data
}
func (m *GroupBuilderManager) isTaxiInRecArea(pcIds []int64) bool {
	if !m.isRecArea {
		return false
	}
	for _, pcId := range pcIds {
		if m.productsMap[pcId].Product.GetProductId() == product_id.ProductIdUnitaxi {
			return true
		}
	}
	return false
}

func (m *GroupBuilderManager) buildBoxExtraData(ctx context.Context, boxId int32, template *BoxDcmpTemplate, data *group.BoxData) {
	checkPcIds := make([]int64, 0)
	var randomPcId int64
	var finalFeeDescPcIds []int64
	var taxiOnlinePricingPcIds []int64
	var redPacket float64
	for _, pcId := range m.boxId2PcId[boxId] {
		if m.pcId2Selected[pcId] == 1 {
			checkPcIds = append(checkPcIds, pcId)
		}
		if m.productsMap[pcId].GetLevelType() == level_type.OnLinePricingLevelType {
			taxiOnlinePricingPcIds = append(taxiOnlinePricingPcIds, pcId)
		}
		if displayLines := m.productsMap[pcId].GetBillDisplayLines(); displayLines != nil {
			for _, item := range displayLines {
				if item.Name == "red_packet" && item.Value > 0.0 {
					redPacket = item.Value
				}
			}
		}
		randomPcId = pcId
	}
	if boxId == group_id.SubGroupIdXDiscount {
		finalFeeDescPcIds = m.boxId2PcId[boxId]
	}
	if boxId == group_id.SubGroupIdTaxiChaoZhiDa || boxId == group_id.SubGroupIdFastMustCheaper {
		finalFeeDescPcIds = []int64{randomPcId}
		data.IsSubNodisplayEtp = util.Int32Ptr(1)
		data.FeeMsg = util.StringPtr(util.ReplaceTag(ctx, *template.FeeMsg, map[string]string{
			"fee_amount": cast.ToString(m.productsMap[randomPcId].GetEstimateFee()),
		}))
		data.FeeDetailUrl = util.StringPtr(util.ReplaceTag(ctx, *template.FeeDetailUrl, map[string]string{
			"eid": m.productsMap[randomPcId].GetEstimateID(),
		}))
	}
	if boxId == group_id.SubGroupShortDistance || boxId == group_id.SubGroupIdDiscountAlliance {
		if len(checkPcIds) > 0 {
			finalFeeDescPcIds = checkPcIds
		} else {
			finalFeeDescPcIds = m.boxId2PcId[boxId]
		}
		data.IsSubNodisplayEtp = util.Int32Ptr(1)
		if redPacket > 0 {
			data.FeeDescIcon = util.StringPtr("")
			data.FeeDesc = template.FeeDesc
		}
	}
	if boxId == group_id.SubGroupIdTaxi {
		carTitle := dcmp.GetJSONContentWithPath(ctx, TaxiBoxCarTitleInCity, nil, cast.ToString(m.baseReqData.AreaInfo.Area))
		if carTitle != "" {
			data.CarTitle = carTitle
		}
		if len(checkPcIds) > 0 {
			finalFeeDescPcIds = checkPcIds
		} else {
			finalFeeDescPcIds = m.boxId2PcId[boxId]
		}
	}
	if boxId == group_id.SubGroupIdTaxiPricing {
		// TODO icon title
		if len(checkPcIds) == 1 {
			finalFeeDescPcIds = checkPcIds
		} else {
			if m.hitTaxiPricingExp(ctx, randomPcId) {
				finalFeeDescPcIds = m.boxId2PcId[boxId]
			} else {
				finalFeeDescPcIds = taxiOnlinePricingPcIds
			}
		}
	}
	data.FeeDescList = m.getFeeDescList(ctx, finalFeeDescPcIds, []int32{feeConsts.TypeDynamic, feeConsts.TypeIncrement, feeConsts.TypeDecrement, feeConsts.TypeBusinessPay, feeConsts.TypeZiyoubao})
	if m.baseReqData.CommonInfo.FontScaleType != consts.NormalFontSize && template.FontScaleBoxDesc != nil {
		data.BoxDesc = *template.FontScaleBoxDesc
	}
}

func (m *GroupBuilderManager) hitTaxiPricingExp(ctx context.Context, pcId int64) bool {
	if m.baseReqData == nil {
		return false
	}

	if m.baseReqData.PassengerInfo.UID == 0 {
		return false
	}

	if m.baseReqData.CommonInfo.OrderType != order.OrderTypeNow {
		return false
	}

	key, params := m.productsMap[pcId].ApolloParamsGen(apollo_model.WithUIDKey)
	params["county_id"] = util.ToString(m.baseReqData.AreaInfo.FromCounty)
	params["order_type"] = util.ToString(m.baseReqData.CommonInfo.OrderType)
	params["lang"] = m.baseReqData.CommonInfo.Lang
	params["tab_id"] = m.baseReqData.CommonInfo.TabId

	return taxi.UsingNewBox(ctx, key, params)
}

func (m *GroupBuilderManager) getFeeDescList(ctx context.Context, checkPCIDs []int64, feeTypes []int32) []*proto.NewFormFeeDesc {
	var (
		res     = make([]*proto.NewFormFeeDesc, 0)
		descMax = make(map[int32]*proto.NewFormFeeDesc)
	)

	for _, pcID := range checkPCIDs {
		if len(m.estimateData) == 0 {
			return res
		}
		if estimateData, ok := m.estimateData[pcID]; ok && estimateData != nil {
			feeDescMaxList := m.distinguishFeeDesc(estimateData.FeeDescList)
			for feeType, curDescMax := range feeDescMaxList {
				if descMax[feeType] != nil {
					if curDescMax.Amount > descMax[feeType].Amount {
						descMax[feeType] = curDescMax
					}
				} else {
					descMax[feeType] = curDescMax
				}
			}
		}
	}

	for _, feeType := range feeTypes {
		if Max := descMax[feeType]; Max != nil && len(res) < 2 {
			if len(checkPCIDs) > 1 {
				Max = m.renderRewrite(ctx, Max, feeType)
			}

			res = append(res, Max)
		}
	}

	return res
}

func (m *GroupBuilderManager) distinguishFeeDesc(feeDescList []*proto.NewFormFeeDesc) map[int32]*proto.NewFormFeeDesc {
	res := make(map[int32]*proto.NewFormFeeDesc)

	for _, feeDesc := range feeDescList {
		if maxFee, ok := res[feeDesc.Type]; ok {
			if maxFee.Amount < feeDesc.Amount {
				res[feeDesc.Type] = feeDesc
			}
		} else {
			res[feeDesc.Type] = feeDesc
		}
	}

	return res
}

func (m *GroupBuilderManager) renderRewrite(ctx context.Context, max *proto.NewFormFeeDesc, feeType int32) *proto.NewFormFeeDesc {
	path := ""
	tag := map[string]string{
		"num": util.FormatPrice(max.Amount, 2),
	}

	if feeType == feeEngineConsts.TypeBusinessPay {
		path = feeEngineConsts.FeeBusinessMaxAnyCar
	} else if feeType == feeEngineConsts.TypeIncrement {
		path = feeEngineConsts.FeeIncrMaxAnyCar
	} else if feeType == feeEngineConsts.TypeDecrement {
		path = feeEngineConsts.FeeDecrMaxAnyCar
	} else if feeType == feeEngineConsts.TypeDynamic {
		path = feeEngineConsts.FeeDynamicMaxAnyCar
	} else {
		return max
	}

	if conf := dcmp.GetJSONMap(ctx, feeEngineConsts.DynamicBoxFeeDetailInfo, path); len(conf) > 0 {
		// 如果是出租车动调信息则只需要替换icon
		if path == feeEngineConsts.FeeDynamicMaxAnyCar {
			return &proto.NewFormFeeDesc{
				BorderColor:    max.BorderColor,
				Content:        max.Content,
				Icon:           conf["icon"].String(),
				Amount:         max.Amount,
				Type:           max.Type,
				TextColor:      max.TextColor,
				BgColor:        max.BgColor,
				HighlightColor: max.HighlightColor,
			}
		}
		return &proto.NewFormFeeDesc{
			BorderColor: conf["border_color"].String(),
			Content:     util.ReplaceTag(ctx, conf["content"].String(), tag),
			Icon:        conf["icon"].String(),
		}
	}
	return nil
}
