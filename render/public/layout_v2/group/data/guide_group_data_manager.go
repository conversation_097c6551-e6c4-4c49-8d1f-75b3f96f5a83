package data

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/carpool_type"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	EstimateV4 "git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v4/data"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/estimate_v4"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout_v2/group"
	"github.com/spf13/cast"
	"time"
)

var GuideDataPcIdDcmpKey = map[int64]string{
	estimate_pc_id.EstimatePcIdDaiJia:              "estimate_new_form-daijia_guide",
	estimate_pc_id.EstimatePcIdLowPriceCarpool:     "estimate_new_form-pincheche_guide",
	estimate_pc_id.EstimatePcIdCarpoolSFCar:        "estimate_new_form-sfc_guide",
	estimate_pc_id.EstimatePcIdCarpoolCrossSFCar:   "estimate_new_form-sfc_guide",
	estimate_pc_id.EstimatePcIdAutoDriving:         "estimate_new_form-auto_driver",
	estimate_pc_id.EstimatePcIdTrainTicket:         "estimate_new_form-train_guide",
	estimate_pc_id.EstimatePcIdPetFastCar:          "estimate_new_form-pet_guide",
	estimate_pc_id.EstimatePcIdEngageCar:           "estimate_new_form-engage_guide",
	estimate_pc_id.EstimatePcIdCaoCaoAccessibility: "estimate_new_form-accessibility_guide",
	estimate_pc_id.EstimatePcIdGoodsTransport:      "estimate_new_form-goods_transport",
	estimate_pc_id.EstimatePcIdHuiXuanCar:          "estimate_new_form-bargain_range",
}

var GuideDataCarpoolTypeDcmpKey = map[int64]string{
	carpool_type.CarpoolTypeInterCity:        "estimate_new_form-intercity_guide",
	carpool_type.CarpoolTypeInterCityStation: "estimate_new_form-intercity_station_guide",
	carpool_type.CarpoolTypeSmartCarpool:     "estimate_new_form-smart_carpool_guide",
}

type GuideDcmpTemplate struct {
	ButtonText   string  `json:"button_text"`
	ActionType   int32   `json:"action_type"`
	JumpType     int32   `json:"jump_type"`
	NaGuidePath  string  `json:"na_guide_path"`
	XcxGuidePath string  `json:"xcx_guide_path"`
	FeeDetailUrl *string `json:"fee_detail_url,omitempty"`
	Dchn         *string `json:"dchn,omitempty"`
	TabId        *string `json:"tab_id,omitempty"`
	ButtonParams *string `json:"button_params,omitempty"`
}

type EngageCarGuideParam struct {
	Lat         float64 `json:"lat"`
	Lng         float64 `json:"lng"`
	CityId      int32   `json:"city_id"`
	CityName    string  `json:"city_name"`
	DisplayName string  `json:"displayname"`
	ContactTel  string  `json:"contact_tel"`
	Address     string  `json:"address"`
	PoiId       string  `json:"poi_id"`
}

type TrainGuideParam struct {
	FromCity map[string]any `json:"from_city"`
	ToCity   map[string]any `json:"to_city"`
	Date     string         `json:"date"`
}

func (m *GroupBuilderManager) buildGuideData(ctx context.Context, pcId int64, product *biz_runtime.ProductInfoFull) *group.GuideData {
	data := &group.GuideData{
		ButtonText: "去呼叫",
	}
	guideTemplate := &GuideDcmpTemplate{}
	dcmpKey := GuideDataCarpoolTypeDcmpKey[product.Product.CarpoolType]
	if dcmpKey == "" {
		dcmpKey = GuideDataPcIdDcmpKey[pcId]
	}
	if dcmpKey != "" {
		err := json.Unmarshal([]byte(dcmp.GetDcmpPlainContent(ctx, dcmpKey)), guideTemplate)
		if err != nil {
			data.ButtonText = guideTemplate.ButtonText
			data.ActionType = guideTemplate.ActionType
			data.JumpType = guideTemplate.JumpType
			data.FeeDetailUrl = guideTemplate.FeeDetailUrl
		}
		if m.isXcx {
			data.GuidePath = m.replaceGuildPath(ctx, guideTemplate.XcxGuidePath, product)
		} else if m.isNA {
			data.GuidePath = m.replaceGuildPath(ctx, guideTemplate.NaGuidePath, product)
		}
	}
	if m.baseReqData.CommonBizInfo.TopRec != nil && m.baseReqData.CommonBizInfo.TopRec.Style == consts.ThemeStyleNormative {
		data.DisableShadow = util.Int32Ptr(1)
	}
	m.buildGuideExtraData(ctx, pcId, product, guideTemplate, data)
	return data
}

func (m *GroupBuilderManager) replaceGuildPath(ctx context.Context, str string, product *biz_runtime.ProductInfoFull) string {
	return util.ReplaceTag(ctx, str, map[string]string{
		"lang":             m.baseReqData.CommonInfo.Lang,
		"access_key_id":    cast.ToString(m.baseReqData.CommonInfo.AccessKeyID),
		"app_version":      m.baseReqData.CommonInfo.AppVersion,
		"token":            m.baseReqData.PassengerInfo.Token,
		"estimate_id":      product.GetEstimateID(),
		"product_category": cast.ToString(product.GetProductCategory()),
		"from_address":     m.baseReqData.AreaInfo.FromAddress,
		"to_address":       m.baseReqData.AreaInfo.ToAddress,
		"from_name":        m.baseReqData.AreaInfo.FromName,
		"to_name":          m.baseReqData.AreaInfo.ToName,
		"from_area":        cast.ToString(m.baseReqData.AreaInfo.Area),
		"to_area":          cast.ToString(m.baseReqData.AreaInfo.ToArea),
		"from_city_name":   m.baseReqData.AreaInfo.FromCityName,
		"to_city_name":     m.baseReqData.AreaInfo.ToCityName,
		"from_lat":         cast.ToString(m.baseReqData.AreaInfo.FromLat),
		"from_lng":         cast.ToString(m.baseReqData.AreaInfo.FromLng),
		"to_lat":           cast.ToString(m.baseReqData.AreaInfo.ToLat),
		"to_lng":           cast.ToString(m.baseReqData.AreaInfo.ToLng),
		"current_lat":      cast.ToString(m.baseReqData.AreaInfo.CurLat),
		"current_lng":      cast.ToString(m.baseReqData.AreaInfo.CurLng),
		"from_poi_id":      m.baseReqData.AreaInfo.FromPoiID,
		"to_poi_id":        m.baseReqData.AreaInfo.ToPoiID,
		"trace_id":         util.GetTraceIDFromCtxWithoutCheck(ctx),
	})
}

func (m *GroupBuilderManager) buildGuideExtraData(ctx context.Context, pcId int64, product *biz_runtime.ProductInfoFull, template *GuideDcmpTemplate, data *group.GuideData) {
	// 城际
	if carpool_type.CarpoolTypeInterCity == product.Product.CarpoolType {
		m.buildGuideIntercityExtra(ctx, template, data, product)
	}
	// 站点巴士
	if carpool_type.CarpoolTypeInterCityStation == product.Product.CarpoolType {
		m.buildGuideStationExtra(ctx, template, data, product)
	}
	// 智能小巴
	if carpool_type.CarpoolTypeSmartCarpool == product.Product.CarpoolType {
		m.buildGuideSmartCarpoolExtra(ctx, template, data)
	}
	// 拼成乐
	if estimate_pc_id.EstimatePcIdLowPriceCarpool == pcId {
		m.buildGuidePinCheCheExtra(ctx, template, data)
	}
	// 顺风车
	if estimate_pc_id.EstimatePcIdCarpoolSFCar == pcId || estimate_pc_id.EstimatePcIdCarpoolCrossSFCar == pcId {
		m.buildGuideSFCExtra(ctx, data)
	}
	// 自动驾驶
	if estimate_pc_id.EstimatePcIdAutoDriving == pcId {
		m.buildGuideAutoDriverExtra(ctx, product, data)
	}
	// 火车
	if estimate_pc_id.EstimatePcIdTrainTicket == pcId {
		m.buildGuideTrainExtra(product, data)
	}
	// 宠物
	if estimate_pc_id.EstimatePcIdPetFastCar == pcId {
		m.buildGuidePetExtra(data, template)
	}
	// 包车
	if estimate_pc_id.EstimatePcIdEngageCar == pcId {
		m.BuildGuideEngageExtra(ctx, template, data)
	}
	// 无障碍
	if estimate_pc_id.EstimatePcIdCaoCaoAccessibility == pcId {
		m.buildGuideAccessibilityExtra(ctx, template, data)
	}
	// 货运
	if estimate_pc_id.EstimatePcIdGoodsTransport == pcId {
		m.buildGuideGoodsTransportExtra(data, pcId, product)
	}
	// 惠选车
	if estimate_pc_id.EstimatePcIdHuiXuanCar == pcId {
		m.buildGuideBargainRangeExtra(ctx, data, product)
	}
}

func (m *GroupBuilderManager) buildGuideBargainRangeExtra(ctx context.Context, data *group.GuideData, product *biz_runtime.ProductInfoFull) {
	fastCarFee := ""
	spFastCarFee := ""
	if fastCar, ok := m.productsMap[estimate_pc_id.EstimatePcIdFastCar]; ok {
		fastCarFee = cast.ToString(fastCar.GetEstimateFee())
	}
	if spFastCar, ok := m.productsMap[estimate_pc_id.EstimatePcIdSpecialRate]; ok {
		fastCarFee = cast.ToString(spFastCar.GetEstimateFee())
	} else if spFastCar, ok = m.productsMap[estimate_pc_id.EstimatePcIdFastSpecialRate]; ok {
		fastCarFee = cast.ToString(spFastCar.GetEstimateFee())
	}
	data.ButtonParams = map[string]string{
		"estimate_id":              product.GetEstimateID(),
		"estimate_trace_id":        util.GetTraceIDFromCtxWithoutCheck(ctx),
		"fast_car_estimate_fee":    fastCarFee,
		"sp_fast_car_estimate_fee": spFastCarFee,
		//"basic_fee":                product.GetBargainRecommendInfo()., TODO
	}
}

func (m *GroupBuilderManager) buildGuideGoodsTransportExtra(data *group.GuideData, pcId int64, product *biz_runtime.ProductInfoFull) {
	data.GuideStyle = util.Int32Ptr(1)
	dchn := "&dchn=wycfenkuangtab"
	if m.topPcIds.Contains(pcId) {
		dchn = "&dchn=wycdaoliuwei"
	}
	data.GuidePath = data.GuidePath + "&hyEstimateProperties=" + product.GetExternalEid() + dchn
}

func (m *GroupBuilderManager) buildGuideAccessibilityExtra(ctx context.Context, template *GuideDcmpTemplate, data *group.GuideData) {
	buttonParam := m.buildButtonParam(ctx, template, map[string]string{
		"trace_id": util.GetTraceIDFromCtxWithoutCheck(ctx),
	})
	data.ButtonParams = buttonParam
	if m.isNA {
		data.ActionType = 0
	}
}

func (m *GroupBuilderManager) BuildGuideEngageExtra(ctx context.Context, template *GuideDcmpTemplate, data *group.GuideData) {
	formStation := &EngageCarGuideParam{
		Lat:         m.baseReqData.AreaInfo.FromLat,
		Lng:         m.baseReqData.AreaInfo.FromLng,
		CityId:      m.baseReqData.AreaInfo.Area,
		CityName:    m.baseReqData.AreaInfo.FromCityName,
		DisplayName: m.baseReqData.AreaInfo.FromName,
		ContactTel:  m.baseReqData.PassengerInfo.Phone,
		Address:     m.baseReqData.AreaInfo.FromAddress,
		PoiId:       m.baseReqData.AreaInfo.FromPoiID,
	}
	toStation := &EngageCarGuideParam{
		Lat:         m.baseReqData.AreaInfo.ToLat,
		Lng:         m.baseReqData.AreaInfo.ToLng,
		CityId:      m.baseReqData.AreaInfo.ToArea,
		CityName:    m.baseReqData.AreaInfo.ToCityName,
		DisplayName: m.baseReqData.AreaInfo.ToName,
		ContactTel:  m.baseReqData.PassengerInfo.Phone,
		Address:     m.baseReqData.AreaInfo.ToAddress,
		PoiId:       m.baseReqData.AreaInfo.ToPoiID,
	}
	buttonParamStr := util.ReplaceTag(ctx, *template.ButtonParams, map[string]string{
		"trace_id": util.GetTraceIDFromCtxWithoutCheck(ctx),
	})
	params := []string{
		"didiFromStation=" + util.ToJSONStringNotNull(formStation),
		"didiToStation=" + util.ToJSONStringNotNull(toStation),
		"stopover_points=" + util.ToJSONStringNotNull(m.baseReqData.AreaInfo.StopoverPointInfo),
		"stopover_points=" + util.ToJSONStringNotNull(m.baseReqData.AreaInfo.StopoverPointInfo),
		"from_type=1",
		"guide_trace_id=" + util.GetTraceIDFromCtxWithoutCheck(ctx),
		"trans_data=" + buttonParamStr,
	}
	for _, param := range params {
		data.GuidePath = data.GuidePath + "&" + param
	}
}

func (m *GroupBuilderManager) buildGuidePetExtra(data *group.GuideData, template *GuideDcmpTemplate) {
	if m.isNA {
		data.GuidePath = data.GuidePath + "&tab_id=" + *template.TabId + "&dchn=" + *template.Dchn
		data.ActionType = 0
	}
}

func (m *GroupBuilderManager) buildGuideTrainExtra(product *biz_runtime.ProductInfoFull, data *group.GuideData) {
	extraMap := cast.ToStringMap(product.GetHistoryExtraMap())
	param := &TrainGuideParam{
		FromCity: map[string]any{
			"city_id":   m.baseReqData.AreaInfo.Area,
			"name":      extraMap["start_city_name"],
			"city_name": extraMap["start_city_name"],
		},
		ToCity: map[string]any{
			"city_id":   m.baseReqData.AreaInfo.ToArea,
			"name":      extraMap["end_city_name"],
			"city_name": extraMap["end_city_name"],
		},
		Date: time.Now().Format("2006-01-02"),
	}
	paramStr, _ := json.Marshal(param)
	data.GuideParams = util.StringPtr(string(paramStr))
}

func (m *GroupBuilderManager) buildGuideSFCExtra(ctx context.Context, data *group.GuideData) {
	data.GuidePath = util.ReplaceTag(ctx, data.GuidePath, map[string]string{
		"extra_params": util.ToJSONStringNotNull(map[string]string{
			"wyc_bubble_id": util.GetTraceIDFromCtxWithoutCheck(ctx),
		}),
	})
}

func (m *GroupBuilderManager) buildGuideSmartCarpoolExtra(ctx context.Context, template *GuideDcmpTemplate, data *group.GuideData) {
	dchn := ""
	if m.isNA {
		dchn = "L0gZDpZ"
	}
	if m.isXcx {
		dchn = "Elknl9x"
	}
	buttonParam := m.buildButtonParam(ctx, template, map[string]string{
		"trace_id": util.GetTraceIDFromCtxWithoutCheck(ctx),
		"dchn":     dchn,
	})
	data.ButtonParams = buttonParam
	data.ButtonParams["dchn"] = dchn
	data.OmgData = map[string]string{
		"dchn": dchn,
	}
}

func (m *GroupBuilderManager) buildGuideStationExtra(ctx context.Context, template *GuideDcmpTemplate, data *group.GuideData, product *biz_runtime.ProductInfoFull) {
	isBestShift := m.baseReqData.CommonBizInfo.IsBestShift
	data.OmgData = map[string]string{
		"custom_carpool_type": cast.ToString(carpool_type.CarpoolTypeInterCityStation),
		"custom_is_bestshift": cast.ToString(isBestShift),
	}
	departureTime := time.Unix(m.baseReqData.CommonInfo.DepartureTime, 0)
	today := time.Date(departureTime.Year(), departureTime.Month(), departureTime.Day(), 0, 0, 0, 0, departureTime.Location())
	timestamp := today.UnixMilli()
	url := ""
	var params map[string]string
	if isBestShift == 1 {
		if m.isXcx {
			url = dcmp.GetJSONContentWithPath(ctx, "estimate_new_form-intercity_station_guide", nil, "estimate_app_url")
		} else if m.isNA {
			url = dcmp.GetJSONContentWithPath(ctx, "estimate_new_form-intercity_station_guide", nil, "estimate_mini_program_url")
		}
		params = map[string]string{
			"guide_trace_id":               util.GetTraceIDFromCtxWithoutCheck(ctx),
			"estimate_id":                  product.GetEstimateID(),
			"dchn":                         *template.Dchn,
			"start_lat":                    cast.ToString(m.baseReqData.AreaInfo.FromLat),
			"start_lng":                    cast.ToString(m.baseReqData.AreaInfo.FromLng),
			"start_poi_name":               m.baseReqData.AreaInfo.FromName,
			"start_poi_id":                 cast.ToString(m.baseReqData.AreaInfo.FromPoiID),
			"start_poi_city":               cast.ToString(m.baseReqData.AreaInfo.Area),
			"end_lat":                      cast.ToString(m.baseReqData.AreaInfo.ToLat),
			"end_lng":                      cast.ToString(m.baseReqData.AreaInfo.ToLng),
			"end_poi_name":                 m.baseReqData.AreaInfo.ToName,
			"end_poi_id":                   cast.ToString(m.baseReqData.AreaInfo.ToPoiID),
			"end_poi_city":                 cast.ToString(m.baseReqData.AreaInfo.ToArea),
			"is_best_shift":                cast.ToString(isBestShift),
			"carry_children_max_inventory": product.GetBizInfo().StationInventoryInfo.SelectInfo.ExtraInfo["remain_carry_child_num"],
			"bus_service_shift_id":         product.Product.ShiftID,
			"start_station_id":             cast.ToString(product.GetBizInfo().IntercityData.StartStationID),
			"end_station_id":               cast.ToString(product.GetBizInfo().IntercityData.DestStationID),
		}
	} else {
		url = dcmp.GetJSONContentWithPath(ctx, "estimate_new_form-intercity_station_guide", nil, "head_url")
		params = map[string]string{
			"start_lat":           cast.ToString(m.baseReqData.AreaInfo.FromLat),
			"start_lng":           cast.ToString(m.baseReqData.AreaInfo.FromLng),
			"start_poi_name":      m.baseReqData.AreaInfo.FromName,
			"start_poi_city_name": m.baseReqData.AreaInfo.FromCityName,
			"start_poi_id":        cast.ToString(m.baseReqData.AreaInfo.FromPoiID),
			"start_poi_city":      cast.ToString(m.baseReqData.AreaInfo.Area),
			"end_lat":             cast.ToString(m.baseReqData.AreaInfo.ToLat),
			"end_lng":             cast.ToString(m.baseReqData.AreaInfo.ToLng),
			"end_poi_name":        m.baseReqData.AreaInfo.ToName,
			"end_poi_city_name":   m.baseReqData.AreaInfo.ToCityName,
			"end_poi_id":          cast.ToString(m.baseReqData.AreaInfo.ToPoiID),
			"end_poi_city":        cast.ToString(m.baseReqData.AreaInfo.ToArea),
			"client_type":         cast.ToString(m.baseReqData.CommonInfo.ClientType),
			"page_type":           cast.ToString(m.baseReqData.CommonInfo.PageType),
			"day_time":            cast.ToString(timestamp),
			"trans_data": util.ToJSONStringNotNull(map[string]string{
				"guide_trace_id": util.GetTraceIDFromCtxWithoutCheck(ctx),
			}),
			"omg_data": util.ToJSONStringNotNull(data.OmgData),
		}
	}
	data.GuidePath = util.ReplaceTag(ctx, url, params)
}

func (m *GroupBuilderManager) buildGuideIntercityExtra(ctx context.Context, template *GuideDcmpTemplate, data *group.GuideData, product *biz_runtime.ProductInfoFull) {
	dchn := *template.Dchn
	buttonParam := m.buildButtonParam(ctx, template, map[string]string{
		"trace_id": util.GetTraceIDFromCtxWithoutCheck(ctx),
		"dchn":     dchn,
	})
	data.ButtonParams = buttonParam
	data.OmgData = map[string]string{
		"dchn": dchn,
	}
	// 惊喜独享
	if product.GetPrivateBizInfo().IsIntercitySurpriseAlone {
		data.ActionType = 2
	}
}

func (m *GroupBuilderManager) buildGuidePinCheCheExtra(ctx context.Context, template *GuideDcmpTemplate, data *group.GuideData) {
	dchn := *template.Dchn
	buttonParam := m.buildButtonParam(ctx, template, map[string]string{
		"trace_id": util.GetTraceIDFromCtxWithoutCheck(ctx),
		"dchn":     dchn,
	})
	data.ButtonParams = buttonParam
	data.OmgData = map[string]string{
		"dchn": dchn,
	}
}

func (m *GroupBuilderManager) buildGuideAutoDriverExtra(ctx context.Context, product *biz_runtime.ProductInfoFull, data *group.GuideData) {
	if m.isNA {
		adapter := &EstimateV4.EstimateV4Adapter{
			ProductInfoFull: product,
		}
		data.GuidePath = data.GuidePath + "&auto_driving_address_info=" + util.ToJSONStringNotNull(estimate_v4.GetAutoDrivingAddressInfo(ctx, adapter))
	}
}

func (m *GroupBuilderManager) buildButtonParam(ctx context.Context, template *GuideDcmpTemplate, replace map[string]string) map[string]string {
	buttonParamStr := util.ReplaceTag(ctx, *template.ButtonParams, replace)
	buttonParam := make(map[string]string)
	_ = json.Unmarshal([]byte(buttonParamStr), &buttonParam)
	return buttonParam
}
