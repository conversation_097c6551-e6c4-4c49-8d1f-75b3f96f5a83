package data

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v4_multi/experiment"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout_v2/group"
)

type RecTagTemplate struct {
	FontColor      string   `json:"font_color" form:"font_color"`
	BorderColor    string   `json:"border_color" form:"border_color"`
	BgGradients    []string `json:"bg_gradients" form:"bg_gradients"` //标签文字颜色
	IconUrl        string   `json:"icon_url" form:"icon_url"`         //标签文字颜色
	HighlightColor string   `json:"highlight_color" form:"highlight_color"`
}

func (m *GroupBuilderManager) buildRecAreaData(ctx context.Context, groupId string) *group.RecAreaData {
	if !m.isRecArea {
		return nil
	}
	recFormModel := m.baseReqData.CommonBizInfo.RecFormFeatureModel
	if recFormModel == nil {
		return nil
	}
	recBgColor := dcmp.GetJSONContentWithPath(ctx, "estimate_form_v3-rec_form_tag", nil, "rec_bg_color")
	recAreaData := &group.RecAreaData{
		RecTagData: &proto.RecData{
			RecBgColor: util.StringPtr(recBgColor),
		},
	}

	tagInfo := recFormModel.RecGroup2tags[groupId]
	if tagInfo != nil {
		if tagInfo.SubTagContent != "" && len(tagInfo.SubTagContent) <= 6 {
			recAreaData.RecTagData.RecTag = m.buildRecTag(ctx, tagInfo.SubTagContent, "sub_tag")
		}
		if !experiment.GetNewStyleFormABParamFromBaseReqData(ctx, m.baseReqData).IsHideRecRightTag() {
			recAreaData.RecTagData.RecRightTag = m.buildRecTag(ctx, tagInfo.RightTagContent, "right_tag")
		}
	}
	return recAreaData
}

func (m *GroupBuilderManager) buildRecTag(ctx context.Context, tagTxt string, dcmpPath string) *proto.RecTag {
	if !m.isRecArea {
		return nil
	}
	path := dcmp.GetJSONContentWithPath(ctx, "estimate_form_v3-rec_form_tag", nil, dcmpPath)
	template := &RecTagTemplate{}
	err := json.Unmarshal([]byte(path), template)
	if err != nil {
		return nil
	}
	return &proto.RecTag{
		Content:        tagTxt,
		FontColor:      template.FontColor,
		BorderColor:    template.BorderColor,
		BgGradients:    template.BgGradients,
		IconUrl:        template.IconUrl,
		HighlightColor: template.HighlightColor,
	}
}
