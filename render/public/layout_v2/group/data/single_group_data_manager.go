package data

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v4_multi/experiment"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout_v2/group"
	"git.xiaojukeji.com/gulfstream/passenger-common/model/price"
	"github.com/spf13/cast"
	"math"
)

type BargainPopupTemplate struct {
	Title                  string                `json:"title,omitempty" form:"title"`                                         //弹框标题
	SubTitleMin            string                `json:"sub_title_min,omitempty" form:"sub_title_min"`                         //子标题-低于推荐价
	SubTitleEqual          string                `json:"sub_title_equal,omitempty" form:"sub_title_equal"`                     //子标题-等于推荐价
	SubTitleMax            string                `json:"sub_title_max,omitempty" form:"sub_title_max"`                         //子标题-高于推荐价
	LeftPriceTag           []int32               `json:"left_price_tag,omitempty" form:"left_price_tag"`                       //左侧价格标签
	RightPriceTag          []int32               `json:"right_price_tag,omitempty" form:"right_price_tag"`                     //右侧价格标签
	ButtonText             string                `json:"button_text,omitempty" form:"button_text"`                             //按钮文案
	FeeDescContentTemplate *proto.NewFormFeeDesc `json:"fee_desc_content_template,omitempty" form:"fee_desc_content_template"` //费用描述信息的模版
	FeeMsgTemplate         string                `json:"fee_msg_template,omitempty" form:"fee_msg_template"`                   //预估价模版
	CeilNotice             string                `json:"ceil_notice,omitempty" form:"ceil_notice"`
	Floor1Notice           string                `json:"floor_1_notice,omitempty" form:"floor_1_notice"`
	Floor2Notice           string                `json:"floor_2_notice,omitempty" form:"floor_2_notice"`
}

type BargainButton struct {
	Text           string `json:"text"`
	UnderLineColor string `json:"under_line_color"`
}

func (m *GroupBuilderManager) buildSingleData(ctx context.Context, pcId int64, product *biz_runtime.ProductInfoFull) *group.SingleData {
	// TODO minibus大字
	if pcId == estimate_pc_id.EstimatePcIdBargain { // TODO 存储UFS
		singleData := &group.SingleData{}
		popupStr := dcmp.GetJSONContentWithPath(ctx, "estimate_new_form_bargain", nil, "popup")
		popup := &BargainPopupTemplate{}
		err := json.Unmarshal([]byte(popupStr), popup)
		if err != nil {
			limitData := m.getBargainRecommendLimitData(ctx, product)
			feeMargin := &proto.BargainFeeMarginV3{
				Ceil: &proto.BargainFeeMarginNode{
					Amount: util.StringPtr(limitData["ceil"]),
					Notice: util.StringPtr(popup.CeilNotice),
				},
				Floor1: &proto.BargainFeeMarginNode{
					Amount: util.StringPtr(limitData["floor1_notice"]),
					Notice: util.StringPtr(popup.Floor1Notice),
				},
				Floor2: &proto.BargainFeeMarginNode{
					Amount: util.StringPtr(limitData["floor2_notice"]),
					Notice: util.StringPtr(popup.Floor2Notice),
				},
			}
			singleData.BargainPopup = &proto.BargainPopup{
				Title:                  util.StringPtr(popup.Title),
				SubTitleMin:            util.StringPtr(popup.SubTitleMin),
				SubTitleEqual:          util.StringPtr(popup.SubTitleEqual),
				SubTitleMax:            util.StringPtr(popup.SubTitleMax),
				LeftPriceTag:           popup.LeftPriceTag,
				RightPriceTag:          popup.RightPriceTag,
				ButtonText:             util.StringPtr(popup.ButtonText),
				FeeMargin:              feeMargin,
				FeeDescContentTemplate: popup.FeeDescContentTemplate,
				FeeMsgTemplate:         util.StringPtr(popup.FeeMsgTemplate),
			}
			if experiment.GetNewStyleFormABParamFromBaseReqData(ctx, m.baseReqData).IsHitNewStyleForm() {
				singleData.BargainPopup.FeeMsgTemplate = util.StringPtr("")
			}
		}
		buttonStr := dcmp.GetJSONContentWithPath(ctx, "estimate_new_form_bargain", nil, "bargain_button")
		button := &BargainButton{}
		err = json.Unmarshal([]byte(buttonStr), button)
		if err != nil {
			text := ""
			fee := cast.ToString(util.RoundAbs(product.GetEstimateFee(), 2))
			if product.GetPrivateBizInfo().BargainData.SenseConfig != nil && len(product.GetPrivateBizInfo().BargainData.SenseConfig.PriceLeftText) > 0 {
				text = util.ReplaceTag(ctx, product.GetPrivateBizInfo().BargainData.SenseConfig.PriceLeftText, map[string]string{
					"num": cast.ToString(fee),
				})
			} else {
				text = util.ReplaceTag(ctx, button.Text, map[string]string{
					"num": cast.ToString(fee),
				})
			}
			singleData.BargainMsg = &proto.NewFormBargainMsg{
				Text:             text,
				UnderlineColor:   button.UnderLineColor,
				ShowPlusAndMinus: util.BoolPtr(true),
			}
		}
		feeDetailUrl := dcmp.GetJSONContentWithPath(ctx, "estimate_new_form_bargain", nil, "fee_detail_url")
		if feeDetailUrl != "" {
			singleData.FeeDetailUrl = util.StringPtr(feeDetailUrl)
		}
		return singleData
	}
	return nil
}

func (m *GroupBuilderManager) getBargainRecommendLimitData(ctx context.Context, product *biz_runtime.ProductInfoFull) map[string]string {
	lowLowLimit := "1"
	lowLimit := "2"
	upLimit := "999"
	limitData := make(map[string]string, 0)

	redPacket := product.GetHolidayFee()

	lowLimitInt := math.Max(redPacket, cast.ToFloat64(lowLimit))
	lowLowLimitInt := math.Max(redPacket, cast.ToFloat64(lowLowLimit))
	limitData["ceil"] = upLimit
	limitData["floor1"] = cast.ToString(lowLimitInt)
	limitData["floor2"] = cast.ToString(lowLowLimitInt)

	if product == nil {
		return limitData
	}

	if product.GetBargainRecommendInfo() != nil {
		recommendPriceInfo := product.GetBargainRecommendInfo()

		limitData["ceil"] = util.FormatPrice(util.Float64Add(recommendPriceInfo.UpperLimit, product.GetHolidayFee()), 1)
		limitData["floor1"] = util.FormatPrice(util.Float64Add(recommendPriceInfo.LowerLimit, product.GetHolidayFee()), 1)
		limitData["floor2"] = util.FormatPrice(util.Float64Add(recommendPriceInfo.LowerLowerLimit, product.GetHolidayFee()), 1)
	}
	price.CheckMulti(ctx, "getRecommendLimitData", "RecommendLimitData", limitData)

	return limitData
}
