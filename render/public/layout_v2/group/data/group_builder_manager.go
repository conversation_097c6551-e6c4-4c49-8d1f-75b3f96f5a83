package data

import (
	"context"
	"fmt"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/category_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/group_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	estimateV4Data "git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v4_multi/data"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/category_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/group_data"
	LayoutConsts "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout_v2/group"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout_v2/group/render"
	"github.com/spf13/cast"
)

// GroupBuilderManager Group构建器管理器
type GroupBuilderManager struct {
	// 输入
	baseReqData  *models.BaseReqData
	productsMap  map[int64]*biz_runtime.ProductInfoFull
	estimateData map[int64]*LayoutConsts.SimpleEstimateData
	topPcIds     *util.Set[int64]
	topBoxIds    *util.Set[int64]
	isRecArea    bool

	// 内部自定义配置
	pcId2BoxId     map[int64]int32
	boxId2PcId     map[int32][]int64
	guidePcIds     *util.Set[int32]
	pcId2Selected  map[int64]int32
	xiaguaConfig   *util.BiMap[int64, int64]
	readyGroup     *util.Set[string]
	isNA           bool
	isXcx          bool
	needMergeGroup bool

	// 流量盒子
	pcId2CategoryIdInFlowBox map[int64]int32
	flowBoxProducts          map[int32]*util.Set[string]
	flowBoxAllPcIds          map[int32][]int64
	boxInFlowBox             *util.Set[string]

	// 构造器
	builders map[group.GroupBuilderType]func(*group.GroupBuilderData) group.IGroupBuilder

	// 输出
	groups         [][]*proto.NewFormGroup
	canMergeGroups []*proto.NewFormGroup
}

// NewGroupBuilderManager 创建Group构建器管理器
func NewGroupBuilderManager(ctx context.Context, pcid2BoxId map[int64]int32, boxId2Pcid map[int32][]int64, guidePcIds []int32, baseReqData *models.BaseReqData,
	estimateData map[int64]*LayoutConsts.SimpleEstimateData, productsMap map[int64]*biz_runtime.ProductInfoFull, pcid2Selected map[int64]int32, isRecArea bool) *GroupBuilderManager {
	manager := &GroupBuilderManager{
		baseReqData:              baseReqData,
		productsMap:              productsMap,
		estimateData:             estimateData,
		pcId2BoxId:               pcid2BoxId,
		boxId2PcId:               boxId2Pcid,
		pcId2CategoryIdInFlowBox: make(map[int64]int32),
		flowBoxProducts:          make(map[int32]*util.Set[string]),
		boxInFlowBox:             util.NewSet[string](),
		flowBoxAllPcIds:          make(map[int32][]int64),
		isRecArea:                isRecArea,
		guidePcIds:               util.NewSet[int32](guidePcIds...),
		pcId2Selected:            pcid2Selected,
		xiaguaConfig:             util.NewBiMap[int64, int64](),
		readyGroup:               util.NewSet[string](),
		builders:                 make(map[group.GroupBuilderType]func(*group.GroupBuilderData) group.IGroupBuilder),
		groups:                   make([][]*proto.NewFormGroup, 0),
		canMergeGroups:           make([]*proto.NewFormGroup, 0),
	}
	if baseReqData.CommonBizInfo.TopData != nil {
		manager.topPcIds = util.NewSet(baseReqData.CommonBizInfo.TopData.TopPcIdList...)
		manager.topBoxIds = util.NewSet(baseReqData.CommonBizInfo.TopData.TopSubGroupIdList...)
		if manager.topPcIds.Size()+manager.topBoxIds.Size() > 1 {
			manager.needMergeGroup = true
		}
	}
	fold := category_info.GetClassifyFold(ctx, baseReqData, productsMap)
	if fold != nil {
		for categoryId, pcIds := range fold.GetFlowBoxCategoryIdToPcIDMap() {
			if isRecArea && categoryId != category_id.RecFormCategory {
				continue
			}
			set := util.NewSet[string]()
			allPcIds := make([]int64, 0)
			for _, pcId := range pcIds {
				manager.pcId2CategoryIdInFlowBox[pcId] = categoryId
				set.Add(group_id.BuildGroupId(group_id.SINGLETYPE, pcId))
				allPcIds = append(allPcIds, pcId)
			}
			manager.flowBoxProducts[categoryId] = set
			manager.flowBoxAllPcIds[categoryId] = allPcIds
		}

		for categoryId, boxIds := range fold.GetFlowBoxCategoryIdToSubGroupIdMap() {
			if isRecArea && categoryId != category_id.RecFormCategory {
				continue
			}
			set := util.NewSet[string]()
			allPcIds := make([]int64, 0)
			if manager.flowBoxProducts[categoryId] != nil {
				set = manager.flowBoxProducts[categoryId]
			}
			if manager.flowBoxAllPcIds[categoryId] != nil {
				allPcIds = manager.flowBoxAllPcIds[categoryId]
			}
			for _, boxId := range boxIds {
				groupId := group_id.BuildGroupId(group_id.AggregationBoxType, cast.ToInt64(boxId))
				set.Add(groupId)
				manager.boxInFlowBox.Add(groupId)
				allPcIds = append(allPcIds, manager.boxId2PcId[boxId]...)
			}
			manager.flowBoxAllPcIds[categoryId] = allPcIds
		}
	}

	if baseReqData.CommonInfo.AccessKeyID == consts.AlipayAccessKeyId || baseReqData.CommonInfo.AccessKeyID == consts.WeChatAccessKeyId {
		manager.isXcx = true
	} else {
		if baseReqData.CommonInfo.AccessKeyID == consts.IosAccessKeyId || baseReqData.CommonInfo.AccessKeyID == consts.AndroidAccessKeyId || baseReqData.CommonInfo.AccessKeyID == consts.HarmonyAccessKeyId {
			manager.isNA = true
		}
	}
	manager.xiaguaConfig.Put(estimate_pc_id.EstimatePcIdFastCar, estimate_pc_id.EstimatePcIdSpaciousCar)
	manager.registerDefaultBuilders()
	return manager
}

func (m *GroupBuilderManager) registerDefaultBuilders() {
	m.builders[group.GroupBuilderTypeSingle] = func(data *group.GroupBuilderData) group.IGroupBuilder {
		return render.NewSingleGroupBuilder(data)
	}
	m.builders[group.GroupBuilderTypeGuide] = func(data *group.GroupBuilderData) group.IGroupBuilder {
		return render.NewGuideGroupBuilder(data)
	}
	m.builders[group.GroupBuilderTypeXiagua] = func(data *group.GroupBuilderData) group.IGroupBuilder {
		return render.NewXiaguaGroupBuilder(data)
	}
	m.builders[group.GroupBuilderTypeBox] = func(data *group.GroupBuilderData) group.IGroupBuilder {
		return render.NewBoxGroupBuilder(data)
	}
}

// createBuilder 创建构建器
func (m *GroupBuilderManager) createBuilder(builderType group.GroupBuilderType, data *group.GroupBuilderData) (group.IGroupBuilder, error) {
	builderFunc, exists := m.builders[builderType]
	if !exists {
		return nil, fmt.Errorf("unsupported builder type: %d", builderType)
	}

	return builderFunc(data), nil
}

func (m *GroupBuilderManager) BuildGroups(ctx context.Context) ([][]*proto.NewFormGroup, error) {
	for pcId, product := range m.productsMap {
		builderType, athenaType, subGroupId := m.autoSelectBuilderType(pcId)

		// 构建GroupBuilderData
		data := m.buildGroupBuilderData(ctx, pcId, product, builderType, athenaType, subGroupId)
		if data == nil {
			continue
		}

		// 构建Group
		newGroup, err := m.buildEveryGroup(ctx, builderType, data)
		if err != nil || newGroup == nil {
			continue // 跳过构建失败的group
		}

		if athenaType == group_id.AggregationBoxType && m.boxInFlowBox.Contains(newGroup.GroupId) {
			groupData := group_data.NewGroupData(ctx)
			groupData.AddGroupData(newGroup.GroupId, newGroup)
			fold := category_info.GetClassifyFold(ctx, m.baseReqData, m.productsMap)
			fold.AddFinalInBoxSubGroupIdSet(newGroup.GroupId)
			continue
		}

		if m.needMergeGroup && (builderType != group.GroupBuilderTypeBox && m.topPcIds.Contains(pcId) || (builderType == group.GroupBuilderTypeBox && m.topBoxIds.Contains(int64(m.pcId2BoxId[pcId])))) {
			m.canMergeGroups = append(m.canMergeGroups, newGroup)
		} else {
			m.groups = append(m.groups, []*proto.NewFormGroup{newGroup})
		}
	}
	m.mergeGroups()
	return m.groups, nil
}

// buildEveryGroup 构建Group
func (m *GroupBuilderManager) buildEveryGroup(ctx context.Context, builderType group.GroupBuilderType, data *group.GroupBuilderData) (*proto.NewFormGroup, error) {
	builder, err := m.createBuilder(builderType, data)
	if err != nil {
		return nil, err
	}

	newGroup := builder.BuildGroup(ctx)
	if newGroup == nil {
		return nil, fmt.Errorf("failed to build group for type: %d", builderType)
	}
	m.readyGroup.Add(newGroup.GroupId)
	return newGroup, nil
}

// autoSelectBuilderType 自动选择构建器类型
func (m *GroupBuilderManager) autoSelectBuilderType(pcId int64) (group.GroupBuilderType, int, int64) {
	if m.guidePcIds.Contains(cast.ToInt32(pcId)) {
		return group.GroupBuilderTypeGuide, group_id.SINGLETYPE, pcId
	}
	if boxId, ok := m.pcId2BoxId[pcId]; ok {
		return group.GroupBuilderTypeBox, group_id.AggregationBoxType, cast.ToInt64(boxId)
	}

	if m.xiaguaConfig.ContainsKey(pcId) || m.xiaguaConfig.ContainsValue(pcId) {
		var mainPcId int64
		if m.xiaguaConfig.ContainsKey(pcId) {
			mainPcId = pcId
		} else {
			mainPcId, _ = m.xiaguaConfig.GetByValue(pcId)
		}
		return group.GroupBuilderTypeXiagua, group_id.SINGLETYPE, mainPcId
	}

	return group.GroupBuilderTypeSingle, group_id.SINGLETYPE, pcId
}

// buildGroupBuilderData 构建GroupBuilderData
func (m *GroupBuilderManager) buildGroupBuilderData(ctx context.Context, pcId int64, product *biz_runtime.ProductInfoFull, builderType group.GroupBuilderType, athenaType int, subGroupId int64) *group.GroupBuilderData {
	var data *group.GroupBuilderData
	groupId := group_id.BuildGroupId(athenaType, subGroupId)
	if m.readyGroup.Contains(groupId) {
		return nil
	}
	var carIconType *int32
	if builderType != group.GroupBuilderTypeBox {
		provider := &estimateV4Data.EstimateV4Adapter{ProductInfoFull: product}
		carIconType = util.Int32Ptr(car_info.GetCarIconType(ctx, provider))
	}
	switch builderType {
	case group.GroupBuilderTypeGuide:
		data = &group.GroupBuilderData{
			Products:    []string{cast.ToString(pcId)},
			IsSelected:  m.pcId2Selected[pcId],
			Type:        group.GuideType,
			GroupId:     groupId,
			CarIconType: carIconType,
		}
		data.GuideData = m.buildGuideData(ctx, pcId, product)
		data.RecAreaData = m.buildRecAreaData(ctx, groupId)

	case group.GroupBuilderTypeBox:
		boxId := cast.ToInt32(subGroupId)
		var products []string
		isSelected := false
		var groupType int32
		if athenaType == group_id.AggregationBoxType {
			products = make([]string, 0)
			for _, pcId := range m.boxId2PcId[boxId] {
				if m.pcId2Selected[pcId] == consts.CHECK {
					isSelected = true
				}
				products = append(products, cast.ToString(pcId))
			}
			if boxId == group_id.SubGroupShortDistance || boxId == group_id.SubGroupIdDiscountAlliance {
				carIconType = util.Int32Ptr(car_info.CarIconTypeNew)
			}
			groupType = group.Box2GroupTypeMap[boxId]
		} else if athenaType == group_id.FLOWBOXType {
			products = m.flowBoxProducts[boxId].ToSlice()
			for _, pcId := range m.flowBoxAllPcIds[boxId] {
				if m.pcId2Selected[pcId] == consts.CHECK {
					isSelected = true
				}
			}
			groupType = group.ShortDistanceBox
		}
		data = &group.GroupBuilderData{
			Products:    products,
			IsSelected:  cast.ToInt32(isSelected),
			Type:        groupType,
			GroupId:     groupId,
			CarIconType: carIconType,
		}
		data.BoxData = m.buildBoxData(ctx, athenaType, boxId)
		data.RecAreaData = m.buildRecAreaData(ctx, groupId)

	case group.GroupBuilderTypeXiagua:
		var mainPcId, linkPcId int64
		if m.xiaguaConfig.ContainsKey(pcId) {
			mainPcId = pcId
			linkPcId, _ = m.xiaguaConfig.GetByKey(pcId)
		} else {
			linkPcId = pcId
			mainPcId, _ = m.xiaguaConfig.GetByValue(pcId)
		}
		data = &group.GroupBuilderData{
			Products:    []string{cast.ToString(mainPcId)},
			IsSelected:  m.pcId2Selected[mainPcId],
			Type:        group.SingleType,
			GroupId:     groupId,
			CarIconType: carIconType,
		}
		data.XiaguaData = m.buildXiaguaData(ctx, linkPcId, product)
		data.RecAreaData = m.buildRecAreaData(ctx, groupId)

	case group.GroupBuilderTypeSingle:
		data = &group.GroupBuilderData{
			Products:    []string{cast.ToString(pcId)},
			IsSelected:  m.pcId2Selected[pcId],
			Type:        group.SingleType,
			GroupId:     groupId,
			CarIconType: carIconType,
			RecAreaData: m.buildRecAreaData(ctx, groupId),
			SingleData:  m.buildSingleData(ctx, pcId, product),
		}
	}

	return data
}

func (m *GroupBuilderManager) mergeGroups() {
	if m.canMergeGroups != nil && len(m.canMergeGroups) > 0 {
		for _, newGroup := range m.canMergeGroups {
			newGroup.IsCompressed = util.Int32Ptr(1)
		}
		m.groups = append(m.groups, m.canMergeGroups)
	}
}
