package group

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/group_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

// IGroupBuilder Group构建器接口
type IGroupBuilder interface {
	BuildGroup(ctx context.Context) *proto.NewFormGroup
	GetBuilderType() GroupBuilderType
}

// GroupBuilderType Group构建器类型
type GroupBuilderType int

const (
	// 4种Group构建器类型
	GroupBuilderTypeSingle GroupBuilderType = 1 // 单车型
	GroupBuilderTypeGuide  GroupBuilderType = 2 // 导流车型
	GroupBuilderTypeXiagua GroupBuilderType = 3 // 下挂车型
	GroupBuilderTypeBox    GroupBuilderType = 4 // 盒子车型
)

// group type
const (
	SingleType       = 1
	ShortDistanceBox = 2
	TaxiBox          = 3
	TaxiPricingBox   = 6
	CapPriceBox      = 7
	MinBusBox        = 8
	GuideType        = 99
	GuideThemeType   = 100
	BargainType      = 101
)

var BoxGroupTypes = util.NewSet(ShortDistanceBox, TaxiBox, TaxiPricingBox, CapPriceBox)

var Box2GroupTypeMap = map[int32]int32{
	group_id.SubGroupShortDistance:      CapPriceBox,
	group_id.SubGroupIdTaxi:             ShortDistanceBox,
	group_id.SubGroupIdTaxiPricing:      TaxiPricingBox,
	group_id.SubGroupIdFastMustCheaper:  CapPriceBox,
	group_id.SubGroupIdXDiscount:        ShortDistanceBox,
	group_id.SubGroupIdDiscountAlliance: ShortDistanceBox,
	group_id.SubGroupIdTaxiChaoZhiDa:    CapPriceBox,
}

// GroupBuilderData Group构建器数据
type GroupBuilderData struct {
	Products    []string
	IsSelected  int32
	Type        int32
	GroupId     string
	CarIconType *int32
	SingleData  *SingleData
	BoxData     *BoxData
	XiaguaData  *XiaguaData
	GuideData   *GuideData
	RecAreaData *RecAreaData
}

type BoxData struct {
	CarTitle           string
	CarIcon            string
	PopupTitle         string
	PopupSubTitle      string
	FeeMsgTemplate     string
	BoxDesc            string
	FeeDescList        []*proto.NewFormFeeDesc
	UnselectPopupTitle *string
	PopupToast         *string
	FeeMsg             *string
	FeeDetailUrl       *string
	FontScaleBoxDesc   *string
	FeeDescIcon        *string
	FeeDesc            *string
	IsSubNodisplayEtp  *int32
}

type SingleData struct {
	BargainPopup *proto.BargainPopup
	BargainMsg   *proto.NewFormBargainMsg
	FeeDetailUrl *string
}

type XiaguaData struct {
	LinkInfo *proto.NewFormLinkInfo
}

type GuideData struct {
	ButtonText    string
	ActionType    int32
	JumpType      int32
	GuidePath     string
	DisableShadow *int32
	FeeDetailUrl  *string
	GuideParams   *string
	OmgData       map[string]string
	GuideStyle    *int32
	ButtonParams  map[string]string
}

type RecAreaData struct {
	RecTagData *proto.RecData // 推荐区推广标签文案
}
