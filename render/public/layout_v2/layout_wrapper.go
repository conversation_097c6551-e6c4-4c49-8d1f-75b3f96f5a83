package layout_v2

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout_v2/other_data"
)

// LayoutWrapper Layout包装器
type LayoutWrapper struct {
	selector *other_data.StrategySelector
}

// NewLayoutWrapper 创建Layout包装器
func NewLayoutWrapper() *LayoutWrapper {
	return &LayoutWrapper{
		selector: other_data.DefaultStrategySelector(),
	}
}

// NewRecLayoutWrapper 创建RecLayout包装器
func NewRecLayoutWrapper() *LayoutWrapper {
	return &LayoutWrapper{
		selector: other_data.RecLayoutStrategySelector(),
	}
}

// wrapGroupToLayout 将Group包装成Layout
func (w *LayoutWrapper) wrapGroupToLayout(ctx context.Context, group []*proto.NewFormGroup, strategyContext *other_data.StrategyContext) *proto.NewFormLayout {
	if group == nil {
		return nil
	}

	// 1. 选择FormShowType策略并构建
	var formShowType int32
	formShowTypeStrategy := w.selector.FormShowTypeFactory.SelectFormShowTypeStrategy()
	if formShowTypeStrategy != nil {
		formShowType = formShowTypeStrategy.BuildFormShowType(ctx, group, strategyContext.BaseReqData)
	}

	// 2. 选择Theme策略并构建
	var themeData *proto.NewFormThemeData
	themeStrategy := w.selector.ThemeFactory.SelectThemeStrategy(strategyContext.BaseReqData)
	if themeStrategy != nil {
		themeData = themeStrategy.BuildTheme(ctx, strategyContext.BaseReqData)
	}

	// 3. 选择Price策略并构建
	var price float64
	var targetPcId int64
	priceStrategy := w.selector.PriceFactory.SelectPriceStrategy()
	if priceStrategy != nil {
		targetPcId, price = priceStrategy.BuildPrice(ctx, group, strategyContext.BaseReqData, strategyContext.ProductsMap)
	}

	// 4. 选择foldType策略并构建
	var foldType *int32
	foldTypeStrategy := w.selector.FoldTypeStrategy.SelectFoldTypeStrategy(strategyContext.BaseReqData)
	if priceStrategy != nil {
		foldType = util.Int32Ptr(foldTypeStrategy.BuildFoldType(ctx, group, strategyContext.BaseReqData, strategyContext.ProductsMap))
	}

	// 5. 选择category_id策略并构建
	var categoryId *int32
	categoryIdStrategy := w.selector.CategoryIdStrategy.SelectCategoryIdStrategy(strategyContext.BaseReqData, strategyContext.IsRecArea)
	if priceStrategy != nil {
		categoryId = util.Int32Ptr(categoryIdStrategy.BuildCategoryId(ctx, group, strategyContext.BaseReqData, strategyContext.ProductsMap))
	}

	var subCategoryId *int32
	// 6. 选择sub_category_id策略并构建
	if w.selector.SubCategoryIdStrategy != nil {
		subCategoryIdStrategy := w.selector.SubCategoryIdStrategy.SelectSubCategoryIdStrategy(strategyContext.BaseReqData)
		if priceStrategy != nil {
			subCategoryId = util.Int32Ptr(subCategoryIdStrategy.BuildSubCategoryId(ctx, group, strategyContext.BaseReqData))
		}
	}

	// 7. 创建Layout
	layout := &proto.NewFormLayout{
		Groups:          group,
		FormShowType:    formShowType,
		ThemeData:       themeData,
		Price:           price,
		FoldType:        foldType,
		CategoryId:      categoryId,
		SubCategoryType: subCategoryId,
	}

	// 8. 选择Weight策略并构建
	weightStrategy := w.selector.WeightFactory.SelectWeightStrategy(strategyContext.BaseReqData, strategyContext.IsRecArea, layout)
	if weightStrategy != nil {
		weight := weightStrategy.BuildWeight(ctx, layout, strategyContext.BaseReqData, strategyContext.ProductsMap, targetPcId)
		layout.Weight = weight
	}

	return layout
}

// wrapMultipleGroupsToLayouts 将多个Group包装成多个NewFormLayout
func (w *LayoutWrapper) wrapMultipleGroupsToLayouts(ctx context.Context, groups [][]*proto.NewFormGroup, strategyContext *other_data.StrategyContext) []*proto.NewFormLayout {
	var layouts []*proto.NewFormLayout

	for _, group := range groups {
		if layout := w.wrapGroupToLayout(ctx, group, strategyContext); layout != nil {
			layouts = append(layouts, layout)
		}
	}

	return layouts
}

// BuildLayoutFromGroups 从Groups构建完整的NewFormLayout列表
func (w *LayoutWrapper) BuildLayoutFromGroups(ctx context.Context, groups [][]*proto.NewFormGroup, baseReqData *models.BaseReqData, productMap map[int64]*biz_runtime.ProductInfoFull, isRecArea bool) []*proto.NewFormLayout {
	strategyContext := &other_data.StrategyContext{
		BaseReqData: baseReqData,
		ProductsMap: productMap,
		IsRecArea:   isRecArea,
	}
	// 包装Groups为Layouts
	return w.wrapMultipleGroupsToLayouts(ctx, groups, strategyContext)
}
