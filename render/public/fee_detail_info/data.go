package fee_detail_info

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/render"
)

type PriceDescProvider interface {
	render.ProductProvider
}

func GetDetailUrlV2(ctx context.Context, prod PriceDescProvider) string {
	return dcmp.GetDcmpContent(ctx, "common-fee_detail_url_v4", nil)
}

func GetDetailUrl(ctx context.Context) string {
	return dcmp.GetDcmpContent(ctx, "common-fee_detail_url_v4", nil)
}

func GetFeeDetailV3(ctx context.Context) string {
	return dcmp.GetDcmpContent(ctx, "common-fee_detail_url", nil)
}

func GetFeeDetail4Box(ctx context.Context) string {
	return dcmp.GetDcmpContent(ctx, "common-fee_detail_url_v4_for_box", nil)
}
