syntax = "proto3";
package Dirpc.SDK.Mamba;

import "google/api/annotations.proto";
import "protoc-gen-swagger/options/annotations.proto";
import "dirpc/dirpc.proto";
import "github.com/mwitkow/go-proto-validators/validator.proto";

option (grpc.gateway.protoc_gen_swagger.options.openapiv2_swagger) = {
    host: "127.0.0.1:8991"
};

option go_package = ".;proto";

enum OrderType {
    NormalOrder=0; // 普通(实时)订单
    BookingOrder=1; // 预约订单
}

enum UserType{
    None=0;
    NormalUser=1; // 普通用户
    BusinessUser=2; // 企业用户
}

message CarpoolEstimateRequest {
    string token = 1; //用户认证token
    UserType user_type = 2 [(validator.field) = {is_in_enum: true}]; //1普通用户；2企业用户
    string app_version = 3 [(validator.field) = {regex: "^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$", human_error: "invalid version"}]; //端版本 https://semver.org/#is-there-a-suggested-regular-expression-regex-to-check-a-semver-string
    int32 access_key_id = 4 [(validator.field) = {int_gt: 0}]; //端来源
    string channel = 5; //渠道号
    int32 client_type = 6; //端类型
    string lang = 7; //端语种
    int32 platform_type = 8; //端(平台)类型-2
    string map_type = 11; //地图类型
    double lat = 12 [(validator.field) = {float_gte: -90.0, float_lte: 90.0}]; //定位点
    double lng = 13 [(validator.field) = {float_gte: -180.0, float_lte: 180.0}];
    double from_lat = 14 [(validator.field) = {float_gte: -90.0, float_lte: 90.0}]; //起点
    double from_lng = 15 [(validator.field) = {float_gte: -180.0, float_lte: 180.0}];
    string from_poi_id = 16;
    string from_poi_type = 17;
    string from_address = 18;
    string from_name = 19;
    string choose_f_searchid = 20; //用户选择起点请求ID
    double to_lat = 21 [(validator.field) = {float_gte: -90.0, float_lte: 90.0}]; //终点
    double to_lng = 22 [(validator.field) = {float_gte: -180.0, float_lte: 180.0}];
    string to_poi_id = 23;
    string to_poi_type = 24;
    string to_address = 25;
    string to_name = 26;
    string choose_t_searchid = 27; //用户选择终点请求ID
    string pre_trace_id = 28; //前一次预估trace
    string departure_range = 29; //选择的出发时间段
    int32 carpool_seat_num = 30; //选择的座位数
    int32 payments_type = 31; //选择的支付方式
    OrderType order_type = 32 [(validator.field) = {is_in_enum: true}]; //这是一个冗余字段, 其实是不需要的, 可以用 departure_range 推断出
    string a3_token = 99; //无用
}

