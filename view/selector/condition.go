package selector

import (
	"errors"
	"strings"
)

var ALLOW_OP = []OpType{OpTypeEq, OpTypeNoEq, OpTypeIn, OpTypeNotIn, OpTypeGt, OpTypeNGt, OpTypeLt, OpTypeNLt, OpTypeVersionGe, OpTypeVersionLe}
var StringToSliceSplit = ","

func isOpAllow(op OpType) bool {
	for _, opStr := range ALLOW_OP {
		if op == opStr {
			return true
		}
	}
	return false
}

type Condition struct {
	Key   string      `json:"key"`
	Op    OpType      `json:"op"`
	Value interface{} `json:"value"`
}

func (c Condition) Check(input map[string]interface{}) bool {
	kValue, ok := input[c.Key]
	if !ok {
		return false
	}
	return Operator(c.Op, kValue, c.Value)
}

type AndConditions []Condition

func (ac AndConditions) Check(input map[string]interface{}) bool {
	rsp := true
	for _, c := range ac {
		rsp = rsp && c.Check(input)
		if !rsp {
			return rsp
		}
	}
	return rsp
}

type Conditions []AndConditions

func (cs Conditions) Check(input map[string]interface{}) bool {
	//未设置条件默认通过
	if len(cs) == 0 {
		return true
	}

	for _, c := range cs {
		if c.Check(input) {
			return true
		}
	}
	return false
}

func ParseConditions(conditions []string) (Conditions, error) {
	var (
		err    error
		result Conditions
	)
	if len(conditions) == 0 {
		return result, err
	}

	for _, cStr := range conditions {
		andPhrases := strings.Split(cStr, "&&")
		andConditions := AndConditions{}
		for _, andPhrase := range andPhrases {
			c, err := parseOneCondition(andPhrase)
			if err != nil {
				return result, err
			}
			andConditions = append(andConditions, *c)
		}
		result = append(result, andConditions)
	}
	return result, nil
}

func parseOneCondition(condStr string) (*Condition, error) {
	oneCond := strings.Split(condStr, "/")
	if len(oneCond) < 3 {
		return nil, errors.New("invalid condition string: not enough params")
	}
	key := oneCond[0]
	op := OpType(oneCond[1])
	valueStr := oneCond[2]
	var value interface{}
	if oneCond[0] == "" || oneCond[1] == "" || oneCond[2] == "" {
		return nil, errors.New("invalid condition string: error params")
	}

	if !isOpAllow(op) {
		return nil, errors.New("invalid condition string: operation not allow")
	}

	if op == OpTypeIn || op == OpTypeNotIn {
		slice := strings.Split(valueStr, StringToSliceSplit)
		if len(slice) == 0 {
			return nil, errors.New("invalid condition slice string")
		}
		value = slice
	} else {
		value = valueStr
	}
	return &Condition{Key: key, Op: op, Value: value}, nil
}
