package selector

import (
	"encoding/json"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"
)

// OpType Operational Character type...
type OpType string

const (
	// OpTypeIg ...
	OpTypeIg OpType = "ig"
	// OpTypeEq ...
	OpTypeEq OpType = "eq"
	// OpTypeNoEq ...
	OpTypeNoEq OpType = "neq"
	// OpTypeLt ...
	OpTypeLt OpType = "lt"
	// OpTypeGt ...
	OpTypeGt OpType = "gt"
	// OpTypeNGt ...
	OpTypeNGt OpType = "ngt"
	// OpTypeNLt ...
	OpTypeNLt OpType = "nlt"
	// OpTypeIn ...
	OpTypeIn OpType = "in"
	// OpTypeNotIn ...
	OpTypeNotIn OpType = "nin"
	// OpTypeContain ...
	OpTypeContain OpType = "contain"
	// OpTypeNotContain ...
	OpTypeNotContain OpType = "ncontain"
	// OpTypeHasIntersection ...
	OpTypeHasIntersection OpType = "has_intersection"
	// OpTypeAlwaysTrue ...
	OpTypeAlwaysTrue OpType = "alwaystrue"
	// OpTypeAlwaysFalse ...
	OpTypeAlwaysFalse OpType = "alwaysfalse"
	//OpTypeVersionGe ...
	OpTypeVersionGe OpType = "version_ge"
	// OpTypeVersionLe ...
	OpTypeVersionLe OpType = "version_le"
)

// Operator ...
func Operator(Op OpType, condVal, reqVal interface{}) bool {
	switch Op {
	case OpTypeIg:
		return true
	case OpTypeEq:
		return Equal(condVal, reqVal)
	case OpTypeNoEq:
		return NotEqual(condVal, reqVal)
	case OpTypeLt:
		return Less(condVal, reqVal)
	case OpTypeGt:
		return Greater(condVal, reqVal)
	case OpTypeNGt:
		return NGreater(condVal, reqVal)
	case OpTypeNLt:
		return NLess(condVal, reqVal)
	case OpTypeIn:
		return Contain(condVal, reqVal)
	case OpTypeNotIn:
		return NotContain(condVal, reqVal)
	case OpTypeContain:
		return Contain(condVal, reqVal)
	case OpTypeNotContain:
		return NotContain(condVal, reqVal)
	case OpTypeHasIntersection:
		return HasIntersection(condVal, reqVal)
	case OpTypeVersionGe:
		return VersionGe(condVal, reqVal)
	case OpTypeVersionLe:
		return VersionLe(condVal, reqVal)
	case OpTypeAlwaysTrue:
		return true
	case OpTypeAlwaysFalse:
		return false
	default:
		return false
	}
}

// Equal ...
func Equal(condVal, reqVal interface{}) bool {
	switch condVal.(type) {
	case int:
		return condVal.(int) == util.ToInt(reqVal)
	case int64:
		return condVal.(int64) == util.ToInt64(reqVal)
	case float64:
		//当reqVal是order_type的时候，order_type类型是OrderType，不是int，在调用ToFloat64后会变成0，坑！！！大家注意了。。。
		return condVal.(float64) == util.ToFloat64(reqVal)
	case string:
		return condVal.(string) == util.ToString(reqVal)
	case bool:
		return condVal.(bool) == util.ToBool(reqVal)
	}
	return false
}

// NotEqual ...
func NotEqual(condVal, reqVal interface{}) bool {
	return Equal(condVal, reqVal) == false
}

// Greater ...
func Greater(condVal, reqVal interface{}) bool {
	switch reqVal.(type) {
	case string:
		return util.ToFloat64(reqVal) > util.ToFloat64(condVal)
	case int:
		return reqVal.(int) > util.ToInt(condVal)
	case int64:
		return reqVal.(int64) > util.ToInt64(condVal)
	case float64:
		return reqVal.(float64) > util.ToFloat64(condVal)
	default:
	}
	return false
}

// Less ...
func Less(condVal, reqVal interface{}) bool {
	switch reqVal.(type) {
	case string:
		val, err := strconv.ParseFloat(reqVal.(string), 64)
		if err != nil {
			return false
		}
		return val < util.ToFloat64(condVal)
	case int:
		return reqVal.(int) < util.ToInt(condVal)
	case int64:
		return reqVal.(int64) < util.ToInt64(condVal)
	case float64:
		return reqVal.(float64) < util.ToFloat64(condVal)
	default:
	}
	return false
}

// NGreater ...
func NGreater(condVal, reqVal interface{}) bool {
	switch reqVal.(type) {
	case string:
		val, err := strconv.ParseFloat(reqVal.(string), 64)
		if err != nil {
			return false
		}
		return val <= util.ToFloat64(condVal)
	case int:
		return reqVal.(int) <= util.ToInt(condVal)
	case int64:
		return reqVal.(int64) <= util.ToInt64(condVal)
	case float64:
		return reqVal.(float64) <= util.ToFloat64(condVal)
	default:
	}
	return false
}

// NLess ...
func NLess(condVal, reqVal interface{}) bool {
	switch reqVal.(type) {
	case string:
		return util.ToFloat64(reqVal) >= util.ToFloat64(condVal)
	case int:
		return reqVal.(int) >= util.ToInt(condVal)
	case int64:
		return reqVal.(int64) >= util.ToInt64(condVal)
	case float64:
		return reqVal.(float64) >= util.ToFloat64(condVal)
	default:
	}
	return false
}

// Contain return weather variable EXPECT Contains Variable WANT.
func Contain(single, set interface{}) bool {
	switch single.(type) {
	case string:
		return stringSliceIn(toStringSlice(set), single.(string))
	case int:
		return intSliceIn(toIntSlice(set), single.(int))
	case int32:
		return int32SliceIn(toInt32Slice(set), single.(int32))
	case int64:
		return int64SliceIn(toInt64Slice(set), single.(int64))
	case float64:
		return floatSliceIn(toFloat64Slice(set), single.(float64))
	default:
		log.Trace.Errorf(nil, trace.DLTagUndefined, "_msg=unknown type for %v %T", single, single)
		return false
	}
}

func HasIntersection(condSet, reqSet interface{}) bool {
	var (
		arr []interface{}
		d   []byte
		err error
	)

	if d, err = json.Marshal(condSet); err != nil {
		return false
	}

	if err = json.Unmarshal(d, &arr); err != nil {
		return false
	}

	for _, item := range arr {
		if Contain(item, reqSet) {
			return true
		}
	}

	return false
}

// NotContain ...
func NotContain(want, expect interface{}) bool {
	return Contain(want, expect) == false
}

func stringSliceIn(strs []string, s string) bool {
	for i := range strs {
		if strs[i] == s {
			return true
		}
	}
	return false
}

func intSliceIn(strs []int, s int) bool {
	for i := range strs {
		if strs[i] == s {
			return true
		}
	}
	return false
}

func int64SliceIn(strs []int64, s int64) bool {
	for i := range strs {
		if strs[i] == s {
			return true
		}
	}
	return false
}

func int32SliceIn(strs []int32, s int32) bool {
	for i := range strs {
		if strs[i] == s {
			return true
		}
	}
	return false
}

func floatSliceIn(strs []float64, s float64) bool {
	for i := range strs {
		if strs[i] == s {
			return true
		}
	}
	return false
}

// VersionGe ...
func VersionGe(condVal, reqVal interface{}) bool {
	switch reqVal.(type) {
	case string:
		compareResp := util.VersionCompare(reqVal.(string), util.ToString(condVal))
		return compareResp >= 0
	default:
	}
	return false
}

// VersionLe ...
func VersionLe(condVal, reqVal interface{}) bool {
	switch reqVal.(type) {
	case string:
		compareResp := util.VersionCompare(util.ToString(condVal), reqVal.(string))
		return compareResp >= 0
	default:
	}
	return false
}

func toStringSlice(v interface{}) []string {
	var (
		arr []interface{}
		ret []string
	)
	switch v.(type) {
	case []interface{}:
		arr = v.([]interface{})
	default:
		var (
			dd  []byte
			err error
		)
		switch v.(type) {
		case string:
			dd = []byte(v.(string))
		default:
			dd, err = json.Marshal(v)
			if err != nil {
				log.Trace.Errorf(nil, trace.DLTagUndefined, "not slice:%s", string(dd))
				return nil
			}
		}
		err = json.Unmarshal(dd, &arr)
		if err != nil {
			log.Trace.Errorf(nil, trace.DLTagUndefined, "not slice:%v", v)
			return nil
		}
	}
	for i := range arr {
		ret = append(ret, fmt.Sprint(arr[i]))
	}
	return ret
}

func toIntSlice(v interface{}) []int {
	var (
		ints []interface{}
		ret  []int
	)
	switch v.(type) {
	case []interface{}:
		ints = v.([]interface{})
	default:
		var (
			dd  []byte
			err error
		)
		switch v.(type) {
		case string:
			dd = []byte(v.(string))
		default:
			dd, err = json.Marshal(v)
			if err != nil {
				log.Trace.Errorf(nil, trace.DLTagUndefined, "not slice:%s", string(dd))
				return nil
			}
		}
		err = json.Unmarshal(dd, &ints)
		if err != nil {
			log.Trace.Errorf(nil, trace.DLTagUndefined, "not slice:%s err:%v", string(dd), err)
			return nil
		}
	}
	for i := range ints {
		intVal := fmt.Sprint(ints[i])
		it, err := strconv.Atoi(intVal)
		if err != nil {
			log.Trace.Errorf(nil, trace.DLTagUndefined, "_msg=%v", err)
			continue
		}
		ret = append(ret, it)
	}
	return ret
}

func toInt64Slice(v interface{}) []int64 {
	var (
		arr []interface{}
		ret []int64
	)
	switch v.(type) {
	case []interface{}:
		arr = v.([]interface{})
	default:
		var (
			dd  []byte
			err error
		)
		switch v.(type) {
		case string:
			dd = []byte(v.(string))
		default:
			dd, err = json.Marshal(v)
			if err != nil {
				log.Trace.Errorf(nil, trace.DLTagUndefined, "not slice:%s", string(dd))
				return nil
			}
		}
		err = json.Unmarshal(dd, &arr)
		if err != nil {
			log.Trace.Errorf(nil, trace.DLTagUndefined, "not slice:%v", v)
			return nil
		}
	}
	for i := range arr {
		it, err := strconv.ParseFloat(fmt.Sprint(arr[i]), 64)
		if err != nil {
			log.Trace.Errorf(nil, trace.DLTagUndefined, "_msg=%v", err)
			continue
		}
		ret = append(ret, int64(it))
	}
	return ret
}

func toInt32Slice(v interface{}) []int32 {
	var (
		arr []interface{}
		ret []int32
	)
	switch v.(type) {
	case []interface{}:
		arr = v.([]interface{})
	default:
		var (
			dd  []byte
			err error
		)
		switch v.(type) {
		case string:
			dd = []byte(v.(string))
		default:
			dd, err = json.Marshal(v)
			if err != nil {
				log.Trace.Errorf(nil, trace.DLTagUndefined, "not slice:%s", string(dd))
				return nil
			}
		}
		err = json.Unmarshal(dd, &arr)
		if err != nil {
			log.Trace.Errorf(nil, trace.DLTagUndefined, "not slice:%v", v)
			return nil
		}
	}
	for i := range arr {
		it, err := strconv.ParseFloat(fmt.Sprint(arr[i]), 64)
		if err != nil {
			log.Trace.Errorf(nil, trace.DLTagUndefined, "_msg=%v", err)
			continue
		}
		ret = append(ret, int32(it))
	}
	return ret
}

func toFloat64Slice(v interface{}) []float64 {
	var (
		arr []interface{}
		ret []float64
	)
	switch v.(type) {
	case []interface{}:
		arr = v.([]interface{})
	default:
		var (
			dd  []byte
			err error
		)
		switch v.(type) {
		case string:
			dd = []byte(v.(string))
		default:
			dd, err = json.Marshal(v)
			if err != nil {
				log.Trace.Errorf(nil, trace.DLTagUndefined, "not slice:%s", string(dd))
				return nil
			}
		}

		err = json.Unmarshal(dd, &arr)
		if err != nil {
			log.Trace.Errorf(nil, trace.DLTagUndefined, "not slice:%v", v)
			return nil
		}
	}
	for i := range arr {
		it, err := strconv.ParseFloat(fmt.Sprint(arr[i]), 64)
		if err != nil {
			log.Trace.Errorf(nil, trace.DLTagUndefined, "_msg=%v", err)
			continue
		}
		ret = append(ret, it)
	}
	return ret
}
