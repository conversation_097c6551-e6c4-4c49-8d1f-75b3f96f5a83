package fee

import (
	"context"
	"strconv"

	dcmp "git.xiaojukeji.com/dirpc/dirpc-go-dcmp/v2"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type FeeMsgRender struct {
}

func (r *FeeMsgRender) GetName() string {
	return "FeeMsgRender"
}

func (r *FeeMsgRender) Do(ctx context.Context, full *biz_runtime.ProductInfoFull, renderConf map[string]string) string {
	var (
		estimateFee  float64
		currencyUnit string
		fee_msg      string
	)
	if dcmpKey, ok := renderConf["dcmp_key"]; ok {
		if len(full.DiscountInfo) > 0 && full.DiscountInfo[0] != nil {
			estimateFee = full.DiscountInfo[0].EstimateFee
		}
		currencyUnit = full.BillDetail.Currency
		tags := map[string]string{
			"fee":           strconv.FormatFloat(estimateFee, 'f', -1, 64),
			"currency_unit": currencyUnit,
		}
		fee_msg = dcmp.GetContent(ctx, dcmpKey, "zh-CN", tags)
	}
	return fee_msg
}
