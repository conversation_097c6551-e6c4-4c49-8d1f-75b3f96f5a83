package intro_msg

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/reqctx"

	"strconv"

	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"

	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/nuwa/trace"

	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/taxi"

	"github.com/spf13/cast"
)

const nsUNIONEMaterial = "taxi_product_name_conf"

type UniOneCarNameRender struct {
}

func (r *UniOneCarNameRender) Do(ctx context.Context, full *biz_runtime.ProductInfoFull, renderConf map[string]string) string {
	var carName string
	if full == nil || full.Product == nil {
		return carName
	}
	// 出租车品牌更名切量  《快的新出租》 ->  《出租车》
	if carName = getUniOneNewName(full); carName != "" && reqctx.GetLang(ctx) == "zh-CN" && full.Product.ProductID == product_id.ProductIdUniOne {
		return carName
	}

	carName = getUniOneCarNameByCity(ctx, full.BaseReqData.AreaInfo.City, full.Product.ProductCategory)
	if carName == "" {
		carName = getBasicCarName(ctx, full.Product.ProductCategory)
	}

	return carName
}

func getUniOneCarNameByCity(ctx context.Context, cityID int32, productCategory int64) string {
	var carName string
	m := map[string]string{"product_category": strconv.Itoa(int(productCategory)), "city_id": strconv.Itoa(int(cityID))}
	configs, err := apollo.GetConfigsByNamespaceAndConditions(nsUNIONEMaterial, model.NewCondition(m))
	if configs == nil || err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "apollo estimate_formdata_materiel read err with %v and conf %v", err, configs)
		return carName
	}

	allConfig := configs.GetAllConfigs()
	if len(allConfig) > 0 {

		if value, err := allConfig[0].GetStringValue("product_name"); err == nil {
			carName = value
		}
	}
	return carName
}

func getUniOneNewName(full *biz_runtime.ProductInfoFull) string {

	TaxiPNParams := taxi.ConfParams{
		PID:             cast.ToInt(full.BaseReqData.PassengerInfo.PID),
		City:            cast.ToInt(full.BaseReqData.AreaInfo.City),
		ProductCategory: cast.ToInt(full.Product.ProductCategory),
		AccessKeyID:     cast.ToInt(full.BaseReqData.CommonInfo.AccessKeyID),
	}

	return taxi.GetTaxiProductName(TaxiPNParams)
}
