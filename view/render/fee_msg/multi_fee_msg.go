package fee_msg

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"

	"github.com/bitly/go-simplejson"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type MultiFeeMsgRender struct {
}

type FeeMsgData struct {
	FeeMsg      string `json:"fee_msg"`
	EstimateFee string `json:"estimate_fee"`
}

func (m MultiFeeMsgRender) Do(ctx context.Context, full *biz_runtime.ProductInfoFull, renderConf map[string]string) interface{} {
	var (
		err error
		sj  *simplejson.Json
		ret []*FeeMsgData
	)
	if len(full.DiscountInfo) < 2 {
		return nil
	}

	// 拼车两口价V3判断
	if !carpool.IsCarpoolDualPriceV3(int32(full.Product.CarpoolType), full.Product.CarpoolPriceType, full.Product.IsDualCarpoolPrice) {
		return nil
	}

	carpoolSuccessPrice := full.DiscountInfo[0].EstimateFee
	carpoolFailPrice := full.DiscountInfo[1].EstimateFee
	unit, _ := util.GetCurrencyUnitAndSymbol(ctx, full.Product.ProductID, full.BillDetail.Currency)
	params := map[string]string{
		"carpool_fail_price":    util.FormatPrice(carpoolFailPrice, -1),
		"carpool_success_price": util.FormatPrice(carpoolSuccessPrice, -1),
		"currency_unit":         unit,
	}
	if sj, err = dcmp.GetTextObject(ctx, "config_text-p_cancel_estimate", params); err != nil {
		return nil
	}
	// 未拼成
	ret = append(ret, &FeeMsgData{
		EstimateFee: strconv.FormatFloat(carpoolFailPrice, 'f', -1, 64),
		FeeMsg:      sj.Get("carpool_fail_price_desc").MustString(),
	})

	// 拼成
	ret = append(ret, &FeeMsgData{
		EstimateFee: strconv.FormatFloat(carpoolSuccessPrice, 'f', -1, 64),
		FeeMsg:      sj.Get("carpool_success_price_desc").MustString(),
	})
	return ret
}
