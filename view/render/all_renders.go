package render

import (
	"errors"
	"reflect"
	"strings"

	"git.xiaojukeji.com/gulfstream/mamba/view/render/carpool_seat_data"
	"git.xiaojukeji.com/gulfstream/mamba/view/render/match_routes_data"

	"git.xiaojukeji.com/gulfstream/mamba/view/render/sub_title_list"

	"git.xiaojukeji.com/gulfstream/mamba/view/render/price_info_desc_list"

	"git.xiaojukeji.com/gulfstream/mamba/view/render/fee_msg"
	"git.xiaojukeji.com/gulfstream/mamba/view/render/intro_msg"
	"git.xiaojukeji.com/gulfstream/mamba/view/render/seat_num"

	"git.xiaojukeji.com/gulfstream/mamba/view/render/common"
	"git.xiaojukeji.com/gulfstream/mamba/view/render/fee"
	"git.xiaojukeji.com/gulfstream/mamba/view/render/pay"
	"git.xiaojukeji.com/gulfstream/mamba/view/render/selection"
)

var LocalRenderMap map[string]RenderUnion

var AllStringRenders = []StringRender{
	&common.PlainText{},
	&fee.FeeMsgRender{},
	&fee.CarpoolFeeMsg{},
	&common.DcmpPlainText{},
	&intro_msg.CarNameRender{},
	&intro_msg.UniOneCarNameRender{},
	&intro_msg.CarIconRender{},
	&fee_msg.CarpoolDualPriceFeeMsgRender{},
	&fee_msg.CapPriceFeeMsgRender{},
	&fee_msg.UniOneFeeMsgRender{},
	&fee_msg.CommonFeeMsgRender{},
}

var allIntRenders = []IntRender{
	&selection.ReBubbleSelection{},
}

var float64Renders = []Float64Render{
	&fee.PriceRender{},
}

var iRenders = []IRender{
	&pay.MixedPayMsgRender{},
	&pay.PayInfoRender{},
	&seat_num.CarpoolSeatNumRender{},
	&price_info_desc_list.CarpoolFailPriceExtraRender{},
	&price_info_desc_list.UniOnePriceDescRender{},
	&price_info_desc_list.PriceDescRender{},
	&price_info_desc_list.HKTaxiPriceDescRender{},
	&sub_title_list.APlusSubTitleList{},
	&carpool_seat_data.CarpoolSeatDataRender{},
	&match_routes_data.MatchRoutesDataRender{},
	&price_info_desc_list.CombinedTravelPriceDescRender{},
}

func Init() error {
	var (
		rName string
		err   error
	)

	LocalRenderMap = make(map[string]RenderUnion, 0)

	for _, r := range AllStringRenders {
		if rName, err = getRenderName(r); err != nil {
			return err
		}

		if _, exist := LocalRenderMap[rName]; exist {
			return errors.New("duplicate r with same name")
		}
		LocalRenderMap[rName] = RenderUnion{
			_type:        StringRenderTag,
			StringRender: r,
			IntRender:    nil,
		}
	}

	for _, r := range allIntRenders {
		if rName, err = getRenderName(r); err != nil {
			return err
		}
		if _, exist := LocalRenderMap[rName]; exist {
			return errors.New("duplicate r with same name")
		}
		LocalRenderMap[rName] = RenderUnion{
			_type:        IntRenderTag,
			StringRender: nil,
			IntRender:    r,
		}
	}

	for _, r := range float64Renders {
		if rName, err = getRenderName(r); err != nil {
			return err
		}
		if _, exist := LocalRenderMap[rName]; exist {
			return errors.New("duplicate r with same name")
		}
		LocalRenderMap[rName] = RenderUnion{
			_type:         Float64RenderTag,
			Float64Render: r,
		}
	}

	for _, r := range iRenders {
		if rName, err = getRenderName(r); err != nil {
			return err
		}
		if _, exist := LocalRenderMap[rName]; exist {
			return errors.New("duplicate r with same name")
		}
		LocalRenderMap[rName] = RenderUnion{
			_type:   IRenderTag,
			IRender: r,
		}
	}

	return nil
}

func getRenderName(iRender interface{}) (name string, err error) {
	if strList := strings.Split(reflect.TypeOf(iRender).String(), "."); len(strList) == 2 {
		name = strList[1]
	} else {
		err = errors.New("invalid render name")
	}
	return
}
