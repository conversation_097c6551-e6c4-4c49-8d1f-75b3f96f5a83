package sort_product

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/view/render"
	"git.xiaojukeji.com/gulfstream/tripcloud-common-go/utils/apollo"
	"git.xiaojukeji.com/nuwa/trace"
)

const (
	sortListConfNameSpace = "products_sort_conf"
	configKey             = "products_sort"
)

type StaticSortList struct {
}

type SortListConf struct {
	ProductCategory models.ProductCategory `json:"product_category"`
}

func (sl *StaticSortList) Do(ctx context.Context, products []render.AbstractProduct, renderConf map[string]string) []models.ProductCategory {
	var (
		err        error
		sortedConf []*SortListConf
		sortedList = make([]models.ProductCategory, 0)
	)

	sortFileName, ok := renderConf["file_name"]
	if !ok || sortFileName == "" {
		return nil
	}
	config, err := apollo.GetApolloConfig(sortListConfNameSpace, sortFileName)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "get sorted list conf fail with err %v", err)
		return nil
	}
	sortedConf = make([]*SortListConf, 0)
	if confStr, ok := config.GetValue(configKey); ok {
		err = json.Unmarshal([]byte(confStr), &sortedConf)
		if err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "unmarshal sorted list conf fail with err %v", err)
			return nil
		}
		for _, p := range sortedConf {
			sortedList = append(sortedList, p.ProductCategory)
		}
	}

	return sortedList
}
