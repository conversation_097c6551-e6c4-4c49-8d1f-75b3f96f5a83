package pay

import (
	"context"
	"fmt"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/nuwa/trace"
)

type MixedPayMsgRender struct {
}

// TODO 确定值获取逻辑
func (m *MixedPayMsgRender) Do(ctx context.Context, full *biz_runtime.ProductInfoFull, renderConf map[string]string) interface{} {

	if len(full.DiscountInfo) == 0 || full.DiscountInfo[0].MixedPayDeductInfo == nil || full.BillDetail == nil {
		return nil
	}
	var (
		actualPayFee, needPayFee, deductFee float64
	)
	deductFee = full.DiscountInfo[0].MixedPayDeductInfo.DeductFee
	needPayFee = full.DiscountInfo[0].EstimateFee
	if needPayFee < deductFee {
		actualPayFee = 0
	} else {
		actualPayFee = needPayFee - deductFee
	}

	unit, symbol := util.GetCurrencyUnitAndSymbol(ctx, full.Product.ProductID, full.BillDetail.Currency)
	params := map[string]string{
		"fee":             util.FormatPrice(actualPayFee, -1),
		"sub_fee":         util.FormatPrice(deductFee, -1),
		"currency_unit":   unit,
		"currency_symbol": symbol,
	}
	sj1, err := dcmp.GetTextObject(ctx, "guide_anycar-payment_type_mixed_fee_msg", params)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "new json fail: %v", err)
		return nil
	}
	key := fmt.Sprintf("m_%d_%d", full.DiscountInfo[0].MixedPayDeductInfo.CarPayType, full.DiscountInfo[0].MixedPayDeductInfo.MixedPayType)
	if sj2, ok := sj1.CheckGet(key); !ok {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "checkGetMissed")
		return nil
	} else {
		sj3, err := sj2.Get("fee_msg").String()
		if err != nil {
			return nil
		}
		sj4, err := sj2.Get("sub_fee_msg").String()
		if err != nil {
			return nil
		}
		return &proto.MixedFeeMsg{
			FeeMsg:    sj3,
			SubFeeMsg: sj4,
		}
	}
}
