package carpool_seat_data

import (
	"context"
	"fmt"

	"git.xiaojukeji.com/gulfstream/biz-lib-go/utils/tagreplace"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/bitly/go-simplejson"
)

// CarpoolSeatDataRender 选座配置
type CarpoolSeatDataRender struct {
}

func (r *CarpoolSeatDataRender) GetName() string {
	return "CarpoolSeatDataRender"
}

func (r *CarpoolSeatDataRender) Do(ctx context.Context, productInfoFull *biz_runtime.ProductInfoFull, renderConf map[string]string) interface{} {

	seatConfig := productInfoFull.BillDetail.CarpoolSeatConfig // [1,2,3]
	if seatConfig == nil {
		return nil
	}

	// 预估默认构建选中座位
	var carpoolSeatRes = &proto.CarpoolSeatData{}

	seatNum := productInfoFull.Product.BizInfo.CarpoolSeatNum
	if seatNum <= 0 {
		return nil
	} else if int(seatNum) > len(seatConfig) {
		carpoolSeatRes.SelectValue = int32(len(seatConfig))
	} else {
		carpoolSeatRes.SelectValue = seatNum
	}

	carpoolSeats, err := buildCarpoolSeats(ctx, len(seatConfig))
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "err:%v", err)
		return nil
	}
	carpoolSeatRes.SeatConfig = carpoolSeats

	carpoolSeatRes.SelectIndex = carpoolSeatRes.SelectValue - 1 //选中座位下次预估默认的构建

	return carpoolSeatRes
}

func buildCarpoolSeats(ctx context.Context, carpoolSeatNum int) (seatList []*proto.HelperSeatConfig, err error) {
	config := dcmp.GetDcmpContent(ctx, "config_text-pget_order_match_info", nil)
	jsonConfig, err := simplejson.NewJson([]byte(config))
	if err != nil {
		return
	}
	textModel, err := jsonConfig.Get("guide_carpool_confirm_seat_num").String()
	if err != nil {
		return
	}
	tr := tagreplace.NewDefaultTagReplacer()
	for i := 1; i <= carpoolSeatNum; i++ {
		seatList = append(seatList, &proto.HelperSeatConfig{
			Value: int32(i),
			Lable: tr.ReplaceTag(textModel, map[string]string{"num": fmt.Sprint(i)}),
		})
	}
	return
}
