package match_routes_data

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	trace "git.xiaojukeji.com/lego/context-go"
)

type MatchRoutesDataRender struct {
}

func (r *MatchRoutesDataRender) Do(ctx context.Context, productFull *biz_runtime.ProductInfoFull, renderConf map[string]string) interface{} {

	routeInfo := productFull.Product.BizInfo.RouteInfo
	if routeInfo == nil || len(routeInfo.TimeSpan) == 0 {
		return nil
	}

	textConfig, err := dcmp.GetTextObject(ctx, "carpool_config-match_routes", nil)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "get carpool_config-match_routes err :%v", err)
		return nil
	}

	matchRoutesData := &proto.MatchRoutesData{
		TimeTitle: textConfig.Get("time_title").MustString(),
		SubTitle:  textConfig.Get("sub_title").MustString(),
		LeftText:  textConfig.Get("left_text").MustString(),
		RightText: textConfig.Get("right_text").MustString(),
		TimeSpan:  []*proto.TimeSpan{},
	}
	err = json.Unmarshal([]byte(util.JustJsonEncode(routeInfo.TimeSpan)), &matchRoutesData.TimeSpan)
	if err != nil {
		return nil
	}
	if productFull.GetBizInfo() != nil && productFull.GetBizInfo().IntercitySkuInfo != nil {
		skuInfo := productFull.GetBizInfo().IntercitySkuInfo
		for _, v := range matchRoutesData.TimeSpan {
			for _, time := range v.Range {
				if time == nil {
					continue
				}
				if value, status := skuInfo[time.Value]; status {
					time.RemainSeats = int32(value.Seat)
				}
			}
		}
	}

	return matchRoutesData
}
