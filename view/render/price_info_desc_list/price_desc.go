package price_info_desc_list

import (
	"context"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

// PriceDescRender 普通品类价格描述构建
type PriceDescRender struct {
}

const (
	maxCountLimit = 2 // 价格描述最多两个位置
)

func (r *PriceDescRender) Do(ctx context.Context, full *biz_runtime.ProductInfoFull, renderConf map[string]string) interface{} {
	var (
		descListResult []*proto.AnyCarPriceDescInfo
	)

	if priceDescList := getIncrementItems(ctx, full, 2); len(priceDescList) > 0 {
		for _, priceDesc := range priceDescList {
			priceDescInfo := &proto.AnyCarPriceDescInfo{
				Content:  priceDesc.content,
				LeftIcon: priceDesc.leftIcon,
			}
			descListResult = append(descListResult, priceDescInfo)
		}
	}

	if len(descListResult) >= maxCountLimit {
		// 加价项数量超过限制, 不再展示减价项
		return descListResult
	}

	leftCount := maxCountLimit - len(descListResult) // 加价项没超过限制 计算剩余位置的数量

	decrementFunctions := r.getDecrementFunctions(full)

	if priceDescList := getDecrementItems(ctx, full, leftCount, decrementFunctions...); len(priceDescList) > 0 {
		for _, desc := range priceDescList {
			priceDescInfo := &proto.AnyCarPriceDescInfo{
				Content:  desc.content,
				LeftIcon: desc.leftIcon,
			}
			descListResult = append(descListResult, priceDescInfo)
		}
	}

	return descListResult
}

func (r *PriceDescRender) getDecrementFunctions(full *biz_runtime.ProductInfoFull) []DecrementFunc {
	var decrementFunctions = []DecrementFunc{
		getCouponDescAndIcon,
		getRewardsDescAndIcon,
	}

	if full.Product.ProductCategory == estimate_pc_id.EstimatePcIdPremiumComfort {
		decrementFunctions = append(decrementFunctions, getPricePrivilegeDescLite)
	}
	return decrementFunctions
}
