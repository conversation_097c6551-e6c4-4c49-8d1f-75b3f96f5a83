package price_info_desc_list

import (
	"context"
	"encoding/json"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/view/common_view_logic"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	trace "git.xiaojukeji.com/lego/context-go"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
)

const (
	DiscountTypeCoupon  = "coupon" // type
	DiscountTypeReward  = "reward"
	RedPacketFee        = "red_packet"
	DiscountTypeHKBonus = "hkbonus"

	CouponTagNewCategory       = "MPT_NEW_CATEGORY" // tag
	CommonPriceDCMPKey         = "common-price_extra_info"
	TaxiPeakFeeKey             = "taxi_peak_fee"
	TaxiPeakFeeWithoutEstimate = "taxi_peak_fee_without_estimate"
)

// DecrementFunc 定义减价项方法
type DecrementFunc func(ctx context.Context, full *biz_runtime.ProductInfoFull) *PriceDesc

type PriceDesc struct {
	amount   float64
	content  string
	leftIcon string
}

type customTagMap struct {
	Content         string `json:"content"`
	Icon            string `json:"icon"`
	ProductCategory []int  `json:"product_category"`
	MaxDeduction    int    `json:"max_deduction"`
}

// getIncrementItems 构建加价项
func getIncrementItems(ctx context.Context, full *biz_runtime.ProductInfoFull, num int) []*PriceDesc {
	var (
		decItems []*PriceDesc
	)

	if redPacketFeePriceDesc := getRedPacketFee(ctx, full); redPacketFeePriceDesc != nil && len(redPacketFeePriceDesc.content) > 0 {
		decItems = append(decItems, redPacketFeePriceDesc)
	}

	for _, displayLine := range full.BillDetail.DisplayLines {
		if displayLine.Name == rpc_process.BillItemName {
			taxiPeakFeeDescInfo := getTaxiPeakFeeItem(ctx, displayLine.Value, full)
			decItems = append(decItems, taxiPeakFeeDescInfo)
			break
		}
	}

	if num >= len(decItems) {
		//暂时未处理加价项过多  返回数量需要小于等于 num int
		return decItems
	}

	return decItems
}

// getDecrementItems 构建减价项
func getDecrementItems(ctx context.Context, full *biz_runtime.ProductInfoFull, num int, functionList ...DecrementFunc) []PriceDesc {
	var (
		decItems    []PriceDesc
		totalAmount float64
	)

	for _, function := range functionList {
		// 遍历需要执行的减价项
		if priceDesc := function(ctx, full); priceDesc != nil && priceDesc.content != "" {
			totalAmount = totalAmount + priceDesc.amount
			decItems = append(decItems, *priceDesc)
		}
	}

	if num >= len(decItems) {
		return decItems
	}

	// 优惠共抵
	if totalDiscountDesc := buildTotalDiscount(ctx, full, totalAmount); totalDiscountDesc != nil {
		return []PriceDesc{*totalDiscountDesc}
	}

	return decItems
}

func getCouponDescAndIcon(ctx context.Context, full *biz_runtime.ProductInfoFull) (priceDesc *PriceDesc) {
	var (
		couponAmount float64
		hitCustomTag bool
		customTag    string
	)

	for _, discount := range full.DiscountInfo[0].DiscountDesc {
		if discount.CustomTag != nil && *discount.CustomTag != "" {
			couponAmount = discount.Amount
			customTag = *discount.CustomTag
			hitCustomTag = true
			break
		}

		if discount.Type == DiscountTypeCoupon {
			couponAmount = discount.Amount
			break
		}
	}
	if couponAmount == 0 {
		return
	}

	priceDesc = &PriceDesc{
		amount: couponAmount,
	}
	if page_type.PageTypeGuideAnyCar == full.BaseReqData.CommonInfo.PageType {
		if content := dcmp.GetSubContent(ctx, "common-price_extra_info", "coupon_desc"); content != "" {
			tag := map[string]string{
				"amount": util.FormatPrice(couponAmount, -1),
			}
			priceDesc.content, priceDesc.leftIcon = util.ReplaceTag(ctx, content, tag), ""
		}

		if hitCustomTag {
			// 命中custom_tag，如果有对应dcmp，则替换。
			var tagMap customTagMap
			config := dcmp.GetJSONContentWithPath(ctx, "common-price_extra_info_custom_tag", nil, customTag)
			err := json.Unmarshal([]byte(config), &tagMap)
			if err != nil {
				return
			}

			content, icon, pcList, maxDeduction := tagMap.Content, tagMap.Icon, tagMap.ProductCategory, tagMap.MaxDeduction
			pcMap := make(map[int]string)
			for _, value := range pcList {
				pcMap[value] = ""
			}

			if len(pcList) > 0 {
				// 限制特定车型展示
				if _, ok := pcMap[int(full.Product.ProductCategory)]; ok {
					if (maxDeduction == 0) || (maxDeduction != 0 && couponAmount < float64(maxDeduction)) {
						// 目前只有特快新客用到maxDeduction
						tag := map[string]string{
							"amount": util.FormatPrice(couponAmount, -1),
						}
						priceDesc.content, priceDesc.leftIcon = util.ReplaceTag(ctx, content, tag), icon
					}
				}
			} else {
				tag := map[string]string{
					"amount": util.FormatPrice(couponAmount, -1),
				}
				priceDesc.content, priceDesc.leftIcon = util.ReplaceTag(ctx, content, tag), icon
			}
		}
	}
	return
}

func getRewardsDescAndIcon(ctx context.Context, full *biz_runtime.ProductInfoFull) (priceDesc *PriceDesc) {
	var (
		rewardsAmount float64
	)
	for _, discount := range full.DiscountInfo[0].DiscountDesc {
		if discount.Type == DiscountTypeReward {
			rewardsAmount = discount.Amount
			break
		}
	}
	if rewardsAmount == 0 {
		return
	}

	priceDesc = &PriceDesc{
		amount: rewardsAmount,
	}

	if page_type.PageTypeGuideAnyCar == full.BaseReqData.CommonInfo.PageType {
		if content := dcmp.GetSubContent(ctx, "common-price_extra_info", "rewards_title"); content != "" {
			unit, symbol := util.GetCurrencyUnitAndSymbol(ctx, full.Product.ProductID, full.BillDetail.Currency)
			tag := map[string]string{
				"currency_symbol": symbol,
				"num":             util.FormatPrice(rewardsAmount, -1),
				"currency_unit":   unit,
			}
			priceDesc.content, priceDesc.leftIcon = util.ReplaceTag(ctx, content, tag), ""
		}
	}

	return
}

func getHKBonusDescAndIcon(ctx context.Context, full *biz_runtime.ProductInfoFull) (priceDesc *PriceDesc) {
	var (
		bonusAmount float64
	)
	for _, discount := range full.DiscountInfo[0].DiscountDesc {
		if discount.Type == DiscountTypeHKBonus {
			bonusAmount = discount.Amount
			break
		}
	}
	if bonusAmount == 0 {
		return
	}

	priceDesc = &PriceDesc{
		amount: bonusAmount,
	}

	if page_type.PageTypeGuideAnyCar == full.BaseReqData.CommonInfo.PageType {
		if content := dcmp.GetSubContent(ctx, "common-price_extra_info_hk", "bonus_title"); content != "" {
			unit, symbol := util.GetCurrencyUnitAndSymbol(ctx, full.Product.ProductID, full.BillDetail.Currency)
			tag := map[string]string{
				"currency_symbol": symbol,
				"num":             util.FormatPrice(bonusAmount, -1),
				"currency_unit":   unit,
			}
			icon := dcmp.GetSubContent(ctx, "common-price_extra_info_hk", "bonus_icon")
			priceDesc.content, priceDesc.leftIcon = util.ReplaceTag(ctx, content, tag), icon
		}
	}

	return
}

func getHKCouponDescAndIcon(ctx context.Context, full *biz_runtime.ProductInfoFull) (priceDesc *PriceDesc) {
	var (
		couponAmount float64
	)

	for _, discount := range full.DiscountInfo[0].DiscountDesc {
		if discount.Type == DiscountTypeCoupon {
			couponAmount = discount.Amount
			break
		}
	}
	if couponAmount == 0 {
		return
	}

	priceDesc = &PriceDesc{
		amount: couponAmount,
	}
	if page_type.PageTypeGuideAnyCar == full.BaseReqData.CommonInfo.PageType {
		if content := dcmp.GetSubContent(ctx, "common-price_extra_info_hk", "coupon_desc"); content != "" {
			tag := map[string]string{
				"amount": util.FormatPrice(couponAmount, -1),
			}
			icon := dcmp.GetSubContent(ctx, "common-price_extra_info_hk", "coupon_icon")
			priceDesc.content, priceDesc.leftIcon = util.ReplaceTag(ctx, content, tag), icon
		}
	}
	return
}

func getPricePrivilegeDescLite(ctx context.Context, full *biz_runtime.ProductInfoFull) (priceDesc *PriceDesc) {

	if full.BillDetail.PricePrivilegeType == nil || biz_runtime.PricePrivilegeTypeFastUpDefault != *full.BillDetail.PricePrivilegeType {
		return nil
	}
	baseReq := full.BaseReqData
	product := full.Product
	params := map[string]string{
		"key":           strconv.FormatInt(baseReq.PassengerInfo.PID, 10),
		"passenger_id":  strconv.FormatInt(baseReq.PassengerInfo.PID, 10),
		"city":          strconv.FormatInt(int64(baseReq.AreaInfo.City), 10),
		"access_key_id": strconv.FormatInt(int64(baseReq.CommonInfo.AccessKeyID), 10),
		"lang":          baseReq.CommonInfo.Lang,
		"require_level": product.RequireLevel,
		"product_id":    strconv.FormatInt(product.ProductID, 10),
		"type":          strconv.FormatInt(int64(product.OrderType), 10),
		"app_version":   baseReq.CommonInfo.AppVersion,
		"from":          "pre_sale_price_info_desc",
	}
	// 是否展示打专车快车价
	if !apollo.FeatureToggle(ctx, biz_runtime.ApolloShowSwitch, strconv.FormatInt(baseReq.PassengerInfo.UID, 10), params) {
		return nil
	}

	priceDesc = &PriceDesc{}
	if content := dcmp.GetSubContent(ctx, "common-price_extra_info", "price_privilege_desc"); content != "" {
		priceDesc.content, priceDesc.leftIcon = content, ""
		return
	}
	return nil
}

func buildTotalDiscount(ctx context.Context, full *biz_runtime.ProductInfoFull, totalAmount float64) (priceDesc *PriceDesc) {
	if content := dcmp.GetSubContent(ctx, "common-price_extra_info", "discount_title"); content != "" {
		priceDesc = &PriceDesc{}
		unit, symbol := util.GetCurrencyUnitAndSymbol(ctx, full.Product.ProductID, full.BillDetail.Currency)
		tag := map[string]string{
			"currency_symbol": symbol,
			"num":             util.FormatPrice(totalAmount, -1),
			"currency_unit":   unit,
		}
		priceDesc.content, priceDesc.leftIcon = util.ReplaceTag(ctx, content, tag), ""
	}
	return
}

// getRedPacketFee 加价项:春节服务费
func getRedPacketFee(ctx context.Context, full *biz_runtime.ProductInfoFull) *PriceDesc {
	if redPacketFee := GetValueFromDisplayLines(full, RedPacketFee); redPacketFee > 0 {
		if content := dcmp.GetSubContent(ctx, "common-price_extra_info", "red_packet_desc"); content != "" {
			tag := map[string]string{
				"amount": util.FormatPrice(redPacketFee, -1),
			}
			return &PriceDesc{
				amount:   redPacketFee,
				content:  util.ReplaceTag(ctx, content, tag),
				leftIcon: "",
			}
		} else {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "common-price_extra_info red_packet_desc not found")
		}
	}
	return nil
}

// GetValueFromDisplayLines 根据key获取display_lines的value
func GetValueFromDisplayLines(full *biz_runtime.ProductInfoFull, key string) float64 {
	if full.BillDetail != nil && len(full.BillDetail.DisplayLines) > 0 {
		for _, displayLine := range full.BillDetail.DisplayLines {
			if key == displayLine.Name {
				return displayLine.Value
			}
		}
	}
	return 0
}

func getTaxiPeakFeeItem(ctx context.Context, fee float64, full *biz_runtime.ProductInfoFull) *PriceDesc {
	var decItems *PriceDesc
	var defaultTaxiPeakFeeKey = TaxiPeakFeeKey

	//若命中展示打表计价
	if !common_view_logic.CheckTaxiAppendShowEstimateFee(ctx, full) {
		defaultTaxiPeakFeeKey = TaxiPeakFeeWithoutEstimate
	}

	if content := dcmp.GetSubContent(ctx, CommonPriceDCMPKey, defaultTaxiPeakFeeKey); content != "" {
		tag := map[string]string{
			"num": cast.ToString(fee),
		}
		return &PriceDesc{
			amount:   fee,
			content:  util.ReplaceTag(ctx, content, tag),
			leftIcon: "",
		}

	}

	return decItems
}
