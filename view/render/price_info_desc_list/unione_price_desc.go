package price_info_desc_list

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/view/common_view_logic"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

// UniOnePriceDescRender 出租车价格描述渲染
type UniOnePriceDescRender struct {
}

func (r *UniOnePriceDescRender) Do(ctx context.Context, full *biz_runtime.ProductInfoFull, renderConf map[string]string) interface{} {
	var descList = make([]*proto.AnyCarPriceDescInfo, 0)

	if priceDescList := getIncrementItems(ctx, full, 2); len(priceDescList) > 0 {
		for _, descInfo := range priceDescList {
			priceDescInfo := &proto.AnyCarPriceDescInfo{
				Content:  descInfo.content,
				LeftIcon: descInfo.leftIcon,
			}
			descList = append(descList, priceDescInfo)
		}

	}

	if len(descList) >= maxCountLimit {
		// 加价项数量超过限制, 不再展示减价项
		return descList
	}
	leftCount := maxCountLimit - len(descList) // 加价项没超过限制 计算剩余位置的数量

	var decrementFunctions = []DecrementFunc{
		getCouponDescAndIcon,
		getRewardsDescAndIcon,
	}
	if priceDescList := getDecrementItems(ctx, full, leftCount, decrementFunctions...); len(priceDescList) > 0 {
		for _, desc := range priceDescList {
			priceDescInfo := &proto.AnyCarPriceDescInfo{
				Content:  desc.content,
				LeftIcon: desc.leftIcon,
			}
			descList = append(descList, priceDescInfo)
		}
	}

	//若命中展示预估价，则将打表计价挪到第二行
	if common_view_logic.CheckTaxiAppendShowEstimateFee(ctx, full) {
		priceDesc := dcmp.GetDcmpContent(ctx, "unione-price_desc_without_price", nil)
		if len(descList) <= 0 {
			priceDescInfo := &proto.AnyCarPriceDescInfo{Content: priceDesc}
			descList = append(descList, priceDescInfo)
		} else {
			descList[0].Content = priceDesc + ", " + descList[0].Content
		}
	}

	return descList
}
