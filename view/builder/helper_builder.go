package builder

import (
	"context"
	"errors"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/view/selector"
)

type HelperEstimateBuilder struct {
	nodesConf    selector.StaticNodesConf
	fieldNodeMap map[string]*selector.BizNode
}

func NewHelperBuilder() (*HelperEstimateBuilder, error) {
	builder := &HelperEstimateBuilder{}
	conf := selector.AllPageFieldConfig["proto.HelperEstimate"]
	if conf == nil {
		return nil, errors.New("no invalid conf")
	}
	builder.nodesConf = conf
	return builder, nil
}

func (b *HelperEstimateBuilder) Build(ctx context.Context, productInfo *biz_runtime.ProductInfoFull) error {
	var (
		allHitNodes = make(map[string]*selector.BizNode)
		baseInput   map[string]interface{}
	)

	if productInfo == nil {
		return errEmptyProductInfo
	}

	baseInput = productInfo.GenSelectorInput()
	if baseInput == nil {
		return errInvalidSelectorInput
	}

	b.fieldNodeMap = make(map[string]*selector.BizNode)
	for field, confList := range b.nodesConf {
		for _, conf := range confList {
			node := selector.NewBizNode(conf)
			if node.IsHit(ctx, baseInput) {
				if n, ok := allHitNodes[node.GetHStr()]; ok {
					b.fieldNodeMap[field] = n
				} else {
					allHitNodes[node.GetHStr()] = node
					b.fieldNodeMap[field] = node
				}
				break
			}
		}
	}

	for _, node := range allHitNodes {
		node.Run(ctx, productInfo)
	}

	return nil
}

func (b *HelperEstimateBuilder) GetString(key string) string {
	if node, exist := b.fieldNodeMap[key]; exist {
		v := node.GetFieldsValue()
		if vv, ok := v.(string); ok {
			return vv
		}
	}

	return ""
}

func (b *HelperEstimateBuilder) GetInt32(key string) int32 {
	if node, exist := b.fieldNodeMap[key]; exist {
		v := node.GetFieldsValue()
		if vv, ok := v.(int); ok {
			return int32(vv)
		}
	}
	return 0
}

func (b *HelperEstimateBuilder) GetInt64(key string) int64 {
	if node, exist := b.fieldNodeMap[key]; exist {
		v := node.GetFieldsValue()
		if vv, ok := v.(int); ok {
			return int64(vv)
		}
	}
	return 0
}

func (b *HelperEstimateBuilder) GeFloat64(key string) float64 {
	if node, exist := b.fieldNodeMap[key]; exist {
		v := node.GetFieldsValue()
		if vv, ok := v.(float64); ok {
			return vv
		}
	}
	return 0
}

func (b *HelperEstimateBuilder) GetPriceDesc() string {
	if node, exist := b.fieldNodeMap["price_desc"]; exist {
		v := node.GetFieldsValue()
		if vv, ok := v.(string); ok {
			return vv
		}
	}
	return ""
}

func (b *HelperEstimateBuilder) GetCarpoolSeatData() *proto.CarpoolSeatData {
	if node, exist := b.fieldNodeMap["carpool_seat_data"]; exist {
		v := node.GetFieldsValue()
		if vv, ok := v.(*proto.CarpoolSeatData); ok {
			return vv
		}
	}
	return nil
}

func (b *HelperEstimateBuilder) GetMatchRoutesData() *proto.MatchRoutesData {
	if node, exist := b.fieldNodeMap["match_routes_data"]; exist {
		v := node.GetFieldsValue()
		if vv, ok := v.(*proto.MatchRoutesData); ok {
			return vv
		}
	}
	return nil
}
