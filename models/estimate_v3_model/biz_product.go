package estimate_v3_model

import PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"

type BizProductMap = map[string]*BizProduct

type BizProduct struct {
	OProduct   OProduct                      `json:"oProduct"`
	OPriceInfo *PriceApi.EstimateNewFormData `json:"oPriceInfo"`
}

type OrderInfo struct {
	SEstimateID            string      `json:"sEstimateID"`
	IOrderType             int         `json:"iOrderType"`
	SMenuID                string      `json:"sMenuID"`
	IPageType              int         `json:"iPageType"`
	IOriginPageType        int         `json:"iOriginPageType"`
	ICarpoolSeatNum        int         `json:"iCarpoolSeatNum"`
	IProductCategory       int64       `json:"iProductCategory"`
	IProductId             int         `json:"iProductId"`
	IBusinessId            int         `json:"iBusinessId"`
	IRequireLevel          int         `json:"iRequireLevel"`
	IComboType             int         `json:"iComboType"`
	ICarpoolType           int         `json:"iCarpoolType"`
	IIsSpecialPrice        bool        `json:"iIsSpecialPrice"`
	ILevelType             int         `json:"iLevelType"`
	ISpaciousCarAlliance   int         `json:"iSpaciousCarAlliance"`
	IAirportType           int         `json:"iAirportType"`
	IBlindType             int         `json:"iBlindType"`
	IDisabilityType        int         `json:"iDisabilityType"`
	ICarpoolPriceType      int         `json:"iCarpoolPriceType"`
	BIsDualCarpoolPrice    bool        `json:"bIsDualCarpoolPrice"`
	IRailwayType           int         `json:"iRailwayType"`
	IHotelType             int         `json:"iHotelType"`
	IRouteType             int         `json:"iRouteType"`
	IIsExtraStop           interface{} `json:"iIsExtraStop"`
	IExamType              int         `json:"iExamType"`
	AMiniBusInfo           interface{} `json:"aMiniBusInfo"`
	ACommuteCardInfo       interface{} `json:"aCommuteCardInfo"`
	AMatchRoutes           interface{} `json:"aMatchRoutes"`
	IComboId               interface{} `json:"iComboId"`
	IComboIds              interface{} `json:"iComboIds"`
	SDepartureRange        string      `json:"sDepartureRange"`
	IDepartureTimeEarliest interface{} `json:"iDepartureTimeEarliest"`
	IBubbleTime            interface{} `json:"iBubbleTime"`
	BRecognitionResult     bool        `json:"bRecognitionResult"`
	AStationList           interface{} `json:"aStationList"`
	ACarpoolExtraInfo      interface{} `json:"aCarpoolExtraInfo"`
	AActivityInfo          interface{} `json:"aActivityInfo"`
	SPaymentsType          int         `json:"sPaymentsType"`
	IDepartureTime         int         `json:"iDepartureTime"`
	ICallCarType           int         `json:"iCallCarType"`
	SCallCarPhone          string      `json:"sCallCarPhone"`
	IFormShowType          int         `json:"iFormShowType"`
	SFlightDepCode         string      `json:"sFlightDepCode"`
	SFlightDepTerminal     string      `json:"sFlightDepTerminal"`
	STrafficDepTime        string      `json:"sTrafficDepTime"`
	SFlightArrCode         string      `json:"sFlightArrCode"`
	SFlightArrTerminal     string      `json:"sFlightArrTerminal"`
	STrafficArrTime        string      `json:"sTrafficArrTime"`
	STrafficNumber         string      `json:"sTrafficNumber"`
	IAirportId             string      `json:"iAirportId"`
	IShiftTime             int         `json:"iShiftTime"`
	IOType                 int         `json:"iOType"`
	ICouponType            int         `json:"iCouponType"`
	ISceneType             int         `json:"iSceneType"`
	IActivityId            int         `json:"iActivityId"`
	IBizTicket             string      `json:"iBizTicket"`
	ISpecialSceneParam     int         `json:"iSpecialSceneParam"`
	IStationServiceControl int         `json:"iStationServiceControl"`
	SDesignatedDriver      interface{} `json:"sDesignatedDriver"`
	SDesignatedDriverTag   interface{} `json:"sDesignatedDriverTag"`
	ADesignatedDriverInfo  interface{} `json:"aDesignatedDriverInfo"`
	// APersonalizedCustomOption struct {
	// } `json:"aPersonalizedCustomOption"`
	AQueueData interface{} `json:"aQueueData"`
	// AFenceInfo struct {
	// 	Airport struct {
	// 		FenceIds  []int `json:"fence_ids"`
	// 		SceneType int   `json:"scene_type"`
	// 		GroupId   int   `json:"group_id"`
	// 	} `json:"airport"`
	// 	Railway interface{} `json:"railway"`
	// 	Hotel   interface{} `json:"hotel"`
	// } `json:"aFenceInfo"`
	IStartingTag        int         `json:"iStartingTag"`
	IDestTag            int         `json:"iDestTag"`
	AStopoverPoints     interface{} `json:"aStopoverPoints"`
	IIsPickOnTime       interface{} `json:"iIsPickOnTime"`
	APricePrivilegeInfo interface{} `json:"aPricePrivilegeInfo"`
	STripCountry        string      `json:"sTripCountry"`
	BNeedGuideInfo      interface{} `json:"bNeedGuideInfo"`
	IIsTripCloud        interface{} `json:"iIsTripCloud"`
}

type OProduct struct {
	OOrderInfo OrderInfo `json:"oOrderInfo"`
	// AMemberPrivilegeList []interface{} `json:"aMemberPrivilegeList"`
	// ACustomFeature       []interface{} `json:"aCustomFeature"`
	// AOptionInfo          interface{}   `json:"aOptionInfo"`
	// OUsageInfo           interface{}   `json:"oUsageInfo"`
	// BDisabled            bool          `json:"bDisabled"`
	// AExtraInfo           []interface{} `json:"aExtraInfo"`
}

func (s *BizProduct) GetEstimateID() string {
	if s.OPriceInfo == nil {
		return ""
	}
	return s.OPriceInfo.EstimateId
}

func (s *BizProduct) GetProductCategory() int64 {
	return s.OProduct.OOrderInfo.IProductCategory
}
