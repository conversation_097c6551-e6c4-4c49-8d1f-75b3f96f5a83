package option_gen_product

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type InsteadOrderStationServiceByQuotation struct {
	baseReq *models.BaseReqData

	quotation *biz_runtime.Quotation
	nTuple    *price_api.QuotationNTuple
}

func NewInsteadOrderStationServiceByQuotation(baseReq *models.BaseReqData, quotation *biz_runtime.Quotation, nTuple *price_api.QuotationNTuple) *InsteadOrderStationServiceByQuotation {
	return &InsteadOrderStationServiceByQuotation{
		baseReq:   baseReq,
		quotation: quotation,
		nTuple:    nTuple,
	}
}

func (s *InsteadOrderStationServiceByQuotation) ExecProducts(ctx context.Context, brd *models.BaseReqData) []*models.Product {
	var (
		product  *models.Product
		products []*models.Product
	)

	product = s.buildProductByQuotation(ctx, brd)
	if product == nil {
		return nil
	}

	products = append(products, product)
	return products
}

// buildProductByQuotation ...
func (s *InsteadOrderStationServiceByQuotation) buildProductByQuotation(ctx context.Context, brd *models.BaseReqData) *models.Product {
	if s.baseReq == nil || s.quotation == nil || s.nTuple == nil {
		return nil
	}

	var (
		quotation = s.quotation
		nTuple    = s.nTuple

		product = &models.Product{}
	)

	product.BuildEstimateByOption(ctx, []string{
		util.Float64ToString(quotation.FromLat),
		util.Float64ToString(quotation.FromLng),
		util.Float64ToString(quotation.ToLat),
		util.Float64ToString(quotation.ToLng),
		util.Int642String(quotation.ComboId),
		util.StringPtr2String(quotation.ShiftId),
	})

	product.ProductCategory = quotation.GetProductCategory()
	product.ProductID = nTuple.ProductID
	product.OrderType = quotation.GetOrderType()
	product.BusinessID = int64(nTuple.BusinessID)
	product.RequireLevelInt = util.String2int64(ctx, nTuple.RequireLevel)
	product.RequireLevel = nTuple.RequireLevel
	product.CarpoolType = nTuple.CarpoolType
	product.ComboType = int64(nTuple.ComboType)
	product.IsSpecialPrice = nTuple.IsSpecialPrice
	product.CarpoolPriceType = int32(nTuple.CarpoolPriceType)
	product.IsDualCarpoolPrice = nTuple.IsDualCarpoolPrice
	product.AirportType = nTuple.AirportType
	product.RailwayType = nTuple.RailwayType
	product.HotelType = nTuple.HotelType
	product.ExamType = util.Int32Ptr2Int32(quotation.ExamType)
	product.LevelType = nTuple.LevelType
	product.StationServiceControl = nTuple.StationServiceControl
	product.RouteType = int64(nTuple.RouteType)
	product.LongRentType = int16(nTuple.LongRentType)
	product.EmergencyServiceType = nTuple.EmergencyServiceType
	product.ShiftID = util.StringPtr2String(quotation.ShiftId)
	product.BizInfo = &models.PrivateBizInfo{
		ComboID:       quotation.ComboId,
		DepartureTime: quotation.DepartureTime,
		IntercityData: models.IntercityData{
			SeatDetailInfo: brd.CommonBizInfo.SeatDetailInfo,
			StationInventoryInfo: &models.StationInventoryInfo{
				SelectInfo: models.StationInventorySelectInfo{
					DepartureTime: quotation.DepartureTime,
					RouteId:       quotation.ComboId,
					FromStationId: int(brd.CommonBizInfo.StartStationId),
					DestStationId: int(brd.CommonBizInfo.EndStationId),
					ShiftID:       util.StringPtr2String(quotation.ShiftId),
				}},
		},
	}
	product.BizInfo.StartStationID = brd.CommonBizInfo.StartStationId
	product.BizInfo.DestStationID = brd.CommonBizInfo.EndStationId
	return product
}
