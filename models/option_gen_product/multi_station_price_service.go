package option_gen_product

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/decision"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
)

type MultiStationPriceResMes struct {
	BaseProducts map[int32]*models.Product `json:"base_products"`
	SkuInfo      []models.SkuInfo          `json:"sku_info"`
}

type MultiStationPriceService struct {
	baseReq *models.BaseReqData
}

func NewMultiStationPriceService(ctx context.Context) *MultiStationPriceService {
	return &MultiStationPriceService{}

}

func (s *MultiStationPriceService) ExecProducts(ctx context.Context, brd *models.BaseReqData) []*models.Product {
	if brd == nil || brd.CommonBizInfo.StationInfo.EndCity == 0 || brd.CommonBizInfo.StationInfo.StartCity == 0 {
		return nil
	}
	stationResMes := &InsteadOrderStationResMes{}
	var resProducts []*models.Product
	// 1. 查dds
	baseProducts := make(map[int32]*models.Product, 0)
	ddsReq := brd.GenDDSProductsReq()
	ddsResp, err := decision.GetDDSProducts(ctx, ddsReq)
	if ddsResp == nil || err != nil || len(ddsResp) == 0 {
		return nil
	}
	productList := []int32{}
	for _, ddsProduct := range ddsResp {
		product := &models.Product{}
		if !ddsProduct.RemoveFlag {
			product.BuildFromDDsResponse(ctx, ddsProduct, brd)
			baseProducts[int32(product.ProductID)] = product
			productList = append(productList, int32(product.ProductID))
		}
	}
	if len(productList) == 0 {
		return nil
	}
	// 2. 查库存
	skuList := rpc_process.NewMutliStationInventory(brd).Fetch(ctx, productList)

	skuListFilter := []models.SkuInfo{}
	for _, v := range skuList {
		if util.InArrayInt32(v.ProductID, productList) {
			skuListFilter = append(skuListFilter, v)
		}
	}
	stationResMes = &InsteadOrderStationResMes{
		SkuInfo:      skuListFilter,
		BaseProducts: baseProducts,
	}
	for i := 0; i < len(stationResMes.SkuInfo); i++ {
		baseProduct := stationResMes.BaseProducts[stationResMes.SkuInfo[i].ProductID]
		resProducts = append(resProducts, NewMultiStationPriceProduct(ctx, baseProduct, stationResMes.SkuInfo[i], brd))
	}

	return resProducts
}

func NewMultiStationPriceProduct(ctx context.Context, baseProduct *models.Product, skuInfo models.SkuInfo, brd *models.BaseReqData) *models.Product {
	p := &models.Product{}
	p.ProductCategory = baseProduct.ProductCategory
	p.OrderType = baseProduct.OrderType
	p.ProductID = baseProduct.ProductID
	p.BusinessID = baseProduct.BusinessID
	p.RequireLevelInt = baseProduct.RequireLevelInt
	p.RequireLevel = baseProduct.RequireLevel
	p.CarpoolType = baseProduct.CarpoolType
	p.ComboType = baseProduct.ComboType
	p.IsSpecialPrice = baseProduct.IsSpecialPrice
	p.CarpoolPriceType = baseProduct.CarpoolPriceType
	p.IsDualCarpoolPrice = baseProduct.IsDualCarpoolPrice
	p.AirportType = baseProduct.AirportType
	p.RailwayType = baseProduct.RailwayType
	p.HotelType = baseProduct.HotelType
	p.ExamType = baseProduct.ExamType
	p.StationServiceControl = baseProduct.StationServiceControl
	p.LevelType = baseProduct.LevelType
	p.RouteType = baseProduct.RouteType
	p.LongRentType = baseProduct.LongRentType
	p.EmergencyServiceType = baseProduct.EmergencyServiceType
	p.BuildEstimateIDByShiftID(ctx, &skuInfo)
	p.ShiftID = skuInfo.ShiftID
	p.BizInfo = &models.PrivateBizInfo{
		ComboID:           skuInfo.RouteGroup,
		DepartureTime:     skuInfo.DepartureTime,
		MaxCarpoolSeatNum: skuInfo.RemainSeats,
		IntercityData: models.IntercityData{
			SeatDetailInfo: brd.CommonBizInfo.SeatDetailInfo,
			StationInventoryInfo: &models.StationInventoryInfo{
				SelectInfo: models.StationInventorySelectInfo{
					DepartureTime:             skuInfo.DepartureTime,
					RouteId:                   skuInfo.RouteGroup,
					RemainSeats:               skuInfo.RemainSeats,
					FromStationId:             int(skuInfo.StartStationID),
					DestStationId:             int(skuInfo.EndStationID),
					ShiftID:                   skuInfo.ShiftID,
					CarryChildrenMaxInventory: skuInfo.CarryChildrenMaxInventory,
					ExtraInfo:                 skuInfo.ExtraInfo,
				}},
		},
	}

	return p

}
