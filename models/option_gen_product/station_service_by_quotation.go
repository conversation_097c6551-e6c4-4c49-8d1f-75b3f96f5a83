package option_gen_product

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type stationServiceByQuotation struct {
	baseReq *models.BaseReqData

	quotation *biz_runtime.Quotation
	nTuple    *price_api.QuotationNTuple
}

func NewStationServiceByQuotation(baseReq *models.BaseReqData, quotation *biz_runtime.Quotation, nTuple *price_api.QuotationNTuple) *stationServiceByQuotation {
	return &stationServiceByQuotation{
		baseReq:   baseReq,
		quotation: quotation,
		nTuple:    nTuple,
	}
}

func (s *stationServiceByQuotation) ExecProducts(ctx context.Context, brd *models.BaseReqData) []*models.Product {
	var (
		product  *models.Product
		products []*models.Product
	)

	product = s.buildProductByQuotation(ctx)
	if product == nil {
		return nil
	}

	products = append(products, product)
	return products
}

// buildProductByQuotation ...
func (s *stationServiceByQuotation) buildProductByQuotation(ctx context.Context) *models.Product {
	if s.baseReq == nil || s.quotation == nil || s.nTuple == nil {
		return nil
	}

	var (
		quotation = s.quotation
		nTuple    = s.nTuple

		product = &models.Product{}
	)

	product.BuildEstimateByOption(ctx, []string{
		util.Float64ToString(quotation.FromLat),
		util.Float64ToString(quotation.FromLng),
		util.Float64ToString(quotation.ToLat),
		util.Float64ToString(quotation.ToLng),
		util.Int642String(quotation.ComboId),
		util.StringPtr2String(quotation.ShiftId),
	})

	product.ProductCategory = quotation.GetProductCategory()
	product.ProductID = nTuple.ProductID
	product.OrderType = quotation.GetOrderType()
	product.BusinessID = int64(nTuple.BusinessID)
	product.RequireLevelInt = util.String2int64(ctx, nTuple.RequireLevel)
	product.RequireLevel = nTuple.RequireLevel
	product.CarpoolType = nTuple.CarpoolType
	product.ComboType = int64(nTuple.ComboType)
	product.IsSpecialPrice = nTuple.IsSpecialPrice
	product.CarpoolPriceType = int32(nTuple.CarpoolPriceType)
	product.IsDualCarpoolPrice = nTuple.IsDualCarpoolPrice
	product.AirportType = nTuple.AirportType
	product.RailwayType = nTuple.RailwayType
	product.HotelType = nTuple.HotelType
	product.ExamType = util.Int32Ptr2Int32(quotation.ExamType)
	product.LevelType = nTuple.LevelType
	product.StationServiceControl = nTuple.StationServiceControl
	product.RouteType = int64(nTuple.RouteType)
	product.LongRentType = int16(nTuple.LongRentType)
	product.EmergencyServiceType = nTuple.EmergencyServiceType
	product.ShiftID = util.StringPtr2String(quotation.ShiftId)

	product.BizInfo = &models.PrivateBizInfo{}
	product.BizInfo.DepartureTime = quotation.DepartureTime
	product.BizInfo.ComboID = quotation.ComboId
	product.BizInfo.SeatDetailInfo = s.baseReq.CommonBizInfo.SeatDetailInfo

	// 切换站点功能
	if s.baseReq.CommonBizInfo.StationInfo.StartStationId > 0 {
		product.BizInfo.StartStationID = s.baseReq.CommonBizInfo.StationInfo.StartStationId
	} else if quotation.FromStationId != nil {
		product.BizInfo.StartStationID = *quotation.FromStationId
	}
	if s.baseReq.CommonBizInfo.StationInfo.EndStationId > 0 {
		product.BizInfo.DestStationID = s.baseReq.CommonBizInfo.StationInfo.EndStationId
	} else if quotation.DestStationId != nil {
		product.BizInfo.DestStationID = *quotation.DestStationId
	}

	return product
}
