package option_gen_product

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/decision"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
	"github.com/tidwall/gjson"
)

type InsteadOrderStationResMes struct {
	BaseProducts map[int32]*models.Product `json:"base_products"`
	SkuInfo      []models.SkuInfo          `json:"sku_info"`
}

type InsteadOrderStationService struct {
	baseReq *models.BaseReqData
}

func NewInsteadOrderStationService(ctx context.Context) *InsteadOrderStationService {
	return &InsteadOrderStationService{}

}

func (s *InsteadOrderStationService) ExecProducts(ctx context.Context, brd *models.BaseReqData) []*models.Product {
	if brd == nil || brd.CommonBizInfo.StationInfo.EndCity == 0 || brd.CommonBizInfo.StationInfo.StartCity == 0 {
		return nil
	}
	stationResMes := &InsteadOrderStationResMes{}
	var resProducts []*models.Product
	// 1. 查dds
	baseProducts := make(map[int32]*models.Product, 0)
	ddsReq := brd.GenDDSProductsReq()
	ddsResp, err := decision.GetDDSProducts(ctx, ddsReq)
	if ddsResp == nil || err != nil || len(ddsResp) == 0 {
		return nil
	}
	productList := []int32{}
	for _, ddsProduct := range ddsResp {
		product := &models.Product{}
		if !ddsProduct.RemoveFlag {
			product.BuildFromDDsResponse(ctx, ddsProduct, brd)
			baseProducts[int32(product.ProductID)] = product
			productList = append(productList, int32(product.ProductID))
		}
	}
	if len(productList) == 0 {
		return nil
	}
	// 2. 查库存
	skuList := rpc_process.NewMutliStationInventory(brd).Fetch(ctx, productList)

	skuListFilter := []models.SkuInfo{}
	for _, v := range skuList {
		if util.InArrayInt32(v.ProductID, productList) {
			skuListFilter = append(skuListFilter, v)
		}
	}
	stationResMes = &InsteadOrderStationResMes{
		SkuInfo:      skuListFilter,
		BaseProducts: baseProducts,
	}
	if brd.CommonInfo.Channel == rpc_process.ChannelHonghuCheckBackground {
		resProducts = s.getResProduct(ctx, brd, stationResMes, resProducts)
	} else if brd.CommonInfo.Channel == rpc_process.ChannelHonghuPhoneCall {
		resProducts = s.getSingleResProduct(ctx, brd, stationResMes, resProducts)
	} else {
		for i := 0; i < len(stationResMes.SkuInfo); i++ {
			baseProduct := stationResMes.BaseProducts[stationResMes.SkuInfo[i].ProductID]
			resProducts = append(resProducts, NewInsteadStationProduct(ctx, baseProduct, stationResMes.SkuInfo[i], brd))
		}
	}

	return resProducts
}

func (s *InsteadOrderStationService) getResProduct(ctx context.Context, brd *models.BaseReqData, stationResMes *InsteadOrderStationResMes, resProducts []*models.Product) []*models.Product {
	//检票端代发单场景，截取最近的10个班次。班次多了，查询账单会有问题
	limitNum := 10
	dcmpText := dcmp.GetDcmpContent(ctx, "intercity_estimate-instead_order", nil)
	if int(gjson.Get(dcmpText, "limit_num").Int()) > 0 {
		limitNum = int(gjson.Get(dcmpText, "limit_num").Int())
	}
	//不指定班次的话，直接取前10个
	if brd.CommonBizInfo.BusServiceShiftId == "" {
		for i := 0; i < len(stationResMes.SkuInfo); i++ {
			if i < limitNum {
				baseProduct := stationResMes.BaseProducts[stationResMes.SkuInfo[i].ProductID]
				resProducts = append(resProducts, NewInsteadStationProduct(ctx, baseProduct, stationResMes.SkuInfo[i], brd))
			}
		}
	} else { //指定班次的话，要将指定班次包装到前10个中
		//1、判断前10个是否命中指定的班次
		isHit := false
		for i := 0; i < len(stationResMes.SkuInfo); i++ {
			if i < limitNum {
				if brd.CommonBizInfo.BusServiceShiftId == stationResMes.SkuInfo[i].ShiftID {
					isHit = true
				}
			}
		}
		//2、从所有的班次中获取指定的班次
		var appointProduct *models.Product
		for i := 0; i < len(stationResMes.SkuInfo); i++ {
			if brd.CommonBizInfo.BusServiceShiftId == stationResMes.SkuInfo[i].ShiftID {
				baseProduct := stationResMes.BaseProducts[stationResMes.SkuInfo[i].ProductID]
				appointProduct = NewInsteadStationProduct(ctx, baseProduct, stationResMes.SkuInfo[i], brd)
			}
		}
		//3、根据前10个是否命中来构建班次
		if isHit { //3.1 若前10个命中班次，则直接取前10个即可
			for i := 0; i < len(stationResMes.SkuInfo); i++ {
				if i < limitNum {
					baseProduct := stationResMes.BaseProducts[stationResMes.SkuInfo[i].ProductID]
					resProducts = append(resProducts, NewInsteadStationProduct(ctx, baseProduct, stationResMes.SkuInfo[i], brd))
				}
			}
		} else { //3.2 若前10个没命中班次，则直接取前9个，再追加指定班次即可
			for i := 0; i < len(stationResMes.SkuInfo); i++ {
				if i < limitNum-1 {
					baseProduct := stationResMes.BaseProducts[stationResMes.SkuInfo[i].ProductID]
					resProducts = append(resProducts, NewInsteadStationProduct(ctx, baseProduct, stationResMes.SkuInfo[i], brd))
				}
			}
			if appointProduct != nil {
				resProducts = append(resProducts, appointProduct)
			}
		}
	}
	return resProducts
}

func (s *InsteadOrderStationService) getSingleResProduct(ctx context.Context, brd *models.BaseReqData, stationResMes *InsteadOrderStationResMes, resProducts []*models.Product) []*models.Product {
	//不指定班次的话，取全部
	if brd.CommonBizInfo.BusServiceShiftId == "" {
		for i := 0; i < len(stationResMes.SkuInfo); i++ {
			baseProduct := stationResMes.BaseProducts[stationResMes.SkuInfo[i].ProductID]
			resProducts = append(resProducts, NewInsteadStationProduct(ctx, baseProduct, stationResMes.SkuInfo[i], brd))
		}
	} else {
		for _, item := range stationResMes.SkuInfo {
			if brd.CommonBizInfo.BusServiceShiftId == item.ShiftID {
				resProducts = append(resProducts, NewInsteadStationProduct(ctx, stationResMes.BaseProducts[item.ProductID], item, brd))
				return resProducts
			}
		}
	}
	return resProducts
}

func NewInsteadStationProduct(ctx context.Context, baseProduct *models.Product, skuInfo models.SkuInfo, brd *models.BaseReqData) *models.Product {
	p := &models.Product{}
	p.ProductCategory = baseProduct.ProductCategory
	p.OrderType = baseProduct.OrderType
	p.ProductID = baseProduct.ProductID
	p.BusinessID = baseProduct.BusinessID
	p.RequireLevelInt = baseProduct.RequireLevelInt
	p.RequireLevel = baseProduct.RequireLevel
	p.CarpoolType = baseProduct.CarpoolType
	p.ComboType = baseProduct.ComboType
	p.IsSpecialPrice = baseProduct.IsSpecialPrice
	p.CarpoolPriceType = baseProduct.CarpoolPriceType
	p.IsDualCarpoolPrice = baseProduct.IsDualCarpoolPrice
	p.AirportType = baseProduct.AirportType
	p.RailwayType = baseProduct.RailwayType
	p.HotelType = baseProduct.HotelType
	p.ExamType = baseProduct.ExamType
	p.StationServiceControl = baseProduct.StationServiceControl
	p.LevelType = baseProduct.LevelType
	p.RouteType = getRouteType(baseProduct, skuInfo)
	p.LongRentType = baseProduct.LongRentType
	p.EmergencyServiceType = baseProduct.EmergencyServiceType
	p.BuildEstimateIDByShiftID(ctx, &skuInfo)
	p.ShiftID = skuInfo.ShiftID
	p.BizInfo = &models.PrivateBizInfo{
		ComboID:           skuInfo.RouteGroup,
		DepartureTime:     skuInfo.DepartureTime,
		MaxCarpoolSeatNum: skuInfo.RemainSeats,
		IntercityData: models.IntercityData{
			SeatDetailInfo: brd.CommonBizInfo.SeatDetailInfo,
			StationInventoryInfo: &models.StationInventoryInfo{
				SelectInfo: models.StationInventorySelectInfo{
					DepartureTime:             skuInfo.DepartureTime,
					RouteId:                   skuInfo.RouteGroup,
					RemainSeats:               skuInfo.RemainSeats,
					FromStationId:             int(skuInfo.StartStationID),
					DestStationId:             int(skuInfo.EndStationID),
					ShiftID:                   skuInfo.ShiftID,
					CarryChildrenMaxInventory: skuInfo.CarryChildrenMaxInventory,
				}},
		},
	}

	return p

}
