package rpc_process

import (
	"context"
	"fmt"
	"git.xiaojukeji.com/nuwa/golibs/zerolog/ddlog"
	"testing"

	ufsThrift "git.xiaojukeji.com/dirpc/dirpc-go-thrift-UFS"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/logic/guide_info/data/order_info"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

// 辅助函数，用于创建字符串指针
func strPtr(s string) *string {
	return &s
}

func mockUfsResponse() {
	// 模拟 ufs.MultiGetFeatures 的返回值
	mockey.Mock(ufs.MultiGetFeatures).Return(
		[]interface{}{
			map[string]interface{}{
				"Errno": 0,
				"Value": strPtr("1"),
			},
		}, nil).Build()
}

func mockUfsResponseFailed() {
	// 模拟 ufs.MultiGetFeatures 返回错误
	mockey.Mock(ufs.MultiGetFeatures).Return(nil, fmt.Errorf("mock error")).Build()
}

func mockUfsResponseDisabled() {
	// 模拟 ufs.MultiGetFeatures 返回禁用状态
	mockey.Mock(ufs.MultiGetFeatures).Return(
		[]interface{}{
			map[string]interface{}{
				"Errno": 0,
				"Value": strPtr("0"),
			},
		}, nil).Build()
}

func TestNewAlwaysPoolRPC(t *testing.T) {
	// 准备测试数据
	ctx := context.Background()
	domain := "test_domain"
	orderInfo := &order_info.SimpleOrderInfo{
		OrderId: "12345",
	}

	// 执行被测试函数
	result := NewAlwaysPoolRPC(ctx, domain, orderInfo)

	// 验证结果
	assert.NotNil(t, result, "UfsAlwaysPoolFeature不应为nil")
	assert.Equal(t, domain, result.Domain, "Domain应匹配")
	assert.Equal(t, orderInfo.OrderId, result.OrderID, "OrderID应匹配")
	assert.Equal(t, ufs.AlwaysPoolKey, result.OrderKey, "OrderKey应匹配")
	assert.False(t, result.AlwaysResult, "AlwaysResult初始值应为false")
}

func TestUfsAlwaysPoolFeatureFetch(t *testing.T) {
	mockey.Mock((*ddlog.DiLogHandle).Infof).Return().Build()
	mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build()
	defer mockey.UnPatchAll()
	// 准备测试上下文
	ctx := context.Background()
	//initMainFunc()
	t.Run("成功获取到拼友推荐状态为启用", func(t *testing.T) {
		// 准备测试数据
		feature := &UfsAlwaysPoolFeature{
			Domain:   "test_domain",
			OrderID:  "12345",
			OrderKey: ufs.AlwaysPoolKey,
		}

		// 模拟RPC调用
		value := "1"
		mocker := mockey.Mock(ufs.MultiGetFeatures).Return(map[string]*ufsThrift.FeatureResponse{
			"test_key": {
				Errno: 0,
				Value: &value,
			},
		}, nil).Build()
		defer mocker.UnPatch()

		// 执行被测试函数
		success := feature.Fetch(ctx)

		// 验证结果
		assert.True(t, success, "Fetch应返回成功")
		assert.True(t, feature.AlwaysResult, "AlwaysResult应被设置为true")
	})

	t.Run("失败获取拼友推荐状态", func(t *testing.T) {
		// 准备测试数据
		feature := &UfsAlwaysPoolFeature{
			Domain:   "test_domain",
			OrderID:  "12345",
			OrderKey: ufs.AlwaysPoolKey,
		}

		// 模拟RPC调用失败
		mocker := mockey.Mock(ufs.MultiGetFeatures).Return(nil, fmt.Errorf("mock error")).Build()
		defer mocker.UnPatch()

		// 执行被测试函数
		success := feature.Fetch(ctx)

		// 验证结果
		assert.False(t, success, "Fetch应返回失败")
		assert.False(t, feature.AlwaysResult, "AlwaysResult应保持为false")
	})

	t.Run("成功获取到拼友推荐状态为禁用", func(t *testing.T) {
		// 准备测试数据
		feature := &UfsAlwaysPoolFeature{
			Domain:   "test_domain",
			OrderID:  "12345",
			OrderKey: ufs.AlwaysPoolKey,
		}

		// 模拟RPC调用
		value := "0"
		mocker := mockey.Mock(ufs.MultiGetFeatures).Return(map[string]*ufsThrift.FeatureResponse{
			"test_key": {
				Errno: 0,
				Value: &value,
			},
		}, nil).Build()
		defer mocker.UnPatch()

		// 执行被测试函数
		success := feature.Fetch(ctx)

		// 验证结果
		assert.True(t, success, "Fetch应返回成功")
		assert.False(t, feature.AlwaysResult, "AlwaysResult应保持为false")
	})
}
