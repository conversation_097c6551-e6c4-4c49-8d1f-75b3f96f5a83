package rpc_process

import (
	"context"
	"encoding/json"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/nuwa/trace"
	"strconv"
	"time"

	CarpoolApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolApi"
	HotspotDIRPC "git.xiaojukeji.com/dirpc/dirpc-go-thrift-Hotspot"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/carpool_api"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/minibus"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/redis"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

// CarpoolEtx 拼车ets etd信息
type CarpoolEtx struct {
	SyncReq int32 // 同步/异步请求预匹配

	area            *models.AreaInfo
	passenger       *models.PassengerInfo
	miniBusRes      map[int32]*CarpoolApi.PrematchMiniBusRes
	preMatchTPErrNo int32
	tokenInfo       *models.TokenInfo
	commonInfo      *models.CommonInfo

	_EtsInfo *HotspotDIRPC.TimeWindow

	_SkuInfo map[string]map[string]struct { // 库存模式信息
		Status int32
		Seat   int
	}

	inviteRes map[string]*CarpoolApi.PinkerInvitedRes //邀约顺路信息
}

const (
	Sync              int32 = 0
	NotSSync          int32 = 1 // 同步请求预匹配
	MinibusCandidate        = -1
	MinibusInfoUfsKey       = "carpool.minibus_prematch_info"
)

// NewCarpoolEtx 拼车ets etd信息
func NewCarpoolEtx(baseReq *models.BaseReqData, sync int32) *CarpoolEtx {
	return &CarpoolEtx{
		area:       &baseReq.AreaInfo,
		passenger:  &baseReq.PassengerInfo,
		SyncReq:    sync,
		commonInfo: &baseReq.CommonInfo,
		miniBusRes: make(map[int32]*CarpoolApi.PrematchMiniBusRes, 0),
		tokenInfo:  new(models.TokenInfo),
	}
}

// BuildProductBizInfo 设置单产品信息
func (etx *CarpoolEtx) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {

	info.MiniBusPreMatch = etx.miniBusRes[product.GetLevelType()]

	if product.Product != nil && carpool.IsPinCheChe(product.Product.ProductCategory) {
		info.PinchecheEtsInfo = etx._EtsInfo
		info.InviteInfo = etx.inviteRes[product.GetEstimateID()]
	}

	interMode := int32(0)
	if product.Product.BizInfo != nil && product.Product.BizInfo.RouteInfo != nil {
		interMode = *(product.Product.BizInfo.RouteInfo.InterMode)
	}

	if product.Product != nil && carpool.IsIntercityNewMode(product.Product.ProductCategory, product.Product.RouteType, int64(interMode)) {
		eid := product.Product.EstimateID
		info.IntercitySkuInfo = etx._SkuInfo[eid]
	}

	if product.Product != nil && carpool.IsIntercityThirdPID(ctx, int(product.Product.ProductID)) {
		eid := product.Product.EstimateID
		info.IntercitySkuInfo = etx._SkuInfo[eid]
	}

}

func (etx *CarpoolEtx) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.TokenInfo = etx.tokenInfo

}

func (etx *CarpoolEtx) Fetch(ctx context.Context, productFulls []*biz_runtime.ProductInfoFull) bool {
	defer func() {
		if rec := recover(); rec != nil {
			log.Trace.Warnf(ctx, consts.TagErrGoRoutine, "carpool prematch info fetch fail: %s", rec)
			return
		}
	}()

	invitationType := 0
	products := make([]*models.Product, 0)
	for _, pf := range productFulls {
		if pf != nil && pf.Product != nil {
			products = append(products, pf.Product)
			if pf.GetCommonBizInfo().InvitationInfo != nil {
				invitationType = 1
			}
		}
	}

	return etx.fetchEtxInfo(ctx, products, invitationType)
}

func (etx *CarpoolEtx) fetchEtxInfo(ctx context.Context, products []*models.Product, invitationType int) bool {
	const (
		LogTag            = "carpool_prematch"
		EtxInfoExpireTime = 1800 * time.Second
	)

	builder := carpool_api.NewPrematchRecommendRequestBuilder().SetPassengerInfo(&carpool_api.PassengerInfo{
		Pid:   strconv.FormatInt(etx.passenger.PID, 10),
		Phone: etx.passenger.Phone,
	})

	geoInfo := &carpool_api.GeoInfo{
		CityID:            etx.area.City,
		CurLng:            etx.area.CurLng,
		CurLat:            etx.area.CurLat,
		FromLng:           etx.area.FromLng,
		FromLat:           etx.area.FromLat,
		FromName:          etx.area.FromName,
		ToLng:             etx.area.ToLng,
		ToLat:             etx.area.ToLat,
		ToName:            etx.area.ToName,
		MapInfoCacheToken: etx.area.MapInfoCacheToken,
	}
	builder.SetGeoInfo(geoInfo)

	// build key => eid
	mapping := make(map[string]string, len(products))

	for _, prod := range products {
		interMode := int32(0)
		routeGroup := int64(0)
		if prod.BizInfo != nil && prod.BizInfo.RouteInfo != nil {
			interMode = *(prod.BizInfo.RouteInfo.InterMode)
			routeGroup = *prod.BizInfo.RouteInfo.RouteGroup
		}

		if !(carpool.IsPinCheChe(prod.ProductCategory) ||
			carpool.IsIntercityNewMode(prod.ProductCategory, prod.RouteType, int64(interMode)) ||
			(carpool.IsIntercityThirdPID(ctx, int(prod.ProductID)) && !carpool.IsIntercityStation(ctx, int(prod.CarpoolType)) ||
				carpool.IsMiniBus(int(prod.CarpoolType))) || carpool.IsSmartBus(int(prod.CarpoolType))) {
			continue
		}

		key := fmt.Sprintf("%d-%d-%s", prod.ProductID, prod.ComboType, prod.RequireLevel)
		mapping[key] = prod.EstimateID

		builder.AddProduct(
			&carpool_api.Product{
				ProductID:    prod.ProductID,
				ComboType:    prod.ComboType,
				RequireLevel: prod.RequireLevel,

				NTuple: carpool_api.NTuple{
					EstimateID:         prod.EstimateID,
					CarpoolPriceType:   int(prod.CarpoolPriceType),
					CarpoolType:        int(prod.CarpoolType),
					RouteType:          int(prod.RouteType),
					OrderType:          int(prod.OrderType),
					MenuID:             "dache_anycar",
					InvitationType:     invitationType,
					IsDualCarpoolPrice: prod.IsDualCarpoolPrice,
					DepartureRange:     prod.BizInfo.DepartureRange,
					LevelType:          prod.LevelType,
					RouteGroup:         routeGroup,
				},
			},
		).AddExtMapRouteGroup(routeGroup)
	}

	builder.SetSyncReq(etx.SyncReq).SetWithCtx(ctx)
	if builder.GetReq() == nil || len(builder.GetReq().ProductReq) == 0 {
		log.Trace.Debug(ctx, LogTag, "empty product, no need to fetch prematch info")
		return true
	}

	resp, err := carpool_api.GetPreMatchRecommendInfo(ctx, builder.GetReq())
	if err != nil {
		log.Trace.Infof(ctx, LogTag, "fetch prematch info failed %s", err)
		return false
	}
	if resp == nil {
		log.Trace.Infof(ctx, LogTag, "fetch prematch info got empty")
		return false
	}
	if resp.ErrorCode != 0 {
		log.Trace.Infof(ctx, LogTag, "fetch prematch info failed(%d) %s", resp.ErrorCode, resp.ErrorMsg)
		return false
	}

	if len(resp.MiniBusRes) > 0 {
		if minibus.JudgeNewMinibus(etx.commonInfo.AccessKeyID, etx.commonInfo.AppVersion) {
			for _, v := range resp.MiniBusRes {
				if v != nil && v.GetMapinfoStartCacheToken() != "" && v.GetMapinfoDestCacheToken() != "" {
					etx.tokenInfo.MapinfoStartCacheToken = v.GetMapinfoStartCacheToken()
					etx.tokenInfo.MapinfoDestCacheToken = v.GetMapinfoDestCacheToken()
				}
			}
		}

		etx.miniBusRes = make(map[int32]*CarpoolApi.PrematchMiniBusRes, 0)
		for _, v := range resp.MiniBusRes {
			if v == nil || v.ProductInfo == nil || len(v.ProductInfo.OrderNTuple) <= 0 {
				continue
			}

			if len(v.ProductInfo.OrderNTuple["level_type"]) > 0 {
				// 1、59999过滤 2、etp_time_duration=0 过滤
				if v.ErrorCode != ErrPreMatchNoMiniBusRes && nil != v.EtpInfo && 0 != v.EtpInfo.EtpTimeDuration {
					k, err := strconv.ParseInt(v.ProductInfo.OrderNTuple["level_type"], 10, 32)
					if err == nil {
						etx.miniBusRes[int32(k)] = v
					}
				}
				// 记录错误码
				if 0 != v.ErrorCode && v.ErrorCode != ErrPreMatchNoMiniBusRes {
					etx.preMatchTPErrNo = v.ErrorCode
				}
			}

			if token := v.MapinfoCacheToken; token != nil {
				go etx.CacheMiniBusData(ctx, products, v)
			}

		}
	}

	// SAVE ETS
	for _, ret := range resp.EtsResults {
		if ret == nil || ret.ProductInfo == nil || ret.ProductInfo.ComboType == nil ||
			ret.ProductInfo.ProductID == nil || ret.ProductInfo.RequireLevel == nil {
			continue
		}
		prod := ret.ProductInfo
		key := fmt.Sprintf("%s-%d-%s", *prod.ProductID, *prod.ComboType, *prod.RequireLevel)

		eid, ok := mapping[key]
		if !ok {
			continue
		}

		if ret.ErrorCode != 0 {
			log.Trace.Warnf(ctx, LogTag, "product %s ets failed(%d) %s", key, ret.ErrorCode, ret.ErrorMsg)
			continue
		}

		_etsInfo := _EtsInfo{
			EtsType:           ret.EtsType,
			TimeWindow:        ret.TimeWindow,
			ExtMap:            ret.ExtMap,
			Full2goTimeWindow: ret.Full2goTimeWindow,
		}
		data, err := json.Marshal(_etsInfo)
		if err != nil {
			continue
		}

		if _, err := redis.GetClient().SetEx(ctx, fmt.Sprintf("P_CARPOOL_ETS_INFO_%s", eid), EtxInfoExpireTime, data); err != nil {
			log.Trace.Warnf(ctx, LogTag, "set redis failed %s", err)
		}

		etx._EtsInfo = &HotspotDIRPC.TimeWindow{
			BegTime: ret.Full2goTimeWindow.GetBegTime(),
			EndTime: ret.Full2goTimeWindow.GetEndTime(),
		}
	}
	// SAVE EXT
	for _, ret := range resp.ExtResults {
		if ret == nil || ret.ProductInfo == nil || ret.ProductInfo.ComboType == nil ||
			ret.ProductInfo.ProductID == nil || ret.ProductInfo.RequireLevel == nil {
			continue
		}
		prod := ret.ProductInfo
		key := fmt.Sprintf("%s-%d-%s", *prod.ProductID, *prod.ComboType, *prod.RequireLevel)
		eid, ok := mapping[key]
		if !ok {
			continue
		}

		if ret.ErrorCode != 0 {
			log.Trace.Warnf(ctx, LogTag, "product %s ext failed(%d) %s", key, ret.ErrorCode, ret.ErrorMsg)
			continue
		}

		data, err := json.Marshal(ret.ResExtMap)
		if err != nil {
			continue
		}

		if _, err := redis.GetClient().SetEx(ctx, fmt.Sprintf("P_CARPOOL_EXT_INFO_%s", eid),
			EtxInfoExpireTime, data); err != nil {
			log.Trace.Warnf(ctx, LogTag, "set redis failed %s", err)
		}
	}

	// 库存模式剩余库存
	skuInfoMaps := map[string]map[string]struct {
		Status int32
		Seat   int
	}{}
	for _, ret := range resp.SkuResults {
		if ret == nil || ret.ProductInfo == nil || ret.ErrorCode != 0 || len(ret.SkuInfoList) == 0 {
			continue
		}
		prod := ret.ProductInfo
		key := fmt.Sprintf("%s-%d-%s", *prod.ProductID, *prod.ComboType, *prod.RequireLevel)
		eid, ok := mapping[key]
		if !ok {
			continue
		}
		skuInfoMap := map[string]struct {
			Status int32
			Seat   int
		}{}
		for _, v := range ret.SkuInfoList {
			begTime := strconv.Itoa(int(v.TimeWindow.BegTime))
			endTime := strconv.Itoa(int(v.TimeWindow.EndTime))
			remainSeats := 0
			if v.RemainSeats != nil {
				remainSeats = int(*v.RemainSeats)
			}
			skuInfoMap["["+begTime+","+endTime+"]"] = struct {
				Status int32
				Seat   int
			}{
				Status: v.Status,
				Seat:   remainSeats,
			}
		}
		skuInfoMaps[eid] = skuInfoMap
		etx._SkuInfo = skuInfoMaps
	}

	etx.inviteRes = make(map[string]*CarpoolApi.PinkerInvitedRes)
	for _, ret := range resp.GetInvitedRes() {
		if ret == nil || ret.ProductInfo == nil || ret.ErrorCode != 0 {
			continue
		}

		prod := ret.ProductInfo
		key := fmt.Sprintf("%s-%d-%s", *prod.ProductID, *prod.ComboType, *prod.RequireLevel)
		eid, ok := mapping[key]
		if !ok {
			continue
		}

		etx.inviteRes[eid] = ret
	}

	return true
}

type MinibusInfo struct {
	Etp    int64             `json:"etp"`
	ExtMap map[string]string `json:"extMap,omitempty"`
}

func (etx *CarpoolEtx) CacheMiniBusData(ctx context.Context, products []*models.Product, v *CarpoolApi.PrematchMiniBusRes) {
	for _, prod := range products {
		if len(*v.MapinfoCacheToken) <= 0 {
			continue
		}

		if v.ProductInfo.ComboType == nil || prod.ComboType != int64(*v.ProductInfo.ComboType) {
			continue
		}
		if v.ProductInfo.RequireLevel == nil || prod.RequireLevel != *v.ProductInfo.RequireLevel {
			continue
		}
		if v.ProductInfo.ProductID == nil {
			productId, err := strconv.ParseInt(*v.ProductInfo.ProductID, 10, 32)
			if err != nil {
				continue
			}
			if prod.ProductID != productId {
				continue
			}
		}

		CarpoolType, err := strconv.ParseInt(v.ProductInfo.OrderNTuple["carpool_type"], 10, 32)
		if err != nil {
			continue
		}
		if CarpoolType != prod.CarpoolType {
			continue
		}

		levelType, err := strconv.ParseInt(v.ProductInfo.OrderNTuple["level_type"], 10, 32)
		if err != nil {
			continue
		}
		if int32(levelType) != prod.LevelType {
			continue
		}

		if _, err := redis.GetClient().SetEx(ctx, fmt.Sprintf("p_carpool_minibus_map_token%s", prod.EstimateID),
			20*time.Second, *v.MapinfoCacheToken); err != nil {
			log.Trace.Warnf(ctx, LogTag, "set redis failed %s", err)
		}

		// 将小巴预匹配etp、extMap信息存入ufs
		minibusInfo := &MinibusInfo{
			Etp: v.EtpInfo.GetEtpTimeDuration(),
		}
		if len(v.ExtMap) > 0 {
			minibusInfo.ExtMap = v.GetExtMap()
		}
		data, err := json.Marshal(minibusInfo)
		if err != nil {
			return
		}
		params := map[string]string{"estimate_id": prod.EstimateID}
		kv := map[string]string{MinibusInfoUfsKey: string(data)}
		if _, err = ufs.SetFeature(ctx, "passenger", params, kv); err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "setFeature: %v", err)
		}
	}
}

type _EtsInfo struct {
	EtsType           int32                  `json:"ets_type"`
	TimeWindow        *CarpoolApi.TimeWindow ` json:"time_window,omitempty"`
	ExtMap            map[string]string      `json:"extra,omitempty"`
	Full2goTimeWindow *CarpoolApi.TimeWindow `json:"full2go_time_window,omitempty"`
}

func (etx *CarpoolEtx) IsShowCandiate() bool {
	param := ApolloModel.NewUser("").
		With("city", strconv.Itoa(int(etx.area.City))).
		With("phone", etx.passenger.Phone).
		With("page_type", strconv.Itoa(int(etx.commonInfo.PageType))).
		With("source_id", strconv.Itoa(int(etx.commonInfo.SourceID)))
	toggle, err := ApolloSDK.FeatureToggle("gs_minibus_candidate", param)
	if err != nil || !toggle.IsAllow() {
		return false
	}
	return true
}

func (etx *CarpoolEtx) Do(ctx context.Context, productMap []*biz_runtime.ProductInfoFull) []models.ProductCategory {
	if len(productMap) == 0 {
		return nil
	}

	var (
		allRemoved    = make([]models.ProductCategory, 0)
		miniBusCache  = make(map[int64]*biz_runtime.ProductInfoFull)
		smartBusCache = make(map[int64]*biz_runtime.ProductInfoFull)

		iRightPcID         = int64(0)
		iSmartPcId         = int64(0)
		iRightLevelTypeMin = 100000
		iSmartLevelTypeMin = 100000
	)

	for _, prod := range productMap {

		if carpool.IsMiniBus(int(prod.GetCarpoolType())) {
			if !prod.JudgePriceInfoValid() {
				allRemoved = append(allRemoved, models.ProductCategory(prod.GetProductCategory()))
				continue
			}

			preMatchInfo := prod.Product.BizInfo.MiniBusPreMatch
			// 候补场景暂时屏蔽
			if preMatchInfo == nil || preMatchInfo.EtpInfo == nil || preMatchInfo.MapinfoCacheToken == nil ||
				(preMatchInfo.EtpInfo.EtpTimeDuration == MinibusCandidate && !etx.IsShowCandiate()) {
				allRemoved = append(allRemoved, models.ProductCategory(prod.GetProductCategory()))
				continue
			}

			// 小巴专线场景暂时屏蔽
			if preMatchInfo.ExtMap != nil && prod.GetPageType() == page_type.PageTypeGuideAnyCar {
				if _, ok := preMatchInfo.ExtMap["long_trip_id"]; ok {
					allRemoved = append(allRemoved, models.ProductCategory(prod.GetProductCategory()))
					continue
				}
			}

			miniBusCache[prod.GetProductCategory()] = prod
			if int(prod.GetLevelType()) < iRightLevelTypeMin {
				iRightLevelTypeMin = int(prod.GetLevelType())
				iRightPcID = prod.GetProductCategory()
			}
		}

		if carpool.IsSmartBus(int(prod.GetCarpoolType())) {
			if !prod.JudgePriceInfoValid() {
				allRemoved = append(allRemoved, models.ProductCategory(prod.GetProductCategory()))
				continue
			}

			//主表单逻辑
			if prod.GetPageType() == page_type.PageTypeUndefined {
				smartBusCache[prod.GetProductCategory()] = prod
				if int(prod.GetLevelType()) < iSmartLevelTypeMin {
					iSmartLevelTypeMin = int(prod.GetLevelType())
					iSmartPcId = prod.GetProductCategory()
				} else if int(prod.GetLevelType()) < iSmartLevelTypeMin && prod.GetProductCategory() < iSmartPcId {
					iSmartPcId = prod.GetProductCategory()
				}
			}
		}

	}

	if len(miniBusCache) > 0 {
		if len(miniBusCache) == 1 {
			for _, full := range miniBusCache {
				full.BaseReqData.CommonBizInfo.MiniBusData.BOnlyMinibus = true
			}
		} else {
			for _, prod := range miniBusCache {
				if prod.GetProductCategory() != iRightPcID {
					allRemoved = append(allRemoved, models.ProductCategory(prod.GetProductCategory()))
				}
			}
		}
	}

	//仅主表单过滤智能小巴品类
	if len(smartBusCache) > 0 {
		for _, prod := range smartBusCache {
			if prod.GetProductCategory() != iSmartPcId {
				allRemoved = append(allRemoved, models.ProductCategory(prod.GetProductCategory()))
			}
		}
	}

	return allRemoved
}
