package rpc_process

import (
	"context"
	"encoding/json"
	"strconv"

	ssseClient "git.xiaojukeji.com/dirpc/dirpc-go-http-Ssse"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ssse"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type AdditionalService struct {
	needSupportServiceIdList []int64                                                // 每个品类必须支持服务id列表
	baseReq                  *models.BaseReqData                                    // 请求信息
	pcId2ServiceListMap      map[models.ProductCategory][]*ssseClient.CustomService // ssse响应
}

// NewAdditionalService http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=731194098
func NewAdditionalService(ctx context.Context, req *models.BaseReqData) *AdditionalService {

	return &AdditionalService{
		needSupportServiceIdList: getNeedFilterServiceIdList(ctx, req),
		baseReq:                  req,
		pcId2ServiceListMap:      make(map[models.ProductCategory][]*ssseClient.CustomService),
	}

}

// 获取rpc错误信息
func (m *AdditionalService) GetErrorInfo(ctx context.Context) error {
	return nil
}

// Fetch RPC  请求ssse, 返回品类对应服务
func (s *AdditionalService) Fetch(ctx context.Context, products []*models.Product) bool {
	var (
		reqs           []*ssseClient.GetServiceReq
		targetProducts = make([]*models.Product, 0)
		pageType       = int64(s.baseReq.CommonInfo.PageType)
	)

	if len(products) == 0 {
		return false
	}

	for _, p := range products {
		if p.IsTripcloudProduct(ctx) {
			continue
		}

		targetProducts = append(targetProducts, p)

		airportId := int64(s.baseReq.CommonInfo.AirportId)
		req := &ssseClient.GetServiceReq{
			Pid:               s.baseReq.PassengerInfo.PID,
			ProductCategory:   p.ProductCategory,
			Phone:             &s.baseReq.PassengerInfo.Phone,
			BusinessId:        p.BusinessID,
			ComboType:         p.ComboType,
			RequireLevel:      p.RequireLevel,
			CarpoolType:       &p.CarpoolType,
			Type:              int64(s.baseReq.CommonInfo.OrderType),
			Area:              int64(s.baseReq.AreaInfo.Area),
			Flat:              s.baseReq.AreaInfo.FromLat,
			Flng:              s.baseReq.AreaInfo.FromLng,
			Tlat:              s.baseReq.AreaInfo.ToLat,
			AppVersion:        &s.baseReq.CommonInfo.AppVersion,
			Lang:              &s.baseReq.CommonInfo.Lang,
			TrafficNumber:     &s.baseReq.CommonInfo.TrafficNumber,
			TrafficDepTime:    &s.baseReq.CommonInfo.TrafficDepTime,
			AirportId:         &airportId,
			CallCarType:       int16(s.baseReq.CommonInfo.CallCarType),
			DepartureTime:     &s.baseReq.CommonInfo.DepartureTime,
			AccessKeyId:       &s.baseReq.CommonInfo.AccessKeyID,
			MenuId:            &s.baseReq.CommonInfo.MenuID,
			SubMenuId:         nil,
			FlightDepCode:     &s.baseReq.CommonInfo.FlightDepCode,
			FlightDepTerminal: &s.baseReq.CommonInfo.FlightDepTerminal,
			PageType:          &pageType,
			ExtraInfo:         make(map[string]string, 1),
		}
		req.ExtraInfo["gender"] = strconv.Itoa(int(s.baseReq.PassengerInfo.UserGender))
		reqs = append(reqs, req)
	}

	if len(reqs) == 0 {
		return false
	}
	data := ssse.GetService(ctx, &ssseClient.GetMultiServiceReq{
		Caller: "mamba",
		Reqs:   reqs,
	})
	if len(data) < len(targetProducts) {
		//log.Trace.Warnf(ctx, consts.TagErrGoRoutine, "GetService data=[%d] products=[%d]", len(data), len(products))
		return false
	}

	for index, product := range targetProducts {
		if product.ProductCategory == estimate_pc_id.EstimatePcIdAplus ||
			product.ProductCategory == estimate_pc_id.EstimatePcIdSpaciousCar {
			// todo 接入新ssse后, 需要删除车大滴滴特快写死的过滤逻辑
			continue
		}
		// 存处理ssse响应
		s.pcId2ServiceListMap[models.ProductCategory(product.ProductCategory)] = data[index]
	}

	return true
}

// BuildCommonBizInfo RPC
func (s *AdditionalService) BuildCommonBizInfo(ctx context.Context, commonBizInfo *models.CommonBizInfo) {
}

// BuildProductBizInfo RPC
func (s *AdditionalService) BuildProductBizInfo(ctx context.Context, product models.Product,
	privateBizInfo *models.PrivateBizInfo) {
	//
	//// 构建每个品类自己支持的服务
	//if value, ok := s.pcId2ServiceListMap[models.ProductCategory(product.ProductCategory)]; ok {
	//	if len(value) > 0 {
	//		privateBizInfo.CustomFeatureList = value
	//	}
	//}
}

// getNeedFilterServiceIdList Filter 返回需要过滤的服务id列表
func getNeedFilterServiceIdList(ctx context.Context, req *models.BaseReqData) []int64 {
	var needFilterServiceIdList []int64

	// 用户没选择服务

	// 后续需要dos中存储全品类的用户选择的需求   目前都是
	if len(req.CommonBizInfo.PcId2CustomFeature) > 0 {
		// 配置的附加需求列表
		IdConfigMap := getAdditionalServiceIdConfigMap(ctx)

		for _, featureStruct := range req.CommonBizInfo.PcId2CustomFeature {

			for _, service := range featureStruct {
				needFilter, isAdditionalService := IdConfigMap[service.Id]
				if isAdditionalService && needFilter {
					// 是附加服务 && 需要过滤品类
					needFilterServiceIdList = append(needFilterServiceIdList, service.Id)
				}
			}
		}
	}

	//if len(req.CommonBizInfo.SelectPcId2CustomFeature) > 0 {
	//	IdConfigMap := getAdditionalServiceIdConfigMap(ctx)
	//	for _, featureStruct := range req.CommonBizInfo.SelectPcId2CustomFeature {
	//
	//		for _, service := range featureStruct {
	//			needFilter, isAdditionalService := IdConfigMap[int64(service.ID)]
	//			if isAdditionalService && needFilter {
	//				// 是附加服务 && 需要过滤品类
	//				needFilterServiceIdList = append(needFilterServiceIdList, int64(service.ID))
	//			}
	//		}
	//	}
	//}

	return needFilterServiceIdList
}

// Exec Filter 选择过滤的品类Id
func (s *AdditionalService) Exec(ctx context.Context, products []*models.Product) (needFilterPcIds []models.ProductCategory) {
	if len(s.needSupportServiceIdList) < 1 {
		// 没有必须支持的服务, 不需要过滤
		return
	}

	// 遍历品类
	for _, product := range products {
		//宠物出行屏蔽增值服务过滤逻辑
		if product.ProductID == product_id.ProductIdPetFastCar {
			continue
		}
		if len(product.BizInfo.CustomFeatureList) == 0 {
			// 需要过滤
			needFilterPcIds = append(needFilterPcIds, models.ProductCategory(product.ProductCategory))
			continue
		}

		// 必须包含needFilterServiceIdMap内所有服务
		productSupportServiceMap := make(map[int64]interface{})
		for _, featureStruct := range product.BizInfo.CustomFeatureList {
			productSupportServiceMap[featureStruct.ServiceId] = true
		}
		for _, serviceId := range s.needSupportServiceIdList {
			// 遍历品类支持的服务, 如果不在必须列表, 则剔除
			if _, ok := productSupportServiceMap[serviceId]; !ok {
				needFilterPcIds = append(needFilterPcIds, models.ProductCategory(product.ProductCategory))
				break
			}
		}
	}

	return needFilterPcIds
}

// ServiceConfig apollo配置
type ServiceConfig struct {
	Id       int64 `json:"id"`
	IsFilter bool  `json:"is_filter"`
}

// Exec Filter 获取附加需求配置  (id:服务id, is_filter:是否需要过滤品类)
func getAdditionalServiceIdConfigMap(ctx context.Context) map[int64]bool {
	config := apollo.GetConfig(ctx, "payment_type_component", "additional_service")
	serviceListString, ok := config["service_list"]
	if !ok {
		return nil
	}

	var list []ServiceConfig
	if err := json.Unmarshal([]byte(serviceListString), &list); err != nil || len(list) < 1 {
		return nil
	}

	IdMap := make(map[int64]bool, len(list))
	for _, serviceConfig := range list {
		IdMap[serviceConfig.Id] = serviceConfig.IsFilter
	}
	return IdMap
}
