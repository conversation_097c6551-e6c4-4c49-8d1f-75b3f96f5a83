package rpc_process

import (
	"context"
	Apollo2 "git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/guardian"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"
)

const guardianIdentitySwitch = "gs_station_bus_guardian_switch"

type GuardianIdentityInfo struct {
	baseReq        *models.BaseReqData
	req            *proto.IntercityEstimateDetailRequest
	identityStatus bool
}

func NewGuardianIdentityInfo(req *proto.IntercityEstimateDetailRequest, baseReq *models.BaseReqData) *GuardianIdentityInfo {
	return &GuardianIdentityInfo{
		baseReq: baseReq,
		req:     req,
	}
}

func (g *GuardianIdentityInfo) GetErrorInfo(ctx context.Context) error {
	return nil
}

func (g *GuardianIdentityInfo) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
}

func (g *GuardianIdentityInfo) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.CanGetIdentityFromGuardian = g.identityStatus
}

func (g *GuardianIdentityInfo) Fetch(ctx context.Context, products []*models.Product) bool {
	param := make(map[string]string)
	param["app_version"] = g.req.AppVersion
	param["access_key_id"] = cast.ToString(g.req.AccessKeyId)
	param["pid"] = cast.ToString(g.baseReq.PassengerInfo.PID)
	toggle := Apollo2.FeatureToggle(ctx, guardianIdentitySwitch, cast.ToString(g.baseReq.PassengerInfo.UID), param)
	if toggle {
		status := guardian.GetPlatformStatus(ctx, g.baseReq.PassengerInfo.UID)
		g.identityStatus = status
	} else {
		g.identityStatus = false
	}
	return true
}
