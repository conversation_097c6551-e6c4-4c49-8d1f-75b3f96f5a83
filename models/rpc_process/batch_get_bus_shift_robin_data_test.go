package rpc_process

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"testing"
)

func TestNewBusInventoryRobinRPC(t *testing.T) {
	// city命中
	req := &models.BaseReqData{
		AreaInfo: models.AreaInfo{
			City: 1,
		},
		PassengerInfo: models.PassengerInfo{
			PID:   17598245467109,
			Phone: "16601873761",
		},
	}
	// 均不命中
	req1 := &models.BaseReqData{
		AreaInfo: models.AreaInfo{
			City: 2,
		},
		PassengerInfo: models.PassengerInfo{
			PID:   17598245467108,
			Phone: "16601873762",
		},
	}
	var req2 *models.BaseReqData
	var po *BusShiftRobinData

	// 模拟apollo.FeatureToggle
	featureToggleMock := mockey.Mock(apollo.FeatureToggle).To(func(ctx context.Context, key string, pid string, params map[string]string) bool {
		return true
	}).Build()

	defer featureToggleMock.UnPatch()

	mockey.PatchConvey("TestCheckParamsTrue", t, func() {
		// 模拟apollo.FeatureToggle
		featureToggleMock := mockey.Mock(apollo.FeatureToggle).To(func(ctx context.Context, key string, pid string, params map[string]string) bool {
			return true
		}).Build()
		defer featureToggleMock.UnPatch()
		res := NewBusInventoryRobinRPC(req)
		convey.So(res, convey.ShouldEqual, po)
		res2 := NewBusInventoryRobinRPC(req2)
		convey.So(res2, convey.ShouldEqual, po)
	})

	mockey.PatchConvey("TestCheckParamsFalse", t, func() {
		// 模拟apollo.FeatureToggle
		featureToggleMock := mockey.Mock(apollo.FeatureToggle).To(func(ctx context.Context, key string, pid string, params map[string]string) bool {
			return false
		}).Build()
		defer featureToggleMock.UnPatch()
		res1 := NewBusInventoryRobinRPC(req1)
		convey.So(res1, convey.ShouldNotEqual, po)
	})

}
