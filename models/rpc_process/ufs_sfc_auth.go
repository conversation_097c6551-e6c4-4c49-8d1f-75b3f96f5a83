package rpc_process

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/tripcloud"
	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/ufsClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/trace"
)

type UfsSFCAuthFeature struct {
	Domain      string
	PassengerID string
	AuthKey     string

	RemoveTripCloud []models.ProductCategory
	//FinalMapProductCategory map[int64]bool
	FinalMapBusinessID map[int64]bool
}

func NewSFCAuthRPC(ctx context.Context, domain string, pid string) *UfsSFCAuthFeature {

	return &UfsSFCAuthFeature{
		Domain:             domain,
		PassengerID:        pid,
		AuthKey:            ufs.SFCTripcloudAuthKey,
		RemoveTripCloud:    make([]models.ProductCategory, 0),
		FinalMapBusinessID: make(map[int64]bool),
	}
}

func (uf *UfsSFCAuthFeature) Fetch(ctx context.Context, productFulls []*biz_runtime.ProductInfoFull) bool {
	var (
		//三方车型
		allTripCloud = make([]models.ProductCategory, 0) // 虽然名字叫tripcloud，但实际上有些场景加了个补天品类
		searchMap    = make(map[int64][]models.ProductCategory)
		// UFSFeatures UFS特征
		UFSFeatures = make([]ufsClient.Feature, 0)
		// AuthBusiness VideoBusiness key：businessID，value：是否授权
		AuthBusiness = make(map[int64]bool)
	)

	for _, p := range productFulls {
		if tripcloud.IsTripcloudProductID(consts.ProductID(p.GetProductId())) {
			allTripCloud = append(allTripCloud, models.ProductCategory(p.GetProductCategory()))
			searchMap[p.GetBusinessID()] = append(searchMap[p.GetBusinessID()], models.ProductCategory(p.GetProductCategory()))
		}
	}

	//构建用户未授权的三方feature列表
	for businessID := range searchMap {
		feature := ufsClient.Feature{
			Domain: uf.Domain,
			Keys:   []string{uf.AuthKey},
			Params: map[string]string{
				"passenger_id": uf.PassengerID,
				"business_id":  strconv.FormatInt(businessID, 10),
			},
		}
		UFSFeatures = append(UFSFeatures, feature)
	}

	UFSResp, err := ufs.MultiGetFeatures(ctx, UFSFeatures, "")
	if err != nil {
		//若获取三方授权和录音录像授权，若状态失败，则remove所有三方车型
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "get auth status from ufs fail with err %v", err)
		uf.RemoveTripCloud = allTripCloud
		return false
	}

	//把UFS结果映射为map
	for strFeature, ufsResp := range UFSResp {
		_, key, params, featureErr := ufsClient.ParseFeature(strFeature)
		if featureErr != nil {
			uf.RemoveTripCloud = allTripCloud
			return false
		}

		if key == uf.AuthKey {
			//三方品类授权状态
			businessID, authErr := strconv.ParseInt(params["business_id"], 10, 64)
			if authErr != nil || businessID == 0 {
				uf.RemoveTripCloud = allTripCloud
				return false
			}

			if ufsResp == nil || ufsResp.Errno != 0 || *ufsResp.Value != "1" {
				AuthBusiness[businessID] = true
			}
		}
	}

	// 1表示授权过了
	//保存未授权录音录像与三方协议的全部品类
	for _, p := range productFulls {
		if !tripcloud.IsTripcloudProductID(consts.ProductID(p.GetProductId())) {
			continue
		}

		_, isAuth := AuthBusiness[p.GetBusinessID()]
		if isAuth {
			uf.FinalMapBusinessID[p.GetBusinessID()] = true
			continue
		}
	}

	return true
}

func (uf *UfsSFCAuthFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {

}

func (uf *UfsSFCAuthFeature) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	//非三方品类不需要判定要不要授权
	if !tripcloud.IsTripcloudProductID(consts.ProductID(product.GetProductId())) {
		return
	}

	_, IsNeed := uf.FinalMapBusinessID[product.GetBusinessID()]
	//已授权的三方品类不需要绑定
	if !IsNeed {
		return
	}

	info.UfsTripCloudAuthBusinessID = uf.FinalMapBusinessID

	return
}

func (uf *UfsSFCAuthFeature) HandlerFilter(ctx context.Context, productMap map[int64]*biz_runtime.ProductInfoFull) []models.ProductCategory {
	// ufs读失败，应该不出，给remove掉
	return uf.RemoveTripCloud
}
