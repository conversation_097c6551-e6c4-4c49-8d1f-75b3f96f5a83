package rpc_process

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestCompensationInfoV2Fetch(t *testing.T) {
	obj := &CompensationInfoV2{}
	productInfos := []*biz_runtime.ProductInfoFull{
		{
			BaseReqData: &models.BaseReqData{
				AreaInfo:      models.AreaInfo{},
				CommonInfo:    models.CommonInfo{},
				PassengerInfo: models.PassengerInfo{},
				DriverInfo:    models.DriverInfo{},
				CommonBizInfo: models.CommonBizInfo{
					BusinessTravelOrderType: "xinzhu_v1",
				},
				SendOrder:           models.SendOrder{},
				RawUserSelectOption: nil,
			},
			Product:      nil,
			BillDetail:   nil,
			DiscountInfo: nil,
			PayInfo:      nil,
			ExtraInfo:    nil,
		},
	}
	ok := obj.Fetch(context.Background(), productInfos)
	assert.Equal(t, ok, false)
}
