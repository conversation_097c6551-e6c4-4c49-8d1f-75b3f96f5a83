package rpc_process

import (
	"context"
	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/prfs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

const (
	FenceStation   = 1
	FencePrefixKey = "fence_station_intro"
)

type BusStationItemInfoData struct {
	baseReq *models.BaseReqData

	StartItemInfo  *models.ItemInfo
	EndItemInfo    *models.ItemInfo
	StationInfoMap map[int32]*Prfs.StationInfo
}

func NewBusStationItemInfoRPC(baseReq *models.BaseReqData) *BusStationItemInfoData {
	if baseReq == nil {
		return nil
	}

	return &BusStationItemInfoData{
		baseReq: baseReq,
	}
}

func (bs *BusStationItemInfoData) Fetch(ctx context.Context) bool {
	bs.StationInfoMap = bs.GetBatchStationInfo(ctx)
	// 起点数据
	bs.StartItemInfo = bs.GetBusItemInfo(ctx, bs.baseReq.CommonBizInfo.StationInfo.StartCity, bs.baseReq.CommonBizInfo.StationInfo.StartCountyId, bs.baseReq.CommonBizInfo.StationInfo.StartStationId)
	// 终点数据
	bs.EndItemInfo = bs.GetBusItemInfo(ctx, bs.baseReq.CommonBizInfo.StationInfo.EndCity, bs.baseReq.CommonBizInfo.StationInfo.EndCountyId, bs.baseReq.CommonBizInfo.StationInfo.EndStationId)

	return true
}

func (bs *BusStationItemInfoData) GetBatchStationInfo(ctx context.Context) map[int32]*Prfs.StationInfo {
	stationInfoMap := make(map[int32]*Prfs.StationInfo)

	// 站点列表
	stationIdList := make([]string, 0, 2)
	if bs.baseReq.CommonBizInfo.StationInfo.StartStationId != 0 {
		stationIdList = append(stationIdList, cast.ToString(bs.baseReq.CommonBizInfo.StationInfo.StartStationId))
	}

	if bs.baseReq.CommonBizInfo.StationInfo.EndStationId != 0 {
		stationIdList = append(stationIdList, cast.ToString(bs.baseReq.CommonBizInfo.StationInfo.EndStationId))
	}

	// 获取起终点信息
	stationReq := &Prfs.PGetStationInfoBatchReq{
		StationIdList: stationIdList,
	}

	stationInfoResp := prfs.GetBatchStationInfo(ctx, stationReq)

	if stationInfoResp == nil || stationInfoResp.Data == nil || len(stationInfoResp.Data) == 0 {
		return nil
	}

	for _, stationInfo := range stationInfoResp.Data {
		if stationInfo == nil {
			continue
		}

		stationId := stationInfo.StationId
		stationInfoMap[stationId] = stationInfo
	}

	return stationInfoMap
}

func (bs *BusStationItemInfoData) GetBusItemInfo(ctx context.Context, cityId int32, countyId int32, stationId int64) *models.ItemInfo {
	if cityId == 0 && countyId == 0 && stationId == 0 {
		return nil
	}

	itemInfo := &models.ItemInfo{}

	if cityId != 0 {
		cityInfo, err := util.GetCityInfo(ctx, cityId)
		if err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "city id = %v || get city info error = %v", cityId, err)
			return nil
		}

		if cityInfo != nil {
			itemInfo.CityId = cityId
			itemInfo.CityName = cityInfo.CityDesc
			itemInfo.Lat = cast.ToFloat64(cityInfo.CityLat)
			itemInfo.Lng = cast.ToFloat64(cityInfo.CityLng)
		}
	}

	if countyId != 0 {
		countyInfo, err := util.GetCountyInfo(ctx, countyId)

		if err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "county id = %v || get county info error = %v", countyId, err)
		}

		if countyInfo != nil {
			itemInfo.CountyId = countyId
			itemInfo.CountyName = countyInfo.CountyName
		}
	}

	if stationId != 0 {
		if bs.StationInfoMap == nil || len(bs.StationInfoMap) == 0 {
			return itemInfo
		}

		stationInfo := bs.StationInfoMap[cast.ToInt32(stationId)]

		if stationInfo == nil {
			return itemInfo
		}

		itemInfo.Lat = cast.ToFloat64(stationInfo.StationLat)
		itemInfo.Lng = cast.ToFloat64(stationInfo.StationLng)
		itemInfo.StationName = stationInfo.StationName
		// 如果是围栏站点，拼接数据
		if stationInfo.StationSceneType == FenceStation {
			config := dcmp.GetDcmpContent(ctx, "intercity_station-stationinfo", nil)
			itemInfo.StationName = gjson.Get(config, FencePrefixKey).String() + itemInfo.StationName
		}
	}

	return itemInfo
}

func (bs *BusStationItemInfoData) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	if bs.StartItemInfo != nil {
		info.StartItemInfo = bs.StartItemInfo
	}

	if bs.EndItemInfo != nil {
		info.EndItemInfo = bs.EndItemInfo
	}
}
