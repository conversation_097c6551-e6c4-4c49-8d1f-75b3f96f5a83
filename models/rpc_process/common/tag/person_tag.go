package tag

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/tag_service"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/golibs/knife"
	"github.com/spf13/cast"
)

type PersonTag struct {
	baseReq   *models.BaseReqData
	personTag string
}

// UserTag 用户标签结构体
type UserTag struct {
	TagID   string `json:"tag_id"`
	TagName string `json:"tag_name"`
}

// 用户标签常量
const (
	LossTag             = "1001380815" // 流失用户
	NewTag              = "1001267300" // 新用户
	ApolloConfPersonTag = "apollo_conf_person_tag"
	DefaultPersonTag    = "normal_user" // 默认用户标签
)

func NewPersonTag(baseReq *models.BaseReqData) *PersonTag {
	return &PersonTag{
		baseReq: baseReq,
	}
}

func (p *PersonTag) Fetch(ctx context.Context) bool {
	if p.baseReq == nil {
		return true
	}

	baseReq := p.baseReq

	apolloParams := map[string]string{
		"key":           cast.ToString(baseReq.PassengerInfo.PID),
		"pid":           cast.ToString(baseReq.PassengerInfo.PID),
		"lang":          baseReq.CommonInfo.Lang,
		"access_key_id": cast.ToString(baseReq.CommonInfo.AccessKeyID),
		"app_version":   baseReq.CommonInfo.AppVersion,
	}

	// 调用Apollo featureToggle
	allow, allParams := apollo.GetParameters(ApolloConfPersonTag, cast.ToString(baseReq.PassengerInfo.PID), apolloParams)
	if !allow || allParams == nil {
		return true
	}

	userTagsStr, exists := allParams["user_tags"]
	if !exists || userTagsStr == "" {
		return true
	}

	// 解析JSON获取用户标签数组
	var userTags []UserTag
	err := json.Unmarshal([]byte(userTagsStr), &userTags)
	if err != nil {
		log.Trace.Warnf(ctx, "person_tag_parse_error", "failed to parse user_tags JSON: %v", err)
		return true
	}

	if len(userTags) == 0 {
		return true
	}

	// 提取所有tag_id
	tagIDs := make([]string, 0, len(userTags))
	for _, tag := range userTags {
		tagIDs = append(tagIDs, tag.TagID)
	}

	// 调用tag service获取命中的标签
	hitTagList, err := tag_service.GetHitTags(ctx, cast.ToString(baseReq.PassengerInfo.PID), tagIDs)
	if err != nil {
		log.Trace.Warnf(ctx, "person_tag_service_error", "failed to get hit tags: %v", err)
		return true
	}

	if len(hitTagList) == 0 {
		return true
	}

	// 将命中的标签转换为map以便快速查找
	hitTagMap := make(map[string]bool)
	for _, hitTag := range hitTagList {
		hitTagMap[hitTag] = true
	}

	// 遍历用户标签，找到第一个命中的标签并返回其tag_name
	for _, tag := range userTags {
		if hitTagMap[tag.TagID] {
			p.personTag = tag.TagName
			return true
		}
	}

	return true
}

func (p *PersonTag) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {

	// 缓存person_tag到context中
	knife.Set(ctx, consts.PersonTagKey, p.personTag)
}

func GetPersonTag(ctx context.Context) string {
	if v := knife.Get(ctx, consts.PersonTagKey); v != nil {
		return v.(string)
	}
	return ""
}
