package common

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"time"

	Hilda "git.xiaojukeji.com/dirpc/dirpc-go-http-Hilda"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/hilda"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"
)

const (
	// Apollo配置key
	HildaPrivilegeUpstreamFeature = "privilege_hilda_upstream"

	// Hilda RPC请求参数常量
	HildaPrivilegeType = "surprise_privilege" // 查询惊喜特价权益
	HildaCaller        = "mamba"              // 调用方标识

	SurpriseSpecialCard = "surprise_special_card"
)

// HildaPrivilege Hilda权益卡RPC处理器
type HildaPrivilege struct {
	baseReq       *models.BaseReqData
	privilegeInfo *Hilda.QueryPrivilegeResp // 权益卡信息
	errorInfo     error                     // 错误信息
}

// NewHildaPrivilege 创建Hilda权益卡RPC处理器
func NewHildaPrivilege(ctx context.Context, baseReq *models.BaseReqData) *HildaPrivilege {
	if baseReq == nil {
		return nil
	}

	params := map[string]string{
		"key":    cast.ToString(baseReq.PassengerInfo.PID),
		"pid":    cast.ToString(baseReq.PassengerInfo.PID),
		"phone":  baseReq.PassengerInfo.Phone,
		"city":   cast.ToString(baseReq.AreaInfo.Area),
		"county": cast.ToString(baseReq.AreaInfo.FromCounty),
	}
	if !apollo.FeatureToggle(ctx, HildaPrivilegeUpstreamFeature, cast.ToString(baseReq.PassengerInfo.PID), params) {
		return nil
	}

	return &HildaPrivilege{
		baseReq:       baseReq,
		privilegeInfo: nil,
	}
}

// Fetch 获取Hilda权益卡数据
func (h *HildaPrivilege) Fetch(ctx context.Context, products []*models.Product) bool {
	if len(products) == 0 {
		log.Trace.Warnf(ctx, "hilda_privilege_no_products", "No products for hilda privilege")
		return true
	}

	// 构建请求参数
	now := time.Now().Unix()

	// 构建起点POI
	startPoi := &Hilda.POI{
		PoiId:       h.baseReq.AreaInfo.FromPoiID,
		Lng:         cast.ToString(h.baseReq.AreaInfo.FromLng),
		Lat:         cast.ToString(h.baseReq.AreaInfo.FromLat),
		Displayname: h.baseReq.AreaInfo.FromName,
		CityId:      h.baseReq.AreaInfo.Area,
	}

	// 构建终点POI
	endPoi := &Hilda.POI{
		PoiId:       h.baseReq.AreaInfo.ToPoiID,
		Lng:         cast.ToString(h.baseReq.AreaInfo.ToLng),
		Lat:         cast.ToString(h.baseReq.AreaInfo.ToLat),
		Displayname: h.baseReq.AreaInfo.ToName,
		CityId:      h.baseReq.AreaInfo.ToArea,
	}

	productCategoryInt32 := make([]int32, len(products))
	for i, pc := range products {
		productCategoryInt32[i] = int32(pc.ProductCategory)
	}

	request := &Hilda.QueryPrivilegeReq{
		EstimateTime:    now,
		DepartureTime:   h.baseReq.CommonInfo.DepartureTime,
		AirportType:     h.baseReq.CommonInfo.AirportType,
		StartPoi:        startPoi,
		EndPoi:          endPoi,
		ProductCategory: productCategoryInt32,
		// PrivilegeType:   []string{HildaPrivilegeType}, // 查询惊喜特价权益
		Caller: HildaCaller,
		Uid:    h.baseReq.PassengerInfo.UID,
		Pid:    h.baseReq.PassengerInfo.PID,
	}

	// 调用Hilda服务
	response, err := hilda.PrivilegeQueryV2(ctx, request)
	if err != nil {
		log.Trace.Warnf(ctx, "hilda_privilege_call_error", "Failed to call hilda service: %v", err)
		h.errorInfo = err
		return true // 不阻断流程
	}

	h.privilegeInfo = response
	return true
}

// BuildCommonBizInfo 构建通用业务信息
func (h *HildaPrivilege) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {

}

// BuildProductBizInfo 构建产品业务信息
func (h *HildaPrivilege) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {

	// 目前只设置惊喜特价权益卡
	if estimate_pc_id.EstimatePcIdEstimatePcIdTimeLimitSpecialRate != product.GetProductCategory() {
		return
	}

	// 根据产品类别获取对应的权益信息
	productCategory := int32(product.ProductCategory)
	privilegeMap, exists := h.privilegeInfo.GetData()[productCategory]
	if !exists {
		return
	}

	if v, ok := privilegeMap[SurpriseSpecialCard]; ok {
		if v.GetEnable() {
			info.HildaPrivilegeInfo = &models.PrivilegeInfo{
				SurpriseSpecialCardBatchId: cast.ToString(v.GetBatchId()),
			}
		}
	}
}

// GetErrorInfo 获取错误信息
func (h *HildaPrivilege) GetErrorInfo(ctx context.Context) error {
	return h.errorInfo
}
