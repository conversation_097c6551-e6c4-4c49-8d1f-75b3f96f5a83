package model

import AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"

type RecFormFeatureModel struct {
	RecGroup2FoldType map[string]int32
	RecGroup2tags     map[string]*TagInfo // 推荐区组标签文案
	RecGroup2SubType  map[string]int32

	RecTabProductSortInfo  *AthenaApiv3.ProductSortInfo
	RecTabFoldSubGroupDown *int32
}

type TagInfo struct {
	SubTagContent   string `json:"sub_tag_content"`
	RightTagContent string `json:"right_tag_content"`
}
