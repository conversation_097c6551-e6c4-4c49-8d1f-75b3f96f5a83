package athena_bubble_recommend

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/redis"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/instance"
	"git.xiaojukeji.com/s3e/common-lib/v2/component/diff"
)

// BubbleRecommendAdapter 冒泡推荐适配器，用于兼容原有接口
type BubbleRecommendAdapter struct {
	generator  *models.BaseReqData
	formClient FormSpecificClient
	//response   *FormSpecificResponse
}

func getClient(req *models.BaseReqData, orderMatchType int32) FormSpecificClient {
	if req.CommonInfo.TabId == tab.TabIdClassify {
		return instance.NewClassifyClient(req)
	}
	return instance.NewBaseClient(orderMatchType, "dafault", req)
}

// NewBubbleRecommendAdapter 创建冒泡推荐适配器
func NewBubbleRecommendAdapter(req *models.BaseReqData, orderMatchType int32) *BubbleRecommendAdapter {
	client := getClient(req, orderMatchType)

	return &BubbleRecommendAdapter{
		formClient: client,
		generator:  req,
	}
}

func (a *BubbleRecommendAdapter) Fetch(ctx context.Context, products []*biz_runtime.ProductInfoFull) bool {
	if len(products) == 0 {
		return true
	}
	request, err := a.formClient.BuildRequest(ctx, a.generator, products)
	if err != nil {

	}

	var resp interface{}
	if diff.CheckDiffStatus(ctx) {
		mocker := diff.NewMockInterceptor(redis.GetMultiEstimateClient(), diff.MultiEstimateDiffPrefix)
		resp = &AthenaApiv3.AthenaGuideRecommendResp{}
		err = mocker.MockResp(ctx, diff.DownStreamDirpcType, "AthenaBubbleRecommend", request, resp)
		if err != nil {
			log.Trace.Warnf(ctx, diff.DiffTag, "mockResp fail:%v", err)
			return false
		}
	} else {
		resp, err = a.formClient.CallAthenaAPI(ctx, request)
		if err != nil {

		}
	}

	err = a.formClient.SetAthenaFeature(ctx, resp)
	if err != nil {

	}
	return true
}

func (a *BubbleRecommendAdapter) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	a.formClient.BuildCommonBizInfo(ctx, info)
}

func (a *BubbleRecommendAdapter) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	a.formClient.BuildProductFeature(ctx, product, info)
}

func (a *BubbleRecommendAdapter) HandlerFilter(ctx context.Context, productMap map[int64]*biz_runtime.ProductInfoFull) []models.ProductCategory {
	return a.formClient.HandlerFilter(ctx, productMap)
}
