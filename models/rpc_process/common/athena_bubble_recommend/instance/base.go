package instance

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/athena"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/feature"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/feature/common"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/feature/filter"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/instance/param"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/instance/param/form"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/instance/param/order_match"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/instance/param/wrapper"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/model"
)

// BaseClient 基础客户端，包含通用的方法实现
type BaseClient struct {
	orderMatchType int32
	formType       model.FormType
	baseReq        *models.BaseReqData

	FeatureSetter []feature.SetRecommendFeature
	FilterSetter  []biz_runtime.FinalProductsFilterV2
}

// NewBaseClient 创建基础客户端
func NewBaseClient(orderMatchType int32, formType model.FormType, baseReq *models.BaseReqData) *BaseClient {
	return &BaseClient{
		orderMatchType: orderMatchType,
		formType:       formType,
		baseReq:        baseReq,
	}
}

// BuildRequest 构建Athena请求（从原始athena_bubble_recommend.go的NewBubbleRecommendInfoRPC提取）
func (c *BaseClient) BuildRequest(ctx context.Context, ge *models.BaseReqData, products []*biz_runtime.ProductInfoFull) (interface{}, error) {

	athenaReq := param.NewAthenaReq()

	wrappers := c.GetRequestWrapper(ctx, ge, products)

	err := athenaReq.SetAthenaReq(ctx, wrappers)

	if err != nil {
		return athenaReq.GetRequest(), err
	}

	return athenaReq.GetRequest(), nil
}

func (c *BaseClient) GetRequestWrapper(ctx context.Context, ge *models.BaseReqData, products []*biz_runtime.ProductInfoFull, _productParamSetter ...wrapper.ProductRequestWrapper) []wrapper.AthenaRequestWrapper {

	// 基础wrapper列表
	wrappers := []wrapper.AthenaRequestWrapper{
		param.WithCommonInfo(ge.CommonInfo, c.orderMatchType),
		param.WithLocationInfo(ge.AreaInfo),
		param.WithUserInfo(ge.PassengerInfo),
		param.WithPassengerPreferenceList(ge.CommonBizInfo, c.orderMatchType),
		param.WithProductList(products, c.orderMatchType, _productParamSetter...),
	}

	if c.orderMatchType == param.AnycarOrderMatchType {
		// 等待应答场景
		wrappers = append(wrappers,
			order_match.WithOrderInfo(ge.SendOrder),
			order_match.WithBubbleTraceId(ge.CommonInfo),
		)
	} else {
		// 非等待应答场景（主预估等）
		wrappers = append(wrappers,
			form.WithFormBasicInfo(ge.CommonInfo),
			form.WithFilterStyle(ge, c.orderMatchType),
		)

		// 如果需要默认拼参数
		if ge.CommonBizInfo.RecCarpoolFormSytle != 0 {
			wrappers = append(wrappers, param.WithRecParams(ge.CommonBizInfo))
		}

		//wrappers = append(wrappers, param.WithExtraInfo(ge.CommonInfo))
	}
	return wrappers
}

func (c *BaseClient) CallAthenaAPI(ctx context.Context, request interface{}) (interface{}, error) {
	if req, ok := request.(*AthenaApiv3.AthenaBubbleRecommendReq); ok {
		resp := athena.GetAthenaBubbleRecommend(ctx, req)
		return resp, nil
	}

	return nil, nil
}

func (c *BaseClient) SetAthenaFeature(ctx context.Context, res interface{}) error {
	c.GetFeatureSetter(ctx, res.(*AthenaApiv3.AthenaGuideRecommendResp))
	return nil
}

func (c *BaseClient) GetFeatureSetter(ctx context.Context, res *AthenaApiv3.AthenaGuideRecommendResp, opts ...feature.FeatureOption) {
	config := &feature.FeatureConfig{}

	// 应用选项
	for _, opt := range opts {
		if opt != nil {
			opt(config)
		}
	}

	c.FeatureSetter = append(c.FeatureSetter,
		common.NewTopRecommendFeature().SetTopRecommendFeature(res.TopRec),
		common.NewDefaultCheck().SetFeature(ctx, res.CheckedProductCategory),
		common.NewSupplyInfoFeature().SetFeature(ctx, res),
		common.NewExtraInfo().SetFeature(ctx, res),
		common.NewExpectInfo().SetFeature(ctx, res),
		feature.NewBoxFeatures(config.BoxFeatureSetter...).SetBoxFeature(ctx, res.SubGroupRecList),
		feature.NewSingleCarFeatures(config.SingleFeatureSetter...).SetSingleCarFeature(ctx, res.RecResult_),
	)
	c.FilterSetter = append(c.FilterSetter,
		filter.NewFilterFeature(res.DisableProductCategory),
	)
}

func (c *BaseClient) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	for _, featureSetter := range c.FeatureSetter {
		featureSetter.BuildCommonBizInfo(ctx, info)
	}
}

func (c *BaseClient) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	for _, featureSetter := range c.FeatureSetter {
		featureSetter.BuildProductFeature(ctx, product, info)
	}
}

func (c *BaseClient) HandlerFilter(ctx context.Context, productMap map[int64]*biz_runtime.ProductInfoFull) []models.ProductCategory {
	res := make([]models.ProductCategory, 0)
	for _, featureSetter := range c.FilterSetter {
		res = append(res, featureSetter.HandlerFilter(ctx, productMap)...)
	}
	return res
}
