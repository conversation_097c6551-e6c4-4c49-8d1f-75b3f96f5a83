package form

import (
	"context"
	"encoding/json"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/instance/param"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/instance/param/wrapper"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/util"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

// WithFormBasicInfo 设置冒泡基础参数
func WithFormBasicInfo(commonInfo models.CommonInfo) wrapper.AthenaRequestWrapper {
	return func(ctx context.Context, req *AthenaApiv3.AthenaBubbleRecommendReq) error {
		if commonInfo.TabList != "" {
			var tabList []*AthenaApiv3.TabItem
			_ = json.Unmarshal([]byte(commonInfo.TabList), &tabList)
			req.AthenaBubbleReq.TabList = tabList
		}
		req.AthenaBubbleReq.EstimateStyleType = &commonInfo.EstimateStyleType
		req.AthenaBubbleReq.TabID = &commonInfo.TabId
		req.AthenaBubbleReq.PreferenceFilterID = &commonInfo.PreferenceFilterId
		req.AthenaBubbleReq.FormHeight = &commonInfo.FormHeight
		req.AthenaBubbleReq.FontScaleType = &commonInfo.FontScaleType
		return nil
	}
}

// WithFilterStyle 设置过滤器样式
func WithFilterStyle(baseReqData *models.BaseReqData, orderMatchType int32) wrapper.AthenaRequestWrapper {
	return func(ctx context.Context, req *AthenaApiv3.AthenaBubbleRecommendReq) error {
		style := int32(0)
		if baseReqData.CommonInfo.OrderType != 0 {
			req.AthenaBubbleReq.FilterStyle = &style
			return nil
		}

		if baseReqData.CommonInfo.Lang != "zh-CN" {
			req.AthenaBubbleReq.FilterStyle = &style
			return nil
		}

		if orderMatchType == param.AnycarOrderMatchType {
			req.AthenaBubbleReq.FilterStyle = &style
			return nil
		}

		// 筛选器暂不支持
		//style = int32(2)
		req.AthenaBubbleReq.FilterStyle = &style
		return nil
	}
}

// WithExtraInfo 设置额外信息
func WithExtraInfo(products []*biz_runtime.ProductInfoFull) wrapper.AthenaRequestWrapper {
	return func(ctx context.Context, req *AthenaApiv3.AthenaBubbleRecommendReq) error {
		if req.AthenaBubbleReq.ExtraInfo == nil {
			req.AthenaBubbleReq.ExtraInfo = make(map[string]string)
		}

		for _, p := range products {
			if p.GetBizInfo().UserMemberProfile.LevelID > 0 {
				req.AthenaBubbleReq.ExtraInfo["member_level_id"] = cast.ToString(p.GetBizInfo().UserMemberProfile.LevelID)
			}

			historyExtra := p.GetBillExtraMap()
			if historyExtra != nil {
				if argueCarInfoString, ok := historyExtra["argue_car_info"].(string); ok {
					req.AthenaBubbleReq.ExtraInfo["bargain_recommend_status"] =
						gjson.Get(argueCarInfoString, "bubble_info").Get("recommend_status").String()
					req.AthenaBubbleReq.ExtraInfo["price_strategy_type"] =
						gjson.Get(argueCarInfoString, "price_strategy_type").String()
				}
			}

			req.AthenaBubbleReq.ExtraInfo["from_type"] = cast.ToString(p.BaseReqData.CommonInfo.FromType)
		}

		// TODO: 根据具体需求添加更多的extraInfo逻辑
		return nil
	}
}

func WithExpectScene() wrapper.AthenaRequestWrapper {
	return func(ctx context.Context, req *AthenaApiv3.AthenaBubbleRecommendReq) error {
		req.AthenaBubbleReq.ExpectScene = []string{
			consts.ClassifyFormGlobalSceneExpectScene,
		}
		return nil
	}
}

func WithPlatFormCompensateInfo(info models.CommonBizInfo) wrapper.AthenaRequestWrapper {
	return func(ctx context.Context, req *AthenaApiv3.AthenaBubbleRecommendReq) error {
		req.AthenaBubbleReq.PlatformCompensateInfo = &AthenaApiv3.PlatformCompensateData{
			CompensateStatus: util.Int32Ptr(info.BubbleCompensationStatus),
			ExtraInfo:        nil,
		}
		return nil
	}
}

func WithEffectiveBenefits(products []*biz_runtime.ProductInfoFull) wrapper.AthenaRequestWrapper {
	return func(ctx context.Context, req *AthenaApiv3.AthenaBubbleRecommendReq) error {
		for _, p := range products {
			if p.GetProductCategory() != estimate_pc_id.EstimatePcIdEstimatePcIdTimeLimitSpecialRate {
				continue
			}

			if p.GetBizInfo().HildaPrivilegeInfo != nil && p.GetBizInfo().HildaPrivilegeInfo.SurpriseSpecialCardBatchId != "" {
				req.AthenaBubbleReq.EffectiveBenefits = append(req.AthenaBubbleReq.EffectiveBenefits,
					AthenaApiv3.BenefitsType_SURPRISE_CARD)
				return nil
			}
		}

		return nil
	}
}
