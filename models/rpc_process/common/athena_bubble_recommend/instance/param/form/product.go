package form

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/instance/param/wrapper"
	"github.com/spf13/cast"
)

func WithAdditionalServices() wrapper.ProductRequestWrapper {
	return func(ctx context.Context, productFull *biz_runtime.ProductInfoFull, apiProduct *AthenaApiv3.ApiAddProduct) error {
		apiProduct.ValueAddedService = make([]*AthenaApiv3.CustomFeature, 0)

		customFeatureList := productFull.GetBizInfo().CustomFeatureList
		if len(customFeatureList) > 0 {
			for _, serviceData := range customFeatureList {
				customFeature := &AthenaApiv3.CustomFeature{
					ID:    util.Int64Ptr(serviceData.ServiceId),
					Count: util.Int64Ptr(1),
				}
				apiProduct.ValueAddedService = append(apiProduct.ValueAddedService, customFeature)
			}
		}
		return nil
	}
}

func WithPriceInfo() wrapper.ProductRequestWrapper {
	return func(ctx context.Context, productFull *biz_runtime.ProductInfoFull, apiProduct *AthenaApiv3.ApiAddProduct) error {
		if apiProduct.APIGuideInfo == nil {
			apiProduct.APIGuideInfo = &AthenaApiv3.ApiGuideInfo{}
		}
		apiProduct.APIGuideInfo.PriceInfo = buildPriceInfo(productFull)
		return nil
	}
}

func WithProductExtraInfo() wrapper.ProductRequestWrapper {
	return func(ctx context.Context, full *biz_runtime.ProductInfoFull, apiProduct *AthenaApiv3.ApiAddProduct) error {
		if apiProduct.ExtraInfo == nil {
			apiProduct.ExtraInfo = make(map[string]string)
		}
		// 是否命中出租车峰期
		for _, displayLine := range full.GetBillDisplayLines() {
			if displayLine.Name == "taxi_peak_price" {
				apiProduct.ExtraInfo["is_peak_period"] = cast.ToString(1)
			}
		}

		if full.Product.ProductCategory == estimate_pc_id.EstimatePcIdBargain {
			if len(full.GetBizInfo().StartFenceList) > 0 {
				apiProduct.ExtraInfo["start_fence_id"] = util.Int64SliceToString(full.GetBizInfo().StartFenceList)
			}
			if len(full.GetBizInfo().StopFenceList) > 0 {
				apiProduct.ExtraInfo["stop_fence_id"] = util.Int64SliceToString(full.GetBizInfo().StopFenceList)
			}
		}
		return nil
	}
}
