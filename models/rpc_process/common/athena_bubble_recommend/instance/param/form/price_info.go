package form

import (
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"github.com/spf13/cast"
	"strconv"
)

// buildPriceInfo 构建价格信息映射
func buildPriceInfo(productFull *biz_runtime.ProductInfoFull) map[string]string {
	priceInfo := make(map[string]string)

	billInfo := productFull.GetBillInfo()
	if billInfo == nil {
		return priceInfo
	}
	historyExtra := productFull.GetBillExtraMap()

	// 设置基础估价ID
	priceInfo["estimate_id"] = productFull.Product.EstimateID

	// 构建总费用信息
	totalFee := map[string]interface{}{
		"estimate_fee":          productFull.GetEstimateFee(),
		"personal_estimate_fee": productFull.GetPersonalEstimateFee(),
		"coupon_amount":         getCouponAmount(productFull),
	}
	if dynamicInfo, ok := historyExtra["dynamic_info"]; ok {
		totalFee["dynamic_info"] = dynamicInfo
	}
	if discountInfo, ok := historyExtra["discount_info"]; ok {
		totalFee["discount_info"] = discountInfo
	}
	// 议价产品特殊处理（根据具体的议价产品类型）
	if productFull.GetProductCategory() == estimate_pc_id.EstimatePcIdBargain {
		if bizInfo := productFull.GetBizInfo(); bizInfo != nil {
			totalFee["left_range_price"] = bizInfo.RecommendPriceLower
			totalFee["right_range_price"] = bizInfo.RecommendPriceUpper
		}
	}
	priceInfo["total_fee"] = util.ToJSONStringNotNull(totalFee)

	// 设置基础价格字段
	priceInfo["dynamic_diff_price"] = cast.ToString(billInfo.DynamicDiffPrice)
	priceInfo["cap_price"] = cast.ToString(billInfo.CapPrice)
	priceInfo["time_cost"] = cast.ToString(billInfo.DriverMinute)
	priceInfo["driver_metre"] = cast.ToString(billInfo.DriverMetre)
	priceInfo["count_price_type"] = cast.ToString(billInfo.CountPriceType)
	priceInfo["pre_total_fee"] = cast.ToString(billInfo.PreTotalFee)
	priceInfo["fee_detail_info"] = util.ToJSONStringNotNull(billInfo.FeeDetailInfo)

	// 从HistoryExtraMap获取额外字段
	priceInfo["price_privilege_type"] = util.GetStringFromMap(historyExtra, "price_privilege_type")
	priceInfo["sp_open"] = util.GetStringFromMap(historyExtra, "sp_open")
	priceInfo["price_hide_level"] = util.GetStringFromMap(historyExtra, "price_hide_level")
	priceInfo["dape_force_sp_open"] = util.GetStringFromMap(historyExtra, "dape_force_sp_open")
	priceInfo["dape_surprise_deal_privilege_discount"] = util.GetStringFromMap(historyExtra, "dape_surprise_deal_privilege_discount")

	// 设置支付方式
	if productFull.GetPaymentInfo() != nil {
		priceInfo["payment_type"] = cast.ToString(productFull.GetPaymentInfo().DefaultPayType)
	}

	// 设置优惠信息
	// 优惠券信息
	priceInfo["coupon_info"] = buildCouponInfoJSON(productFull.GetCouponInfo())

	// A+支付返现信息
	if rebateInfo := productFull.GetRevolvingAccountRebate(); rebateInfo != nil {
		priceInfo["aplus_pay_return"] = rebateInfo.Amount
		priceInfo["crazy_aplus_pay_return_type"] = cast.ToString(rebateInfo.ActivityType)
		priceInfo["crazy_aplus_pay_return_multiple"] = cast.ToString(rebateInfo.Multiple)
	}

	// 内循环账户余额
	if discountInfo := productFull.GetRevolvingAccountDiscount(); discountInfo != nil {
		priceInfo["revolving_account_balance"] = discountInfo.Amount
	}

	// 设置拼车相关信息
	priceInfo["carpool_station_info"] = util.ToJSONStringNotNull(productFull.GetBizInfo().StationList)
	priceInfo["carpool_dual_price_info"] = buildCarpoolDualPriceInfo(productFull)

	// 设置新竹现金返还
	if page_type.PageTypePageTypeXinZhu == productFull.GetPageType() {
		if productFull.GetDiscountSet() != nil && productFull.GetDiscountSet().CashbackXinZhuInfo != nil {
			priceInfo["xin_zhu_cashback"] = cast.ToString(productFull.GetDiscountSet().CashbackXinZhuInfo.CashbackAmount)
		}
	}

	return priceInfo
}

// buildCouponInfoJSON 构建优惠券信息JSON字符串 (对应PHP的_buildCouponInfo)
func buildCouponInfoJSON(couponInfo *PriceApi.EstimateNewFormCouponInfo) string {
	if couponInfo == nil {
		return ""
	}

	couponData := make(map[string]interface{})
	var couponKey string
	if couponInfo.CouponSource == "pope" {
		couponKey = "activity_coupon"
	} else {
		couponKey = "default_coupon"
	}
	couponDetail := map[string]interface{}{
		"batch_id":      couponInfo.BatchId,
		"batch_type":    couponInfo.CouponType,
		"discount":      couponInfo.Discount,
		"coupon_amount": couponInfo.Amount,
		"coupon_type":   couponInfo.CouponType,
		"expire_time":   couponInfo.ExpireTime,
		"custom_tag":    couponInfo.CustomTag,
	}
	couponData[couponKey] = couponDetail

	if len(couponData) == 0 {
		return ""
	}

	return util.ToJSONStringNotNull(couponData)
}

// buildCarpoolDualPriceInfo 构建拼车双价格信息 (对应PHP的_buildCarpoolDualPriceInfo)
func buildCarpoolDualPriceInfo(productFull *biz_runtime.ProductInfoFull) string {
	fee, b := productFull.GetCarpoolFailEstimateFee()
	if b && productFull.IsDualCarpoolPrice() {
		failCouponAmount := 0
		if productFull.GetCarpoolFailCouponInfo() != nil {
			failCouponAmount = cast.ToInt(productFull.GetCarpoolFailCouponInfo().Amount)
		}

		var data = map[string]map[string]interface{}{
			"success": {
				"estimate_fee":  productFull.GetEstimateFee(),
				"coupon_amount": cast.ToInt(productFull.GetCouponAmount()),
			},
			"failed": {
				"estimate_fee":  fee,
				"coupon_amount": failCouponAmount,
			},
		}
		return util.ToJSONStringNotNull(data)
	}

	return ""
}

// 辅助函数
func getCouponAmount(productFull *biz_runtime.ProductInfoFull) float64 {
	if couponInfo := productFull.GetCouponInfo(); couponInfo != nil {
		if amount, err := strconv.ParseFloat(couponInfo.Amount, 64); err == nil && amount > 0 {
			return amount / 100.0 // 转换为元
		}
	}
	return 0
}
