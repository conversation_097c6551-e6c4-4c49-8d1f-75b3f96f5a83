package param

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/instance/param/wrapper"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

// WithRecParams 默认拼表单参数
func WithRecParams(commonBizInfo models.CommonBizInfo) wrapper.AthenaRequestWrapper {
	return func(ctx context.Context, req *AthenaApiv3.AthenaBubbleRecommendReq) error {
		formStyle := int32(commonBizInfo.RecCarpoolFormSytle)
		req.AthenaBubbleReq.CarpoolFormStyle = &formStyle

		preferenceSeatNum := int32(-1)
		if len(commonBizInfo.MultiRequireProduct) > 0 {
			for _, item := range commonBizInfo.MultiRequireProduct {
				if item.CarpoolSeatNum != 0 && item.IsSelected == 1 {
					preferenceSeatNum = item.CarpoolSeatNum
					break
				}
				if item.CarpoolSeatNum == 0 && item.IsSelected == 1 {
					preferenceSeatNum = 0
					break
				}
			}
		}
		if preferenceSeatNum == -1 {
			preferenceSeatNum = 1
		}
		req.AthenaBubbleReq.PreferenceSeatNum = &preferenceSeatNum
		return nil
	}
}
