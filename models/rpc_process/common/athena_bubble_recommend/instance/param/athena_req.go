package param

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/instance/param/wrapper"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
)

const (
	EstimateOrderMatchType       = 0 // 默认(主预估)
	AnycarOrderMatchType         = 1 // 等待应答
	CombinedTravelOrderMatchType = 2 // 组合出行
)

type AthenaReq struct {
	req *AthenaApiv3.AthenaBubbleRecommendReq
}

func NewAthenaReq() *AthenaReq {
	return &AthenaReq{req: &AthenaApiv3.AthenaBubbleRecommendReq{
		AthenaBubbleReq: &AthenaApiv3.AthenaNewFormBubbleReq{},
		Mode:            2, // 1:供需准入 2:获取所有能力
	}}
}

func (a *AthenaReq) SetAthenaReq(ctx context.Context, funcList []wrapper.AthenaRequestWrapper) error {
	for _, opt := range funcList {
		if err := opt(ctx, a.req); err != nil {
			return err
		}
	}
	return nil
}

// GetRequest 获取内部的AthenaBubbleRecommendReq
func (a *AthenaReq) GetRequest() *AthenaApiv3.AthenaBubbleRecommendReq {
	return a.req
}
