package wrapper

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type AthenaRequestWrapper func(ctx context.Context, req *AthenaApiv3.AthenaBubbleRecommendReq) error

// ProductRequestWrapper 请求参数设置函数类型
type ProductRequestWrapper func(ctx context.Context, productFull *biz_runtime.ProductInfoFull, req *AthenaApiv3.ApiAddProduct) error
