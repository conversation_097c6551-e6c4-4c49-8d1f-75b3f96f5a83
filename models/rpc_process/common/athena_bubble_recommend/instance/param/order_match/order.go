package order_match

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	bizCommonConsts "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/tripcloud"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/instance/param/wrapper"
)

// WithOrderInfo 设置订单信息
func WithOrderInfo(sendOrder models.SendOrder) wrapper.AthenaRequestWrapper {
	return func(ctx context.Context, req *AthenaApiv3.AthenaBubbleRecommendReq) error {
		req.AthenaBubbleReq.OrderID = &sendOrder.OrderId
		req.AthenaBubbleReq.OrderCreateTime = &sendOrder.NewTime
		req.AthenaBubbleReq.IsQueue = &sendOrder.IsQueue

		multiRequireProduct := sendOrder.MultiRequiredProduct
		if len(multiRequireProduct) == 0 {
			return nil
		}

		var sendOrderCarList []*AthenaApiv3.ApiAddProduct
		for _, p := range multiRequireProduct {
			pcId := int64(p.ProductCategory)
			levelType := int32(p.LevelType)
			isTC := tripcloud.IsTripcloudProductID(bizCommonConsts.ProductID(p.ProductId))
			bid := int64(p.BusinessId)
			carpoolType := p.CarpoolType
			sendOrderCarList = append(sendOrderCarList, &AthenaApiv3.ApiAddProduct{
				ProductCategory: &pcId,
				ProductID:       int64(p.ProductId),
				RequireLevel:    p.RequiredLevel,
				ComboType:       int64(p.ComboType),
				LevelType:       &levelType,
				IsTripCloud:     &isTC,
				BusinessID:      &bid,
				CarpoolType:     &carpoolType,
			})
		}

		req.AthenaBubbleReq.SendOrderProduct = sendOrderCarList
		return nil
	}
}

func WithBubbleTraceId(commonInfo models.CommonInfo) wrapper.AthenaRequestWrapper {
	return func(ctx context.Context, req *AthenaApiv3.AthenaBubbleRecommendReq) error {
		if req.AthenaBubbleReq.ExtraInfo == nil {
			req.AthenaBubbleReq.ExtraInfo = make(map[string]string, 0)
		}

		req.AthenaBubbleReq.ExtraInfo["bubble_trace_Id"] = commonInfo.MainEstimateTraceId
		return nil
	}
}
