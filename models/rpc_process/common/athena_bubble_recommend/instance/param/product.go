package param

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/instance/param/wrapper"
	"github.com/spf13/cast"
)

type ProductParamBuilder struct {
	orderMatchType      int32
	_productParamSetter []wrapper.ProductRequestWrapper
}

func NewProductParamBuilder(orderMatchType int32, _productParamSetter []wrapper.ProductRequestWrapper) *ProductParamBuilder {
	return &ProductParamBuilder{orderMatchType: orderMatchType, _productParamSetter: _productParamSetter}
}

func (pb *ProductParamBuilder) buildSingleAPIAddProduct(ctx context.Context, productFull *biz_runtime.ProductInfoFull) *AthenaApiv3.ApiAddProduct {

	p := productFull.Product

	// 基础产品信息
	apiProduct := &AthenaApiv3.ApiAddProduct{
		ProductCategory: &p.ProductCategory,
		ProductID:       p.ProductID,
		RequireLevel:    p.RequireLevel,
		ComboType:       p.ComboType,
		LevelType:       &p.LevelType,
		CarpoolType:     &p.CarpoolType,
		IsSpecialPrice:  &p.IsSpecialPrice,
		EstimateID:      &p.EstimateID,
		AirportType:     &p.AirportType,
		SubGroupID:      &p.SubGroupId,
		DepartureTime:   util.Int64Ptr(productFull.GetDepartureTime()),
		SceneType:       util.Int32Ptr(int32(p.SceneType)),
		RouteID:         util.Int64Ptr(cast.ToInt64(p.RouteID)),
		IsTripCloud:     util.BoolPtr(p.IsTripcloudProduct(ctx)),
		GuideActivity:   make([]*AthenaApiv3.GuideActivity, 0),
		//Disabled:       p.Disabled,
		APIGuideInfo: &AthenaApiv3.ApiGuideInfo{
			GuideType: getGuideType(int32(productFull.GetBizInfo().FormShowType)),
			// PriceInfo:          buildPriceInfo(productFull),
			// MockPriceInfo:      buildMockPriceInfo(productFull),
			IsDualCarpoolPrice: isDualCarpoolPrice(productFull),
			CarpoolPriceType:   buildCarpoolPriceType(productFull.Product),
			ExtraInfo:          nil,
		},
		// ExtraInfo: buildProductExtraInfo(productFull),
	}

	for _, productRequestWrapper := range pb._productParamSetter {
		if err := productRequestWrapper(ctx, productFull, apiProduct); err != nil {
			return apiProduct
		}
	}

	return apiProduct
}

func isDualCarpoolPrice(productFull *biz_runtime.ProductInfoFull) *bool {
	bIsDualCarpoolPrice := false
	if productFull.IsDualCarpoolPrice() {
		bIsDualCarpoolPrice = true
	}
	return &bIsDualCarpoolPrice
}

func buildCarpoolPriceType(p *models.Product) *int32 {
	carpoolPriceType := int32(0)
	if p.CarpoolPriceType != 0 {
		carpoolPriceType = p.CarpoolPriceType
	}
	return &carpoolPriceType
}

// getGuideType 获取指南类型 (对应PHP的_getGuideType)
func getGuideType(formShowType int32) int32 {
	switch formShowType {
	case 2:
		return 2
	case 1:
		fallthrough
	default:
		return 1
	}
}
