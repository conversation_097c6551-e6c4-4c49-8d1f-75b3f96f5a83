package param

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/instance/param/wrapper"
	"strconv"
)

// WithCommonInfo 设置基础信息
func WithCommonInfo(commonInfo models.CommonInfo, orderMatchType int32) wrapper.AthenaRequestWrapper {
	return func(ctx context.Context, req *AthenaApiv3.AthenaBubbleRecommendReq) error {
		req.AthenaBubbleReq.PreOrderMatch = &orderMatchType
		req.AthenaBubbleReq.ClientType = int64(commonInfo.ClientType)
		req.AthenaBubbleReq.AppVersion = commonInfo.AppVersion
		req.AthenaBubbleReq.Lang = commonInfo.Lang
		req.AthenaBubbleReq.OrderType = commonInfo.OrderType
		req.AthenaBubbleReq.Channel = &commonInfo.Channel
		req.AthenaBubbleReq.MenuID = &commonInfo.MenuID
		req.AthenaBubbleReq.CallCarType = &commonInfo.CallCarType
		req.AthenaBubbleReq.AccessKeyID = &commonInfo.AccessKeyID
		req.AthenaBubbleReq.PageType = &commonInfo.PageType
		req.AthenaBubbleReq.SourceID = &commonInfo.SourceID
		req.AthenaBubbleReq.ScreenPixels = &commonInfo.ScreenPixels
		req.AthenaBubbleReq.ScreenScale = &commonInfo.ScreenScale
		req.AthenaBubbleReq.EventKey = commonInfo.EventKey
		return nil
	}
}

// WithUserInfo 设置用户信息
func WithUserInfo(passengerInfo models.PassengerInfo) wrapper.AthenaRequestWrapper {
	return func(ctx context.Context, req *AthenaApiv3.AthenaBubbleRecommendReq) error {
		req.AthenaBubbleReq.Phone = passengerInfo.Phone
		req.AthenaBubbleReq.Pid = strconv.FormatInt(passengerInfo.PID, 10)
		req.AthenaBubbleReq.UserType = &passengerInfo.UserType
		return nil
	}
}

// WithLocationInfo 设置地点信息
func WithLocationInfo(areaInfo models.AreaInfo) wrapper.AthenaRequestWrapper {
	return func(ctx context.Context, req *AthenaApiv3.AthenaBubbleRecommendReq) error {
		toArea := int64(areaInfo.ToArea)

		req.AthenaBubbleReq.FromArea = int64(areaInfo.Area)
		req.AthenaBubbleReq.FromLng = areaInfo.FromLng
		req.AthenaBubbleReq.FromLat = areaInfo.FromLat
		req.AthenaBubbleReq.ToLng = areaInfo.ToLng
		req.AthenaBubbleReq.ToLat = areaInfo.ToLat
		req.AthenaBubbleReq.ToArea = &toArea
		req.AthenaBubbleReq.FromName = &areaInfo.FromName
		req.AthenaBubbleReq.ToName = &areaInfo.ToName
		req.AthenaBubbleReq.FromCounty = &areaInfo.FromCounty
		req.AthenaBubbleReq.ToCounty = &areaInfo.ToCounty
		req.AthenaBubbleReq.CurrentLng = &areaInfo.CurLng
		req.AthenaBubbleReq.CurrentLat = &areaInfo.CurLat
		req.AthenaBubbleReq.District = &areaInfo.District
		req.AthenaBubbleReq.DestPoiID = &areaInfo.ToPoiID
		req.AthenaBubbleReq.MapType = areaInfo.MapType

		req.AthenaBubbleReq.FromPoiCode = &areaInfo.FromPoiID
		req.AthenaBubbleReq.DestPoiCode = &areaInfo.ToPoiID
		return nil
	}
}

func WithPassengerPreferenceList(commonBizInfo models.CommonBizInfo, orderMatchType int32) wrapper.AthenaRequestWrapper {
	return func(ctx context.Context, req *AthenaApiv3.AthenaBubbleRecommendReq) error {

		multiRequireProduct := commonBizInfo.MultiRequireProduct
		if len(multiRequireProduct) == 0 {
			return nil
		}

		var list []*AthenaApiv3.PreferenceProductItem
		for _, item := range multiRequireProduct {
			judge := item.IsSelected == 1
			if orderMatchType == AnycarOrderMatchType {
				judge = true
			}
			if judge {
				list = append(list, &AthenaApiv3.PreferenceProductItem{
					ProductCategory: int32(item.ProductCategory),
				})
			}
		}
		req.AthenaBubbleReq.PassengerPreferenceList = list
		return nil
	}
}

// WithProductList 设置品类列表
func WithProductList(products []*biz_runtime.ProductInfoFull, orderMatchType int32, _productParamSetter ...wrapper.ProductRequestWrapper) wrapper.AthenaRequestWrapper {
	return func(ctx context.Context, req *AthenaApiv3.AthenaBubbleRecommendReq) error {
		// 确保ExtraInfo已初始化
		if req.AthenaBubbleReq.ExtraInfo == nil {
			req.AthenaBubbleReq.ExtraInfo = make(map[string]string)
		}

		apiAddProducts := make([]*AthenaApiv3.ApiAddProduct, 0, len(products))
		for _, productFull := range products {
			builder := NewProductParamBuilder(orderMatchType, _productParamSetter)

			apiProduct := builder.buildSingleAPIAddProduct(ctx, productFull)
			apiAddProducts = append(apiAddProducts, apiProduct)
		}
		req.AthenaBubbleReq.APIAddProduct = apiAddProducts

		// 根据订单匹配类型设置全局信息
		//if orderMatchType == EstimateOrderMatchType {
		//	setEstimateOrderExtraInfo(products, req.AthenaBubbleReq.ExtraInfo)
		//}

		return nil
	}
}
