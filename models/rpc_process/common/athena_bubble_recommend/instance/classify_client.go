package instance

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/feature"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/feature/box"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/feature/rec_form"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/feature/single_car"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/instance/param"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/instance/param/form"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/instance/param/wrapper"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/model"
)

// ClassifyClient 7.0表单客户端
type ClassifyClient struct {
	*BaseClient // 继承基础客户端
}

// NewClassifyClient 创建7.0推荐区表单客户端
func NewClassifyClient(baseReq *models.BaseReqData) *ClassifyClient {
	return &ClassifyClient{
		BaseClient: NewBaseClient(0, model.FormTypeRecommend70, baseReq),
	}
}

func WithCategoryInfo(ctx context.Context, baseReq *models.BaseReqData) wrapper.ProductRequestWrapper {
	return func(ctx context.Context, productFull *biz_runtime.ProductInfoFull, req *AthenaApiv3.ApiAddProduct) error {
		confUtil := productFull.GetCommonBizInfo().BaseCategoryConf
		if confUtil != nil {
			req.PossibleCategoryIds = confUtil.GetProductPossibleIds(ctx, productFull.Product, baseReq)
			req.DefaultCategoryID = util.Int32Ptr(confUtil.GetProductCategoryId(productFull.Product))
		}

		return nil
	}
}

func (c *ClassifyClient) BuildRequest(ctx context.Context, ge *models.BaseReqData, products []*biz_runtime.ProductInfoFull) (interface{}, error) {
	athenaReq := param.NewAthenaReq()

	wrappers := c.GetRequestWrapper(ctx, ge, products,
		WithCategoryInfo(ctx, ge),
		form.WithAdditionalServices(),
		form.WithPriceInfo(),
		form.WithProductExtraInfo(),
	)
	wrappers = append(wrappers,
		form.WithExtraInfo(products),
		form.WithEffectiveBenefits(products),
		form.WithExpectScene(),
		form.WithPlatFormCompensateInfo(ge.CommonBizInfo),
	)

	if err := athenaReq.SetAthenaReq(ctx, wrappers); err != nil {
		return athenaReq.GetRequest(), err
	}

	return athenaReq.GetRequest(), nil
}

func (c *ClassifyClient) SetAthenaFeature(ctx context.Context, res interface{}) error {
	rsp := res.(*AthenaApiv3.AthenaGuideRecommendResp)

	recFormFeature := rec_form.NewRecFormFeature().SetFeature(ctx, c.baseReq, rsp.RecTabInfo)

	// 推荐区表单
	if recFormFeature.IsRecForm {
		c.GetFeatureSetter(ctx, rsp,
			feature.WithSingleFeatures(
				single_car.NewEtpProductFeature(),
				single_car.NewSingleCarExtraInfo(),
				single_car.NewGuideProductFeature(),
				single_car.NewSubGroupIdFeature(),
				single_car.NewDiscountChengyiFeature(),

				single_car.NewCategoryIdFeature(ctx, c.baseReq),
			),
			feature.WithBoxFeatures(
				box.NewSubGroupInOutFeature(),
				box.NewSubGroupCategoryIdFeature(ctx, c.baseReq),
			),
		)

		c.FeatureSetter = append(c.FeatureSetter,
			recFormFeature,
		)
	} else {
		c.GetFeatureSetter(ctx, rsp,
			feature.WithSingleFeatures(
				single_car.NewEtpProductFeature(),
				single_car.NewSingleCarExtraInfo(),
				single_car.NewGuideProductFeature(),
				single_car.NewRecPosFeature(),
				single_car.NewRecommendBubbleFeature(),
				single_car.NewSubGroupIdFeature(),
				single_car.NewDiscountChengyiFeature(),

				single_car.NewCategoryIdFeature(ctx, c.baseReq),
				single_car.NewProductFoldFeature(),
			),
			feature.WithBoxFeatures(
				box.NewSubGroupInOutFeature(),
				box.NewSubGroupFoldFeature(),
				box.NewSubGroupCategoryIdFeature(ctx, c.baseReq),
			),
		)
	}
	return nil
}
