package athena_bubble_recommend

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/feature"
)

// FormSpecificClient 表单专用客户端接口
type FormSpecificClient interface {
	// BuildRequest 构建特定表单的请求
	BuildRequest(ctx context.Context, ge *models.BaseReqData, products []*biz_runtime.ProductInfoFull) (interface{}, error)

	// CallAthenaAPI 调用Athena API
	CallAthenaAPI(ctx context.Context, request interface{}) (interface{}, error)

	// SetAthenaFeature 解析响应
	SetAthenaFeature(ctx context.Context, response interface{}) error

	feature.SetRecommendFeature
	biz_runtime.FinalProductsFilterV2
}
