package filter

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type FilterFeature struct {
	disableProductCategory []*AthenaApiv3.DisableProductItem
}

func NewFilterFeature(disableProductCategory []*AthenaApiv3.DisableProductItem) *FilterFeature {
	return &FilterFeature{
		disableProductCategory: disableProductCategory,
	}
}

func (rp *FilterFeature) HandlerFilter(ctx context.Context, productMap map[int64]*biz_runtime.ProductInfoFull) (filter []models.ProductCategory) {

	for _, full := range productMap {
		// 过滤导流品类，Athena未设置需要导流的
		if full.Product.BizInfo.FormShowType == consts.FormShowTypeGuide &&
			!util.InArrayInt32(int32(full.GetProductCategory()), full.BaseReqData.CommonBizInfo.GuidePcIds) {
			filter = append(filter, models.ProductCategory(full.GetProductCategory()))
		}
	}

	if len(rp.disableProductCategory) < 1 {
		return
	}

	for _, item := range rp.disableProductCategory {
		if item.ProductItem == nil {
			continue
		}
		filter = append(filter, models.ProductCategory(item.ProductItem.ProductCategory))
	}

	return filter
}
