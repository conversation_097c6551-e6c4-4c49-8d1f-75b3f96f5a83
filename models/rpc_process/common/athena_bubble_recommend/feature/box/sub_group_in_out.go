package box

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/group_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type SubGroupInOutFeature struct {
	subGroup2RecPos   map[int32]int32
	subGroup2IsOutBox map[int32]bool
	groupId2RecPos    map[string]int32
}

func NewSubGroupInOutFeature() *SubGroupInOutFeature {
	return &SubGroupInOutFeature{
		subGroup2RecPos:   make(map[int32]int32),
		subGroup2IsOutBox: make(map[int32]bool),
		groupId2RecPos:    make(map[string]int32),
	}
}

func (s *SubGroupInOutFeature) SetFeature(ctx context.Context, subGroupRecItem *AthenaApiv3.SubGroupRecInfo) {

	// 盒子位置
	s.subGroup2RecPos[subGroupRecItem.GetSubGroupID()] = subGroupRecItem.GetRecPos()
	// 需要出盒子的品类
	for _, pcID := range subGroupRecItem.SplitCategory {
		s.subGroup2IsOutBox[pcID] = true
	}
	s.groupId2RecPos[group_id.BuildGroupId(group_id.AggregationBoxType, int64(subGroupRecItem.GetSubGroupID()))] = subGroupRecItem.GetRecPos()

	return
}

func (s *SubGroupInOutFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.SubGroup2RecPos = s.subGroup2RecPos
	info.SubGroup2IsOutBox = s.subGroup2IsOutBox
	info.GroupId2RecPos = util.MergeAnyMap(info.GroupId2RecPos, s.groupId2RecPos)
}

func (s *SubGroupInOutFeature) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	// athena出盒子
	if len(product.BaseReqData.CommonBizInfo.SubGroup2IsOutBox) > 0 {
		if v, ok := product.BaseReqData.CommonBizInfo.SubGroup2IsOutBox[int32(product.GetProductCategory())]; ok && v {
			product.Product.SubGroupId = 0
		}
	}

	// athena 移动盒子
	if len(product.BaseReqData.CommonBizInfo.PcID2NewSubGroupId) > 0 {
		if newSubGroupId, ok := product.BaseReqData.CommonBizInfo.PcID2NewSubGroupId[int32(product.GetProductCategory())]; ok {
			product.Product.SubGroupId = newSubGroupId
		}
	}
}
