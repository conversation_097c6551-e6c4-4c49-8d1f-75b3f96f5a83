package box

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/group_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type SubGroupCategoryIdFeature struct {
	categoryIdPosition map[string]int32
}

func NewSubGroupCategoryIdFeature(ctx context.Context, baseReq *models.BaseReqData) *SubGroupCategoryIdFeature {
	return &SubGroupCategoryIdFeature{
		categoryIdPosition: make(map[string]int32),
	}
}

func (s *SubGroupCategoryIdFeature) SetFeature(ctx context.Context, subGroupRecItem *AthenaApiv3.SubGroupRecInfo) {
	if subGroupRecItem.CategoryID != nil && *subGroupRecItem.CategoryID != 0 {
		s.categoryIdPosition[group_id.BuildGroupId(group_id.AggregationBoxType, int64(subGroupRecItem.SubGroupID))] = subGroupRecItem.GetCategoryID()
	}
}

func (s *SubGroupCategoryIdFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.GroupId2CategoryId = util.MergeAnyMap(info.GroupId2CategoryId, s.categoryIdPosition)
}

func (s *SubGroupCategoryIdFeature) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {

}
