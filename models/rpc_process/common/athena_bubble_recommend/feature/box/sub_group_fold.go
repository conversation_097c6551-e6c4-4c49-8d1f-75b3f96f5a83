package box

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/group_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type SubGroupFoldFeature struct {
	subGroupFoldType map[string]int32
}

func NewSubGroupFoldFeature() *SubGroupFoldFeature {
	return &SubGroupFoldFeature{
		subGroupFoldType: make(map[string]int32),
	}
}

func (s *SubGroupFoldFeature) SetFeature(ctx context.Context, subGroupRecItem *AthenaApiv3.SubGroupRecInfo) {
	if subGroupRecItem.FoldType != nil {
		id := group_id.BuildGroupId(group_id.AggregationBoxType, int64(subGroupRecItem.SubGroupID))
		s.subGroupFoldType[id] = subGroupRecItem.GetFoldType()
	}
}

func (s *SubGroupFoldFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.GroupId2FoldType = util.MergeAnyMap(info.GroupId2FoldType, s.subGroupFoldType)
}

func (s *SubGroupFoldFeature) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {

}
