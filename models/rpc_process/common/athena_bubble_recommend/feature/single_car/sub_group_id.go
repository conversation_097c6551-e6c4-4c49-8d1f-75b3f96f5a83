package single_car

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type SubGroupIdFeature struct {
	pcID2NewSubGroupId map[int32]int32
}

func NewSubGroupIdFeature() *SubGroupIdFeature {
	return &SubGroupIdFeature{
		pcID2NewSubGroupId: make(map[int32]int32),
	}
}

func (s *SubGroupIdFeature) SetFeature(ctx context.Context, recItem *AthenaApiv3.RecItem) {

	if recItem.GetSubGroupID() == consts.SubGroupIdDiscountAlliance {
		s.pcID2NewSubGroupId[recItem.GetProductCategory()] = recItem.GetSubGroupID()
	}
}

func (s *SubGroupIdFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.PcID2NewSubGroupId = s.pcID2NewSubGroupId
}

func (s *SubGroupIdFeature) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	
}
