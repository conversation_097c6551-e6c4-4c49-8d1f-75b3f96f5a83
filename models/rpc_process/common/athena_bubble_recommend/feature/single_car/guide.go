package single_car

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

const (
	REC_POS_TOP_AREA       = 1 // 顶部区域
	REC_POS_RECOMMEND_AREA = 2 // 底部区域
	REC_POS_BOTTOM_AREA    = 4
)

// GuideProductFeature 导流产品特征
type GuideProductFeature struct {
	guidePcIds []int32
}

func NewGuideProductFeature() *GuideProductFeature {
	return &GuideProductFeature{
		guidePcIds: make([]int32, 0),
	}
}

func (g *GuideProductFeature) SetFeature(ctx context.Context, recItem *AthenaApiv3.RecItem) {
	// 设置导流位车型 (对应PHP导流位逻辑)
	if (recItem.RecPos == REC_POS_TOP_AREA || recItem.RecPos == REC_POS_BOTTOM_AREA) && recItem.ProductCategory != 0 {
		g.guidePcIds = append(g.guidePcIds, recItem.ProductCategory)
	}
}

func (g *GuideProductFeature) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {

}

func (g *GuideProductFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.GuidePcIds = g.guidePcIds
}
