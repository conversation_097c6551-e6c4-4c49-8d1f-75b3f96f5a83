package single_car

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

// EtpProductFeature ETP产品特征
type EtpProductFeature struct {
	eTInfoMap map[int32]*AthenaApiv3.EstimatedTimeInfo
}

// NewEtpProductFeature 创建新的ETP产品特征实例
func NewEtpProductFeature() *EtpProductFeature {
	return &EtpProductFeature{
		eTInfoMap: make(map[int32]*AthenaApiv3.EstimatedTimeInfo),
	}
}

func (e *EtpProductFeature) SetFeature(ctx context.Context, recItem *AthenaApiv3.RecItem) {
	if recItem.EstimatedTimeInfo != nil {
		e.eTInfoMap[recItem.GetProductCategory()] = recItem.EstimatedTimeInfo
	}
}

func (e *EtpProductFeature) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {

}

func (e *EtpProductFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.ETInfoMap = e.eTInfoMap
}
