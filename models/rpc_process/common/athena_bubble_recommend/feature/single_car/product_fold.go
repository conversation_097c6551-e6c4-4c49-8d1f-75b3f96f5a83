package single_car

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/group_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type ProductFoldFeature struct {
	productFoldType map[string]int32
}

func NewProductFoldFeature() *ProductFoldFeature {
	return &ProductFoldFeature{
		productFoldType: make(map[string]int32),
	}
}

func (p *ProductFoldFeature) SetFeature(ctx context.Context, recItem *AthenaApiv3.RecItem) {
	if recItem.FoldType != nil {
		id := group_id.BuildGroupId(group_id.SINGLETYPE, int64(recItem.ProductCategory))
		p.productFoldType[id] = recItem.GetFoldType()
	}
}

func (p *ProductFoldFeature) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {

}

func (p *ProductFoldFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.GroupId2FoldType = util.MergeAnyMap(info.GroupId2FoldType, p.productFoldType)
}
