package single_car

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/redis"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"time"
)

type DiscountChengyiFeature struct {
	chengYiDanDiscount map[int32]bool
}

func NewDiscountChengyiFeature() *DiscountChengyiFeature {
	return &DiscountChengyiFeature{
		chengYiDanDiscount: make(map[int32]bool),
	}
}

func (d *DiscountChengyiFeature) SetFeature(ctx context.Context, recItem *AthenaApiv3.RecItem) {
	if recItem.DiscountForm != nil && *recItem.DiscountForm == 1 {
		d.chengYiDanDiscount[recItem.ProductCategory] = true
	}
}

func (d *DiscountChengyiFeature) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {

	if len(d.chengYiDanDiscount) > 0 && d.chengYiDanDiscount[int32(product.GetProductCategory())] {
		go setChengYiDanCache(ctx, product)
	}
}

func (d *DiscountChengyiFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {

}

func setChengYiDanCache(ctx context.Context, p biz_runtime.ProductInfoFull) {
	if _, err := redis.GetClient().SetEx(ctx,
		"P_GUIDE_DISCOUNT_FORM"+p.GetEstimateID(), 600*time.Second, "1"); err != nil {

		log.Trace.Warnf(ctx, "setChengYiDanCache", "set redis failed %s", err)
	}
}
