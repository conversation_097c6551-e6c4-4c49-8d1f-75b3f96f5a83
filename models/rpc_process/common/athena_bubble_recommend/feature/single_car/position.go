package single_car

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/group_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type RecPosFeature struct {
	recPosMap     map[int32]int32
	groupIdRecPos map[string]int32
}

// NewRecPosFeature 创建新的位置产品特征实例
func NewRecPosFeature() *RecPosFeature {
	return &RecPosFeature{
		recPosMap:     make(map[int32]int32),
		groupIdRecPos: make(map[string]int32),
	}
}

func (p *RecPosFeature) SetFeature(ctx context.Context, recItem *AthenaApiv3.RecItem) {
	p.recPosMap[recItem.ProductCategory] = recItem.RecPos

	p.groupIdRecPos[group_id.BuildGroupId(group_id.SINGLETYPE, int64(recItem.ProductCategory))] = recItem.RecPos
}

func (p *RecPosFeature) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {

}

func (p *RecPosFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.RecPosMap = p.recPosMap
	info.GroupId2RecPos = util.MergeAnyMap(info.GroupId2RecPos, p.groupIdRecPos)
}
