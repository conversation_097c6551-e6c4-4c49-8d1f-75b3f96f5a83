package single_car

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type RecommendBubbleFeature struct {
	recommendInfoMap map[int32]*AthenaApiv3.RecommendInfo // 推荐信息 ShowType1:标签推荐  2:气泡推荐语
}

func NewRecommendBubbleFeature() *RecommendBubbleFeature {
	return &RecommendBubbleFeature{
		recommendInfoMap: make(map[int32]*AthenaApiv3.RecommendInfo),
	}
}

func (r *RecommendBubbleFeature) SetFeature(ctx context.Context, recItem *AthenaApiv3.RecItem) {
	r.recommendInfoMap[recItem.GetProductCategory()] = recItem.Info
}

func (r *RecommendBubbleFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.RecommendInfoMap = r.recommendInfoMap
}

func (r *RecommendBubbleFeature) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	////TODO implement me
	//panic("implement me")
}
