package single_car

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/group_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type CategoryIdFeature struct {
	categoryIdPosition map[string]int32
}

func NewCategoryIdFeature(ctx context.Context, baseReq *models.BaseReqData) *CategoryIdFeature {
	return &CategoryIdFeature{
		categoryIdPosition: make(map[string]int32),
	}
}

func (c *CategoryIdFeature) SetFeature(ctx context.Context, recItem *AthenaApiv3.RecItem) {
	if recItem.CategoryID != nil && *recItem.CategoryID != 0 {
		groupId := group_id.BuildGroupIdById(group_id.SINGLETYPE, int64(recItem.ProductCategory))
		c.categoryIdPosition[groupId] = recItem.GetCategoryID()
	}
}

func (c *CategoryIdFeature) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
}

func (c *CategoryIdFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.GroupId2CategoryId = util.MergeAnyMap(info.GroupId2CategoryId, c.categoryIdPosition)
}
