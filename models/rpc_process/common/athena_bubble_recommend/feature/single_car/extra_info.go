package single_car

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type SingleCarExtraInfo struct {
	recommendExtraInfoMap map[int32]map[string]string // 推荐信息 ShowType1:标签推荐  2:气泡推荐语
}

func NewSingleCarExtraInfo() *SingleCarExtraInfo {
	return &SingleCarExtraInfo{
		recommendExtraInfoMap: make(map[int32]map[string]string),
	}
}

func (s SingleCarExtraInfo) SetFeature(ctx context.Context, recItem *AthenaApiv3.RecItem) {
	s.recommendExtraInfoMap[recItem.GetProductCategory()] = recItem.ExtraInfo
}

func (s SingleCarExtraInfo) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {

}

func (s SingleCarExtraInfo) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.RecommendExtraInfoMap = s.recommendExtraInfoMap
}
