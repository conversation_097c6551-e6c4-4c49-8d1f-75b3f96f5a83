package feature

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/feature/single_car"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
)

// SingleCarFeature 单车特征接口
type SingleCarFeature interface {
	SetFeature(ctx context.Context, recItem *AthenaApiv3.RecItem)
	SetRecommendFeature
}

type SetRecommendFeature interface {
	BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo)
	BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo)
}

// SingleCarFeatures 单车特征聚合器
type SingleCarFeatures struct {
	_singleCarFeature []SingleCarFeature
}

// NewSingleCarFeatures 创建单车特征聚合器
func NewSingleCarFeatures(s ...SingleCarFeature) *SingleCarFeatures {
	if len(s) > 0 {
		return &SingleCarFeatures{
			_singleCarFeature: s,
		}
	}

	return &SingleCarFeatures{
		_singleCarFeature: []SingleCarFeature{
			single_car.NewGuideProductFeature(),
			single_car.NewRecPosFeature(),
			single_car.NewEtpProductFeature(),
			single_car.NewSubGroupIdFeature(),
		},
	}
}

// SetSingleCarFeature 设置单车特征
func (s *SingleCarFeatures) SetSingleCarFeature(ctx context.Context, recItems []*AthenaApiv3.RecItem) *SingleCarFeatures {
	if len(recItems) == 0 {
		return s
	}

	// 遍历推荐结果，统一处理数据并分发给各个特征模块
	for _, item := range recItems {
		for _, feature := range s._singleCarFeature {
			feature.SetFeature(ctx, item)
		}
	}
	return s
}

// BuildCommonBizInfo 构建通用业务信息
func (s *SingleCarFeatures) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	if info == nil {
		return
	}

	// 批量调用所有特征的BuildCommonBizInfo方法
	for _, feature := range s._singleCarFeature {
		feature.BuildCommonBizInfo(ctx, info)
	}
}

// BuildProductFeature 构建产品特征信息
func (s *SingleCarFeatures) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	if info == nil {
		return
	}

	// 批量调用所有特征的BuildProductFeature方法
	for _, feature := range s._singleCarFeature {
		feature.BuildProductFeature(ctx, product, info)
	}
}
