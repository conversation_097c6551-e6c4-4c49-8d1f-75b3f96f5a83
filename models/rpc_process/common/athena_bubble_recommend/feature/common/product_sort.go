package common

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type ProductSortFeature struct {
	ProductSortInfo *AthenaApiv3.ProductSortInfo `json:"product_sort"`
}

func NewProductSortFeature() *ProductSortFeature {
	return &ProductSortFeature{}
}

func (p *ProductSortFeature) SetFeature(ctx context.Context, sort *AthenaApiv3.ProductSortInfo) {
	p.ProductSortInfo = sort
}

func (p *ProductSortFeature) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {

}

func (p *ProductSortFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.ProductSortInfo = p.ProductSortInfo
}
