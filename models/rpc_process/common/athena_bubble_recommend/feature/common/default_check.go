package common

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type DefaultCheck struct {
	checkedPcIdMap map[int64]int8
}

func NewDefaultCheck() *DefaultCheck {
	return &DefaultCheck{
		checkedPcIdMap: make(map[int64]int8),
	}
}

func (d *DefaultCheck) SetFeature(ctx context.Context, CheckedProductCategory []*AthenaApiv3.ProductCategoryItem) *DefaultCheck {
	if len(CheckedProductCategory) > 0 {
		for _, item := range CheckedProductCategory {
			d.checkedPcIdMap[int64(item.GetProductCategory())] = consts.Checked
		}
	}
	return d
}

func (d *DefaultCheck) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	productCategory := product.GetProductCategory()
	if d.checkedPcIdMap[productCategory] == consts.Checked {
		info.CheckStatus = consts.Checked
	}
}

func (d *DefaultCheck) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {

}
