package common

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type ExpectInfo struct {
	estimateStripInfo *AthenaApiv3.EstimateStripInfo
	expectInfo        *AthenaApiv3.AthenaBubbleExpectInfoResp
}

func NewExpectInfo() *ExpectInfo {
	return &ExpectInfo{}
}

func (e *ExpectInfo) SetFeature(ctx context.Context, rp *AthenaApiv3.AthenaGuideRecommendResp) *ExpectInfo {
	e.estimateStripInfo = rp.GetEstimateStripInfo()
	e.expectInfo = rp.GetExpectInfo()
	return e
}

func (e *ExpectInfo) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
}

func (e *ExpectInfo) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.EstimateStripInfo = e.estimateStripInfo
	info.ExpectInfo = e.expectInfo
}
