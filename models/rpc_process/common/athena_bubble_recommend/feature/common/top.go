package common

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/feature"
)

// TopRecommendFeature 置顶功能
type TopRecommendFeature struct {
	topRec            *AthenaApiv3.TopRecRes
	topPcIdList       []int64 // 置顶车型列表
	topSubGroupIdList []int64 // 置顶聚合车型列表
}

// NewTopRecommendFeature 创建置顶特征实例
func NewTopRecommendFeature() *TopRecommendFeature {
	return &TopRecommendFeature{
		topPcIdList:       make([]int64, 0),
		topSubGroupIdList: make([]int64, 0),
	}
}

// SetTopRecommendFeature 设置置顶特征（原有方法）
func (t *TopRecommendFeature) SetTopRecommendFeature(res *AthenaApiv3.TopRecRes) *TopRecommendFeature {
	if res == nil {
		return t
	}
	t.topRec = res

	for _, item := range res.TopRecList {
		if feature.SHORT_DISTANCE_TYPE == item.Type {
			t.topSubGroupIdList = append(t.topSubGroupIdList, int64(item.Category))
		}

		if feature.SINGLE_TYPE == item.Type {
			t.topPcIdList = append(t.topPcIdList, int64(item.Category))
		}
	}
	return t
}

// BuildProductFeature 实现SingleCarFeature接口
func (t *TopRecommendFeature) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	// 根据产品信息构建置顶特征
	// 这里可以根据产品的属性来决定是否需要置顶
	if info != nil {

	}
}

func (t *TopRecommendFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.TopData = new(models.TopData)
	info.TopData.TopPcIdList = t.topPcIdList
	info.TopData.TopSubGroupIdList = t.topSubGroupIdList

	info.TopRec = t.topRec
}
