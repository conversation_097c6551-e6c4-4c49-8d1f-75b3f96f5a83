package common

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type SupplyInfoFeature struct {
	isImbalanced int32
	supplyInfo   *AthenaApiv3.SupplyInfo
}

func NewSupplyInfoFeature() *SupplyInfoFeature {
	return &SupplyInfoFeature{}
}

func (s *SupplyInfoFeature) SetFeature(ctx context.Context, rp *AthenaApiv3.AthenaGuideRecommendResp) *SupplyInfoFeature {

	// 供需失衡场景
	if rp.SupplyInfo != nil && rp.SupplyInfo.GetIsBubbleShortSupply() != 0 && rp.ExtraInfo != nil {
		extraInfo := rp.GetExtraInfo()
		if groupName, ok := extraInfo["bubble_supply_exp_group"]; ok {
			if groupName != "" && groupName != "empty_group" {
				s.isImbalanced = 1
			}
		}

	}

	if rp.SupplyInfo != nil {
		s.supplyInfo = rp.SupplyInfo
	}
	return s
}

func (s *SupplyInfoFeature) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	// athena返回的呼叫人数
	if s.supplyInfo != nil && s.supplyInfo.ProductSupplyDataMap != nil &&
		s.supplyInfo.ProductSupplyDataMap[int32(product.GetProductCategory())] != nil {
		product.Product.BizInfo.OrderReceiveCnt = s.supplyInfo.ProductSupplyDataMap[int32(product.GetProductCategory())].GetOrderReceiveCnt()
	}
}

func (s *SupplyInfoFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.IsImbalanced = s.isImbalanced
	if s.supplyInfo != nil {
		info.SupplySceneInfo = s.supplyInfo.SupplySceneInfo
	}
}
