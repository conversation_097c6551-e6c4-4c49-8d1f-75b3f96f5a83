package common

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"
)

type ExtraInfo struct {
	showCarNum           int
	hitLuxPremiumProtect int
}

func NewExtraInfo() *ExtraInfo {
	return &ExtraInfo{}
}

func (e *ExtraInfo) SetFeature(ctx context.Context, rp *AthenaApiv3.AthenaGuideRecommendResp) *ExtraInfo {

	if len(rp.GetExtraInfo()) > 0 {
		m := rp.GetExtraInfo()

		if num, ok := m["show_car_num"]; ok {
			e.showCarNum = cast.ToInt(num)
		} else {
			e.showCarNum = 5
		}

		if _, ok := m["hit_luxr_premium_protect"]; ok {
			e.hitLuxPremiumProtect = 1
		} else {
			e.hitLuxPremiumProtect = 0
		}
	}
	return e
}

func (e *ExtraInfo) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {

}

func (e *ExtraInfo) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.ShowCarNum = e.showCarNum
	info.HitLuxPremiumProtect = e.hitLuxPremiumProtect
}
