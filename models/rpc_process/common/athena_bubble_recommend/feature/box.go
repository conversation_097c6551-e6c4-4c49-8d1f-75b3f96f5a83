package feature

import (
	"context"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/feature/box"
)

type BoxFeature interface {
	SetFeature(ctx context.Context, recItem *AthenaApiv3.SubGroupRecInfo)
	SetRecommendFeature
}

// BoxFeatures 盒子特征聚合器
type BoxFeatures struct {
	_boxFeature []BoxFeature
}

// NewBoxFeatures 创建单车特征聚合器
func NewBoxFeatures(s ...BoxFeature) *BoxFeatures {
	if len(s) > 0 {
		return &BoxFeatures{_boxFeature: s}
	}

	return &BoxFeatures{
		_boxFeature: []BoxFeature{
			box.NewSubGroupInOutFeature(),
		},
	}
}

// SetBoxFeature 设置盒子特征
func (s *BoxFeatures) SetBoxFeature(ctx context.Context, recItems []*AthenaApiv3.SubGroupRecInfo) *BoxFeatures {
	if len(recItems) == 0 {
		return s
	}

	// 遍历推荐结果，统一处理数据并分发给各个特征模块
	for _, item := range recItems {
		for _, feature := range s._boxFeature {
			feature.SetFeature(ctx, item)
		}
	}
	return s
}

// BuildCommonBizInfo 构建通用业务信息
func (s *BoxFeatures) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	if info == nil {
		return
	}

	// 批量调用所有特征的BuildCommonBizInfo方法
	for _, feature := range s._boxFeature {
		feature.BuildCommonBizInfo(ctx, info)
	}
}

// BuildProductFeature 构建产品特征信息
func (s *BoxFeatures) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	if info == nil {
		return
	}

	// 批量调用所有特征的BuildProductFeature方法
	for _, feature := range s._boxFeature {
		feature.BuildProductFeature(ctx, product, info)
	}
}
