package rec_form

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/group_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"

	"git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v4_multi/experiment"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/model"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

const (
	REC_POS_OPERATION_POS = 3 // 推广位
)

// TagInfo 标签信息结构体
type TagInfo struct {
	SubTagContent   string `json:"sub_tag_content"`
	RightTagContent string `json:"right_tag_content"`
}

type RecFormFeature struct {
	IsRecForm      bool
	RecPcIds       []int32
	RecSubGroupIds []int32

	*model.RecFormFeatureModel
}

func NewRecFormFeature() *RecFormFeature {
	return &RecFormFeature{
		IsRecForm:      false,
		RecPcIds:       make([]int32, 0),
		RecSubGroupIds: make([]int32, 0),
		RecFormFeatureModel: &model.RecFormFeatureModel{
			RecGroup2FoldType:      nil,
			RecGroup2tags:          nil,
			RecGroup2SubType:       nil,
			RecTabProductSortInfo:  nil,
			RecTabFoldSubGroupDown: nil,
		},
	}
}

func (r *RecFormFeature) SetFeature(ctx context.Context, baseReq *models.BaseReqData, recTabResult *AthenaApiv3.RecTabInfo) *RecFormFeature {
	if recTabResult == nil {
		return r
	}

	if (recTabResult.RecResult_ == nil || len(recTabResult.RecResult_) == 0) &&
		(recTabResult.SubGroupRecList == nil || len(recTabResult.SubGroupRecList) == 0) {
		return r
	}

	// 推荐区表单实验
	recABParam := experiment.GetClassifyRecFormABParam(ctx, baseReq)
	if !recABParam.IsHitClassifyRecTab() {
		log.Trace.Warnf(ctx, "classify_rec_form_ab", "form ab diff with athena")
		return r
	}

	r.IsRecForm = true

	// 设置单车型结果
	for _, recItem := range recTabResult.RecResult_ {
		r.RecPcIds = append(r.RecPcIds, recItem.ProductCategory)

		if recItem.FoldType != nil {
			r.RecGroup2FoldType[group_id.BuildGroupIdById(group_id.SINGLETYPE, int64(recItem.ProductCategory))] = recItem.GetFoldType()
		}

		// 处理推广位标签
		if recItem.RecPos == REC_POS_OPERATION_POS && recItem.ExtraInfo != nil {
			tagInfo := &model.TagInfo{}
			if operatingLabel, exists := recItem.ExtraInfo["operating_label"]; exists {
				tagInfo.SubTagContent = operatingLabel
			}
			if operatingTitle, exists := recItem.ExtraInfo["operating_title"]; exists {
				tagInfo.RightTagContent = operatingTitle
			}
			r.RecGroup2tags[group_id.BuildGroupIdById(group_id.SINGLETYPE, int64(recItem.ProductCategory))] = tagInfo
		}

		// 设置推荐子类型
		if recItem.RecTabType != nil {
			r.RecGroup2SubType[group_id.BuildGroupIdById(group_id.SINGLETYPE, int64(recItem.ProductCategory))] = recItem.GetRecTabType()
		}
	}

	// 设置盒子结果
	for _, subGroup := range recTabResult.SubGroupRecList {
		r.RecSubGroupIds = append(r.RecSubGroupIds, subGroup.SubGroupID)

		if subGroup.FoldType != nil {
			r.RecGroup2FoldType[group_id.BuildGroupIdById(group_id.AggregationBoxType, int64(subGroup.SubGroupID))] = subGroup.GetFoldType()
		}

		// 处理推广位标签
		if subGroup.RecPos == REC_POS_OPERATION_POS && subGroup.ExtraInfo != nil {
			tagInfo := &model.TagInfo{}
			if operatingLabel, exists := subGroup.ExtraInfo["operating_label"]; exists {
				tagInfo.SubTagContent = operatingLabel
			}
			if operatingTitle, exists := subGroup.ExtraInfo["operating_title"]; exists {
				tagInfo.RightTagContent = operatingTitle
			}

			r.RecGroup2tags[group_id.BuildGroupIdById(group_id.AggregationBoxType, int64(subGroup.SubGroupID))] = tagInfo
		}

		// 设置推荐子类型
		if subGroup.RecTabType != nil {
			r.RecGroup2SubType[group_id.BuildGroupIdById(group_id.AggregationBoxType, int64(subGroup.SubGroupID))] = subGroup.GetRecTabType()
		}
	}
	if recTabResult.RecTabSortInfo != nil {
		r.RecTabProductSortInfo = recTabResult.RecTabSortInfo.ProductSortInfo
		r.RecTabFoldSubGroupDown = recTabResult.RecTabSortInfo.FoldSubGroupDown
	}

	return r
}

func (r *RecFormFeature) BuildProductFeature(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	if product.Product.SubGroupId != 0 && util.InArrayInt32(product.Product.SubGroupId, r.RecSubGroupIds) {
		info.IsRec = true
	}

	if product.Product.SubGroupId == 0 && util.InArrayInt32(int32(product.Product.ProductCategory), r.RecPcIds) {
		info.IsRec = true
	}

}

func (r *RecFormFeature) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.RecFormFeatureModel = r.RecFormFeatureModel
	if r.IsRecForm {
		info.RecForm = 1
	}
}
