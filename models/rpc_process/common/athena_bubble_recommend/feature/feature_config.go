package feature

type FeatureOption func(*FeatureConfig)

type FeatureConfig struct {
	SingleFeatureSetter []SingleCarFeature
	BoxFeatureSetter    []BoxFeature
}

func WithSingleFeatures(features ...SingleCarFeature) FeatureOption {
	return func(config *FeatureConfig) {
		config.SingleFeatureSetter = features
	}
}

func WithBoxFeatures(features ...BoxFeature) FeatureOption {
	return func(config *FeatureConfig) {
		config.BoxFeatureSetter = features
	}
}
