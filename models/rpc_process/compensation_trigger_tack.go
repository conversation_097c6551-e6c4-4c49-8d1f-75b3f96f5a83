package rpc_process

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/compensation"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"

	Compensation "git.xiaojukeji.com/dirpc/dirpc-go-http-Compensation"
)

const NormalNoAnswerCompensation = "normal_no_answer_compensation"

type CompensationTriggerTack struct {
	compensationReq *Compensation.TriggerTackReq
	resp            *Compensation.TriggerTackResult
}

func NewCompensationTriggerTack(ctx context.Context, commonInfo *models.CommonInfo, areaInfo *models.AreaInfo, passengerInfo *models.PassengerInfo) *CompensationTriggerTack {
	req := &Compensation.TriggerTackReq{
		CompensationBusinesses: []string{NormalNoAnswerCompensation},
		Area:                   areaInfo.Area,
		ToArea:                 areaInfo.ToArea,
		PassengerId:            cast.ToString(passengerInfo.PID),
		FromLat:                cast.ToString(areaInfo.FromLat),
		FromLng:                cast.ToString(areaInfo.FromLng),
		ToLat:                  cast.ToString(areaInfo.ToLat),
		ToLng:                  cast.ToString(areaInfo.ToLng),
		Lang:                   commonInfo.Lang,
		CallCarType:            commonInfo.CallCarType,
		AppVersion:             commonInfo.AppVersion,
		AccessKeyId:            commonInfo.AccessKeyID,
		From:                   "mamba",
		ClientType:             commonInfo.ClientType,
		Phone:                  passengerInfo.Phone,
		FromName:               areaInfo.FromName,
		ToName:                 areaInfo.ToName,
		OrderType:              commonInfo.OrderType,
		BubbleTraceId:          util.GetTraceIDFromCtxWithoutCheck(ctx),
	}

	return &CompensationTriggerTack{req, nil}
}

// 获取rpc错误信息
func (rp *CompensationTriggerTack) GetErrorInfo(ctx context.Context) error {
	return nil
}

func (rp *CompensationTriggerTack) Fetch(ctx context.Context, productInfos []*biz_runtime.ProductInfoFull) bool {
	// 大字版 or 新竹
	baseReqData := productInfos[0].BaseReqData
	if baseReqData == nil || baseReqData.CommonInfo.FontScaleType != 0 || baseReqData.CommonInfo.PageType == page_type.PageTypeXinZhu {
		return false
	}

	var estimateItemList []*Compensation.EstimateItem
	for _, p := range productInfos {
		isSpecialPrice := p.GetIsSpecialPrice()
		requireLevel := cast.ToInt32(p.GetRequireLevel())
		comboType := cast.ToInt32(p.GetComboType())

		estimateItemList = append(estimateItemList, &Compensation.EstimateItem{
			ProductCategory: p.Product.ProductCategory,
			DynamicTotalFee: p.GetDynamicTotalFee(),
			EstimateFee:     p.GetEstimateFee(),
			RequireLevel:    &requireLevel,
			ComboType:       &comboType,
			BusinessId:      &p.Product.BusinessID,
			LevelType:       &p.Product.LevelType,
			CarpoolType:     &p.Product.CarpoolType,
			IsSpecialPrice:  &isSpecialPrice,
			ProductId:       &p.Product.ProductID,
			EstimateId:      p.GetEstimateID(),
		})
	}

	rp.compensationReq.EstimateProducts = estimateItemList
	resp := compensation.TriggerTack(ctx, rp.compensationReq)
	if resp == nil {
		return false
	}

	if resp.Errno != 0 {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "get compensation trigger tack error with errno %d", resp.Errno)
		return false
	}

	rp.resp = resp.Data
	return true
}

func (rp *CompensationTriggerTack) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	if rp.resp == nil || rp.resp.CompensationInfo == nil || rp.resp.CompensationInfo[NormalNoAnswerCompensation] == nil {
		info.BubbleCompensationStatus = 0
		return
	}

	info.BubbleCompensationStatus = 1
	return
}

func (rp *CompensationTriggerTack) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	return
}
