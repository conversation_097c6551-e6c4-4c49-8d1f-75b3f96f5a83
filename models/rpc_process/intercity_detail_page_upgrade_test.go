package rpc_process

import (
	"context"
	"testing"

	dirpcSdkBrick "git.xiaojukeji.com/dirpc/dirpc-go-http-Brick"
	ticketPrice "git.xiaojukeji.com/dirpc/dirpc-go-http-TicketPrice"
	DetailConst "git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

func createProduct() *biz_runtime.ProductInfoFull {
	//sic.product.BaseReqData.CommonBizInfo.IdentityPageInfo.RuleInfo = nil
	return &biz_runtime.ProductInfoFull{
		Product: &models.Product{
			ShiftID: "123",
			BizInfo: &models.PrivateBizInfo{
				IntercityData: models.IntercityData{
					IntercityRule: &ticketPrice.RuleData{
						RebookChargeRule: &ticketPrice.RebookChargeRuleData{
							RebookRuleId: util.StringPtr("default7"),
							RuleName:     util.StringPtr("可改签改签后不可退"),
							RuleDetail: &ticketPrice.RebookChargeRuleDetail{
								MaxRebookTimes: 1,
								ChargeRate: []*ticketPrice.RebookChargeRate{
									{Times: 1,
										Rate: 100},
								},
							},
						},
						RefundChargeRule: &ticketPrice.RefundChargeRuleData{
							BeforeDepartureRules: []*ticketPrice.RefundChargeRule{
								{
									LeftTimeInterval:  24,
									RightTimeInterval: 48,
									RefundChargeRate:  50,
								},
							},
							TimeUnit: util.String2PtrString("hour"),
						},
					},
				}}},
		BaseReqData: &models.BaseReqData{
			CommonBizInfo: models.CommonBizInfo{
				IsBestShift: 1,
				IdentityPageInfo: &dirpcSdkBrick.IdentityPageInfoData{
					IdentityHistoryInfo: []*dirpcSdkBrick.IdentityItem{
						{
							Id:           1,
							Name:         "test",
							IdentityType: 1,
							IdentityNo:   "123456789012345678",
							Type:         1,
						},
					},
					RuleInfo: &dirpcSdkBrick.RuleData{},
				},
				RuleData: &dirpcSdkBrick.RuleData{},
			},
		},
	}
}

// TestBuildNewSubTitle
func TestBuildNewSubTitle(t *testing.T) {
	ctx := context.Background()
	product := createProduct()
	in := NewIntercityDetailPageUpgrade(product.BaseReqData)

	t.Run("测试 01- 获取到", func(t *testing.T) {
		err := in.GetErrorInfo(ctx)
		assert.Nil(t, err)
		in.BuildProductBizInfo(ctx, models.Product{}, &models.PrivateBizInfo{})
		productInfo := models.Product{
			CarpoolType: 8,
		}
		products := []*models.Product{
			&productInfo,
		}
		result := in.Fetch(ctx, products)
		assert.Equal(t, result, true)
	})

	t.Run("测试 02- 获取不到", func(t *testing.T) {
		err := in.GetErrorInfo(ctx)
		assert.Nil(t, err)
		in.BuildProductBizInfo(ctx, models.Product{}, &models.PrivateBizInfo{})
		products := make([]*models.Product, 1)
		result := in.Fetch(ctx, products)
		assert.Equal(t, result, true)
	})

	t.Run("测试 03- 扫码", func(t *testing.T) {
		product := createProduct()
		in := NewIntercityDetailPageUpgrade(product.BaseReqData)
		in.targetProduct = &models.Product{}
		in.baseReq.CommonInfo.AgentType = DetailConst.QuickScanCode
		featureToggleMock := mockey.Mock(apollo.FeatureToggle).To(func(ctx context.Context, key string, pid string, params map[string]string) bool {
			return true
		}).Build()
		switchApollo := mockey.Mock(JudgeNewOrderPageSwitch).To(func(ctx context.Context, pageParams *NewPageSwitchParams) bool { return true }).Build()
		defer featureToggleMock.UnPatch()
		defer switchApollo.UnPatch()
		info := &models.CommonBizInfo{}
		in.BuildCommonBizInfo(ctx, info)
		assert.Equal(t, info.IsNewPageUpgrade, true)
	})

	t.Run("测试 04- 普通", func(t *testing.T) {
		product := createProduct()
		in := NewIntercityDetailPageUpgrade(product.BaseReqData)
		in.targetProduct = &models.Product{}
		featureToggleMock := mockey.Mock(apollo.FeatureToggle).To(func(ctx context.Context, key string, pid string, params map[string]string) bool {
			return false
		}).Build()
		defer featureToggleMock.UnPatch()
		info := &models.CommonBizInfo{}
		in.BuildCommonBizInfo(ctx, info)
		assert.Equal(t, info.IsNewPageUpgrade, false)
	})

}

// getRealNameSeatInfoCard
func TestSwitch(t *testing.T) {
	t.Run("测试 01- 获取到", func(t *testing.T) {
		ctx := context.Background()
		featureToggleMock := mockey.Mock(apollo.FeatureToggle).To(func(ctx context.Context, key string, pid string, params map[string]string) bool {
			return true
		}).Build()
		defer featureToggleMock.UnPatch()

		result := JudgeNewOrderPageSwitch(ctx, &NewPageSwitchParams{
			Pid: "123",
			Uid: "345",
		})

		assert.Equal(t, result, true)
	})
	t.Run("测试 01- 获取不到", func(t *testing.T) {
		ctx := context.Background()
		featureToggleMock := mockey.Mock(apollo.FeatureToggle).To(func(ctx context.Context, key string, pid string, params map[string]string) bool {
			return false
		}).Build()
		defer featureToggleMock.UnPatch()

		result := JudgeNewOrderPageSwitch(ctx, &NewPageSwitchParams{
			Pid: "123",
			Uid: "345",
		})

		assert.Equal(t, result, false)
	})
}
