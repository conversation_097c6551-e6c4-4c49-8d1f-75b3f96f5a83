package classify

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/group_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	Trace "git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"
	"strings"
)

const (
	ClassifyCategoryConfigNamespace          = "classify_category_info_config"            // 兜底分组配置
	ClassifyCategoryByProductConfigNamespace = "classify_category_info_config_by_product" // 品类可移动的分组配置
)

// DefaultCategoryConfigLoad CategoryConfig 三方表单分栏配置
type DefaultCategoryConfigLoad struct {
	baseReq *models.BaseReqData

	baseConf *models.BaseCategoryConf
}

func NewClassifyCategoryConfig(ctx context.Context, baseReq *models.BaseReqData) *DefaultCategoryConfigLoad {
	if !tab.IsClassifyTab(baseReq.CommonInfo.TabId) {
		return nil
	}

	return &DefaultCategoryConfigLoad{
		baseReq: baseReq,
		baseConf: &models.BaseCategoryConf{
			GroupIdPosition:     make(map[string]int32),
			PossibleCategoryMap: make(map[int64][]int32),
		},
	}
}

func (d *DefaultCategoryConfigLoad) Fetch(ctx context.Context, products []*biz_runtime.ProductInfoFull) bool {
	var defaultCategoryConfigs []*apollo_model.ClassifyCategoryConfig
	configBytes, err := apollo.GetConfigsByNamespace(ctx, ClassifyCategoryConfigNamespace)
	if err != nil {
		log.Trace.Warnf(ctx, Trace.DLTagUndefined, "get config:%s err:%v", ClassifyCategoryConfigNamespace, err)
		return true
	}
	err = json.Unmarshal(configBytes, &defaultCategoryConfigs)
	if err != nil {
		log.Trace.Warnf(ctx, Trace.DLTagUndefined, "get config:%s err:%v", ClassifyCategoryConfigNamespace, err)
		return true
	}

	if defaultCategoryConfigs == nil {
		return true
	}

	// 构建映射关系
	for _, config := range defaultCategoryConfigs {
		// 构建sub_group_id到category_id的映射
		for _, subGroupID := range config.SubGroupID {
			d.baseConf.GroupIdPosition[group_id.BuildGroupId(group_id.AggregationBoxType, cast.ToInt64(subGroupID))] = config.CategoryID
		}

		// 构建product_category到category_id的映射
		for _, pcID := range config.ProductList {
			if pcIDInt, err := cast.ToInt64E(pcID); err == nil {
				d.baseConf.GroupIdPosition[group_id.BuildGroupId(group_id.SINGLETYPE, pcIDInt)] = config.CategoryID
			}
		}

		// 构建分栏配置
		categoryItem := &models.CategoryItem{
			CategoryID:       config.CategoryID,
			Title:            config.Title,
			SubTitle:         config.SubTitle,
			FoldText:         util.GetOrDefault(config.FoldText != "", config.FoldText, "其他品牌%s个"),
			Icon:             config.Icon,
			BgGradients:      config.BgGradients,
			IsSelected:       0,
			ProductList:      config.ProductList,
			SubGroupID:       config.SubGroupID,
			SectionTitle:     config.SectionTitle,
			Rank:             config.Rank,
			IsFold:           0,
			SubCategoryTitle: config.SubCategoryTitle,
		}
		d.baseConf.CategoryConf = append(d.baseConf.CategoryConf, categoryItem)
	}

	// 获取可移动分栏配置
	possibleConfigs := d.loadPossibleCategoryConfig(ctx)
	if possibleConfigs == nil {
		return true
	}
	apolloParams := d.baseReq.GetApolloParam()

	for _, config := range possibleConfigs {
		if config.ProductCategory == 0 || len(config.PossibleCategoryIds) == 0 {
			continue
		}
		// 加载个性化配置
		if config.StrategyAB != "" {
			if apollo.FeatureToggle(ctx, config.StrategyAB, d.baseReq.PassengerInfo.Phone, apolloParams) {
				// 从实验参数中获取possible_category_ids
				_, assignParams := apollo.GetParameters(config.StrategyAB, d.baseReq.PassengerInfo.Phone, apolloParams)
				if assignParams != nil {
					if possibleIds, ok := assignParams["possible_category_ids"]; ok && possibleIds != "" {
						var possibleList []int32
						for _, idStr := range strings.Split(possibleIds, ",") {
							if id, err := cast.ToInt32E(strings.TrimSpace(idStr)); err == nil {
								possibleList = append(possibleList, id)
							}
						}
						if len(possibleList) > 0 {
							d.baseConf.PossibleCategoryMap[config.ProductCategory] = possibleList
							continue
						}
					}
				}
			}
		}
		// 使用默认配置
		d.baseConf.PossibleCategoryMap[config.ProductCategory] = config.PossibleCategoryIds
	}

	return true
}

func (d *DefaultCategoryConfigLoad) loadPossibleCategoryConfig(ctx context.Context) []*apollo_model.PossibleCategoryConfig {
	var configs []*apollo_model.PossibleCategoryConfig

	configBytes, err := apollo.GetConfigsByNamespace(ctx, ClassifyCategoryByProductConfigNamespace)
	if err != nil {
		log.Trace.Warnf(ctx, Trace.DLTagUndefined, "get config:%s err:%v", ClassifyCategoryByProductConfigNamespace, err)
		return nil
	}
	err = json.Unmarshal(configBytes, &configs)
	if err != nil {
		log.Trace.Warnf(ctx, Trace.DLTagUndefined, "get config:%s err:%v", ClassifyCategoryByProductConfigNamespace, err)
		return nil
	}
	for _, config := range configs {
		for _, id := range config.PossibleCategoryStrIds {
			config.PossibleCategoryIds = append(config.PossibleCategoryIds, cast.ToInt32(id))
		}
	}
	return configs
}

func (d *DefaultCategoryConfigLoad) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.BaseCategoryConf = d.baseConf

	info.GroupId2CategoryId = d.baseConf.GroupIdPosition
}

func (d *DefaultCategoryConfigLoad) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {

}
