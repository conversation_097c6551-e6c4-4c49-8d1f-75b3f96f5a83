package rpc_process

import (
	"context"
	Dirpc_SDK_Ferrari "git.xiaojukeji.com/dirpc/dirpc-go-http-Ferrari"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ferrari"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type travelQuotation struct {
	oid   int64
	eid   string
	field []string

	travelQuotationInfo *Dirpc_SDK_Ferrari.TravelQuotation
	err                 error
}

func NewTravelQuotation(ctx context.Context, oid int64, eid string, field []string) biz_runtime.RpcProcessWithBaseProducts {
	return &travelQuotation{
		oid:   oid,
		eid:   eid,
		field: field,
	}
}

func (t *travelQuotation) Fetch(ctx context.Context, products []*models.Product) bool {
	travelQuotationInfo, err := ferrari.GetTravelQuotationNew(ctx, t.eid, t.field, t.oid)
	if err != nil {
		return false
	}

	t.travelQuotationInfo = travelQuotationInfo
	return true
}
func (t *travelQuotation) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.TravelQuotation = t.travelQuotationInfo
}
func (t *travelQuotation) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
}
func (t *travelQuotation) GetErrorInfo(ctx context.Context) error {
	return t.err
}
