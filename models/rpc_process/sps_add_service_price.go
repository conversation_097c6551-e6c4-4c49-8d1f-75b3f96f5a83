package rpc_process

import (
	"context"
	"strconv"

	Sps "git.xiaojukeji.com/dirpc/dirpc-go-http-Sps"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/sps"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"
)

// SpsAddServicePrice SPS增值服务价格处理器
type SpsAddServicePrice struct {
	baseReq   *models.BaseReqData              // 请求信息
	spsFeeMap map[int32]map[int64]*Sps.FeeItem // SPS返回的费用信息: [pcId][serviceId] -> feeInfo
}

// NewSpsAddServicePrice 创建SPS增值服务价格处理器
func NewSpsAddServicePrice(ctx context.Context, req *models.BaseReqData) *SpsAddServicePrice {
	return &SpsAddServicePrice{
		baseReq:   req,
		spsFeeMap: make(map[int32]map[int64]*Sps.FeeItem),
	}
}

// GetErrorInfo 获取rpc错误信息
func (s *SpsAddServicePrice) GetErrorInfo(ctx context.Context) error {
	return nil
}

// Fetch RPC 请求SPS, 获取增值服务价格信息
func (s *SpsAddServicePrice) Fetch(ctx context.Context, products []*models.Product) bool {
	var (
		targetProducts = make([]*models.Product, 0)
		reqList        = make([]*Sps.ReqItem, 0)
	)

	if len(products) == 0 {
		return false
	}

	apolloParam := s.baseReq.GetApolloParam()
	apolloParam["caller"] = "mamba"

	// SPS降级开关
	if !apollo.FeatureToggle(ctx, "sps_access_controller", strconv.Itoa(int(s.baseReq.PassengerInfo.UID)), apolloParam) {
		return false
	}

	for _, p := range products {
		if p.IsTripcloudProduct(ctx) {
			continue
		}

		// 检查产品是否有增值服务需要获取价格
		if len(p.BizInfo.CustomFeatureList) == 0 {
			continue
		}

		targetProducts = append(targetProducts, p)

		// 构建服务列表
		serviceList := make([]*Sps.ServiceItem, 0)
		for _, feature := range p.BizInfo.CustomFeatureList {
			serviceList = append(serviceList, &Sps.ServiceItem{
				ServiceId: feature.ServiceId,
				Count:     1, // 默认数量为1，或者从feature中获取
			})
		}

		if len(serviceList) == 0 {
			continue
		}

		// 构建会员权益信息
		memberPrivilege := make(map[string]int32)
		// 这里可以根据需要添加会员权益映射逻辑

		reqItem := &Sps.ReqItem{
			PassengerId:     s.baseReq.PassengerInfo.PID,
			City:            cast.ToInt32(s.baseReq.AreaInfo.Area),
			ProductId:       cast.ToInt32(p.ProductID),
			CarLevel:        p.RequireLevel,
			ComboType:       cast.ToInt32(p.ComboType),
			ProductCategory: cast.ToInt32(p.ProductCategory),
			DepartureTime:   s.baseReq.CommonInfo.DepartureTime,
			OrderType:       s.baseReq.CommonInfo.OrderType,
			AccessKeyId:     cast.ToInt32(s.baseReq.CommonInfo.AccessKeyID),
			AppVersion:      s.baseReq.CommonInfo.AppVersion,
			CountyId:        cast.ToInt32(s.baseReq.AreaInfo.ToCounty),
			ServiceList:     serviceList,
			MemberPrivilege: memberPrivilege,
		}

		reqList = append(reqList, reqItem)
	}

	if len(reqList) == 0 {
		return false
	}

	spsReq := &Sps.GetCustomedServiceFeeInfoReq{
		ReqList: reqList,
	}

	s.spsFeeMap = sps.GetCustomServiceFeeInfo(ctx, spsReq)
	return s.spsFeeMap != nil
}

// BuildCommonBizInfo RPC
func (s *SpsAddServicePrice) BuildCommonBizInfo(ctx context.Context, commonBizInfo *models.CommonBizInfo) {
	// SPS处理器一般不需要构建通用业务信息
}

// BuildProductBizInfo RPC 构建产品业务信息
func (s *SpsAddServicePrice) BuildProductBizInfo(ctx context.Context, product models.Product,
	privateBizInfo *models.PrivateBizInfo) {

	// 获取该产品品类对应的SPS价格信息
	if pcFeeMap, ok := s.spsFeeMap[cast.ToInt32(product.ProductCategory)]; ok && len(pcFeeMap) > 0 {
		// 将SPS价格信息存储到私有业务信息中
		// 可以通过扩展 PrivateBizInfo 结构或使用现有的方式存储
		// 这里暂时通过其他方式访问，比如在全局map中存储或通过其他字段
		// 具体的存储方式需要根据业务需求确定
		privateBizInfo.SpsFeeMap = pcFeeMap
	}
}
