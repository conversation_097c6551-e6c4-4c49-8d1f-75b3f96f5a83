package rpc_process

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/athena"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
)

type AthenaRankInfo struct {
	orderId int64
	cityId  int32
	resp    []*AthenaApiv3.MultiRequireProduct
}

func NewAthenaRankInfoRpc(orderId int64, cityId int32) *AthenaRankInfo {
	return &AthenaRankInfo{orderId, cityId, nil}
}

func (rp *AthenaRankInfo) Fetch(ctx context.Context, products []*models.Product) bool {
	var (
		orderInfo            *AthenaApiv3.MultiProductOrderInfo
		multiRequireProducts = make([]*AthenaApiv3.MultiRequireProduct, 0)
		athenaResp           []*AthenaApiv3.MultiRequireProduct
	)
	orderInfo = &AthenaApiv3.MultiProductOrderInfo{
		OrderID: rp.orderId,
		CityID:  rp.cityId,
	}

	for _, p := range products {
		mrP := &AthenaApiv3.MultiRequireProduct{}
		requireLevel, _ := strconv.Atoi(p.RequireLevel)
		mrP.BusinessID = int32(p.BusinessID)
		mrP.RequireLevel = int32(requireLevel)
		mrP.ComboType = int32(p.ComboType)
		mrP.LevelType = &p.LevelType
		multiRequireProducts = append(multiRequireProducts, mrP)
	}

	athenaResp = athena.GetMultiProductRank(ctx, orderInfo, multiRequireProducts)
	if athenaResp == nil {
		return false
	}
	rp.resp = athenaResp
	return true
}

func (rp *AthenaRankInfo) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {

}

func (rp *AthenaRankInfo) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
	for _, mrp := range rp.resp {
		if product.BusinessID == int64(mrp.BusinessID) && product.RequireLevel == strconv.FormatInt(int64(mrp.RequireLevel), 10) &&
			product.ComboType == int64(mrp.ComboType) && product.LevelType == *mrp.LevelType {
			info.Ranking = mrp.Ranking
			info.WaitTime = mrp.WaitTime
		}
	}
}
