package rpc_process

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/s3e/x-engine/condition"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

// IntercitySurpriseAloneProcessor 城际惊喜独享标识处理器
type IntercitySurpriseAloneProcessor struct {
	baseReq *models.BaseReqData // 请求信息
	comboID int64               // 路线组合ID
}

// NewIntercitySurpriseAloneProcessor 创建城际惊喜独享标识处理器
func NewIntercitySurpriseAloneProcessor(ctx context.Context, req *models.BaseReqData) *IntercitySurpriseAloneProcessor {
	return &IntercitySurpriseAloneProcessor{
		baseReq: req,
		comboID: 0,
	}
}

// GetErrorInfo 获取rpc错误信息
func (i *IntercitySurpriseAloneProcessor) GetErrorInfo(ctx context.Context) error {
	return nil
}

// Fetch 获取数据
func (i *IntercitySurpriseAloneProcessor) Fetch(ctx context.Context, products []*models.Product) bool {
	// 1. 查找城际自营拼满走品类，获取ComboID
	i.comboID = 0
	for _, product := range products {
		if product == nil {
			continue
		}

		// 自营拼满走才会命中：品类为城际拼车(5) 且 RouteType为拼满走(3)
		if product.ProductCategory == estimate_pc_id.EstimatePcIdCarpoolInter &&
			carpool.IsIntercityFullGo(product.ProductCategory, product.RouteType) {
			if product.BizInfo != nil && product.BizInfo.RouteInfo != nil && product.BizInfo.RouteInfo.RouteId != nil {
				i.comboID = *product.BizInfo.RouteInfo.RouteId
				break
			}
		}
	}

	// 非城际自营过滤
	if i.comboID == 0 {
		return true
	}

	// 2. 城际自营命中惊喜独享对应的端、小程序版本
	apolloParams := i.baseReq.GetApolloParam()
	pidKey := strconv.FormatInt(i.baseReq.PassengerInfo.PID, 10)
	toggleSwitch := apollo.FeatureToggle(ctx, "gs_intercity_surprise_alone_switch", pidKey, apolloParams)
	if !toggleSwitch {
		return true
	}

	// 3. 命中惊喜独享配置 - 使用x-engine条件引擎
	conditionParams := map[string]interface{}{
		"city":        int(i.baseReq.AreaInfo.Area),
		"route_group": i.comboID,
	}

	conditionResult, err := condition.Check(ctx, "intercity_carpool_surprise_alone_conf", conditionParams)
	if err != nil || conditionResult == nil || !conditionResult.IsAllow {
		return true
	}

	// 4. AB实验 - 走惊喜独享实验
	abParams := ApolloModel.NewUser(strconv.FormatInt(i.baseReq.PassengerInfo.PID, 10)).
		With("key", strconv.FormatInt(i.baseReq.PassengerInfo.PID, 10)).
		With("pid", strconv.FormatInt(i.baseReq.PassengerInfo.PID, 10)).
		With("phone", i.baseReq.PassengerInfo.Phone).
		With("city", strconv.Itoa(int(i.baseReq.AreaInfo.Area))).
		With("combo_id", strconv.FormatInt(i.comboID, 10))

	abToggle, err := ApolloSDK.FeatureToggle("intercity_uni_express", abParams)
	if err != nil || !abToggle.IsAllow() {
		return true
	}

	// 检查实验参数
	if assignment := abToggle.GetAssignment(); assignment != nil {
		hitShowSurpriseAlone := assignment.GetParameter("hit_show_surprise_alone", "")
		if hitShowSurpriseAlone != "1" {
			return true
		}
	} else {
		return true
	}

	// 5. 为城际拼车品类设置惊喜独享标识
	for _, product := range products {
		if product == nil {
			continue
		}

		if product.ProductCategory == estimate_pc_id.EstimatePcIdCarpoolInter {
			// 设置惊喜独享标识 - 通过GuideStationBusData字段设置
			// 根据PHP代码中的iIsIntercitySurpriseAlone设置对应标识
			// 这里可以在后续处理中使用这个标识
		}
	}

	return true
}

// BuildCommonBizInfo 构建通用业务信息
func (i *IntercitySurpriseAloneProcessor) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	// 通用业务信息处理（如果需要的话）
}

// BuildProductBizInfo 构建产品业务信息
func (i *IntercitySurpriseAloneProcessor) BuildProductBizInfo(ctx context.Context, product models.Product,
	privateBizInfo *models.PrivateBizInfo) {
	// 产品业务信息处理（如果需要的话）
}
