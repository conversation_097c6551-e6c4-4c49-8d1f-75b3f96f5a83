package rpc_process_test

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"testing"

	ufsThrift "git.xiaojukeji.com/dirpc/dirpc-go-thrift-UFS"
	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/ufsClient"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/smartystreets/goconvey/convey"
)

func TestFetch_Failure_NoPermission(t *testing.T) {
	//录音录像授权校验-未授权场景
	convey.Convey("当 Fetch 方法执行时", t, func() {
		var (
			ctx          = context.Background()
			pid          = "test_pid"
			productInfos = []*biz_runtime.ProductInfoFull{{}}
			value        = "0" //未授权
			commonInfo   = &models.CommonBizInfo{}
			productInfo  = biz_runtime.ProductInfoFull{}
			bizInfo      = &models.PrivateBizInfo{}
		)

		expectedResponse := map[string]*ufsThrift.FeatureResponse{
			"passenger.video_sign_new@{\"passenger_id\":\"70368747127077\"}": {
				Errno:  0,
				Errmsg: "",
				Value:  &value,
			},
		}

		patches := gomonkey.ApplyFunc(ufs.MultiGetFeatures, func(ctx context.Context, features []ufsClient.Feature, srcMethod string) (map[string]*ufsThrift.FeatureResponse, error) {
			return expectedResponse, nil
		})

		defer patches.Reset()

		convey.Convey("ufs返回用户未授权场景", func() {
			//校验赋值逻辑
			uf := rpc_process.NewVideoAuthRPC(ctx, pid)
			convey.So(uf.PassengerID == pid, convey.ShouldBeTrue)

			//校验调用rpc逻辑
			rpcResult := uf.Fetch(ctx, productInfos)
			convey.So(rpcResult, convey.ShouldBeTrue)          //rpc正常执行
			convey.So(uf.IsNeedAuthVideo, convey.ShouldBeTrue) //需要授权

			//校验构建数据总线逻辑
			uf.BuildCommonBizInfo(ctx, commonInfo)                                          //无数据
			uf.BuildProductBizInfo(ctx, productInfo, bizInfo)                               //填充数据总线
			convey.So(bizInfo.IsNeedDefaultAuth == uf.IsNeedAuthVideo, convey.ShouldBeTrue) //保持一致
		})
	})

	//录音录像授权校验-已授权场景
	convey.Convey("当 Fetch 方法执行时", t, func() {
		var (
			ctx          = context.Background()
			pid          = "test_pid"
			productInfos = []*biz_runtime.ProductInfoFull{{}}
			value        = "1" //已授权
			commonInfo   = &models.CommonBizInfo{}
			productInfo  = biz_runtime.ProductInfoFull{}
			bizInfo      = &models.PrivateBizInfo{}
		)

		expectedResponse := map[string]*ufsThrift.FeatureResponse{
			"passenger.video_sign_new@{\"passenger_id\":\"70368747127077\"}": {
				Errno:  0,
				Errmsg: "",
				Value:  &value,
			},
		}

		patches := gomonkey.ApplyFunc(ufs.MultiGetFeatures, func(ctx context.Context, features []ufsClient.Feature, srcMethod string) (map[string]*ufsThrift.FeatureResponse, error) {
			return expectedResponse, nil
		})

		defer patches.Reset()

		convey.Convey("ufs返回用户已授权场景", func() {
			//校验赋值逻辑
			uf := rpc_process.NewVideoAuthRPC(ctx, pid)
			convey.So(uf.PassengerID == pid, convey.ShouldBeTrue)

			//校验调用rpc逻辑
			rpcResult := uf.Fetch(ctx, productInfos)
			convey.So(rpcResult, convey.ShouldBeTrue)           //rpc正常执行
			convey.So(uf.IsNeedAuthVideo, convey.ShouldBeFalse) //不需要授权

			//校验构建数据总线逻辑
			uf.BuildCommonBizInfo(ctx, commonInfo)                                          //无数据
			uf.BuildProductBizInfo(ctx, productInfo, bizInfo)                               //填充数据总线
			convey.So(bizInfo.IsNeedDefaultAuth == uf.IsNeedAuthVideo, convey.ShouldBeTrue) //保持一致
		})
	})

}
