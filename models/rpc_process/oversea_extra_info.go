package rpc_process

import (
	"context"
	TripCloudPassengerGo "git.xiaojukeji.com/dirpc/dirpc-go-http-TripCloudPassengerGo"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/trip_cloud_passenger_go"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type OverseaExtraInfo struct {
	infoMap map[string]int64
}

func NewOverseaExtraInfo() *OverseaExtraInfo {
	return &OverseaExtraInfo{}
}

func (o *OverseaExtraInfo) Fetch(ctx context.Context, productFulls []*biz_runtime.ProductInfoFull) bool {
	var products []*TripCloudPassengerGo.EstimateProduct
	if len(productFulls) == 0 {
		return false
	}
	for _, productFull := range productFulls {
		if productFull.GetEstimateID() == "" || productFull.GetExternalEid() == "" {
			continue
		}
		info := &TripCloudPassengerGo.EstimateProduct{
			EstimateId:    productFull.GetEstimateID(),
			ExtEstimateId: productFull.GetExternalEid(),
		}

		products = append(products, info)

	}

	req := &TripCloudPassengerGo.PEstimateExtraInfoReq{
		BusinessId:   productFulls[0].Product.BusinessID,
		MultiProduct: products,
	}
	resp, err := trip_cloud_passenger_go.GetPEstimateExtraInfo(ctx, req)
	if err != nil {
		log.Trace.Infof(ctx, consts.TagTripcloudErr, "GetPEstimateExtraInfo err: %v", err)
		return false
	}
	if resp.Data != nil && resp.Data.EstimateExtraInfoList != nil && len(resp.Data.EstimateExtraInfoList) > 0 {
		infoMap := make(map[string]int64)
		for _, pcInfo := range resp.Data.EstimateExtraInfoList {
			if pcInfo.GetEts() > 0 {
				infoMap[pcInfo.EstimateId] = pcInfo.GetEts()
			}
		}
		o.infoMap = infoMap
		return true
	}

	return false

}

func (o *OverseaExtraInfo) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
}

func (o *OverseaExtraInfo) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	if o.infoMap == nil || len(o.infoMap) == 0 {
		return
	}

	if o.infoMap[product.GetEstimateID()] > 0 {
		extra := &models.OverseaExtraInfo{
			ETS: o.infoMap[product.GetEstimateID()],
		}
		info.OverseaExtraInfo = extra
	}

}
