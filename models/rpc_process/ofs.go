package rpc_process

import (
	"context"
	"encoding/json"
	dirpc_ofs "git.xiaojukeji.com/dirpc/dirpc-go-ofs"
	ProductCategory "git.xiaojukeji.com/gulfstream/biz-common-go/v6/category"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
	"git.xiaojukeji.com/gulfstream/passenger-common/sdk/ofs"
	"strconv"
)

type OFS struct {
	orderInfo                    *order_info.OrderInfo
	isHitMemberPrivilegeAddAPlus bool

	err error
}

type ProductMemberInfo struct {
	MemberPrivilegeNewOrder string `json:"member_privilege_new_order"`
}

type ProductMemberProfile struct {
	MemberPrivilegeAddAPlus *MemberPrivilegeAddAPlus `json:"member_privilege_add_aplus"`
}

type MemberPrivilegeAddAPlus struct {
	Used int `json:"used"`
}

func (o *OFS) Fetch(ctx context.Context, products []*models.Product) bool {
	if o.orderInfo == nil {
		return true
	}

	featureScene := make([]*dirpc_ofs.FeatureScene, 0)

	featureScene = append(featureScene, &dirpc_ofs.FeatureScene{
		FeatureSceneType: dirpc_ofs.FeatureSceneType_BASIC,
		FeatureKeys:      []string{"multi_product_members"},
	})
	feature, err := ofs.GetOfsClient().GetOrderFeature(ctx, o.orderInfo.GetOrderID(), featureScene)
	if err != nil {
		return false
	}

	log.Trace.Infof(ctx, "get_ofs", "%v", util.JustJsonEncode(feature))

	KVMap, ok := feature[dirpc_ofs.FeatureSceneType_BASIC]
	if !ok {
		return true
	}

	multiProductMembers := make(map[string]*ProductMemberInfo)
	productMemberProfile := &ProductMemberProfile{}
	memberInfo, ok := KVMap["multi_product_members"]
	if !ok {
		return true
	}

	err = json.Unmarshal([]byte(memberInfo), &multiProductMembers)
	if err != nil {
		log.Trace.Warnf(ctx, "ofs_fetch", "ofs fail,err:%v", err)
		return false
	}

	fastGroupKey := ""
	productCategory := 0
	if o.orderInfo.EstimatePcId != nil {
		productCategory = util.String2Int(ctx, *o.orderInfo.EstimatePcId)
	}

	for _, productInfo := range o.orderInfo.ExtendFeatureParsed.MultiRequiredProduct {
		if productInfo.EstimatePcID == strconv.Itoa(ProductCategory.ProductCategoryFast) {
			fastGroupKey = productInfo.GroupKey
			break
		}
	}

	if fastGroupKey == "" || productCategory != ProductCategory.ProductCategoryAplus {
		return true
	}

	productMemberInfo, ok := multiProductMembers[fastGroupKey]
	if !ok || productMemberInfo == nil || len(productMemberInfo.MemberPrivilegeNewOrder) <= 0 {
		return true
	}

	err = json.Unmarshal([]byte(productMemberInfo.MemberPrivilegeNewOrder), &productMemberProfile)
	if err != nil {
		log.Trace.Warnf(ctx, "ofs_fetch", "ofs fail,err:%v", err.Error())
		return true
	}

	if productMemberProfile != nil && productMemberProfile.MemberPrivilegeAddAPlus != nil && productMemberProfile.MemberPrivilegeAddAPlus.Used == 1 {
		o.isHitMemberPrivilegeAddAPlus = true
		return true
	}

	return true
}

func (o *OFS) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	info.IsHitMemberPrivilegeAddAPlus = o.isHitMemberPrivilegeAddAPlus
}

func (o *OFS) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
}

func (o *OFS) GetErrorInfo(ctx context.Context) error {
	return o.err
}

func NewOFS(orderInfo *order_info.OrderInfo) biz_runtime.RpcProcessWithBaseProducts {
	return &OFS{
		orderInfo: orderInfo,
	}
}
