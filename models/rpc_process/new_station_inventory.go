package rpc_process

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity"
	"sort"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/source_id"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"

	// "runtime"
	"strconv"

	CarpoolOpenApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolOpenApi"
	Apollo2 "git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/carpool_open_api"
)

const (
	ChannelHonghuCheckBackground = 40082
	ChannelHonghuPhoneCall       = 40083
	AccessKeyIdHonghu            = 49
)

const (
	IntercityMultiEstimatePrice = "pIntercityMutliEstimatePrice"
	AccuRate                    = 0
)

type MutliStationInventory struct {
	baseReq *models.BaseReqData
}

func NewMutliStationInventory(baseReq *models.BaseReqData) *MutliStationInventory {
	return &MutliStationInventory{
		baseReq: baseReq,
	}
}

func (m *MutliStationInventory) Fetch(ctx context.Context, products []int32) []models.SkuInfo {
	const (
		LogTag = "carpool_station_inventory"
	)
	stb := carpool_open_api.NewMultiStationInventoryRequestBuilder(m.getTimeDuration()).SetGeoInfo(&m.baseReq.CommonBizInfo.StationInfo).SetPassengerInfo(strconv.FormatInt(m.baseReq.PassengerInfo.PID, 10)).
		SetShiftBriefInfoList(m.buildShiftBriefInfoList()).SetExtraInfo(m.buildExtraInfo(ctx)).SetSortType(m.buildIsSort(ctx)) //.SetSortType(m.baseReq.CommonInfo.SortType)

	traceInfo := stb.SetProductIds(products).BuildCtx(ctx)
	resp, err := carpool_open_api.GetBatchQueryRouteShiftInventoryInfo(ctx, stb.Build(), traceInfo)

	if err != nil {
		// log.Trace.Errorf(ctx, LogTag, "fetch BatchQueryRouteShiftInventoryInfo failed %s", err)
		return nil
	}

	if resp == nil {
		log.Trace.Warnf(ctx, LogTag, "fetch BatchQueryRouteShiftInventoryInfo got empty")
		return nil
	}

	if resp.GetRetCode() != 0 || (len(resp.RouteShiftInfoList) == 0 && len(resp.SortedShiftList) == 0) {
		//log.Trace.Errorf(ctx, LogTag, "fetch BatchQueryRouteShiftInventoryInfo failed(%d) %s", resp.GetRetCode(), resp.GetRetMsg())
		return nil
	}
	return m.InitSkuInfos(ctx, resp.RouteShiftInfoList, resp.SortedShiftList)
}

func (m *MutliStationInventory) getTimeDuration() (fromTime int64, toTime int64) {
	DayTime := m.baseReq.CommonBizInfo.StationInfo.DayTime
	StartTime := m.baseReq.CommonBizInfo.StationInfo.StartTime
	EndTime := m.baseReq.CommonBizInfo.StationInfo.EndTime

	if StartTime != 0 && EndTime != 0 {
		return int64(StartTime), int64(EndTime)
	}
	if m.baseReq.CommonInfo.AccessKeyID == AccessKeyIdHonghu && m.baseReq.CommonInfo.Channel == ChannelHonghuPhoneCall {
		StartTime = int32(util.GetNowEarlyTimeStamp())
		EndTime = int32(util.GetNowLastTimeStamp() + 13*24*60*60)
		return int64(StartTime), int64(EndTime)
	}
	if DayTime == 0 || DayTime < int32(util.GetNowLastTimeStamp()) {
		StartTime = int32(util.GetNowEarlyTimeStamp())
		EndTime = int32(util.GetNowLastTimeStamp())
		return int64(StartTime), int64(EndTime)
	} else {
		StartTime = DayTime
		EndTime = DayTime + 24*60*60
		return int64(StartTime), int64(EndTime)
	}
}

func (m *MutliStationInventory) FenceBusAppversionFilter(ctx context.Context, productId int32, shiftInventory *CarpoolOpenApi.ShiftInventory) bool {
	// true-过滤，false-不过滤
	if value, ok := shiftInventory.ExtraInfo[intercity.RouteSceneType]; !ok || value != intercity.FenceRouteSceneType {
		return false
	}
	params := map[string]string{
		"access_key_id": cast.ToString(m.baseReq.CommonInfo.AccessKeyID),
		"app_version":   m.baseReq.CommonInfo.AppVersion,
		"city":          cast.ToString(m.baseReq.AreaInfo.Area),
		"phone":         m.baseReq.PassengerInfo.Phone,
		"product_id":    cast.ToString(productId),
		"scene":         IntercityMultiEstimatePrice,
	}

	if !Apollo2.FeatureToggle(ctx, "gs_intercity_fence_bus_appversion_toggle", strconv.FormatInt(m.baseReq.PassengerInfo.PID, 10), params) {
		return true
	}
	return false
}

func (m *MutliStationInventory) InitSkuInfos(ctx context.Context, routeShiftInfoList []*CarpoolOpenApi.RouteShiftInventoryInfo, sortedShiftList []*CarpoolOpenApi.ShiftInventory) []models.SkuInfo {
	var res []models.SkuInfo
	routeId := m.baseReq.CommonBizInfo.StationInfo.RouteId
	if m.baseReq.CommonInfo.Channel == ChannelHonghuCheckBackground {
		res = initCheckSkuInfo(ctx, routeShiftInfoList, m, routeId, res)
	} else if m.baseReq.CommonInfo.Channel == ChannelHonghuPhoneCall {
		res = initBackSkuInfo(ctx, routeShiftInfoList, routeId, m, res)
	} else {
		if sortedShiftList != nil && len(sortedShiftList) > 0 {
			res = initSkuInfoV2(ctx, sortedShiftList, m, res) // 端相关端排序
		} else {
			res = initSkuInfo(ctx, routeShiftInfoList, routeId, m, res) // 原下发逻辑
		}
	}

	var haveRemainSeatsRes []models.SkuInfo
	var noRemainSeatsRes []models.SkuInfo
	for _, v := range res {
		if v.RemainSeats <= 0 {
			noRemainSeatsRes = append(noRemainSeatsRes, v)
		} else {
			haveRemainSeatsRes = append(haveRemainSeatsRes, v)
		}
	}

	return append(haveRemainSeatsRes, noRemainSeatsRes...)
}

func initCheckSkuInfo(ctx context.Context, routeShiftInfoList []*CarpoolOpenApi.RouteShiftInventoryInfo, m *MutliStationInventory, routeId int32, res []models.SkuInfo) []models.SkuInfo {
	if m.baseReq.CommonBizInfo.BusServiceShiftId == "" {
		for _, v := range routeShiftInfoList {
			if v == nil || len(v.ShiftInventoryList) == 0 {
				continue
			}
			if routeId == 0 || routeId == int32(v.RouteGroup) {
				for _, i := range v.ShiftInventoryList {
					//匹配起终站点id完全一致的路线
					if m.baseReq.CommonBizInfo.StationInfo.StartStationId != *i.SrcRegion.StationID || m.baseReq.CommonBizInfo.StationInfo.EndStationId != *i.DestRegion.StationID {
						continue
					}
					// 移动端检票端代发单过滤多点到门
					if value, ok := i.ExtraInfo[intercity.RouteSceneType]; ok && value == intercity.FenceRouteSceneType {
						continue
					}
					res = append(res, models.SkuInfo{
						RouteGroup:                v.RouteGroup,
						ProductID:                 v.ProductID,
						DepartureTime:             i.DepartureTime,
						RemainSeats:               i.RemainSeats,
						ShiftID:                   i.ShiftID,
						StartStationID:            *i.SrcRegion.StationID,
						EndStationID:              *i.DestRegion.StationID,
						CarryChildrenMaxInventory: util.String2int32(ctx, i.ExtraInfo["remain_carry_child_num"]),
					})
				}
				//匹配到唯一的路线就返回，保证只有一个路线
				if len(res) > 0 {
					break
				}
			}
		}
	} else {
		res = m.getMatchedSkuInfoByShiftId(ctx, routeShiftInfoList, res)
	}
	sort.SliceStable(res, func(i, j int) bool {
		return res[i].DepartureTime < res[j].DepartureTime
	})
	return res
}

func initBackSkuInfo(ctx context.Context, routeShiftInfoList []*CarpoolOpenApi.RouteShiftInventoryInfo, routeId int32, m *MutliStationInventory, res []models.SkuInfo) []models.SkuInfo {
	for _, v := range routeShiftInfoList {
		if v == nil || len(v.ShiftInventoryList) == 0 {
			continue
		}
		if routeId == 0 || routeId == int32(v.RouteGroup) {
			for _, i := range v.ShiftInventoryList {
				res = append(res, models.SkuInfo{
					RouteGroup:                v.RouteGroup,
					ProductID:                 v.ProductID,
					DepartureTime:             i.DepartureTime,
					RemainSeats:               i.RemainSeats,
					ShiftID:                   i.ShiftID,
					StartStationID:            *i.SrcRegion.StationID,
					EndStationID:              *i.DestRegion.StationID,
					SrcCost:                   i.SrcCost,
					DestCost:                  i.DestCost,
					CarryChildrenMaxInventory: util.String2int32(ctx, i.ExtraInfo["remain_carry_child_num"]),
					ShiftType:                 util.String2int32(ctx, i.ExtraInfo["shift_type"]), // 用来分精准班次还是扩招班次
					ExtraInfo:                 i.ExtraInfo,
				})
			}
		}
	}

	sort.SliceStable(res, func(i, j int) bool {
		if res[i].ShiftType == res[j].ShiftType {
			return res[i].DepartureTime < res[j].DepartureTime
		}
		return res[i].ShiftType <= res[j].ShiftType
	})
	return res
}

func sortShift(res []models.SkuInfo) []models.SkuInfo {
	var (
		orderRes  = make([]models.SkuInfo, 0, len(res))
		accurate  = make([]models.SkuInfo, 0)
		recommend = make([]models.SkuInfo, 0)
	)
	for _, val := range res {
		if val.ShiftType == AccuRate {
			accurate = append(accurate, val)
		} else {
			recommend = append(recommend, val)
		}
	}
	sort.SliceStable(accurate, func(i, j int) bool {
		return accurate[i].DepartureTime < accurate[j].DepartureTime
	})
	orderRes = append(orderRes, accurate...)
	sort.SliceStable(recommend, func(i, j int) bool {
		return recommend[i].DepartureTime < recommend[j].DepartureTime
	})
	orderRes = append(orderRes, recommend...)
	return orderRes
}

func sortShiftV2(res []models.SkuInfo) []models.SkuInfo {
	var (
		orderRes  = make([]models.SkuInfo, 0, len(res))
		accurate  = make([]models.SkuInfo, 0)
		recommend = make([]models.SkuInfo, 0)
	)
	for _, val := range res {
		if val.ShiftType == AccuRate {
			accurate = append(accurate, val)
		} else {
			recommend = append(recommend, val)
		}
	}
	orderRes = append(orderRes, accurate...)
	orderRes = append(orderRes, recommend...)
	return orderRes
}

func initSkuInfoV2(ctx context.Context, sortedShiftList []*CarpoolOpenApi.ShiftInventory, m *MutliStationInventory, res []models.SkuInfo) []models.SkuInfo {
	for _, i := range sortedShiftList {
		var routeID int64
		if i != nil && i.RouteGroup != nil {
			routeID = *i.RouteGroup
		}
		var productId int32
		if i != nil && i.RouteGroup != nil {
			productId = *i.ProductID
		}

		if m.FenceBusAppversionFilter(ctx, productId, i) {
			continue
		}
		res = append(res, models.SkuInfo{
			RouteGroup:                routeID,
			ProductID:                 productId,
			DepartureTime:             i.DepartureTime,
			RemainSeats:               i.RemainSeats,
			ShiftID:                   i.ShiftID,
			StartStationID:            *i.SrcRegion.StationID,
			EndStationID:              *i.DestRegion.StationID,
			SrcCost:                   i.SrcCost,
			DestCost:                  i.DestCost,
			CarryChildrenMaxInventory: util.String2int32(ctx, i.ExtraInfo["remain_carry_child_num"]),
			ShiftType:                 util.String2int32(ctx, i.ExtraInfo["shift_type"]), // 用来分精准班次还是扩招班次
			ExtraInfo:                 i.ExtraInfo,
		})
	}
	res = sortShiftV2(res)
	return res
}

func initSkuInfo(ctx context.Context, routeShiftInfoList []*CarpoolOpenApi.RouteShiftInventoryInfo, routeId int32, m *MutliStationInventory, res []models.SkuInfo) []models.SkuInfo {
	for _, v := range routeShiftInfoList {
		if v == nil || len(v.ShiftInventoryList) == 0 {
			continue
		}
		if routeId == 0 || routeId == int32(v.RouteGroup) {
			for _, i := range v.ShiftInventoryList {
				if m.FenceBusAppversionFilter(ctx, v.ProductID, i) {
					continue
				}
				res = append(res, models.SkuInfo{
					RouteGroup:                v.RouteGroup,
					ProductID:                 v.ProductID,
					DepartureTime:             i.DepartureTime,
					RemainSeats:               i.RemainSeats,
					ShiftID:                   i.ShiftID,
					StartStationID:            *i.SrcRegion.StationID,
					EndStationID:              *i.DestRegion.StationID,
					SrcCost:                   i.SrcCost,
					DestCost:                  i.DestCost,
					CarryChildrenMaxInventory: util.String2int32(ctx, i.ExtraInfo["remain_carry_child_num"]),
					ShiftType:                 util.String2int32(ctx, i.ExtraInfo["shift_type"]), // 用来分精准班次还是扩招班次
					ExtraInfo:                 i.ExtraInfo,
				})
			}
		}
	}
	res = sortShift(res)
	return res
}

func (m *MutliStationInventory) getMatchedSkuInfoByShiftId(ctx context.Context, routeShiftInfoList []*CarpoolOpenApi.RouteShiftInventoryInfo, res []models.SkuInfo) []models.SkuInfo {
	matchRouteId := int64(0)
	//1、根据用户选择的班次id获取对应的路线id
	for _, v := range routeShiftInfoList {
		if v == nil || len(v.ShiftInventoryList) == 0 {
			continue
		}
		for _, i := range v.ShiftInventoryList {
			//匹配唯一的班次
			if m.baseReq.CommonBizInfo.BusServiceShiftId == i.ShiftID {
				matchRouteId = v.RouteGroup
			}
		}
	}
	//2、根据路线id获取该路线所有的班次列表
	for _, v := range routeShiftInfoList {
		if v == nil || len(v.ShiftInventoryList) == 0 {
			continue
		}
		if v.RouteGroup == matchRouteId {
			for _, i := range v.ShiftInventoryList {
				res = append(res, models.SkuInfo{
					RouteGroup:                v.RouteGroup,
					ProductID:                 v.ProductID,
					DepartureTime:             i.DepartureTime,
					RemainSeats:               i.RemainSeats,
					ShiftID:                   i.ShiftID,
					StartStationID:            *i.SrcRegion.StationID,
					EndStationID:              *i.DestRegion.StationID,
					CarryChildrenMaxInventory: util.String2int32(ctx, i.ExtraInfo["remain_carry_child_num"]),
				})
			}
		}
	}
	return res
}
func (m *MutliStationInventory) buildShiftBriefInfoList() []*CarpoolOpenApi.ShiftBriefInfo {
	//pbd渠道大巴根据班次过滤
	if m.isPbdBusStationBus() && m.baseReq.CommonBizInfo.BusServiceShiftId != "" {
		shiftBriefInfoList := make([]*CarpoolOpenApi.ShiftBriefInfo, 0)
		shiftBriefInfo := new(CarpoolOpenApi.ShiftBriefInfo)
		shiftBriefInfo.ShiftID = m.baseReq.CommonBizInfo.BusServiceShiftId
		shiftBriefInfo.RouteGroup = &m.baseReq.CommonBizInfo.RouteId
		shiftBriefInfoList = append(shiftBriefInfoList, shiftBriefInfo)
		return shiftBriefInfoList
	}
	return nil
}

func (m *MutliStationInventory) buildIsSort(ctx context.Context) *int32 {
	//pbd渠道大巴根据班次过滤
	sortType := int32(0) // 或直接返回m.baseReq.CommonInfo.SortType的值
	if m.isPbdBusStationBus() {
		return &sortType
	}
	if m.baseReq.CommonInfo.Channel == ChannelHonghuCheckBackground || m.baseReq.CommonInfo.Channel == ChannelHonghuPhoneCall {
		return &sortType
	}

	if m.baseReq.CommonInfo.SortType == nil {
		return &sortType
	}
	return m.baseReq.CommonInfo.SortType
}

func (m *MutliStationInventory) buildExtraInfo(ctx context.Context) map[string]string {
	extraInfo := make(map[string]string)
	//pbd渠道大巴处理扩展参数
	if m.isPbdBusStationBus() {
		extraInfo["channel_list"] = cast.ToString(m.baseReq.CommonInfo.Channel)
		if m.baseReq != nil && m.baseReq.CommonBizInfo.StationInfo.RouteId != 0 {
			extraInfo["routes"] = util.Int32String(m.baseReq.CommonBizInfo.StationInfo.RouteId)
		}
		return extraInfo
	}
	//检票代发单来源跳过停售时间过滤
	if m.baseReq.CommonInfo.AccessKeyID == AccessKeyIdHonghu && m.baseReq.CommonInfo.Channel == ChannelHonghuCheckBackground {
		extraInfo["skip_stop_sell_time"] = "1"
	}
	// 班次是否扩召回
	if m.checkNeedRecall(ctx) {
		// 进行班次扩召回，1 扩召回；0 不扩召回
		extraInfo["need_station_recall"] = "1"
	}

	// 线路推荐｜ 线路码 如果有的话则传参
	if m.baseReq != nil && m.baseReq.CommonBizInfo.StationInfo.RouteId != 0 {
		extraInfo["routes"] = util.Int32String(m.baseReq.CommonBizInfo.StationInfo.RouteId)
	}

	if len(m.baseReq.CommonInfo.Dchn) > 0 {
		extraInfo["dchn"] = m.baseReq.CommonInfo.Dchn
	}

	return extraInfo
}
func (m *MutliStationInventory) isPbdBusStationBus() bool {
	if m.baseReq.CommonInfo.SourceID == source_id.SourceIDPbdStationBus {
		return true
	}
	return false
}

func (m *MutliStationInventory) checkNeedRecall(ctx context.Context) bool {
	// 总灰度控制，开量验证后可删除总灰度控制逻辑
	apolloParams := map[string]string{
		"pid":           util.ToString(m.baseReq.PassengerInfo.PID),
		"city":          util.ToString(m.baseReq.CommonBizInfo.StationInfo.StartCity),
		"route_id":      util.ToString(m.baseReq.CommonBizInfo.StationInfo.RouteId),
		"access_key_id": util.ToString(m.baseReq.CommonInfo.AccessKeyID),
	}

	if !Apollo2.FeatureToggle(ctx, "gs_recall", m.baseReq.PassengerInfo.Phone, apolloParams) {
		return false
	}

	// 根据access_key_id控制是否扩召回, 在此里面的值不需要扩招
	return !Apollo2.FeatureToggle(ctx, "gs_porsche_station_merge", m.baseReq.PassengerInfo.Phone, apolloParams)
}
