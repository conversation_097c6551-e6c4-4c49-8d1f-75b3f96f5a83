package rpc_process

import (
	"context"
	"strconv"

	CarpoolOpenApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolOpenApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/carpool_open_api"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	legoTrace "git.xiaojukeji.com/lego/context-go"
	"github.com/spf13/cast"
)

// DuseBusShiftProcessor DUSE班次逻辑处理器
// 对应PHP中的StationBusInfoLogic，用于获取和处理站点巴士推荐班次信息
type DuseBusShiftProcessor struct {
	baseReq          *models.BaseReqData // 请求信息
	hasBusProducts   bool                // 是否有站点巴士品类
	busRecommendInfo *BusRecommendInfo   // 巴士推荐信息
}

// BusRecommendInfo 巴士推荐信息
type BusRecommendInfo struct {
	ShiftID             string                 // 班次ID
	OnStation           string                 // 上车站点
	OffStation          string                 // 下车站点
	RouteGroup          int64                  // 路线组合ID
	ProductID           int64                  // 产品ID
	DepartureTime       int64                  // 出发时间
	ExtraInfo           map[string]interface{} // 额外信息
	RemainSeats         int32                  // 剩余座位数
	OptimalShift        bool                   // 是否是最优班次
	OptimalShiftDist    int32                  // 最优班次距离
	RemainCarryChildNum string                 // 剩余携童数量
	MaxInventory        int32                  // 最大库存
	ExpGroup            int32                  // 实验组
}

// NewDuseBusShiftProcessor 创建DUSE班次逻辑处理器
func NewDuseBusShiftProcessor(ctx context.Context, req *models.BaseReqData) *DuseBusShiftProcessor {
	return &DuseBusShiftProcessor{
		baseReq:        req,
		hasBusProducts: false,
	}
}

// GetErrorInfo 获取rpc错误信息
func (d *DuseBusShiftProcessor) GetErrorInfo(ctx context.Context) error {
	return nil
}

// Fetch 获取/加载巴士推荐班次信息
func (d *DuseBusShiftProcessor) Fetch(ctx context.Context, products []*models.Product) bool {
	const logTag = "duse_bus_shift_processor"

	// 检查是否有站点巴士品类产品
	busPcList := d.getBusProductList(products)
	if len(busPcList) == 0 {
		log.Trace.Infof(ctx, logTag, "no bus products found")
		return true
	}

	d.hasBusProducts = true

	// 构建请求参数
	req := d.buildBusBubbleRecommendRequest(busPcList)
	if req == nil {
		log.Trace.Warnf(ctx, logTag, "failed to build bus bubble recommend request")
		return false
	}

	// 调用 CarpoolOpenApi.BusBubbleRecommendShift
	resp, err := d.callBusBubbleRecommendShift(ctx, req)
	if err != nil || resp == nil {
		log.Trace.Warnf(ctx, logTag, "bus bubble recommend shift failed: %v", err)
		return false
	}

	// 解析响应
	d.busRecommendInfo = d.parseBusRecommendResponse(resp)

	return true
}

// getBusProductList 获取站点巴士产品列表
func (d *DuseBusShiftProcessor) getBusProductList(products []*models.Product) []string {
	var busPcList []string

	for _, product := range products {
		if product != nil && product.CarpoolType == consts.CarPoolTypeInterCityStation {
			busPcList = append(busPcList, strconv.FormatInt(product.ProductID, 10))
		}
	}

	return busPcList
}

// buildBusBubbleRecommendRequest 构建巴士推荐请求
func (d *DuseBusShiftProcessor) buildBusBubbleRecommendRequest(busPcList []string) map[string]interface{} {
	if d.baseReq == nil {
		return nil
	}

	// 构建from和to坐标点
	fromGeoPoint := map[string]float64{
		"lng": d.baseReq.AreaInfo.FromLng,
		"lat": d.baseReq.AreaInfo.FromLat,
	}

	toGeoPoint := map[string]float64{
		"lng": d.baseReq.AreaInfo.ToLng,
		"lat": d.baseReq.AreaInfo.ToLat,
	}

	// 构建请求参数
	params := map[string]interface{}{
		"passenger_id":    strconv.FormatInt(d.baseReq.PassengerInfo.PID, 10),
		"city_id":         d.baseReq.AreaInfo.Area,
		"product_id_list": busPcList,
		"from_xy":         fromGeoPoint,
		"to_xy":           toGeoPoint,
	}

	return params
}

// callBusBubbleRecommendShift 调用巴士推荐班次接口
func (d *DuseBusShiftProcessor) callBusBubbleRecommendShift(ctx context.Context, params map[string]interface{}) (*CarpoolOpenApi.BusBubbleRecommendResponse, error) {
	// 构建BusBubbleRecommendRequest
	req := &CarpoolOpenApi.BusBubbleRecommendRequest{}

	// 设置乘客ID
	if passengerID, ok := params["passenger_id"].(string); ok {
		req.PassengerID = passengerID
	}

	// 设置城市ID
	if cityID, ok := params["city_id"]; ok {
		cityIDInt32 := cast.ToInt32(cityID)
		req.CityID = cityIDInt32
	}

	// 设置产品ID列表
	if productIDList, ok := params["product_id_list"].([]string); ok {
		req.ProductIDList = productIDList
	}

	// 构建地理坐标
	if fromXY, ok := params["from_xy"].(map[string]float64); ok {
		req.FromXy = &CarpoolOpenApi.GeoPoint{
			Lng: fromXY["lng"],
			Lat: fromXY["lat"],
		}
	}

	if toXY, ok := params["to_xy"].(map[string]float64); ok {
		req.ToXy = &CarpoolOpenApi.GeoPoint{
			Lng: toXY["lng"],
			Lat: toXY["lat"],
		}
	}

	// 构建trace信息
	trace := d.buildTrace(ctx)

	// 调用实际的RPC接口
	return carpool_open_api.BusBubbleRecommendShift(ctx, req, trace)
}

// buildTrace 构建trace信息
func (d *DuseBusShiftProcessor) buildTrace(ctx context.Context) *CarpoolOpenApi.Trace {
	if tracer, ok := legoTrace.GetCtxTrace(ctx); ok {
		hintCode := cast.ToInt64(tracer.HintCode)
		return &CarpoolOpenApi.Trace{
			LogId:       tracer.TraceId,
			Caller:      tracer.CallerFunc,
			SpanId:      &tracer.SpanId,
			HintContent: &tracer.HintContent,
			HintCode:    &hintCode,
		}
	}
	return nil
}

// parseBusRecommendResponse 解析巴士推荐响应
func (d *DuseBusShiftProcessor) parseBusRecommendResponse(resp *CarpoolOpenApi.BusBubbleRecommendResponse) *BusRecommendInfo {
	if resp == nil || resp.RetCode != 0 {
		return nil
	}

	// 解析第一个推荐信息（如果有的话）
	if resp.Data != nil && len(resp.Data.RecommendInfoList) > 0 {
		info := resp.Data.RecommendInfoList[0]

		busInfo := &BusRecommendInfo{
			ShiftID:       info.ShiftID,
			OnStation:     getStringValue(info.OnStation),
			OffStation:    getStringValue(info.OffStation),
			RouteGroup:    info.RouteGroup,
			ProductID:     cast.ToInt64(info.ProductID),
			DepartureTime: info.DepartureTime,
		}

		// 解析额外信息
		if info.ExtraInfo != nil {
			busInfo.ExtraInfo = make(map[string]interface{})
			for k, v := range info.ExtraInfo {
				busInfo.ExtraInfo[k] = v
			}
		}

		return busInfo
	}

	return nil
}

// getStringValue 安全获取字符串值
func getStringValue(ptr interface{}) string {
	if ptr == nil {
		return ""
	}
	if strPtr, ok := ptr.(*string); ok && strPtr != nil {
		return *strPtr
	}
	if str, ok := ptr.(string); ok {
		return str
	}
	return ""
}

// validateRecommendInfo 验证推荐信息完整性
func (d *DuseBusShiftProcessor) validateRecommendInfo(info map[string]interface{}) bool {
	requiredFields := []string{"on_station", "off_station", "shift_id", "route_group", "product_id", "departure_time"}

	for _, field := range requiredFields {
		if _, exists := info[field]; !exists {
			return false
		}
	}

	return true
}

// buildBusRecommendInfo 构建巴士推荐信息
func (d *DuseBusShiftProcessor) buildBusRecommendInfo(info map[string]interface{}) *BusRecommendInfo {
	extraInfo, _ := info["extra_info"].(map[string]interface{})

	busInfo := &BusRecommendInfo{
		ShiftID:       cast.ToString(info["shift_id"]),
		OnStation:     cast.ToString(info["on_station"]),
		OffStation:    cast.ToString(info["off_station"]),
		RouteGroup:    cast.ToInt64(info["route_group"]),
		ProductID:     cast.ToInt64(info["product_id"]),
		DepartureTime: cast.ToInt64(info["departure_time"]),
		ExtraInfo:     extraInfo,
	}

	// 解析额外信息
	if extraInfo != nil {
		busInfo.OptimalShift = cast.ToInt(extraInfo["optimal_shift"]) == 1
		busInfo.OptimalShiftDist = cast.ToInt32(extraInfo["optimal_shift_dist"])
		busInfo.RemainCarryChildNum = cast.ToString(extraInfo["remain_carry_child_num"])
		busInfo.MaxInventory = cast.ToInt32(extraInfo["remain_seats"])
		busInfo.ExpGroup = cast.ToInt32(extraInfo["exp_group"])
	}

	return busInfo
}

// BuildCommonBizInfo RPC 构建公共业务信息
func (d *DuseBusShiftProcessor) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	// DUSE班次处理器主要处理产品级别的信息，公共信息无需特殊处理
}

// BuildProductBizInfo RPC 构建产品业务信息
func (d *DuseBusShiftProcessor) BuildProductBizInfo(ctx context.Context, product models.Product,
	privateBizInfo *models.PrivateBizInfo) {

	if !d.hasBusProducts || d.busRecommendInfo == nil {
		return
	}

	// 只处理城际站点巴士品类
	if product.CarpoolType != consts.CarPoolTypeInterCityStation {
		return
	}

	// 检查产品是否匹配推荐信息，如果匹配则重写产品信息
	if product.ProductID == d.busRecommendInfo.ProductID {
		d.rewriteProduct(ctx, &product)
	}
}

// Exec 实现BeforePriceProductsFilter接口，处理rewrite逻辑
func (d *DuseBusShiftProcessor) Exec(ctx context.Context, products []*models.Product) []models.ProductCategory {
	const logTag = "duse_bus_shift_rewrite"
	var toRemove []models.ProductCategory

	// 如果没有站点巴士品类，直接返回
	if !d.hasBusProducts {
		log.Trace.Infof(ctx, logTag, "no bus products, skip rewrite")
		return toRemove
	}

	// 遍历所有产品进行rewrite处理
	for _, product := range products {
		if product.CarpoolType != consts.CarPoolTypeInterCityStation {
			continue // 只处理站点巴士类型的产品
		}

		// 如果没有获取到巴士推荐信息，过滤掉所有站点巴士产品
		if d.busRecommendInfo == nil {
			log.Trace.Infof(ctx, logTag, "no bus recommend info, filter product=%d", product.ProductCategory)
			toRemove = append(toRemove, models.ProductCategory(product.ProductCategory))
			continue
		}

		// 如果产品ID不匹配推荐信息，过滤掉该产品
		if product.ProductID != d.busRecommendInfo.ProductID {
			log.Trace.Infof(ctx, logTag, "product id not match, filter product=%d, expect=%d",
				product.ProductCategory, d.busRecommendInfo.ProductID)
			toRemove = append(toRemove, models.ProductCategory(product.ProductCategory))
			continue
		}

		// 如果匹配，则重写产品信息
		log.Trace.Infof(ctx, logTag, "rewrite product=%d with bus info", product.ProductCategory)
		d.rewriteProduct(ctx, product)
	}

	return toRemove
}

// rewriteProduct 重写产品信息，对应PHP中的rewrite方法
func (d *DuseBusShiftProcessor) rewriteProduct(ctx context.Context, product *models.Product) {
	if d.busRecommendInfo == nil {
		return
	}

	const logTag = "duse_bus_shift_rewrite"

	// 设置订单类型和基本信息，对应PHP代码：
	// $oOrder->iOrderType     = 1;
	// $oOrder->iComboId       = $oBusInfo['route_group'];
	// $oOrder->iDepartureTime = $oBusInfo['departure_time'];
	product.OrderType = 1
	product.BizInfo.ComboID = d.busRecommendInfo.RouteGroup
	product.BizInfo.DepartureTime = d.busRecommendInfo.DepartureTime

	// 重写请求信息，对应PHP中的rewriteRequest方法
	d.rewriteRequest(ctx, product)

	// 设置大巴班次信息，对应PHP代码中的 $oOrder->aBusShiftInfo
	if product.BizInfo.IntercityData.StationInventoryInfo == nil {
		product.BizInfo.IntercityData.StationInventoryInfo = &models.StationInventoryInfo{}
	}

	// 从ExtraInfo中提取信息
	optimalShift := 0
	optimalShiftDist := 0
	remainCarryChildNum := "0"
	maxInventory := int32(5)
	expGroup := 0

	if d.busRecommendInfo.ExtraInfo != nil {
		if val, ok := d.busRecommendInfo.ExtraInfo["optimal_shift"]; ok {
			optimalShift = cast.ToInt(val)
		}
		if val, ok := d.busRecommendInfo.ExtraInfo["optimal_shift_dist"]; ok {
			optimalShiftDist = cast.ToInt(val)
		}
		if val, ok := d.busRecommendInfo.ExtraInfo["remain_carry_child_num"]; ok {
			remainCarryChildNum = cast.ToString(val)
		}
		if val, ok := d.busRecommendInfo.ExtraInfo["remain_seats"]; ok {
			maxInventory = cast.ToInt32(val)
		}
		if val, ok := d.busRecommendInfo.ExtraInfo["exp_group"]; ok {
			expGroup = cast.ToInt(val)
		}
	}

	// 设置选择信息，对应PHP中的aBusShiftInfo数组
	product.BizInfo.IntercityData.StationInventoryInfo.SelectInfo = models.StationInventorySelectInfo{
		ShiftID:                   d.busRecommendInfo.ShiftID,
		RouteId:                   d.busRecommendInfo.RouteGroup,
		DepartureTime:             d.busRecommendInfo.DepartureTime,
		RemainSeats:               maxInventory,
		CarryChildrenMaxInventory: maxInventory,
		// 注意：以下字段需要存储在ExtraInfo中，因为结构体中没有对应字段
		ExtraInfo: map[string]string{
			"is_best_shift":          cast.ToString(optimalShift),
			"optimal_shift_dist":     cast.ToString(optimalShiftDist),
			"remain_carry_child_num": remainCarryChildNum,
			"max_inventory":          cast.ToString(maxInventory),
			"exp_group":              cast.ToString(expGroup),
		},
	}

	log.Trace.Infof(ctx, logTag, "rewritten product=%d with shift_id=%s, route_group=%d, departure_time=%d",
		product.ProductCategory, d.busRecommendInfo.ShiftID, d.busRecommendInfo.RouteGroup, d.busRecommendInfo.DepartureTime)
}

// rewriteRequest 重写请求信息，对应PHP中的rewriteRequest方法
func (d *DuseBusShiftProcessor) rewriteRequest(ctx context.Context, product *models.Product) {
	if d.busRecommendInfo == nil || d.baseReq == nil {
		return
	}

	const logTag = "duse_bus_shift_rewrite_request"

	// 提取上车站点坐标，对应PHP代码：
	// $oReq->setFromLat($oBusInfo['on_station']['xy']['lat']);
	// $oReq->setFromLng($oBusInfo['on_station']['xy']['lng']);
	if onStationXY := d.extractStationCoordinates(d.busRecommendInfo.OnStation); onStationXY != nil {
		d.baseReq.AreaInfo.FromLat = onStationXY.Lat
		d.baseReq.AreaInfo.FromLng = onStationXY.Lng
		log.Trace.Infof(ctx, logTag, "updated from coordinates: lat=%f, lng=%f", onStationXY.Lat, onStationXY.Lng)
	}

	// 提取下车站点坐标，对应PHP代码：
	// $oReq->setToLat($oBusInfo['off_station']['xy']['lat']);
	// $oReq->setToLng($oBusInfo['off_station']['xy']['lng']);
	if offStationXY := d.extractStationCoordinates(d.busRecommendInfo.OffStation); offStationXY != nil {
		d.baseReq.AreaInfo.ToLat = offStationXY.Lat
		d.baseReq.AreaInfo.ToLng = offStationXY.Lng
		log.Trace.Infof(ctx, logTag, "updated to coordinates: lat=%f, lng=%f", offStationXY.Lat, offStationXY.Lng)
	}

	// 设置其他请求参数，对应PHP代码：
	// $oReq->setOrderType(1);
	// $oReq->setDepartureTime($oBusInfo['departure_time']);
	// $oReq->setPreferredRouteId($oBusInfo['route_group']);

	// 注意：这些信息已经在rewriteProduct中设置了，这里主要是更新坐标信息
	log.Trace.Infof(ctx, logTag, "rewritten request for product=%d, order_type=1, departure_time=%d, route_group=%d",
		product.ProductCategory, d.busRecommendInfo.DepartureTime, d.busRecommendInfo.RouteGroup)
}

// extractStationCoordinates 从站点信息中提取坐标
func (d *DuseBusShiftProcessor) extractStationCoordinates(stationInfo string) *struct{ Lat, Lng float64 } {
	// 这里需要根据实际的站点信息格式来解析坐标
	// 如果stationInfo是JSON格式，可以使用json.Unmarshal
	// 目前先返回nil，实际使用时需要根据数据格式来实现
	// TODO: 实现具体的坐标提取逻辑
	return nil
}
