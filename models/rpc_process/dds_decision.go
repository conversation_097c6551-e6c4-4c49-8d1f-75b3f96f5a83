package rpc_process

import (
	"context"
	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/decision"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"
)

const DecisionTypeDefault = 1

type DdsDecision struct {
	decisionReq  *EstimateDecision.DecisionRequest
	decisionResp []*EstimateDecision.ProductDecisionItem
}

func NewDdsDecision(ctx context.Context, commonInfo *models.CommonInfo, areaInfo *models.AreaInfo, passengerInfo *models.PassengerInfo) *DdsDecision {
	userInfo := &EstimateDecision.UserInfo{
		Id:           passengerInfo.PID,
		Uid:          passengerInfo.UID,
		Pid:          passengerInfo.PID,
		Phone:        passengerInfo.Phone,
		CallCarPhone: util.StringPtr(commonInfo.CallCarPhone),
	}

	common := &EstimateDecision.CommonInfo{
		StartLat:        areaInfo.Lat,
		StartLng:        areaInfo.Lng,
		ToLat:           areaInfo.ToLat,
		ToLng:           areaInfo.ToLng,
		ClientType:      cast.ToInt64(commonInfo.ClientType),
		City:            cast.ToString(areaInfo.Area),
		MenuId:          commonInfo.MenuID,
		County:          cast.ToInt64(areaInfo.FromCounty),
		AppVersion:      util.StringPtr(commonInfo.AppVersion),
		Channel:         util.StringPtr(cast.ToString(commonInfo.Channel)),
		OrderType:       util.Int32Ptr(commonInfo.OrderType),
		MapType:         util.StringPtr(areaInfo.MapType),
		EstimateTraceId: util.GetTraceIDFromCtxWithoutCheck(ctx),
		PageType:        commonInfo.PageType,
		Lang:            commonInfo.Lang,
		AccessKeyId:     util.Int32Ptr(commonInfo.AccessKeyID),
		ToCity:          util.StringPtr(cast.ToString(areaInfo.ToArea)),
		TabId:           util.StringPtr(commonInfo.TabId),
	}

	req := &EstimateDecision.DecisionRequest{
		User:         userInfo,
		Common:       common,
		DecisionType: util.IntToInt64Ptr(DecisionTypeDefault),
	}
	return &DdsDecision{req, nil}
}

// 获取rpc错误信息
func (rp *DdsDecision) GetErrorInfo(ctx context.Context) error {
	return nil
}

func (rp *DdsDecision) Fetch(ctx context.Context, productInfos []*biz_runtime.ProductInfoFull) bool {
	// todo: 差carpool_long_order
	productInfoMap := make(map[int64]*biz_runtime.ProductInfoFull)
	var products []*EstimateDecision.ProductInfo
	var preferenceProduct []*EstimateDecision.ProductCell
	// 预先构建
	for _, p := range productInfos {
		productInfoMap[p.GetProductCategory()] = p
	}

	for _, p := range productInfos {
		var spOpen int64
		var taxiSpOpen int64
		var multiRequireProduct []*EstimateDecision.ProductCell

		if extraMap := p.GetBillExtraMap(); extraMap != nil {
			spOpen = cast.ToInt64(extraMap["sp_open"])
		}
		taxiSpsDiscountFee := p.GetBillDetail().TaxiSpDiscountFee
		if taxiSpsDiscountFee > 0 {
			taxiSpOpen = 1
		}

		for _, requireProduct := range p.GetCommonBizInfo().MultiRequireProduct {
			productInfo := productInfoMap[requireProduct.ProductCategory]
			multiRequireProduct = append(multiRequireProduct, &EstimateDecision.ProductCell{
				ProductId:          productInfo.GetProductId(),
				BusinessId:         productInfo.GetBusinessID(),
				RequireLevel:       cast.ToInt64(productInfo.GetRequireLevel()),
				ComboType:          productInfo.GetComboType(),
				CarpoolType:        productInfo.GetCarpoolType(),
				MenuId:             productInfo.GetMenuId(),
				RouteType:          productInfo.GetRouteType(),
				IsDualCarpoolPrice: productInfo.GetIsDualCarpoolPrice(),
				IsSpecialPrice:     productInfo.GetIsSpecialPrice(),
				ProductCategory:    productInfo.GetProductCategory(),
				CarpoolPriceType:   productInfo.GetCarpoolPriceType(),
				LevelType:          productInfo.GetLevelType(),
				CarpoolLongOrder:   0, // todo: check下出处
			})
		}

		products = append(products, &EstimateDecision.ProductInfo{
			ProductId:          p.GetProductId(),
			BusinessId:         p.GetBusinessID(),
			RequireLevel:       cast.ToInt64(p.GetRequireLevel()),
			ComboType:          p.GetComboType(),
			PreTotalFee:        cast.ToInt64(p.GetPreTotalFee()),
			EstimateFee:        p.GetEstimateFee(),
			IsSpecialPrice:     p.GetIsSpecialPrice(),
			RouteType:          p.GetRouteType(),
			IsDualCarpoolPrice: p.GetIsDualCarpoolPrice(),
			EstimateId:         p.GetEstimateID(),
			AthenaCapability: &EstimateDecision.AthenaCapability{
				SpCapability:            1,
				UnioneYouxuanCapability: 1,
				AplusFlashCapability:    1,
				SpFlashDisabledType:     0,
			},
			PriceCapability: &EstimateDecision.PriceCapability{
				SpOpen: spOpen,
				SpRate: 1,
			},
			TaxiSpDiscountFeeInfo: &EstimateDecision.TaxiSpDiscountFeeInfo{
				SpOpen:            taxiSpOpen,
				TaxiSpDiscountFee: cast.ToInt32(taxiSpsDiscountFee),
			},
			CarpoolType:         p.GetCarpoolType(),
			DriverMetre:         p.GetBillDriverMetre(),
			CarpoolPriceType:    cast.ToInt16(p.GetCarpoolPriceType()),
			MultiRequireProduct: nil,
			ProductCategory:     p.GetProductCategory(),
			LevelType:           p.GetLevelType(),
		})
	}

	for _, requireProduct := range productInfos[0].GetCommonBizInfo().MultiRequireProduct {
		productInfo := productInfoMap[requireProduct.ProductCategory]
		preferenceProduct = append(preferenceProduct, &EstimateDecision.ProductCell{
			ProductId:          productInfo.GetProductId(),
			BusinessId:         productInfo.GetBusinessID(),
			RequireLevel:       cast.ToInt64(productInfo.GetRequireLevel()),
			ComboType:          productInfo.GetComboType(),
			CarpoolType:        productInfo.GetCarpoolType(),
			MenuId:             productInfo.GetMenuId(),
			RouteType:          productInfo.GetRouteType(),
			IsDualCarpoolPrice: productInfo.GetIsDualCarpoolPrice(),
			IsSpecialPrice:     productInfo.GetIsSpecialPrice(),
			ProductCategory:    productInfo.GetProductCategory(),
			CarpoolPriceType:   productInfo.GetCarpoolPriceType(),
			LevelType:          productInfo.GetLevelType(),
		})
	}

	rp.decisionReq.Products = products
	rp.decisionReq.PreferenceProduct = preferenceProduct
	resp, err := decision.GetDDSDecision(ctx, rp.decisionReq)
	if resp == nil || err != nil {
		return false
	}

	if resp.ProductDecisions == nil {
		return false
	}

	rp.decisionResp = resp.ProductList
	return true
}

func (rp *DdsDecision) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
	selectTypeMap := make(map[int64]bool)
	for _, p := range rp.decisionResp {
		if p.IsSelected != 0 {
			selectTypeMap[p.ProductCell.ProductCategory] = true
		}
	}

	singleProductsSort := make([]int64, len(rp.decisionResp))
	for i, product := range rp.decisionResp {
		singleProductsSort[i] = product.ProductCell.ProductCategory
	}

	info.SelectTypeMap = selectTypeMap
	info.SingleProductsSort = singleProductsSort
	return
}

func (rp *DdsDecision) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	return
}

func (rp *DdsDecision) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) []models.ProductCategory {
	var removedProducts []models.ProductCategory
	for _, product := range rp.decisionResp {
		if product.RemoveFlag == true {
			removedProducts = append(removedProducts, models.ProductCategory(product.ProductCell.ProductCategory))
		}
	}

	return removedProducts
}
