package rpc_process

import (
	"context"
	CarpoolOpenApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolOpenApi"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	. "github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
	"testing"
)

func Test_InitSkuInfoV2(t *testing.T) {
	sortedShiftList := make([]*CarpoolOpenApi.ShiftInventory, 0)
	var routeGroup int64
	var productID int32
	var stationId int64
	shift := &CarpoolOpenApi.ShiftInventory{
		DepartureTime: 123456,
		ShiftID:       "1234",
		SrcRegion: &CarpoolOpenApi.BusRegion{
			StationID: &stationId,
		},
		DestRegion: &CarpoolOpenApi.BusRegion{
			StationID: &stationId,
		},
		SrcCost:    &CarpoolOpenApi.StationRouteCost{},
		DestCost:   &CarpoolOpenApi.StationRouteCost{},
		RouteGroup: &routeGroup,
		ProductID:  &productID,
		ExtraInfo:  make(map[string]string),
	}
	sortedShiftList = append(sortedShiftList, shift)
	m := &MutliStationInventory{}
	res := make([]models.SkuInfo, 0)
	PatchConvey("Test_InitSkuInfoV2", t, func() {
		Convey("Case1: FenceBusAppversionFilter false", func() {
			mockFenceBusAppversionFilter := Mock(m.FenceBusAppversionFilter).Return(false).Build()
			defer mockFenceBusAppversionFilter.UnPatch()
			var context context.Context
			initSkuInfoV2(context, sortedShiftList, m, res)
		})

		Convey("Case2: FenceBusAppversionFilter true", func() {
			mockFenceBusAppversionFilter := Mock(m.FenceBusAppversionFilter).Return(true).Build()
			defer mockFenceBusAppversionFilter.UnPatch()
			var context context.Context
			initSkuInfoV2(context, sortedShiftList, m, res)
		})

	})
}

func Test_buildIsSort(t *testing.T) {
	PatchConvey("Test_buildIsSort", t, func() {
		Convey("Case1: isPbdBusStationBus true", func() {
			m := &MutliStationInventory{
				baseReq: &models.BaseReqData{
					CommonInfo: models.CommonInfo{
						Channel:  11,
						SourceID: 11,
					},
				},
			}
			var context context.Context
			m.buildIsSort(context)
		})

		Convey("Case2: ChannelHonghuCheckBackground", func() {
			channelHonghuCheckBackground := &MutliStationInventory{
				baseReq: &models.BaseReqData{
					CommonInfo: models.CommonInfo{
						Channel: 40082,
					},
				},
			}
			mockisPbdBusStationBus := Mock(channelHonghuCheckBackground.isPbdBusStationBus).Return(false).Build()
			defer mockisPbdBusStationBus.UnPatch()
			var context context.Context
			channelHonghuCheckBackground.buildIsSort(context)
		})

		Convey("Case3: checkApolloToggle(ctx) false", func() {
			var sortType int32
			m := &MutliStationInventory{
				baseReq: &models.BaseReqData{
					CommonInfo: models.CommonInfo{
						SortType: &sortType,
					},
				},
			}
			var context context.Context
			m.buildIsSort(context)
		})

		Convey("Case6: checkApolloToggle(ctx) true", func() {
			var sortType int32 = 1
			m := &MutliStationInventory{
				baseReq: &models.BaseReqData{
					CommonInfo: models.CommonInfo{
						SortType: &sortType,
					},
				},
			}
			var context context.Context
			m.buildIsSort(context)
		})

	})
}

func getAccurate() ([]models.SkuInfo, []models.SkuInfo) {
	var data = []models.SkuInfo{

		{
			ShiftID:       "22222",
			ShiftType:     0,
			DepartureTime: 22222,
		},
		{
			ShiftID:       "33333",
			ShiftType:     0,
			DepartureTime: 33333,
		},
		{
			ShiftID:       "11111",
			ShiftType:     0,
			DepartureTime: 11111,
		},
	}

	var res = []models.SkuInfo{
		{
			ShiftID:       "11111",
			ShiftType:     0,
			DepartureTime: 11111,
		},
		{
			ShiftID:       "22222",
			ShiftType:     0,
			DepartureTime: 22222,
		},
		{
			ShiftID:       "33333",
			ShiftType:     0,
			DepartureTime: 33333,
		},
	}

	return data, res
}

func getRecommend() ([]models.SkuInfo, []models.SkuInfo) {
	var data = []models.SkuInfo{

		{
			ShiftID:       "22222",
			ShiftType:     2,
			DepartureTime: 22222,
		},
		{
			ShiftID:       "33333",
			ShiftType:     3,
			DepartureTime: 33333,
		},
		{
			ShiftID:       "11111",
			ShiftType:     1,
			DepartureTime: 11111,
		},
	}

	var res = []models.SkuInfo{
		{
			ShiftID:       "11111",
			ShiftType:     1,
			DepartureTime: 11111,
		},
		{
			ShiftID:       "22222",
			ShiftType:     2,
			DepartureTime: 22222,
		},
		{
			ShiftID:       "33333",
			ShiftType:     3,
			DepartureTime: 33333,
		},
	}

	return data, res
}

func getMix() ([]models.SkuInfo, []models.SkuInfo) {
	var data = []models.SkuInfo{
		{
			ShiftID:       "33333",
			ShiftType:     2,
			DepartureTime: 33333,
		},
		{
			ShiftID:       "22222",
			ShiftType:     3,
			DepartureTime: 22222,
		},

		{
			ShiftID:       "11111",
			ShiftType:     0,
			DepartureTime: 11111,
		},
		{
			ShiftID:       "00000",
			ShiftType:     0,
			DepartureTime: 0,
		},
	}

	var res = []models.SkuInfo{
		{
			ShiftID:       "00000",
			ShiftType:     0,
			DepartureTime: 0,
		},
		{
			ShiftID:       "11111",
			ShiftType:     0,
			DepartureTime: 11111,
		},
		{
			ShiftID:       "22222",
			ShiftType:     3,
			DepartureTime: 22222,
		},
		{
			ShiftID:       "33333",
			ShiftType:     2,
			DepartureTime: 33333,
		},
	}

	return data, res
}

//
//func TestCheckParams(t *testing.T) {
//	mockey.PatchConvey("accurate", t, func() {
//		data, answer := getAccurate()
//		res := sortShift(data)
//		convey.So(res, convey.ShouldEqual, answer)
//	})
//	mockey.PatchConvey("recommend", t, func() {
//		data, answer := getRecommend()
//		res := sortShift(data)
//		convey.So(res, convey.ShouldEqual, answer)
//	})
//	mockey.PatchConvey("mix", t, func() {
//		data, answer := getMix()
//		res := sortShift(data)
//		convey.So(res, convey.ShouldEqual, answer)
//	})
//}
