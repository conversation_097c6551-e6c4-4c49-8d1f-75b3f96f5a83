package rpc_process

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/logic/guide_info/data/order_info"

	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/ufsClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/nuwa/trace"
)

type UfsAlwaysPoolFeature struct {
	Domain       string
	OrderID      string
	OrderKey     string
	AlwaysResult bool
}

func NewAlwaysPoolRPC(ctx context.Context, domain string, orderInfo *order_info.SimpleOrderInfo) *UfsAlwaysPoolFeature {
	return &UfsAlwaysPoolFeature{
		Domain:       domain,
		OrderID:      orderInfo.OrderId,
		OrderKey:     ufs.AlwaysPoolKey,
		AlwaysResult: false,
	}
}

func (uf *UfsAlwaysPoolFeature) Fetch(ctx context.Context) bool {
	var (
		// AlwaysCarpoolFeatures UFS特征
		AlwaysCarpoolFeatures = make([]ufsClient.Feature, 0)
	)

	// 构建拼友feature
	feature := ufsClient.Feature{
		Domain: uf.Domain,
		Keys:   []string{uf.OrderKey},
		Params: map[string]string{
			"order_id": uf.OrderID,
		},
	}

	AlwaysCarpoolFeatures = append(AlwaysCarpoolFeatures, feature)
	AlwaysCarpoolResp, err := ufs.MultiGetFeatures(ctx, AlwaysCarpoolFeatures, "")
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "Getting always carpool status failed with err %v", err)
		return false
	}

	for _, ufsResp := range AlwaysCarpoolResp {
		if ufsResp == nil || ufsResp.Errno != 0 {
			continue
		}
		//获得ufs结果
		if *ufsResp.Value == "1" {
			uf.AlwaysResult = true
		}
	}

	return true
}
