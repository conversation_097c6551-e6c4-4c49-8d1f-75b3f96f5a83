package rpc_process

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	Vcard "git.xiaojukeji.com/dirpc/dirpc-go-http-Vcard"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	vcardClient "git.xiaojukeji.com/gulfstream/mamba/dao/rpc/vcard"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type VcardInfo struct {
	MatchInfoDatas map[string]*Vcard.MatchInfoData
}

func NewVcardInfo(_ *models.CommonInfo, areaInfo *models.AreaInfo, passengerInfo *models.PassengerInfo) *VcardInfo {
	return &VcardInfo{}
}

func (rp *VcardInfo) Fetch(ctx context.Context, productFulls []*biz_runtime.ProductInfoFull) bool {
	estimateMap := make(map[string]*Vcard.EstimateInfo)
	estimateId := ""
	for _, productFull := range productFulls {
		if !carpool.IsPinCheCheV2(productFull.Product.ProductCategory, productFull.Product.CarpoolPriceType) {
			continue
		}
		estimateId = productFull.Product.EstimateID
		if estimateId == "" {
			continue
		}

		estimateMap[estimateId] = &Vcard.EstimateInfo{
			PreTotalFee: productFull.GetPreTotalFee(),
			DriverMetre: productFull.GetBillDriverMetre(),
		}
	}

	if len(estimateMap) == 0 {
		return true
	}

	resp, err := vcardClient.GetBatchVcardInfo(ctx, &Vcard.MatchInfoBatchReq{EstimateMap: estimateMap})
	if err != nil {
		return false
	}

	rp.MatchInfoDatas = resp.Data

	return true

}

func (rp *VcardInfo) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {
}

func (rp *VcardInfo) BuildProductBizInfo(ctx context.Context, product biz_runtime.ProductInfoFull, info *models.PrivateBizInfo) {
	if !carpool.IsPinCheCheV2(product.Product.ProductCategory, product.Product.CarpoolPriceType) {
		return
	}
	info.VcardData = &models.VcardResult{}
	if len(rp.MatchInfoDatas) == 0 {
		return
	}

	estimateId := product.Product.EstimateID
	if matchInfoData, ok := rp.MatchInfoDatas[estimateId]; ok && matchInfoData != nil {
		if matchInfoData.UsableCard != nil {
			info.VcardData.UsableCard = matchInfoData.UsableCard
		}

		if matchInfoData.SendCard != nil {
			info.VcardData.SendCard = matchInfoData.SendCard
		}
	}

}
