package option_gen_price

import (
	"context"
	"encoding/json"
	"errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"github.com/spf13/cast"
	"strconv"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	Dirpc_SDK_Ferrari "git.xiaojukeji.com/dirpc/dirpc-go-http-Ferrari"
	Plutus "git.xiaojukeji.com/dirpc/dirpc-go-http-Plutus"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ferrari"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
)

const (
	CallerRouteEstimate   = "p_route_estimate"
	CallerEstimateByOrder = "estimate_by_order"
)

const (
	NotRecalculateRoute = 1
)

type EstimatePriceByOrderOutput struct {
	orderInfo       *order_info.OrderInfo
	quotationInfo   *biz_runtime.QuotationV2
	travelQuotation *Dirpc_SDK_Ferrari.TravelQuotation
	caller          string
}

func NewEstimatePriceByOrder(orderInfo *order_info.OrderInfo, quotationInfo *biz_runtime.QuotationV2, travelQuotation *Dirpc_SDK_Ferrari.TravelQuotation, caller string) biz_runtime.OptionProcessPrice {
	return &EstimatePriceByOrderOutput{orderInfo: orderInfo, quotationInfo: quotationInfo, travelQuotation: travelQuotation, caller: caller}
}

func (e *EstimatePriceByOrderOutput) ExecPrice(ctx context.Context, baseReq *models.BaseReqData, products []*models.Product) ([]*biz_runtime.ProductInfoFull, error) {
	request := e.buildRequest(ctx, baseReq, products)
	productsFull := make([]*biz_runtime.ProductInfoFull, 0)

	if request == nil {
		return nil, errors.New("build price req is fail")
	}
	resp, err := price_api.MultiEstimateByOrder(ctx, request)
	if err != nil {
		return nil, err
	}

	for _, product := range products {
		priceResp, ok := resp[product.EstimateID]
		if product == nil || !ok || priceResp == nil || priceResp.EstimateData == nil {
			continue
		}

		if product.BizInfo == nil {
			product.BizInfo = &models.PrivateBizInfo{}
		}

		product.BizInfo.OriginBills = priceResp.OriginBills

		productFull := &biz_runtime.ProductInfoFull{
			BaseReqData: baseReq,
			Product:     product,
			ExtraInfo:   priceResp.ExtraInfo,
		}

		productFull.SetHu(priceResp.EstimateData)

		productsFull = append(productsFull, productFull)
	}

	return productsFull, nil
}

func (e *EstimatePriceByOrderOutput) buildRequest(ctx context.Context, baseReq *models.BaseReqData, products []*models.Product) *PriceApi.MultiEstimateByOrderRequest {
	if len(products) <= 0 || e.orderInfo == nil {
		return nil
	}

	orderInfo, quotationInfo, travelQuotation := e.orderInfo, e.quotationInfo, e.travelQuotation
	if travelQuotation == nil {
		travelQuotation = baseReq.CommonBizInfo.TravelQuotation
	}

	request := &PriceApi.MultiEstimateByOrderRequest{}
	reqs := make([]*PriceApi.EstimatePriceReq, 0)

	for _, product := range products {
		if product == nil {
			continue
		}

		priceReq := biz_runtime.GenProductPriceReq(ctx, "", baseReq, product)
		genProductPriceReqByProduct(ctx, product, priceReq)
		if priceReq != nil {
			extraInfo := priceReq.ExtraInfo
			if extraInfo == nil {
				extraInfo = make(map[string]string)
			}

			extraInfo["order"] = util.JustJsonEncode(priceReq.OrderExt)
			if preProduct := e.buildPreProduct(ctx, baseReq); preProduct != "" {
				extraInfo["pre_require_product"] = preProduct
			}

			reqs = append(reqs, &PriceApi.EstimatePriceReq{
				CommonInfo:        priceReq.CommonInfoSt,
				PassengerInfo:     util.JustJsonEncode(priceReq.PassengerInfo),
				OrderInfo:         priceReq.OrderInfoSt,
				ExtraInfo:         extraInfo,
				CustomServiceInfo: "[]",
				OneKeyActivity:    "{}",
			})
		}
	}

	request.OrderId = orderInfo.GetOrderID()
	request.AccessKeyId = baseReq.CommonInfo.AccessKeyID
	request.AppVersion = baseReq.CommonInfo.AppVersion
	request.Caller = e.caller
	request.BubbleEstimateId = orderInfo.EstimateId
	request.UpdateTravelSceneType = util.IntToInt64Ptr(NotRecalculateRoute)

	if quotationInfo != nil {
		request.DynamicTotalFee = quotationInfo.DynamicTotalFee
		request.FeeDetailInfo = quotationInfo.FeeDetailInfo
	}

	request = replaceBillReq(ctx, request, travelQuotation, orderInfo)

	request.OrderInfo = BuildOrderInfo(ctx, orderInfo)
	request.ExtraInfo = buildExtraInfo(ctx, baseReq, orderInfo, travelQuotation)

	request.EstimatePriceReq = reqs

	return request
}

func genProductPriceReqByProduct(ctx context.Context, product *models.Product, priceReq *price_api.PriceEstimateReq) {
	if priceReq == nil || product == nil {
		return
	}

	if len(product.RouteID) <= 0 {
		return
	}

	priceReq.CommonInfoSt.RouteId = util.Int64Ptr(util.String2int64(ctx, product.RouteID))
}

func buildExtraInfo(ctx context.Context, baseReq *models.BaseReqData, orderInfo *order_info.OrderInfo, travelQuotation *Dirpc_SDK_Ferrari.TravelQuotation) map[string]string {
	extraInfo := make(map[string]string)
	if travelQuotation != nil {
		if priceData, ok := travelQuotation.PriceData[ferrari.BeforeFreeUpgradeFee]; priceData != nil && ok {
			extraInfo[ferrari.BeforeFreeUpgradeFee] = strconv.Itoa(int(priceData.FeeValue))
		}

		if priceData, ok := travelQuotation.PriceData[ferrari.WaitFreeUpgradeFee]; priceData != nil && ok {
			extraInfo[ferrari.WaitFreeUpgradeFee] = strconv.Itoa(int(priceData.FeeValue))
			var priceDataList []*Dirpc_SDK_Ferrari.PriceItem
			priceDataList = append(priceDataList, priceData)
			priceDataStr, err := json.Marshal(priceDataList)
			if err == nil {
				extraInfo[ferrari.PassengerBillInfoList] = string(priceDataStr)
			}
		}

		// 读取行中报价单相关权益数据
		if priceData, ok := travelQuotation.GetPriceData()[ferrari.SurprisePrivilege]; priceData != nil && ok {
			priceDataExtraInfo := priceData.GetExtraInfo()
			extraInfoMap := make(map[string]interface{})
			err := json.Unmarshal([]byte(priceDataExtraInfo), &extraInfoMap)
			if err == nil {
				batchID, ok := extraInfoMap[ferrari.BatchID]
				if ok {
					extraInfo[ferrari.BatchID] = cast.ToString(batchID)
				}
			}
		}
	}

	if baseReq.CommonBizInfo.IsHitMemberPrivilegeAddAPlus {
		extraInfo["is_upgrade_aplus"] = "1"
	}

	extraInfo["region_type"] = orderInfo.RegionType
	extraInfo["is_short_book"] = orderInfo.IsShortBook
	extraInfo["assign_type"] = orderInfo.AssignType
	extraInfo["curr_lat"] = cast.ToString(baseReq.AreaInfo.Lat)
	extraInfo["curr_lng"] = cast.ToString(baseReq.AreaInfo.Lng)
	extraInfo["business_travel_order_type"] = orderInfo.ExtendFeatureParsed.BusinessTravelOrderType
	return extraInfo
}

func BuildOrderInfo(ctx context.Context, orderData *order_info.OrderInfo) string {
	if orderData == nil {
		return ""
	}

	orderInfo := &Plutus.Order{
		OrderId:                      util.String2int64(ctx, orderData.OrderId),
		DriverId:                     util.String2int64(ctx, orderData.DriverId),
		DriverPhone:                  orderData.DriverPhone,
		PassengerId:                  util.String2int64(ctx, orderData.PassengerId),
		PassengerPhone:               orderData.PassengerPhone,
		PassengerCount:               util.String2int64(ctx, orderData.PassengerCount),
		TravelId:                     util.String2int64(ctx, orderData.TravelId),
		SchemaId:                     util.String2int64(ctx, orderData.SchemaId),
		ComboType:                    util.String2int64(ctx, orderData.ComboType),
		ComboId:                      util.String2int64(ctx, orderData.ComboId),
		StrategyToken:                orderData.StrategyToken,
		CarId:                        util.String2int64(ctx, orderData.CarId),
		Area:                         util.String2int64(ctx, orderData.Area),
		Type:                         util.String2int64(ctx, orderData.Type),
		ExtraType:                    util.String2int64(ctx, orderData.ExtraType),
		DriverType:                   util.String2int64(ctx, orderData.DriverType),
		ProductId:                    util.String2int64(ctx, orderData.ProductId),
		BusinessId:                   orderData.BusinessId,
		Tip:                          util.String2int64(ctx, orderData.Tip),
		IncentiveFee:                 0,
		Token:                        "",
		ProductToken:                 "",
		IsSep:                        0,
		LimitFee:                     0,
		CapPrice:                     util.String2float64(ctx, orderData.CapPrice),
		CarpoolFailFee:               0,
		DynamicPrice:                 util.String2float64(ctx, orderData.DynamicPrice),
		DelayTimeStart:               orderData.DelayTimeStart,
		BeginChargeTime:              orderData.BeginChargeTime,
		FinishTime:                   orderData.FinishTime,
		DriverDisplayPrice:           util.String2float64(ctx, orderData.DriverDisplayPrice),
		Channel:                      util.String2int64(ctx, orderData.Channel),
		PreTotalFee:                  util.String2float64(ctx, orderData.PreTotalFee),
		Pangu:                        "",
		IsAirport:                    0,
		Bonus:                        util.String2float64(ctx, orderData.Bonus),
		Bouns:                        util.String2float64(ctx, orderData.Bouns),
		Airport:                      0,
		StriveCarLevel:               orderData.StriveCarLevel,
		WaitFee:                      0,
		WaitTime:                     0,
		StartDestDistance:            util.String2PtrString(orderData.StartDestDistance),
		DepartureTime:                orderData.DepartureTime,
		StartingLng:                  util.String2PtrString(orderData.StartingLng),
		StartingLat:                  util.String2PtrString(orderData.StartingLat),
		DestLng:                      util.String2PtrString(orderData.DestLng),
		DestLat:                      util.String2PtrString(orderData.DestLat),
		OrderStatus:                  util.String2int64(ctx, orderData.OrderStatus),
		District:                     orderData.District,
		AbstractDistrict:             "",
		BeginChargeLng:               util.String2PtrString(orderData.BeginChargeLng),
		BeginChargeLat:               util.String2PtrString(orderData.BeginChargeLat),
		EstimateId:                   orderData.EstimateId,
		PayType:                      util.String2PtrString(orderData.PayType),
		CompanyCarpoolFlag:           0,
		TripCountry:                  "",
		CountPriceType:               util.String2PtrString(orderData.CountPriceType),
		DonateFee:                    0,
		BegunTime:                    orderData.BegunTime,
		BegunLat:                     util.String2PtrString(orderData.BegunLat),
		BegunLng:                     util.String2PtrString(orderData.BegunLng),
		OrderNTuple:                  nil,
		IsInsure:                     false,
		InsureCount:                  0,
		BeginWaitFeeChargeTime:       0,
		ProductFields:                nil,
		SerialOrderForDispatch:       nil,
		County:                       orderData.County,
		CountryIsoCode:               orderData.CountryIsoCode,
		EstimateTime:                 util.String2PtrString(orderData.EstimateTime),
		DriverDynamicTimes:           orderData.DriverDynamicTimes,
		FreeCommission:               0,
		UpgradePrivilegeEnable:       0,
		PrivilegeDistanceLimit:       0,
		CompanyId:                    util.String2int64(ctx, util.StringPtr2String(orderData.CompanyId)),
		FenceId:                      0,
		DriverFenceId:                0,
		ToArea:                       &orderData.ToArea,
		DriverCarpoolFailFee:         0,
		DriverDynamicPrice:           util.String2PtrString(orderData.DriverDynamicPrice),
		DriverDynamicType:            util.String2PtrString(orderData.DriverDynamicType),
		MemberLevel:                  0,
		CapacityLevel:                orderData.CapacityLevel,
		RequireLevel:                 orderData.RequireLevel,
		DepartureRange:               nil,
		OriDistrict:                  "",
		EndWaitFeeChargeTime:         0,
		WaitFeeAssignToDriver:        false,
		DriverWaitFee:                0,
		StriveTime:                   orderData.StriveTime,
		ArriveTime:                   orderData.ArriveTime,
		TaxesFee:                     0,
		CreateTimeStamp:              0,
		DriverProductId:              orderData.DriverProductId,
		OptionalFeature:              nil,
		PlutusActivity:               0,
		CoronavirusDriverHasFee:      0,
		IsCarpoolPriority:            0,
		OriginalPassengerCount:       util.String2PtrString(orderData.OriginalPassengerCount),
		AdditionalEstimateId:         orderData.AdditionalEstimateId,
		AdditionalCapPrice:           util.String2PtrString(orderData.AdditionalCapPrice),
		AdditionalPreTotalFee:        util.String2PtrString(orderData.AdditionalPreTotalFee),
		AdditionalOrderNTuple:        nil,
		AdditionalExtraType:          0,
		AdditionalDynamicPrice:       0,
		AdditionalLevelId:            nil,
		AdditionalDynamicCapping:     nil,
		AdditionalCountPriceType:     nil,
		CarpoolOrderSence:            util.String2PtrString(orderData.CarpoolOrderScene),
		PickUpDistance:               0,
		FreeWait:                     "",
		ReducePrice:                  0,
		AdditionalEstimateFixedFees:  orderData.AdditionalEstimateFixedFees,
		IsMixedPayment:               0,
		BaseCarLevelTotalFee:         0,
		MockPrice:                    0,
		SpBillType:                   0,
		SpDriverLevelType:            0,
		AssignedLat:                  util.String2PtrString(orderData.AssignedLat),
		AssignedLng:                  util.String2PtrString(orderData.AssignedLng),
		PreparedLat:                  util.String2PtrString(orderData.PreparedLat),
		PreparedLng:                  util.String2PtrString(orderData.PreparedLng),
		KflowerAppointmentTrPrice:    0,
		KflowerCarpoolSuccTrPrice:    0,
		KflowerCarpoolFailTrPrice:    0,
		CarpoolScenePrice:            nil,
		PoolNum:                      0,
		PayHourFlag:                  0,
		MachinePhone:                 "",
		MachineUserId:                0,
		FromName:                     orderData.FromName,
		ToName:                       orderData.ToName,
		RequiredLevel:                orderData.RequireLevel,
		WayPointsVersion:             orderData.WayPointsVersion,
		PLang:                        "",
		DLang:                        "",
		IntercityBasePrice:           0,
		UpperLimitPriceDiscount:      util.String2PtrString(orderData.UpperLimitPriceDiscount),
		UpperLimitPrice:              util.String2PtrString(orderData.UpperLimitPrice),
		UpperLimitPriceType:          util.String2PtrString(orderData.UpperLimitPriceType),
		OpenId:                       "",
		AssignedTime:                 &orderData.AssignedTime,
		ZeroTrType:                   0,
		StartingName:                 orderData.StartingName,
		DestName:                     orderData.DestName,
		IsCanoe:                      0,
		DispatchPackType:             0,
		DriverTakeRateMode:           0,
		DriverTakeRate:               0,
		PassengerPriceModel:          0,
		DriverPriceModel:             0,
		FreeRideType:                 0,
		CrossAreaOrderType:           0,
		NotUseCouponIds:              nil,
		CarpoolChooseOrderPriceModel: orderData.CarpoolChooseOrderPriceModel,
		PickOnTimeReward:             nil,
		PickOnTimePtimes:             nil,
		PickOnTimeDtimes:             nil,
		WaitFeeType:                  nil,
		DepartureTimeOrigin:          orderData.DepartureTimeOrigin,
	}

	orderInfoStr, err := json.Marshal(orderInfo)
	if err != nil {
		log.Trace.Warnf(ctx, "buildOrderInfo", "")
		return ""
	}

	return string(orderInfoStr)
}

func replaceBillReq(ctx context.Context, request *PriceApi.MultiEstimateByOrderRequest, travelQuotation *Dirpc_SDK_Ferrari.TravelQuotation, orderInfo *order_info.OrderInfo) *PriceApi.MultiEstimateByOrderRequest {
	apolloParams := map[string]string{
		"pid":        cast.ToString(orderInfo.PassengerId),
		"phone":      cast.ToString(orderInfo.PassengerPhone),
		"city":       cast.ToString(orderInfo.Area),
		"product_id": cast.ToString(orderInfo.ProductId),
	}
	if !apollo.FeatureToggle(ctx, "gs_mamba_replace_bill_request_switch", orderInfo.PassengerId, apolloParams) {
		return request
	}
	if travelQuotation != nil && travelQuotation.OtherData[ferrari.OrderFeeResult] != "" {
		var orderFeeData ferrari.OrderFeeResultInfo
		err := json.Unmarshal([]byte(travelQuotation.OtherData[ferrari.OrderFeeResult]), &orderFeeData)
		if orderFeeData.FeeDetailInfo == nil {
			return request
		}
		if err == nil {
			request.FeeDetailInfo = orderFeeData.FeeDetailInfo
			request.DynamicTotalFee = orderFeeData.DynamicTotalFee
		}
	}
	return request
}

func (e *EstimatePriceByOrderOutput) buildPreProduct(ctx context.Context, baseReq *models.BaseReqData) string {

	orderInfo := e.orderInfo

	// 修改路线增加灰度控制是否放开
	if e.caller == CallerRouteEstimate && !apollo.FeatureToggle(ctx,
		"route_estimate_upgrade_open",
		cast.ToString(baseReq.PassengerInfo.PID),
		baseReq.GetApolloParam()) {
		return ""
	}

	if orderInfo.PreUpgradeGroupKey != nil &&
		*orderInfo.PreUpgradeGroupKey != "" &&
		*orderInfo.PreUpgradeGroupKey != "0" &&
		orderInfo.ExtendFeatureParsed.MultiRequiredProduct != nil {
		if productStruct, ok := orderInfo.ExtendFeatureParsed.MultiRequiredProduct[*orderInfo.PreUpgradeGroupKey]; ok {
			return util.JustJsonEncode(productStruct)
		}
	}
	return ""
}
