package option_gen_price

import (
	"context"
	"testing"

	sdk "git.xiaojukeji.com/dirpc/dirpc-go-http-Dos"
	Dirpc_SDK_Ferrari "git.xiaojukeji.com/dirpc/dirpc-go-http-Ferrari"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ferrari"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

func Test_buildExtraInfo(t *testing.T) {
	type args struct {
		ctx             context.Context
		baseReq         *models.BaseReqData
		orderInfo       *order_info.OrderInfo
		travelQuotation *Dirpc_SDK_Ferrari.TravelQuotation
	}
	tests := []struct {
		name     string
		args     args
		want     map[string]string
		mocker   func() []*mockey.Mocker
		asserter func(t *testing.T, got map[string]string)
	}{
		{
			name: "test surprise privilege with batch_id",
			args: args{
				ctx:     context.Background(),
				baseReq: &models.BaseReqData{},
				orderInfo: &order_info.OrderInfo{
					OrderInfo:           &sdk.OrderInfo{},
					ExtendFeatureParsed: dos.ExtendFeatureStruct{},
				},
				travelQuotation: &Dirpc_SDK_Ferrari.TravelQuotation{
					PriceData: map[string]*Dirpc_SDK_Ferrari.PriceItem{
						ferrari.SurprisePrivilege: {
							ExtraInfo: `{"batch_id": "test_batch_id"}`,
						},
					},
				},
			},
			mocker: func() []*mockey.Mocker {
				return nil
			},
			asserter: func(t *testing.T, got map[string]string) {
				assert.NotNil(t, got)
				assert.Equal(t, "test_batch_id", got[ferrari.BatchID])
			},
		},
		{
			name: "test surprise privilege without batch_id",
			args: args{
				ctx:     context.Background(),
				baseReq: &models.BaseReqData{},
				orderInfo: &order_info.OrderInfo{
					OrderInfo:           &sdk.OrderInfo{},
					ExtendFeatureParsed: dos.ExtendFeatureStruct{},
				},
				travelQuotation: &Dirpc_SDK_Ferrari.TravelQuotation{
					PriceData: map[string]*Dirpc_SDK_Ferrari.PriceItem{
						ferrari.SurprisePrivilege: {
							ExtraInfo: `{"other_key": "other_value"}`,
						},
					},
				},
			},
			mocker: func() []*mockey.Mocker {
				return nil
			},
			asserter: func(t *testing.T, got map[string]string) {
				assert.NotNil(t, got)
				_, ok := got[ferrari.BatchID]
				assert.False(t, ok)
			},
		},
		{
			name: "test surprise privilege with invalid json",
			args: args{
				ctx:     context.Background(),
				baseReq: &models.BaseReqData{},
				orderInfo: &order_info.OrderInfo{
					OrderInfo:           &sdk.OrderInfo{},
					ExtendFeatureParsed: dos.ExtendFeatureStruct{},
				},
				travelQuotation: &Dirpc_SDK_Ferrari.TravelQuotation{
					PriceData: map[string]*Dirpc_SDK_Ferrari.PriceItem{
						ferrari.SurprisePrivilege: {
							ExtraInfo: `invalid_json`,
						},
					},
				},
			},
			mocker: func() []*mockey.Mocker {
				return nil
			},
			asserter: func(t *testing.T, got map[string]string) {
				assert.NotNil(t, got)
				_, ok := got[ferrari.BatchID]
				assert.False(t, ok)
			},
		},
		{
			name: "test with nil travelQuotation",
			args: args{
				ctx:     context.Background(),
				baseReq: &models.BaseReqData{},
				orderInfo: &order_info.OrderInfo{
					OrderInfo:           &sdk.OrderInfo{},
					ExtendFeatureParsed: dos.ExtendFeatureStruct{},
				},
				travelQuotation: nil,
			},
			mocker: func() []*mockey.Mocker {
				return nil
			},
			asserter: func(t *testing.T, got map[string]string) {
				assert.NotNil(t, got)
				_, ok := got[ferrari.BatchID]
				assert.False(t, ok)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mocker != nil {
				patches := tt.mocker()
				defer func() {
					for _, p := range patches {
						p.UnPatch()
					}
				}()
			}
			got := buildExtraInfo(tt.args.ctx, tt.args.baseReq, tt.args.orderInfo, tt.args.travelQuotation)
			if tt.asserter != nil {
				tt.asserter(t, got)
			}
		})
	}
}
