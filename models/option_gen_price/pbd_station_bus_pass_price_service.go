package option_gen_price

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type PbdStationBusPassPrice struct {
}

func NewPbdStationBusPassPrice() *PbdStationBusPassPrice {
	return &PbdStationBusPassPrice{}
}

func (s *PbdStationBusPassPrice) ExecPrice(ctx context.Context, brd *models.BaseReqData, baseProducts []*models.Product) ([]*biz_runtime.ProductInfoFull, error) {
	// 6. 组装productInfoFull list
	var (
		products []*biz_runtime.ProductInfoFull
	)
	for _, product := range baseProducts {
		pFull := &biz_runtime.ProductInfoFull{
			BaseReqData:  brd,
			Product:      product,
			BillDetail:   nil,
			DiscountInfo: nil,
			PayInfo:      nil,
		}
		products = append(products, pFull)
	}

	return products, nil
}
