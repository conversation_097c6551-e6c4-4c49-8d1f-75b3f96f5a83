package before_price_filter

import (
	"context"
	ticketPrice "git.xiaojukeji.com/dirpc/dirpc-go-http-TicketPrice"
	"sort"
	"strconv"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	DetailConst "git.xiaojukeji.com/gulfstream/mamba/common/consts/intercity_estimate_detail"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

const (
	DefaultModel           = -1
	NormalWithStationId    = 0
	NormalWithoutStationId = 1
	QuickWithCap           = 2
	QuickWithTiered        = 3
	CorrectStation         = 4

	InEffectiveStationId = 0
)

type DerivateStation struct {
	baseReq *models.BaseReqData
}

func NewDerivateStation(req *models.BaseReqData) *DerivateStation {
	return &DerivateStation{
		baseReq: req,
	}
}

func GetStationOrderList(stationInfo *Prfs.RouteDetailData) []int64 {
	var res []int64
	for _, v := range stationInfo.RouteBasicInfo.StationList {
		res = append(res, int64(v.StationId))
	}
	return res
}

type PriceInfo []*ticketPrice.StationPriceItem

func (priceInfo PriceInfo) Len() int {
	return len(priceInfo)
}

func (priceInfo PriceInfo) Less(i, j int, order []int64) bool {
	for k := 0; k < len(order); k++ {
		if order[k] == priceInfo[i].From {
			return false
		} else if order[k] == priceInfo[j].From {
			return true
		}
	}
	return priceInfo[i].From > priceInfo[j].From
}

func (priceInfo PriceInfo) Swap(i, j int) {
	priceInfo[i], priceInfo[j] = priceInfo[j], priceInfo[i]
}

func (d *DerivateStation) getLocation(stationid int32, stationInfo *Prfs.RouteDetailData) (float64, float64, string, int32) {
	var lat, lng float64
	var addressName string
	var cityId int32
	for _, v := range stationInfo.RouteBasicInfo.StationList {
		if v.StationId == stationid {
			if tmp, err := strconv.ParseFloat(v.StationLat, 64); err == nil {
				lat = tmp
			}
			if tmp, err := strconv.ParseFloat(v.StationLng, 64); err == nil {
				lng = tmp
			}
			addressName = v.StationName
			cityId = v.City
			break
		}
	}
	return lat, lng, addressName, cityId
}
func getStationDepartureTime(fromIds int64, commonBizInfo models.CommonBizInfo) int64 {
	if commonBizInfo.ShiftInfo == nil || commonBizInfo.ShiftInfo.StationDetails == nil {
		return 0
	}
	stationDetails := commonBizInfo.ShiftInfo.StationDetails
	for _, item := range stationDetails {
		if item.StationID == fromIds && nil != item.DepartureTime {
			return *item.DepartureTime
		}
	}
	return 0
}

func (d *DerivateStation) correctStationInfo(fromIds int64, toIds int64, stationInfo *Prfs.RouteDetailData, products []*models.Product) {
	fromStationLat, fromStationLng, fromAddress, FromCityId := d.getLocation(int32(fromIds), stationInfo)
	ToStationLat, ToStationLng, toAddress, toCityId := d.getLocation(int32(toIds), stationInfo)
	d.baseReq.AreaInfo.CurLat = fromStationLat
	d.baseReq.AreaInfo.CurLng = fromStationLng
	d.baseReq.AreaInfo.FromLat = fromStationLat
	d.baseReq.AreaInfo.FromLng = fromStationLng
	d.baseReq.AreaInfo.Area = FromCityId
	d.baseReq.AreaInfo.ToLat = ToStationLat
	d.baseReq.AreaInfo.ToLng = ToStationLng
	d.baseReq.AreaInfo.FromName = fromAddress
	d.baseReq.AreaInfo.ToName = toAddress
	d.baseReq.AreaInfo.ToArea = toCityId
	d.baseReq.CommonInfo.DepartureTime = getStationDepartureTime(fromIds, d.baseReq.CommonBizInfo)
	products[0].BizInfo.IntercityData.StartStationID = fromIds
	products[0].BizInfo.IntercityData.DestStationID = toIds
	products[0].BizInfo.DepartureTime = d.baseReq.CommonInfo.DepartureTime
}

func checkValidStation(stationId int64, RouteDetail *Prfs.RouteDetailData) bool {
	if RouteDetail == nil || RouteDetail.RouteBasicInfo == nil {
		return false
	}
	for _, station := range RouteDetail.RouteBasicInfo.StationList {
		if station != nil && int64(station.StationId) == stationId {
			return true
		}
	}
	return false
}

func (d *DerivateStation) getTravelStationids(travelType int32, RouteDetail *Prfs.RouteDetailData) (int64, int64) {
	var (
		defaultFromStationId, defaultToStationId int64
	)
	stationInfo := d.baseReq.CommonBizInfo.StationInfo
	stationPrice := d.baseReq.CommonBizInfo.RoutePrice
	stationLen := len(RouteDetail.RouteBasicInfo.StationList)
	defaultFromStationId, defaultToStationId = int64(RouteDetail.RouteBasicInfo.StationList[0].StationId), int64(RouteDetail.RouteBasicInfo.StationList[stationLen-1].StationId)
	switch travelType {
	case NormalWithStationId:
		for _, v := range stationPrice.StationPrice {
			if v.From == stationInfo.StartStationId && v.To == stationInfo.EndStationId {
				d.baseReq.CommonBizInfo.AdultPrice = v.Price
			}
		}
		return stationInfo.StartStationId, stationInfo.EndStationId
	case NormalWithoutStationId:
		return defaultFromStationId, defaultToStationId
	case QuickWithCap:
		return defaultFromStationId, defaultToStationId
	case QuickWithTiered:
		// 按照线路配置，对起终点对儿进行排序
		sort.Slice(stationPrice.StationPrice, func(i, j int) bool {
			return PriceInfo(stationPrice.StationPrice).Less(i, j, GetStationOrderList(RouteDetail))
		})
		for _, v := range stationPrice.StationPrice {
			if v.Price == d.baseReq.CommonBizInfo.AdultPrice && checkValidStation(v.From, RouteDetail) && checkValidStation(v.To, RouteDetail) {
				return v.From, v.To
			}
		}
		return defaultFromStationId, defaultToStationId
	case CorrectStation:
		return d.baseReq.CommonBizInfo.StartStationId, d.baseReq.CommonBizInfo.EndStationId
	default:
		return defaultFromStationId, defaultToStationId
	}
}

func genModel(baseReq *models.BaseReqData) int32 {
	stationInfo := baseReq.CommonBizInfo.StationInfo
	if len(baseReq.CommonBizInfo.AgentType) == 0 && baseReq.CommonBizInfo.CorrectStation {
		return CorrectStation
	}
	priceInfo := baseReq.CommonBizInfo.RoutePrice
	if baseReq.CommonBizInfo.AgentType == DetailConst.FormatScanCode {
		if stationInfo.StartStationId == InEffectiveStationId || stationInfo.EndStationId == InEffectiveStationId {
			return NormalWithoutStationId
		} else {
			return NormalWithStationId
		}
	} else if baseReq.CommonBizInfo.AgentType == DetailConst.QuickScanCode && priceInfo != nil {
		if priceInfo.IsPrice == DetailConst.CapPrice {
			return QuickWithCap
		} else if priceInfo.IsPrice == DetailConst.TieredPrice {
			return QuickWithTiered
		}
	}
	return DefaultModel
}

func checkParams(model int32, products []*models.Product, baseReq *models.BaseReqData) bool {
	switch model {
	case CorrectStation:
		return products[0].BizInfo.IntercityData.RouteDetailV2 != nil
	case NormalWithStationId, NormalWithoutStationId, QuickWithCap, QuickWithTiered:
		return products[0].BizInfo.IntercityData.RouteDetailV2 != nil && baseReq.CommonBizInfo.RoutePrice != nil
	default:
		return false
	}
}

// 反推站点后的过滤价格
func (d *DerivateStation) Exec(ctx context.Context, products []*models.Product) []models.ProductCategory {
	var (
		toBeRemoved                  []models.ProductCategory
		fromStationId, DestStationId int64
	)

	travelModel := genModel(d.baseReq)
	if travelModel != DefaultModel && checkParams(travelModel, products, d.baseReq) {
		stationInfo := Prfs.RouteDetailData(*(products[0].BizInfo.IntercityData.RouteDetailV2))
		fromStationId, DestStationId = d.getTravelStationids(travelModel, &stationInfo)
		d.correctStationInfo(fromStationId, DestStationId, &stationInfo, products)
	} else {
		toBeRemoved = append(toBeRemoved, models.ProductCategory(products[0].ProductCategory))
	}

	return toBeRemoved
}
