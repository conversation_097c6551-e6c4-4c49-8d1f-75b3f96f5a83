package before_price_filter

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	bill "git.xiaojukeji.com/dirpc/dirpc-go-http-Plutus"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/plutus"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

var comboCache sync.Map

const (
	Normal int32 = iota
	PickUp
	DropOff
)

const (
	ComboTypeNormal = iota
	ComboTypeCombo
	ComboTypePickUp
	ComboTypeDropOff
)

const (
	expiredTime = 5 * 60 // 过期时间5min
	// expiredTime = 0
	descTemp = "%d小时%d公里"
)

// MeetingCombo 会议用车套餐
type MeetingCombo struct {
	ComboId   int32  // 套餐ID
	ProductId int32  // 产品线
	CarLevel  string // 车型
	Desc      string // 套餐描述
}

// MeetingCombos 会议用车套餐列表
type MeetingCombos struct {
	Combos     map[string]map[int32]*MeetingCombo // 专车包车套餐 car_level -> combo_id -> combo
	UpdateTime int64                              // 更新时间
}

type MeetingCarComboFilter struct {
	ProductCategory     int32
	ProductId           int32
	CarLevel            string
	ComboType           int32
	ComboId             int32 // 套餐ID
	Area                int32
	ServiceFeePassenger int64
	ServiceFeeDriver    int64
	District            string
	Combo               *MeetingCombo // 套餐内容
	IsOpenCity          bool          // 是否开城
}

type MeetingServiceFee struct {
	DriverFeeAmount    int32 `json:"driver_fee_amount"`
	PassengerFeeAmount int32 `json:"passenger_fee_amount"`
}

func (f *MeetingCarComboFilter) Exec(ctx context.Context, products []*models.Product) []models.ProductCategory {
	removed := make([]models.ProductCategory, 0, len(products))
	for _, v := range products {
		// 非包车套餐则只保留用户选择
		if v.ProductCategory != int64(f.ProductCategory) {
			removed = append(removed, models.ProductCategory(v.ProductCategory))
		}
		f.fixProduct(v)
		serviceFee := &MeetingServiceFee{
			DriverFeeAmount:    int32(f.ServiceFeeDriver),
			PassengerFeeAmount: int32(f.ServiceFeePassenger),
		}
		serviceFeeInfo, _ := json.Marshal(serviceFee)
		v.BizInfo.ServiceFeeInfo = string(serviceFeeInfo)
		if f.ComboId == 0 {
			f.IsOpenCity = true
			continue
		}
		f.CarLevel = v.RequireLevel
		// 如果是包车套餐需要判断套餐是否合法
		combos, err := GetComboCache(ctx, f.ProductId, f.Area, f.District, int64(v.LevelType))
		if err != nil {
			removed = append(removed, models.ProductCategory(v.ProductCategory))
			continue
		}
		combo, ok := combos[f.CarLevel]
		if !ok {
			removed = append(removed, models.ProductCategory(v.ProductCategory))
			continue
		}
		if _, ok := combo[f.ComboId]; !ok {
			removed = append(removed, models.ProductCategory(v.ProductCategory))
			continue
		}
		f.IsOpenCity = true
	}
	return removed
}

// fixProduct 对dds返回的product进行修改，传递给账单
func (f *MeetingCarComboFilter) fixProduct(product *models.Product) {
	// 会议用车，包车 commercial_type = 1 && long_rent_type = 1
	if f.ComboId != 0 {
		product.LongRentType = 1
		product.ComboType = 1
		product.BizInfo.ComboID = int64(f.ComboId)
		product.AirportType = Normal
	} else {
		// 会议用车，非包车场景
		product.LongRentType = 0
		// 市内单程
		if f.ComboType == ComboTypeNormal {
			product.AirportType = Normal
		} else if f.ComboType == ComboTypePickUp {
			// 接机
			product.AirportType = PickUp
		} else if f.ComboType == ComboTypeDropOff {
			// 送机
			product.AirportType = DropOff
		}
	}
}

// Fetch 调账单获取套餐内容
func (f *MeetingCarComboFilter) Fetch(ctx context.Context, products []*models.Product) bool {
	if f.ComboId == 0 {
		return true
	}
	for _, v := range products {
		if f.ProductCategory != int32(v.ProductCategory) {
			continue
		}
		combos, err := GetComboCache(ctx, f.ProductId, f.Area, f.District, int64(v.LevelType))
		if err != nil {
			return true
		}
		combo, ok := combos[f.CarLevel]
		if !ok {
			return true
		}
		f.Combo = combo[f.ComboId]
	}
	return true
}

func (f *MeetingCarComboFilter) BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) {}

func (f *MeetingCarComboFilter) BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) {
}

func (f *MeetingCarComboFilter) GetErrorInfo(ctx context.Context) error {
	return nil
}

// GetMeetingCombos 获取乘客与司机同时满足的套餐
func GetMeetingCombos(ctx context.Context, levelType int64, area int32, district string, productId int32) (map[string]map[int32]*MeetingCombo, error) {
	pStrategy, err := getBillStrategies(ctx, levelType, area, 2, district, productId)
	if err != nil {
		return nil, err
	}
	dStrategy, err := getBillStrategies(ctx, levelType, area, 1, district, productId)
	if err != nil {
		return nil, err
	}
	overlapping := make(map[string]map[int32]*MeetingCombo)
	for k, v := range pStrategy {
		if _, ok := dStrategy[k]; !ok {
			continue
		}
		overlapping[k] = make(map[int32]*MeetingCombo)
		for kk, vv := range v {
			if _, ok := dStrategy[k][kk]; !ok {
				continue
			}
			overlapping[k][kk] = vv
		}
	}
	return overlapping, nil
}

func getBillStrategies(ctx context.Context, levelType int64, area int32, role int64, district string, productID int32) (map[string]map[int32]*MeetingCombo, error) {
	longRentType := int16(1)
	commericalType := int16(1)
	airportType := int16(0)
	// 会议用车套餐
	billReq := &bill.GetStrategiesRequest{
		Area:      area,
		Role:      role,
		District:  district,
		ProductId: productID,
		Order: &bill.Order{
			OrderNTuple: &bill.OrderNTuple{
				AirportType:    &airportType,
				LongRentType:   &longRentType,
				CommercialType: &commericalType,
				LevelType:      &levelType,
			},
		},
	}
	strategies, err := plutus.GetStrategies(ctx, billReq)
	if err != nil {
		log.Trace.Warnf(ctx, "meeting_car", "GetMeetingCombos GetStrategies failed, err: %v", err)
		return nil, err
	}
	if strategies == nil || strategies.Data == nil {
		log.Trace.Warnf(ctx, "meeting_car", "GetMeetingCombos GetStrategies failed, resp is nil")
		return nil, nil
	}
	combos := make(map[string]map[int32]*MeetingCombo)
	for _, v := range strategies.Data.Strategies {
		if v == nil {
			continue
		}
		if _, ok := combos[v.CarLevel]; !ok {
			combos[v.CarLevel] = make(map[int32]*MeetingCombo)
		}
		combos[v.CarLevel][int32(v.ComboId)] = &MeetingCombo{
			ComboId:   int32(v.ComboId),
			ProductId: int32(v.ProductId),
			CarLevel:  v.CarLevel,
			Desc:      fmt.Sprintf(descTemp, int64(v.PackageTime)/60, int64(v.PackageDistance)),
		}
	}
	return combos, nil
}

func GetComboCache(ctx context.Context, productId int32, area int32, district string, levelType int64) (map[string]map[int32]*MeetingCombo, error) {
	key := fmt.Sprintf("%d_%d", area, productId)
	if v, ok := comboCache.Load(key); ok {
		cache, okk := v.(MeetingCombos)
		if !okk {
			return nil, fmt.Errorf("cache type error")
		}
		if time.Now().Unix()-cache.UpdateTime > int64(expiredTime) {
			comboCache.Delete(key)
		} else {
			return cache.Combos, nil
		}
	}
	combos, err := GetMeetingCombos(ctx, levelType, area, district, productId)
	if err != nil {
		return nil, err
	}
	newCache := MeetingCombos{
		Combos:     combos,
		UpdateTime: time.Now().Unix(),
	}
	comboCache.Store(key, newCache)
	return combos, nil
}
