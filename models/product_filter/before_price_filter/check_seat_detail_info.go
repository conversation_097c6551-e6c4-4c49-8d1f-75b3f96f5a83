package before_price_filter

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts/ticket_detail_consts"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/seat_selection"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

// 不是filter没有过滤能力, 但是还需要在price-api前检查下座位数, 错误修正为一座预估

type checkSeatInfoFilter struct {
	baseReq *models.BaseReqData
}

type extProduct struct {
	*models.Product

	defaultChildIsOccupy seat_selection_consts.Occupy
	ruleList             []seat_selection.Rule
}

func NewCheckSeatInfoFilter(baseReq *models.BaseReqData) biz_runtime.BeforePriceProductsFilter {
	return &checkSeatInfoFilter{
		baseReq: baseReq,
	}
}

func (c *checkSeatInfoFilter) Exec(ctx context.Context, products []*models.Product) []models.ProductCategory {
	var (
		routeGroup  int64
		needProduct []*extProduct
	)

	if len(products) <= 0 || c.baseReq == nil || len(c.baseReq.CommonBizInfo.SeatDetailInfo) <= 0 {
		return nil
	}

	if !apollo.FeatureToggle(ctx, "gs_child_ticket_switch", strconv.Itoa(int(c.baseReq.PassengerInfo.UID)), c.baseReq.GetApolloParam()) {
		return nil
	}

	baseData := carpool.BaseData{
		City:        int(c.baseReq.AreaInfo.City),
		Phone:       c.baseReq.PassengerInfo.Phone,
		AccessKeyID: int(c.baseReq.CommonInfo.AccessKeyID),
	}

	for _, product := range products {
		if product == nil {
			continue
		}

		if product.BizInfo != nil && product.BizInfo.RouteInfo != nil && product.BizInfo.RouteInfo.RouteGroup != nil {
			routeGroup = *product.BizInfo.RouteInfo.RouteGroup
		}

		if carpool.IsIntercityStation(ctx, int(product.CarpoolType)) {
			c.updateRouteType(product)
			c.updateSeatDetail(product)

			needProduct = append(needProduct, &extProduct{
				Product:              product,
				defaultChildIsOccupy: product.BizInfo.CarryChildrenIsOccupySeat == 1,
				ruleList:             seat_selection.BigBus,
			})
		} else if carpool.IsIntercityThirdPID(ctx, int(product.ProductID)) && carpool.IsSkuModelNoSelf(baseData, int(routeGroup)) {
			product.BizInfo.SeatDetailInfo = c.baseReq.CommonBizInfo.SeatDetailInfo

			needProduct = append(needProduct, &extProduct{
				Product:              product,
				defaultChildIsOccupy: true,
				ruleList:             seat_selection.SmallCar,
			})
		}
	}

	if len(needProduct) <= 0 {
		return nil
	}

	for _, product := range needProduct {
		if product == nil {
			continue
		}

		c.checkSeatDetailInfo(ctx, product)
	}

	return nil
}

// checkSeatDetailInfo 检查座位数
func (c *checkSeatInfoFilter) checkSeatDetailInfo(ctx context.Context, product *extProduct) {
	var (
		bizInfo        = product.BizInfo
		seatDetailInfo = c.baseReq.CommonBizInfo.SeatDetailInfo
		seatDetailMap  = make(map[int32]int32)

		childOccupySeat   bool
		maxPassengerCount int32
	)

	if bizInfo == nil || seatDetailInfo == nil || len(product.ruleList) <= 0 {
		return
	}

	for _, seatInfoItem := range seatDetailInfo {
		if seatInfoItem == nil {
			continue
		}

		if seatInfoItem.PassengerType == seat_selection_consts.CarryChildren.ToInt32() {
			// 携带儿童  站点巴士  不占座
			seatInfoItem.IsOccupySeat = product.defaultChildIsOccupy.ToInt32()
		} else {
			seatInfoItem.IsOccupySeat = seat_selection_consts.IsOccupy.ToInt32()
		}

		seatDetailMap[seatInfoItem.PassengerType] = seatInfoItem.PassengerCount
	}

	if carpool.IsIntercityStation(ctx, int(product.CarpoolType)) {
		if bizInfo.RouteDetail != nil && bizInfo.RouteDetail.RouteExtendInfo != nil && bizInfo.RouteDetail.RouteExtendInfo.SeatLimit != nil {
			maxPassengerCount = bizInfo.RouteDetail.RouteExtendInfo.SeatLimit.MaxPassengerCount
		}
	} else {
		if bizInfo.RouteInfo != nil && bizInfo.RouteInfo.MaxSeatNum != nil {
			maxPassengerCount = *bizInfo.RouteInfo.MaxSeatNum
		}

		childOccupySeat = true
	}

	if maxPassengerCount == 0 {
		// log.Trace.Warnf(ctx, "checkSeatDetailInfo", "maxPassengerCount is zero")
		return
	}

	carpoolSeatNum := c.baseReq.CommonBizInfo.CarpoolSeatNum
	if carpoolSeatNum == 0 {
		carpoolSeatNum = 1
	}

	childOccupySeat = product.defaultChildIsOccupy.ToBool()

	totalNum := seatDetailMap[seat_selection_consts.Adult.ToInt32()] + seatDetailMap[seat_selection_consts.Children.ToInt32()] + seatDetailMap[seat_selection_consts.PreferentialPeople.ToInt32()]
	if childOccupySeat {
		totalNum += seatDetailMap[seat_selection_consts.CarryChildren.ToInt32()]
	}

	if carpoolSeatNum != totalNum {
		log.Trace.Warnf(ctx, "checkSeatDetailInfo", "carpool seat num not equal total num. carpool seat num:%v, total num:%v", carpoolSeatNum, totalNum)
		carpoolSeatNum = totalNum
	}

	//if bizInfo.RouteDetail != nil && bizInfo.RouteDetail.RouteExtendInfo != nil && bizInfo.RouteDetail.RouteExtendInfo.ChildOccupySeat != nil {
	//	// 携童是否占座
	//	childOccupySeat = *bizInfo.RouteDetail.RouteExtendInfo.ChildOccupySeat
	//}

	// 构建一些座位元信息
	seatMetaInfo := &models.SeatMetaInfo{
		ChildOccupySeat:   childOccupySeat,
		MaxPassengerCount: maxPassengerCount,
	}

	for _, rule := range product.ruleList {
		if isFiltered, reason := rule(seatMetaInfo, seatDetailMap); isFiltered && c.baseReq.CommonBizInfo.Scene != ticket_detail_consts.SceneRebook {
			log.Trace.Warnf(ctx, "checkSeatDetailInfo", "seat info is filtered, reason:%v, meta:%v, seat: %v", reason.ToString(), util.JustJsonEncode(seatMetaInfo), util.JustJsonEncode(seatDetailMap))
			c.baseReq.CommonBizInfo.IsRestSeatInfo = true
			product.BizInfo.SeatDetailInfo = c.resetSeatDetail(product, seatMetaInfo)
			product.BizInfo.CarpoolSeatNum = 1
			return
		}
	}
}

func (c *checkSeatInfoFilter) updateSeatDetail(product *models.Product) {

	if product.BizInfo.StationInventoryInfo == nil {
		return
	}

	commBizInfo := c.baseReq.CommonBizInfo
	carryChildOccupy := product.BizInfo.CarryChildrenIsOccupySeat
	var passengerCount int32
	var ticketInfo = make([]*models.SeatDetailInfo, 0)

	for _, sdi := range commBizInfo.SeatDetailInfo {
		if sdi == nil {
			continue
		}
		ticketInfo = append(ticketInfo, &models.SeatDetailInfo{
			PassengerType:  sdi.PassengerType,
			PassengerCount: sdi.PassengerCount,
			IsOccupySeat:   sdi.IsOccupySeat,
		})
	}

	for _, pti := range commBizInfo.PassengerTickerInfo {
		if pti == nil {
			continue
		}

		if pti.TicketType == seat_selection_consts.CarryChildren.ToInt32() {
			pti.IsOccupySeat = carryChildOccupy
		}
	}

	for _, sdi := range ticketInfo {
		if sdi == nil {
			continue
		}

		// 站点巴士携童占座处理
		if sdi.PassengerType == seat_selection_consts.CarryChildren.ToInt32() {
			sdi.IsOccupySeat = carryChildOccupy
		} else {
			sdi.IsOccupySeat = seat_selection_consts.IsOccupy.ToInt32()
		}

		if sdi.IsOccupySeat == 1 {
			passengerCount += sdi.PassengerCount
		}
	}

	product.BizInfo.SeatDetailInfo = ticketInfo
	if passengerCount > 0 {
		c.baseReq.CommonBizInfo.PassengerCount = &passengerCount
		c.baseReq.CommonBizInfo.CarpoolSeatNum = passengerCount
		product.BizInfo.CarpoolSeatNum = passengerCount
	}

}

// resetSeatDetail 重置座位数
func (c *checkSeatInfoFilter) resetSeatDetail(product *extProduct, seatMetaInfo *models.SeatMetaInfo) []*models.SeatDetailInfo {
	var (
		childOccupySeat seat_selection_consts.Occupy
	)

	// 兜底携童占座
	childOccupySeat = product.defaultChildIsOccupy

	if seatMetaInfo != nil {
		if seatMetaInfo.ChildOccupySeat {
			childOccupySeat = seat_selection_consts.IsOccupy
		} else {
			childOccupySeat = seat_selection_consts.NoOccupy
		}
	}
	resultList := []*models.SeatDetailInfo{
		{
			PassengerType:  seat_selection_consts.Adult.ToInt32(),
			PassengerCount: 1,
			IsOccupySeat:   seat_selection_consts.IsOccupy.ToInt32(),
		},
		{
			PassengerType:  seat_selection_consts.Children.ToInt32(),
			PassengerCount: 0,
			IsOccupySeat:   seat_selection_consts.IsOccupy.ToInt32(),
		},
		{
			PassengerType:  seat_selection_consts.CarryChildren.ToInt32(),
			PassengerCount: 0,
			IsOccupySeat:   childOccupySeat.ToInt32(),
		},
	}
	if c.baseReq.CommonBizInfo.IsAllowHomeOwnerTicket() {
		resultList = append(resultList, []*models.SeatDetailInfo{
			{
				PassengerType:  seat_selection_consts.HomeOwnerAdult.ToInt32(),
				PassengerCount: 0,
				IsOccupySeat:   seat_selection_consts.IsOccupy.ToInt32(),
			},
			{
				PassengerType:  seat_selection_consts.HomeOwnerChildren.ToInt32(),
				PassengerCount: 0,
				IsOccupySeat:   seat_selection_consts.IsOccupy.ToInt32(),
			},
			{
				PassengerType:  seat_selection_consts.HomeOwnerOldMan.ToInt32(),
				PassengerCount: 0,
				IsOccupySeat:   seat_selection_consts.IsOccupy.ToInt32(),
			},
		}...)
	}

	return resultList
}

// 大巴改签，继承上一笔订单route_type
func (c *checkSeatInfoFilter) updateRouteType(product *models.Product) {
	commBizInfo := c.baseReq.CommonBizInfo
	product.RouteType = commBizInfo.RouteType
}
