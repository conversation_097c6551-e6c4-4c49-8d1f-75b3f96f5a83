package before_price_filter

import (
	"context"
	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"
)

type FilterStations struct {
	baseReq *models.BaseReqData
}

func NewFilterStations(baseReq *models.BaseReqData) *FilterStations {
	return &FilterStations{
		baseReq: baseReq,
	}
}

func check(products []*models.Product) bool {
	if products[0].BizInfo.IntercityData.RouteDetailV2 == nil || products[0].BizInfo.IntercityData.RouteDetailV2.RouteBasicInfo == nil || products[0].BizInfo.IntercityData.RouteDetailV2.RouteBasicInfo.StationList == nil {
		return false
	}
	return true
}

func (f *FilterStations) updateAndFilterValidStation(stationList []*Prfs.StationInfo) []*Prfs.StationInfo {
	// 存储实际有效的站点信息
	var res []*Prfs.StationInfo
	busShiftInfo := f.baseReq.CommonBizInfo.ShiftInfo
	routeConfigMap := make(map[int32]*Prfs.StationInfo)

	// 配置全集
	for _, stationConfig := range stationList {
		routeConfigMap[stationConfig.StationId] = stationConfig
	}

	// 有效站点
	for _, shiftInfo := range busShiftInfo.StationDetails {
		stationId := cast.ToInt32(shiftInfo.StationID)
		if routeConfigMap[stationId] != nil {
			res = append(res, routeConfigMap[stationId])
		}
	}

	return res
}

func (f *FilterStations) Exec(ctx context.Context, products []*models.Product) []models.ProductCategory {
	var product []models.ProductCategory
	// 未通过校验，不执行后面逻辑
	if !check(products) {
		return product
	}

	// 使用 updateAndFilterValidStation 方法过滤站点并更新信息
	if f.baseReq.CommonBizInfo.ShiftInfo != nil && f.baseReq.CommonBizInfo.ShiftInfo.StationDetails != nil {
		staticStationList := products[0].BizInfo.IntercityData.RouteDetailV2.RouteBasicInfo.StationList
		products[0].BizInfo.IntercityData.RouteDetailV2.RouteBasicInfo.StationList = f.updateAndFilterValidStation(staticStationList)
		return product
	}

	// 没有拿到duse关键数据，无法处理逻辑，用配置兜底
	log.Trace.Infof(ctx, "filterStations", "shiftInfo or stationDetails is nil, use prfs static station list")
	return product
}
