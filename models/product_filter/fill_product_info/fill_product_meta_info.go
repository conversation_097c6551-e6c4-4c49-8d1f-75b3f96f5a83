package fill_product_info

import (
	"context"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/bargain"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"
)

const (
	// Apollo灰度控制key
	FormShowTypeFeature = "gs_form_show_type"
)

type FillProductMetaInfo struct {
	baseReq *models.BaseReqData // 请求信息
}

func NewFillProductMetaInfo(ctx context.Context, req *models.BaseReqData) *FillProductMetaInfo {
	return &FillProductMetaInfo{
		baseReq: req,
	}
}

func (c *FillProductMetaInfo) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	for _, prod := range products {
		// 构建表单显示类型
		prod.BizInfo.FormShowType = c.buildFormShowType(ctx, prod)
	}
	return nil, ""
}

// buildFormShowType 构建表单显示类型 - 从PHP代码转换而来
func (c *FillProductMetaInfo) buildFormShowType(ctx context.Context, product *models.Product) int64 {

	// 惠选车支持多勾
	if product.ProductCategory == estimate_pc_id.EstimatePcIdHuiXuanCar &&
		(bargain.IsBargainRangeCheckable(&c.baseReq.CommonInfo) || tab.IsClassifyTab(c.baseReq.CommonInfo.TabId)) {
		return consts.FormShowTypeAnycar
	}

	// Apollo灰度控制
	apolloParams := c.buildApolloParams(product)
	if apollo.FeatureToggle(ctx, FormShowTypeFeature, c.baseReq.PassengerInfo.Phone, apolloParams) {
		return consts.FormShowTypeGuide
	}

	// 检查特定产品类型
	if c.isSpecialProductType(product) {
		return consts.FormShowTypeGuide
	}

	// 智能巴士拼车在分类tab下的处理
	if carpool.IsSmartBus(int(product.CarpoolType)) && tab.IsClassifyTab(c.baseReq.CommonInfo.TabId) {
		return consts.FormShowTypeGuide
	}

	return consts.FormShowTypeAnycar
}

// buildApolloParams 构建Apollo参数
func (c *FillProductMetaInfo) buildApolloParams(product *models.Product) map[string]string {
	return map[string]string{
		"product_id":       cast.ToString(product.ProductID),
		"product_category": cast.ToString(product.ProductCategory),
		"require_level":    cast.ToString(product.RequireLevel),
		"business_id":      cast.ToString(product.BusinessID),
		"level_type":       cast.ToString(product.LevelType),
		"phone":            c.baseReq.PassengerInfo.Phone,
		"pid":              cast.ToString(c.baseReq.PassengerInfo.PID),
		"access_key_id":    cast.ToString(c.baseReq.CommonInfo.AccessKeyID),
		"app_version":      c.baseReq.CommonInfo.AppVersion,
		"tab_id":           c.baseReq.CommonInfo.TabId, // classify/normal
	}
}

// isSpecialProductType 检查是否是特殊产品类型
func (c *FillProductMetaInfo) isSpecialProductType(product *models.Product) bool {
	// 低价拼车
	if carpool.IsLowPriceCarpoolByInfos(product.ComboType, product.ProductID, product.CarpoolType, product.RequireLevel) {
		return true
	}

	// 城际拼车（非立即出发）
	if carpool.IsInterCityCarpool(product.CarpoolType, product.ComboType) &&
		product.CarpoolType != consts.CarPoolTypeInterCityNew {
		return true
	}

	// 自动驾驶
	if product_id.IsAutoDriving(product.ProductID, cast.ToInt64(product.RequireLevel), product.ComboType) {
		return true
	}

	// 议价车
	if product_id.IsBargainCar(product.ProductID, cast.ToInt64(product.RequireLevel)) {
		return true
	}

	return false
}
