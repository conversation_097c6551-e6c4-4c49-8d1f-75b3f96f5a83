package after_dds_filter

import (
	"context"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/user_type"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
)

type GuideAnycarFilterFastCar struct {
	isHitFilterFastCar bool
}

func NewGuideAnyCarFilterFastCar(ctx context.Context, multiRequireProduct map[string]dos.RequiredProductStruct, userType int32) *GuideAnycarFilterFastCar {
	if len(multiRequireProduct) == 0 || user_type.IsBusinessUser(userType) {
		return nil
	}
	hasTargetCarpool := false
	for _, p := range multiRequireProduct {
		if carpool.IsCarpoolUnsuccRealTimePrice(p.CarpoolPriceType) {
			hasTargetCarpool = true
			break
		}
	}

	if !hasTargetCarpool {
		return nil
	}

	filter := &GuideAnycarFilterFastCar{
		true,
	}

	return filter
}

func (f *GuideAnycarFilterFastCar) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	if f.isHitFilterFastCar {
		return []models.ProductCategory{estimate_pc_id.EstimatePcIdFastCar}, ""
	} else {
		return nil, ""
	}
}
