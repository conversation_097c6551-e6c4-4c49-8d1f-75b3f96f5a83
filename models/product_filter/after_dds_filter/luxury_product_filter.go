package after_dds_filter

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"
	"strconv"
)

const LuxuryProductId = 9

type LuxuryProductFilter struct {
	filterSixSeatLuxury bool
}

func NewLuxuryProductFilter(ctx context.Context, multiRequireProduct map[string]dos.RequiredProductStruct, reqData *models.BaseReqData) *LuxuryProductFilter {
	if len(multiRequireProduct) == 0 {
		return nil
	}

	filter := &LuxuryProductFilter{}
	for _, p := range multiRequireProduct {
		estimatePcId := cast.ToInt(p.EstimatePcID)
		if estimatePcId <= 0 {
			estimatePcId = p.ProductCategory
		}
		// 六座豪华车过滤: 低版本 或 已发单六座
		if !isSupport(ctx, reqData) || (p.ProductId == LuxuryProductId && isSixLux(ctx, int64(estimatePcId))) {
			filter.filterSixSeatLuxury = true
			break
		}
	}

	return filter
}

func (lf *LuxuryProductFilter) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	var (
		allRemoved = make([]models.ProductCategory, 0)
	)

	if len(products) <= 0 || !lf.filterSixSeatLuxury {
		return allRemoved, ""
	}

	for _, product := range products {
		if isSixLux(ctx, product.ProductCategory) {
			allRemoved = append(allRemoved, models.ProductCategory(product.ProductCategory))
		}
	}

	return allRemoved, ""
}

func isSupport(ctx context.Context, data *models.BaseReqData) bool {
	ABparam := data.GetApolloParam()
	return apollo.FeatureToggle(ctx, "six_seat_lux_toggle", ABparam["pid"], ABparam)
}

func isSixLux(ctx context.Context, pcid int64) bool {
	return apollo.FeatureToggle(ctx, "six_seat_lux_judge", "", map[string]string{"product_category": strconv.FormatInt(pcid, 10)})
}
