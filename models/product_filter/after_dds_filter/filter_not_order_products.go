package after_dds_filter

import (
	"context"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/user_type"
)

type FilterNoOrderProducts struct {
	orderProducts   []models.ProductCategory
	orderProductMap map[models.ProductCategory]int64
	filterProducts  []models.ProductCategory
}

func NewFilterNoOrderProducts(ctx context.Context, estimatePcID string, userType int32) *FilterNoOrderProducts {
	var productMap = make(map[models.ProductCategory]int64)
	filter := &FilterNoOrderProducts{}
	// 企业用户需要保留快车支持拆费
	if user_type.IsBusinessUser(userType) {
		filter.orderProducts = append(filter.orderProducts, estimate_pc_id.EstimatePcIdFastCar)
	}

	// 泛快特惠需要依赖快车
	if util.ToInt(estimatePcID) == estimate_pc_id.EstimatePcIdFastSpecialRate {
		filter.orderProducts = append(filter.orderProducts, estimate_pc_id.EstimatePcIdFastCar)
	}

	filter.orderProducts = append(filter.orderProducts, models.ProductCategory(util.ToInt64(estimatePcID)))

	for _, pcid := range filter.orderProducts {
		productMap[pcid] = 1
	}
	filter.orderProductMap = productMap

	return filter
}

func (f *FilterNoOrderProducts) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	for _, pItem := range products {
		value, ok := f.orderProductMap[models.ProductCategory(pItem.ProductCategory)]
		if !ok || value != 1 {
			// 过滤未发单品类
			f.filterProducts = append(f.filterProducts, models.ProductCategory(pItem.ProductCategory))
		}
	}

	return f.filterProducts, ""
}
