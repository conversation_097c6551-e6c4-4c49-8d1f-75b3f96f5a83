package after_dds_filter

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

const (
	ChaoZhiDaGroupId = 10
)

type ChaoZhiDaAccess struct {
	access bool
}

func NewChaoZhiDaAccess(ctx context.Context, reqFrom string, reqData *models.BaseReqData) *ChaoZhiDaAccess {
	return &ChaoZhiDaAccess{
		access: reqFrom == V3FORM || reqFrom == V4FORM,
	}
}

func (f *ChaoZhiDaAccess) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	var toBeRemoved []models.ProductCategory
	if f.access {
		return toBeRemoved, ""
	} else {
		for _, p := range products {
			if p.LevelType == 201 {
				toBeRemoved = append(toBeRemoved, models.ProductCategory(p.ProductCategory))
			}
		}
		return toBeRemoved, ""
	}
}
