package after_dds_filter

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type PbdFilterCurrentProducts struct {
	needProducts map[int64]struct{}
}

func NewPbdFilterCurrentProducts(ctx context.Context, needPcIds []int32) *PbdFilterCurrentProducts {
	if len(needPcIds) == 0 {
		return nil
	}

	filter := &PbdFilterCurrentProducts{
		needProducts: make(map[int64]struct{}),
	}
	for _, p := range needPcIds {
		filter.needProducts[int64(p)] = struct{}{}
	}

	return filter
}

func (f *PbdFilterCurrentProducts) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	ret := []models.ProductCategory{}

	for _, p := range products {
		if _, hit := f.needProducts[p.ProductCategory]; !hit {
			ret = append(ret, models.ProductCategory(p.ProductCategory))
		}
	}

	return ret, ""
}
