package after_dds_filter

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"

	"git.xiaojukeji.com/gulfstream/bronze-door-sdk-go/common/utils"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
)

type AnyCarFilterTCSendProductsFilter struct {
	userType          int32
	currentProductIDs []int64
}

func NewAnyCarFilterTCSendProductsFilter(ctx context.Context, multiRequireProduct map[string]dos.RequiredProductStruct, reqData *models.BaseReqData) *AnyCarFilterTCSendProductsFilter {
	if len(multiRequireProduct) == 0 {
		return nil
	}

	ABparam := reqData.GetApolloParam()
	if !apollo.FeatureToggle(ctx, "tc_send_product_filter_toggle", ABparam["pid"], ABparam) {
		return nil
	}

	filter := &AnyCarFilterTCSendProductsFilter{}
	for _, p := range multiRequireProduct {
		if utils.InArrayInt64(int64(p.ProductId), []int64{
			product_id.ProductIdCaoCaoFastCar,
			product_id.ProductIdHaLuoFastCar,
			product_id.ProductIdGuangQiFastCar,
			product_id.ProductIdTThreeFastCar,
		}) {
			filter.currentProductIDs = append(filter.currentProductIDs, int64(p.ProductId))
		}

	}
	return filter
}

func (f *AnyCarFilterTCSendProductsFilter) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	rst := make([]models.ProductCategory, 0)
	if len(f.currentProductIDs) > 0 {
		for _, p := range products {
			if utils.InArrayInt64(p.ProductID, f.currentProductIDs) {
				rst = append(rst, models.ProductCategory(p.ProductCategory))
			}
		}
	}
	return rst, ""
}
