package after_dds_filter

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/order"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
)

type APlusAbFilter struct {
	remove bool
}

func NewAPlusAbFilter(ctx context.Context, extraType string, reqData *models.BaseReqData) *APlusAbFilter {
	filter := &APlusAbFilter{remove: false}
	if reqData == nil {
		return filter
	}
	// 不跨城
	if reqData.AreaInfo.Area != reqData.AreaInfo.ToArea {
		filter.remove = true
		return filter
	}

	params := map[string]string{
		"phone":         reqData.PassengerInfo.Phone,
		"city":          strconv.FormatInt(int64(reqData.AreaInfo.City), 10),
		"menu_id":       reqData.CommonInfo.MenuID,
		"access_key_id": strconv.FormatInt(int64(reqData.CommonInfo.AccessKeyID), 10),
		"app_version":   reqData.CommonInfo.AppVersion,
		"channel":       strconv.FormatInt(reqData.CommonInfo.Channel, 10),
	}
	// 开城Apollo
	if !apollo.FeatureToggle(ctx, "gs_aplus_open_city_switch", strconv.FormatInt(reqData.PassengerInfo.PID, 10), params) {
		filter.remove = true
		log.Trace.Infof(ctx, trace.DLTagUndefined, "aplus check switch fail with params %v", params)
		return filter
	}

	// Athena开城
	if ok, assign := apollo.FeatureExp(ctx, "athena_aplus_flash_delta_gmv_ab_v2", strconv.FormatInt(reqData.PassengerInfo.PID, 10), params); ok {
		if assign.GetGroupName() == "treatment_group" {
			filter.remove = true
		}

		intExtraType, _ := strconv.ParseUint(extraType, 10, 64)
		if assign.GetGroupName() == "queue_group" && !order.IsLineUpOrder(intExtraType) {
			log.Trace.Infof(ctx, trace.DLTagUndefined, "aplus check gmv ab fail with extra_type %v", intExtraType)
			filter.remove = true
		}
	}
	return filter
}

func (f *APlusAbFilter) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	if f.remove {
		return []models.ProductCategory{estimate_pc_id.EstimatePcIdAplus}, ""
	} else {
		return nil, ""
	}
}
