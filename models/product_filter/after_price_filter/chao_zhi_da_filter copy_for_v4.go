package after_price_filter

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type ChaoZhiDaFilterForV4 struct{}

func NewChaoZhiDaFilterForV4() *ChaoZhiDaFilterForV4 {
	return &ChaoZhiDaFilterForV4{}
}

func (f *ChaoZhiDaFilterForV4) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) []models.ProductCategory {
	var (
		allRemoved = make([]models.ProductCategory, 0)
		// chaoZhiDaMap  = make(map[int64]bool, 0)
		chaoZhiDaList = make([]models.ProductCategory, 0)
	)

	// for _, p := range biz_runtime.GetAggregationCars(ctx, ChaoZhiDaGroupId, products) {
	// 	chaoZhiDaMap[p] = true
	// }

	// 当前列表中包含的所有超值达品类
	for _, pfull := range products {
		if pfull.GetLevelType() == 201 {
			chaoZhiDaList = append(chaoZhiDaList, models.ProductCategory(pfull.GetProductCategory()))
		}
	}

	// 端不支持展示超值达盒子
	// if !isSupport(ctx, *products[0].BaseReqData) {
	// 	return chaoZhiDaList
	// }

	// 远必省盒子车型价格不一致 过滤所有远必省特价车
	ok, chaoZhiDaProds := priceEqual(ctx, products, 201)
	if !ok {
		return chaoZhiDaProds
	}
	return allRemoved
}
