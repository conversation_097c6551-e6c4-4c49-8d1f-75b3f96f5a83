package after_price_filter

import (
	"context"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_category"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type AfterPriceFilterForV4 struct {
	baseReq *models.BaseReqData // 请求信息
}

func NewAfterPriceFilterForV4(ctx context.Context, req *models.BaseReqData) *AfterPriceFilterForV4 {
	return &AfterPriceFilterForV4{
		baseReq: req,
	}
}

func (c *AfterPriceFilterForV4) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) (res []models.ProductCategory) {
	for _, p := range products {
		// 过滤车大没价格
		if p.GetProductCategory() == product_category.ProductCategorySpaciousCar {
			if len(p.GetBillFeeDetailInfo()) <= 0 || p.GetBillFeeDetailInfo()["talos_spacious_car_selection_fee"] > 0 {
				res = append(res, models.ProductCategory(p.GetProductCategory()))
			}
		}

		// 香港一口价 价格过滤
		// todo 暂时没看到这段代码
		// if p.GetProductCategory() == ProductCategory.ProductCategoryHKCapTaxi {
		// 	hkPriceThreshold := GetHKCapPriceThreshold(ctx, p)
		// 	if p.GetPreTotalFee() < hkPriceThreshold {
		// 		res = append(res, models.ProductCategory(p.GetProductCategory()))
		// 	}
		// }

	}
	return res
}
