package after_price_filter

import (
	"context"
	"encoding/json"
	"fmt"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/source_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
	"sort"
	"strconv"
	"time"
)

const (
	// IntercityDistanceThreshold 主表单配置
	IntercityDistanceThreshold = "gs_carpool_to_intercity_distance_threshold"
	// IntercityDistanceThresholdIndividuation 个性化配置
	IntercityDistanceThresholdIndividuation = "gs_carpool_to_intercity_distance_threshold_new"
	// publicLog
	PublicKey = "g_estimate_distance_log"
	// 默认进入时
	InitReadStatus = -2
	// 配置获取成功
	ReadSuccessStatus = 1
	// 配置未获取成功
	ReadFailStatus = -1
)

type IntercityAnycarFilter struct {
	baseReq *models.BaseReqData
}

type innerDistanceConvert struct {
	FromCityID int
	ToCity     int
	PcID       int
	PageType   int
	Phone      string
	Channel    int64
}

type tIntercityDistenceFilterConfig struct {
	UpperLimit        int64 // 上限
	SameCityThreshold int64 // 不跨城的阈值
	DiffCityThreshold int64 // 跨城的阈值
}

type OpenFenceDistanceLimit struct {
	OpenFenceDistanceLower string `json:"open_fence_distance_lower_limit"`
	OpenFenceDistanceUpper string `json:"open_fence_distance_upper_limit"`
}

type CrossCityDistanceLimit struct {
	CrossCityDistanceLower string `json:"cross_city_distance_lower"`
	CrossCityDistanceUpper string `json:"cross_city_distance_upper"`
}

type DistanceLimitConf struct {
	OpenFenceDistanceLimit OpenFenceDistanceLimit `json:"open_fence_distance_limit"`
	CrossCityDistanceLimit CrossCityDistanceLimit `json:"cross_city_distance_limit"`
}

// NewIntercityAnycarFilter 本过滤器为城际拼车小车迁移anycar设置，本次过滤器无法在price之前做，因为需要对距离做过滤。
func NewIntercityAnycarFilter(baseReq *models.BaseReqData) *IntercityAnycarFilter {
	return &IntercityAnycarFilter{
		baseReq: baseReq,
	}
}

func (filter *IntercityAnycarFilter) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) []models.ProductCategory {
	if len(products) == 0 {
		return nil
	}

	// 外层有判断page_type，此处为了预防错误使用，防御性编程
	if filter.baseReq.CommonInfo.PageType != page_type.PageTypeIntercityEstimate {
		return nil
	}
	// 对products的金额进行排序，后续展示城际三方取最低金额
	sort.Slice(products, func(i, j int) bool {
		return products[i].GetEstimateFee() < products[j].GetEstimateFee()
	})
	// 记录需要过滤的品类
	removePcList := make([]models.ProductCategory, 0, 0)
	fromCity, toCity := filter.baseReq.AreaInfo.Area, filter.baseReq.AreaInfo.ToArea
	predictDistance := filter.getPredictDistance(products[0])
	// 记录下剩余的品类
	remainSelfPcList := make([]models.ProductCategory, 0, 0)
	remainThirdPcList := make([]models.ProductCategory, 0, 0)
	for _, prod := range products {
		if !carpool.IsIntercitySmallCar(prod.Product.CarpoolType, prod.Product.ComboType) {
			removePcList = append(removePcList, models.ProductCategory(prod.Product.ProductCategory))
			continue
		}
		conf := filter.loadIntercityConf(ctx, filter.baseReq, int(fromCity), int(toCity), int(prod.Product.ProductCategory),
			int(filter.baseReq.CommonInfo.PageType), filter.baseReq.PassengerInfo.Phone, filter.baseReq.CommonInfo.Channel)
		if fromCity == toCity {
			if !(predictDistance > conf.SameCityThreshold && predictDistance < conf.UpperLimit) {
				removePcList = append(removePcList, models.ProductCategory(prod.Product.ProductCategory))
				continue
			}
		} else {
			if !(predictDistance > conf.DiffCityThreshold && predictDistance < conf.UpperLimit) {
				removePcList = append(removePcList, models.ProductCategory(prod.Product.ProductCategory))
				continue
			}
		}
		// 选择座位数大于当前品类可选做大座位数，过滤
		if prod.GetBizInfo().CarpoolSeatNum > prod.GetBizInfo().MaxCarpoolSeatNum {
			removePcList = append(removePcList, models.ProductCategory(prod.Product.ProductCategory))
			continue
		}
		// 记录下剩余的城际自营 or 三方品类
		if prod.Product.ProductCategory == estimate_pc_id.EstimatePcIdCarpoolInter {
			remainSelfPcList = append(remainSelfPcList, models.ProductCategory(prod.Product.ProductCategory))
		} else {
			remainThirdPcList = append(remainThirdPcList, models.ProductCategory(prod.Product.ProductCategory))
		}
	}
	now := time.Now()
	// product里面的时段是解析过的，所有品类时间片一样，取第一个判断即可
	if util.IsCurrentTimeDepartureRange(products[0].Product.BizInfo.DepartureRange, now) {
		return removePcList
	}
	// 如果有城际三方，将自营过滤，留一个三方。
	if len(remainThirdPcList) > 0 {
		removePcList = append(removePcList, remainSelfPcList...)
		removePcList = append(removePcList, remainThirdPcList[1:]...)

	}
	return removePcList
}

func (filter *IntercityAnycarFilter) getPredictDistance(prod *biz_runtime.ProductInfoFull) int64 {
	if prod == nil {
		return 0
	}
	estimateFee := prod.GetDirectEstimatePrice()
	if estimateFee == nil {
		return 0
	}
	if meter := estimateFee.GetFeeAttributes().GetInt("driver_metre"); meter != nil {
		return *meter
	}
	return 0
}

// 主表单&个性化配置公里数获取
// 0、个性化配置有，读取个性化配置control_group 或者 分流group(关注，主表单配置读取内，还包了一个城际的个性化配置...老逻辑)
// 1、主表单配置有，读取主表单配置control_group 或者 分流group
// 开量过程，个性化配置通过source_id、page_type、channel，划分配置内容，使主表单配置失效。
func (filter *IntercityAnycarFilter) loadIntercityConf(ctx context.Context, baseReq *models.BaseReqData, fromCityID int, toCity int, pcID int, pageType int, phone string, channel int64) *tIntercityDistenceFilterConfig {
	distanceConvert := &innerDistanceConvert{
		FromCityID: fromCityID,
		Phone:      phone,
		PcID:       pcID,
		PageType:   pageType,
		ToCity:     toCity,
		Channel:    channel,
	}

	// 先读取对应的非互斥的配置，如果配置allow，则读取其control-group或者by city group
	intercityDistanceFilterConfig := filter.GetIndividuationDistanceConfig(ctx, baseReq, *distanceConvert)
	if intercityDistanceFilterConfig != nil {
		return intercityDistanceFilterConfig
	}

	// 走原有的互斥逻辑
	return filter.GetMainDistanceConfig(ctx, baseReq, *distanceConvert)
}

// 这里过滤非主表单内，无需互斥，会进行统一的公里，当然，入口也需要by不同渠道进行公里限制配置
func (filter *IntercityAnycarFilter) GetIndividuationDistanceConfig(ctx context.Context, baseReq *models.BaseReqData, distanceConvert innerDistanceConvert) *tIntercityDistenceFilterConfig {
	param := ApolloModel.NewUser("").
		With("city", strconv.Itoa(distanceConvert.FromCityID)).
		With("phone", distanceConvert.Phone).
		With("product_category", strconv.Itoa(distanceConvert.PcID)).
		With("page_type", strconv.Itoa(distanceConvert.PageType)).
		With("source_id", strconv.Itoa(int(baseReq.CommonInfo.SourceID))).
		With("to_city", strconv.Itoa(distanceConvert.ToCity)).
		With("channel", strconv.Itoa(int(distanceConvert.Channel)))
	toggle, err := ApolloSDK.FeatureToggle(IntercityDistanceThresholdIndividuation, param)
	if err != nil || toggle == nil || !toggle.IsAllow() {
		writePublic(ctx, distanceConvert, ReadFailStatus, IntercityDistanceThresholdIndividuation, "")
		return nil
	}

	intercityDistanceFilterConfig := &tIntercityDistenceFilterConfig{
		UpperLimit:        100000,
		SameCityThreshold: 20000,
		DiffCityThreshold: 10000,
	}
	jsonStr, _ := json.Marshal(toggle.GetAssignment())
	fmt.Printf(string(jsonStr))
	if assign := toggle.GetAssignment(); assign != nil {
		// 记录对应命中的分组。
		intercityDistanceFilterConfig.UpperLimit = util.IntParseWithDefault(assign.GetParameter("upper_limit", "100000"), 100000)
		intercityDistanceFilterConfig.SameCityThreshold = util.IntParseWithDefault(assign.GetParameter("same_city", "30000"), 30000)
		intercityDistanceFilterConfig.DiffCityThreshold = util.IntParseWithDefault(assign.GetParameter("diff_city", "20000"), 20000)
		writePublic(ctx, distanceConvert, ReadSuccessStatus, IntercityDistanceThresholdIndividuation, toggle.GetAssignment().GetGroupName())
		return intercityDistanceFilterConfig
	}

	writePublic(ctx, distanceConvert, ReadSuccessStatus, IntercityDistanceThresholdIndividuation, "")
	return intercityDistanceFilterConfig
}

// 这里，过滤主表单内互斥公里，但需要注意，其下没读到IntercityDistanceThreshold时，会触发兜底城际的老公里配置。
func (filter *IntercityAnycarFilter) GetMainDistanceConfig(ctx context.Context, baseReq *models.BaseReqData, distanceConvert innerDistanceConvert) *tIntercityDistenceFilterConfig {
	param := ApolloModel.NewUser("").
		With("city", strconv.Itoa(distanceConvert.FromCityID)).
		With("phone", distanceConvert.Phone).
		With("product_category", strconv.Itoa(distanceConvert.PcID)).
		With("page_type", strconv.Itoa(distanceConvert.PageType)).
		With("source_id", strconv.Itoa(int(baseReq.CommonInfo.SourceID))).
		With("to_city", strconv.Itoa(distanceConvert.ToCity)).
		With("channel", strconv.Itoa(int(distanceConvert.Channel)))

	toggle, err := ApolloSDK.FeatureToggle(IntercityDistanceThreshold, param)
	writePublic(ctx, distanceConvert, InitReadStatus, IntercityDistanceThreshold, "")
	intercityDistanceFilterConfig := &tIntercityDistenceFilterConfig{
		UpperLimit:        100000,
		SameCityThreshold: 30000,
		DiffCityThreshold: 20000,
	}

	if err != nil || toggle == nil || !toggle.IsAllow() {
		writePublic(ctx, distanceConvert, ReadFailStatus, IntercityDistanceThreshold, "")
		log.Trace.Warnf(ctx, consts.TagApolloLoadErr, "fail to check gs_carpool_to_intercity_distance_threshold with err %v, and toggle %v", err, toggle)
		return intercityDistanceFilterConfig
	}

	if assign := toggle.GetAssignment(); assign != nil &&
		assign.GetGroupName() != "control_group" {
		intercityDistanceFilterConfig.UpperLimit = util.IntParseWithDefault(assign.GetParameter("upper_limit", "100000"), 100000)
		intercityDistanceFilterConfig.SameCityThreshold = util.IntParseWithDefault(assign.GetParameter("same_city", "30000"), 30000)
		intercityDistanceFilterConfig.DiffCityThreshold = util.IntParseWithDefault(assign.GetParameter("diff_city", "20000"), 20000)
		writePublic(ctx, distanceConvert, ReadSuccessStatus, IntercityDistanceThreshold, assign.GetGroupName())
		return intercityDistanceFilterConfig
	}

	writePublic(ctx, distanceConvert, ReadSuccessStatus, IntercityDistanceThreshold, "")
	var sourceID int32
	if baseReq != nil {
		sourceID = baseReq.CommonInfo.SourceID
	}

	pageType := distanceConvert.PageType
	fromCityID := distanceConvert.FromCityID
	if pageType == page_type.PageTypeIntercityEstimate ||
		pageType == page_type.PageTypeCarpoolTabEstimate ||
		pageType == page_type.PageTypeLowCarpoolEstimate ||
		pageType == page_type.PageTypeUndefined ||
		sourceID == source_id.SourceIDPBD {
		confCity := filter.getIntercityConfigByCity(ctx, fromCityID, param)
		if confCity == nil {
			return intercityDistanceFilterConfig
		}
		return confCity
	}
	return intercityDistanceFilterConfig
}

func writePublic(ctx context.Context,
	distanceConvert innerDistanceConvert,
	readStatus int,
	apolloName, groupName string) {
	logInfo := make(map[string]interface{})
	logInfo["from_city"] = distanceConvert.FromCityID
	logInfo["to_city"] = distanceConvert.ToCity
	logInfo["pc_id"] = distanceConvert.PcID
	logInfo["page_type"] = distanceConvert.PageType
	logInfo["phone"] = distanceConvert.Phone
	logInfo["group_name"] = groupName
	logInfo["apollo_name"] = apolloName
	logInfo["read_status"] = readStatus

	log.Public.Public(ctx, PublicKey, logInfo)
}

func (filter *IntercityAnycarFilter) getIntercityConfigByCity(ctx context.Context, cityID int, user *ApolloModel.User) *tIntercityDistenceFilterConfig {
	openFenceDistanceLimit := OpenFenceDistanceLimit{}
	crossCityDistanceLimit := CrossCityDistanceLimit{}

	if tg2, err2 := ApolloSDK.FeatureToggle("gs_intercity_distance_open_rule_by_city", user); err2 == nil && tg2.IsAllow() && tg2.GetAssignment() != nil {
		sameCityLowLimit := tg2.GetAssignment().GetParameter("same_city_low_limit", "30000")
		sameCityUpperLimit := tg2.GetAssignment().GetParameter("same_city_upper_limit", "100000")

		diffCityLowLimit := tg2.GetAssignment().GetParameter("diff_city_low_limit", "10000")
		diffCityUpperLimit := tg2.GetAssignment().GetParameter("diff_city_upper_limit", "100000")

		openFenceDistanceLimit.OpenFenceDistanceLower = sameCityLowLimit
		openFenceDistanceLimit.OpenFenceDistanceUpper = sameCityUpperLimit

		crossCityDistanceLimit.CrossCityDistanceLower = diffCityLowLimit
		crossCityDistanceLimit.CrossCityDistanceUpper = diffCityUpperLimit
	}

	distanceLimitConf := &DistanceLimitConf{
		OpenFenceDistanceLimit: openFenceDistanceLimit,
		CrossCityDistanceLimit: crossCityDistanceLimit,
	}
	upperLimit := int64(0)
	sameCityThreshold := int64(0)
	diffCityThreshold := int64(0)
	crossCityDistanceLower, _ := strconv.Atoi(distanceLimitConf.CrossCityDistanceLimit.CrossCityDistanceLower)
	crossCityDistanceUpper, _ := strconv.Atoi(distanceLimitConf.CrossCityDistanceLimit.CrossCityDistanceUpper)
	if crossCityDistanceLower != 0 || crossCityDistanceUpper != 0 {
		if int64(crossCityDistanceUpper) > upperLimit {
			upperLimit = int64(crossCityDistanceUpper)

		}
		diffCityThreshold = int64(crossCityDistanceLower)
	}
	openFenceDistanceLower, _ := strconv.Atoi(distanceLimitConf.OpenFenceDistanceLimit.OpenFenceDistanceLower)
	openFenceDistanceUpper, _ := strconv.Atoi(distanceLimitConf.OpenFenceDistanceLimit.OpenFenceDistanceUpper)
	if openFenceDistanceLower != 0 || openFenceDistanceUpper != 0 {
		if int64(openFenceDistanceUpper) > upperLimit {
			upperLimit = int64(openFenceDistanceUpper)

		}
		sameCityThreshold = int64(openFenceDistanceLower)
	}
	if upperLimit == 0 {
		upperLimit = 100000
	}
	if diffCityThreshold == 0 {
		diffCityThreshold = 30000
	}
	return &tIntercityDistenceFilterConfig{
		UpperLimit:        upperLimit,
		SameCityThreshold: sameCityThreshold,
		DiffCityThreshold: diffCityThreshold,
	}

}
