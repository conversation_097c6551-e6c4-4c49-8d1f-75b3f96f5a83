package after_price_filter

import (
	"context"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_category"
	ProductCategory "git.xiaojukeji.com/gulfstream/biz-common-go/v6/category"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type AfterPriceFilter struct {
	baseReq *models.BaseReqData // 请求信息
}

func NewAfterPriceFilter(ctx context.Context, req *models.BaseReqData) *AfterPriceFilter {
	return &AfterPriceFilter{
		baseReq: req,
	}
}

func (c *AfterPriceFilter) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) (res []models.ProductCategory) {
	for _, p := range products {

		// 过滤车大没价格
		if p.GetProductCategory() == product_category.ProductCategorySpaciousCar {
			if len(p.GetBillFeeDetailInfo()) <= 0 || p.GetBillFeeDetailInfo()["talos_spacious_car_selection_fee"] <= 0 {
				res = append(res, models.ProductCategory(p.GetProductCategory()))
			}
		}

		// 香港一口价 价格过滤
		if p.GetProductCategory() == ProductCategory.ProductCategoryHKCapTaxi {
			hkPriceThreshold := GetHKCapPriceThreshold(ctx, p)
			if p.GetPreTotalFee() < hkPriceThreshold {
				res = append(res, models.ProductCategory(p.GetProductCategory()))
			}
		}

	}
	return res
}

func GetHKCapPriceThreshold(ctx context.Context, product *biz_runtime.ProductInfoFull) float64 {
	threshold := "90"
	isAllow, assignParams := apollo.GetParameters("hk_cap_taxi_price_threshold", "", map[string]string{"phone": product.GetUserPhone()})
	if isAllow && assignParams != nil && len(assignParams) > 0 {
		threshold = assignParams["threshold"]
	}

	return util.ToFloat64(threshold)
}
