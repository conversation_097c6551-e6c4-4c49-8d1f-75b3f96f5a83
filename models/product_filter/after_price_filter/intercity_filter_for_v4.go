package after_price_filter

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/combo_type"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/order"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/tripcloud-common-go/models/product_category"
	"github.com/spf13/cast"
	apolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

//     private $_aGuideProductCategory = [OrderEstimatePcId::EstimatePcIdTrainTicket, OrderEstimatePcId::EstimatePcIdCarpoolSFCar, OrderEstimatePcId::EstimatePcIdCarpoolCrossSFCar];

var (
	GuideProductCategory = []int64{
		estimate_pc_id.EstimatePcIdTrainTicket,
		estimate_pc_id.EstimatePcIdCarpoolSFCar,
		estimate_pc_id.EstimatePcIdCarpoolCrossSFCar,
	}
)

type IntercityFilterForV4 struct {
	ctx         context.Context
	productsGen *biz_runtime.ProductsGenerator

	showIntercityClassifyTab bool
}

func NewIntercityFilterForV4(ctx context.Context, productsGen *biz_runtime.ProductsGenerator) *IntercityFilterForV4 {
	if GetIntercityDisplayFromAthenaSwitch(ctx, productsGen.BaseReqData) {
		return nil
	}
	return &IntercityFilterForV4{
		ctx:         ctx,
		productsGen: productsGen,
	}
}

func (b *IntercityFilterForV4) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) []models.ProductCategory {
	if len(products) == 0 {
		return nil
	}
	var result []models.ProductCategory

	// 3. 获取实验参数
	isShowSFC := b.IsShowSFC()
	isShowTrain := b.IsShowTrain()
	isShowStationBus := b.IsShowStationBus()

	// 4. 判断是否命中城际tab
	if !GetBShowIntercityClassifyTab(ctx, b.productsGen.BaseReqData, products) {
		// 未命中，过滤顺风车和火车票
		result = append(result, models.ProductCategory(estimate_pc_id.EstimatePcIdTrainTicket))
		result = append(result, models.ProductCategory(estimate_pc_id.EstimatePcIdCarpoolSFCar))
		result = append(result, models.ProductCategory(estimate_pc_id.EstimatePcIdCarpoolCrossSFCar))
	} else {
		// 命中，根据实验策略过滤
		for _, product := range products {
			if (product.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolSFCar || product.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolCrossSFCar) && !isShowSFC {
				result = append(result, models.ProductCategory(product.GetProductCategory()))
			} else if product.GetProductCategory() == estimate_pc_id.EstimatePcIdTrainTicket && !isShowTrain {
				result = append(result, models.ProductCategory(product.GetProductCategory()))
			} else if product != nil && product.GetCarpoolType() == consts.CarPoolTypeInterCityStation && !isShowStationBus {
				result = append(result, models.ProductCategory(product.GetProductCategory()))
			}
		}
	}

	return result
}

// 获取是否展示城际tab
func GetBShowIntercityClassifyTab(ctx context.Context, req *models.BaseReqData, products []*biz_runtime.ProductInfoFull) bool {
	isShow := isShowNumCheck(ctx, req, products)
	return buildShowIntercityClassifyTab(ctx, req, products) && isShow
}

// 是否展示顺风车
func (b *IntercityFilterForV4) IsShowSFC() bool {
	expResult := b.GetExpResult()
	if expResult.IsAllow() && expResult.GetAssignment().GetParameter("is_show_sfc", "0") == "1" {
		return true
	}
	return false
}

// 是否展示火车票
func (b *IntercityFilterForV4) IsShowTrain() bool {
	expResult := b.GetExpResult()
	if expResult.IsAllow() && expResult.GetAssignment().GetParameter("is_show_train", "0") == "1" {
		return true
	}
	return false
}

// 是否展示城际大巴
func (b *IntercityFilterForV4) IsShowStationBus() bool {
	expResult := b.GetExpResult()
	if expResult.IsAllow() && expResult.GetAssignment().GetParameter("is_show_station_bus", "0") == "1" {
		return true
	}
	return false
}

// 判断是否目标导流品类
func (b *IntercityFilterForV4) isTargetGuide(product *biz_runtime.ProductInfoFull) bool {
	for _, cat := range GuideProductCategory {
		if product.GetProductCategory() == int64(cat) {
			return true
		}
	}
	if product.GetCarpoolType() == consts.CarPoolTypeInterCityStation || product.GetComboType() == combo_type.ComboTypeCarpoolInterCity {
		return true
	}
	return false
}

// GetExpResult 获取实验结果（带缓存）
// todo 看看缓存怎么搞
func (b *IntercityFilterForV4) GetExpResult() *apolloModel.ToggleResult {
	toggleResult := apollo.GetHitToggleByNamespace(b.ctx, cast.ToString(b.productsGen.BaseReqData.PassengerInfo.PID),
		"intercity_dynamic_tab", b.productsGen.BaseReqData.GetApolloParam())
	return toggleResult
}

// 检查是否满足展示数量的条件
func isShowNumCheck(ctx context.Context, req *models.BaseReqData, products []*biz_runtime.ProductInfoFull) bool {
	iIsShowNumControl := 2
	allow, parameters := apollo.GetParameters("intercity_tab_show_num_toggle", cast.ToString(req.PassengerInfo.PID), req.GetApolloParam())
	if allow {
		if showNumControl, ok := parameters["is_show_num_control"]; ok {
			iIsShowNumControl = cast.ToInt(showNumControl)
		}
	}
	iGuideProductNum := 0
	for _, product := range products {
		if carpool.IsTargetGuide(product.GetProductCategory(), product.GetCarpoolType(), product.GetComboType()) {
			iGuideProductNum++
		}
	}

	if iGuideProductNum < iIsShowNumControl {
		return false
	}

	return true
}

// buildShowIntercityClassifyTab 构建是否显示城际分类tab的逻辑
func buildShowIntercityClassifyTab(ctx context.Context, req *models.BaseReqData, products []*biz_runtime.ProductInfoFull) bool {
	// 检查是否命中分类tab
	bHitClassifyTab := tab.IsClassifyTab(req.CommonInfo.TabId)
	if !bHitClassifyTab {
		return false
	}

	// 检查订单类型是否为即时单
	if req.CommonInfo.OrderType != order.OrderTypeNow {
		return false
	}

	// 获取实验结果
	toggleResult := apollo.GetHitToggleByNamespace(ctx, cast.ToString(req.PassengerInfo.PID),
		"intercity_dynamic_tab", req.GetApolloParam())
	if !toggleResult.IsAllow() {
		return false
	}

	// 检查是否显示城际tab参数
	if cast.ToInt(toggleResult.GetAssignment().GetParameter("is_show_intercity_tab", "0")) != 1 {
		return false
	}

	// 同城距离检查
	if req.AreaInfo.Area == req.AreaInfo.ToArea {
		if !sameCityDistanceCheck(products, toggleResult) {
			return false
		}
	}

	// 跨城距离检查
	if req.AreaInfo.Area != req.AreaInfo.ToArea {
		if !diffCityDistanceCheck(products, toggleResult) {
			return false
		}
	}

	return true
}

// sameCityDistanceCheck 同城距离检查
func sameCityDistanceCheck(products []*biz_runtime.ProductInfoFull, toggleResult *apolloModel.ToggleResult) bool {
	if len(products) == 0 {
		return false
	}

	var targetProduct *biz_runtime.ProductInfoFull
	for _, product := range products {
		if product.GetProductCategory() == product_category.ProductCategoryFast {
			targetProduct = product
			break
		}
	}

	if targetProduct == nil || targetProduct.GetBillInfo() == nil {
		return false
	}

	driverMetre := targetProduct.GetBillDriverMetre()
	sameCityDistance := cast.ToInt64(toggleResult.GetAssignment().GetParameter("same_city_distance", "30000"))
	if driverMetre < sameCityDistance {
		return false
	}

	return true
}

// diffCityDistanceCheck 跨城距离检查
func diffCityDistanceCheck(products []*biz_runtime.ProductInfoFull, toggleResult *apolloModel.ToggleResult) bool {
	if len(products) == 0 {
		return false
	}

	var targetProduct *biz_runtime.ProductInfoFull
	for _, product := range products {
		if product.GetProductCategory() == product_category.ProductCategoryFast {
			targetProduct = product
			break
		}
	}

	if targetProduct == nil || targetProduct.GetBillInfo() == nil {
		return false
	}

	driverMetre := targetProduct.GetBillDriverMetre()
	diffCityDistance := cast.ToInt64(toggleResult.GetAssignment().GetParameter("diff_city_distance", "10000"))
	if driverMetre < diffCityDistance {
		return false
	}

	return true
}

func GetIntercityDisplayFromAthenaSwitch(ctx context.Context, req *models.BaseReqData) bool {
	bHitClassifyTab := tab.IsClassifyTab(req.CommonInfo.TabId)
	if !bHitClassifyTab {
		return false
	}

	return true
}
