package after_price_filter

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"strconv"
)

const (
	intercityNightOpen  = "intercity_night_open_toggle"
	intercityNightGuide = "night_intercity_5_guidebar"
	pincheGuideSwitch   = "athena_pinche_guide_switch"
)

type CarpoolGuideBarFilter struct {
	hasIntercityStation bool
	hasPincheche        bool
	hasIntercity        bool
	hasSFC              bool
	hasTrain            bool
	removeIntercitySelf bool
}

// NewCarpoolGuideBarFilter 拼车导流位pk
func NewCarpoolGuideBarFilter(baseReq *models.BaseReqData) *CarpoolGuideBarFilter {
	if access(baseReq) {
		return &CarpoolGuideBarFilter{}
	}
	return nil
}

func access(baseReq *models.BaseReqData) bool {
	// Athena 拼车导流决策迁移计划 Apollo
	params := baseReq.GetApolloParam()
	return apollo.FeatureToggle(nil, pincheGuideSwitch, params["pid"], params)
}

func (c *CarpoolGuideBarFilter) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) []models.ProductCategory {
	var (
		allRemoved = make([]models.ProductCategory, 0)
	)

	if len(products) == 0 {
		return allRemoved
	}

	// 城际拼车自营 根据 运营时间/实验 出导流
	if c.removeIntercitySelf = intercityTimeFilter(ctx, products); c.removeIntercitySelf {
		allRemoved = append(allRemoved, estimate_pc_id.EstimatePcIdCarpoolInter)
	}

	c.buildExistStatus(products)

	// 导流品类pk拼成乐
	if (c.hasIntercityStation || c.hasIntercity || c.hasSFC || c.hasTrain) && c.hasPincheche {
		allRemoved = append(allRemoved, estimate_pc_id.EstimatePcIdLowPriceCarpool)
	}

	return allRemoved
}

func intercityTimeFilter(ctx context.Context, products []*biz_runtime.ProductInfoFull) bool {
	for _, pfull := range products {
		if pfull == nil {
			continue
		}

		if pfull.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolInter {
			pidKey, params := pfull.GetApolloParams(biz_runtime.WithPIDKey)
			if pfull.GetAreaInfo() != nil && pfull.GetAreaInfo().ToArea != 0 {
				params["to_city"] = string(pfull.GetAreaInfo().ToArea)
			}
			if pfull.GetBizInfo() != nil && pfull.GetBizInfo().ComboID != 0 {
				params["route_id"] = strconv.FormatInt(pfull.GetBizInfo().ComboID, 10)
			}
			if apollo.FeatureToggle(ctx, intercityNightOpen, pidKey, params) {
				return false
			}
			if apollo.FeatureToggle(ctx, intercityNightGuide, pidKey, params) {
				return true
			}
		}
	}
	return false
}

func (c *CarpoolGuideBarFilter) buildExistStatus(products []*biz_runtime.ProductInfoFull) {
	for _, pfull := range products {
		if pfull == nil {
			continue
		}
		// 站点巴士
		if pfull.GetCarpoolType() == consts.CarpoolTypeIntercityStation {
			c.hasIntercityStation = true
			continue
		}
		// 城际自营
		if pfull.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolInter && !c.removeIntercitySelf {
			c.hasIntercity = true
			continue
		}
		// 城际三方
		if pfull.GetProductCategory() != estimate_pc_id.EstimatePcIdCarpoolInter && consts.CarpoolType(pfull.GetCarpoolType()) == consts.CarpoolTypeIntercity {
			c.hasIntercity = true
			continue
		}
		// 拼成乐
		if pfull.GetProductCategory() == estimate_pc_id.EstimatePcIdLowPriceCarpool {
			c.hasPincheche = true
			continue
		}
		// 顺风车
		if util.InArrayInt64(pfull.GetProductCategory(), []int64{estimate_pc_id.EstimatePcIdCarpoolSFCar, estimate_pc_id.EstimatePcIdCarpoolCrossSFCar}) {
			c.hasSFC = true
			continue
		}
		// 火车票
		if pfull.GetProductCategory() == estimate_pc_id.EstimatePcIdTrainTicket {
			c.hasTrain = true
			continue
		}
	}
}
