package after_price_filter

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type FarMustCheapAgainstTCFilterForV4 struct {
	productsGen *biz_runtime.ProductsGenerator
}

func NewFarMustCheapAgainstTCFilterForV4(productsGen *biz_runtime.ProductsGenerator) *FarMustCheapAgainstTCFilterForV4 {
	return &FarMustCheapAgainstTCFilterForV4{
		productsGen: productsGen,
	}
}

func (f *FarMustCheapAgainstTCFilterForV4) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) []models.ProductCategory {

	allRemoved := make([]models.ProductCategory, 0)

	if len(products) <= 0 {
		return allRemoved
	}

	ret := biz_runtime.GetTCExperimentParams(ctx, *products[0].BaseReqData)

	var doSupport bool
	anchorConf := getAnchorConf(ctx)
	if anchorConf != nil && anchorConf.ProductCategory != 0 {
		doSupport = true
	}
	isHuaxiaozhuCapOut := ret["isHuaxiaozhuCapOut"]
	needFilterThirdPartyBox := ret["needFilterThirdPartyBox"]
	supportOther := ret["supportOther"]
	hitFarMustCheaperBox := ret["hitFarMustCheaperBox"]
	needFilter := false

	if hitFarMustCheaperBox == "1" {
		for _, product := range products {
			// todo 原逻辑走不到 bHitFarMustCheaperBox 一定是 true
			// 过滤远必省盒子
			// if (!$bHitFarMustCheaperBox
			//     && LayoutBuilder::SUB_GROUP_ID_FAR_MUST_CHEAPER == DecisionV2Service::getInstance()->getSubGroupIDByPcID($oBizProduct->getProductCategory())
			// ) {
			//     unset($aBizProductMap[$iPcID]);
			//     continue;
			// }

			// 过滤三方盒子
			if product.GetSubGroupId() == TCSubGroupId {
				// 不过滤花小猪 拿出盒子
				if isHuaxiaozhuCapOut == "1" && product.GetProductCategory() == KFlowerSp {
					continue
				}
				if needFilterThirdPartyBox == "1" {
					if supportOther == "1" && doSupport {
						pcID := anchorConf.ProductCategory
						coefficient := anchorConf.Coefficient
						var referPrice float64
						for _, prod := range products {
							if prod.GetProductCategory() == pcID {
								referPrice = prod.GetEstimateFee() * coefficient
								break
							}
						}
						thisPrice := product.GetEstimateFee()

						// 读apollo配置
						apolloParams := map[string]string{
							"product_category": cast.ToString(product.GetProductCategory()),
							"product_id":       cast.ToString(product.GetProductId()),
							"business_id":      cast.ToString(product.GetBusinessID()),
						}
						allow := apollo.FeatureToggle(ctx, "support_third_allow_switch", cast.ToString(f.productsGen.BaseReqData.PassengerInfo.PID), apolloParams)
						if allow && thisPrice <= referPrice {
							// todo 逻辑有点深，没看明白，需要关注下每个字段都是干嘛的
							/*
															    public function unsetSubGroupIDByPcID($iPcId, $iGroupID) {
								        if (empty($this->_newSubGroupIDToProducts) || !is_array($this->_newSubGroupIDToProducts)) {
								            return;
								        }

								        if (in_array($iPcId, $this->_newSubGroupIDToProducts[$iGroupID])) {//特定品类出盒子
								            $this->_newSubGroupIDToProducts[$iGroupID] = array_diff($this->_newSubGroupIDToProducts[$iGroupID],[$iPcId]);
								            $this->_newPcIDToSubGroupID[$iPcId]        = 0;
								        }
								    }
							*/
							// DecisionV2Service::getInstance()->unsetSubGroupIDByPcID($oBizProduct->getProductCategory(), LayoutBuilder::SUB_GROUP_ID_SHORT_DISTANCE);
							//f.productsGen.[product.GetProductId()] = int32(0)
							//f.productsGen.UnsetSubGroupIDByPcID(product.GetProductCategory(), TCSubGroupId)
							continue
						}
						allRemoved = append(allRemoved, models.ProductCategory(product.GetProductCategory()))
					}
				}
			}
		}
	} else {
		needFilter = true
	}

	baseEstimateFee := -1.0
	pcIdList := []int64{}
	errorPcID := int64(-1)
	for _, product := range products {
		if product.GetLevelType() != 105 {
			continue
		}
		pcIdList = append(pcIdList, product.GetProductCategory())
		if baseEstimateFee < 0 {
			baseEstimateFee = product.GetEstimateFee()
			continue
		}
		if baseEstimateFee != product.GetEstimateFee() {
			errorPcID = product.GetProductCategory()
		}
	}

	if errorPcID > 0 {
		log.Trace.Warn(ctx, trace.DLTagUndefined, "FarMustCheapAgainstTCFilterForV4: %v", errorPcID)
	}

	if needFilter || errorPcID > 0 {
		for _, pcID := range pcIdList {
			allRemoved = append(allRemoved, models.ProductCategory(pcID))
		}
	}

	return allRemoved
}

// 锚点配置结构
type AnchorConf struct {
	ProductCategory int64   `json:"product_category"`
	Coefficient     float64 `json:"coefficient"`
}

// 获取锚点配置
func getAnchorConf(ctx context.Context) *AnchorConf {
	// 这里需要根据实际的配置获取方式来实现
	// 对应PHP中的 ApolloHelper::getConfigContent('classify_price_axle_config', 'support_pcid_and_coefficient_conf')
	// 暂时返回nil，需要根据实际配置系统实现
	anchorConf := apollo.GetConfig(ctx, "classify_price_axle_config", "support_pcid_and_coefficient_conf")
	if anchorConf != nil {
		anchorConf := AnchorConf{
			ProductCategory: util.ToInt64(anchorConf["product_category"]),
			Coefficient:     util.ToFloat64(anchorConf["coefficient"]),
		}
		return &anchorConf
	}
	return nil
}
