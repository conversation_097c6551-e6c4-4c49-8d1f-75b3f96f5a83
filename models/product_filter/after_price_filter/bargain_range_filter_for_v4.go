package after_price_filter

import (
	"context"
	"encoding/json"
	"math"
	"strconv"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/bargain"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	apolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

type BargainRangeFilterForV4 struct {
	productsGen *biz_runtime.ProductsGenerator
}

func NewBargainRangeFilterForV4(productsGen *biz_runtime.ProductsGenerator) *BargainRangeFilterForV4 {
	return &BargainRangeFilterForV4{
		productsGen: productsGen,
	}
}

func (b *BargainRangeFilterForV4) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) []models.ProductCategory {
	if bargain.IsBargainRangeCheckable(&b.productsGen.BaseReqData.CommonInfo) {
		return b.filterBargainRangeCheckable(ctx, products)
	}

	// 前置校验：是否含有泛快价格range品类
	bargainRangeProduct := b.getBargainRangeProduct(products)
	if bargainRangeProduct == nil {
		return nil
	}
	// 前置校验：没有快车券后价、特惠券后价、推荐价下界、系数等信息，过滤自选车价格range品类
	ok, fastPrice, specialRatePrice := b.checkDataValid(ctx, products, bargainRangeProduct)
	if !ok {
		log.Trace.Infof(ctx, logTag, "bargain range data check fail")
		return nil
	}
	lowRecommendPrice := bargainRangeProduct.GetBargainRangeRecommendPriceLower(&b.productsGen.BaseReqData.CommonInfo)
	upperRecommendPrice := bargainRangeProduct.GetBargainRangeRecommendPriceUpper(&b.productsGen.BaseReqData.CommonInfo)
	leftLimitRatio := bargainRangeProduct.GetBargainRangeLeftLimitRatio()
	rightLimitRatio := bargainRangeProduct.GetBargainRangeRightLimitRatio()

	// 1、判断是否满足里程、出发时段
	filterByMetreAndTime := b.canFilterByMetreAndTime(ctx, &b.productsGen.BaseReqData.AreaInfo, bargainRangeProduct)
	// 2、推荐价最低值≥（特惠券后实付价×X）或推荐价最低值≥（快车券后实付价×X）
	filterByActualFeeRatio := b.canFilterByActualFeeRatio(ctx, fastPrice, lowRecommendPrice, specialRatePrice)
	// 3、左边界<=推荐价下界<推荐价上界<=右边界
	filterByOrder := false
	leftLimit := math.Floor(leftLimitRatio * fastPrice)
	rightLimit := math.Floor(rightLimitRatio * fastPrice)
	if !(leftLimit <= lowRecommendPrice && lowRecommendPrice < upperRecommendPrice && upperRecommendPrice <= rightLimit) {
		log.Trace.Infof(ctx, logTag, "bargain range filter  order=1 left= %v||low= %v|| upper= %v|| right= %v", leftLimit, lowRecommendPrice, upperRecommendPrice, rightLimit)
		filterByOrder = true
	}
	if filterByMetreAndTime || filterByActualFeeRatio || filterByOrder {
		log.Trace.Infof(ctx, logTag, "bargain range filter || orderAndTime=%v||actualFeeRatio=%v||order=%v", filterByMetreAndTime, filterByActualFeeRatio, filterByOrder)
		return nil
	}
	return []models.ProductCategory{models.ProductCategory(bargainRangeProduct.GetProductCategory())}
}

// filterBargainRangeCheckable
// 惠选车支持多勾-过滤自选车range品类
func (b *BargainRangeFilterForV4) filterBargainRangeCheckable(ctx context.Context, products []*biz_runtime.ProductInfoFull) []models.ProductCategory {
	bargainRangeProduct := b.getBargainRangeProduct(products)
	if bargainRangeProduct == nil {
		return nil
	}
	/*
			        $fFastPrice = self::_getFastCarPrice($aBizProductMap);
		        $fSpecialRatePrice = self::_getSpecialRatePrice($aBizProductMap);
		        $fLowRecommendPrice = $aBargainRangeProductInfo->getFastRangeRecommendPriceLower();
		        $fUpperRecommendPrice = $aBargainRangeProductInfo->getFastRangeRecommendPriceUpper();
		        $fLowerBorderPrice = $aBargainRangeProductInfo->getFastRangeBorderPriceLower();
		        $fUpperBorderPrice = $aBargainRangeProductInfo->getFastRangeBorderPriceUpper();
	*/

	fastPrice := b.getFastCarPrice(products)
	specialRatePrice := b.getSpecialRatePrice(products)
	lowRecommendPrice := bargainRangeProduct.GetFastRangeRecommendPriceLower()
	upperRecommendPrice := bargainRangeProduct.GetFastRangeRecommendPriceUpper()
	lowerBorderPrice := bargainRangeProduct.GetFastRangeBorderPriceLower()
	upperBorderPrice := bargainRangeProduct.GetFastRangeBorderPriceUpper()

	// 校验：没有快车券后价、特惠券后价、推荐价上下界、边界价上下界
	if fastPrice <= 0 || specialRatePrice <= 0 || lowRecommendPrice <= 0 ||
		upperRecommendPrice <= 0 || upperBorderPrice <= 0 || lowerBorderPrice < 0 {
		log.Trace.Infof(ctx, logTag, "bargain range data check fail")
		// 不符合条件，直接过滤掉自选车range品类
		return []models.ProductCategory{estimate_pc_id.EstimatePcIdHuiXuanCar}
	}

	// 1、判断是否满足里程、出发时段
	filterByMetreAndTime := b.canFilterByMetreAndTime(ctx, &b.productsGen.BaseReqData.AreaInfo, bargainRangeProduct)
	// 2、推荐价最低值＜（特惠券后实付价×X）且推荐价最低值＜（快车券后实付价×X）
	filterByActualFee := b.canFilterByActualFeeRatio(ctx, fastPrice, lowRecommendPrice, specialRatePrice)
	// 3、左边界<=推荐价下界<推荐价上界<=右边界
	filterByPriceOrder := false
	if !(lowerBorderPrice <= lowRecommendPrice && lowRecommendPrice < upperRecommendPrice && upperRecommendPrice <= upperBorderPrice) {
		log.Trace.Infof(ctx, logTag, "bargain range filter: left= %v||low= %v|| upper= %v|| right= %v", lowerBorderPrice, lowRecommendPrice, upperRecommendPrice, upperBorderPrice)
		filterByPriceOrder = true
	}
	if filterByMetreAndTime || filterByActualFee || filterByPriceOrder {
		log.Trace.Infof(ctx, logTag, "bargain range filter || orderAndTime=%v||actualFeeRatio=%v||order=%v", filterByMetreAndTime, filterByActualFee, filterByPriceOrder)
		return []models.ProductCategory{estimate_pc_id.EstimatePcIdHuiXuanCar}
	}
	// 满足条件，返回自选车range品类
	return nil
}

// getSpecialRatePrice
func (b *BargainRangeFilterForV4) getSpecialRatePrice(products []*biz_runtime.ProductInfoFull) float64 {
	for _, pfull := range products {
		if estimate_pc_id.EstimatePcIdSpecialRate == pfull.GetProductCategory() {
			return pfull.GetEstimateFee()
		}
		if estimate_pc_id.EstimatePcIdFastSpecialRate == pfull.GetProductCategory() {
			return pfull.GetEstimateFee()
		}
	}
	return -1.0
}

func (b *BargainRangeFilterForV4) getFastCarPrice(products []*biz_runtime.ProductInfoFull) float64 {
	for _, pfull := range products {
		if estimate_pc_id.EstimatePcIdFastCar == pfull.GetProductCategory() {
			return pfull.GetEstimateFee()
		}
	}
	return -1
}

func (b *BargainRangeFilterForV4) getBargainRangeProduct(products []*biz_runtime.ProductInfoFull) *biz_runtime.ProductInfoFull {
	for _, pfull := range products {
		if estimate_pc_id.EstimatePcIdHuiXuanCar == pfull.GetProductCategory() {
			return pfull
		}
	}
	return nil
}

// _canFilterByMetreAndTime 判断是否满足里程、出发时段过滤条件
// oBizCommonInfo: 公共信息
// bargainRangeProduct: 自选车range品类产品
// 返回值: true-需要过滤, false-不需要过滤
func (b *BargainRangeFilterForV4) canFilterByMetreAndTime(ctx context.Context, areaInfo *models.AreaInfo, bargainRangeProduct *biz_runtime.ProductInfoFull) bool {
	// 命中白名单直接不做过滤
	if b.isHitWhiteList(ctx, bargainRangeProduct) {
		return false
	}

	// 获取apollo配置
	config, err := apollo.GetConfigsByNamespaceAndConditions(ctx, bargainRangeOpenConf, apolloModel.NewCondition(
		map[string]string{
			"city_id": cast.ToString(areaInfo.City),
		},
	))

	if err != nil || len(config) == 0 {
		return false
	}

	now := time.Now().Local()
	driverMetre := int64(0)
	if bargainRangeProduct != nil && bargainRangeProduct.GetBillInfo() != nil {
		driverMetre = bargainRangeProduct.GetBillDriverMetre()
	}

	openConf := BargainRangeOpenRule{}
	if err = json.Unmarshal(config, &openConf); err != nil || len(openConf) < 1 {
		return false
	}

	for _, config := range openConf {
		for _, cond := range config.CheckCondition {
			start := hm2TodayTime(cond.StartTime)
			end := hm2TodayTime(cond.EndTime)
			if cond.StartDriverMetre <= driverMetre && driverMetre <= cond.EndDriverMetre &&
				start.Before(now) && end.After(now) {
				return false
			}
		}
	}

	return true
}

func (b *BargainRangeFilterForV4) isHitWhiteList(ctx context.Context, prod *biz_runtime.ProductInfoFull) bool {
	pidKey, apoParams := prod.GetApolloParams(biz_runtime.WithPIDKey)
	return apollo.FeatureToggle(ctx, "bargain_range_white_list_switch", pidKey, apoParams)
}

// canFilterByActualFeeRatio 判断是否需要根据推荐价与快车/特惠价的比率过滤
// 返回值: true-需要过滤, false-不需要过滤
func (b *BargainRangeFilterForV4) canFilterByActualFeeRatio(ctx context.Context, fastPrice, lowRecommendPrice, specialRatePrice float64) bool {
	// 命中白名单直接不做过滤
	if b.isHitWhiteList(ctx, nil) {
		return false
	}
	isFilter := false
	ratio := 0.9

	// 获取apollo配置
	params := map[string]string{
		"phone":         b.productsGen.BaseReqData.PassengerInfo.Phone,
		"app_version":   b.productsGen.BaseReqData.CommonInfo.AppVersion,
		"access_key_id": cast.ToString(b.productsGen.BaseReqData.CommonInfo.AccessKeyID),
		"city":          cast.ToString(b.productsGen.BaseReqData.AreaInfo.City),
		"pid":           cast.ToString(b.productsGen.BaseReqData.PassengerInfo.PID),
	}
	allow, params := apollo.GetParameters("bargain_range_fee_ratio_switch", cast.ToString(b.productsGen.BaseReqData.PassengerInfo.PID), params)
	if allow {
		if v, ok := params["ratio"]; ok {
			if r, err := strconv.ParseFloat(v, 64); err == nil {
				ratio = r
			}
		}
	}

	if lowRecommendPrice >= ratio*fastPrice {
		log.Trace.Infof(ctx, logTag, "bargain range filter  actualFeeRatio=1 ratio= %v||fastPrice= %v|| total= %v|| low= %v", ratio, fastPrice, ratio*fastPrice, lowRecommendPrice)
		isFilter = true
	}
	if lowRecommendPrice >= ratio*specialRatePrice {
		log.Trace.Infof(ctx, logTag, "bargain range filter  actualFeeRatio=1 ratio= %v||spfastPrice= %v|| total= %v|| low= %v", ratio, specialRatePrice, ratio*specialRatePrice, lowRecommendPrice)
		isFilter = true
	}
	return isFilter
}

// checkDataValid 校验数据有效性
// 返回值：bValidRes 是否有效，fastPrice 快车预估价，specialRatePrice 特惠预估价
func (b *BargainRangeFilterForV4) checkDataValid(
	ctx context.Context,
	products []*biz_runtime.ProductInfoFull,
	bargainRangeProduct *biz_runtime.ProductInfoFull,
) (bValidRes bool, fastPrice, specialRatePrice float64) {
	bValidRes = true
	fastPrice = 0
	specialRatePrice = 0

	// 获取快车预估价
	for _, pfull := range products {
		if estimate_pc_id.EstimatePcIdFastCar == pfull.GetProductCategory() {
			fastPrice = pfull.GetEstimateFee()
		}
	}

	// 获取特惠预估价
	specialRatePrice = b.getSpecialRatePrice(products)

	// 获取推荐价下界、上界、左边界系数、右边界系数
	lowRecommendPrice := bargainRangeProduct.GetBargainRangeRecommendPriceLower(&b.productsGen.BaseReqData.CommonInfo)
	upperRecommendPrice := bargainRangeProduct.GetBargainRangeRecommendPriceUpper(&b.productsGen.BaseReqData.CommonInfo)
	leftLimitRatio := bargainRangeProduct.GetBargainRangeLeftLimitRatio()
	rightLimitRatio := bargainRangeProduct.GetBargainRangeRightLimitRatio()

	if fastPrice == 0 || specialRatePrice == 0 ||
		lowRecommendPrice <= 0 || upperRecommendPrice <= 0 ||
		leftLimitRatio <= 0 || rightLimitRatio <= 0 {
		bValidRes = false
	}
	return
}
