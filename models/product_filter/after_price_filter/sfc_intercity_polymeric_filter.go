package after_price_filter

import (
	"context"
	"encoding/json"
	"fmt"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/redis"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/tag_service"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/passenger-common/util/money"
	context2 "git.xiaojukeji.com/lego/context-go"
	"github.com/spf13/cast"
	"strconv"
	"time"
)

const (
	ControlGroup                     = "0" // "0" // 不展示
	TreatmentGroup                   = "1" // 预估和等应答都展示
	TreatmentGroup2                  = "2" // 等应答展示
	expireSec                        = 10
	EstimateSource                   = "estimate"
	SfcGuideIntercityABMasterDCMPKey = "sfc-chengji_daoliu"
	CheckedTag                       = "need_check_tags"
)

type ConfigABTest struct {
	ABMultiTest []string `json:"ab_multi_test"`
}

type SfcIntercityPolymericFilter struct {
	req *models.BaseReqData
}

type AdditionalInfo struct {
	EstimateId string `json:"estimate_id"`
}

func NewSfcIntercityPolymericFilter(BaseReqData *models.BaseReqData) *SfcIntercityPolymericFilter {
	return &SfcIntercityPolymericFilter{
		req: BaseReqData,
	}
}

func specialFilter(ctx context.Context, req *models.BaseReqData, city int, productId, driverMeter int64, priceDistance int, isCrossCity int, timeGap int) bool {
	// 校验caller是否有效
	key := cast.ToString(req.PassengerInfo.Phone)
	apolloParam := map[string]string{
		"phone":           req.PassengerInfo.Phone,
		"city":            strconv.Itoa(city),
		"product_id":      strconv.Itoa(int(productId)),
		"driver_meter":    strconv.Itoa(int(driverMeter)),
		"is_cross_city":   strconv.Itoa(isCrossCity),
		"price_distance":  strconv.Itoa(priceDistance),
		"app_version":     req.CommonInfo.AppVersion,
		"access_key_id":   strconv.Itoa(int(req.CommonInfo.AccessKeyID)),
		"same_city_limit": strconv.Itoa(int(driverMeter)),
		"time_gap":        strconv.Itoa(timeGap),
	}

	if priceDistance > 0 {
		apolloParam["intercity_big"] = strconv.Itoa(1)
	} else {
		apolloParam["intercity_big"] = strconv.Itoa(0)
	}

	if !apollo.FeatureToggle(ctx, "gs_sfc_intercity_version_filter_toggle", key, apolloParam) {
		return true
	}

	hitToggle, assignment := apollo.FeatureExp(ctx, "gs_sfc_intercity_polymeric_filter_toggle", key, apolloParam)

	if !hitToggle {
		return true
	}
	tags := assignment.GetParameter("need_check_tags", "")
	// 命中新流不出
	if tag_service.IsHitTags(ctx, strconv.FormatInt(req.PassengerInfo.PID, 10), tags) {
		log.Trace.Infof(ctx, CheckedTag, "intercity filter by hit tags, product_id = %v", productId)
		return true
	}

	return false
}

func (filter *SfcIntercityPolymericFilter) filterSingleProduct(ctx context.Context, sfcCarpool, intercityProduct *biz_runtime.ProductInfoFull) bool {
	priceDistanct := money.Yuan2Fen(intercityProduct.GetEstimateFee() - sfcCarpool.GetEstimateFee())
	isCrossCity := IsCrossCity(sfcCarpool.GetCityID(), sfcCarpool.GetToCityID())
	timeGap := getTimeGap(intercityProduct)
	if specialFilter(ctx, filter.req, intercityProduct.GetCityID(), intercityProduct.GetProductId(), sfcCarpool.GetBillDriverMetre(), int(priceDistanct), isCrossCity, timeGap) {
		return true
	}
	return false
}

func removeAllIntercity(intercityProducts []*biz_runtime.ProductInfoFull) (toRemovedList []models.ProductCategory) {
	for _, v := range intercityProducts {
		toRemovedList = append(toRemovedList, models.ProductCategory(v.GetProductCategory()))
	}
	return toRemovedList
}

func removeNotHitIntercity(hit []*biz_runtime.ProductInfoFull, intercityProducts []*biz_runtime.ProductInfoFull) (toRemovedList []models.ProductCategory) {
	var used bool
	for _, v := range intercityProducts {
		used = false
		for _, vv := range hit {
			if v == nil || vv == nil {
				continue
			}
			if v.GetProductCategory() == vv.GetProductCategory() {
				used = true
				break
			}
		}
		if !used {
			toRemovedList = append(toRemovedList, models.ProductCategory(v.GetProductCategory()))
		}
	}
	return toRemovedList
}

func (filter *SfcIntercityPolymericFilter) filterABtoggle(ctx context.Context, intercityProducts []*biz_runtime.ProductInfoFull) (toRemovedList []models.ProductCategory) {
	tag := "sfc_intercity"
	abTest := dcmp.GetDcmpContent(ctx, SfcGuideIntercityABMasterDCMPKey, nil)
	var (
		configABTest      ConfigABTest
		hitBubbleTest     []*biz_runtime.ProductInfoFull
		hitAdditionalTest []*biz_runtime.ProductInfoFull
	)
	err := json.Unmarshal([]byte(abTest), &configABTest)
	if err != nil || len(configABTest.ABMultiTest) == 0 {
		return toRemovedList
	}
	for _, valueABTest := range configABTest.ABMultiTest {
		for _, value := range intercityProducts {
			if value.Product == nil || value.Product.BizInfo == nil {
				continue
			}
			apolloParams := map[string]string{
				"phone":         filter.req.PassengerInfo.Phone,
				"city":          strconv.FormatInt(int64(filter.req.AreaInfo.City), 10), //"142",
				"access_key_id": strconv.FormatInt(int64(filter.req.CommonInfo.AccessKeyID), 10),
				"app_version":   filter.req.CommonInfo.AppVersion, // "6.9.13",
				"route_id":      strconv.FormatInt(value.Product.BizInfo.ComboID, 10),
				"source":        EstimateSource,
			}
			ok, assign := apollo.FeatureExp(ctx, valueABTest, cast.ToString(filter.req.PassengerInfo.PID), apolloParams)
			if ok {
				switch assign.GetParameter("show_type", ControlGroup) {
				case ControlGroup:
					log.Trace.Infof(ctx, tag, "hit group = %v, route_id = %v, test is %v", ControlGroup, valueABTest, value.Product.BizInfo.ComboID)
					break
				case TreatmentGroup:
					log.Trace.Infof(ctx, tag, "hit group = %v, route_id = %v, test is %v", TreatmentGroup, valueABTest, value.Product.BizInfo.ComboID)
					hitBubbleTest = append(hitBubbleTest, value)
					break
				case TreatmentGroup2:
					log.Trace.Infof(ctx, tag, "hit group = %v, route_id = %v, test is %v", TreatmentGroup2, valueABTest, value.Product.BizInfo.ComboID)
					hitAdditionalTest = append(hitAdditionalTest, value)
					break
				default:
					break
				}
			}
		}
	}
	if len(hitAdditionalTest) > 0 {
		storeAdditionalInfo(ctx, hitAdditionalTest)
	}
	toRemovedList = append(toRemovedList, removeNotHitIntercity(hitBubbleTest, intercityProducts)...)
	return toRemovedList
}

func storeAdditionalInfo(ctx context.Context, intercityProducts []*biz_runtime.ProductInfoFull) {
	var intercityProduct *biz_runtime.ProductInfoFull
	for _, v := range intercityProducts {
		if v != nil {
			intercityProduct = v
		}
	}
	if intercityProduct == nil {
		return
	}
	data := &AdditionalInfo{
		EstimateId: intercityProduct.GetEstimateID(),
	}

	expireTime := time.Duration(expireSec) * time.Minute

	respStr := util.ToJSONStringNotNull(data)
	redisKey := generateIntercityAdditionalCacheKey(ctx)

	_, redisErr := redis.GetClient().SetEx(ctx, redisKey, expireTime, respStr)

	if redisErr != nil {
		log.Trace.Warnf(ctx, "redis_error", "WriteCacheError=%v||key=%v||value=%v", redisErr, redisKey, respStr)
	}
}

func generateIntercityAdditionalCacheKey(ctx context.Context) string {
	redisKey := fmt.Sprintf("%s_%s",
		"sfcIntercity", context2.GetTrace(ctx).GetTraceId())
	return redisKey
}

func (filter *SfcIntercityPolymericFilter) filterPolymeric(ctx context.Context, sfcCarpool *biz_runtime.ProductInfoFull, intercityProducts []*biz_runtime.ProductInfoFull) (toRemovedList []models.ProductCategory) {
	var intercity *biz_runtime.ProductInfoFull

	for _, v := range intercityProducts {
		if intercity != nil || filter.filterSingleProduct(ctx, sfcCarpool, v) {
			toRemovedList = append(toRemovedList, models.ProductCategory(v.GetProductCategory()))
		} else {
			intercity = v
		}
	}
	return
}

func (filter *SfcIntercityPolymericFilter) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) []models.ProductCategory {
	var (
		sfcCarpool        *biz_runtime.ProductInfoFull
		toRemovedList     = make([]models.ProductCategory, 0)
		intercityProducts []*biz_runtime.ProductInfoFull
	)
	for _, v := range products {
		if v.Product.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolSFCar || v.Product.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolCrossSFCar {
			sfcCarpool = v
		}
		if consts.CarpoolType(v.Product.GetCarpoolType()) == consts.CarpoolTypeIntercity {
			intercityProducts = append(intercityProducts, v)
		}
	}
	if len(intercityProducts) == 0 {
		return toRemovedList
	}
	// 必须具有拼车
	if sfcCarpool == nil {
		return removeAllIntercity(intercityProducts)
	}
	// 对照实验过滤
	//
	toRemovedList = append(toRemovedList, filter.filterABtoggle(ctx, intercityProducts)...)

	// 聚合过滤
	toRemovedList = append(toRemovedList, filter.filterPolymeric(ctx, sfcCarpool, intercityProducts)...)
	return toRemovedList
}

func getTimeGap(product *biz_runtime.ProductInfoFull) int {
	if len(product.Product.BizInfo.DepartureRange) > 1 {
		return int(product.Product.BizInfo.DepartureRange[1] - product.Product.BizInfo.DepartureRange[0])
	}
	return 0
}

func IsCrossCity(lCity int, rCity int) int {
	if lCity != rCity {
		return 1
	}
	return 0
}
