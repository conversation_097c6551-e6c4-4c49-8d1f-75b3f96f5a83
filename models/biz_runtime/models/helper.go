package models

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	Plutus "git.xiaojukeji.com/dirpc/dirpc-go-http-Plutus"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_category"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/passenger-common/sdk/locsvrhttp"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
)

type GEOInfo struct {
	MapType string // 地图类型

	CurrLat     float64
	CurrLng     float64
	Lat         float64
	Lng         float64
	FromLat     float64 // 起点
	FromLng     float64 // 起点
	FromPOIID   string
	FromPOIType string
	FromAddress string
	FromName    string

	ToLat             float64 // 终点
	ToLng             float64 // 终点
	ToPOIID           string
	ToPOIType         string
	ToAddress         string
	ToName            string
	StopoverPointInfo string
	MapInfoCacheToken string // 地图信息token

	MapInfoStartCacheToken string
	MapInfoDestCacheToken  string

	RouteId int64 // 路线id

	ChooseFSearchid string //用户选择起点请求ID
	ChooseTSearchid string //用户选择终点请求ID
}

type ClientInfo struct {
	AppVersion   string // 端版本
	AccessKeyID  int32  // 端来源
	Channel      int64  // 渠道号
	ClientType   int32  // 端类型
	Lang         string // 端语种
	PlatformType int32  // 端(平台)类型2

	TerminalID int64 //
	OriginID   int64 // ??

	Xpsid     string
	XpsidRoot string

	Imei string // Deprecated: 个人信息法整改要求删除该信息
	// AgentType string

	MenuID   string
	PageType int32
	SourceID int32

	TabList    string
	TabId      string
	FormHeight int64

	BargainFrom string // 司乘议价来源

	IsScanCode string // 扫码上车标识

	ScanCodeShiftId string // 班次id

	Dchn string // 投放标识

	FromType int16 // 来源

	EstimateStyleType int32 // 预估表单样式，0:老样式，1:单行  2双排新表单 3多tab新表单 4:一站式出行

	ScreenPixels string // 屏幕参数
	ScreenScale  float64
	GuideTraceId string
	IsBestShift  int32

	IsEstimateV2   bool   // h5切量版本后续下掉
	SortType       *int32 // 班次列表页排序类型
	StopoverPoints *string
	IsDRN          bool // 是否前端走DRN
	CategoryInfo   map[int32]int32
	OneStopVersion string
}

type PassengerInfoV2 struct {
	UID      int64
	PID      int64
	Phone    string
	Role     int32
	Channel  string
	AppID    int32
	UserType int32
	OriginID string
	Token    string
	ExtPID   string
}

type DriverData struct {
	DriverId int64
}

type DepartureRange struct {
	From time.Time
	To   time.Time
}

type OtherInfo struct {
	SourceChannel    string
	ChooseFSearchID  string
	ChooseTSearchID  string
	PreferredRouteId string
	Xpsid            string
	XpsidRoot        string
}

func (rng *DepartureRange) DepartureTime() time.Time {
	return rng.To
}

type UserOption struct {
	CallCarType            int32
	CallCarPhone           string // 代叫手机号
	CarpoolSeatNum         int32
	OrderType              int32
	DepartureTime          int64 // Deprecated: 用DepartureRange表示
	DepartureRange         *DepartureRange
	PaymentsType           int32   // 支付方式
	StopoverPoints         *string // 途经点数据 json string 格式 todo 处理
	MultiRequireProduct    string  // 二次预估
	IsGuide                *int32  // 是否来源预估导流位
	LuxurySelectCarlevels  string  // 豪华车预估车型 该数据赋值 "[1500]" json序列化好的数据
	LuxurySelectDriver     string
	PreferredRouteId       string
	BusServiceShiftId      *string
	AgentType              string
	CarPlate               string
	AdultPrice             float64
	ComboId                int32 //套餐id
	BusCardSelectedBatchId int32 //用户选择的卡批次id
	AdditionalService      string
	IsFemaleDriverFirst    int32
	UserChangeTime         bool
}

type SendOrderProvider interface {
	GetLowOrderID() int64
	GetHighOrderID() int64
}

type BaseReqDataBuilder struct {
	inner *BaseReqData
}

func NewBaseReqDataBuilder() *BaseReqDataBuilder {
	return &BaseReqDataBuilder{
		inner: new(BaseReqData),
	}
}

// SetAreaInfo ...
func (bd *BaseReqDataBuilder) SetGEOInfo(geo *GEOInfo) *BaseReqDataBuilder {
	// assert
	if geo == nil {
		panic("fuck")
	}
	// merge
	bd.inner.AreaInfo = AreaInfo{
		MapType: geo.MapType,

		FromLat:     geo.FromLat,
		FromLng:     geo.FromLng,
		FromAddress: geo.FromAddress,
		FromName:    geo.FromName,
		FromPoiType: geo.FromPOIType,
		FromPoiID:   geo.FromPOIID,

		ToLat:                  geo.ToLat,
		ToLng:                  geo.ToLng,
		ToAddress:              geo.ToAddress,
		ToName:                 geo.ToName,
		ToPoiType:              geo.ToPOIType,
		ToPoiID:                geo.ToPOIID,
		Lat:                    geo.Lat,
		Lng:                    geo.Lng,
		CurLat:                 geo.CurrLat,
		CurLng:                 geo.CurrLng,
		MapInfoCacheToken:      geo.MapInfoCacheToken,
		MapInfoStartCacheToken: geo.MapInfoStartCacheToken,
		MapInfoDestCacheToken:  geo.MapInfoDestCacheToken,
		ChooseFSearchid:        geo.ChooseFSearchid,
		ChooseTSearchid:        geo.ChooseTSearchid,

		StartingName: geo.FromName,
		DestName:     geo.ToName,
	}
	bd.inner.CommonInfo.RouteID = geo.RouteId
	if geo.StopoverPointInfo != "" {
		var wayPoint []*price_api.StopoverPointInfo
		_ = json.Unmarshal([]byte(geo.StopoverPointInfo), &wayPoint)
		bd.inner.AreaInfo.StopoverPointInfo = wayPoint
	}
	return bd
}

// SetAreaInfo ...
func (bd *BaseReqDataBuilder) SetClientInfo(client *ClientInfo) *BaseReqDataBuilder {
	// assert
	if client == nil {
		panic("fuck")
	}
	// copy
	bd.inner.CommonInfo.AppVersion = client.AppVersion
	bd.inner.CommonInfo.AccessKeyID = client.AccessKeyID
	bd.inner.CommonInfo.Channel = client.Channel
	bd.inner.CommonInfo.ClientType = client.ClientType
	bd.inner.CommonInfo.Lang = client.Lang
	bd.inner.CommonInfo.PlatformType = client.PlatformType
	bd.inner.CommonInfo.TerminalID = client.TerminalID
	bd.inner.CommonInfo.OriginID = client.OriginID
	bd.inner.CommonInfo.Imei = client.Imei

	bd.inner.CommonInfo.PageType = client.PageType
	bd.inner.CommonInfo.FromType = client.FromType
	bd.inner.CommonInfo.GuideTraceId = client.GuideTraceId
	bd.inner.CommonBizInfo.IsBestShift = client.IsBestShift
	bd.inner.CommonInfo.MenuID = client.MenuID
	bd.inner.CommonInfo.TabList = client.TabList
	bd.inner.CommonInfo.TabId = client.TabId
	bd.inner.CommonInfo.SourceID = client.SourceID
	bd.inner.CommonInfo.EstimateStyleType = client.EstimateStyleType
	bd.inner.CommonInfo.FormHeight = client.FormHeight

	bd.inner.CommonInfo.Xpsid = client.Xpsid
	bd.inner.CommonInfo.XpsidRoot = client.XpsidRoot

	bd.inner.CommonBizInfo.BargainForm = client.BargainFrom
	bd.inner.CommonInfo.IsScanCode = client.IsScanCode
	bd.inner.CommonInfo.ScanCodeShiftId = client.ScanCodeShiftId
	bd.inner.CommonInfo.Dchn = client.Dchn

	bd.inner.CommonInfo.ScreenPixels = client.ScreenPixels
	bd.inner.CommonInfo.ScreenScale = client.ScreenScale
	bd.inner.CommonInfo.IsEstimateV2 = client.IsEstimateV2

	bd.inner.CommonInfo.SortType = client.SortType // 班次列表页面排序类型
	bd.inner.CommonInfo.StopoverPoints = client.StopoverPoints
	bd.inner.CommonInfo.OneStopVersion = client.OneStopVersion

	bd.inner.CommonInfo.IsDRN = client.IsDRN
	bd.inner.CommonInfo.CategoryInfo = client.CategoryInfo
	return bd
}

// SetAreaInfo ...
func (bd *BaseReqDataBuilder) SetUserOption(option *UserOption) *BaseReqDataBuilder {
	// assert
	if option == nil {
		panic("fuck")
	}
	// copy
	bd.inner.CommonInfo.OrderType = option.OrderType
	bd.inner.CommonInfo.DepartureTime = option.DepartureTime
	if option.DepartureRange != nil {
		bd.inner.CommonInfo.DepartureRange = []int64{option.DepartureRange.From.Unix(), option.DepartureRange.To.Unix()}
	}
	bd.inner.CommonInfo.PaymentsType = option.PaymentsType
	bd.inner.CommonInfo.CallCarType = option.CallCarType
	bd.inner.CommonInfo.CallCarPhone = option.CallCarPhone
	bd.inner.CommonInfo.LuxurySelectCarlevels = option.LuxurySelectCarlevels
	bd.inner.CommonInfo.LuxurySelectDriver = option.LuxurySelectDriver

	bd.inner.CommonInfo.PreferredRouteId = option.PreferredRouteId
	bd.inner.CommonInfo.RouteID, _ = strconv.ParseInt(option.PreferredRouteId, 10, 64)

	bd.inner.CommonBizInfo.CarpoolSeatNum = option.CarpoolSeatNum
	bd.inner.RawUserSelectOption = option
	bd.inner.CommonInfo.StopoverPoints = option.StopoverPoints
	bd.inner.CommonBizInfo.InitByMultiRequireProduct(option.MultiRequireProduct)
	if option.IsGuide != nil {
		bd.inner.CommonBizInfo.IsGuide = *(option.IsGuide)
	}
	if option.BusServiceShiftId != nil {
		bd.inner.CommonBizInfo.BusServiceShiftId = *(option.BusServiceShiftId)
	}
	bd.inner.CommonBizInfo.AdultPrice = option.AdultPrice
	bd.inner.CommonBizInfo.AgentType = option.AgentType
	bd.inner.CommonBizInfo.CarPlate = option.CarPlate
	bd.inner.CommonBizInfo.ComboId = option.ComboId
	bd.inner.CommonBizInfo.BusCardSelectedBatchId = option.BusCardSelectedBatchId
	return bd
}

// SetAreaInfo ...
func (bd *BaseReqDataBuilder) SetPassengerInfoV2(passenger *PassengerInfoV2) *BaseReqDataBuilder {
	// assert
	if passenger == nil {
		panic("fuck")
	}
	// copy
	bd.inner.PassengerInfo.UID = passenger.UID
	bd.inner.PassengerInfo.PID = passenger.PID
	bd.inner.PassengerInfo.Phone = passenger.Phone
	bd.inner.PassengerInfo.Role = passenger.Role
	bd.inner.PassengerInfo.Channel = passenger.Channel

	bd.inner.PassengerInfo.UserType = passenger.UserType
	bd.inner.PassengerInfo.OriginId = passenger.OriginID
	bd.inner.PassengerInfo.AppID = passenger.AppID
	bd.inner.PassengerInfo.Token = passenger.Token
	bd.inner.PassengerInfo.ExtPid = passenger.ExtPID

	return bd
}

// SetStationInfo ...
func (bd *BaseReqDataBuilder) SetStationInfo(si *StationInfo, stV2 int64, etV2 int64) *BaseReqDataBuilder {
	if si == nil {
		return bd
	}
	// 热点路线扫码上车daytime=0兜底
	if si.DayTime == 0 {
		si.DayTime = int32(util.GetNowEarlyTimeStamp())
	}
	if stV2 >= etV2 {
		stV2 = int64(0)
		etV2 = int64(86400)
	}
	// 传了v2时间走相对时间校验逻辑
	if si.DayTime > 0 && stV2 >= 0 && etV2 > 0 && etV2 <= 24*60*60 && etV2 > stV2 {
		si.StartTime = si.DayTime + int32(stV2)
		si.EndTime = si.DayTime + int32(etV2)
		bd.inner.CommonBizInfo.StationInfo = *si
		return bd
	}
	// 临时兼容端传的start_time end_time有问题的情况
	if si.DayTime > 0 && si.StartTime > 0 && si.EndTime > 0 && (si.StartTime < si.DayTime || si.EndTime > si.DayTime+24*60*60) {
		baseTime := si.StartTime
		si.StartTime = si.DayTime + si.StartTime - int32(util.GetSomeDayEarlyTimeStamp(int64(baseTime)))
		si.EndTime = si.DayTime + si.EndTime - int32(util.GetSomeDayEarlyTimeStamp(int64(baseTime)))
	}
	bd.inner.CommonBizInfo.StationInfo = *si
	return bd
}

// SetBaseStationInfo ...
func (bd *BaseReqDataBuilder) SetBaseStationInfo(si *StationInfo) *BaseReqDataBuilder {
	if si == nil {
		return bd
	}
	bd.inner.CommonBizInfo.StationInfo = *si
	return bd
}

func (bd *BaseReqDataBuilder) GetStationInfo() *StationInfo {
	return &bd.inner.CommonBizInfo.StationInfo
}

func (bd *BaseReqDataBuilder) SetOtherInfo(otherInfo *OtherInfo) *BaseReqDataBuilder {
	// 不影响流程
	if otherInfo == nil {
		return bd
	}

	bd.inner.CommonInfo.SourceChannel = otherInfo.SourceChannel
	bd.inner.CommonInfo.ChooseTSearchID = otherInfo.ChooseFSearchID
	bd.inner.CommonInfo.ChooseFSearchID = otherInfo.ChooseFSearchID
	bd.inner.CommonInfo.PreferredRouteId = otherInfo.PreferredRouteId
	bd.inner.CommonInfo.Xpsid = otherInfo.Xpsid
	bd.inner.CommonInfo.XpsidRoot = otherInfo.XpsidRoot
	return bd
}

// TryBuild 尝试构建
// 由于依赖的城市信息不能缺失, 而获取需要发起一次rpc, rpc有失败可能
// 因此只能暴露 try-build 方法
func (bd *BaseReqDataBuilder) TryBuild(ctx context.Context) (*BaseReqData, error) {
	if bd.inner.CommonBizInfo.StationInfo.IsScanCode {
		return bd.inner, nil
	}
	if err := bd.inner.AreaInfo.TryFillCityInfo(ctx); err != nil {
		return nil, err
	}
	return bd.inner, nil
}

// TryBuildWithStopover 尝试构建，途径点cityid没传的话需要填充
// 由于依赖的城市信息不能缺失, 而获取需要发起一次rpc, rpc有失败可能
// 因此只能暴露 try-build 方法
func (bd *BaseReqDataBuilder) TryBuildWithStopover(ctx context.Context) (*BaseReqData, error) {
	if bd.inner.CommonBizInfo.StationInfo.IsScanCode {
		return bd.inner, nil
	}
	if err := bd.inner.AreaInfo.TryFillCityInfoWithStopover(ctx); err != nil {
		return nil, err
	}
	return bd.inner, nil
}

func (bd *BaseReqDataBuilder) GetBaseReqData() *BaseReqData {
	return bd.inner
}

func (bd *BaseReqDataBuilder) SetOverseaAreaInfo(ctx context.Context, fromCity int32, toCity int32) (*BaseReqDataBuilder, error) {

	if bd.inner == nil {
		bd.inner = &BaseReqData{}
	}
	bd.inner.AreaInfo.City = fromCity
	bd.inner.AreaInfo.Area = fromCity
	bd.inner.AreaInfo.ToArea = toCity
	resp, err := locsvrhttp.GetClient().GetCityByIDs(ctx, []int32{fromCity})

	if err == nil && resp != nil && len(resp.GetCityinfo()) != 0 {
		for _, info := range resp.GetCityinfo() {
			if info.DistrictCode != nil && info.DistrictCodeV2 != nil {
				bd.inner.AreaInfo.District = *(info.DistrictCode)
				bd.inner.AreaInfo.DistrictV2 = info.DistrictCodeV2
				bd.inner.AreaInfo.CountryIsoCode = &info.CanonicalCountryCode
			}
			if info.Cityid == fromCity {
				cityInfo, err := json.Marshal(info)
				if err != nil {
					log.Trace.Infof(ctx, "MarshalCityinfoERR", "city info Marshal err ,err=%s", err)
				}
				bd.inner.AreaInfo.FromCityInfo = string(cityInfo)
			}
		}
	} else {
		log.Trace.Warnf(ctx, "GetCityinfoERR", "city info empty,err=%s", err)
		return nil, err
	}
	return bd, nil
}

func (bd *BaseReqDataBuilder) SetAreaInfo(ctx context.Context, fromCity int32, toCity int32) *BaseReqDataBuilder {

	if bd.inner == nil {
		bd.inner = &BaseReqData{}
	}
	bd.inner.AreaInfo.City = fromCity
	bd.inner.AreaInfo.Area = fromCity
	bd.inner.AreaInfo.ToArea = toCity

	cityInfo, _ := util.GetCityInfo(ctx, fromCity)
	if cityInfo != nil && cityInfo.DistrictCode != nil {
		bd.inner.AreaInfo.District = *(cityInfo.DistrictCode)
	}
	return bd
}

func (bd *BaseReqDataBuilder) SetAreaInfov2(ai *AreaInfo) *BaseReqDataBuilder {
	if bd.inner == nil {
		bd.inner = &BaseReqData{}
	}
	bd.inner.AreaInfo.City = ai.City
	if ai.District != "" {
		bd.inner.AreaInfo.District = ai.District
	}
	return bd

}

func (bd *BaseReqDataBuilder) SetFullAreaInfo(areaInfo AreaInfo) *BaseReqDataBuilder {
	if bd.inner == nil {
		bd.inner = &BaseReqData{}
	}
	bd.inner.AreaInfo = areaInfo
	return bd
}
func (bd *BaseReqDataBuilder) SetCustomTimeOutConf(customTimeOutConf map[string]int) *BaseReqDataBuilder {
	if bd == nil {
		return bd
	}
	bd.inner.CommonBizInfo.CustomTimeOutConf = customTimeOutConf
	return bd
}

func (bd *BaseReqDataBuilder) SetStopoverPointInfoByOrder(ctx context.Context, orderInfo *order_info.OrderInfo) *BaseReqDataBuilder {
	if bd.inner == nil {
		return bd
	}

	stopoverPointInfo := []*price_api.StopoverPointInfo{}

	if orderInfo.WayPointsAInfo != "" {
		wayPoint := &price_api.StopoverPointInfo{}
		err := json.Unmarshal([]byte(orderInfo.WayPointsAInfo), wayPoint)
		if err != nil {
			log.Trace.Warnf(ctx, "SetStopoverPointInfoByOrder", "err:%v", err)
		}

		if orderInfo.WayPointsAStatus != "" {
			wayPoint.Status, err = strconv.Atoi(orderInfo.WayPointsAStatus)
			if err != nil {
				log.Trace.Warnf(ctx, "SetStopoverPointInfoByOrder", "err:%v", err)
			}
		}
		stopoverPointInfo = append(stopoverPointInfo, wayPoint)

	}
	if orderInfo.WayPointsBInfo != "" {
		wayPoint := &price_api.StopoverPointInfo{}
		err := json.Unmarshal([]byte(orderInfo.WayPointsBInfo), wayPoint)
		if err != nil {
			log.Trace.Warnf(ctx, "SetStopoverPointInfoByOrder", "err:%v", err)
		}

		if orderInfo.WayPointsBStatus != "" {
			wayPoint.Status, err = strconv.Atoi(orderInfo.WayPointsBStatus)
			if err != nil {
				log.Trace.Warnf(ctx, "SetStopoverPointInfoByOrder", "err:%v", err)
			}
		}
		stopoverPointInfo = append(stopoverPointInfo, wayPoint)
	}

	bd.inner.AreaInfo.StopoverPointInfo = stopoverPointInfo

	return bd
}

func (bd *BaseReqDataBuilder) SetSendOrder(sendOrder SendOrderProvider) *BaseReqDataBuilder {
	bd.inner.SendOrder = SendOrder{
		OrderId:    sendOrder.GetHighOrderID(),
		OrderIdLow: sendOrder.GetLowOrderID(),
	}

	return bd
}

func (bd *BaseReqDataBuilder) SetPetsInfo(info *PetInfo) *BaseReqDataBuilder {
	if bd.inner == nil {
		bd.inner = &BaseReqData{}
	}
	if info == nil {
		return bd
	}

	//获取增值服务映射关系
	apolloParam := bd.GetBaseReqData().GetApolloParam()
	apolloParam["weight_category"] = cast.ToString(info.WeightCategory)
	apolloParam["pet_service_tag"] = cast.ToString(info.PetServiceTag)
	isAllow, assignParams := apollo.GetParameters("convert_pet_service_info", strconv.Itoa(int(bd.GetBaseReqData().PassengerInfo.UID)), apolloParam)
	feature := dos.CustomFeatureStruct{
		Count: 1,
	}
	if isAllow {
		id, err1 := strconv.Atoi(assignParams["id"])
		serviceType, err2 := strconv.Atoi(assignParams["service_type"])
		if err1 == nil && err2 == nil {
			feature.Id = int64(id)
			st32 := int32(serviceType)
			feature.ServiceType = &st32
		}

	}
	var featureList = make([]dos.CustomFeatureStruct, 0)
	featureList = append(featureList, feature)
	jsonData, err := json.Marshal(featureList)
	if err != nil {
		info.PersonalizedCustomOption = ""
	}
	listStr := string(jsonData)
	info.PersonalizedCustomOption = listStr
	//mock前端传参
	options := make(map[int64]*Plutus.PersonalizedCustomizedOptions)
	item := &Plutus.PersonalizedCustomizedOptions{
		Count:       1,
		ServiceType: feature.ServiceType,
	}
	options[feature.Id] = item
	dosMap := make(map[int64][]dos.CustomFeatureStruct)
	dosMap[product_category.ProductCategoryPremiumComfort] = featureList

	bd.inner.CommonBizInfo.PersonalizedCustomOptions = options
	bd.inner.CommonBizInfo.PcId2CustomFeature = dosMap
	bd.inner.CommonBizInfo.PetInfo = &PetInfo{
		PetNo:                    info.PetNo,
		PetType:                  info.PetType,
		PetTypeDesc:              info.PetTypeDesc,
		NickName:                 info.NickName,
		Avatar:                   info.Avatar,
		WeightCategory:           info.WeightCategory,
		WeightCategoryDesc:       info.WeightCategoryDesc,
		IsDefault:                info.IsDefault,
		PetServiceTag:            info.PetServiceTag,
		PersonalizedCustomOption: info.PersonalizedCustomOption,
	}

	return bd
}

func (bd *BaseReqDataBuilder) SetDriverInfo(info *DriverData) *BaseReqDataBuilder {
	if nil == info {
		return bd
	}

	bd.inner.DriverInfo.DriverId = info.DriverId

	return bd
}

// Apply 执行自定义的赋值逻辑函数
// 接受多个函数参数，按顺序对BaseReqData进行自定义操作
func (bd *BaseReqDataBuilder) Apply(ctx context.Context, customFuncs ...func(context.Context, *BaseReqData)) *BaseReqDataBuilder {
	if len(customFuncs) == 0 {
		return bd
	}

	// 依次执行所有传入的函数
	for _, customFunc := range customFuncs {
		if customFunc != nil {
			customFunc(ctx, bd.inner)
		}
	}

	return bd
}
