package models

type AnyCarEstimateAppendCarFormMaterial struct {
	Title      string      `json:"title"`       //追加车型title
	TitleV2    string      `json:"title_v2"`    //追加车型title v2
	Button     *Button     `json:"button"`      //按钮信息
	AuthButton *AuthButton `json:"auth_button"` //授权信息
	TopButton  *TopButton  `json:"top_button"`  //顶部全选按钮
	ClickTitle string      `json:"click_title"` //点击后需要替换的标题
}

type Style struct {
	BorderColor     string   `json:"border_color"`
	FontColor       string   `json:"font_color"`
	BgGradientColor []string `json:"bg_gradient_color"`
	BoxShadowColor  string   `json:"box_shadow_color"`
}

type Button struct {
	ActionType int    `json:"action_type"`
	Text       string `json:"text"`
	Style      *Style `json:"style"`
}

type AuthButton struct {
	AuthButtonText string `json:"auth_button_text"`
}

type TopButton struct {
	ActionType int    `json:"action_type"`
	Text       string `json:"text"`
}
