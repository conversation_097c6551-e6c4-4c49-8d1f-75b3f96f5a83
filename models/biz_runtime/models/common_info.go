package models

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"github.com/spf13/cast"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/agent_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/client_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/order"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/taxi"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

type CommonInfo struct {
	AppVersion            string          `json:"app_version"`
	MenuID                string          `json:"menu_id"`
	PageType              int32           `json:"page_type"`
	SourceID              int32           `json:"source_id"`
	AccessKeyID           int32           `json:"access_key_id"`
	OriginID              int64           `json:"origin_id"`
	CallCarType           int32           `json:"call_car_type"`
	Channel               int64           `json:"channel"`
	ScreenPixels          string          `json:"screen_pixels,omitempty"`
	ScreenScale           float64         `json:"screen_scale,omitempty"`
	OrderType             int32           `json:"order_type"`
	PlatformType          int32           `json:"platform_type"`
	Lang                  string          `json:"lang"`
	ClientType            int32           `json:"client_type"`
	Imei                  string          `json:"imei"`
	AgentType             string          `json:"agent_type"`
	TerminalID            int64           `json:"terminal_id"`
	RouteID               int64           `json:"route_id"`
	From                  string          `json:"from"`
	DepartureTime         int64           `json:"departure_time"`
	DepartureRange        []int64         `json:"departure_range"`
	ActivityID            int32           `json:"activity_id"`             //一键叫数字ID
	PaymentsType          int32           `json:"payments_type"`           //支付方式
	LuxurySelectCarlevels string          `json:"luxury_select_carlevels"` // 低版本 豪华车选择预估车型 该数据赋值 "[1500]" json序列化好的数据
	LuxurySelectDriver    string          `json:"luxury_select_driver"`    // 低版本 豪华车选择的司机 "", "0", "-1", "-2"
	AirportType           int32           `json:"airport_type"`            // 接送机字段确认
	CallCarPhone          string          `json:"call_car_phone"`          // 代叫手机号
	UserType              int32           `json:"user_type"`               // 用户类型 0表示普通用户，2表示企业用户
	BusinessOption        string          `json:"business_option"`         // 企业使用可选多因素一口价等配置
	CategoryInfo          map[int32]int32 `json:"category_info"`

	// ssse相关参数
	TrafficNumber     string `json:"traffic_number"`      //航班号，如CA1405
	TrafficDepTime    string `json:"traffic_dep_time"`    //航班起飞时间字符串
	TrafficArrTime    string `json:"traffic_arr_time"`    //航班到达时间
	AirportId         int32  `json:"airport_id"`          //接机时为航班落地航站楼id，送机时为出发航站楼id，如1573
	FlightDepCode     string `json:"flight_dep_code"`     //航班出发地三字码,如CTU
	FlightArrCode     string `json:"arr_code"`            //航班到达地三字码,如PEK
	FlightDepTerminal string `json:"flight_dep_terminal"` //航班出发航站楼，如T2

	// 原始预估信息
	CompareEstimateTraceId string `json:"compare_estimate_trace_id"`
	CompareEstimateId      string `json:"compare_estimate_id"`

	TabList            string  `json:"tab_list"`
	TabId              string  `json:"tab_id"`
	FormHeight         int64   `json:"form_height"`
	StopoverPoints     *string `json:"stopover_points"`
	PreferenceFilterId int32   `json:"preference_filter_id"`

	ChooseFSearchID   string `json:"choose_f_searchid"`
	ChooseTSearchID   string `json:"choose_t_searchid"`
	SourceChannel     string `json:"source_channel"`
	PreferredRouteId  string `json:"preferred_route_id"`
	Xpsid             string `json:"xpsid"`
	XpsidRoot         string `json:"xpsid_root"`
	EstimateStyleType int32  `json:"estimate_style_type"` //预估表单样式，0:老样式，1:单行  2双排新表单 3多tab新表单 4:一站式出行
	OneStopVersion    string `json:"one_stop_version"`

	PricingBoxData      *taxi.PricingBoxData `json:"pricing_box_data"`
	EventKey            *string              `json:"event_key"`
	IsScanCode          string               `json:"is_scan_code"`
	ScanCodeShiftId     string               `json:"scan_code_shift_id"`
	MainEstimateTraceId string               `json:"main_estimate_trace_id"`
	Dchn                string               `json:"dchn"`

	FromType                int16 `json:"from_type"` // 小巴独立页来源
	DisableUnione           bool
	SelectedCarlevel        string `json:"selected_carlevel"`          // 普通豪华车选择车型
	SixSeatSelectedCarlevel string `json:"six_seat_selected_carlevel"` // 六座豪华车选择车型
	DesignatedDriver        string `json:"designated_driver"`          // 普通豪华车选择司务员
	SixSeatDesignatedDriver string `json:"six_seat_designated_driver"` // 六座豪华车选择司务员
	GuideTraceId            string `json:"guide_trace_id"`             //导流位trace id
	FontScaleType           int32  `json:"font_scale_type"`            // 大字版标识

	IsEstimateV2 bool   `json:"is_estimate_v2"` //是否是v2的预估,h5切量使用
	SortType     *int32 `json:"sort_type"`      //班次列表排序类型  1 综合排序 2 距离最近
	IsDRN        bool   // 前端是否走mpx2rn

	RiskCode int32 `json:"risk_code"`
}

func (c *CommonInfo) InitByReq(req *proto.MultiEstimatePriceRequest) {
	c.AppVersion = req.AppVersion
	c.MenuID = req.MenuId
	c.PageType = req.PageType
	c.AccessKeyID = req.AccessKeyId
	c.OriginID = req.OriginId
	c.CallCarType = req.GetCallCarType()
	c.Channel = req.Channel
	c.OrderType = req.OrderType
	c.Lang = req.Lang
	c.ClientType = req.ClientType
	c.Imei = req.GetImei()
	c.From = req.GetFrom()
	c.PaymentsType = req.GetPaymentsType()
	c.DepartureTime, _ = strconv.ParseInt(req.DepartureTime, 10, 32)
	c.DepartureRange = util.StringTIntArray(req.GetDepartureRange())
	c.PlatformType = req.PlatformType
	c.ActivityID = req.GetActivityId()
	c.AgentType = req.GetAgentType()
	c.TrafficNumber = req.GetTrafficNumber()
	c.TrafficDepTime = req.GetTrafficDepTime()
	c.TrafficArrTime = req.GetTrafficArrTime()
	c.FlightArrCode = req.GetFlightArrCode()
	c.AirportId = req.GetAirportId()
	c.FlightDepCode = req.GetFlightDepCode()
	c.FlightDepTerminal = req.GetFlightDepTerminal()
	c.CompareEstimateId = req.GetCompareEstimateId()
	c.CompareEstimateTraceId = req.GetCompareEstimateTraceId()
	c.RouteID = util.ToInt64(req.GetPreferredRouteId())
	c.Xpsid = req.GetXpsid()
	c.XpsidRoot = req.GetXpsidRoot()
}

func (c *CommonInfo) SetBusinessOption(businessOption string) {
	c.BusinessOption = businessOption
}

func (c *CommonInfo) InitByAnycarReq(_ context.Context, request *proto.AnyCarEstimateReq) {
	c.ScreenScale = request.GetScreenScale()
	c.ScreenPixels = request.GetScreenPixels()
	c.Xpsid = util.StringPtr2String(request.Xpsid)
	c.XpsidRoot = util.StringPtr2String(request.XpsidRoot)
	c.EventKey = request.EventKey
	c.FontScaleType = util.Int32Ptr2Int32(request.FontSacleType)
}

func (c *CommonInfo) InitByReqAndOrderInfo(ctx context.Context, orderInfo *dos.OrderInfo, request OrderMatchRequest, orderFeature *ufs.OrderFeature) {
	c.MenuID = consts.MenuIDDaCheAnyCar
	c.Lang = request.GetLang()
	c.AppVersion = request.GetAppVersion()
	c.AccessKeyID = request.GetAccessKeyId()
	c.ClientType = request.GetClientType()
	c.PageType = request.GetPageType()
	c.OriginID, _ = strconv.ParseInt(orderFeature.OriginId, 10, 64)

	if cType, err := strconv.ParseInt(orderInfo.CallCar, 10, 32); err == nil {
		c.CallCarType = int32(cType)
		if cType == 1 {
			c.CallCarPhone = orderInfo.PassengerPhone
		}
	}
	if orderInfo.PromiseType != "" && orderInfo.PromiseTime != "" {
		c.OrderType = order.OrderTypeNow
	} else {
		c.OrderType = util.String2int32(ctx, orderInfo.Type)
	}

	c.PlatformType = util.String2int32(ctx, orderFeature.PlatformType)
	c.Channel = util.String2int64(ctx, orderFeature.Channel)
	c.PaymentsType = util.String2int32(ctx, orderInfo.PayType)
	c.DepartureTime = util.DateStringToUnixTime(ctx, orderInfo.DepartureTime)
	c.AgentType = agent_type.AgentTypeBothCallAnyCar
	c.MainEstimateTraceId = orderFeature.EstimateTraceId
}

// 构建dds/products入参
func (c *CommonInfo) genDDSProductsReq(request *EstimateDecision.ProductsReq) {
	if request.CommonInfo == nil {
		request.CommonInfo = &EstimateDecision.CommonInfoV2{}
	}
	request.CommonInfo.AppVersion = c.AppVersion
	request.CommonInfo.AccessKeyId = int64(c.AccessKeyID)
	request.CommonInfo.ClientType = int64(c.ClientType)
	request.CommonInfo.Channel = c.Channel
	request.CommonInfo.Lang = c.Lang
	request.CommonInfo.Imei = c.Imei
	request.CommonInfo.From = c.From
	request.CommonInfo.OrderType = int16(c.OrderType)
	request.CommonInfo.CallCarType = int64(c.CallCarType)
	request.CommonInfo.DepartureTime = int32(c.DepartureTime)
	if len(c.DepartureRange) > 0 {
		request.CommonInfo.DepartureRange = util.JustJsonEncode(c.DepartureRange)
	}
	request.CommonInfo.MenuId = c.MenuID
	request.CommonInfo.PageType = int64(c.PageType)
	request.CommonInfo.FromType = cast.ToString(c.FromType)
	request.CommonInfo.LuxurySelectCarlevels = &c.LuxurySelectCarlevels
	if c.AirportType != 0 {
		request.CommonInfo.AirportType = &c.AirportType
	}
	if c.StopoverPoints != nil {
		request.CommonInfo.StopoverPoints = c.StopoverPoints
	}
	request.CommonInfo.SourceId = c.SourceID
	request.CommonInfo.SelectedCarlevel = &c.SelectedCarlevel
	request.CommonInfo.SixSeatSelectedCarlevel = &c.SixSeatSelectedCarlevel
	request.CommonInfo.TabId = &c.TabId
	request.CommonInfo.AirportType = &c.AirportType
	request.CommonInfo.DepartureTime = int32(c.DepartureTime)
}

func (c *CommonInfo) BuildPriceReq(request *price_api.PriceEstimateReq) {

	if request.CommonInfoSt == nil {
		request.CommonInfoSt = &PriceApi.CommonInfoSt{}
	}

	request.CommonInfoSt.Imei = c.Imei
	request.CommonInfoSt.Lang = c.Lang
	request.CommonInfoSt.AppVersion = c.AppVersion
	request.CommonInfoSt.ClientType = int64(c.ClientType)
	request.CommonInfoSt.IsFromWebapp = client_type.IsFromWebApp(c.ClientType)
	request.CommonInfoSt.IsFromB2B = client_type.IsFromB2B(c.ClientType)
	request.CommonInfoSt.PlatformType = &c.PlatformType
	request.CommonInfoSt.OriginId = c.OriginID
	request.CommonInfoSt.RouteId = &c.RouteID
	request.CommonInfoSt.Channel = &c.Channel
	if request.OrderExt == nil {
		request.OrderExt = &price_api.OrderExt{}
	}

	request.OrderExt.AccessKeyID = c.AccessKeyID
	request.OrderExt.MenuID = c.MenuID
	request.OrderExt.PageType = c.PageType
	request.OrderExt.AgentType = c.AgentType
	timeNow := time.Now().Unix()
	request.OrderExt.BubbleTime = &timeNow

	if request.OrderInfoSt == nil {
		request.OrderInfoSt = &PriceApi.OrderInfoSt{}
	}
	request.OrderInfoSt.Channel = c.Channel
	request.OrderInfoSt.ActivityId = int64(c.ActivityID)
	request.OrderInfoSt.PaymentsType = c.PaymentsType
	request.OrderInfoSt.CallCarPhone = c.CallCarPhone
	request.OrderInfoSt.CallCarType = int64(c.CallCarType)
	request.OrderInfoSt.UserType = int64(c.UserType)
	request.OrderInfoSt.OrderType = int64(c.OrderType)
	//if c.LuxurySelectDriver != "" {
	//	request.OrderExt.DesignatedDriver = c.LuxurySelectDriver
	//	request.OrderExt.DesignatedDriverTag = c.LuxurySelectDriver
	//}

	// 城际拼车时间片 (如果端上传了用端的上)
	if len(c.DepartureRange) == 2 {
		bytes, err := json.Marshal(c.DepartureRange)
		if err == nil {
			request.OrderExt.DepartureRange = string(bytes)
			request.OrderInfoSt.DepartureTime = util.ToInt64(c.DepartureRange[1])
		}
	}
	if c.DepartureTime > 0 {
		request.OrderInfoSt.DepartureTime = c.DepartureTime
	}
	if c.BusinessOption != "" {
		if request.ExtraInfo == nil {
			request.ExtraInfo = make(map[string]string)
		}
		request.ExtraInfo["business_option"] = c.BusinessOption
	}
	//多路线
	if len(c.PreferredRouteId) > 0 {
		routeId, err := strconv.Atoi(c.PreferredRouteId)
		if err == nil && routeId > 0 {
			route := int64(routeId)
			request.CommonInfoSt.RouteId = &route

		}
		if request.ExtraInfo == nil {
			request.ExtraInfo = make(map[string]string)
		}
		request.ExtraInfo["preferred_route_id"] = c.PreferredRouteId
	}
}

func (c *CommonInfo) SetSourceID(sourceID int32) {
	if c == nil {
		return
	}
	c.SourceID = sourceID
}

func (c *CommonInfo) SetRiskCode(riskCode int32) {
	if c == nil {
		return
	}
	c.RiskCode = riskCode
}

func (c *CommonInfo) GetLangWithDefault(defaultLang string) string {
	if c.Lang == "" {
		return defaultLang
	}
	return c.Lang
}
