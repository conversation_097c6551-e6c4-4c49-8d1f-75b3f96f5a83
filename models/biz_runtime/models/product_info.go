package models

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/redis"
	"git.xiaojukeji.com/nuwa/trace"
	"git.xiaojukeji.com/s3e/common-lib/v2/component/diff"
	"strconv"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
	Plutus "git.xiaojukeji.com/dirpc/dirpc-go-http-Plutus"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/combo_type"
	bizconsts "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	page_type2 "git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	tcutil "git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tripcloud"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/reqctx"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	context2 "git.xiaojukeji.com/lego/context-go"
)

type ProductCategory int64

type Product struct {
	EstimateID            string `json:"estimate_id"`
	ProductCategory       int64  `json:"product_category"`
	OrderType             int16  `json:"order_type"`
	ProductID             int64  `json:"product_id"`
	BusinessID            int64  `json:"business_id"`
	RequireLevel          string `json:"require_level"` // 因为php的nTuple sdk 和 go里的NTuple 都是string
	RequireLevelInt       int64  `json:"-"`
	CarpoolType           int64  `json:"carpool_type"`
	LevelType             int32  `json:"level_type"`
	SpaciousCarAlliance   int32  `json:"spacious_car_alliance"`
	ComboType             int64  `json:"combo_type"`
	SceneType             int64  `json:"scene_type"`
	IsSpecialPrice        bool   `json:"is_special_price"`
	CarpoolPriceType      int32  `json:"carpool_price_type"`
	IsDualCarpoolPrice    bool   `json:"is_dual_carpool_price"`
	AirportType           int32  `json:"airport_type"`
	RailwayType           int32  `json:"railway_type"`
	HotelType             int32  `json:"hotel_type"`
	StationServiceControl int32  `json:"station_service_control"`
	OType                 int32  `json:"otype"`
	PaymentsType          int32  `json:"payments_type"`
	SubGroupId            int32  `json:"sub_group_id"` // 聚合ID
	LongRentType          int16  `json:"long_rent_type"`
	CommercialtType       int16  `json:"commercial_type"`
	EmergencyServiceType  int32  `json:"emergency_service_type"`
	// AirportId			  string `json:"airport_id"`
	RouteType    int64           `json:"route_type"`
	ExamType     int32           `json:"exam_type"`
	IsPickOnTime int16           `json:"is_pick_on_time"`
	RouteID      string          `json:"route_id"`
	ShiftID      string          `json:"shift_id"`
	BizInfo      *PrivateBizInfo `json:"-"`
	IsTripcloud  *bool           `json:"is_tripcloud"` // 使用方先确定是否初始化
}

func (p *Product) IsTripcloudProduct(ctx context.Context) bool {
	if p.IsTripcloud != nil {
		return *p.IsTripcloud
	}

	return tcutil.IsTripcloudProductID(ctx, bizconsts.ProductID(p.ProductID))
}

func (p *Product) BuildFromDDsResponse(ctx context.Context, ddsProduct *EstimateDecision.Product, baseReq *BaseReqData) {
	p.ProductCategory = ddsProduct.ProductCategory
	p.OrderType = ddsProduct.OrderType
	p.ProductID = ddsProduct.ProductId
	p.BusinessID = ddsProduct.BusinessId
	p.RequireLevelInt = ddsProduct.RequireLevel
	p.RequireLevel = strconv.FormatInt(ddsProduct.RequireLevel, 10)
	p.CarpoolType = ddsProduct.CarpoolType
	p.ComboType = ddsProduct.ComboType
	p.IsSpecialPrice = ddsProduct.IsSpecialPrice
	p.CarpoolPriceType = ddsProduct.CarpoolPriceType
	p.IsDualCarpoolPrice = ddsProduct.IsDualCarpoolPrice
	p.AirportType = ddsProduct.AirportType
	p.RailwayType = ddsProduct.RailwayType
	p.HotelType = ddsProduct.HotelType
	p.ExamType = int32(ddsProduct.ExamType)
	p.StationServiceControl = ddsProduct.StationServiceControl
	p.LevelType = ddsProduct.LevelType
	p.SpaciousCarAlliance = ddsProduct.SpaciousCarAlliance
	p.RouteType = ddsProduct.RouteType
	p.LongRentType = baseReq.CommonBizInfo.LongRentType
	if p.LongRentType == 0 && ddsProduct.LongRentType != nil && *ddsProduct.LongRentType != 0 {
		p.LongRentType = *ddsProduct.LongRentType
	}
	p.CommercialtType = baseReq.CommonBizInfo.CommericalType
	p.EmergencyServiceType = baseReq.CommonBizInfo.EmergencyServiceType
	if ddsProduct.OrderInfo != nil {
		if ddsProduct.OrderInfo.AirportInfo != nil {
			p.OType = ddsProduct.OrderInfo.AirportInfo.Otype
		}
	}
	p.IsPickOnTime = baseReq.CommonBizInfo.IsPickOnTime
	p.BuildEstimateID(ctx, &baseReq.AreaInfo, &baseReq.CommonInfo)

	if p.BizInfo == nil {
		p.BizInfo = &PrivateBizInfo{}
	}
	p.BizInfo.build(ctx, &baseReq.CommonInfo, &baseReq.CommonBizInfo, ddsProduct)
	p.IsTripcloud = &ddsProduct.IsTripcloud
}

func (p *Product) BuildEstimateID(ctx context.Context, areaInfo *AreaInfo, commonInfo *CommonInfo) {
	if diff.CheckDiffStatus(ctx) {
		if cacheEstimateID := p.buildEstimateIDByCache(ctx); cacheEstimateID != "" {
			p.EstimateID = cacheEstimateID
			return
		}
	}

	traceId := context2.GetTrace(ctx).GetTraceId()
	var randCount = 0
	if ctxValue := reqctx.GetInt(ctx, "#eid_rand_count"); ctxValue >= 0 {
		randCount = ctxValue
	}
	defer func() {
		randCount++
		reqctx.Set(ctx, "#eid_rand_count", randCount)
	}()

	// 将起终点经纬度加入
	ori_dest_latlng := ""
	if areaInfo != nil {
		ori_dest_latlng = util.ToString(areaInfo.FromLng) + util.ToString(areaInfo.FromLat) + util.ToString(areaInfo.ToLng) + util.ToString(areaInfo.ToLat)
	}
	page_source_type := ""
	if commonInfo != nil {
		page_source_type = util.ToString(commonInfo.PageType) + "_" + util.ToString(commonInfo.SourceID)
	}

	// eid的生成添加必有车标识，防止出现等待应答页面追加车型预估和必有车预估estimate_id一致的问题
	eidStr := fmt.Sprintf("%s_%d_%s_%d_%d_%d_%d_%s_%s", traceId, p.ProductID, p.RequireLevel, p.ComboType, p.ProductCategory, p.IsPickOnTime, randCount, ori_dest_latlng, page_source_type)

	hash := md5.Sum([]byte(eidStr))
	p.EstimateID = fmt.Sprintf("%x", hash)
}

func (p *Product) BuildEstimateIDByRouteID(ctx context.Context, areaInfo *AreaInfo) {
	traceId := context2.GetTrace(ctx).GetTraceId()
	var randCount = 0
	if ctxValue := reqctx.GetInt(ctx, "#eid_rand_count"); ctxValue >= 0 {
		randCount = ctxValue
	}
	defer func() {
		randCount++
		reqctx.Set(ctx, "#eid_rand_count", randCount)
	}()

	// 将起终点经纬度加入
	oriDestLatLng := ""
	if areaInfo != nil {
		oriDestLatLng = util.ToString(areaInfo.FromLng) + util.ToString(areaInfo.FromLat) + util.ToString(areaInfo.ToLng) + util.ToString(areaInfo.ToLat)
	}

	// eid的生成添加必有车标识，防止出现等待应答页面追加车型预估和必有车预估estimate_id一致的问题
	eidStr := fmt.Sprintf("%s_%d_%s_%d_%d_%d_%d_%s_%s", traceId, p.ProductID, p.RequireLevel, p.ComboType, p.ProductCategory, p.IsPickOnTime, randCount, oriDestLatLng, p.RouteID)

	hash := md5.Sum([]byte(eidStr))
	p.EstimateID = fmt.Sprintf("%x", hash)
}

// BuildEstimateByOption 生成eid
func (p *Product) BuildEstimateByOption(ctx context.Context, options []string) {
	traceId := context2.GetTrace(ctx).GetTraceId()
	var randCount = 0
	if ctxValue := reqctx.GetInt(ctx, "#eid_rand_count"); ctxValue >= 0 {
		randCount = ctxValue
	}
	defer func() {
		randCount++
		reqctx.Set(ctx, "#eid_rand_count", randCount)
	}()

	eidStr := fmt.Sprintf("%s_%d_%s_%d_%d_%d_%d", traceId, p.ProductID, p.RequireLevel, p.ComboType, p.ProductCategory, p.IsPickOnTime, randCount)
	for _, option := range options {
		if option == "" {
			continue
		}

		eidStr += fmt.Sprintf("_%s", option)
	}

	hash := md5.Sum([]byte(eidStr))
	p.EstimateID = fmt.Sprintf("%x", hash)
}

func (p *Product) BuildEstimateIDByShiftID(ctx context.Context, skuInfo *SkuInfo) {
	traceId := context2.GetTrace(ctx).GetTraceId()
	var randCount = 0
	if ctxValue := reqctx.GetInt(ctx, "#eid_rand_count"); ctxValue >= 0 {
		randCount = ctxValue
	}
	defer func() {
		randCount++
		reqctx.Set(ctx, "#eid_rand_count", randCount)
	}()

	// 将起终点站点经纬度加入
	oriDestLatLng := ""
	if skuInfo != nil {
		oriDestLatLng = skuInfo.ShiftID + util.ToString(skuInfo.DepartureTime) + util.ToString(skuInfo.StartStationID) + util.ToString(skuInfo.EndStationID)
	}

	// eid的生成添加必有车标识，防止出现等待应答页面追加车型预估和必有车预估estimate_id一致的问题
	eidStr := fmt.Sprintf("%s_%d_%s_%d_%d_%d_%d_%s_%s", traceId, p.ProductID, p.RequireLevel, p.ComboType, p.ProductCategory, p.IsPickOnTime, randCount, oriDestLatLng, p.RouteID)

	hash := md5.Sum([]byte(eidStr))
	p.EstimateID = fmt.Sprintf("%x", hash)
}

func (p *Product) BuildPriceReq(ctx context.Context, caller string, request *price_api.PriceEstimateReq, baseReq *BaseReqData) {
	if request.CommonInfoSt == nil {
		request.CommonInfoSt = &PriceApi.CommonInfoSt{}
	}
	request.CommonInfoSt.BusinessId = p.BusinessID

	if request.OrderInfoSt == nil {
		request.OrderInfoSt = &PriceApi.OrderInfoSt{}
	}
	request.OrderInfoSt.ProductId = p.ProductID
	request.OrderInfoSt.ComboType = p.ComboType
	request.OrderInfoSt.RequireLevel = p.RequireLevel
	request.OrderInfoSt.SceneType = p.SceneType
	request.OrderInfoSt.IsFastCar = product_id.IsFastCar(p.ProductID)

	if request.OrderExt == nil {
		request.OrderExt = &price_api.OrderExt{}
	}
	request.OrderExt.IsSpecialPrice = p.IsSpecialPrice
	request.OrderExt.EstimateID = p.EstimateID
	request.OrderExt.ProductCategory = p.ProductCategory
	request.OrderExt.LevelType = p.LevelType
	request.OrderExt.SpaciousCarAlliance = p.SpaciousCarAlliance
	request.OrderExt.ExamType = p.ExamType
	if orderNTupleStr, err := json.Marshal(p); err == nil {
		nTuple := &price_api.OrderNTuple{}
		err = json.Unmarshal(orderNTupleStr, nTuple)
		if err == nil {
			request.OrderExt.OrderNTuple = nTuple
		}
	}

	//个性化服务
	request.OrderExt.PersonalizedCustomOptions = p.getServiceMap(caller, baseReq, request.OrderExt.PersonalizedCustomOptions)

	if request.ExtraInfo == nil {
		request.ExtraInfo = make(map[string]string)
	}
	request.ExtraInfo["product_category"] = strconv.FormatInt(p.ProductCategory, 10)

	// 用bizInfo补充price入参
	if p.BizInfo != nil {
		p.BizInfo.BuildPriceReq(ctx, request)

		if p.ComboType == combo_type.ComboTypeCarpoolInterCity && p.BizInfo.SpecialOrderInfo != nil {
			request.OrderInfoSt.OrderType = int64(p.BizInfo.SpecialOrderInfo.OrderType)
			request.OrderInfoSt.DepartureTime = p.BizInfo.SpecialOrderInfo.DepartureTime
		}

		if request.OrderExt.PageType == page_type2.PageTypeLowCarpoolEstimate {
			request.OrderInfoSt.OrderType = int64(p.OrderType)
		}
	}
}

func (p *Product) GetComboType() int64 {
	return p.ComboType
}

func (p *Product) GetProductId() int64 {
	return p.ProductID
}

func (p *Product) GetCarpoolType() int64 {
	return p.CarpoolType
}

func (p *Product) GetProductCategory() int64 {
	return p.ProductCategory
}

func (p *Product) GetRequireLevel() string {
	return p.RequireLevel
}

func (p *Product) GetRequireLevelInt() int64 {
	return p.RequireLevelInt
}

func (p *Product) getServiceMap(caller string, baseReq *BaseReqData, serviceMap map[int64]*Plutus.PersonalizedCustomizedOptions) map[int64]*Plutus.PersonalizedCustomizedOptions {
	//个性化服务
	if len(serviceMap) == 0 || serviceMap == nil {
		serviceMap = make(map[int64]*Plutus.PersonalizedCustomizedOptions)
	}

	for _, feature := range p.BizInfo.CustomFeatureList {
		switch feature.ServiceId {
		case consts.ServiceIdFemaleDriver: //夜间乘车偏好-女司机
			if baseReq.CommonBizInfo.IsFemaleDriverFirst == 1 {
				serviceMap[consts.ServiceIdFemaleDriver] = &Plutus.PersonalizedCustomizedOptions{
					Count: 1,
				}
			}
		case consts.ServiceIDUniTaxi:
			if product_id.ProductIdUniOne == p.ProductID && p.BizInfo.IsTaxiPeakFeeUserSelected {
				// 适用出租车峰期加价场景
				// 当前城市配置有出口模式，追加车型强制模拟无出口--模拟用户勾选
				if consts.CanSelect == strconv.Itoa(p.BizInfo.TaxiSps.CanSelect) {
					serviceMap[consts.ServiceIDUniTaxi] = &Plutus.PersonalizedCustomizedOptions{
						Count: 1,
					}
				}
			}

			//如果是新链路，且信息费无出口，说明需要默认入账
			if product_id.ProductIdUniOne == p.ProductID &&
				p.BizInfo.IsSwitchIntel &&
				consts.CanSelect != strconv.Itoa(p.BizInfo.TaxiSps.CanSelect) {
				serviceMap[consts.ServiceIDUniTaxi] = &Plutus.PersonalizedCustomizedOptions{
					Count: 1,
				}
			}
		case consts.DriverPickUpService:
			if caller != "pBusinessEstimate" {
				//目前只有企业级
				continue
			}

			if len(baseReq.CommonBizInfo.MultiRequireProduct) == 0 || len(baseReq.CommonBizInfo.PcId2CustomFeature) == 0 {
				//用户未勾选
				continue
			}

			userSelectList := baseReq.CommonBizInfo.PcId2CustomFeature[p.ProductCategory]
			for _, serviceInfo := range userSelectList {
				if serviceInfo.Id != consts.DriverPickUpService || serviceInfo.Count == 0 {
					continue
				}

				serviceMap[consts.DriverPickUpService] = &Plutus.PersonalizedCustomizedOptions{
					Count: 1,
				}
			}
		}
	}

	return serviceMap
}

func (p *Product) buildEstimateIDByCache(ctx context.Context) string {
	traceId := context2.GetTrace(ctx).GetTraceId()
	cacheKey := fmt.Sprintf("%s-%s-%d-%s-%d-%d", diff.MultiEstimateDiffPrefix, traceId, p.ProductID, p.RequireLevel, p.ComboType, p.ProductCategory)
	res, err := redis.GetMultiEstimateClient().Get(ctx, cacheKey)
	if err != nil || res == "" {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "get estimate_id from cache failed, err: %v, %v", err, res)
		return ""
	}

	return res
}
