package models

import (
	"context"
	"encoding/json"
	Sps "git.xiaojukeji.com/dirpc/dirpc-go-http-Sps"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend/model"
	"strconv"
	"strings"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/source_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"

	hundunClient "git.xiaojukeji.com/dirpc/dirpc-go-http-Hundun"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_category"

	ticketPrice "git.xiaojukeji.com/dirpc/dirpc-go-http-TicketPrice"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/models/carpoolstation"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/route_type"

	"git.xiaojukeji.com/gulfstream/mamba/models/entity"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/charter_car/charter_open_products"

	Dirpc_SDK_Ferrari "git.xiaojukeji.com/dirpc/dirpc-go-http-Ferrari"

	TripCloudPassenger "git.xiaojukeji.com/dirpc/dirpc-go-http-TripCloudPassenger"
	DynamicPrice "git.xiaojukeji.com/dirpc/dirpc-go-thrift-DapeDynamicPrice"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dfs"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	dirpcSdkBrick "git.xiaojukeji.com/dirpc/dirpc-go-http-Brick"
	Carpool "git.xiaojukeji.com/dirpc/dirpc-go-http-Carpool"
	Compensation "git.xiaojukeji.com/dirpc/dirpc-go-http-Compensation"
	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
	midl "git.xiaojukeji.com/dirpc/dirpc-go-http-Member"
	Plutus "git.xiaojukeji.com/dirpc/dirpc-go-http-Plutus"
	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	Dirpc_SDK_Vcard "git.xiaojukeji.com/dirpc/dirpc-go-http-Vcard"
	Vcard "git.xiaojukeji.com/dirpc/dirpc-go-http-Vcard"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	CarpoolApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolApi"
	CarpoolOpenApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolOpenApi"
	HotspotDIRPC "git.xiaojukeji.com/dirpc/dirpc-go-thrift-Hotspot"
	passenger_common "git.xiaojukeji.com/gulfstream/passenger-common/dto"
	ptsConsts "git.xiaojukeji.com/s3e/pts/common/consts"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/hestia_charge"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

// 非通用的request参数
type CommonBizInfo struct {
	MultiRequireProduct []RequireProduct `json:"multi_require_product"`
	RecForm             int32            `json:"rec_form"`
	FormStyleExp        int32            `json:"form_style_exp"`
	// 统一存放推荐策略结果
	AthenaResult

	PassengerCount *int32                    `json:"passenger_count"`
	CarpoolSeatNum int32                     `json:"carpool_seat_num"`
	BargainForm    string                    `json:"bargain_form"`    // 司乘议价来源标识
	CustomFeature  []dos.CustomFeatureStruct `json:"custom_feature"`  // dos查询出的用户勾选的[增值服务+品类下服务]
	TpInfoMap      map[int32]*TpInfo         `json:"tp_info_map"`     // TP  map[sub_group_id]数据
	LongRentType   int16                     `json:"long_rent_type"`  // 助手接口使用 是否为客企包车
	CommericalType int16                     `json:"commerical_type"` // 是否内部会议用车

	EmergencyServiceType int32                               `json:"emergency_service_type"` // 保障车队类型
	PcId2CustomFeature   map[int64][]dos.CustomFeatureStruct // dos查询出的用户勾选的[增值服务+品类下服务]
	//SelectPcId2CustomFeature    map[int64][]FeatureItem             // 用户选择的增值服务
	PersonalizedCustomOptions   map[int64]*Plutus.PersonalizedCustomizedOptions
	NoAnswerCompensationData    []*Compensation.GetNoAnswerCompensationTackData // 未应答赔付信息
	DefaultSelectedCompensation *DefaultSelectedCompensation
	BubbleCompensationStatus    int32                               // 冒泡命中平台无车赔
	IdentityPageInfo            *dirpcSdkBrick.IdentityPageInfoData // 实名身份信息

	FastCarPrice        float64 `json:"fast_car_price"` // 快车价格用来对比拼车金额
	PricingByMeterPrice float64 // 打表计价预估价格
	SourceId            int32   // 城际接口入口来源id
	RouteId             int64   // 城际接口请求的路线id
	StartStationId      int64   // 城际接口请求的起始站id
	EndStationId        int64   // 城际接口请求的终点站id
	IsPickOnTime        int16   `json:"is_pick_on_time"` // 必有车标识,1必有车

	IsFemaleDriverFirst     int64 // 用户是否倾向女司机优先
	IsShowFemaleDriverFirst int64 // 是否给用户展示女司机优先标签

	SeatDetailInfo      []*SeatDetailInfo
	PassengerDetailInfo *PassengerDetailInfo
	RuleData            *dirpcSdkBrick.RuleData
	IsRestSeatInfo      bool // 座位数是否因为不符合规则被重置  不符合规则时会弹弹窗
	IsNewPageUpgrade    bool // 是否新版页面升级

	IsGuide       int32 `json:"is_guide"`
	IsBestShift   int32 // 0是, 1否 是否最优班次
	ValidScanCode bool  // 扫码上车班次有效

	OpenAppointmentRange int64  // 放开预约时间
	Scene                string // 预估场景

	BusServiceShiftId          string // 大巴班次
	IsSort                     int32  // 班次列表页面是否需要排序
	PreBusServiceShiftId       string
	PreDepartureTime           int64
	SeletedBusServiceShiftId   string // 勾选的大巴班次
	SelectedBusServiceDay      string // 勾选的大巴预约时间
	IsCommonCashierOrder       bool   // 是否使用通用收银台
	CanGetIdentityFromGuardian bool   // 可以从用户中心获取实名信息
	RouteType                  int64

	PassengerTickerInfo []*passenger_common.PassengerDetail
	PreOrderID          string
	PreEstimateID       string
	PreHighOrderID      string

	SeatNumInfo *Carpool.SeatResult // 小巴座位信息

	MaxInventory              int32
	CarryChildrenMaxInventory int32
	CarryChildrenIsOccupySeat int32

	// 站点巴士批量页的数据
	StationInfo StationInfo

	// 站点巴士筛选使用站点列表带区县信息（V2版本）
	StationListV2 StationListV2
	StartStation  *Prfs.StationInfo
	EndStation    *Prfs.StationInfo

	// 站点巴士起终点城市/区县/站点信息归总
	StartItemInfo *ItemInfo
	EndItemInfo   *ItemInfo

	// 小巴数据
	MiniBusData

	// 优惠情况
	DatesCouponInfo map[int64]bool
	SceneList       string

	// duse大巴班次维度的信息
	ShiftInfo *CarpoolOpenApi.ShiftDetail
	// duse大巴批量班次纬度的信息
	BatchShiftInfo map[string]*CarpoolOpenApi.ShiftDetail
	// 站点巴士扫码上车线路价格
	RoutePrice     *ticketPrice.StationPriceData
	RouteDetail    *Prfs.RouteDetailData
	AgentType      string  // 扫码类型
	CarPlate       string  // 车牌号
	AdultPrice     float64 // 成人票价
	NeedVisitPrice bool    // 是否需要访问priceapi
	CorrectStation bool    // 是否切换站点
	SceneType      int32   // 代发单大巴场景类型

	CustomTimeOutConf map[string]int // 个性化超时时间配置 rpc阶段->超时

	OrderEstimateData
	PbdStationBusData  PbdStationBusData                                // pbd大巴数据
	CharterOpenProduct *charter_open_products.CharterOpenProductHandler // 包车开城
	StationPropertyMap map[int32][]*Prfs.PropertyInfo                   // 站点对应的属性

	BusRegionInventoryList []*CarpoolOpenApi.BusRegionInventory // 14天的班次信息
	ComboId                int32                                // 套餐id
	RouteDirection         int                                  // 景区接驳车路线方向
	FenceId                string                               // 景区接驳车用户所在区域围栏

	BusShiftInventoryRobinData map[string]*carpoolstation.BusServerShift // 班次对应的源数据

	PetInfo                *PetInfo        // 宠物信息
	BusCardSelectedBatchId int32           // 用户选择的卡批次id
	InvitationInfo         *InvitationInfo // 邀请信息

	//公交小巴
	SmartBusData *SmartBusData

	//预匹配TP信息
	PreMatchTPErrNo int32

	// 商旅订单类型：xinzhu
	BusinessTravelOrderType string `json:"business_travel_order_type"`

	AvailableServiceList []int64
	DdsResult

	PeakFee          *PeakFee
	HadFilterProduct bool

	ClassifyInfo
}

type ClassifyInfo struct {
	BaseCategoryConf *BaseCategoryConf
}

type InvitationInfo struct {
	EstimateType          int32   // 预估类型 0:普通预估｜ 1:恢复首次顺路预估
	InviterOid            string  // 邀约者低位订单号
	InviterHighOid        string  // 邀约者高位订单号
	InviterPassengerCount int32   // 邀约者乘车人数
	InviterOrderType      int16   // 邀约者订单类型
	InviterDepartureRange []int64 // 邀约者出发时间片
}

type ItemInfo struct {
	CityId      int32
	CountyId    int32
	StationId   int32
	CityName    string
	CountyName  string
	StationName string
	Lat         float64
	Lng         float64
}

type OrderEstimateData struct {
	TravelQuotation              *Dirpc_SDK_Ferrari.TravelQuotation
	IsHitMemberPrivilegeAddAPlus bool
}

type PbdStationBusData struct {
	BizSceneType string // 场景类型
	RouteId      int64  // 路线id
}

type MiniBusData struct {
	TokenInfo *TokenInfo

	BOnlyMinibus bool
}

type TokenInfo struct {
	MapinfoStartCacheToken string
	MapinfoDestCacheToken  string
}

type AthenaResult struct {
	// 公用-策略
	ProductSortInfo *AthenaApiv3.ProductSortInfo // 品类排序信息

	// 公用-数据
	ETInfoMap          map[int32]*AthenaApiv3.EstimatedTimeInfo // 车型预期信息
	SubGroup2RecPos    map[int32]int32                          // 聚合车型推荐信息 sub_group_id => RecPos
	SubGroup2IsOutBox  map[int32]bool                           // 聚合车型推荐信息 sub_group_id => 是否出盒子
	PcID2NewSubGroupId map[int32]int32                          // 聚合车型推荐信息 pcid => 新的盒子s_g_id
	TopRec             *AthenaApiv3.TopRecRes                   // 置顶数据
	TopData            *TopData                                 // athena返回的置顶数据
	FilterInfo         *AthenaApiv3.BubbleFilterInfo            // 筛选器 todo:未使用

	// 公用-推荐信息
	RecPosMap map[int32]int32 // 推荐车型位置

	GroupId2RecPos     map[string]int32
	GroupId2CategoryId map[string]int32
	GroupId2FoldType   map[string]int32 //车型折叠信息

	GuidePcIds            []int32                              // 导流车型
	RecommendInfoMap      map[int32]*AthenaApiv3.RecommendInfo // 推荐信息 ShowType1:标签推荐  2:气泡推荐语
	RecommendExtraInfoMap map[int32]map[string]string          // athena的rec_result.extra_info (bargain_scene_recommend:司乘议价是否推荐态)

	// 默认拼 表单独有
	RecCarpoolInfo      RecCarpoolInfo `json:"rec_carpool_info"`       // 默认拼 Athena数据整合
	RecCarpoolFormSytle int64          `json:"rec_carpool_form_sytle"` // 默认拼 标识表单形态
	// 拼成乐表单信息
	PinCheCheFormInfo PinCheCheFormInfo `json:"pin_che_che_form_info"`

	// 其他
	ShowCarNum           int
	HitLuxPremiumProtect int                          // 0没命中
	IsImbalanced         int32                        // 供需失衡 1:命中  0：没命中
	SupplySceneInfo      *AthenaApiv3.SupplySceneInfo // 供需数据

	RecFormFeatureModel *model.RecFormFeatureModel
	ExpectInfo          *AthenaApiv3.AthenaBubbleExpectInfoResp
	EstimateStripInfo   *AthenaApiv3.EstimateStripInfo
}

type BaseStation struct {
	StationId int32  `json:"stationid"`
	Lat       string `json:"lat"`
	Lng       string `json:"lng"`
	Name      string `json:"name"`
}

type StationListV2 struct {
	FromCounties   []*Prfs.County `json:"from_counties"`
	ToCounties     []*Prfs.County `json:"to_counties"`
	FromName       string         `json:"from_name"`
	ToName         string         `json:"to_name"`
	FromCityName   string         `json:"from_city_name"`
	ToCityName     string         `json:"to_city_name"`
	FromCountyName string         `json:"from_county_name"`
	ToCountyName   string         `json:"to_county_name"`
}

type StationInfo struct {
	RouteId        int32   `json:"route_id"`
	StartStationId int64   `json:"start_station_id"`
	EndStationId   int64   `json:"end_station_id"`
	FromLng        float64 `json:"from_lng"`
	FromLat        float64 `json:"from_lat"`
	ToLng          float64 `json:"to_lng"`
	ToLat          float64 `json:"to_lat"`
	StartPoiName   string  `json:"start_poi_name"`
	EndPoiName     string  `json:"end_poi_name"`
	StartPoiId     string  `json:"start_poi_id"`
	EndPoiId       string  `json:"end_poi_id"`
	StartCity      int32   `json:"start_city"`
	EndCity        int32   `json:"end_city"`
	StartTime      int32   `json:"start_time"`
	EndTime        int32   `json:"end_time"`
	DayTime        int32   `json:"day_time"`
	LastShiftId    string  `json:"last_shift_id"`
	StartCountyId  int32   `json:"start_county_id"` // 起点区县ID
	EndCountyId    int32   `json:"end_county_id"`   // 终点区县ID
	SupportCounty  int32   `json:"support_county"`  // 是否支持区县能力，传1标识下发区县相关数据，其他场景均下发旧结构数据
	ProductId      int32   `json:"product_id"`
	OpenSource     string  `json:"open_source"`

	SupportCorrectStationId bool `json:"support_correct_station_id"` // 是否支持切换站点的能力
	IsScanCode              bool `json:"is_scan_code"`
}

type SkuInfo struct {
	RouteGroup                int64                            `json:"route_group"`
	ProductID                 int32                            `json:"product_id"`
	DepartureTime             int64                            `json:"departure_time"`
	RemainSeats               int32                            `json:"remain_seats"`
	ShiftID                   string                           `json:"shift_id"`
	StartStationID            int64                            `json:"station_station_id"`
	EndStationID              int64                            `json:"end_station_id"`
	CarryChildrenMaxInventory int32                            `json:"carry_children_max_inventory"`
	ShiftType                 int32                            `json:"shift_type"`
	SrcCost                   *CarpoolOpenApi.StationRouteCost `json:"src_dist,omitempty"`
	DestCost                  *CarpoolOpenApi.StationRouteCost `json:"dest_dist,omitempty"`
	ExtraInfo                 map[string]string                `json:"extra_info"`
}

type DefaultSelectedCompensation struct {
	Decision    int32                                `json:"decision"`
	Contents    *DefaultSelectedCompensationContents `json:"compensation_contents"`
	IsAddInsure bool                                 `json:"is_add_insure"`
}

type DefaultSelectedCompensationContents struct {
	CouponAmount          int     `json:"coupon_amount"`
	CompensationTimeStr   string  `json:"compensation_time_str"`
	ProductCategoryList   []int64 `json:"product_category_list"`
	CompensationTimeRound int64   `json:"compensation_time_round"`
}

func (c *CommonBizInfo) GetChildTicket() *dirpcSdkBrick.SpecialSeatRuleDate {
	if c == nil || c.RuleData == nil || len(c.RuleData.SpecialSeatRules) < 1 {
		return nil
	}
	for _, data := range c.RuleData.SpecialSeatRules {
		if "child_ticket" == data.RuleName && data.IsSupport == 1 {
			return data
		}
	}
	return nil
}

func (c *CommonBizInfo) GetCarryChildTicket() *dirpcSdkBrick.SpecialSeatRuleDate {
	if c == nil || c.RuleData == nil || len(c.RuleData.SpecialSeatRules) < 1 {
		return nil
	}

	for _, data := range c.RuleData.SpecialSeatRules {
		if seat_selection_consts.CarryChildTicket == data.RuleName && data.IsSupport == 1 {
			return data
		}
	}

	// 旧逻辑 携童票依附儿童票
	for _, data := range c.RuleData.SpecialSeatRules {
		if seat_selection_consts.ChildTicket == data.RuleName && data.IsSupport == 1 {
			for _, childTicket := range data.GetTypePercent() {
				if childTicket.GetType() == seat_selection_consts.CarryChildren.ToInt64() {
					return &dirpcSdkBrick.SpecialSeatRuleDate{
						RuleName:    seat_selection_consts.CarryChildTicket,
						IsSupport:   1,
						TypePercent: []*dirpcSdkBrick.TypePercentList{childTicket},
						OccupySeat:  int64(c.CarryChildrenIsOccupySeat),
					}
				}
			}
		}
	}

	return nil
}

func (c *CommonBizInfo) GetChildTicketNew() *dirpcSdkBrick.SpecialSeatRuleDate {
	if c == nil || c.IdentityPageInfo == nil || c.IdentityPageInfo.RuleInfo == nil || len(c.IdentityPageInfo.RuleInfo.SpecialSeatRules) < 1 {
		return nil
	}
	for _, data := range c.IdentityPageInfo.RuleInfo.SpecialSeatRules {
		if "child_ticket" == data.RuleName && data.IsSupport == 1 {
			return data
		}
	}
	return nil
}

func (c *CommonBizInfo) GetCouponTicket() *dirpcSdkBrick.SpecialSeatRuleDate {
	if c == nil || c.IdentityPageInfo == nil || c.IdentityPageInfo.RuleInfo == nil || len(c.IdentityPageInfo.RuleInfo.SpecialSeatRules) < 1 {
		return nil
	}
	for _, data := range c.IdentityPageInfo.RuleInfo.SpecialSeatRules {
		if seat_selection_consts.CouponTicket == data.RuleName && data.IsSupport == 1 {
			return data
		}
	}
	return nil
}

func (c *CommonBizInfo) GetHomeOwnerTicketRule() *dirpcSdkBrick.SpecialSeatRuleDate {
	// 获取业主票规则
	if c == nil || c.RuleData == nil || len(c.RuleData.SpecialSeatRules) < 1 {
		return nil
	}
	for _, data := range c.RuleData.SpecialSeatRules {
		if "owner_ticket" == data.RuleName && data.IsSupport == 1 {
			return data
		}
	}
	return nil
}

func (c *CommonBizInfo) IsAllowHomeOwnerTicket() bool {
	// 是否是业主票
	rule := c.GetHomeOwnerTicketRule()
	if rule == nil {
		return false
	}
	for _, data := range rule.TypePercent {
		if util.InArrayInt64(data.Type, []int64{
			seat_selection_consts.HomeOwnerAdult.ToInt64(),
			seat_selection_consts.HomeOwnerChildren.ToInt64(),
			seat_selection_consts.HomeOwnerOldMan.ToInt64(),
		}) {
			return true
		}
	}

	return false
}

type RequireProduct struct {
	ProductCategory      int64  `json:"product_category"`
	IsSelect             int32  `json:"is_select"`
	IsSelected           int32  `json:"is_selected"` // 兼容端上 上传字段名称不一致。。。
	CarpoolSeatNum       int32  `json:"carpool_seat_num"`
	UseDpa               *int32 `json:"use_dpa"`
	CustomFeature        string `json:"custom_feature"`
	OptionSettings       string `json:"option_settings"`
	LuxurySelectDriver   string `json:"luxury_select_driver"`
	LuxurySelectCarlevel string `json:"luxury_select_carlevel"`
	PayType              int    `json:"pay_type"`
}

type OptionSetting struct {
	Title   string         `json:"title"`
	Options []*FeatureItem `json:"options"`
}

type FeatureItem struct {
	ID       int32 `json:"id"`
	OptionId int32 `json:"option_id"`
	Count    int32 `json:"count"`
}

type TopData struct {
	TopPcIdList       []int64 `json:"top_pc_id_list"`       // 单车型置顶
	TopSubGroupIdList []int64 `json:"top_subgroup_id_list"` // 聚合车型置顶
}

type TpInfo struct {
	TPDataList        []*AthenaApiv3.CommonItem `json:"tp_data_list"`
	TPRecCheckProduct int32                     `json:"tp_rec_check_product"`
	GroupInfo         map[string]string         `json:"group_info"`
}

type RecCarpoolPCInfo struct {
	// RecInfo    *AthenaApiv3.RecommendInfo `json:"rec_info"`
	IsSelected bool `json:"is_selected"`
}
type RecCarpoolInfo struct {
	RecCarpoolInfoMap             map[int32]*RecCarpoolPCInfo  `json:"rec_carpool_info_map"`              // Athena 品类维度 选中 推荐
	RecCarpoolOther               *AthenaApiv3.CarpoolFormInfo `json:"rec_carpool_other"`                 // Athena 表单维度
	RecCarpoolDefaultNum          int16                        `json:"rec_carpool_default_num"`           // 默认focus的座位数 -1 1 2 or 0（不拼座）
	RecCarpoolMultiRequireProduct map[int32]struct{}           `json:"rec_carpool_multi_require_product"` // 不拼座的用户二次预估 品类维度
}

type PinCheCheFormInfo struct {
	FormSytle        int64 `json:"form_sytle"`
	DefaultOrderType int64 `json:"default_order_type"`
}

func (c *CommonBizInfo) InitByRequest(ctx context.Context, request *proto.MultiEstimatePriceRequest) {
	_ = ctx
	c.CarpoolSeatNum = request.GetCarpoolSeatNum()
	// LongRentType 客企包车使用
	if request.LongRentType != nil && *(request.LongRentType) != 0 {
		c.LongRentType = int16(*request.LongRentType)
	}
}

func (c *CommonBizInfo) InitByOrderInfo(ctx context.Context, orderInfo *dos.OrderInfo, request OrderMatchRequest) {
	count := util.String2int32(ctx, orderInfo.PassengerCount)
	c.PassengerCount = &count
	c.CarpoolSeatNum = int32(request.GetCarpoolSeatNum())

	// 构建source_id
	c.buildSourceId(ctx, orderInfo, request)

	// 商旅订单类型
	c.BusinessTravelOrderType = orderInfo.ExtendFeatureParsed.BusinessTravelOrderType

	multiRequiredProduct := orderInfo.ExtendFeatureParsed.MultiRequiredProduct
	if len(multiRequiredProduct) > 0 {
		c.PcId2CustomFeature = make(map[int64][]dos.CustomFeatureStruct)

		for _, productInfo := range multiRequiredProduct {
			var res []dos.CustomFeatureStruct
			var sceneList []dos.SceneItem
			if len(productInfo.CustomFeature) >= 0 {
				err := json.Unmarshal([]byte(productInfo.CustomFeature), &res)
				if err == nil {
					c.PcId2CustomFeature[int64(productInfo.ProductCategory)] = res
				}
			}

			// 好友同行场景 追击表单车型继承场景特征
			if "" != productInfo.SceneList {
				err := json.Unmarshal([]byte(productInfo.SceneList), &sceneList)
				if err != nil {
					continue
				}

				for _, sceneItem := range sceneList {
					if ptsConsts.SceneIdFriendWith == sceneItem.Id && "" == c.Scene {
						extendScene, err := json.Marshal([]dos.SceneItem{sceneItem})
						if err != nil {
							continue
						}
						c.SceneList = string(extendScene)
						break
					}
				}
			}
		}
	}
}

func (c *CommonBizInfo) buildSourceId(ctx context.Context, orderInfo *dos.OrderInfo, request OrderMatchRequest) {
	// xinzhu 场景
	if orderInfo != nil && util.IsFromXinZhuWithBusinessTravelOrderType(orderInfo.ExtendFeatureParsed.BusinessTravelOrderType) {
		c.SourceId = source_id.SourceIDXinZhuAppendCar
	}
}

func (c *CommonBizInfo) BuildPriceReq(request *price_api.PriceEstimateReq, product *Product) {

	// dos中的[增值服务]构建price参数
	if len(c.PcId2CustomFeature) > 0 {
		serviceMap := make(map[int64]*Plutus.PersonalizedCustomizedOptions)

		pcID := product.ProductCategory
		if pcID <= 0 {
			pcID = request.OrderExt.ProductCategory
		}
		serviceList := c.PcId2CustomFeature[pcID]
		for _, featureStruct := range serviceList {
			serviceMap[featureStruct.Id] = &Plutus.PersonalizedCustomizedOptions{
				Count:       featureStruct.Count,
				ServiceType: featureStruct.ServiceType,
			}
		}
		c.PersonalizedCustomOptions = serviceMap
		request.OrderExt.PersonalizedCustomOptions = serviceMap
	}

	if c.SourceId != 0 {
		request.OrderExt.SourceID = c.SourceId
	}

	if c.BusinessTravelOrderType != "" {
		request.OrderExt.BusinessTravelOrderType = c.BusinessTravelOrderType
	}

	if c.SceneList != "" {
		request.OrderExt.SceneList = c.SceneList
	}
	if c.RouteDirection != 0 && c.FenceId != "" {
		request.OrderExt.RouteDirection = c.RouteDirection
		request.OrderExt.FenceId = c.FenceId
	}

	if request.OrderInfoSt != nil && request.OrderInfoSt.FromAddress == "" && c.StartStation != nil {
		request.OrderInfoSt.FromName = c.StartStation.StationName
	}

	if request.OrderInfoSt != nil && request.OrderInfoSt.ToAddress == "" && c.EndStation != nil {
		request.OrderInfoSt.ToName = c.EndStation.StationName
	}
	request.OrderInfoSt.PreOrderId = proto.StrPtr(c.PreOrderID)
	request.OrderInfoSt.PreEstimateId = proto.StrPtr(c.PreEstimateID)

	if c.PassengerTickerInfo != nil && len(c.PassengerTickerInfo) > 0 {
		request.OrderInfoSt.PassengerTicketDetail = make([]*PriceApi.PassengerTicketDetail, 0, len(c.PassengerTickerInfo))
		for _, item := range c.PassengerTickerInfo {
			if item == nil {
				continue
			}
			ticket := &PriceApi.PassengerTicketDetail{
				TicketId:     int64(item.TicketId),
				Type:         item.TicketType,
				Status:       item.Status,
				IsOccupySeat: item.IsOccupySeat,
				DiscountType: item.DiscountType,
			}
			request.OrderInfoSt.PassengerTicketDetail = append(request.OrderInfoSt.PassengerTicketDetail, ticket)
		}
	}
	if request.ExtraInfo == nil {
		request.ExtraInfo = make(map[string]string)
	}
	if len(c.BusServiceShiftId) > 0 {
		request.ExtraInfo["shift_id"] = c.BusServiceShiftId
	}

	if c.IsCommonCashierOrder {
		request.OrderInfoSt.IsCommonCashierOrder = &c.IsCommonCashierOrder
	}

	if len(c.PreHighOrderID) > 0 {
		request.OrderInfoSt.PreHighOrderId = &c.PreHighOrderID
	}

	// 宠物出行
	if c.PetInfo != nil {
		request.OrderExt.PetInfo = &price_api.PetInfo{}
		request.OrderExt.PetInfo.PetNo = c.PetInfo.PetNo
		request.OrderExt.PetInfo.PetType = c.PetInfo.PetType
		request.OrderExt.PetInfo.PetTypeDesc = c.PetInfo.PetTypeDesc
		request.OrderExt.PetInfo.NickName = c.PetInfo.NickName
		request.OrderExt.PetInfo.Avatar = c.PetInfo.Avatar
		request.OrderExt.PetInfo.WeightCategory = c.PetInfo.WeightCategory
		request.OrderExt.PetInfo.WeightCategoryDesc = c.PetInfo.WeightCategoryDesc
		request.OrderExt.PetInfo.PetServiceTag = c.PetInfo.PetServiceTag
		request.OrderExt.PetInfo.PersonalizedCustomOption = c.PetInfo.PersonalizedCustomOption
	}

	// 会议用车
	if c.CommericalType == 1 {
		request.ExtraInfo["meeting_service_fee_info"] = product.BizInfo.ServiceFeeInfo
	}
}

func (c *CommonBizInfo) InitByMultiRequireProduct(multiRequireProductString string) {
	if multiRequireProductString == "" {
		return
	}
	multiRequireProduct := []RequireProduct{}
	_ = json.Unmarshal([]byte(multiRequireProductString), &multiRequireProduct)
	c.MultiRequireProduct = multiRequireProduct
	if len(multiRequireProduct) > 0 {
		c.PcId2CustomFeature = make(map[int64][]dos.CustomFeatureStruct)
		for _, product := range multiRequireProduct {
			var res []dos.CustomFeatureStruct
			if len(product.CustomFeature) == 0 {
				continue
			}
			err := json.Unmarshal([]byte(product.CustomFeature), &res)
			if err != nil {
				continue
			}

			c.PcId2CustomFeature[product.ProductCategory] = res
		}
	}
}

// 构建dds/products入参
func (c *CommonBizInfo) genDDSProductsReq(request *EstimateDecision.ProductsReq) {
	if request.CommonInfo == nil {
		request.CommonInfo = &EstimateDecision.CommonInfoV2{}
	}
	if request.CommonInfo.SourceId == 0 && c.SourceId != 0 {
		request.CommonInfo.SourceId = c.SourceId
	}
	carpoolSeatNumber := int16(1)
	if c.CarpoolSeatNum != 0 {
		carpoolSeatNumber = int16(c.CarpoolSeatNum)
	}
	request.CommonInfo.CarpoolSeatNum = &carpoolSeatNumber
	request.CommonInfo.RouteId = &c.RouteId
	carpoolSeatNum := int16(c.CarpoolSeatNum)
	request.CommonInfo.CarpoolSeatNum = &carpoolSeatNum

	if c.InvitationInfo != nil {
		request.CommonInfo.InvitationType = 1
		request.CommonInfo.InvitationOrder = c.InvitationInfo.InviterHighOid
	}

	c.buildCustomForSmartBus(request)

}

func (c *CommonBizInfo) buildCustomForSmartBus(request *EstimateDecision.ProductsReq) {
	//公交小巴场景:起点
	if nil != c.SmartBusData && nil != c.SmartBusData.StartStationInfo {
		bt, _ := json.Marshal(c.SmartBusData.StartStationInfo) //NOLINT
		request.CommonInfo.StartStationInfo = string(bt)
	}

	//公交小巴场景:终点
	if nil != c.SmartBusData && nil != c.SmartBusData.DestStationInfo {
		bt, _ := json.Marshal(c.SmartBusData.DestStationInfo) //NOLINT
		request.CommonInfo.DestStationInfo = string(bt)
	}
}

type UserMemberProfile struct {
	UseDpaSelected      int32
	IsUserUseDpa        bool
	MemberProfile       *midl.V1QueryInfo
	PricePrivilegeInfo  map[string]interface{}
	MemberPrivilegeList map[string]interface{}
	LevelID             int32 `json:"level_id"`
}

// UserPerferOption 用户豪车偏好数据，当前仅企业级6.0预估接口使用
type UserPerferOption struct {
	DisplayTags      string `json:"display_tags"`      // 偏好设置 + 乘客姓名等（x先生/播放音乐/标准防护) 专、豪车相关字段
	DesignatedDriver string `json:"designated_driver"` // 指定司务员名称 豪车相关字段
}

type PrivateBizInfo struct {
	FormShowType int64 // 0:普通车型展示在主列表中 1:导流位 2: 推荐置顶

	ComboID           int64 `json:"combo_id"`
	CarpoolSeatNum    int32 `json:"carpool_seat_num"`     // 用户选择的拼车座位数
	MaxCarpoolSeatNum int32 `json:"max_carpool_seat_num"` // 当前品类可选的最大拼车座位数

	TimeSpan       []*EstimateDecision.TimeSpanV2 `json:"time_span"`
	DepartureRange []int64                        `json:"departure_range"`
	DepartureTime  int64
	MatchRoutes    []interface{} // 透传给price-api 空

	RouteType int64                         `json:"route_type"`
	RouteInfo *EstimateDecision.RouteInfoV2 `json:"route_info"` // 城际拼车路线信息

	// MetroInfo *EstimateDecision.MetroInfo `json:"metro_info"` // 地铁站信息

	Ranking  int32
	WaitTime int32

	CompensationInfo map[string]*Compensation.CompensationAbilityResult

	CarpoolRouteInfo   *CarpoolRouteInfo
	CarpoolCommuteCard *CarpoolCommuteCard
	StationList        []*EstimateDecision.StationInfoV2

	// SelectType
	UserMemberProfile

	// hundun返回的该品类支持的服务列表
	CustomFeatureList []*hundunClient.PcServiceData `json:"custom_feature_list"`

	UserPerferOption

	// 出租车峰期加价sps数据
	TaxiSps                   *hestia_charge.TaxiSpsData
	IsTaxiPeakFeeUserSelected bool
	IsSwitchIntel             bool // 是否切到智能出价链路
	IsHoliday                 bool // 是否是特殊节假日

	SpsFeeMap map[int64]*Sps.FeeItem
	// 拼车车ETS
	PinchecheEtsInfo *HotspotDIRPC.TimeWindow
	InviteInfo       *CarpoolApi.PinkerInvitedRes

	// 城际品类数据
	IntercityData

	CheckStatus int // 勾选状态

	// 司乘议价
	BargainData

	// 特价拼车-惊喜权益卡-vcard返回数据
	VcardData *VcardResult

	UfsFeatures map[string]string

	// 用户未授权三方车型
	UfsTripCloudAuthBusinessID map[int64]bool

	// 用户未授权的wyc
	UfsWycAuthBusinessID map[int64]bool

	// 补天三方是否需要授权
	IsButianNeedAuth bool

	// 是否需要默认授权
	IsNeedDefaultAuth bool

	// 待授权列表
	NeedAuthList []string

	AthenaEstimateEtpEtdInfo *AthenaApiv3.EstimateEtpEtdInfo // etp相关信息

	MiniBusDataPrivate
	RouteDataPrivate

	WaitMinuteSendEmptyCarTime int64 // 拼成乐等必走

	BargainRangeData             // 惠选车相关价格信息
	IsFission           bool     // 是裂变出的品类
	PetsInfo            *PetInfo // 宠物相关信息
	SendOrderId         int64
	SmartBusDataPrivate // 智能小巴相关信息

	GuideStationBusData // 大巴相关数据

	// 会议用车费用
	ServiceFeeInfo   string
	OverseaExtraInfo *OverseaExtraInfo //境外品类相关数据

	OrderReceiveCnt int32 // 车型呼叫人数

	ClassifyFeatureModel

	HildaPrivilegeInfo *PrivilegeInfo // Hilda权益卡信息
}

type ClassifyFeatureModel struct {
	IsRec bool
}

type PrivilegeInfo struct {
	SurpriseSpecialCardBatchId string
}

type MiniBusDataPrivate struct {
	// 小巴模式推荐班次、etp信息
	MiniBusPreMatch  *CarpoolApi.PrematchMiniBusRes
	MiniBusDistLimit int64
}

type SmartBusDataPrivate struct {
	// 智能小巴模式推荐班次、etp信息
	SmartBusPreMatch *CarpoolApi.PrematchMiniBusRes
	WalkDist         *int32
}

type RouteDataPrivate struct {
	OriginBills *PriceApi.OriginBills
}

type GuideStationBusData struct {
	// 是否最优班次, 距起点poi距离
	IsBestShift              int32  `json:"is_best_shift"`
	OptimalShiftDistance     int32  `json:"optimal_shift_dist"`
	IsIntercitySurpriseAlone bool   `json:"-"`
	DepartureRangeStr        string `json:"-"`
}

// BargainData 司乘议价数据
type BargainData struct {
	// 推荐价
	RecommendData []*DynamicPrice.RecommendData
	// 是否多勾
	BargainIsMultiSelect bool

	StartFenceList []int64 `json:"start_fence_id"` // 起点围栏列表
	StopFenceList  []int64 `json:"stop_fence_id"`  // 终点围栏列表

	SenseConfig *entity.SenseConfig `json:"sense_config"` // 命中场景配置
	FixPrice    *entity.FixPrice    // 改价后价格
}

type BargainRangeData struct {
	PriceLimitLower        int64   `json:"price_limit_lower,omitempty"`
	PriceLimitUpper        int64   `json:"price_limit_upper,omitempty"`
	RecommendPriceLower    int64   `json:"recommend_price_lower,omitempty"`
	RecommendPriceUpper    int64   `json:"recommend_price_upper,omitempty"`
	FastCarEstimateFee     float64 `json:"fast_car_estimate_fee,omitempty"`
	SpFastCarEstimateFee   float64 `json:"sp_fast_car_estimate_fee,omitempty"`
	BbargainRangeCheckable bool    `json:"b_bargain_range_checkable,omitempty"`
}
type PetInfo struct {
	PetNo                    string `json:"pet_no"`
	PetType                  int32  `json:"pet_type"`
	PetTypeDesc              string `json:"pet_type_desc"`
	NickName                 string `json:"nick_name"`
	Avatar                   string `json:"avatar"`
	WeightCategory           int32  `json:"weight_category"`
	WeightCategoryDesc       string `json:"weight_category_desc"`
	IsDefault                bool   `json:"is_default"`
	PetServiceTag            int32  `json:"pet_service_tag"`
	PersonalizedCustomOption string `json:"personalized_custom_option"`
}

type OverseaExtraInfo struct {
	ETS int64 `json:"ets"`
}

type IntercityData struct {
	// 客企是否需要实名
	NeedVerified bool

	// 城际 库存信息 起终时间字符串拼接-Status 0/1 是否可用 Seat 可用座位数
	IntercitySkuInfo map[string]struct {
		Status int32
		Seat   int
	}

	// 班车模式推荐站点、时间片、库存信息
	StartStationID            int64
	DestStationID             int64
	StationInventoryInfo      *StationInventoryInfo
	ShiftDetail               *CarpoolOpenApi.ShiftDetail
	RouteDetail               *Prfs.RouteDetailData // 路线信息
	RouteDetailV2             *RouteDetail          // 路线信息
	SeatDetailInfo            []*SeatDetailInfo
	CarryChildrenNum          int32 // 携童数量
	CarryChildrenIsOccupySeat int32 // 携童是否占座

	HasPickAndDropStation bool // 路线上有既上又下站点
	// 城际 根据路线   把城际实时单改成预约单
	SpecialOrderInfo *SpecialOrderInfo
	// 大巴运营「资质」，包含大巴、公交、包车、... 等多种车型和承运资质类型
	BusMode string
	// 站点的阶梯定价
	StationPrice *TripCloudPassenger.StationPriceDatas
	// 推荐卡列表
	BusCardListData        *Dirpc_SDK_Vcard.PGetBusCardListData
	BusCardSelectedBatchId int32 // 用户选择的卡批次id
	BusCardIsShow          bool  // 用户选择的乘客中是否包含成人
	//站点的阶梯定价，后续替换掉 StationPrice
	StationBusStationPrice *ticketPrice.StationPriceData
	IntercityRule          *ticketPrice.RuleData
}

type SpecialOrderInfo struct {
	// 城际 根据路线   把城际实时单改成预约单
	OrderType      int32
	DepartureRange []int64
	DepartureTime  int64
}

type StationInventoryInfo struct {
	RecommendStation  *CarpoolOpenApi.RecommendStation
	StationInventorys []*CarpoolOpenApi.StationInventory
	SelectInfo        StationInventorySelectInfo
	StationList       []*Prfs.StationInfo
	SeatLimit         *Prfs.SeatLimit
	ChildOccupySeat   *bool
	StationProperty   map[int32][]string
}

type StationInventorySelectInfo struct {
	DepartureTime             int64
	RouteId                   int64
	SeatNum                   int32
	RemainSeats               int32
	UserMaxSeatNum            int32
	FromStationId             int
	DestStationId             int
	SrcCost                   *CarpoolOpenApi.StationRouteCost
	DestCost                  *CarpoolOpenApi.StationRouteCost
	FromStationInfo           *Prfs.StationInfo
	DestStationInfo           *Prfs.StationInfo
	IsFirst                   bool
	FromStationIndex          int
	ExtraInfo                 map[string]string
	ShiftID                   string
	LastItem                  bool
	CarryChildrenMaxInventory int32
	Refresh                   int32
	ShiftType                 int32
}

type CarpoolCommuteCard struct {
	CardID        string  `json:"card_id"`
	RouteID       string  `json:"route_id"`
	CardType      int64   `json:"card_type"`
	CardValue     float64 `json:"card_value"`
	CardTitle     string  `json:"card_title"`
	OriginalValue float64 `json:"original_value"`
	CardBizID     int64   `json:"card_biz_id"`
}
type CarpoolRouteInfo struct {
	RouteID     string
	Icon        string
	MegTemplate string
}

type VcardResult struct {
	UsableCard *Vcard.UsableCard   // 可用卡
	SendCard   *Vcard.SendCardResp // 赠卡
}

func (p *PrivateBizInfo) build(ctx context.Context, commonInfo *CommonInfo, commonBizInfo *CommonBizInfo, product *EstimateDecision.Product) {

	if carpool.IsCarpool(product.CarpoolType) || product_id.IsSFC(product.ProductId) {
		p.CarpoolSeatNum = 1
		if commonBizInfo.CarpoolSeatNum != 0 {
			p.CarpoolSeatNum = commonBizInfo.CarpoolSeatNum
		} else if commonBizInfo.PassengerCount != nil {
			p.CarpoolSeatNum = *commonBizInfo.PassengerCount
		}
	}

	// 豪华车 设置勾选司务员
	if product_id.ProductIdFirstClassCar == product.ProductId {
		// 六座豪华
		if apollo.FeatureToggle(ctx, "six_seat_lux_judge", "", map[string]string{"product_category": strconv.FormatInt(product.ProductCategory, 10)}) {
			p.DesignatedDriver = commonInfo.SixSeatDesignatedDriver
		} else {
			p.DesignatedDriver = commonInfo.DesignatedDriver
		}
	}

	if product_id.ProductIdBusinessLuxCar == product.ProductId && "" != commonInfo.LuxurySelectDriver && "0" != commonInfo.LuxurySelectDriver {
		p.DesignatedDriver = commonInfo.LuxurySelectDriver
	}
	// 景区接驳构造路线信息
	if product.RouteType == route_type.RouteTypeCarpoolShuttleBus {
		routeInfo := new(EstimateDecision.RouteInfoV2)
		routeInfo.RouteId = &commonInfo.RouteID
		p.MatchRoutes = append(p.MatchRoutes, routeInfo)
	}
	if product.OrderInfo != nil && product.OrderInfo.OrderExtraInfo != nil {
		routeInfo := product.OrderInfo.OrderExtraInfo.RouteInfo
		if routeInfo != nil {
			p.MatchRoutes = append(p.MatchRoutes, routeInfo)
			if routeInfo.MaxSeatNum != nil {
				p.MaxCarpoolSeatNum = *routeInfo.MaxSeatNum
			}

			if routeInfo.RouteId != nil && carpool.IsInterCityCarpool(product.CarpoolType, product.ComboType) {
				p.ComboID = *routeInfo.RouteId
				p.RouteInfo = product.OrderInfo.OrderExtraInfo.RouteInfo
				p.RouteType = product.RouteType
			}

			p.TimeSpan = routeInfo.TimeSpan

			if len(commonInfo.DepartureRange) > 0 {
				p.DepartureRange = commonInfo.DepartureRange
			} else if len(p.TimeSpan) > 0 && p.TimeSpan[0] != nil && len(p.TimeSpan[0].Range) > 0 {
				rangeValue := p.TimeSpan[0].Range[0].Value
				_ = json.Unmarshal([]byte(rangeValue), &p.DepartureRange)
			}
		}
		if c := product.OrderInfo.OrderExtraInfo.CommuteCard; c != nil {
			p.CarpoolCommuteCard = &CarpoolCommuteCard{
				CardID:        c.CardId,
				RouteID:       c.RouteId,
				CardType:      c.CardType,
				CardValue:     c.CardValue,
				CardTitle:     c.CardTitle,
				OriginalValue: c.OriginalValue,
				CardBizID:     c.CardBizId,
			}
		}
	}

	if product.OrderInfo != nil && product.OrderInfo.StationList != nil {
		p.StationList = product.OrderInfo.StationList
	}
}

func (p *PrivateBizInfo) BuildPriceReq(ctx context.Context, request *price_api.PriceEstimateReq) {
	if request.OrderInfoSt == nil {
		request.OrderInfoSt = &PriceApi.OrderInfoSt{}
	}

	request.OrderInfoSt.CarpoolSeatNum = p.CarpoolSeatNum
	request.OrderInfoSt.ComboId = p.ComboID

	if request.OrderExt == nil {
		request.OrderExt = &price_api.OrderExt{}
	}
	request.OrderExt.ComboIDs = strconv.FormatInt(p.ComboID, 10)

	if len(p.StartFenceList) > 0 {
		request.ExtraInfo["start_fence_id"] = util.Int64SliceToString(p.StartFenceList)
	}
	if len(p.StopFenceList) > 0 {
		request.ExtraInfo["stop_fence_id"] = util.Int64SliceToString(p.StopFenceList)
	}

	if p.DesignatedDriver != "" {
		request.OrderExt.DesignatedDriver = p.DesignatedDriver
		driverId, err := strconv.Atoi(p.DesignatedDriver)
		// dfs获取司务员星级 用以计费
		if err == nil && driverId > 0 {
			tags, err := dfs.GetLuxDriverTags(ctx, int64(driverId))
			if err == nil && len(tags) > 0 {
				for _, tag := range tags {
					// 英语司务员和100以后的值预留给其他的
					if -1 != tag && tag > -100 {
						request.OrderExt.DesignatedDriverTag = strconv.Itoa(tag)
						break
					}
				}
			}
		}

		if err == nil && driverId < 0 {
			request.OrderExt.DesignatedDriverTag = p.DesignatedDriver
		}

	}

	if p.MatchRoutes != nil {
		if jsByte, err := json.Marshal(p.MatchRoutes); err == nil {
			_ = json.Unmarshal(jsByte, &request.OrderExt.MatchRoutes)
		}
	}

	// 补充会员权益信息到priceApi-request
	request.PassengerInfo.IsUserUseDpa = p.IsUserUseDpa
	request.PassengerInfo.UserDpaSelected = p.UseDpaSelected
	if p.MemberProfile != nil {
		request.PassengerInfo.MemberProfile = p.MemberProfile
	}
	if p.PricePrivilegeInfo != nil {
		request.OrderExt.PricePrivilegeInfo = p.PricePrivilegeInfo
	}

	request.OrderInfoSt.ComboId = p.ComboID
	request.OrderInfoSt.CarpoolSeatNum = p.CarpoolSeatNum

	routeInfo := p.RouteInfo
	if len(p.DepartureRange) > 1 {
		bytes, err := json.Marshal(p.DepartureRange)
		if err == nil {
			request.OrderExt.DepartureRange = string(bytes)
			request.OrderInfoSt.DepartureTime = util.ToInt64(p.DepartureRange[1])
		}
	} else if routeInfo != nil {
		// 获取dds第一个可用城际时间片
		departureRangeDds := ""
		if len(routeInfo.TimeSpan) > 0 && len(routeInfo.TimeSpan[0].Range) > 0 {
			departureRangeDds = routeInfo.TimeSpan[0].Range[0].Value
		} else if len(routeInfo.TimeSpan) > 1 && len(routeInfo.TimeSpan[1].Range) > 0 {
			//  当天23点后可能没有时间片，需要取明天的第一个时间片
			departureRangeDds = routeInfo.TimeSpan[1].Range[0].Value
		}
		departureTimeDds := int64(0)
		if departureRangeDds != "" {
			var intArray []int64
			err := json.Unmarshal([]byte(departureRangeDds), &intArray)
			if err == nil && len(intArray) == 2 {
				departureTimeDds = intArray[1]
			}
		}
		if request.OrderExt.DepartureRange == nil {
			// 端上没传需要从routeInfo取
			// 首次冒泡，用户没有选择时间片，采用最近的第一个时间片冒泡
			request.OrderExt.DepartureRange = departureRangeDds
			if departureTimeDds != 0 {
				request.OrderInfoSt.DepartureTime = departureTimeDds
			}
		} else {
			// 用户传递departureRange
			if v, ok := request.OrderExt.DepartureRange.(string); ok {
				var intArray []int64
				err := json.Unmarshal([]byte(v), &intArray)
				if err == nil && len(intArray) == 2 {
					departureTimeUser := intArray[1]
					// 处理二次勾选时间片过期场景
					if departureTimeUser < departureTimeDds {
						request.OrderExt.DepartureRange = departureRangeDds
						if departureTimeDds != 0 {
							request.OrderInfoSt.DepartureTime = departureTimeDds
						}
					}

				}

			}
		}
	}

	if request.OrderExt.OrderNTuple.CarpoolType != nil {
		if *(request.OrderExt.OrderNTuple.CarpoolType) == consts.CarPoolTypeInterCityStation {
			// 大车站点 走特殊的获取时间点
			request.OrderInfoSt.DepartureTime = p.DepartureTime
			if request.ExtraInfo == nil {
				request.ExtraInfo = make(map[string]string)
			}

			if p.StartStationID != 0 {
				request.ExtraInfo["from_station_id"] = strconv.Itoa(int(p.StartStationID))
			}

			if p.DestStationID != 0 {
				request.ExtraInfo["dest_station_id"] = strconv.Itoa(int(p.DestStationID))
			}

			if p.StationInventoryInfo != nil && p.StationInventoryInfo.SelectInfo.FromStationInfo != nil && p.StationInventoryInfo.SelectInfo.DestStationInfo != nil {
				selectInfo := p.StationInventoryInfo.SelectInfo
				// 这俩字段需要写报价单里所以需要传给price-api
				request.ExtraInfo["from_station_id"] = strconv.Itoa(selectInfo.FromStationId)
				request.ExtraInfo["dest_station_id"] = strconv.Itoa(selectInfo.DestStationId)
				request.ExtraInfo["shift_id"] = selectInfo.ShiftID
				// 修改预估起终点 不要学
				fromLat, _ := strconv.ParseFloat(selectInfo.FromStationInfo.StationLat, 64)
				fromLng, _ := strconv.ParseFloat(selectInfo.FromStationInfo.StationLng, 64)
				destLat, _ := strconv.ParseFloat(selectInfo.DestStationInfo.StationLat, 64)
				destLng, _ := strconv.ParseFloat(selectInfo.DestStationInfo.StationLng, 64)
				request.OrderInfoSt.FromLat = fromLat
				request.OrderInfoSt.FromLng = fromLng
				request.OrderInfoSt.FromName = selectInfo.FromStationInfo.StationName
				request.OrderInfoSt.ToLat = destLat
				request.OrderInfoSt.ToLng = destLng
				request.OrderInfoSt.ToName = selectInfo.DestStationInfo.StationName
			}
			if p.BusCardSelectedBatchId != 0 {
				request.ExtraInfo["bus_card_selected_batch_id"] = cast.ToString(p.BusCardSelectedBatchId)
			}
		}
	}

	if request.OrderExt.PageType == page_type.PageTypeLowCarpoolEstimate && p.IsFission {
		if len(p.DepartureRange) == 2 {
			bytes, err := json.Marshal(p.DepartureRange)
			if err == nil {
				request.OrderExt.DepartureRange = string(bytes)
				request.OrderInfoSt.DepartureTime = util.ToInt64(p.DepartureRange[1])
			}
		} else {
			request.OrderExt.DepartureRange = ""
			request.OrderInfoSt.DepartureTime = time.Now().Unix()
		}
	}

	if c := p.CarpoolCommuteCard; c != nil {
		if request.OrderExt == nil {
			request.OrderExt = new(price_api.OrderExt)
		}
		request.OrderExt.CommuteCardInfo = &price_api.CommuteCardInfo{
			CardID:        c.CardID,
			RouteID:       c.RouteID,
			CardType:      c.CardType,
			CardValue:     c.CardValue,
			CardTitle:     c.CardTitle,
			OriginalValue: c.OriginalValue,
			CardBizID:     c.CardBizID,
		}
	}

	if sdi := p.SeatDetailInfo; len(sdi) > 0 {
		seatInfoList := make([]*PriceApi.SeatDetailInfo, 0)
		for _, seatItem := range sdi {
			if seatItem == nil {
				continue
			}

			seatInfoList = append(seatInfoList, &PriceApi.SeatDetailInfo{
				Type:           seatItem.PassengerType,
				PassengerCount: seatItem.PassengerCount,
				IsOccupySeat:   seatItem.IsOccupySeat,
			})
		}

		if len(seatInfoList) > 0 {
			request.OrderInfoSt.SeatDetailInfo = seatInfoList
		}
	}
}

func (c *CommonBizInfo) InitPetInfoByOrderInfo(ctx context.Context, orderInfo *dos.OrderInfo, request OrderMatchRequest) {
	if orderInfo == nil {
		return
	}
	if orderInfo.ExtendFeature == "" {
		return
	}
	var extendFeature map[string]interface{}
	err := json.Unmarshal([]byte(orderInfo.ExtendFeature), &extendFeature)
	if err != nil {
		return
	}
	if petInfo, ok := extendFeature["order_pet_info"]; ok {
		pet, ok1 := petInfo.(map[string]interface{})
		if !ok1 {
			return
		}
		apolloParams := map[string]string{
			"weight_category": cast.ToString(pet["weight_category"]),
			"pet_service_tag": cast.ToString(pet["pet_service_tag"]),
		}

		isAllow, assignParams := apollo.GetParameters("convert_pet_service_info", orderInfo.PassengerId, apolloParams)
		feature := dos.CustomFeatureStruct{
			Count: 1,
		}
		if isAllow {
			id, err1 := strconv.Atoi(assignParams["id"])
			serviceType, err2 := strconv.Atoi(assignParams["service_type"])
			if err1 == nil && err2 == nil {
				feature.Id = int64(id)
				st32 := int32(serviceType)
				feature.ServiceType = &st32
			}

		}
		var featureList = make([]dos.CustomFeatureStruct, 0)
		featureList = append(featureList, feature)
		//mock前端传参
		//options := make(map[int64]*Plutus.PersonalizedCustomizedOptions)
		//item := &Plutus.PersonalizedCustomizedOptions{
		//	Count:       1,
		//	ServiceType: feature.ServiceType,
		//}
		//options[feature.Id] = item
		dosMap := make(map[int64][]dos.CustomFeatureStruct)
		dosMap[product_category.ProductCategoryPremiumComfort] = featureList
		//c.PersonalizedCustomOptions = options
		c.PcId2CustomFeature = dosMap
	}
}

// SmartBusData 公交小巴业务数据
type SmartBusData struct {
	//公交小巴站点信息
	StartStationInfo *SmartBusStation
	DestStationInfo  *SmartBusStation

	//MiniBusEstimateRequest.ExtMap （string）for SmartBus
	SmartBusExtData *SmartBusExtData
}

// SmartBusStation 公交小巴站点
type SmartBusStation struct {
	Displayname string  `json:"displayname"`
	Address     string  `json:"address"`
	Lng         float64 `json:"lng"`
	Lat         float64 `json:"lat"`
	PoiId       string  `json:"poi_id"`
	LinkId      string  `json:"link_id"`
	//MType       string `json:"m_type"`
}

func BuildSmartBusStation(ctx context.Context, stationJson string) (*SmartBusStation, error) {
	stationInfo := &SmartBusStation{}
	err := json.Unmarshal([]byte(stationJson), stationInfo)
	if err != nil {
		log.Trace.Warnf(ctx, consts.TagErrJsonUnMarshal, "json.Unmarshal error, err=%v", err)
		return nil, err
	}

	stationInfo.LinkId = strings.TrimSpace(stationInfo.LinkId)
	stationInfo.PoiId = strings.TrimSpace(stationInfo.PoiId)

	return stationInfo, nil
}

// SmartBusExtData MiniBusEstimateRequest.ExtMap （string）for SmartBus
type SmartBusExtData struct {
	//车牌号
	PlateNo string `json:"plate_no"`

	//产品线id
	ProductID string `json:"product_id"`
}

func BuildSmartBusExtData(ctx context.Context, extMap string) (*SmartBusExtData, error) {
	extData := &SmartBusExtData{}
	err := json.Unmarshal([]byte(extMap), extData)
	if err != nil {
		log.Trace.Warnf(ctx, consts.TagErrJsonUnMarshal, "json.Unmarshal error, err=%v", err)
		return nil, err
	}

	extData.PlateNo = strings.TrimSpace(extData.PlateNo)
	extData.ProductID = strings.TrimSpace(extData.ProductID)

	return extData, nil
}

type DdsResult struct {
	SelectTypeMap      map[int64]bool
	SingleProductsSort []int64
}
