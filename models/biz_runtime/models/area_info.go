package models

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/s3e/common-lib/v2/component/diff"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"

	CommonConsts "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/locsvr"

	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

type AreaInfo struct {
	City                   int32                          `json:"city"`     // Deprecated: 表意不明
	District               string                         `json:"district"` // 国内-城市维度，国外-国家维度
	FromCounty             int32                          `json:"from_county"`
	ToCounty               int32                          `json:"to_county"`
	FromCityName           string                         `json:"from_city_name"`
	ToCityName             string                         `json:"to_city_name"`
	FromLat                float64                        `json:"from_lat"`
	FromLng                float64                        `json:"from_lng"`
	FromAddress            string                         `json:"from_address"`
	FromName               string                         `json:"from_name"`
	FromPoiID              string                         `json:"from_poi_id"`
	FromPoiType            string                         `json:"from_poi_type"`
	ToLat                  float64                        `json:"to_lat"`
	ToLng                  float64                        `json:"to_lng"`
	ToAddress              string                         `json:"to_address"`
	ToName                 string                         `json:"to_name"`
	ToPoiID                string                         `json:"to_poi_id"`
	ToPoiType              string                         `json:"to_poi_type"`
	CurLng                 float64                        `json:"cur_lng"`
	CurLat                 float64                        `json:"cur_lat"`
	Lng                    float64                        `json:"lng"`
	Lat                    float64                        `json:"lat"`
	Area                   int32                          `json:"area"` // Deprecated: 和City字段重复
	ToArea                 int32                          `json:"to_area"`
	MapType                string                         `json:"map_type"`
	AbstractDistrict       string                         `json:"abstract_district"`
	StartingName           string                         `json:"starting_name"`
	DestName               string                         `json:"dest_name"`
	FromCountyName         string                         `json:"from_county_name"`
	ToCountyName           string                         `json:"to_county_name"`
	StopoverPointInfo      []*price_api.StopoverPointInfo `json:"stopover_point_info"`
	TripCountry            string                         `json:"trip_country"`
	MetroFenceID           int64                          `json:"metro_fence_id"`
	MapInfoCacheToken      string                         `json:"map_info_cache_token"`
	MapInfoStartCacheToken string                         `json:"mapinfo_start_cache_token"`
	MapInfoDestCacheToken  string                         `json:"mapinfo_dest_cache_token"`
	ChooseFSearchid        string                         `json:"choose_f_searchid"`          //用户选择起点请求ID
	ChooseTSearchid        string                         `json:"choose_t_searchid"`          //用户选择终点请求ID
	FromCityInfo           string                         `json:"from_city_info"`             //起点城市详细信息
	DistrictV2             *string                        `json:"district_v2,omitempty"`      // 城市维度的district，仅境外场景使用
	CountryIsoCode         *string                        `json:"country_iso_code,omitempty"` //起点国家iso码（唯一标识）
}

func (a *AreaInfo) InitByReq(ctx context.Context, req *proto.MultiEstimatePriceRequest) error {
	a.CurLat = req.Lat
	a.CurLng = req.Lng
	a.FromLng = req.FromLng
	a.FromLat = req.FromLat
	a.FromName = req.FromName
	a.FromAddress = req.FromAddress
	a.FromPoiID = req.FromPoiId
	a.FromPoiType = req.FromPoiType

	a.ToLat = req.ToLat
	a.ToLng = req.ToLng
	a.ToAddress = req.ToAddress
	a.ToName = req.ToName
	a.ToPoiID = req.ToPoiId
	a.ToPoiType = req.ToPoiType

	a.MapType = req.Maptype
	a.StartingName = util.MergeAddress(a.FromName, a.FromAddress)
	a.DestName = util.MergeAddress(a.ToName, a.ToAddress)

	if err := a.getAreaInfo(ctx); err != nil {
		return err
	}
	return nil
}

func (a *AreaInfo) InitByOrderInfo(ctx context.Context, orderInfo *dos.OrderInfo, feature *ufs.OrderFeature) {
	a.CurLat = util.String2float64(ctx, orderInfo.CurrentLat)
	a.CurLng = util.String2float64(ctx, orderInfo.CurrentLng)
	a.FromLng = util.String2float64(ctx, feature.FromLng)
	a.FromLat = util.String2float64(ctx, feature.FromLat)

	a.FromAddress = feature.FromAddress
	a.FromName = feature.FromName
	a.FromPoiID = orderInfo.StartingPoiId
	a.FromPoiType = feature.FromPoiType

	a.ToLat = util.String2float64(ctx, feature.ToLat)
	a.ToLng = util.String2float64(ctx, feature.ToLng)

	a.ToAddress = feature.ToAddress
	a.ToName = feature.ToName
	a.ToPoiID = orderInfo.DestPoiId
	a.ToPoiType = feature.ToPoiType
	a.StartingName = orderInfo.StartingName
	a.DestName = orderInfo.DestName
	a.City = util.String2int32(ctx, orderInfo.Area)
	a.Area = util.String2int32(ctx, orderInfo.Area)
	a.ToArea = util.String2int32(ctx, orderInfo.ToArea)
	a.FromCounty = util.String2int32(ctx, orderInfo.County)
	a.ToCounty = util.String2int32(ctx, orderInfo.ToCounty)
	a.FromCountyName = orderInfo.ExtendFeatureParsed.AddrInfo.StartCountyName
	a.ToCountyName = orderInfo.ExtendFeatureParsed.AddrInfo.DestCountyName
	a.District = orderInfo.District
	a.AbstractDistrict = a.District + "," + strconv.Itoa(int(a.FromCounty))
	if orderInfo.WayPointsAInfo != "" {
		wayPoint := &price_api.StopoverPointInfo{}
		_ = json.Unmarshal([]byte(orderInfo.WayPointsAInfo), wayPoint)
		if orderInfo.WayPointsAStatus != "" {
			wayPoint.Status, _ = strconv.Atoi(orderInfo.WayPointsAStatus)
		}
		a.StopoverPointInfo = append(a.StopoverPointInfo, wayPoint)

	}
	if orderInfo.WayPointsBInfo != "" {
		wayPoint := &price_api.StopoverPointInfo{}
		_ = json.Unmarshal([]byte(orderInfo.WayPointsBInfo), wayPoint)
		if orderInfo.WayPointsBStatus != "" {
			wayPoint.Status, _ = strconv.Atoi(orderInfo.WayPointsBStatus)
		}
		a.StopoverPointInfo = append(a.StopoverPointInfo, wayPoint)
	}

	a.TripCountry = "CN"
	// 起终点都在香港时，改写trip_country
	if a.Area == CommonConsts.HKCityId && a.ToArea == CommonConsts.HKCityId {
		a.TripCountry = "HK"
	}
}

// TryFillCityInfo ...
func (a *AreaInfo) TryFillCityInfo(ctx context.Context) error {
	return a.getAreaInfo(ctx)
}

// TryFillCityInfo ...
func (a *AreaInfo) TryFillCityInfoWithStopover(ctx context.Context) error {
	return a.getAreaInfoWithStopover(ctx)
}

func (a *AreaInfo) getAreaInfo(ctx context.Context) error {
	var coords = []map[string]float64{}

	coords = append(coords, map[string]float64{"lat": a.FromLat, "lng": a.FromLng})
	coords = append(coords, map[string]float64{"lat": a.ToLat, "lng": a.ToLng})

	if diff.CheckDiffStatus(ctx) {
		diff.CheckDiffAndLog(ctx, diff.DownStreamDirpcType, "MultiAreaInfoByCoord", coords)
	}
	cityInfos, err := locsvr.MultiAreaInfoByCoords(ctx, coords, a.MapType)
	if err != nil || len(cityInfos) == 0 {
		return err
	}

	if cityInfos[0] != nil {
		a.City = cityInfos[0].Cityid
		if cityInfos[0].DistrictCode != nil {
			a.District = *cityInfos[0].DistrictCode
		}
		a.Area = cityInfos[0].Cityid
		a.FromCounty = cityInfos[0].Countyid
		if cityInfos[0].CanonicalCountryCode != nil {
			a.TripCountry = *cityInfos[0].CanonicalCountryCode
		}
		a.FromCityName = cityInfos[0].CityDesc
		a.FromCountyName = cityInfos[0].CountyDesc
	}

	if cityInfos[1] != nil {
		a.ToArea = cityInfos[1].Cityid
		a.ToCounty = cityInfos[1].Countyid
		a.ToCityName = cityInfos[1].CityDesc
		a.ToCountyName = cityInfos[1].CountyDesc
	}
	a.AbstractDistrict = a.District + "," + strconv.Itoa(int(a.FromCounty))

	return nil
}

// getAreaInfoWithStopover 填充city信息，如果途径点中没有cityid也填充上
func (a *AreaInfo) getAreaInfoWithStopover(ctx context.Context) error {
	var coords = []map[string]float64{}
	coords = append(coords, map[string]float64{"lat": a.FromLat, "lng": a.FromLng})
	coords = append(coords, map[string]float64{"lat": a.ToLat, "lng": a.ToLng})

	// 途径点中的CityID没有值则需要根据经纬度填充
	if a.StopoverPointInfo != nil && len(a.StopoverPointInfo) > 0 {
		for _, stopoverPoint := range a.StopoverPointInfo {
			cityId, _ := stopoverPoint.CityID.Int64()
			if cityId == 0 {
				coords = append(coords, map[string]float64{"lat": stopoverPoint.Lat, "lng": stopoverPoint.Lng})
			}
		}
	}

	cityInfos, err := locsvr.MultiAreaInfoByCoords(ctx, coords, a.MapType)
	if err != nil || len(cityInfos) == 0 {
		return err
	}
	// 起点和终点的index分别为0和1，途径点的index从2开始
	stopoverPointCityInfoIndex := 2
	if a.StopoverPointInfo != nil && len(a.StopoverPointInfo) > 0 {
		for _, stopoverPoint := range a.StopoverPointInfo {
			cityId, _ := stopoverPoint.CityID.Int64()
			if cityId == 0 && cityInfos[int64(stopoverPointCityInfoIndex)] != nil {
				stopoverPoint.CityID = json.Number(strconv.FormatInt(int64(cityInfos[int64(stopoverPointCityInfoIndex)].Cityid), 10))
				stopoverPointCityInfoIndex++
			}
		}
	}

	if cityInfos[0] != nil {
		a.City = cityInfos[0].Cityid
		if cityInfos[0].DistrictCode != nil {
			a.District = *cityInfos[0].DistrictCode
		}
		a.Area = cityInfos[0].Cityid
		a.FromCounty = cityInfos[0].Countyid
		if cityInfos[0].CanonicalCountryCode != nil {
			a.TripCountry = *cityInfos[0].CanonicalCountryCode
		}
		a.FromCityName = cityInfos[0].CityDesc
		a.FromCountyName = cityInfos[0].CountyDesc
	}

	if cityInfos[1] != nil {
		a.ToArea = cityInfos[1].Cityid
		a.ToCounty = cityInfos[1].Countyid
		a.ToCityName = cityInfos[1].CityDesc
		a.ToCountyName = cityInfos[1].CountyDesc
	}
	a.AbstractDistrict = a.District + "," + strconv.Itoa(int(a.FromCounty))

	return nil
}

func (a *AreaInfo) genDDSProductsReq(request *EstimateDecision.ProductsReq) {
	if request.CommonInfo == nil {
		request.CommonInfo = &EstimateDecision.CommonInfoV2{}
	}
	request.CommonInfo.StartLng = a.FromLng
	request.CommonInfo.StartLat = a.FromLat
	request.CommonInfo.DestLat = a.ToLat
	request.CommonInfo.DestLng = a.ToLng
	request.CommonInfo.CurrentLat = a.CurLat
	request.CommonInfo.CurrentLng = a.CurLng
	request.CommonInfo.StartName = a.FromName
	request.CommonInfo.StartAddress = a.FromAddress
	request.CommonInfo.DestName = a.ToName
	request.CommonInfo.DestAddress = a.ToAddress
	request.CommonInfo.City = int64(a.City)
	toArea := int64(a.ToArea)
	request.CommonInfo.ToCity = &toArea
	request.CommonInfo.ToCounty = int64(a.ToCounty)
	request.CommonInfo.County = int64(a.FromCounty)
	request.CommonInfo.StartPoiId = a.FromPoiID
	request.CommonInfo.StartPoiType = a.FromPoiType
	request.CommonInfo.DestPoiId = a.ToPoiID
	request.CommonInfo.DestPoiType = a.ToPoiType
	request.CommonInfo.MetroFenceId = &a.MetroFenceID
	request.CommonInfo.MapInfoToken = a.MapInfoCacheToken
	request.CommonInfo.MapinfoStartCacheToken = a.MapInfoStartCacheToken
	request.CommonInfo.MapinfoDestCacheToken = a.MapInfoDestCacheToken
	if a.StopoverPointInfo != nil && len(a.StopoverPointInfo) > 0 {
		bytesStopoverPoints, err := json.Marshal(a.StopoverPointInfo)
		if err != nil {

		}
		strStopoverPoints := string(bytesStopoverPoints)
		request.CommonInfo.StopoverPoints = &strStopoverPoints
	}
	request.CommonInfo.ChooseFSearchid = a.ChooseFSearchid
	request.CommonInfo.ChooseTSearchid = a.ChooseTSearchid
	request.CommonInfo.District = a.District
}

func (a *AreaInfo) BuildPriceReq(request *price_api.PriceEstimateReq) {
	if request.OrderInfoSt == nil {
		request.OrderInfoSt = &PriceApi.OrderInfoSt{}
	}
	request.OrderInfoSt.District = util.StringPtr(a.District)
	request.OrderInfoSt.CurrentLat = a.CurLat
	request.OrderInfoSt.CurrentLng = a.CurLng
	request.OrderInfoSt.AbstractDistrict = &a.AbstractDistrict

	// FROM
	request.OrderInfoSt.Area = int64(a.City)
	request.OrderInfoSt.FromLat = a.FromLat
	request.OrderInfoSt.FromLng = a.FromLng
	request.OrderInfoSt.FromName = a.FromName
	request.OrderInfoSt.FromAddress = a.FromAddress
	request.OrderInfoSt.FromPoiId = a.FromPoiID
	request.OrderInfoSt.FromPoiType = a.FromPoiType
	request.OrderExt.County = int64(a.FromCounty)
	request.OrderInfoSt.ChooseFSearchid = &a.ChooseFSearchid

	// TO
	request.OrderExt.ToArea = int64(a.ToArea)
	request.OrderInfoSt.ToLat = a.ToLat
	request.OrderInfoSt.ToLng = a.ToLng
	request.OrderInfoSt.ToAddress = a.ToAddress
	request.OrderInfoSt.ToName = a.ToName
	request.OrderInfoSt.ToPoiId = a.ToPoiID
	request.OrderInfoSt.ToPoiType = a.ToPoiType
	request.OrderExt.ToCounty = int64(a.ToCounty)
	request.OrderInfoSt.ChooseTSearchid = &a.ChooseTSearchid

	if request.OrderExt == nil {
		request.OrderExt = &price_api.OrderExt{}
	}
	if a.StopoverPointInfo != nil && len(a.StopoverPointInfo) > 0 {
		request.OrderExt.StopoverPoints = a.StopoverPointInfo
	}
	if a.DistrictV2 != nil {
		request.OrderExt.DistrictV2 = a.DistrictV2
	}

	request.OrderExt.TripCountry = a.TripCountry
}

func (a *AreaInfo) InitByB2bEstimateReq(ctx context.Context, req *proto.B2BEstimateReq) error {

	a.FromLng = req.FromLng
	a.FromLat = req.FromLat
	a.FromName = req.FromName
	a.FromAddress = req.FromAddress
	a.FromPoiID = req.FromPoiId
	a.FromPoiType = req.FromPoiType

	a.ToLat = req.ToLat
	a.ToLng = req.ToLng
	a.ToAddress = req.ToAddress
	a.ToName = req.ToName
	a.ToPoiID = req.ToPoiId
	a.ToPoiType = req.ToPoiType

	a.MapType = req.Maptype
	a.StartingName = util.MergeAddress(a.FromName, a.FromAddress)
	a.DestName = util.MergeAddress(a.ToName, a.ToAddress)

	if len(req.StopoverPoints) > 0 {
		for _, stopoverItem := range req.StopoverPoints {
			if stopoverItem == nil {
				continue
			}

			a.StopoverPointInfo = append(a.StopoverPointInfo, &price_api.StopoverPointInfo{
				StopID:  stopoverItem.StopId,
				Lat:     stopoverItem.Lat,
				Lng:     stopoverItem.Lng,
				PoiID:   stopoverItem.PoiId,
				Name:    stopoverItem.Name,
				Address: stopoverItem.Address,
			})
		}
	}

	if err := a.getAreaInfo(ctx); err != nil {
		return err
	}
	return nil
}

func (a *AreaInfo) InitByB2bTailorReq(ctx context.Context, req *proto.LuxMultiEstimatePriceRequest) error {
	a.FromLng = req.FromLng
	a.FromLat = req.FromLat
	a.FromName = req.FromName
	a.FromAddress = req.FromAddress
	a.FromPoiID = req.FromPoiId
	a.FromPoiType = req.FromPoiType

	a.ToLat = req.ToLat
	a.ToLng = req.ToLng
	a.ToAddress = req.ToAddress
	a.ToName = req.ToName
	a.ToPoiID = req.ToPoiId
	a.ToPoiType = req.ToPoiType

	a.MapType = req.Maptype
	a.StartingName = util.MergeAddress(a.FromName, a.FromAddress)
	a.DestName = util.MergeAddress(a.ToName, a.ToAddress)

	if err := a.getAreaInfo(ctx); err != nil {
		return err
	}
	return nil
}
