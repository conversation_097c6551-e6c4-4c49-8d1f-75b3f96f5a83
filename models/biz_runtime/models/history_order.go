package models

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	util2 "git.xiaojukeji.com/gulfstream/passenger-common/util"
	trace "git.xiaojukeji.com/lego/context-go"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
)

// SendOrder 已发单信息
type SendOrder struct {
	OrderId                int64
	OrderIdLow             int64                                // 低位订单号
	MultiRequiredProduct   map[string]dos.RequiredProductStruct // 已发单车型
	UnderWaterBargainOrder bool                                 // 司乘议价水下单
	NewTime                string                               // 发单时间
	IsQueue                bool                                 // 是否排队
}

func (p *SendOrder) BuildByDosOrder(ctx context.Context, orderInfo *dos.OrderInfo, orderId string) {
	if "" == orderId {
		return
	}
	orderID, district, err := util.DecodeOrderID(orderId)
	if err != nil {
		log.Trace.Errorf(ctx, trace.DLTagUndefined, "DecodeOrderID: %v orderId:%v,", err, orderId)
	} else if base64, err2 := util.EncodeOrderIdWithoutBase64(orderID, district); err2 == nil {
		p.OrderId = base64
		p.OrderIdLow = orderID
	}

	if orderInfo != nil {
		p.MultiRequiredProduct = orderInfo.ExtendFeatureParsed.MultiRequiredProduct
		p.UnderWaterBargainOrder = orderInfo.ExtendFeatureParsed.UnderWaterBargainOrder
		p.NewTime = orderInfo.NewTime
		p.IsQueue = util2.NewUtilHelper().CheckOrderExtraType(cast.ToUint64(orderInfo.ExtraType), []string{"o_lineup"})
	}
}

func (p *SendOrder) BuildPriceReq(request *price_api.PriceEstimateReq, baseProduct *Product) {
	if request.OrderExt == nil {
		request.OrderExt = &price_api.OrderExt{}
	}

	request.OrderExt.OrderID = p.OrderIdLow

	// 对属于已经发单对车型进行标识
	for _, product := range p.MultiRequiredProduct {
		if util.ToInt64(product.EstimatePcID) == baseProduct.ProductCategory {
			request.ExtraInfo["is_send_order"] = "1"
		}
	}
}
