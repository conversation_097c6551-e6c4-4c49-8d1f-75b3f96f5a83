package models

import (
	"fmt"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
)

type PassengerMode int

const (
	Unknown           PassengerMode = -1
	RealNameNoHistory PassengerMode = 0
	RealName          PassengerMode = 1
	NoRealName        PassengerMode = 2

	NoCheck = 0
	Check   = 1

	NoDisable = 0
	Disable   = 1
)

type PassengerDetailInfo struct {
	Mode          int32            `json:"mode"`
	PassengerList []*PassengerItem `json:"passenger_list"`
}

type InfoData struct {
	InfoList []*InfoItem `json:"info_list"`
}

type InfoItem struct {
	ID              int    `json:"id"`
	Name            string `json:"name"`
	IdentityType    string `json:"identity_type"`
	IdentityNo      string `json:"identity_no"`
	Type            int    `json:"type"`
	CoverIdentityNo string `json:"cover_identity_no"`
}

type PassengerItem struct {
	TicketType     int32  `json:"ticket_type"`
	PassengerCount int32  `json:"passenger_count"`
	IdentityType   int32  `json:"identity_type"`
	IdentityName   string `json:"identity_name"`
	IdentityID     string `json:"encryption_id"`
	DeIdentityID   string
}

type PassengerH5Item struct {
	ID           int32  `json:"id"`
	TicketType   int32  `json:"type"`
	Name         string `json:"name"`
	IdentityType int32  `json:"identity_type"`
	IdentityNo   string `json:"identity_no"`
}

// ToInt32 ...
func (pm PassengerMode) ToInt32() int32 {
	return int32(pm)
}

// GenPassengerKey ...
func GenPassengerKey(identityID string, identityType int32, ticketType int32) string {
	return fmt.Sprintf("%s_%d_%d", identityID, identityType, ticketType)
}

// GenPassengerKeyIngoreTicketType ...
func GenPassengerKeyIngoreTicketType(identityID string, identityType int32) string {
	return fmt.Sprintf("%s_%d", identityID, identityType)
}

// BuildSeatDetailInfoList ...
func BuildSeatDetailInfoList(passengerDetailInfo *PassengerDetailInfo, carryChildrenIsOccupy int32) []*SeatDetailInfo {
	if passengerDetailInfo == nil {
		return nil
	}

	var (
		seatDetailInfoList = make([]*SeatDetailInfo, 0)
		seatDetailInfoMap  = make(map[int32]*SeatDetailInfo)
	)

	for _, passenger := range passengerDetailInfo.PassengerList {
		if passenger == nil {
			continue
		}

		if passengerDetailInfo.Mode == NoRealName.ToInt32() && passenger.PassengerCount == 0 {
			continue
		}

		if passengerDetailInfo.Mode == RealName.ToInt32() && passenger.TicketType == seat_selection_consts.CarryChildren.ToInt32() && passenger.PassengerCount == 0 {
			continue
		}

		if seatDetailInfo, exist := seatDetailInfoMap[passenger.TicketType]; exist && seatDetailInfo != nil {
			updateSeatDetailInfo(seatDetailInfo)
		} else {
			seatDetailInfoNew := newSeatDetailInfo(passengerDetailInfo.Mode, passenger, carryChildrenIsOccupy)
			seatDetailInfoMap[passenger.TicketType] = seatDetailInfoNew
			seatDetailInfoList = append(seatDetailInfoList, seatDetailInfoNew)
		}
	}

	return seatDetailInfoList
}

// updateSeatDetailInfo ...
func updateSeatDetailInfo(info *SeatDetailInfo) {
	if info == nil {
		return
	}

	info.PassengerCount += 1
}

// newSeatDetailInfo ...
func newSeatDetailInfo(mode int32, passengerInfo *PassengerItem, carryChildrenIsOccupy int32) *SeatDetailInfo {
	if passengerInfo == nil {
		return nil
	}

	seatDetailInfo := &SeatDetailInfo{
		PassengerType: passengerInfo.TicketType,
	}

	if seatDetailInfo.PassengerType == seat_selection_consts.CarryChildren.ToInt32() {
		seatDetailInfo.IsOccupySeat = carryChildrenIsOccupy
	} else {
		seatDetailInfo.IsOccupySeat = seat_selection_consts.IsOccupy.ToInt32()
	}

	if mode == NoRealName.ToInt32() {
		seatDetailInfo.PassengerCount = passengerInfo.PassengerCount
	} else {
		if seatDetailInfo.PassengerType == seat_selection_consts.CarryChildren.ToInt32() {
			seatDetailInfo.PassengerCount = passengerInfo.PassengerCount
		} else {
			seatDetailInfo.PassengerCount = 1
		}
	}

	return seatDetailInfo
}

// BuildCarpoolSeatNumBySeatDetailInfo ...
func BuildCarpoolSeatNumBySeatDetailInfo(seatDetailInfo []*SeatDetailInfo) int32 {
	if len(seatDetailInfo) <= 0 {
		return 0
	}

	var carpoolNum int32

	for _, item := range seatDetailInfo {
		if item == nil {
			continue
		}

		if item.IsOccupySeat == seat_selection_consts.NoOccupy.ToInt32() {
			continue
		}

		carpoolNum += item.PassengerCount
	}

	return carpoolNum
}

// BuildInsteadOrderSeatDetailInfoList 无须处理实名、非实名的信息
func BuildInsteadOrderSeatDetailInfoList(passengerDetailInfo *PassengerDetailInfo, carryChildrenIsOccupy int32) []*SeatDetailInfo {

	if passengerDetailInfo == nil {
		return nil
	}

	var (
		seatDetailInfoList = make([]*SeatDetailInfo, 0)
	)

	for _, passenger := range passengerDetailInfo.PassengerList {
		if passenger == nil || passenger.PassengerCount == 0 {
			continue
		}
		seatDetailInfo := &SeatDetailInfo{
			PassengerType:  passenger.TicketType,
			PassengerCount: passenger.PassengerCount,
		}
		if seatDetailInfo.PassengerType == seat_selection_consts.CarryChildren.ToInt32() {
			seatDetailInfo.IsOccupySeat = carryChildrenIsOccupy
		} else {
			seatDetailInfo.IsOccupySeat = seat_selection_consts.IsOccupy.ToInt32()
		}
		seatDetailInfoList = append(seatDetailInfoList, seatDetailInfo)
	}

	return seatDetailInfoList
}
