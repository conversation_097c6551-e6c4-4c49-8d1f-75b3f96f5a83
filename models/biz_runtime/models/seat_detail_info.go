package models

type SeatDetailInfo struct {
	PassengerType  int32 `json:"type"`
	PassengerCount int32 `json:"passenger_count"`
	IsOccupySeat   int32 `json:"is_occupy_seat"`
}

type SeatDetailInfoCache struct {
	MaxPassengerCount    *int32          `json:"max_passenger_count,omitempty"`     // 乘客最大可选座位数
	RemainSeats          *int32          `json:"remain_seats,omitempty"`            // 库存剩余数量
	RemainCarryChildSeat *int32          `json:"remain_carry_child_seat,omitempty"` // 携童剩余数量
	ChildOccupySeat      bool            `json:"child_occupy_seat"`                 // 携童是否占座
	DiscountInfo         map[int64]int64 `json:"discount_info"`                     // 儿童票折扣信息
	AllowSeatType        []int64         `json:"allow_seat_type,omitempty"`         // 允许的选座类型list
}

type SeatMetaInfo struct {
	ChildOccupySeat   bool  `json:"child_occupy_seat"`   // 携童是否占座
	MaxPassengerCount int32 `json:"max_passenger_count"` // 乘客最大可选座位数
}
