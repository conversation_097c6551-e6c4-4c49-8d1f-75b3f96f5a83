package models

import (
	"context"
	"testing"

	Locsvr "git.xiaojukeji.com/dirpc/dirpc-go-http-Locsvr"
	"git.xiaojukeji.com/gulfstream/passenger-common/sdk/locsvrhttp"
	"git.xiaojukeji.com/nuwa/golibs/zerolog/ddlog"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

// 生成Mock数据的辅助函数
func generateMockCityInfo(cityid int32, districtCode, districtCodeV2, canonicalCountryCode, cityDesc string, countyid int32, countyDesc string) *Locsvr.CityInfo {
	return &Locsvr.CityInfo{
		Cityid:               cityid,
		DistrictCode:         &districtCode,
		DistrictCodeV2:       &districtCodeV2,
		CanonicalCountryCode: canonicalCountryCode,
		CityDesc:             cityDesc,
		Countryid:            countyid,
		CountryDesc:          countyDesc,
	}
}

func TestBaseReqDataBuilder_SetOverseaAreaInfo(t *testing.T) {
	ctx := context.Background()

	t.Run("成功场景-正常设置海外区域信息", func(t *testing.T) {
		// 准备测试数据
		fromCity := int32(1001)
		toCity := int32(1002)
		districtCode := "110000"
		districtCodeV2 := "110000_v2"
		canonicalCountryCode := "CN"
		cityDesc := "北京市"
		countyid := int32(110101)
		countyDesc := "东城区"

		// 创建Mock响应
		mockResp := &Locsvr.GetCityResp{
			Errno:  0,
			Errmsg: "success",
			Cityinfo: []*Locsvr.CityInfo{
				generateMockCityInfo(fromCity, districtCode, districtCodeV2, canonicalCountryCode, cityDesc, countyid, countyDesc),
			},
		}

		// 设置Mock
		mockey.Mock(locsvrhttp.GetClient).Return(&locsvrhttp.Client{}).Build()
		mockey.Mock((*locsvrhttp.Client).GetCityByIDs).Return(mockResp, nil).Build()
		mockey.Mock((*ddlog.DiLogHandle).Infof).Return().Build()
		mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build()

		defer mockey.UnPatchAll()

		// 执行测试
		builder := NewBaseReqDataBuilder()
		result, err := builder.SetOverseaAreaInfo(ctx, fromCity, toCity)

		// 验证结果
		assert.NoError(t, err, "SetOverseaAreaInfo应该成功执行")
		assert.NotNil(t, result, "返回的builder不应该为nil")
		assert.Equal(t, fromCity, builder.inner.AreaInfo.City, "City应该正确设置")
		assert.Equal(t, fromCity, builder.inner.AreaInfo.Area, "Area应该正确设置")
		assert.Equal(t, toCity, builder.inner.AreaInfo.ToArea, "ToArea应该正确设置")
		assert.Equal(t, districtCode, builder.inner.AreaInfo.District, "District应该正确设置")
		assert.Equal(t, districtCodeV2, *builder.inner.AreaInfo.DistrictV2, "DistrictV2应该正确设置")
		assert.Equal(t, canonicalCountryCode, *builder.inner.AreaInfo.CountryIsoCode, "CountryIsoCode应该正确设置")
		assert.NotEmpty(t, builder.inner.AreaInfo.FromCityInfo, "FromCityInfo应该不为空")
	})

	t.Run("失败场景-RPC调用失败", func(t *testing.T) {
		// 准备测试数据
		fromCity := int32(1001)
		toCity := int32(1002)

		// 设置Mock - 模拟RPC失败
		mockey.Mock(locsvrhttp.GetClient).Return(&locsvrhttp.Client{}).Build()
		mockey.Mock((*locsvrhttp.Client).GetCityByIDs).Return(nil, assert.AnError).Build()
		mockey.Mock((*ddlog.DiLogHandle).Infof).Return().Build()
		mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build()

		defer mockey.UnPatchAll()

		// 执行测试
		builder := NewBaseReqDataBuilder()
		result, err := builder.SetOverseaAreaInfo(ctx, fromCity, toCity)

		// 验证结果
		assert.Error(t, err, "SetOverseaAreaInfo应该返回错误")
		assert.Nil(t, result, "返回的builder应该为nil")
		assert.Equal(t, assert.AnError, err, "错误应该匹配预期")
	})

	t.Run("成功场景-验证CountryIsoCode字段设置", func(t *testing.T) {
		// 准备测试数据 - 特别关注CountryIsoCode字段
		fromCity := int32(1001)
		toCity := int32(1002)
		districtCode := "110000"
		districtCodeV2 := "110000_v2"
		canonicalCountryCode := "US" // 使用不同的国家代码
		cityDesc := "New York"
		countyid := int32(110101)
		countyDesc := "Manhattan"

		// 创建Mock响应
		mockResp := &Locsvr.GetCityResp{
			Errno:  0,
			Errmsg: "success",
			Cityinfo: []*Locsvr.CityInfo{
				generateMockCityInfo(fromCity, districtCode, districtCodeV2, canonicalCountryCode, cityDesc, countyid, countyDesc),
			},
		}

		// 设置Mock
		mockey.Mock(locsvrhttp.GetClient).Return(&locsvrhttp.Client{}).Build()
		mockey.Mock((*locsvrhttp.Client).GetCityByIDs).Return(mockResp, nil).Build()
		mockey.Mock((*ddlog.DiLogHandle).Infof).Return().Build()
		mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build()

		defer mockey.UnPatchAll()

		// 执行测试
		builder := NewBaseReqDataBuilder()
		result, err := builder.SetOverseaAreaInfo(ctx, fromCity, toCity)

		// 验证结果
		assert.NoError(t, err, "SetOverseaAreaInfo应该成功执行")
		assert.NotNil(t, result, "返回的builder不应该为nil")
		assert.NotNil(t, builder.inner.AreaInfo.CountryIsoCode, "CountryIsoCode不应该为nil")
		assert.Equal(t, canonicalCountryCode, *builder.inner.AreaInfo.CountryIsoCode, "CountryIsoCode应该正确设置为US")
	})
}
