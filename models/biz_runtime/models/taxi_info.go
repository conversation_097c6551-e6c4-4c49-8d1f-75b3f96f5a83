package models

import (
	consts2 "git.xiaojukeji.com/gulfstream/mamba/render/private/simple_estimate/consts"
	"github.com/tidwall/gjson"
)

// PeakFee 出租车峰期加价
type PeakFee struct {
	Status              bool // 开城状态
	IsFirstEstimate     bool // 是否首次预估
	IsUserSelected      bool // 用户是否勾选
	IsTaxiPeakFeeFlag   bool // 是否为峰期加价单标识
	IsShowEstimatePrice bool // 是否展示预估价
	HasDiscount         bool // 是否包含峰期加价灵活补贴
	IsHoliday           bool // 是否特殊节假日

	// 价格相关
	SpsData *SpsData

	PeakFeeConfig *PeakFeeConfig
	TextConfig    map[string]gjson.Result
	RejectReason  string
}

// SpsData 定价数据
type SpsData struct {
	PassengerPrice    int  `json:"passenger_price"`
	DriverPrice       int  `json:"driver_price"`
	CanSelect         int  `json:"can_select"`
	PassengerDiscount int  `json:"passenger_discount"`
	IsHoliday         bool `json:"is_holiday"`
}

// PeakFeeConfig 出租车峰期加价配置
type PeakFeeConfig struct {
	CityID          string    `json:"city_id"`
	ProductCategory int       `json:"product_category"`
	OpenStatus      string    `json:"open_status"`
	OpenRules       OpenRules `json:"open_rules"`
	DCMPKey         DCMPKey   `json:"dcmp_key"`
}

// OpenRules 开城规则配置
type OpenRules struct {
	AccessKeyID      []string   `json:"access_key_id"`
	Lang             []string   `json:"lang"`
	CountyConf       CountyConf `json:"county_conf"`
	FenceInfo        FenceInfo  `json:"fence_info"`
	LaunchApolloName string     `json:"launch_apollo_name"`
}

// CountyConf 区县配置
type CountyConf struct {
	SupportType int   `json:"support_type"`
	CountyList  []int `json:"county_list"`
}

// FenceInfo 围栏信息
type FenceInfo struct {
	GroupID        int      `json:"group_id"`
	FenceCheckType int      `json:"fence_check_type"`
	StartFenceID   []string `json:"start_fence_id"`
	StopFenceID    []string `json:"stop_fence_id"`
}

type DCMPKey struct {
	WayOut     string      `json:"way_out"`
	PeakPeriod *PeakPeriod `json:"peak_period"`
}

// PeakPeriod 高峰期配置
type PeakPeriod struct {
	Content       string   `json:"content"`
	TimeQuantum   []string `json:"time_quantum"`
	SpecialRemark []string `json:"special_remark"`
}

func NewPeakFee() *PeakFee {
	return &PeakFee{
		IsShowEstimatePrice: true,
		IsFirstEstimate:     true,
	}
}

func (t *PeakFee) GetStatus() bool {
	return t.Status
}

func (t *PeakFee) GetIsFirstEstimate() bool {
	return t.IsFirstEstimate
}

func (t *PeakFee) GetIsInterActive() bool {
	return t.SpsData != nil && t.SpsData.CanSelect == consts2.FormInterActive
}

func (t *PeakFee) GetIsUserSelect() bool {
	return t.IsUserSelected
}

func (t *PeakFee) GetIsTaxiPeakFee() bool {
	return t.IsTaxiPeakFeeFlag
}

// UpdateIsShowEstimatePrice 更新是否展示预估价
func (t *PeakFee) UpdateIsShowEstimatePrice(isShowEstimatePrice bool) {
	t.IsShowEstimatePrice = isShowEstimatePrice
}

func (t *PeakFee) GetIsShowEstimatePrice() bool {
	return t.IsShowEstimatePrice
}
