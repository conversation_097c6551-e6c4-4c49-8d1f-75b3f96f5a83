package models

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/group_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"github.com/spf13/cast"
	"strings"
)

const (
	TripcloudPossibleCategoryApollo = "get_tripcloud_possible_category_ids"
)

type BaseCategoryConf struct {
	// 侧边栏配置
	CategoryConf []*CategoryItem

	GroupIdPosition map[string]int32

	// 新配置，map[pcid]分栏id列表
	PossibleCategoryMap map[int64][]int32
}

// CategoryItem 分栏配置项
type CategoryItem struct {
	CategoryID       int32    `json:"category_id"`
	Title            string   `json:"title"`
	SubTitle         string   `json:"sub_title"`
	FoldText         string   `json:"fold_text"`
	Icon             string   `json:"icon"`
	BgGradients      []string `json:"bg_gradients"`
	IsSelected       int32    `json:"is_selected"`
	ProductList      []string `json:"product_list"`
	SubGroupID       []string `json:"sub_group_id"`
	SectionTitle     string   `json:"section_title"`
	Rank             int32    `json:"rank"`
	IsFold           int32    `json:"is_fold"`
	SubCategoryTitle string   `json:"sub_category_title"`
}

func (b *BaseCategoryConf) GetProductPossibleIds(ctx context.Context, product *Product, baseReq *BaseReqData) []int32 {
	if b.PossibleCategoryMap != nil {
		if v, ok := b.PossibleCategoryMap[product.ProductCategory]; ok {
			return v
		}
	}
	return b.getTripCloudPossibleList(ctx, product, baseReq)
}
func (b *BaseCategoryConf) getTripCloudPossibleList(ctx context.Context, product *Product, baseReq *BaseReqData) []int32 {
	// 大巴城际品类不配置可移动分栏
	if product.CarpoolType == consts.CarPoolTypeInterCityStation ||
		product.CarpoolType == consts.CarPoolTypeInterCity {
		return []int32{}
	}

	// 判断是否为三方产品
	if product.IsTripcloudProduct(ctx) {
		apolloParams := map[string]string{
			"business_id":      cast.ToString(product.BusinessID),
			"product_category": cast.ToString(product.ProductCategory),
			"carpool_type":     cast.ToString(product.CarpoolType),
			"product_id":       cast.ToString(product.ProductID),
		}

		if apollo.FeatureToggle(ctx, TripcloudPossibleCategoryApollo, baseReq.PassengerInfo.Phone, apolloParams) {
			_, assignParams := apollo.GetParameters(TripcloudPossibleCategoryApollo, baseReq.PassengerInfo.Phone, apolloParams)
			if assignParams != nil {
				if possibleIds, ok := assignParams["possible_category_ids"]; ok && possibleIds != "" {
					var possibleList []int32
					for _, idStr := range strings.Split(possibleIds, ",") {
						if id, err := cast.ToInt32E(strings.TrimSpace(idStr)); err == nil {
							possibleList = append(possibleList, id)
						}
					}
					return possibleList
				}
			}
		}
	}
	return []int32{}
}

func (b *BaseCategoryConf) GetProductCategoryId(product *Product) int32 {
	id := group_id.BuildGroupIdById(int64(product.SubGroupId), product.ProductCategory)
	if v, ok := b.GroupIdPosition[id]; ok {
		return v
	}

	return 0
}
