package biz_runtime

import (
	"context"
	"encoding/json"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
)

type Quotation PriceApi.EstimateQuotation

// QuotationV2 v2对于N元组有性能优化
type QuotationV2 struct {
	*Quotation

	nTuple *price_api.QuotationNTuple
}

// EstimateQuotation2V1 ...
func EstimateQuotation2V1(quotation PriceApi.EstimateQuotation) *Quotation {
	q := Quotation(quotation)

	return &q
}

// EstimateQuotation2V2 ...
func EstimateQuotation2V2(quotation PriceApi.EstimateQuotation) *QuotationV2 {
	q := &QuotationV2{}

	q.Quotation = EstimateQuotation2V1(quotation)
	q.nTuple = q.GetNTuple()

	return q
}

func (q *QuotationV2) GetProductID() int64 {
	if q.nTuple != nil {
		return q.nTuple.ProductID
	}

	return 0
}

func (q *QuotationV2) GetRequireLevel() string {
	if q.nTuple != nil {
		return q.nTuple.RequireLevel
	}

	return ""
}

func (q *QuotationV2) GetComboType() int16 {
	if q.nTuple != nil {
		return q.nTuple.ComboType
	}

	return 0
}

func (q *QuotationV2) GetCarpoolType() int64 {
	if q.nTuple != nil {
		return q.nTuple.CarpoolType
	}

	return 0
}

func (q *QuotationV2) GetRouteType() int32 {
	if q.nTuple != nil {
		return q.nTuple.RouteType
	}

	return 0
}

func (q *QuotationV2) GetIsSpecialPrice() bool {
	if q.nTuple != nil {
		return q.nTuple.IsSpecialPrice
	}

	return false
}

func (q *QuotationV2) GetLevelType() int32 {
	if q.nTuple != nil {
		return q.nTuple.LevelType
	}

	return 0
}

func (q *QuotationV2) GetBusinessID() int32 {
	if q.nTuple != nil {
		return q.nTuple.BusinessID
	}

	return 0
}

func (q *QuotationV2) GetSpaciousCarAlliance() int64 {
	if q.nTuple != nil {
		return q.nTuple.SpaciousCarAlliance
	}

	return 0
}

func (q *QuotationV2) GetIsDualCarpoolPrice() bool {
	if q.nTuple != nil {
		return q.nTuple.IsDualCarpoolPrice
	}

	return false
}

func (q *Quotation) GetCountPriceType() int32 {
	return int32(q.CountPriceType)
}

func (q *QuotationV2) GetCarpoolPriceType() int32 {
	if q.nTuple != nil {
		return int32(q.nTuple.CarpoolPriceType)
	}

	return 0
}

func (q *Quotation) GetSceneType() int64 {
	return q.SceneType
}

func (q *Quotation) EnableFeeRange() bool {
	return q.EnableAplusRange != nil && *q.EnableAplusRange == 1
}

func (q *Quotation) GetMinFeeRange() float64 {
	if q.AplusMinFee == nil {
		return 0
	}
	return *q.AplusMinFee
}

// GetDynamicTimes 获取动调倍数
func (q *Quotation) GetDynamicTimes() float64 {
	if q == nil {
		return 0
	}

	return q.DynamicTimes
}

// GetTunnelFeeDetail 隧道费
func (q *Quotation) GetTunnelFeeDetail() map[string]float64 {
	if q == nil {
		return nil
	}

	return q.BILL2APITunnelFeeDetail
}

func (q *Quotation) GetQuotationPageType() *int64 {
	if q == nil {
		return nil
	}

	return &q.PageType
}

// GetDynamicMemberReduce 获取溢价保护价格 不需要使用时需要填充sdk
func (q *Quotation) GetDynamicMemberReduce() float64 {
	if q == nil {
		return 0
	}

	return 0
}

// GetNTuple 获取n元组
func (q *Quotation) GetNTuple() *price_api.QuotationNTuple {
	if q == nil || len(q.NTuple) <= 0 {
		return nil
	}

	info := &price_api.QuotationNTuple{}
	err := json.Unmarshal([]byte(q.NTuple), &info)
	if err != nil {
		return nil
	}

	return info
}

// GetOrderType 获取订单信息
func (q *Quotation) GetOrderType() int16 {
	if info := q.GetNTuple(); info != nil {
		return int16(info.OrderType)
	}

	return 0
}

// GetLevelID 获取会员等级
func (q *Quotation) GetLevelID() int64 {
	return q.LevelId
}

// GetProductId 获取productId
func (q *Quotation) GetProductId() int64 {
	if q == nil {
		return 0
	}

	if nTuple := q.GetNTuple(); nTuple != nil {
		return nTuple.ProductID
	}

	return 0
}

// GetRequireLevel 获取require_level
func (q *Quotation) GetRequireLevel() string {
	if q == nil {
		return ""
	}

	if nTuple := q.GetNTuple(); nTuple != nil {
		return nTuple.RequireLevel
	}

	return ""
}

// GetCarpoolType 获取carpool_type
func (q *Quotation) GetCarpoolType() int64 {
	if q == nil {
		return 0
	}

	if nTuple := q.GetNTuple(); nTuple != nil {
		return nTuple.CarpoolType
	}

	return 0
}

// GetComboType 获取combo_type
func (q *Quotation) GetComboType() int16 {
	if q == nil {
		return 0
	}

	if nTuple := q.GetNTuple(); nTuple != nil {
		return nTuple.ComboType
	}

	return 0
}

// GetLevelType 获取level_type
func (q *Quotation) GetLevelType() int32 {
	if q == nil {
		return 0
	}

	if nTuple := q.GetNTuple(); nTuple != nil {
		return nTuple.LevelType
	}

	return 0
}

// GetIsSpecialPrice 获取is_special_price
func (q *Quotation) GetIsSpecialPrice() bool {
	if q == nil {
		return false
	}

	if nTuple := q.GetNTuple(); nTuple != nil {
		return nTuple.IsSpecialPrice
	}

	return false
}

// GetBusinessId 获取business_id
func (q *Quotation) GetBusinessId() int32 {
	if q == nil {
		return 0
	}

	if nTuple := q.GetNTuple(); nTuple != nil {
		return nTuple.BusinessID
	}

	return 0
}

// GetRouteType 获取route_type
func (q *Quotation) GetRouteType() int32 {
	if q == nil {
		return 0
	}

	if nTuple := q.GetNTuple(); nTuple != nil {
		return nTuple.RouteType
	}

	return 0
}

// GetProductCategory 获取product_category
func (q *Quotation) GetProductCategory() int64 {
	if q == nil {
		return 0
	}

	return q.ProductCategory
}

// GetDefaultPayType 获取默认支付方式
func (q *Quotation) GetDefaultPayType() int64 {
	if q == nil {
		return 0
	}

	return q.DefaultPayType
}

// GetMixedPaymentInfo 获取支付信息
func (q *Quotation) GetMixedPaymentInfo(ctx context.Context) *price_api.MixedPaymentInfo {
	if q == nil || len(q.MixedPaymentInfo) <= 0 {
		return nil
	}

	payInfo := &price_api.MixedPaymentInfo{}

	err := json.Unmarshal([]byte(q.MixedPaymentInfo), &payInfo)
	if err != nil {
		log.Trace.Warnf(ctx, "GetMixedPaymentInfo", "mixed_payment_info unmarshal fail, data:%+v", q.MixedPaymentInfo)
		return nil
	}

	return payInfo
}

// GetFeeDetailInfo 获取fee_detail_info
func (q *Quotation) GetFeeDetailInfo() map[string]float64 {
	if q == nil {
		return nil
	}

	return q.FeeDetailInfo
}

// GetCityID 获取当前城市
func (q *Quotation) GetCityID() int {
	if q == nil {
		return 0
	}

	return int(q.Area)
}

// GetToCityID 获取目的地城市
func (q *Quotation) GetToCityID() int {
	if q == nil {
		return 0
	}

	return int(q.ToArea)
}

// GetFromCounty 目前取不到也不需要, 用它做实验需要补齐报价单
func (q *Quotation) GetFromCounty() int32 {
	return 0
}

// GetDiscountDesc 获取discount_desc
func (q *Quotation) GetDiscountDesc(ctx context.Context) *price_api.DiscountItem {
	if q == nil || len(q.DiscountDesc) <= 0 {
		return nil
	}

	discountItem := &price_api.DiscountItem{}
	err := json.Unmarshal([]byte(q.DiscountDesc), &discountItem)
	if err != nil {
		log.Trace.Warnf(ctx, "GetDiscountDesc", "discount_desc unmarshal fail, data:%+v", q.DiscountDesc)
		return nil
	}

	return discountItem
}

// GetDiscountDescByTypes 根据types取值
func (q *Quotation) GetDiscountDescByTypes(ctx context.Context, types []string) *price_api.DiscountItem {
	if q == nil || len(q.DiscountDesc) <= 0 {
		return nil
	}

	discountItems := make([]*price_api.DiscountItem, 0)
	err := json.Unmarshal([]byte(q.DiscountDesc), &discountItems)
	if err != nil {
		log.Trace.Warnf(ctx, "GetDiscountDesc", "discount_desc unmarshal fail, data:%+v, err:%+v", q.DiscountDesc, err)
		return nil
	}

	if len(discountItems) < 0 {
		return nil
	}
	for _, item := range discountItems {
		if util.InArrayStr(item.Type, types) {
			return item
		}
	}

	return nil
}

func (q *Quotation) GetAlipayCoupon() *PriceApi.CouponInfoV2 {
	if q == nil || q.DiscountSet == nil || q.DiscountSet.ExternalCoupon == nil {
		return nil
	}

	if "alipay" == q.DiscountSet.ExternalCoupon.CouponSource {
		return q.DiscountSet.ExternalCoupon
	}

	return nil
}

// IsBusinessPay 判断是否是企业付
func (q *Quotation) IsBusinessPay() bool {
	if q == nil {
		return false
	}

	if q.GetDefaultPayType() == consts.BusinessPaymentType ||
		q.GetDefaultPayType() == consts.BusinessPayByTeam {
		return true
	}

	return false
}

// GetEstimateFee 获取价格
func (q *Quotation) GetEstimateFee() float64 {
	if q == nil {
		return 0
	}

	return q.EstimateFee
}

// GetCarpoolScenePrice 获取拼成乐价格
func (q *Quotation) GetCarpoolScenePrice() []*PriceApi.CarpoolScenePrice {
	if q == nil {
		return make([]*PriceApi.CarpoolScenePrice, 0)
	}

	return q.CarpoolSceneBill.ScenePrice
}

// GetDynamicTotalFee 获取券前价
func (q *Quotation) GetDynamicTotalFee() float64 {
	if q == nil {
		return 0
	}

	return q.DynamicTotalFee
}

// GetPersonalEstimateFee 获取个人付预估价
func (q *Quotation) GetPersonalEstimateFee() float64 {
	if q == nil {
		return 0
	}

	return q.PersonalEstimateFee
}

// GetPreTotalFee 获取pre total fee
func (q *Quotation) GetPreTotalFee() float64 {
	if q == nil {
		return 0
	}
	return q.PreTotalFee
}

func (q *Quotation) GetTotalFeeWithoutDiscount() float64 {
	if q == nil {
		return 0
	}
	return q.TotalFeeWithoutDiscount
}

// GetCapPrice 获取cap price
func (q *Quotation) GetCapPrice() float64 {
	if q == nil {
		return 0
	}
	return q.CapPrice
}

// GetTCDiscountFee 获取第三方优惠
func (q *Quotation) GetTCDiscountFee() float64 {
	return q.TripCloudDiscountFee
}

func (q *Quotation) GetBillFeeDetailInfo() map[string]float64 {
	return q.FeeDetailInfo
}

func (q *Quotation) GetBillDriverMetre() int64 {
	return q.DriverMetre
}

func (q *Quotation) GetBillInfoCurrency() string {
	return q.Currency
}

func (q *Quotation) GetVCard() *PriceApi.EstimateNewFormVCardInfo {
	if q.DiscountSet != nil && q.DiscountSet.VCardInfo != nil {
		info := q.DiscountSet.VCardInfo
		return &PriceApi.EstimateNewFormVCardInfo{
			CardId:           info.CardId,
			CardBizId:        info.CardBizId,
			CardSubBizId:     info.CardSubBizId,
			PayStatus:        info.PayStatus,
			Source:           info.Source,
			CardType:         info.CardType,
			UseScope:         info.UseScope,
			UseRule:          info.UseRule,
			BatchId:          info.BatchId,
			SendEmptyCarTime: info.SendEmptyCarTime,
		}
	}
	return nil
}

func (q *Quotation) GetCouponInfo() *PriceApi.EstimateNewFormCouponInfo {
	item := q.GetDiscountDescByTypes(nil, []string{"coupon"})
	if item == nil {
		return nil
	}

	return &PriceApi.EstimateNewFormCouponInfo{
		Amount:    util.FormatPrice(item.Amount, 2),
		CustomTag: item.CustomTag,
	}
}

func (q *Quotation) GetVcard() *PriceApi.EstimateNewFormVCardInfo {
	if q.DiscountSet != nil && q.DiscountSet.VCardInfo != nil {
		info := q.DiscountSet.VCardInfo
		return &PriceApi.EstimateNewFormVCardInfo{
			CardId:           info.CardId,
			CardBizId:        info.CardBizId,
			CardSubBizId:     info.CardSubBizId,
			PayStatus:        info.PayStatus,
			Source:           info.Source,
			CardType:         info.CardType,
			UseScope:         info.UseScope,
			UseRule:          info.UseRule,
			BatchId:          info.BatchId,
			SendEmptyCarTime: info.SendEmptyCarTime,
		}
	}

	return nil
}

func (q *Quotation) GetMixedDeductPrice() float64 {
	var deductInfo float64

	if !q.IsBusinessPay() {
		return 0.0
	}

	deductInfo = q.GetEstimateFee()

	if payInfo := q.GetMixedPaymentInfo(context.TODO()); payInfo != nil && payInfo.DeductFee > 0 {
		deductInfo = payInfo.DeductFee
	}

	return deductInfo
}
func (q *Quotation) GetPaymentInfo() *PriceApi.EstimateNewFormPaymentInfo {

	res := &PriceApi.EstimateNewFormPaymentInfo{
		DefaultPayType: int32(q.GetDefaultPayType()),
	}

	if mixInfo := q.GetMixedPaymentInfo(context.TODO()); mixInfo != nil {
		res.MixedPayDeductInfo = &PriceApi.MixedPayDeductInfo{
			DeductFee: mixInfo.DeductFee,
		}
	}

	return res
}
