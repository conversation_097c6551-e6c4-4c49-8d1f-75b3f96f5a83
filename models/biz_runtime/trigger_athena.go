package biz_runtime

import (
	"context"
	"git.xiaojukeji.com/s3e/common-lib/v2/component/diff"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/athena"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

func TriggerBubbleAthena(ctx context.Context, req *models.BaseReqData, products []*models.Product) {

	athenaReq := &AthenaApiv3.AthenaBubbleNewReq{
		// commonInfo
		ClientType:  int64(req.CommonInfo.ClientType),
		AppVersion:  req.CommonInfo.AppVersion,
		Lang:        req.CommonInfo.Lang,
		Channel:     &req.CommonInfo.Channel,
		PageType:    &req.CommonInfo.PageType,
		MenuID:      &req.CommonInfo.MenuID,
		AccessKeyID: &req.CommonInfo.AccessKeyID,
		OrderType:   req.CommonInfo.OrderType,
		TabID:       &req.CommonInfo.TabId,
		LinkSource:  util.StringPtr("v3"),

		// location info
		MapType:    "", // 处理和原 mq->athena_click_stream -> athena_api:TriggerBubbleAthena 链路对齐，暂时传空值
		FromArea:   int64(req.AreaInfo.Area),
		FromLat:    req.AreaInfo.FromLat,
		FromLng:    req.AreaInfo.FromLng,
		FromName:   &req.AreaInfo.FromName,
		ToLat:      req.AreaInfo.ToLat,
		ToLng:      req.AreaInfo.ToLng,
		ToName:     &req.AreaInfo.ToName,
		CurrentLng: &req.AreaInfo.CurLng,
		CurrentLat: &req.AreaInfo.CurLat,

		// passenger info
		Phone:    req.PassengerInfo.Phone,
		Pid:      strconv.FormatInt(req.PassengerInfo.PID, 10),
		UserType: &req.PassengerInfo.UserType,

		// extra info
		ExtraInfo: map[string]string{
			"athena_rpc_trigger_switch": "1",
		},
	}

	// 构建产品信息
	var apiAddProducts []*AthenaApiv3.AthenaEstimateProductInfo
	for _, product := range products {
		apiAddProducts = append(apiAddProducts, &AthenaApiv3.AthenaEstimateProductInfo{
			ProductID:          product.ProductID,
			RequireLevel:       product.RequireLevel,
			ComboType:          product.ComboType,
			CarpoolType:        &product.CarpoolType,
			IsSpecialPrice:     &product.IsSpecialPrice,
			ProductCategory:    &product.ProductCategory,
			EstimateID:         &product.EstimateID,
			LevelType:          &product.LevelType,
			IsDualCarpoolPrice: &product.IsDualCarpoolPrice,
			CarpoolPriceType:   &product.CarpoolPriceType,
			DepartureTime:      util.Int64Ptr(util.GetOrDefault(product.BizInfo.DepartureTime != 0, product.BizInfo.DepartureTime, req.CommonInfo.DepartureTime)), // 根据需要设置
			// business_id 处理和原 mq->athena_click_stream -> athena_api:TriggerBubbleAthena 链路对齐，暂时传空值
		})

		// 设置会员等级
		if product.BizInfo != nil && product.BizInfo.UserMemberProfile.LevelID > 0 {
			athenaReq.MemberLevel = &product.BizInfo.UserMemberProfile.LevelID
		}
	}
	athenaReq.APIAddProduct = apiAddProducts

	if diff.CheckDiffStatus(ctx) {
		diff.CheckDiffAndLog(ctx, diff.DownStreamDirpcType, "TriggerBubbleAthena", athenaReq)
		return
	}

	athena.TriggerBubbleAthena(ctx, athenaReq)
}
