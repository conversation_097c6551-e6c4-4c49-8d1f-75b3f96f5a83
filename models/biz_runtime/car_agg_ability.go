package biz_runtime

import (
	"context"
	"encoding/json"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"github.com/spf13/cast"
	apollo2 "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
	"strconv"
	"strings"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type GroupConfig struct {
	SubGroupID  int32    `json:"sub_group_id"`
	Remark      string   `json:"remark"`
	Toggle      string   `json:"city_toggle"`
	ProductList []string `json:"product_list"`
	LevelType   int32    `json:"level_type"`
}

const tag = "HandlerCarAggregation"

func loadCarAggregationConf(ctx context.Context, pg *ProductsGenerator) (map[int32]GroupConfig, map[int64]int32) {
	var (
		PcIDToSubGroupIDMap = make(map[int64]int32)
		groupsConfig        []GroupConfig
	)

	// apollo配置的聚合车型
	configByteArray, err := apollo.GetConfigsByNamespace(ctx, "car_aggregation")
	if err != nil || len(configByteArray) == 0 {
		log.Trace.Warnf(ctx, tag, "err: %v", err)
		return nil, nil
	}

	err = json.Unmarshal(configByteArray, &groupsConfig)
	if err != nil {
		log.Trace.Warnf(ctx, tag, "err: %v", err)
		return nil, nil
	}

	if len(groupsConfig) == 0 {
		log.Trace.Warnf(ctx, tag, "group config is nil")
		return nil, nil
	}

	// 获取原始
	params := map[string]string{
		"key":           fmt.Sprint(pg.BaseReqData.PassengerInfo.PID),
		"pid":           fmt.Sprint(pg.BaseReqData.PassengerInfo.PID),
		"phone":         pg.BaseReqData.PassengerInfo.Phone,
		"app_version":   pg.BaseReqData.CommonInfo.AppVersion,
		"access_key_id": fmt.Sprint(pg.BaseReqData.CommonInfo.AccessKeyID),
		"lang":          pg.BaseReqData.CommonInfo.Lang,
		"city":          fmt.Sprintf("%d", pg.BaseReqData.AreaInfo.Area),
		"page_type":     fmt.Sprintf("%d", pg.BaseReqData.CommonInfo.PageType),
	}

	finalSubGroupId2Config := make(map[int32]GroupConfig)
	for _, config := range groupsConfig {
		if config.Toggle != "" {
			if ok := apollo.FeatureToggle(ctx, config.Toggle, fmt.Sprint(pg.BaseReqData.PassengerInfo.PID), params); !ok {
				continue
			}
		}
		finalSubGroupId2Config[config.SubGroupID] = config
	}

	//填充绑定level_type的盒子
	finalSubGroupId2Config = fillingProductListByLevelType(finalSubGroupId2Config, pg.productsInfoFull)

	// 车企进盒子
	//  $aFinalGroupList = self::_addCarManufacturer($aFinalGroupList, $aParams);

	//   if (is_array($this->_newSubGroupIDToProducts[$aGroupId]) && 1 == sizeof($this->_newSubGroupIDToProducts[$aGroupId])) {
	//                $this->_newSubGroupIDToProducts[$aGroupId] = [];
	//                $this->_newPcIDToSubGroupID[$this->_newSubGroupIDToProducts[$aGroupId][0]] = 0;
	//            }

	finalSubGroupId2Config = OutsideCarManufacturer(ctx, finalSubGroupId2Config, pg.productsInfoFull, *pg.BaseReqData)

	// pk优先级
	if _, ok := finalSubGroupId2Config[consts.SubGroupIdTaxiPricingBox]; ok {
		if pg.BaseReqData.CommonInfo.PricingBoxData == nil {
			delete(finalSubGroupId2Config, consts.SubGroupIdTaxiPricingBox)
		}
	}

	// 初始化
	for subGroupID, config := range finalSubGroupId2Config {
		for _, pcId := range config.ProductList {
			pcIdInt, err := strconv.ParseInt(pcId, 10, 64)
			if err != nil {
				log.Trace.Warnf(ctx, tag, "pcId 2 int fail. err:%+v", err)
				continue
			}

			if !allowProductIntoBox(ctx, params, pcIdInt, int64(subGroupID)) {
				continue
			}

			if _, ok := PcIDToSubGroupIDMap[pcIdInt]; ok {
				log.Trace.Warnf(ctx, tag, "pcId already exists")
				continue
			}
			PcIDToSubGroupIDMap[pcIdInt] = subGroupID
		}
	}

	return finalSubGroupId2Config, PcIDToSubGroupIDMap
}

func fillingProductListByLevelType(finalSubGroupId2Config map[int32]GroupConfig, productList []*ProductInfoFull) map[int32]GroupConfig {
	if len(productList) == 0 {
		return finalSubGroupId2Config
	}

	for _, conf := range finalSubGroupId2Config {
		//若盒子和level_type绑定，则遍历product_map,往product_list append
		if conf.LevelType == 0 {
			continue
		}

		for _, p := range productList {
			if p.GetLevelType() == conf.LevelType {
				conf.ProductList = append(conf.ProductList, cast.ToString(p.GetProductCategory()))
			}
		}
	}
	return finalSubGroupId2Config
}

func (pg *ProductsGenerator) HandlerCarAggregation(ctx context.Context, productsFull []*ProductInfoFull) {
	var PcIDToSubGroupIDMap = make(map[int64]int32)

	// X折特价车盒子
	pg.FinalSubGroupId2Config = HandleXDiscountBox(ctx, pg.FinalSubGroupId2Config, productsFull, *pg.BaseReqData)

	params := map[string]string{
		"key":           fmt.Sprint(pg.BaseReqData.PassengerInfo.PID),
		"pid":           fmt.Sprint(pg.BaseReqData.PassengerInfo.PID),
		"phone":         pg.BaseReqData.PassengerInfo.Phone,
		"app_version":   pg.BaseReqData.CommonInfo.AppVersion,
		"access_key_id": fmt.Sprint(pg.BaseReqData.CommonInfo.AccessKeyID),
		"lang":          pg.BaseReqData.CommonInfo.Lang,
		"city":          fmt.Sprintf("%d", pg.BaseReqData.AreaInfo.Area),
		"page_type":     fmt.Sprintf("%d", pg.BaseReqData.CommonInfo.PageType),
	}

	for subGroupID, config := range pg.FinalSubGroupId2Config {
		for _, pcId := range config.ProductList {
			pcIdInt, err := strconv.ParseInt(pcId, 10, 64)
			if err != nil {
				log.Trace.Warnf(ctx, tag, "pcId 2 int fail. err:%+v", err)
				continue
			}

			// 品类维度控制
			if !allowProductIntoBox(ctx, params, pcIdInt, int64(subGroupID)) {
				continue
			}

			if _, ok := PcIDToSubGroupIDMap[pcIdInt]; ok {
				log.Trace.Warnf(ctx, tag, "pcId already exists")
				continue
			}
			PcIDToSubGroupIDMap[pcIdInt] = subGroupID
		}
	}

	pg.pcIDToSubGroupIDMap = PcIDToSubGroupIDMap

	// athena前首次赋值
	ret := GetTCExperimentParams(ctx, *pg.BaseReqData)
	for _, product := range productsFull {
		// 花小猪出盒子
		if product.GetProductCategory() == estimate_pc_id.EstimatePcIdKFlower &&
			ret["hitFarMustCheaperBox"] == "1" && ret["isHuaxiaozhuCapOut"] == "1" {
			continue
		}
		if subId, ok := pg.pcIDToSubGroupIDMap[product.GetProductCategory()]; ok {
			product.Product.SubGroupId = subId
		}
	}
}

// GetTCExperimentParams
/**
三方和远必省盒子实验逻辑
*/
func GetTCExperimentParams(ctx context.Context, data models.BaseReqData) map[string]string {
	var (
		params = make(map[string]string, 0)
	)
	// 实验参数
	ABparam := data.GetApolloParam()
	// 命中的一个开关
	ret := _getFarMustCheaperApolloResult(ctx, ABparam)
	if ret == nil {
		return params
	}
	// 命中获取参数
	params["hitFarMustCheaperBox"] = ret.GetAssignment().GetParameter("is_hit_box", "0")
	params["isHuaxiaozhuCapOut"] = ret.GetAssignment().GetParameter("huaxiaozhu_cap_out", "0")
	params["needFilterThirdPartyBox"] = ret.GetAssignment().GetParameter("filter_third_party_box", "0")
	params["supportOther"] = ret.GetAssignment().GetParameter("support_other", "0")

	return params
}

func HandleXDiscountBox(ctx context.Context, finalSubGroupId2Config map[int32]GroupConfig, productInfoFull []*ProductInfoFull, data models.BaseReqData) map[int32]GroupConfig {
	if _, ok := finalSubGroupId2Config[consts.SubGroupIdXDiscount]; !ok {
		return finalSubGroupId2Config
	}

	params := data.GetApolloParam()
	// 和外层的toggle是同一个
	isAllow, assignment := apollo.GetParameters("x_discount_box_open_switch", params["pid"], params)
	if !isAllow {
		delete(finalSubGroupId2Config, consts.SubGroupIdXDiscount)
		return finalSubGroupId2Config
	}

	discount, ok := assignment["exact_discount"]
	if !ok || util.ToInt64(discount) == 0 {
		delete(finalSubGroupId2Config, consts.SubGroupIdXDiscount)
		return finalSubGroupId2Config
	}

	var (
		productsMap           = make(map[int64]*ProductInfoFull)
		hasFastCar            bool
		fastCarItem           *ProductInfoFull
		xDiscountProducts     = make([]string, 0)
		shortDistanceProducts = make([]string, 0)
	)
	for _, product := range productInfoFull {
		productsMap[product.GetProductCategory()] = product

		if estimate_pc_id.EstimatePcIdFastCar == product.GetProductCategory() {
			hasFastCar = true
			fastCarItem = product
		}
	}

	// 无快车情况
	if !hasFastCar || fastCarItem == nil {
		delete(finalSubGroupId2Config, consts.SubGroupIdXDiscount)
		return finalSubGroupId2Config
	}

	discountNum := util.ToFloat64(discount) / 100.0
	fastCarEstimateFee := fastCarItem.GetEstimateFee()
	comparedPrice := fastCarEstimateFee * discountNum

	for _, pcID := range finalSubGroupId2Config[consts.SubGroupIdXDiscount].ProductList {
		productItem, ok1 := productsMap[util.ToInt64(pcID)]
		if !ok1 || productItem == nil {
			continue
		}

		if productItem.GetEstimateFee() <= comparedPrice {
			xDiscountProducts = append(xDiscountProducts, pcID)
		}
	}

	if len(xDiscountProducts) == 0 {
		delete(finalSubGroupId2Config, consts.SubGroupIdXDiscount)
		return finalSubGroupId2Config
	}

	// 重写X折特价车盒子的product_list
	xDiscountConfig := finalSubGroupId2Config[consts.SubGroupIdXDiscount]
	xDiscountConfig.ProductList = xDiscountProducts
	finalSubGroupId2Config[consts.SubGroupIdXDiscount] = xDiscountConfig

	xDiscountProductsMap := make(map[string]bool)
	for _, pcID := range xDiscountProducts {
		xDiscountProductsMap[pcID] = true
	}

	// 摘掉三方盒子重复的product_list（已进入X折盒子）
	for _, pcID := range finalSubGroupId2Config[consts.SubGroupIdShortDistance].ProductList {
		inBox, exist := xDiscountProductsMap[pcID]
		if exist && inBox {
			continue
		} else {
			shortDistanceProducts = append(shortDistanceProducts, pcID)
		}
	}

	shortDistanceConfig := finalSubGroupId2Config[consts.SubGroupIdShortDistance]
	shortDistanceConfig.ProductList = shortDistanceProducts
	finalSubGroupId2Config[consts.SubGroupIdShortDistance] = shortDistanceConfig

	return finalSubGroupId2Config
}

func _getFarMustCheaperApolloResult(ctx context.Context, abParam map[string]string) *model.ToggleResult {
	exp := dcmp.GetDcmpPlainContent(ctx, "config_text-far_must_cheaper_exp")
	if "" != strings.TrimSpace(exp) {
		res, err := apollo2.FeatureToggle(exp, apollo.ConvertToModel(abParam["pid"], abParam))
		if err == nil && res.IsAllow() {
			return res
		}
		res, err = apollo2.FeatureToggle("far_must_cheaper_press_toggle", apollo.ConvertToModel(abParam["pid"], abParam))
		return res
	}
	return apollo.GetHitToggleByNamespace(ctx, abParam["pid"], "wycll_bubble_form_mogou", abParam)
}

func OutsideCarManufacturer(ctx context.Context, finalSubGroupId2Config map[int32]GroupConfig, productInfoFull []*ProductInfoFull, data models.BaseReqData) map[int32]GroupConfig {
	params := data.GetApolloParam()

	// 追加车型支持三方扶持开关
	isOpen, _ := apollo.GetParameters("anycar_estimate_support_car_manufacturer_switch", params["pid"], params)
	if !isOpen {
		return finalSubGroupId2Config
	}

	isAllow, assignment := apollo.GetParameters("estimate_support_car_manufacturer", params["pid"], params)
	if !isAllow {
		return finalSubGroupId2Config
	}

	var (
		supportPcIds          []string
		unsupportPcIds        []string
		shortDistanceProducts = make([]string, 0)
	)

	supportPcIdStr, ok := assignment["supported_pcid"]
	if ok && supportPcIdStr != "" {
		supportPcIds = strings.Split(supportPcIdStr, ",")
	}

	unsupportPcIdStr, ok := assignment["unsupported_pcid"]
	if ok && unsupportPcIdStr != "" {
		unsupportPcIds = strings.Split(unsupportPcIdStr, ",")
	}

	expName, ok := assignment["exp_name"]
	isAllow, assignment1 := apollo.GetParameters(expName, params["pid"], params)
	if !isAllow {
		return finalSubGroupId2Config
	}

	nextExpName, ok := assignment["next_exp_name"]
	if nextExpName != "" {
		isAllowNext, assignment2 := apollo.GetParameters(nextExpName, params["pid"], params)
		if !isAllowNext {
			return finalSubGroupId2Config
		}

		isSupportOutside, ok2 := assignment2["is_support_outside"]
		isSupport, _ := strconv.ParseInt(isSupportOutside, 10, 64)
		if !ok2 || isSupport != 1 {
			return finalSubGroupId2Config
		}
	} else {
		isSupportOutside, ok1 := assignment1["is_support_outside"]
		isSupport, _ := strconv.ParseInt(isSupportOutside, 10, 64)
		if !ok1 || isSupport != 1 {
			return finalSubGroupId2Config
		}
	}

	supportProductsMap := make(map[string]bool)
	for _, supportPcId := range supportPcIds {
		supportProductsMap[supportPcId] = true
	}

	// 扶持品类出盒子
	for _, pcID := range finalSubGroupId2Config[consts.SubGroupIdShortDistance].ProductList {
		outside, exist := supportProductsMap[pcID]
		if exist && outside {
			continue
		} else {
			shortDistanceProducts = append(shortDistanceProducts, pcID)
		}
	}

	// 非扶持品类 & 不在盒子内 => 进盒子
	for _, unsupportPcId := range unsupportPcIds {
		if !util.InArrayStr(unsupportPcId, shortDistanceProducts) {
			shortDistanceProducts = append(shortDistanceProducts, unsupportPcId)
		}
	}

	configTmp := finalSubGroupId2Config[consts.SubGroupIdShortDistance]
	configTmp.ProductList = shortDistanceProducts
	finalSubGroupId2Config[consts.SubGroupIdShortDistance] = configTmp

	return finalSubGroupId2Config
}

func allowProductIntoBox(ctx context.Context, params map[string]string, pcID, groupID int64) bool {
	if groupID != consts.SubGroupIdUnitaxi {
		return true
	}

	// 创建一个新的 map 副本
	newParams := make(map[string]string)
	for k, v := range params {
		newParams[k] = v
	}

	newParams["pc_id"] = strconv.FormatInt(pcID, 10)
	newParams["group_id"] = strconv.FormatInt(groupID, 10)
	return apollo.FeatureToggle(ctx, "gs_car_aggregation_product_filter", params["key"], newParams)
}
