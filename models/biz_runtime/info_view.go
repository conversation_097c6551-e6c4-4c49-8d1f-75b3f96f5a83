package biz_runtime

import (
	"context"
	hundun "git.xiaojukeji.com/dirpc/dirpc-go-http-Hundun"
	Sps "git.xiaojukeji.com/dirpc/dirpc-go-http-Sps"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/category_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/group_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/ab"
	"git.xiaojukeji.com/nuwa/golibs/knife"
	"strconv"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"

	GoMember "git.xiaojukeji.com/dirpc/dirpc-go-http-GoMember"
	midl "git.xiaojukeji.com/dirpc/dirpc-go-http-Member"
	CarpoolApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/hestia_charge"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

const HitNormalNoAnswerCompensation = "hit_normal_no_answer_compensation"

/* ==================  product ================ */
func (s *ProductInfoFull) GetProductCategory() int64 {
	return s.Product.ProductCategory
}

func (s *ProductInfoFull) GetProductId() int64 {
	return s.Product.ProductID
}

func (s *ProductInfoFull) GetBusinessID() int64 {
	return s.Product.BusinessID
}

func (s *ProductInfoFull) GetComboType() int64 {
	return s.Product.ComboType
}

func (s *ProductInfoFull) GetRequireLevel() string {
	return s.Product.RequireLevel
}

func (s *ProductInfoFull) GetLevelType() int32 {
	return s.Product.LevelType
}

func (s *ProductInfoFull) GetRouteType() int64 {
	return s.Product.RouteType
}

func (s *ProductInfoFull) IsSpecialPrice() bool {
	return s.Product.IsSpecialPrice
}

func (s *ProductInfoFull) GetCarpoolPriceType() int32 {
	return s.Product.CarpoolPriceType
}

func (s *ProductInfoFull) IsDualCarpoolPrice() bool {
	return s.Product.IsDualCarpoolPrice
}

func (s *ProductInfoFull) GetIsDualCarpoolPrice() bool {
	return s.Product.IsDualCarpoolPrice
}

func (s *ProductInfoFull) GetCarpoolType() int64 {
	if s.Product == nil {
		return 0
	}
	return s.Product.CarpoolType
}

func (s *ProductInfoFull) GetSubGroupId() int32 {
	return s.Product.SubGroupId
}

func (s *ProductInfoFull) GetOrderType() int16 {
	if s.Product == nil {
		return 0
	}
	return s.Product.OrderType
}

func (s *ProductInfoFull) GetAirType() int32 {
	if s.Product == nil {
		return 0
	}

	return s.Product.AirportType
}

func (s *ProductInfoFull) GetIsSpecialPrice() bool {
	if s.Product == nil {
		return false
	}

	return s.Product.IsSpecialPrice
}

func (s *ProductInfoFull) GetSceneType() int64 {
	if s.Product == nil {
		return 0
	}

	return s.Product.SceneType
}

func (s *ProductInfoFull) GetExamType() int32 {
	if s.Product == nil {
		return 0
	}

	return s.Product.ExamType
}

func (s *ProductInfoFull) GetRailType() int32 {
	if s.Product == nil {
		return 0
	}

	return s.Product.RailwayType
}

func (prod *ProductInfoFull) GetCommonBizInfo() models.CommonBizInfo {
	return prod.BaseReqData.CommonBizInfo
}

func (prod *ProductInfoFull) GetSelectInfo() *models.StationInventorySelectInfo {
	bizInfo := prod.GetBizInfo()
	if bizInfo == nil {
		return nil
	}
	if bizInfo.StationInventoryInfo == nil {
		return nil
	}
	return &bizInfo.StationInventoryInfo.SelectInfo
}

// NTuple 标识
type NTuple struct {
	ProductCategory       int64  `json:"product_category"`
	OrderType             int16  `json:"order_type"`
	ProductID             int64  `json:"product_id"`
	BusinessID            int64  `json:"business_id"`
	RequireLevel          string `json:"require_level"` // 因为php的nTuple sdk 和 go里的NTuple 都是string
	CarpoolType           int64  `json:"carpool_type"`
	LevelType             int32  `json:"level_type"`
	ComboType             int64  `json:"combo_type"`
	SceneType             int64  `json:"scene_type"`
	IsSpecialPrice        bool   `json:"is_special_price"`
	CarpoolPriceType      int32  `json:"carpool_price_type"`
	IsDualCarpoolPrice    bool   `json:"is_dual_carpool_price"`
	AirportType           int32  `json:"airport_type"`
	RailwayType           int32  `json:"railway_type"`
	HotelType             int32  `json:"hotel_type"`
	StationServiceControl int32  `json:"station_service_control"`
	OType                 int32  `json:"otype"`
	RouteType             int64  `json:"route_type"`
}

// todo removed
func (s *ProductInfoFull) GetNTuple() *NTuple {
	return &NTuple{
		ProductCategory:       s.GetProductCategory(),
		OrderType:             s.Product.OrderType,
		ProductID:             s.Product.ProductID,
		BusinessID:            s.Product.BusinessID,
		RequireLevel:          s.Product.RequireLevel,
		CarpoolType:           s.Product.CarpoolType,
		LevelType:             s.Product.LevelType,
		ComboType:             s.Product.ComboType,
		SceneType:             s.Product.SceneType,
		IsSpecialPrice:        s.Product.IsSpecialPrice,
		CarpoolPriceType:      s.Product.CarpoolPriceType,
		IsDualCarpoolPrice:    s.Product.IsDualCarpoolPrice,
		AirportType:           s.Product.AirportType,
		RailwayType:           s.Product.RailwayType,
		HotelType:             s.Product.HotelType,
		StationServiceControl: s.Product.StationServiceControl,
		OType:                 s.Product.OType,
		RouteType:             s.Product.RouteType,
	}
}

// todo
func (s *ProductInfoFull) GetNTupleMapForCarpoolAPI() map[string]string {
	var nTupleMap = make(map[string]string)
	nTupleMap["product_id"] = strconv.FormatInt(s.Product.ProductID, 10)
	nTupleMap["carpool_type"] = strconv.FormatInt(s.Product.CarpoolType, 10)
	nTupleMap["route_type"] = strconv.FormatInt(s.Product.RouteType, 10)
	nTupleMap["require_level"] = s.Product.RequireLevel
	nTupleMap["combo_type"] = strconv.FormatInt(s.Product.ComboType, 10)
	nTupleMap["scene_type"] = strconv.FormatInt(s.Product.SceneType, 10)
	nTupleMap["carpool_price_type"] = strconv.FormatInt(int64(s.Product.CarpoolPriceType), 10)
	nTupleMap["is_dual_carpool_price"] = strconv.FormatBool(s.Product.IsDualCarpoolPrice)
	return nTupleMap
}

/* ==================  billInfo ================ */
func (s *ProductInfoFull) GetBillRouteIdList() []string {
	if s.GetBillInfo() == nil {
		return nil
	}
	return s.GetBillInfo().RouteIdList
}

// todo removed
func (s *ProductInfoFull) GetBillDriverMetre() int64 {
	if s.GetBillInfo() == nil {
		return 0
	}
	return s.GetBillInfo().DriverMetre
}

func (s *ProductInfoFull) GetBillDriverMinute() int64 {
	if s.GetBillInfo() == nil {
		return 0
	}
	return s.GetBillInfo().DriverMinute
}

// GetDriveInfo 获取行驶里程 和 时间
func (s *ProductInfoFull) GetDriveInfo() (driverMeter int64, driverMinute int64) {
	return s.hu.BillInfo.DriverMetre, s.hu.BillInfo.DriverMinute
}

// todo removed
func (s *ProductInfoFull) GetRouteIdList() []string {
	return s.hu.BillInfo.RouteIdList
}

/* ==================  bizInfo ================ */
//todo removed
func (s *ProductInfoFull) GetBizInfo() *models.PrivateBizInfo {
	return s.Product.BizInfo
}

// todo
func (s *ProductInfoFull) GetPrivateBizInfo() *models.PrivateBizInfo {
	return s.Product.BizInfo
}

func (s *ProductInfoFull) GetProductCheckStatus() int {
	return s.GetBizInfo().CheckStatus
}

func (s *ProductInfoFull) GetMaxSeatNum() int32 {
	bizInfo := s.GetBizInfo()
	if bizInfo == nil {
		return 1
	}
	return bizInfo.MaxCarpoolSeatNum
}

func (s *ProductInfoFull) GetUserSelectSeatNum() int32 {
	bizInfo := s.GetBizInfo()
	if bizInfo == nil {
		return 1
	}
	return bizInfo.CarpoolSeatNum
}

func (s *ProductInfoFull) GetTaxiSps() *hestia_charge.TaxiSpsData {
	bizInfo := s.GetBizInfo()
	if bizInfo == nil {
		return nil
	}
	return s.Product.BizInfo.TaxiSps
}

// GetMemberDpaInfo 获取dpa权益信息
func (s *ProductInfoFull) GetMemberDpaInfo() *GoMember.PrivInfo {
	bizInfo := s.GetBizInfo()
	if bizInfo == nil || bizInfo.MemberProfile == nil || bizInfo.MemberProfile.Privileges == nil {
		return nil
	}

	privileges := bizInfo.MemberProfile.Privileges
	if privilegeInfo, ok := privileges.(map[string]*GoMember.PrivInfo); ok {
		if dpa, ok := privilegeInfo["dpa"]; ok {
			return dpa
		}
	}

	return nil
}

// GetLevelID 获取会员等级
func (s *ProductInfoFull) GetLevelID() int32 {
	bizInfo := s.GetBizInfo()
	if bizInfo == nil {
		return 0
	}

	return bizInfo.UserMemberProfile.LevelID
}

func (s *ProductInfoFull) GetUfsFeatures() map[string]string {
	bizInfo := s.GetBizInfo()
	if bizInfo == nil {
		return nil
	}

	return bizInfo.UfsFeatures
}

func (s *ProductInfoFull) GetMiniBusPreMatch() *CarpoolApi.PrematchMiniBusRes {
	if s != nil && s.Product != nil && s.Product.BizInfo != nil && s.Product.BizInfo.MiniBusPreMatch != nil {
		return s.Product.BizInfo.MiniBusPreMatch
	}
	return nil
}

func (s *ProductInfoFull) GetSmartBusPreMatch() *CarpoolApi.PrematchMiniBusRes {
	if s != nil && s.Product != nil && s.Product.BizInfo != nil && s.Product.BizInfo.SmartBusPreMatch != nil {
		return s.Product.BizInfo.SmartBusPreMatch
	}
	return nil
}

func (s *ProductInfoFull) GetIsSkuMode() bool {
	bizInfo := s.GetBizInfo()
	if bizInfo == nil || bizInfo.RouteInfo == nil || bizInfo.RouteInfo.IsSkuModel == nil {
		return false
	}
	if *bizInfo.RouteInfo.IsSkuModel == 1 {
		return true
	} else {
		return false
	}
}

func (s *ProductInfoFull) GetWalkDist() *int32 {
	if s != nil && s.Product != nil && s.Product.BizInfo != nil && s.Product.BizInfo.WalkDist != nil {
		return s.Product.BizInfo.WalkDist
	}
	return nil
}

/* ==================  areaInfo ================ */
//todo
func (s *ProductInfoFull) GetAreaInfo() *models.AreaInfo {
	return &s.BaseReqData.AreaInfo
}

func (s *ProductInfoFull) GetCityID() int {
	area := s.GetAreaInfo()
	return int(area.Area)
}
func (s *ProductInfoFull) GetToCityID() int {
	area := s.GetAreaInfo()
	return int(area.ToArea)
}
func (s *ProductInfoFull) GetFromCounty() int32 {
	area := s.GetAreaInfo()
	return area.FromCounty
}

func (s *ProductInfoFull) GetCountyID() int {
	area := s.GetAreaInfo()
	return int(area.FromCounty)
}

func (s *ProductInfoFull) GetCountyName() string {
	area := s.GetAreaInfo()
	return area.FromCountyName
}

/* ==================  userInfo/passengerInfo ================ */
//todo
func (s *ProductInfoFull) GetUserInfo() *models.PassengerInfo {
	return &s.BaseReqData.PassengerInfo
}

func (s *ProductInfoFull) GetUserPID() int64 {
	user := s.GetUserInfo()
	if user == nil {
		return 0
	}

	return user.PID
}

func (s *ProductInfoFull) GetUID() int64 {
	user := s.GetUserInfo()
	if user == nil {
		return 0
	}

	return user.UID
}

func (s *ProductInfoFull) GetUserPhone() string {
	if s.BaseReqData == nil {
		return ""
	}
	return s.BaseReqData.PassengerInfo.Phone
}

/* ==================  clientInfo/commonInfo ================ */
//todo
func (s *ProductInfoFull) GetClientInfo() *models.ClientInfo {
	return &models.ClientInfo{
		AppVersion:   s.BaseReqData.CommonInfo.AppVersion,
		AccessKeyID:  s.BaseReqData.CommonInfo.AccessKeyID,
		Channel:      s.BaseReqData.CommonInfo.Channel,
		ClientType:   s.BaseReqData.CommonInfo.ClientType,
		Lang:         s.BaseReqData.CommonInfo.Lang,
		PlatformType: s.BaseReqData.CommonInfo.PlatformType,
		TerminalID:   s.BaseReqData.CommonInfo.TerminalID,
		OriginID:     s.BaseReqData.CommonInfo.OriginID,
		Imei:         s.BaseReqData.CommonInfo.Imei,
		PageType:     s.BaseReqData.CommonInfo.PageType,
		MenuID:       s.BaseReqData.CommonInfo.MenuID,
		Xpsid:        s.BaseReqData.CommonInfo.Xpsid,
		XpsidRoot:    s.BaseReqData.CommonInfo.XpsidRoot,
		Dchn:         s.BaseReqData.CommonInfo.Dchn,
		IsDRN:        s.BaseReqData.CommonInfo.IsDRN,
	}
}

func (s *ProductInfoFull) GetAppVersion() string {
	if s.GetClientInfo() == nil {
		return ""
	}
	return s.GetClientInfo().AppVersion
}

func (s *ProductInfoFull) GetAccessKeyId() int32 {
	if s.GetClientInfo() == nil {
		return 0
	}
	return s.GetClientInfo().AccessKeyID
}

func (s *ProductInfoFull) GetMenuId() string {
	return s.BaseReqData.CommonInfo.MenuID
}

func (s *ProductInfoFull) GetLang() string {
	return s.BaseReqData.CommonInfo.Lang
}

func (s *ProductInfoFull) GetUserChoosePayment() int32 {
	if s.BaseReqData == nil {
		return 0
	}
	return s.BaseReqData.CommonInfo.PaymentsType
}

func (s *ProductInfoFull) GetPageType() int32 {
	if s.BaseReqData == nil {
		return 0
	}
	return s.BaseReqData.CommonInfo.PageType
}

/* ==================  commonBizInfo ================ */
func (s *ProductInfoFull) GetMultiRequireProduct() []models.RequireProduct {
	if s.BaseReqData == nil {
		return nil
	}

	return s.BaseReqData.CommonBizInfo.MultiRequireProduct
}

func (s *ProductInfoFull) GetCarpoolSeatNum() int32 {
	if s.BaseReqData == nil {
		return 0
	}
	for _, v := range s.BaseReqData.CommonBizInfo.MultiRequireProduct {
		if s.GetProductCategory() == v.ProductCategory {
			return v.CarpoolSeatNum
		}
	}

	return 0
}

func (s *ProductInfoFull) GetExactSeatNum() int32 {
	if s.BaseReqData == nil {
		return 0
	}

	if s.BaseReqData.CommonBizInfo.CarpoolSeatNum > 0 {
		return s.BaseReqData.CommonBizInfo.CarpoolSeatNum
	}

	return 1
}

func (s *ProductInfoFull) GetPassengerCount() *int32 {
	if s.BaseReqData == nil {
		return nil
	}
	return s.BaseReqData.CommonBizInfo.PassengerCount
}

// GetFastCarPrice 获取快车价格
func (s *ProductInfoFull) GetFastCarPrice() float64 {
	return s.BaseReqData.CommonBizInfo.FastCarPrice
}

// GetFastCarPrice 获取打表计价预估价
func (s *ProductInfoFull) GetPricingByMeterPrice() float64 {
	return s.BaseReqData.CommonBizInfo.PricingByMeterPrice
}

func (s *ProductInfoFull) GetSourceId() int32 {
	if s.BaseReqData == nil {
		return 0
	}

	return s.BaseReqData.CommonBizInfo.SourceId
}

func (s *ProductInfoFull) GetOpenCitySourceId() int32 {
	if s.BaseReqData == nil {
		return 0
	}
	return s.BaseReqData.CommonInfo.SourceID
}

// GetUserOption 获取乘客选择
func (s *ProductInfoFull) GetUserOption() *models.UserOption {
	tmp := s.BaseReqData.RawUserSelectOption
	if tmp == nil {
		return nil
	}
	if rng := s.GetBizInfo().DepartureRange; len(rng) >= 2 {
		tmp.DepartureRange = &models.DepartureRange{
			From: time.Unix(rng[0], 0),
			To:   time.Unix(rng[1], 0),
		}
	}
	return tmp
}

func (s *ProductInfoFull) GetEtp() int64 {
	tmp := s.GetBizInfo()
	if tmp.MiniBusPreMatch == nil || tmp.MiniBusPreMatch.EtpInfo == nil {
		return 100
	}
	return tmp.MiniBusPreMatch.EtpInfo.EtpTimeDuration
}

func (s *ProductInfoFull) GetOverseaEts() int64 {
	tmp := s.GetBizInfo()
	if tmp.OverseaExtraInfo == nil {
		return 0
	}
	return tmp.OverseaExtraInfo.ETS
}

func (s *ProductInfoFull) GetSmartBusEtp() int64 {
	tmp := s.GetBizInfo()
	if tmp.SmartBusPreMatch == nil || tmp.SmartBusPreMatch.EtpInfo == nil {
		return 100
	}
	return tmp.SmartBusPreMatch.EtpInfo.EtpTimeDuration
}

func (s *ProductInfoFull) GetMemberProfile() *midl.V1QueryInfo {
	bizInfo := s.GetBizInfo()
	if bizInfo == nil {
		return nil
	}

	return bizInfo.UserMemberProfile.MemberProfile
}

func (s *ProductInfoFull) GetComboID() int64 {
	if s.GetBizInfo() == nil {
		return 0
	}

	return s.GetBizInfo().ComboID
}

func (s *ProductInfoFull) GetIsUserUseDpa() bool {
	if s.GetBizInfo() == nil {
		return false
	}

	return s.GetBizInfo().IsUserUseDpa
}

func (s *ProductInfoFull) GetSendOrderMultiRequiredProduct() map[string]dos.RequiredProductStruct {
	if s.BaseReqData == nil {
		return nil
	}

	return s.BaseReqData.SendOrder.MultiRequiredProduct

}

func (s *ProductInfoFull) IsHongKong() bool {
	return consts.HKCityId == s.GetCityID() && consts.HKCityId == s.GetToCityID()
}

func (s *ProductInfoFull) GetUserSelectPayType() int32 {
	if s.BaseReqData == nil {
		return 0
	}
	return s.BaseReqData.CommonInfo.PaymentsType
}

func (s *ProductInfoFull) IsHitDynamicIconAb(ctx context.Context) bool {
	pid, param := s.GetApolloParams(WithPIDKey, WithPageType, WithToCity, WithFontScaleType)
	return ab.IsHitDynamicIconAb(ctx, pid, param)
}

func (s *ProductInfoFull) IsHitNormalNoAnswerCompensation(ctx context.Context) bool {
	v := knife.Get(ctx, HitNormalNoAnswerCompensation)
	if v != nil {
		return v.(bool)
	}

	return false
}

func (s *ProductInfoFull) GetSpsFeeMap() map[int64]*Sps.FeeItem {
	return s.GetBizInfo().SpsFeeMap
}

func (s *ProductInfoFull) GetPcId2CustomFeature() map[int64][]dos.CustomFeatureStruct {
	return s.GetCommonBizInfo().PcId2CustomFeature
}

func (s *ProductInfoFull) GetDepartureTime() int64 {
	return s.BaseReqData.CommonInfo.DepartureTime
}

func (s *ProductInfoFull) GetServiceList() []*hundun.PcServiceData {
	return s.GetBizInfo().CustomFeatureList
}

func (s *ProductInfoFull) GetAthenaRecommend() int32 {
	if s.GetProductCheckStatus() == consts.Checked {
		return consts.Checked
	}
	return consts.UnChecked
}

func (s *ProductInfoFull) GetCategoryId(ctx context.Context, subGroupId, pcId int64) int32 {
	recommendCategory := s.GetCommonBizInfo().AthenaResult.GroupId2CategoryId
	if v, ok := recommendCategory[group_id.BuildGroupIdById(subGroupId, pcId)]; ok {
		return v
	}

	// 记录日志
	logMap := map[string]interface{}{
		"product_category": pcId,
	}
	log.Public.Public(ctx, "get_category_id_by_pcid_doudi", logMap)

	// 兜底"立即出发"分组
	return category_id.FastCategoryID
}

type AssembleResp struct {
	EstimateData    *proto.NewFormEstimateData
	RawEstimateData *proto.RawEstimateData
}
