package mq

// func (msg Message) MarshalJSON() ([]byte, error) {
// 	type taowa Message // 这一行很关键
// 	return json.Marshal(taowa(msg))
// }

type Trace struct {
	TraceID     string `json:"traceId"`
	SpanID      string `json:"spanId"`
	HintCode    string `json:"hintCode"`
	HintContent string `json:"hintContent"`
}

type ScenePrice struct {
	EstimateFee      float64 `json:"estimate_fee"`
	IsCarpoolSuccess bool    `json:"is_carpool_success"`
}

type Data struct {
	Biztype               int           `json:"biztype"`
	Role                  int           `json:"role"`
	UID                   int64         `json:"uid"`
	Gpid                  string        `json:"gpid"`
	PassengerPhone        string        `json:"passenger_phone"`
	District              string        `json:"district"`
	Area                  int           `json:"area"`
	Channel               int64         `json:"channel"`
	StartingLng           float64       `json:"starting_lng"`
	StartingLat           float64       `json:"starting_lat"`
	County                string        `json:"county"`
	DestLng               float64       `json:"dest_lng"`
	DestLat               float64       `json:"dest_lat"`
	FromPoiID             string        `json:"from_poi_id"`
	ToPoiID               string        `json:"to_poi_id"`
	CreateTime            string        `json:"create_time"`
	ProductID             int           `json:"product_id"`
	CarType               string        `json:"car_type"`
	SceneType             int           `json:"scene_type"`
	IsAnycar              int           `json:"is_anycar"`
	MultiRequireProduct   string        `json:"multi_require_product"`
	PreferenceProduct     string        `json:"preference_product"`
	NTuple                string        `json:"n_tuple"`
	OffPeakPushTimestamp  int           `json:"off_peak_push_timestamp"`
	CurrentLng            float64       `json:"current_lng"`
	CurrentLat            float64       `json:"current_lat"`
	ClientType            int           `json:"client_type"`
	PlatformType          int           `json:"platform_type"`
	EstimateDistanceMetre int           `json:"estimate_distance_metre"`
	EstimateTimeMinutes   int           `json:"estimate_time_minutes"`
	EstimateFee           float64       `json:"estimate_fee"`
	StartingName          string        `json:"starting_name"`
	DestName              string        `json:"dest_name"`
	DepartureTime         int64         `json:"departure_time"`
	IsFastCar             int           `json:"is_fast_car"`
	OType                 int           `json:"oType"`
	AppVersion            string        `json:"app_version"`
	OriginID              int           `json:"origin_id"`
	BasicTotalFee         float64       `json:"basic_total_fee"`
	FinalCouponValue      int           `json:"final_coupon_value"`
	PayType               int           `json:"pay_type"`
	DynamicTotalFee       float64       `json:"dynamic_total_fee"`
	CapPrice              float64       `json:"cap_price"`
	DynamicDiffPrice      int           `json:"dynamic_diff_price"`
	DynamicInfo           string        `json:"dynamic_info"`
	SelectType            int           `json:"select_type"`
	RecommendType         int           `json:"recommend_type"`
	FormShowType          int           `json:"form_show_type"`
	MenuID                string        `json:"menu_id"`
	MapRequest            string        `json:"map_request"`
	EstimateType          string        `json:"estimate_type"`
	BubbleID              string        `json:"bubble_id"`
	EstimateID            string        `json:"estimate_id"`
	ComboType             int           `json:"combo_type"`
	ProductCategory       int           `json:"product_category"`
	MapType               string        `json:"map_type"`
	PassengerNumber       int           `json:"passenger_number"`
	DepartureRange        []int64       `json:"departure_range"`
	ScenePrice            []*ScenePrice `json:"scene_price"`
}

// Message 预估MQ消息
//
// 每个controller可以有自己的MQ, 通过Hook的方式注册到 Genrator 上
//
// 虽然下面的消息体和预估的MQ恰好一致, 但是目前(2022-01-25)Genrator还不完善, 只能暂时放这里
type Message struct {
	No      int    `json:"no"`
	Type    int    `json:"type"`
	Ct      int64  `json:"ct"`
	MapType string `json:"map_type"`
	Data    *Data  `json:"data"`
	Trace   *Trace `json:"trace"`
}
