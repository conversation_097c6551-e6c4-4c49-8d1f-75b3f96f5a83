package biz_runtime

import (
	"testing"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

// SetExternalEid 是一个测试辅助函数，用于设置 ProductInfoFull 中的 ExternalEid
func SetExternalEid(p *ProductInfoFull, externalEid string) {
	if p == nil {
		return
	}
	p.hu = &PriceApi.EstimateNewFormData{
		ExternalEid: externalEid,
	}
}

func TestProductInfoFull_GetFontScaleType(t *testing.T) {
	type fields struct {
		BaseReqData   *models.BaseReqData
		Product       *models.Product
		BillDetail    *PriceApi.BillDetail
		DiscountInfo  []*PriceApi.DiscountInfo
		PayInfo       *PriceApi.PaymentInfo
		ExtraInfo     map[string]string
		hu            *PriceApi.EstimateNewFormData
		selectorInput map[string]interface{}
		_ap           *apollo_model.ParamsConnector
	}
	tests := []struct {
		name   string
		fields fields
		want   int32
	}{
		// TODO: Add test cases.
		{
			name: "a",
			fields: fields{
				BaseReqData: &models.BaseReqData{
					CommonInfo: models.CommonInfo{
						FontScaleType: 1,
					},
				},
			},
			want: 1,
		},
		{
			name:   "b",
			fields: fields{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &ProductInfoFull{
				BaseReqData:   tt.fields.BaseReqData,
				Product:       tt.fields.Product,
				BillDetail:    tt.fields.BillDetail,
				DiscountInfo:  tt.fields.DiscountInfo,
				PayInfo:       tt.fields.PayInfo,
				ExtraInfo:     tt.fields.ExtraInfo,
				hu:            tt.fields.hu,
				selectorInput: tt.fields.selectorInput,
				_ap:           tt.fields._ap,
			}
			if got := a.GetFontScaleType(); got != tt.want {
				t.Errorf("ProductInfoFull.GetFontScaleType() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProductInfoFull_GetCashBack(t *testing.T) {
	tests := []struct {
		name string
		hu   *PriceApi.EstimateNewFormData
		want *PriceApi.CashbackXinZhuInfo
	}{
		{
			name: "正常场景-有返现信息",
			hu: &PriceApi.EstimateNewFormData{
				DiscountSet: &PriceApi.EstimateNewFormDiscountSet{
					CashbackXinZhuInfo: &PriceApi.CashbackXinZhuInfo{
						CashbackAmount: 100,
					},
				},
			},
			want: &PriceApi.CashbackXinZhuInfo{
				CashbackAmount: 100,
			},
		},
		{
			name: "hu为nil",
			hu:   nil,
			want: nil,
		},
		{
			name: "DiscountSet为nil",
			hu: &PriceApi.EstimateNewFormData{
				DiscountSet: nil,
			},
			want: nil,
		},
		{
			name: "CashbackXinZhuInfo为nil",
			hu: &PriceApi.EstimateNewFormData{
				DiscountSet: &PriceApi.EstimateNewFormDiscountSet{
					CashbackXinZhuInfo: nil,
				},
			},
			want: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &ProductInfoFull{
				hu: tt.hu,
			}
			got := p.GetCashBack()
			if got != nil && tt.want != nil && got.CashbackAmount != tt.want.CashbackAmount {
				t.Errorf("ProductInfoFull.GetCashBack() = %v, want %v", got, tt.want)
			}
		})
	}
}
