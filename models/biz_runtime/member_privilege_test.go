package biz_runtime

import (
	GoMember "git.xiaojukeji.com/dirpc/dirpc-go-http-GoMember"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestBuildProductBizInfo_buildMemberPrivilegeList(t *testing.T) {
	baseRPC := NewMemberPrivilegeRPC(&models.BaseReqData{}, nil)
	product := models.Product{ProductCategory: 1}
	privateBizInfo := &models.PrivateBizInfo{}
	var withoutPrivilege int32
	// 测试用例1: 当服务 & WithoutPrivilege=1时，不设置
	t.Run("WithoutPrivilege=1", func(t *testing.T) {
		mockey.PatchConvey("test without privilege case", t, func() {
			memberInfo := &GoMember.ProductListRespV2{
				Privileges: map[string]*GoMember.PrivOptionsItem{
					"airport_pick_up": &GoMember.PrivOptionsItem{},
				},
			}
			withoutPrivilege = 1
			baseRPC.BaseReqData.CommonBizInfo.PcId2CustomFeature = map[int64][]dos.CustomFeatureStruct{
				1: {
					{
						Id:               consts.DriverPickUpService,
						WithoutPrivilege: &withoutPrivilege,
					},
				},
			}

			// 执行测试
			baseRPC.buildMemberPrivilegeList(product, privateBizInfo, memberInfo)
			// 验证结果
			assert.Empty(t, privateBizInfo.MemberPrivilegeList, "MemberPrivilegeList should be empty")
		})
	})

	// 测试用例2: 当有机场接机权益，服务 & WithoutPrivilege=0时，设置对应标志
	t.Run("WithoutPrivilege=0", func(t *testing.T) {
		mockey.PatchConvey("test with airport pick up privilege", t, func() {
			memberInfo := &GoMember.ProductListRespV2{
				Privileges: map[string]*GoMember.PrivOptionsItem{
					"airport_pick_up": &GoMember.PrivOptionsItem{},
				},
			}
			withoutPrivilege = 0
			baseRPC.BaseReqData.CommonBizInfo.PcId2CustomFeature = map[int64][]dos.CustomFeatureStruct{
				1: {
					{
						Id:               consts.DriverPickUpService,
						WithoutPrivilege: &withoutPrivilege,
					},
				},
			}
			// 执行测试
			baseRPC.buildMemberPrivilegeList(product, privateBizInfo, memberInfo)
			// 验证结果
			assert.Equal(t, 1, privateBizInfo.MemberPrivilegeList["airport_pick_up"], "airport_pick_up should be set to 1")
		})
	})
}
