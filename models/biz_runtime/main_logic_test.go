package biz_runtime

import (
	"testing"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/stretchr/testify/assert"
)

// 生成Mock数据的辅助函数
func generateMockBaseReqData(countryIsoCode *string) *models.BaseReqData {
	return &models.BaseReqData{
		AreaInfo: models.AreaInfo{
			CountryIsoCode: countryIsoCode,
		},
	}
}

func TestProductsGenerator_GetCountryIsoCode(t *testing.T) {

	t.Run("成功场景-正常获取国家ISO代码", func(t *testing.T) {
		// 准备测试数据
		countryCode := "US"
		baseReqData := generateMockBaseReqData(&countryCode)
		pg := &ProductsGenerator{
			BaseReqData: baseReqData,
		}

		// 执行测试
		result := pg.GetCountryIsoCode()

		// 断言结果
		assert.Equal(t, "US", result, "应该正确返回国家ISO代码")
	})

	t.Run("成功场景-国家ISO代码为空字符串", func(t *testing.T) {
		// 准备测试数据
		countryCode := ""
		baseReqData := generateMockBaseReqData(&countryCode)
		pg := &ProductsGenerator{
			BaseReqData: baseReqData,
		}

		// 执行测试
		result := pg.GetCountryIsoCode()

		// 断言结果
		assert.Equal(t, "", result, "应该返回空字符串")
	})

	t.Run("边界场景-BaseReqData为nil", func(t *testing.T) {
		// 准备测试数据
		pg := &ProductsGenerator{
			BaseReqData: nil,
		}

		// 执行测试
		result := pg.GetCountryIsoCode()

		// 断言结果
		assert.Equal(t, "", result, "当BaseReqData为nil时应该返回空字符串")
	})

	t.Run("边界场景-CountryIsoCode为nil", func(t *testing.T) {
		// 准备测试数据
		baseReqData := generateMockBaseReqData(nil)
		pg := &ProductsGenerator{
			BaseReqData: baseReqData,
		}

		// 执行测试
		result := pg.GetCountryIsoCode()

		// 断言结果
		assert.Equal(t, "", result, "当CountryIsoCode为nil时应该返回空字符串")
	})

	t.Run("边界场景-完整的BaseReqData但CountryIsoCode为nil", func(t *testing.T) {
		// 准备测试数据
		baseReqData := &models.BaseReqData{
			AreaInfo: models.AreaInfo{
				City:     1001,
				Area:     1001,
				ToArea:   1002,
				District: "北京",
			},
			CommonInfo: models.CommonInfo{
				AppVersion: "1.0.0",
			},
		}
		pg := &ProductsGenerator{
			BaseReqData: baseReqData,
		}

		// 执行测试
		result := pg.GetCountryIsoCode()

		// 断言结果
		assert.Equal(t, "", result, "当CountryIsoCode为nil时应该返回空字符串")
	})

	t.Run("成功场景-不同国家代码", func(t *testing.T) {
		// 准备测试数据
		testCases := []string{"CN", "HK", "US", "JP", "KR", "SG"}

		for _, countryCode := range testCases {
			t.Run("国家代码_"+countryCode, func(t *testing.T) {
				baseReqData := generateMockBaseReqData(&countryCode)
				pg := &ProductsGenerator{
					BaseReqData: baseReqData,
				}

				// 执行测试
				result := pg.GetCountryIsoCode()

				// 断言结果
				assert.Equal(t, countryCode, result, "应该正确返回国家ISO代码: %s", countryCode)
			})
		}
	})
}
