package biz_runtime

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/s3e/common-lib/v2/component/diff"
	"github.com/spf13/cast"
	"math/rand"
	"strconv"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	context2 "git.xiaojukeji.com/lego/context-go"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/ddmq"
)

type EstimateReqData struct {
	TimeStamp              int64           `json:"timeStamp"`
	EstimateTraceId        string          `json:"estimate_trace_id"`
	MainEstimateTraceId    string          `json:"main_estimate_trace_id"`
	Area                   int32           `json:"area"`            // 区域id
	Role                   int32           `json:"role"`            // 角色，１＝司机，２＝乘客，３＝订单(消息中该值为2)
	PassengerPhone         string          `json:"passenger_phone"` // 乘客手机号
	PassengerId            string          `json:"passenger_id"`
	Gpid                   string          `json:"gpid"`         // 库中乘客id
	Uid                    int64           `json:"uid"`          // 乘客uid
	County                 string          `json:"county"`       // 起始区县ID
	District               string          `json:"district"`     // 地区
	ClientType             int32           `json:"client_type"`  // 端类型
	FromPoiId              string          `json:"from_poi_id"`  // 起始poiId
	StartingLng            float64         `json:"starting_lng"` // 起始经度
	StartingLat            float64         `json:"starting_lat"` // 起始纬度
	CurrentLat             float64         `json:"current_lat"`
	CurrentLng             float64         `json:"current_lng"`
	ToPoiId                string          `json:"to_poi_id"`              // 目的地poiId
	DestLng                float64         `json:"dest_lng"`               // 起始经度
	DestLat                float64         `json:"dest_lat"`               // 起始纬度
	MapType                string          `json:"map_type"`               // 地图类型
	StartingName           string          `json:"starting_name"`          // 起点名称
	DestName               string          `json:"dest_name"`              // 终点名称
	FromPoiType            string          `json:"from_poi_type"`          // 起点的点位来源标签
	ToPoiType              string          `json:"to_poi_type"`            // 终点的点位来源标签
	StopoverPoints         string          `json:"stopover_points"`        // 途经点信息json
	AppVersion             string          `json:"app_version"`            // 版本信息
	AccessKeyId            int32           `json:"access_key_id"`          // 终端来源标识
	Channel                string          `json:"channel"`                // channel
	MenuId                 string          `json:"menu_id"`                // 顶导id
	HasDualPriceCarpool    int8            `json:"has_dual_price_carpool"` // 是否包含两口价拼车V3
	HasInterCityCarpool    int8            `json:"has_inter_city_carpool"` // 是否包含城际拼车
	ProductList            []SimpleProduct `json:"product_list"`
	PageType               int32           `json:"page_type"`
	Lang                   string          `json:"lang"`
	OrderType              int32           `json:"order_type"`
	DepartureTime          int64           `json:"departure_time"`
	UserType               int32           `json:"user_type"`
	PayType                int32           `json:"pay_type"`
	Source                 string          `json:"_source"`
	ToCity                 int32           `json:"to_city"`
	CallCar                int32           `json:"call_car"`
	ExtPid                 string          `json:"ext_pid"`
	ChooseFSearchId        string          `json:"choose_f_searchid"`
	ChooseTSearchId        string          `json:"choose_t_searchid"`
	TabId                  string          `json:"tab_id"`
	OrderId                int64           `json:"order_id"`
	IsQueue                bool            `json:"is_queue"`
	AthenaRpcTriggerSwitch int32           `json:"athena_rpc_trigger_switch"`
}

type SimpleProduct struct {
	EstimateId         string `json:"estimate_id"`
	ProductId          int64  `json:"product_id"`
	BusinessId         int64  `json:"business_id"`
	RequireLevel       int64  `json:"require_level"`
	ComboType          int64  `json:"combo_type"`
	CarpoolType        int64  `json:"carpool_type"`
	IsSpecialPrice     bool   `json:"is_special_price"`
	LevelType          int32  `json:"level_type"`
	CarpoolPriceType   int32  `json:"carpool_price_type"`
	IsDualCarpoolPrice bool   `json:"is_dual_carpool_price"`
	DepartureTime      string `json:"departure_time"`
	RouteType          int64  `json:"route_type"`
	ProductCategory    int64  `json:"product_category"`
	DepartureRange     string `json:"departure_range"`
	MemberLevel        int32  `json:"member_level"`
}

func SendEstimateReqKafka(ctx context.Context, req *models.BaseReqData, products []*models.Product, reqFrom string) {
	if req == nil || len(products) <= 0 {
		return
	}
	var hasDualPriceCarpool, hasIntercityCarpool int8
	departureTime := time.Unix(req.CommonInfo.DepartureTime, 0).Format("2006-01-02 15:04:05 -0700 MST")
	simpleProducts := make([]SimpleProduct, 0)
	for _, product := range products {
		sp := SimpleProduct{
			EstimateId:         product.EstimateID,
			ProductId:          product.ProductID,
			BusinessId:         product.BusinessID,
			RequireLevel:       product.RequireLevelInt,
			ComboType:          product.ComboType,
			CarpoolType:        product.CarpoolType,
			IsSpecialPrice:     product.IsSpecialPrice,
			LevelType:          product.LevelType,
			CarpoolPriceType:   product.CarpoolPriceType,
			IsDualCarpoolPrice: product.IsDualCarpoolPrice,
			DepartureTime:      departureTime,
			RouteType:          product.RouteType,
			ProductCategory:    product.ProductCategory,
			MemberLevel:        product.BizInfo.LevelID,
		}
		if carpool.IsPinCheChe(product.ProductCategory) && product.BizInfo != nil && len(product.BizInfo.DepartureRange) == 2 {
			departureRange, _ := json.Marshal(product.BizInfo.DepartureRange)
			sp.DepartureRange = string(departureRange)
		}
		simpleProducts = append(simpleProducts, sp)
		if carpool.IsCarpoolDualPriceV3(int32(product.CarpoolType), product.CarpoolPriceType, product.IsDualCarpoolPrice) {
			hasDualPriceCarpool = 1
		}

		if carpool.IsInterCityCarpool(product.CarpoolType, product.ComboType) {
			hasIntercityCarpool = 1
		}
	}

	var params = &EstimateReqData{
		// 公共数据
		TimeStamp:           time.Now().Unix(),
		EstimateTraceId:     context2.GetTrace(ctx).GetTraceId(),
		MainEstimateTraceId: req.CommonInfo.MainEstimateTraceId,
		ClientType:          req.CommonInfo.ClientType,
		AppVersion:          req.CommonInfo.AppVersion,
		AccessKeyId:         req.CommonInfo.AccessKeyID,
		Channel:             cast.ToString(req.CommonInfo.Channel),
		MenuId:              req.CommonInfo.MenuID,
		PageType:            req.CommonInfo.PageType,
		OrderType:           req.CommonInfo.OrderType,
		Lang:                req.CommonInfo.Lang,
		DepartureTime:       req.CommonInfo.DepartureTime,
		PayType:             req.CommonInfo.PaymentsType,
		TabId:               req.CommonInfo.TabId,

		// 位置信息
		Area:            req.AreaInfo.Area,
		County:          strconv.FormatInt(int64(req.AreaInfo.FromCounty), 10),
		District:        req.AreaInfo.District,
		FromPoiId:       req.AreaInfo.FromPoiID,
		StartingLng:     req.AreaInfo.FromLng,
		StartingLat:     req.AreaInfo.FromLat,
		ToPoiId:         req.AreaInfo.ToPoiID,
		DestLng:         req.AreaInfo.ToLng,
		DestLat:         req.AreaInfo.ToLat,
		MapType:         req.AreaInfo.MapType,
		StartingName:    req.AreaInfo.FromName,
		DestName:        req.AreaInfo.DestName,
		FromPoiType:     req.AreaInfo.FromPoiType,
		ToPoiType:       req.AreaInfo.ToPoiType,
		CurrentLat:      req.AreaInfo.CurLat,
		CurrentLng:      req.AreaInfo.CurLng,
		ToCity:          req.AreaInfo.ToArea,
		ChooseFSearchId: req.AreaInfo.ChooseFSearchid,
		ChooseTSearchId: req.AreaInfo.ChooseTSearchid,

		// 用户信息
		Role:           ddmq.PassengerRole,
		PassengerPhone: req.PassengerInfo.Phone,
		PassengerId:    strconv.FormatInt(req.PassengerInfo.PID, 10),
		Uid:            req.PassengerInfo.UID,
		Gpid:           strconv.FormatInt(req.PassengerInfo.PID, 10),
		ExtPid:         req.PassengerInfo.ExtPid,
		UserType:       req.PassengerInfo.UserType,

		HasDualPriceCarpool: hasDualPriceCarpool,
		HasInterCityCarpool: hasIntercityCarpool,

		ProductList: simpleProducts,
		CallCar:     req.CommonInfo.CallCarType,

		// 订单信息
		OrderId:                req.SendOrder.OrderId,
		IsQueue:                req.SendOrder.IsQueue,
		AthenaRpcTriggerSwitch: 1,
	}

	if req.CommonInfo.StopoverPoints != nil {
		params.StopoverPoints = *req.CommonInfo.StopoverPoints
	}
	if req.CommonInfo.PageType == page_type.PageTypeGuideAnyCar {
		params.Source = "anycar"
	} else {
		params.Source = "v3"
	}

	if reqFrom == consts.FromTypeCombinedTravel {
		params.Source = "combined_travel"
	}

	hashKey := rand.Int63n(16383)
	if diff.CheckDiffStatus(ctx) {
		diff.CheckDiffAndLog(ctx, diff.DownStreamDirpcType, "sendSync/"+ddmq.TopicWanliuMultiEstimate, params)
		return
	}

	err := ddmq.Send(ctx, ddmq.TopicWanliuMultiEstimate, params, hashKey, ddmq.TopicTypeWanliuMultiEstimate)
	if err != nil {
		log.Trace.Warnf(ctx, consts.TagErrDDmq, "send req kafka msg fail with %v", err)
		return
	}
}
