package biz_runtime

import (
	"context"
	"reflect"
	"runtime"
	"runtime/debug"
	"sync/atomic"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
)

// BeforePriceTimeout Price之前的rpc超时时间
const BeforePriceTimeout = 300

// BeforePrice2Timeout Price之前的rpc超时时间
const BeforePrice2Timeout = 300

// ConcurrentPriceTimeout Price当中的rpc超时时间
const ConcurrentPriceTimeout = 650

// AfterPriceTimeout Price之后的rpc超时时间
const AfterPriceTimeout = 250

type AfterDdsProductsFilter interface {
	// 传入当前dds返回的product 列表，返回需要被过滤的pcId list
	AfterDdsFilter(ctx context.Context, products []*models.Product) (removeProductList []models.ProductCategory, reason string)
}

type AfterDdsProductsFission interface {
	// 传入当前dds返回的product 列表，返回需要被新增的pcId list
	Do(ctx context.Context, products []*models.Product) []*models.Product
}
type BeforePriceProductsFilter interface {
	// Exec 传入 dds响应过滤后 的品类列表，返回需要被过滤的pcId list
	Exec(ctx context.Context, products []*models.Product) []models.ProductCategory
}

type AfterPriceProductsFilter interface {
	// 传入完整的有价格/优惠/产品信息 的product列表，返回需要被过滤的pcId list
	Do(ctx context.Context, products []*ProductInfoFull) []models.ProductCategory
}

// FinalProductsFilterV2 串行过滤 (依赖顺序)
type FinalProductsFilterV2 interface {
	HandlerFilter(ctx context.Context, productMap map[int64]*ProductInfoFull) []models.ProductCategory
}

type RpcProcessWithBaseProducts interface {
	Fetch(ctx context.Context, products []*models.Product) bool                                   // rpc获取数据
	BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo)                           // 干预公共参数
	BuildProductBizInfo(ctx context.Context, product models.Product, info *models.PrivateBizInfo) // 干预各品类数据
	GetErrorInfo(ctx context.Context) error                                                       // err不为空，强阻断后续预估流程
}

type RpcProcessWithNoBaseProducts interface {
	Fetch(ctx context.Context) bool                                     // rpc获取数据
	BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo) // 干预公共参数
}

type RpcProcessWithFullProducts interface {
	Fetch(ctx context.Context, products []*ProductInfoFull) bool
	BuildCommonBizInfo(ctx context.Context, info *models.CommonBizInfo)                            // 干预公共参数
	BuildProductBizInfo(ctx context.Context, product ProductInfoFull, info *models.PrivateBizInfo) // 干预各品类数据
}

type OptionProcessProduct interface {
	// ExecProducts 传入 参数 获取个性化品类列表
	ExecProducts(ctx context.Context, brd *models.BaseReqData) []*models.Product
}

type OptionProcessPrice interface {
	ExecPrice(ctx context.Context, brd *models.BaseReqData, products []*models.Product) ([]*ProductInfoFull, error)
}

// 批量执行price-api前的RPC
func (pg *ProductsGenerator) multiExecBeforePriceSyncRpc(ctx context.Context, products []*models.Product) error {
	if len(pg._rpcProcessBeforePrice) <= 0 || pg.BaseReqData == nil {
		return nil
	}
	timeout := BeforePriceTimeout
	if pg.BaseReqData.CommonBizInfo.CustomTimeOutConf["rpcProcessBeforePrice"] != 0 {
		timeout = pg.BaseReqData.CommonBizInfo.CustomTimeOutConf["rpcProcessBeforePrice"]
	}
	finishedRpc, failedRpc := multiExec(ctx, products, pg._rpcProcessBeforePrice, time.Duration(timeout))
	for _, rpc := range finishedRpc {
		rpc.BuildCommonBizInfo(ctx, &pg.BaseReqData.CommonBizInfo)
		for _, product := range products {
			if product == nil {
				continue
			}

			if product.BizInfo == nil {
				product.BizInfo = &models.PrivateBizInfo{}
			}

			rpc.BuildProductBizInfo(ctx, *product, product.BizInfo)
		}
	}
	for _, rpc := range failedRpc {
		if err := rpc.GetErrorInfo(ctx); err != nil {
			return err
		}
	}
	return nil
}

// multiExecBeforePriceSyncRpc2 批量执行price-api前的RPC
func (pg *ProductsGenerator) multiExecBeforePriceSyncRpc2(ctx context.Context, products []*models.Product) error {
	if len(pg._rpcProcessBeforePrice2) <= 0 || pg.BaseReqData == nil {
		return nil
	}

	finishedRpc, failedRpc := multiExec(ctx, products, pg._rpcProcessBeforePrice2, BeforePrice2Timeout)
	for _, rpc := range finishedRpc {
		rpc.BuildCommonBizInfo(ctx, &pg.BaseReqData.CommonBizInfo)
		for _, product := range products {
			if product == nil {
				continue
			}

			if product.BizInfo == nil {
				product.BizInfo = &models.PrivateBizInfo{}
			}

			rpc.BuildProductBizInfo(ctx, *product, product.BizInfo)
		}
	}

	for _, rpc := range failedRpc {
		if err := rpc.GetErrorInfo(ctx); err != nil {
			return err
		}
	}
	return nil
}

func (pg *ProductsGenerator) multiExecConcurrentRpc(ctx context.Context, products []*models.Product, done func()) error {
	defer done()

	if len(pg._rpcProcessConcurrentPrice) <= 0 || pg.BaseReqData == nil {
		return nil
	}

	finishedRpc, failedRpc := multiExec(ctx, products, pg._rpcProcessConcurrentPrice, ConcurrentPriceTimeout)
	for _, rpc := range finishedRpc {
		rpc.BuildCommonBizInfo(ctx, &pg.BaseReqData.CommonBizInfo)
		for _, product := range products {
			if product == nil {
				continue
			}

			if product.BizInfo == nil {
				product.BizInfo = &models.PrivateBizInfo{}
			}

			rpc.BuildProductBizInfo(ctx, *product, product.BizInfo)
		}
	}
	for _, rpc := range failedRpc {
		if err := rpc.GetErrorInfo(ctx); err != nil {
			return err
		}
	}
	return nil
}

func (pg *ProductsGenerator) multiExecAfterDdsRpc(ctx context.Context) {
	if len(pg._rpcProcessAfterDdsRpc) <= 0 || pg.BaseReqData == nil {
		return
	}
	finishedRpc := multiExecNoProducts(ctx, pg._rpcProcessAfterDdsRpc, BeforePriceTimeout)
	for _, rpc := range finishedRpc {
		rpc.BuildCommonBizInfo(ctx, &pg.BaseReqData.CommonBizInfo)
	}
}

func (pg *ProductsGenerator) multiExecAfterPriceRpc(ctx context.Context, products []*ProductInfoFull) {

	if len(pg._rpcProcessAfterPrice) <= 0 || pg.BaseReqData == nil || len(products) == 0 {
		return
	}

	finishedRpc := multiExecWithFull(ctx, products, pg._rpcProcessAfterPrice, AfterPriceTimeout)
	for _, rpc := range finishedRpc {
		rpc.BuildCommonBizInfo(ctx, &pg.BaseReqData.CommonBizInfo)
		for _, productFull := range products {
			if productFull == nil || productFull.Product == nil {
				continue
			}

			if productFull.Product.BizInfo == nil {
				productFull.Product.BizInfo = &models.PrivateBizInfo{}
			}

			rpc.BuildProductBizInfo(ctx, *productFull, productFull.Product.BizInfo)
		}
	}
}

func multiExecWithFull(ctx context.Context, products []*ProductInfoFull, rpcList []RpcProcessWithFullProducts, timeout time.Duration) []RpcProcessWithFullProducts {
	var (
		isTimeout int32 = 0
		// 成功和失败的rpc channel
		finishedChan = make(chan RpcProcessWithFullProducts, len(rpcList))
		failedChan   = make(chan struct{}, len(rpcList))
		// 成功和失败的rpc 的收集器
		finishedCollector = make([]RpcProcessWithFullProducts, 0)
		failedCollector   = make([]struct{}, 0)
	)

	t := time.NewTimer(timeout * time.Millisecond)
	for _, rpc := range rpcList {
		go func(ctx context.Context, products []*ProductInfoFull, rpc RpcProcessWithFullProducts) {
			defer func() {
				if err := recover(); err != nil {
					stack := make([]byte, 8192)
					stack = stack[:runtime.Stack(stack, false)]
					log.Trace.Warnf(ctx, consts.TagErrGoRoutine, "multi_call panic:%v\n%s", err, stack)
				}
			}()

			rst := rpc.Fetch(ctx, products)
			if 0 == atomic.LoadInt32(&isTimeout) && rst {
				finishedChan <- rpc
			} else {
				failedChan <- struct{}{}
				if 0 != atomic.LoadInt32(&isTimeout) {
					rpcName := reflect.TypeOf(rpc).Elem().Name()
					log.Trace.Warnf(ctx, consts.TagErrGoRoutine, "rpc process with rst and timeout||rpcName=%v||rst=%v||isTimeout=%d", rpcName, rst, isTimeout)
				}
			}
		}(ctx, products, rpc)
	}

	for {
		select {
		case rpc := <-finishedChan:
			finishedCollector = append(finishedCollector, rpc)
		case rpc := <-failedChan:
			failedCollector = append(failedCollector, rpc)
		case <-t.C:
			atomic.StoreInt32(&isTimeout, 1)
		}
		if len(finishedCollector)+len(failedCollector) == len(rpcList) || 1 == atomic.LoadInt32(&isTimeout) {
			break
		}
	}

	return finishedCollector
}

func multiExec(ctx context.Context, products []*models.Product, rpcList []RpcProcessWithBaseProducts, timeout time.Duration) ([]RpcProcessWithBaseProducts, []RpcProcessWithBaseProducts) {
	var (
		isTimeout int32 = 0
		// 成功和失败的rpc channel
		finishedChan = make(chan RpcProcessWithBaseProducts, len(rpcList))
		failedChan   = make(chan RpcProcessWithBaseProducts, len(rpcList))
		// 成功和失败的rpc 的收集器
		finishedCollector = make([]RpcProcessWithBaseProducts, 0)
		failedCollector   = make([]RpcProcessWithBaseProducts, 0)
	)

	t := time.NewTimer(timeout * time.Millisecond)
	for _, rpc := range rpcList {
		go func(ctx context.Context, products []*models.Product, rpc RpcProcessWithBaseProducts) {
			defer func() {
				if err := recover(); err != nil {
					log.Trace.Errorf(ctx, consts.TagErrGoRoutine, "panic in rpcProcess %v with %v \n stack: %s", rpc, err, debug.Stack())
				}
			}()

			rst := rpc.Fetch(ctx, products)
			if 0 == atomic.LoadInt32(&isTimeout) && rst {
				finishedChan <- rpc
			} else {
				failedChan <- rpc
				if 0 != atomic.LoadInt32(&isTimeout) {
					rpcName := reflect.TypeOf(rpc).Elem().Name()
					log.Trace.Warnf(ctx, consts.TagErrGoRoutine, "rpc process with rst and timeout: %v||%v||%d", rpcName, rst, isTimeout)
				}
			}
		}(ctx, products, rpc)
	}

	for {
		select {
		case rpc := <-finishedChan:
			finishedCollector = append(finishedCollector, rpc)
		case rpc := <-failedChan:
			failedCollector = append(failedCollector, rpc)
		case <-t.C:
			atomic.StoreInt32(&isTimeout, 1)
		}
		if len(finishedCollector)+len(failedCollector) == len(rpcList) || 1 == atomic.LoadInt32(&isTimeout) {
			break
		}
	}

	return finishedCollector, failedCollector
}

func multiExecNoProducts(ctx context.Context, rpcList []RpcProcessWithNoBaseProducts, timeout time.Duration) []RpcProcessWithNoBaseProducts {
	var (
		isTimeout int32 = 0
		// 成功和失败的rpc channel
		finishedChan = make(chan RpcProcessWithNoBaseProducts, len(rpcList))
		failedChan   = make(chan struct{}, len(rpcList))
		// 成功和失败的rpc 的收集器
		finishedCollector = make([]RpcProcessWithNoBaseProducts, 0)
		failedCollector   = make([]struct{}, 0)
	)

	t := time.NewTimer(timeout * time.Millisecond)
	for _, rpc := range rpcList {
		go func(ctx context.Context, rpc RpcProcessWithNoBaseProducts) {
			defer func() {
				if err := recover(); err != nil {
					log.Trace.Errorf(ctx, consts.TagErrGoRoutine, "panic in rpcProcess %v with %v \n stack: %s", rpc, err, debug.Stack())
				}
			}()

			rst := rpc.Fetch(ctx)
			if 0 == atomic.LoadInt32(&isTimeout) && rst {
				finishedChan <- rpc
			} else {
				failedChan <- struct{}{}
				if 0 != atomic.LoadInt32(&isTimeout) {
					rpcName := reflect.TypeOf(rpc).Elem().Name()
					log.Trace.Warnf(ctx, consts.TagErrGoRoutine, "rpc process with rst and timeout: %v||%v||%d", rpcName, rst, isTimeout)
				}
			}
		}(ctx, rpc)
	}

	for {
		select {
		case rpc := <-finishedChan:
			finishedCollector = append(finishedCollector, rpc)
		case rpc := <-failedChan:
			failedCollector = append(failedCollector, rpc)
		case <-t.C:
			atomic.StoreInt32(&isTimeout, 1)
		}
		if len(finishedCollector)+len(failedCollector) == len(rpcList) || 1 == atomic.LoadInt32(&isTimeout) {
			break
		}
	}

	return finishedCollector
}
