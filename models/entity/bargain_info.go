package entity

type SenseConfig struct {
	//Ceil string `json:"ceil"`
	//Floor1 string `json:"floor1"`
	//Floor2 string `json:"floor2"`
	//SenseType int `json:"sense_type"`
	//RecommendType int    `json:"recommend_type"`
	PriceLeftText string `json:"price_left_text"`
	Icon          string `json:"icon"`
	SubTitle      string `json:"sub_title"`
	FontColor     string `json:"font_color"`
	BorderColor   string `json:"border_color"`
	ConfigID      string `json:"config_id"`
}

type FixPrice struct {
	EstimateFee     string `json:"estimate_fee"`
	DynamicTotalFee string `json:"dynamic_total_fee"`
	CouponDiscount  string `json:"coupon_discount"`
	DiscountAmount  string `json:"discount_amount"`
}
