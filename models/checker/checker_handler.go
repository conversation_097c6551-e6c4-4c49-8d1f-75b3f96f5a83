package checker

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"
)

const (
	CheckerWhiteList      = "white_list"
	CheckerOpenStatus     = "open_status"
	CheckerBigDataTag     = "big_data_tag"
	CheckerFenceInfo      = "fence_info"
	CheckerDatePeriod     = "date_period"
	CheckerWeekList       = "week_list"
	CheckerTimePeriodList = "time_period_list"
)

const (
	SuccessChecker = iota
	EmptyCheckerFail
	OpenStatusFail
	BigDataTagFail
	StartFenceFail
	StopFenceFail
	AllFenceFail
	DateFail
	WeekdayFail
	TimeFail
)

type Params struct {
	AccessKeyID   int32
	AppVersion    string
	Pid           int64
	Uid           int64
	Phone         string
	FromLat       float64
	FromLng       float64
	ToLat         float64
	ToLng         float64
	OrderType     int32
	DepartureTime int64
	MapType       string
	BigDataTag    []string
}

var CheckerMap = map[string]GetCheckerFunc{
	CheckerWhiteList: func() Checker { return &WhiteListChecker{} },

	CheckerOpenStatus: func() Checker { return &OpenStatusChecker{} },

	CheckerBigDataTag: func() Checker { return &TagChecker{} },

	CheckerFenceInfo: func() Checker { return &FenceChecker{} },

	CheckerDatePeriod: func() Checker { return &DateChecker{} },

	CheckerWeekList: func() Checker { return &WeekdayChecker{} },

	CheckerTimePeriodList: func() Checker { return &TimeChecker{} },
}

func LoadChecker(ctx context.Context, checkerName string, checkerConf interface{}, param *Params) *CheckerUnit {
	var getCheckerFunc GetCheckerFunc
	var checker Checker
	if getFunc, ok := CheckerMap[checkerName]; ok {
		getCheckerFunc = getFunc
	}

	if getCheckerFunc == nil {
		return nil
	}

	checker = getCheckerFunc()
	if checker == nil {
		return nil
	}

	if checker.SetRule(ctx, checkerConf) && checker.CheckRuleValid(param) {
		cUnit := &CheckerUnit{}
		cUnit.CheckerName = CheckerName(checkerName)
		cUnit.Checker = checker
		return cUnit
	}

	return nil
}

func ExecChecker(ctx context.Context, checkerList []*CheckerUnit, params *Params) (bool, int) {
	if len(checkerList) == 0 {
		return false, EmptyCheckerFail
	}

	// 不需要单独过白名单的checker
	for _, unit := range checkerList {
		if unit != nil && unit.Checker != nil {
			if isHit, reasonTag := unit.Checker.Do(ctx, params); !isHit {
				log.Trace.Infof(ctx, trace.DLTagUndefined, "ExecChecker remove checker: %s", unit.CheckerName)
				return false, reasonTag
			}
		}
	}

	return true, SuccessChecker
}
