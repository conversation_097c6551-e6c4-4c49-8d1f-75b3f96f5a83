package checker

import (
	"context"
	"time"

	"git.xiaojukeji.com/gulfstream/biz-common-go/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/nuwa/trace"
)

type WeekdayChecker struct {
	weekList []int
}

func (c *WeekdayChecker) SetRule(ctx context.Context, input interface{}) bool {
	weekList, ok := input.([]int)
	if !ok {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "week SetRule false")
		return false
	}
	c.weekList = weekList
	return true
}

func (c *WeekdayChecker) CheckRuleValid(params *Params) bool {
	if len(c.weekList) == 0 {
		return false
	}
	return true
}

func (c *WeekdayChecker) Do(ctx context.Context, params *Params) (bool, int) {
	var (
		departureTime time.Time
	)

	if params.OrderType == int32(consts.OrderTypeNow) {
		departureTime = time.Now()
	} else if params.OrderType == int32(consts.OrderTypeBooking) {
		departureTime = time.Unix(params.DepartureTime, 0)
	} else {
		return false, WeekdayFail
	}

	if util.InArrayInt(int(departureTime.Weekday()), c.weekList) {
		return true, SuccessChecker
	} else {
		return false, WeekdayFail
	}
}
