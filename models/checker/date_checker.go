package checker

import (
	"context"

	"time"

	"git.xiaojukeji.com/gulfstream/biz-common-go/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"
)

type DateChecker struct {
	datePeriod []string
}

func (c *DateChecker) SetRule(ctx context.Context, input interface{}) bool {
	datePeriod, ok := input.([]string)
	if !ok {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "date SetRule false")
		return false
	}
	c.datePeriod = datePeriod
	return true
}

func (c *DateChecker) CheckRuleValid(params *Params) bool {
	if len(c.datePeriod) == 0 {
		return false
	}
	return true
}

func (c *DateChecker) Do(ctx context.Context, params *Params) (bool, int) {
	var (
		startTime     int64
		endTime       int64
		departureTime int64
	)

	if len(c.datePeriod) != 2 || c.datePeriod[0] == "" || c.datePeriod[1] == "" {
		return false, DateFail
	}

	if params.OrderType == int32(consts.OrderTypeNow) {
		departureTime = time.Now().Unix()
	} else if params.OrderType == int32(consts.OrderTypeBooking) {
		departureTime = params.DepartureTime
	} else {
		return false, DateFail
	}

	startTime = util.ParseAllDate(c.datePeriod[0])
	endTime = util.ParseAllDate(c.datePeriod[1])

	if departureTime < startTime || departureTime >= endTime {
		return false, DateFail
	}

	return true, SuccessChecker
}
