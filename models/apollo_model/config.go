package apollo_model

// TODO 需要检查apollo 上的所有配置，类型是否都正确
type ClassifyCategoryConfig struct {
	Language string `json:"__language"`

	CategoryID       int32    `json:"category_id"`
	Title            string   `json:"title"`
	SubTitle         string   `json:"sub_title"`
	FoldText         string   `json:"fold_text"`
	Icon             string   `json:"icon"`
	BgGradients      []string `json:"bg_gradients"`
	ProductList      []string `json:"product_list"`
	SubGroupID       []string `json:"sub_group_id"`
	SectionTitle     string   `json:"section_title"`
	Rank             int32    `json:"rank"`
	SubCategoryTitle string   `json:"sub_category_title"`
}

type PossibleCategoryConfig struct {
	ProductCategory        int64    `json:"product_category"`
	PossibleCategoryStrIds []string `json:"possible_category_ids"`
	PossibleCategoryIds    []int32  `json:"-"`
	StrategyAB             string   `json:"strategy_ab"`
}
