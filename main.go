package main

import (
	"context"
	"flag"
	"fmt"
	"os"

	_ "git.xiaojukeji.com/nuwa/tools/fastdev/v2/replayer"

	gaiaSdk "git.xiaojukeji.com/engine/gaia-sdk"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/adx"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/baichuanRpc"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/carpool_web"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/daijia"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/guardian"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/hilda"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/pettripapi"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/sps"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ssse"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ticket_api"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/trip_cloud_passenger_go"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/sdk/foras"
	"git.xiaojukeji.com/intercity/biz-api/intercity-common-go/sdk/robin"
	apolloV2 "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/product_center"
	"git.xiaojukeji.com/gulfstream/bronze-door-sdk-go/bootstrap"
	"git.xiaojukeji.com/gulfstream/bronze-door-sdk-go/common"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/hundun"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/plutus"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ticket_price"
	"git.xiaojukeji.com/nuwa/golibs/goutils"

	dcmp "git.xiaojukeji.com/dirpc/dirpc-go-dcmp/v2"
	"git.xiaojukeji.com/gulfstream/passenger-common/common/carlevel"
	legoTrace "git.xiaojukeji.com/lego/context-go"
	"git.xiaojukeji.com/lego/dirpc-go"
	"git.xiaojukeji.com/nuwa/go-monitor"
	"git.xiaojukeji.com/nuwa/golibs/ballast"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/conf"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/ddmq"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/lib"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/redis"
	"git.xiaojukeji.com/gulfstream/mamba/common/server"
	"git.xiaojukeji.com/gulfstream/mamba/dao/beatles"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/athena"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/bicycle_route"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/brick"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/carpool_api"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/carpool_open_api"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/combined_travel_v2"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/coupon"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dape"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dfs"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/engine"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/compensation"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/decision"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/fence"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ferrari"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/gcs"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/hestia_charge"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/horae"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/hotspot"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/insurance"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/kronos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/locsvr"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/member"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/pios"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/pope_pap"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/prfs"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/risk"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/route_broker"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/routebroker"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/seat"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/tag_service"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/transit_wind"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/trip_cloud_passenger"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/uranus"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/vcard"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/walking_route"
	"git.xiaojukeji.com/gulfstream/mamba/logic/shuttle_bus_estimate/config"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/estimate_v4"
	"git.xiaojukeji.com/gulfstream/mamba/view/render"
	"git.xiaojukeji.com/gulfstream/mamba/view/selector"
	passengerCommonLog "git.xiaojukeji.com/gulfstream/passenger-common/sdk/log"
	log3 "git.xiaojukeji.com/intercity/biz-api/intercity-common-go/sdk/log"
	commonLib "git.xiaojukeji.com/s3e/common-lib/v2"
)

var (
	confPath string
)

const MainLogTag = "main_log_tag"

func init() {
	flag.StringVar(&confPath, "c", "./conf/app.toml", "-c set config file path") // default config file is conf/app.toml
	flag.Parse()
	fmt.Printf("confPath is %s\n", confPath)
	conf.InitConf(confPath)
	log.Init()
	passengerCommonLog.Init(log.Public, log.Trace)
	commonLib.Init(log.Trace)
}

func rpcInit() {
	var err error

	// dirpc init
	if err = dirpc.Setup("./conf/dirpc.json", nil); err != nil {
		printAndDie(err)
	}
	// apollo init
	if err = apollo.Init(); err != nil {
		printAndDie(err)
	}

	ddmq.Init()

	// nolint: gocritic
	// mysql.Init()
	if err = redis.Init(); err != nil {
		printAndDie(err)
	}

	// redis.InitEstimateClient()
	if err = redis.InitEstimateClient(); err != nil {
		printAndDie(err)
	}

	if err = redis.InitCarpoolRedisClient(); err != nil {
		printAndDie(err)
	}

	if err = redis.InitFusionRedisClient(); err != nil {
		printAndDie(err)
	}

	if err = redis.InitMultiEstimateRedisClient(); err != nil {
		printAndDie(err)
	}

	// dds Init
	if err = decision.InitClient(); err != nil {
		printAndDie(err)
	}

	// dos Init
	if err = dos.InitClient(); err != nil {
		printAndDie(err)
	}
	if err = adx.Init(); err != nil {
		printAndDie(err)
	}

	// athena Init (先thrift 后初始化http)
	if err = athena.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = foras.Init(); err != nil {
		printAndDie(err)
	}

	// passport Init
	if err = passport.InitClient(); err != nil {
		printAndDie(err)
	}
	// 风控 init
	if err = risk.InitClient(); err != nil {
		printAndDie(err)
	}
	if err = gcs.Init(); err != nil {
		printAndDie(err)
	}
	if err = hotspot.Init(); err != nil {
		printAndDie(err)
	}

	// price_api Init
	if err = price_api.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = ferrari.Init(); err != nil {
		printAndDie(err)
	}

	// member Init
	if err = member.InitClient(); err != nil {
		printAndDie(err)
	}

	// pios Init
	if err = pios.InitClient(); err != nil {
		printAndDie(err)
	}

	// dcmp Init
	if err = dcmp.Init(consts.Mamba, "./", consts.DCMPBranch); err != nil {
		printAndDie(err)
	}

	// compensation init
	if err = compensation.InitClient(); err != nil {
		printAndDie(err)
	}
	if err = prfs.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = carpool_open_api.Init(); err != nil {
		printAndDie(err)
	}

	if err = brick.Init(); err != nil {
		printAndDie(err)
	}

	if err = guardian.Init(); err != nil {
		printAndDie(err)
	}

	if err = locsvr.Init(); err != nil {
		printAndDie(err)
	}

	// ufs init
	if err = ufs.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = ssse.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = sps.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = hundun.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = hilda.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = transit_wind.InitClient(); err != nil {
		printAndDie(err)
	}
	if err = engine.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = fence.Init(); err != nil {
		printAndDie(err)
	}

	if err = hestia_charge.Init(); err != nil {
		printAndDie(err)
	}

	if err = dape.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = coupon.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = tag_service.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = route_broker.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = vcard.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = combined_travel_v2.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = routebroker.Init(); err != nil {
		printAndDie(err)
	}

	if err = seat.Init(); err != nil {
		printAndDie(err)
	}

	if err = trip_cloud_passenger.InitClient(); err != nil {
		printAndDie(err)
	}
	if err = trip_cloud_passenger_go.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = carpool_api.Init(); err != nil {
		printAndDie(err)
	}

	if err = bicycle_route.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = walking_route.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = insurance.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = kronos.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = horae.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = pope_pap.Init(); err != nil {
		printAndDie(err)
	}
	if err = uranus.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = carpool_web.Init(); err != nil {
		printAndDie(err)
	}

	if err = dfs.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = plutus.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = ticket_price.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = pettripapi.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = ticket_api.InitClient(); err != nil {
		printAndDie(err)
	}

	if err = bootstrap.Init(context.Background(), &common.BronzeDoorOption{
		PubLog:    log.Public,
		DiLog:     log.Trace,
		Namespace: common.NamespaceBronzeDoorPassenger,
		//VersionName: "carpool_ticket_card_effective", // 湾流测试分支
	}); err != nil {
		printAndDie(err)
	}

	if err = apolloV2.AutoInit(); err != nil {
		printAndDie(err)
	}

	if err = gaiaSdk.InitGaia(apollo.GaiaNs); err != nil {
		printAndDie(err)
	}

	if err = daijia.InitClient(); err != nil {
		printAndDie(err)
	}

	beatles.Init()
	product_center.InitPriceDesc()

	if err = baichuanRpc.InitClient(); err != nil {
		printAndDie(err)
	}
}

func main() {
	ballast.SetSize(2 * ballast.GB)
	// rpc client 初始化
	rpcInit()

	//biz conf init
	modelInit()

	// 服务监控，打通odin，参考：http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=125476958
	go monitor.Start(":9981", monitor.AllPlugin) // nolint: errcheck

	// 启动服务
	if err := server.Run(); err != nil {
		log.Trace.Fatalf(context.Background(), legoTrace.DLTagUndefined, "server Run err %v \n", err)
		os.Exit(1)
	}

	// 传入日志句柄
	goutils.Init(log.Trace)
}

func modelInit() {
	var err error
	if err = render.Init(); err != nil {
		printAndDie(err)
	}

	// selector
	if err = selector.InitPageFieldsRouter(); err != nil {
		printAndDie(err)
	}

	if err = lib.PassengerCommonInit(); err != nil {
		printAndDie(err)
	}

	if err = carlevel.InitCarLevelMap(); err != nil {
		printAndDie(err)
	}

	if err = estimate_v4.InitLuxInfoMap(); err != nil {
		printAndDie(err)
	}

	if err = product_center.InitPCNTupleConfigMap(); err != nil {
		printAndDie(err)
	}
	if err = config.InitRouteConfMapApolloMap(); err != nil {
		printAndDie(err)
	}

	if err = product_center.InitLoadCityProductOpenRulesApolloMap(); err != nil {
		printAndDie(err)
	}

	if err = robin.Init(); err != nil {
		printAndDie(err)
	}

	log3.Init(log.Public, log.Trace)
}

func printAndDie(err error) {
	_, _ = fmt.Fprintf(os.Stderr, "main init error: %v", err)
	log.Trace.Fatalf(context.Background(), MainLogTag, "server Run err %v \n", err)
	os.Exit(-1)
}
