# 给 idl 加校验

## 背景介绍

参数校验大多都是样板代码, 其实可以通过生成来解决这个重复的工作.  
但目前idl不支持添加检验tag, 所以不能只直接在idl文件中加.  
不过目前nuwa的工作方式是通过`idl -> proto -> grpc`的步骤完成的生成.  给proto文件加校验tag, 社区是有对应工具的.  
因此可以通过编辑中间的proto文件, 自己生成对应的pb文件, 完成这一工作.

## 操作步骤

以 `/gulfstream/mamba/v1/pCarpoolEstimatePrice`为例,

1. 将IDL文件拆分为 [request](../idl/carpool_estimate_request.idl)和[response](../idl/carpool_estimate_response.idl)两部分
2. 在[主IDL](../idl/mamba.idl)中引用这两个文件, 并添加对应 *service*

    ```idl
    include "carpool_estimate_request.idl"
    include "carpool_estimate_response.idl"

    CarpoolEstimateResponse PCarpoolEstimatePrice(1:CarpoolEstimateRequest request) (
        ...
    )
    ```

3. 安装 [`nuwa`](https://git.xiaojukeji.com/nuwa/nuwa)
4. 执行nuwa标准的生成

    ```bash
    nuwa gen -l go
    ```

5. 通过idl产出base版本的proto文件

    ```bash
    dirpcgen -gen json -out proto idl/carpool_estimate_request.idl
    thrift2proto proto/carpool_estimate_request.json > proto/carpool_estimate_request.proto
    ```

6. 安装[Golang ProtoBuf Validator Compiler](https://github.com/mwitkow/go-proto-validators)

    ```bash
    git clone https://github.com/mwitkow/go-proto-validators.git ${GOPATH}/src/github.com/mwitkow/go-proto-validators
    cd ${GOPATH}/src/github.com/mwitkow/go-proto-validators
    latesttag=$(git describe --tags)
    git checkout ${latesttag}
    go install github.com/mwitkow/go-proto-validators/protoc-gen-govalidators
    ```

7. 编辑 `proto/carpool_estimate_request.proto`, 具体细节见[如何编辑](#编辑proto文件)
8. 执行`protoc`生成对应的文件

    ```bash
    protoc \
    --proto_path=${GOPATH}/src \
    --proto_path=$GOPATH/bin/gopath/src/git.xiaojukeji.com/nuwa/tools/rpc-tools/protobuf \
    --proto_path=$GOPATH/bin/gopath/src/git.xiaojukeji.com/nuwa/tools/rpc-tools/protobuf/grpc-gateway/third_party/googleapis \
    --proto_path=. \
    --govalidators_out=./idl/proto \
    proto/carpool_estimate_request.proto
    ```

9. 引入依赖

    ```bash
    go get github.com/mwitkow/go-proto-validators
    ```

**注意**:  

1. 最后生成文件时, 没有 `--go_out= .idl/proto` 这个参数, 否则会覆盖nuwa生成的文件.

**说明**:  

1. 为什么用 `https://github.com/mwitkow/go-proto-validators`?
    * 因为 [grpc-ecosystem/go-grpc-middleware](https://github.com/grpc-ecosystem/go-grpc-middleware) 在用
    * 不过目前这个包已经好久没有维护了
    * 待[envoyproxy/protoc-gen-validate](https://github.com/envoyproxy/protoc-gen-validate)稳定了可以替换.
2. 为什么可以这么操作?
    * 主要是因为OE并没有执行`nuwa`命令进行生成, 而是本地生成后提交, 因此理论上手动编写校验文件也可
    * nuwa 支持多文件idl定义

### 编辑proto文件

1. 添加 `option go_package = ".;proto";` 指定生成文件的package
    * 生成的文件的开头会变为 `package proto`
2. 添加 `import "github.com/mwitkow/go-proto-validators/validator.proto";`
3. 添加校验tag

### 效果与使用

执行完命令, 会生成 [carpool_estimate_request.validator.pb.go](../idl/proto/carpool_estimate_request.validator.pb.go) 文件, 给对应的结构体, 加了新的绑定方法`Validate`,

由于是绑定在`*CarpoolEstimateRequest`上的方法, 所以可以在对应的 `controller` 中直接调用.

```go
func (controller CarpoolEstimateController) PCarpoolEstimatePrice(ctx context.Context, req *proto.CarpoolEstimateRequest) (*proto.CarpoolEstimateResponse error) {
    if err := req.Validate(); err != nil {
  return nil, fmt.Errorf("param invalid %w", err)
 }
}
```

也可以结合拦截器 [go-grpc-middleware : validator](https://github.com/grpc-ecosystem/go-grpc-middleware/tree/master/validator) 使用.

## 局限性

1. 由于是在 idl产出的中间文件 上做的修改, 因此idl文件有变化时, 重新生成可能会覆盖之前已经加好的校验规则
    * 自己手动merge
2. 由于是通过在 proto 文件内对字段加校验规则, 因此自动生成只能做到单字段, 字面含义的校验
    * 业务含义的校验, 可以手改 生成的 validator 文件
    * 聚合字段的校验, 可以改 *Compiler* 或者改 生成的 validator 文件

## 参考

* [Golang ProtoBuf Validator Compiler](https://github.com/mwitkow/go-proto-validators)
* [Go gRPC Middleware](https://github.com/grpc-ecosystem/go-grpc-middleware)
* [nuwa - dirpcgen](https://git.xiaojukeji.com/nuwa/tools/dirpcgen)
* [nuwa - thrift2proto](https://git.xiaojukeji.com/nuwa/tools/thrift2proto)
