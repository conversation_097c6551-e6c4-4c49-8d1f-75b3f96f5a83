package carpool_invitation

import (
	"context"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/public_log"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/carpool_invitation"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/pincheche"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_detail_info"
	"git.xiaojukeji.com/gulfstream/mamba/view/render/price_info_desc_list"
	LegoContext "git.xiaojukeji.com/lego/context-go"
	"strconv"
)

func Render(ctx context.Context, req *proto.CarpoolInvitationEstimateRequest, products []*biz_runtime.ProductInfoFull) (*proto.InvitationEstimateData, int) {
	defer func() {
		writePublicLog(ctx, products)
	}()

	if len(products) == 0 {
		return nil, consts.ErrnoNoProductOpen
	}

	var baseProd *biz_runtime.ProductInfoFull
	for _, prod := range products {
		if prod.GetProductCategory() == estimate_pc_id.EstimatePcIdLowPriceCarpool {
			baseProd = prod
			break
		}
	}

	if baseProd == nil {
		return nil, consts.ErrnoNoProductOpen
	}

	return renderPageData(ctx, baseProd, req), consts.NoErr
}

func renderPageData(ctx context.Context, product *biz_runtime.ProductInfoFull, req *proto.CarpoolInvitationEstimateRequest) *proto.InvitationEstimateData {
	if product == nil || product.Product == nil {
		return nil
	}

	prod := &CarpoolInviationAdapter{product}

	CacheInvitationMatchInfo(ctx, product.BaseReqData, prod.GetEstimateID(), prod.IsRouteMatch(), prod.GetMatchDepartureRange())
	// 缓存路线匹配的预估请求信息
	if prod.IsRouteMatch() {
		CacheEstimateInfo(ctx, product.BaseReqData, req)
	}

	matchIcon, matchMsg := carpool_invitation.BuildMatchRateInfo(ctx, prod)

	data := &proto.InvitationEstimateData{
		EstimateId:        product.GetEstimateID(),
		HeadImg:           carpool_invitation.GetHeadImg(ctx, prod),
		IntroMsg:          dcmp.GetJSONContentWithPath(ctx, carpool_invitation.DcmpKeyPinchecheInvitation, nil, "intro_msg"),
		IntroUrl:          dcmp.GetJSONContentWithPath(ctx, carpool_invitation.DcmpKeyPinchecheInvitation, nil, "intro_url"),
		MatchRateIcon:     matchIcon,
		MatchRateMsg:      matchMsg,
		EstimateTips:      dcmp.GetJSONContentWithPath(ctx, carpool_invitation.DcmpKeyPinchecheInvitation, nil, "estimate_tips"),
		MultiPriceDesc:    carpool_invitation.BuildMultiFeeMsg(ctx, prod),
		TravelInfo:        carpool_invitation.BuildTravelInfo(ctx, prod),
		CarpoolSeatModule: carpool_invitation.BuildSeatModule(ctx, prod),
		ExtraMap:          pincheche.ExtraMap(ctx, prod),
		CarpoolBooking:    carpool_invitation.BuildBookingModule(ctx, prod),
		FeeDetailUrl:      fee_detail_info.GetDetailUrlV2(ctx, product),
		ButtonList:        carpool_invitation.BuildButtonList(ctx, prod),
		PassThrough:       prod.GetPassThrough(ctx, req),
		OmegaInfo:         carpool_invitation.BuildOmegaInfo(ctx, prod),
	}
	return data
}

func writePublicLog(ctx context.Context, products []*biz_runtime.ProductInfoFull) {
	// 屏蔽压测流量
	if _trace := LegoContext.GetTrace(ctx); _trace.GetHintCode() == strconv.Itoa(int(consts.PressureHintCode)) {
		return
	}
	for _, product := range products {
		if product == nil || product.Product == nil || product.GetSceneEstimatePrice() == nil {
			continue
		}

		reqData := product.BaseReqData
		bill := product.GetBillInfo()
		logInfo := make(map[string]interface{})

		logInfo["xpsid"] = reqData.CommonInfo.Xpsid
		logInfo["xpsid_root"] = reqData.CommonInfo.XpsidRoot

		// ..... 基础信息 .....
		logInfo["estimate_trace_id"] = LegoContext.GetTrace(ctx).GetTraceId()
		logInfo["estimate_id"] = product.GetEstimateID()
		logInfo["pLang"] = reqData.CommonInfo.Lang
		logInfo["lang"] = reqData.CommonInfo.Lang
		logInfo["member_level_id"] = product.GetLevelID()
		logInfo["menu_id"] = reqData.CommonInfo.MenuID
		logInfo["access_key_id"] = reqData.CommonInfo.AccessKeyID
		logInfo["channel"] = reqData.CommonInfo.Channel
		logInfo["appversion"] = reqData.CommonInfo.AppVersion
		logInfo["app_version"] = reqData.CommonInfo.AppVersion
		logInfo["agent_type"] = "both_call_anycar"
		logInfo["channel_id"] = reqData.CommonInfo.Channel
		logInfo["client_type"] = reqData.CommonInfo.ClientType
		logInfo["biz_user_type"] = reqData.PassengerInfo.UserType
		logInfo["call_car_type"] = reqData.CommonInfo.CallCarType

		logInfo["pid"] = reqData.PassengerInfo.PID
		logInfo["uid"] = reqData.PassengerInfo.UID
		logInfo["phone"] = reqData.PassengerInfo.Phone

		logInfo["scene_type"] = product.GetSceneType()
		logInfo["product_id"] = product.GetProductId()
		logInfo["business_id"] = product.GetBusinessID()
		logInfo["require_level"] = product.GetRequireLevel()
		logInfo["combo_type"] = product.GetComboType()
		logInfo["product_category"] = product.GetProductCategory()
		logInfo["count_price_type"] = bill.CountPriceType
		logInfo["carpool_type"] = product.GetCarpoolType()
		logInfo["is_special_price"] = product.GetIsSpecialPrice()
		logInfo["order_type"] = product.GetOrderType()
		logInfo["exam_type"] = product.GetExamType()
		logInfo["level_type"] = product.GetLevelType()
		logInfo["airport_type"] = product.GetAirType()
		logInfo["railway_type"] = product.GetRailType()
		logInfo["estimate_pc_id"] = product.GetProductCategory()

		logInfo["default_pay_type"] = product.GetDefaultPayType()

		logInfo["origin_page_type"] = reqData.CommonInfo.PageType
		logInfo["source_id"] = reqData.CommonInfo.SourceID
		logInfo["combo_id"] = product.GetComboID()

		logInfo["order_n_tuple"] = util.JustJsonEncode(product.Product)

		logInfo["current_lng"] = reqData.AreaInfo.CurLng
		logInfo["current_lat"] = reqData.AreaInfo.CurLat
		logInfo["flng"] = reqData.AreaInfo.FromLng
		logInfo["flat"] = reqData.AreaInfo.FromLat
		logInfo["tlng"] = reqData.AreaInfo.ToLng
		logInfo["tlat"] = reqData.AreaInfo.ToLat
		logInfo["county"] = reqData.AreaInfo.FromCounty
		logInfo["to_county"] = reqData.AreaInfo.ToCounty
		logInfo["area"] = reqData.AreaInfo.Area
		logInfo["to_area"] = reqData.AreaInfo.ToArea
		logInfo["from_name"] = util.StringEscape(reqData.AreaInfo.FromName)
		logInfo["to_name"] = util.StringEscape(reqData.AreaInfo.ToName)
		logInfo["stopover_points"] = util.JustJsonEncode(reqData.AreaInfo.StopoverPointInfo)
		logInfo["district"] = reqData.AreaInfo.District
		logInfo["starting_poi_id"] = reqData.AreaInfo.FromPoiID
		logInfo["dest_poi_id"] = reqData.AreaInfo.ToPoiID

		logInfo["carpool_seat_num"] = product.GetExactSeatNum()
		logInfo["departure_time"] = product.GetPrivateBizInfo().DepartureTime

		// ..... 价格信息 .....
		logInfo["driver_metre"] = bill.DriverMetre
		logInfo["driver_minute"] = bill.DriverMinute
		logInfo["time_cost"] = bill.DriverMinute
		logInfo["total_fee"] = bill.TotalFee
		logInfo["pre_total_fee"] = bill.PreTotalFee
		logInfo["dynamic_total_fee"] = bill.DynamicTotalFee
		logInfo["estimate_fee"] = product.GetEstimateFee()
		logInfo["dynamic_diff_price"] = bill.DynamicDiffPrice
		// 开平新动调
		logInfo["dups_dynamic_times"] = bill.DupsDynamicTimes
		logInfo["dups_dynamic_raise"] = bill.DupsDynamicRaise
		logInfo["cap_price"] = bill.CapPrice
		logInfo["highway_fee"] = bill.HighwayFee
		logInfo["red_packet"] = price_info_desc_list.GetValueFromDisplayLines(product, price_info_desc_list.RedPacketFee)

		logInfo["dynamic_times"] = bill.DynamicTimes
		if bill.DynamicInfo != nil {
			logInfo["dynamic_price_id"] = bill.DynamicInfo.DynamicPriceId
		}

		log.Public.Public(ctx, public_log.GOrderEstimatePrice, logInfo)
	}
}
