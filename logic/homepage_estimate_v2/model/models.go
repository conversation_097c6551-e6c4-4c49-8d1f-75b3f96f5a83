package model

import "git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"

type Logic struct {
	generator    *biz_runtime.ProductsGenerator
	bizTag       string
	destType     string
	expGroupName string // 新流一键叫1.2实验分组名
}

type PositionInfo struct {
	ToLat             float64 // 终点
	ToLng             float64 // 终点
	ToPOIID           string
	ToPOIType         string
	ToAddress         string
	ToName            string
	SearchId          string
	ToPoiCode         string
	IsRecommendAbsorb int32
	CityId            int32
	CoordinateType    string
	ToCityName        string
	ToAddressAll      string
}

type LastDestPoiInfo struct {
	PoiId             string  `json:"poi_id"`
	Lat               float64 `json:"lat"`
	Lng               float64 `json:"lng"`
	DisplayName       string  `json:"displayname"`
	AddressAll        string  `json:"addressAll"`
	Address           string  `json:"address"`
	SrcTag            string  `json:"srctag"`
	CoordinateType    string  `json:"coordinate_type"`
	UpdateTime        int64   `json:"update_time"`
	SearchId          string  `json:"search_id"`
	DestPoiCode       string  `json:"dest_poi_code"`
	IsRecommendAbsorb int32   `json:"is_recommend_absorb"`
	CityId            int32   `json:"cityid"`
	CityName          string  `json:"city_name"`
}
