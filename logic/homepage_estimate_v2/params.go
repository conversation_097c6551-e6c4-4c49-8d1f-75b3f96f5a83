package homepage_estimate

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace/v2"
)

func NewServiceRequest(ctx context.Context, httpReq *proto.HomePageCallCarEstimateReq) (
	*param_handler.EstimateRequest, BizError.BizError) {

	serviceReq := &param_handler.EstimateRequest{
		ReqFromParams: httpReq,
	}
	handler := param_handler.NewHandler(serviceReq)
	do := handler.Do(ctx, []param_handler.RequestWrapper{
		// 需要保证顺序
		CheckParams(ctx, serviceReq),                                       // 参数校验
		param_handler.GetUserInfo(ctx, httpReq.Token, httpReq.AccessKeyId), // 用户信息获取 + 校验
	})

	return serviceReq, do

}

func CheckParams(ctx context.Context, serviceReq *param_handler.EstimateRequest) param_handler.RequestWrapper {
	return func(ctx context.Context, serviceReq *param_handler.EstimateRequest) BizError.BizError {

		value, ok := serviceReq.ReqFromParams.(*proto.HomePageCallCarEstimateReq)
		if !ok {
			return BizError.ErrInvalidArgument
		}

		if len(value.BizTag) == 0 || !util.InArrayStr(value.BizTag, []string{consts.CallCarBizTagDefault, consts.CallCarBizTagXinliu, consts.CallCarBizTagNewLossHomePage}) {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "biz_tag not found [%s]", value.BizTag)
			return BizError.ErrInvalidArgument
		}

		return nil
	}
}
