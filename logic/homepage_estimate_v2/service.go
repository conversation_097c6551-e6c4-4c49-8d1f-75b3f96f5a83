package homepage_estimate

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"
	"git.xiaojukeji.com/gulfstream/mamba/logic/homepage_estimate_common"
	"git.xiaojukeji.com/gulfstream/mamba/logic/homepage_estimate_v2/filter"
	"git.xiaojukeji.com/gulfstream/mamba/logic/homepage_estimate_v2/model"
	"git.xiaojukeji.com/gulfstream/mamba/logic/homepage_estimate_v2/render"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
	"git.xiaojukeji.com/nuwa/trace"
)

type Service struct {
	generator *biz_runtime.ProductsGenerator
	bizTag    string
	destType  string
	destInfo  *model.PositionInfo
}

func NewService(ctx context.Context, estimateReq *param_handler.EstimateRequest) (*Service, error) {
	var (
		err         error
		destType    string
		destInfo    *model.PositionInfo
		baseReqData *models.BaseReqData
		productsGen *biz_runtime.ProductsGenerator
	)
	request, ok := estimateReq.ReqFromParams.(*proto.HomePageCallCarEstimateReq)
	if !ok {
		return nil, errors.New("error")
	}

	// 构造终点信息
	if destType, destInfo = buildDestInfo(ctx, request.BizTag, estimateReq.GetPassengerInfo().PID, request); destInfo == nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "buildDestInfo failed")
		return nil, nil
	}

	// 构造数据
	if baseReqData, err = models.NewBaseReqDataBuilder().SetPassengerInfoV2(
		&models.PassengerInfoV2{
			UID:      int64(estimateReq.GetPassengerInfo().UID),
			PID:      int64(estimateReq.GetPassengerInfo().PID),
			Phone:    estimateReq.GetPassengerInfo().Phone,
			Role:     estimateReq.GetPassengerInfo().Role,
			Channel:  estimateReq.GetPassengerInfo().Channel,
			UserType: request.UserType,
			OriginID: estimateReq.GetPassengerInfo().OriginId,
		}).
		SetClientInfo(&models.ClientInfo{
			AppVersion:   request.AppVersion,
			AccessKeyID:  request.AccessKeyId,
			Channel:      util.IntParseWithDefault(request.Channel, 0),
			ClientType:   request.ClientType,
			Lang:         request.Lang,
			PlatformType: request.PlatformType,
			MenuID:       "dache_anycar",
			PageType:     page_type.PageTypeCallCarEstimate,
			Xpsid:        util.StringPtr2String(request.Xpsid),
			XpsidRoot:    util.StringPtr2String(request.XpsidRoot),
		}).
		SetGEOInfo(&models.GEOInfo{
			MapType:     request.MapType,
			CurrLat:     request.Lat,
			CurrLng:     request.Lng,
			FromLat:     request.FromLat,
			FromLng:     request.FromLng,
			FromPOIID:   request.FromPoiId,
			FromPOIType: request.FromPoiType,
			FromAddress: request.FromAddress,
			FromName:    request.FromName,
			ToLat:       destInfo.ToLat,
			ToLng:       destInfo.ToLng,
			ToPOIID:     destInfo.ToPOIID,
			ToPOIType:   destInfo.ToPOIType,
			ToAddress:   destInfo.ToAddress,
			ToName:      destInfo.ToName,
		}).
		SetUserOption(&models.UserOption{
			CallCarType:   0,
			OrderType:     0,
			PaymentsType:  0,
			DepartureTime: time.Now().Unix(),
		}).TryBuild(ctx); err != nil {
		return nil, err
	}

	if productsGen, err = biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("pHomePageEstimate"),
		biz_runtime.WithBaseReq(baseReqData),
	); err != nil {
		return nil, err
	}

	// 获取一键叫使用车型，用于dds处理后进行过滤，防止请求账单无用品类
	productsGen.RegisterAfterDdsFilter(filter.NewFilterProducts(ctx, strconv.FormatUint(estimateReq.GetPassengerInfo().PID, 10), request.BizTag))

	if consts.CallCarBizTagNewLossHomePage != request.BizTag {
		// 支付方式不一致不展示一键叫
		productsGen.RegisterAfterPriceFilter(filter.NewFilterMismatchPaymentInfo())
		// 额外费项过滤
		productsGen.RegisterAfterPriceFilter(&filter.PriceSceneFilter{})
	}

	productsGen.RegisterAfterPriceFilter(&homepage_estimate_common.CombinePriceFilter{BizTag: request.BizTag})

	// 针对新流，价格高于设定值不展示一键叫\
	// 新流首页follow这个逻辑
	if consts.CallCarBizTagXinliu == request.BizTag || consts.CallCarBizTagNewLossHomePage == request.BizTag {
		productsGen.RegisterAfterPriceFilter(&filter.MaxPriceFilter{})
	}

	productsGen.SetNeedMember(false)
	productsGen.SetSendReqKafka(true)
	if estimateEtpEtdRpc := rpc_process.NewGetMultiEtpEtdInfo(productsGen.BaseReqData); estimateEtpEtdRpc != nil {
		productsGen.RegisterAfterPriceRPCProcess(estimateEtpEtdRpc)
	}
	return &Service{generator: productsGen, bizTag: request.BizTag, destType: destType, destInfo: destInfo}, nil
}

func (s *Service) Do(ctx context.Context, request *proto.HomePageCallCarEstimateReq) (data *proto.HomePageCallCarEstimateDataV2, err error) {
	var (
		products []*biz_runtime.ProductInfoFull
	)
	defer func() {
		if err == nil && data != nil {
			s.writePublicLog(ctx, products)
		}
	}()
	if products, err = s.generator.GenProducts(ctx); err != nil {
		// 开城为空不打warning日志
		if err.Error() == consts.ErrorGetFromDDSFail.Error() {
			log.Trace.Infof(ctx, trace.DLTagUndefined, "before render err is %v", err)
			return
		}
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "before render err is %v", err)

		return
	}
	if len(products) == 0 {
		return
	}

	// if products = filterSpecialScene(ctx, products); len(products) == 0 {
	//	return
	// }

	renderService := render.NewRender(ctx, products, s.bizTag, s.destType, request.AppVersion,
		util.ToString(request.AccessKeyId), uint64(s.generator.BaseReqData.PassengerInfo.PID), s.destInfo)

	data = render.Do(renderService)

	return
}

func buildDestInfo(ctx context.Context, bizTag string, pid uint64, req *proto.HomePageCallCarEstimateReq) (string, *model.PositionInfo) {
	if req.ToLng != 0 && req.ToLat != 0 {
		return consts.CallCarDestTypeGuess, &model.PositionInfo{
			ToLat:             req.ToLat,
			ToLng:             req.ToLng,
			ToPOIID:           req.ToPoiId,
			ToPOIType:         req.ToPoiType,
			ToAddress:         req.ToAddress,
			ToAddressAll:      req.ToAddressAll,
			ToName:            req.ToName,
			SearchId:          req.SearchId,
			ToPoiCode:         req.ToPoiCode,
			IsRecommendAbsorb: req.ToIsRecommendAbsorb,
			CityId:            req.ToCityId,
			CoordinateType:    req.CoordinateType,
			ToCityName:        req.ToCityName,
		}
	}

	dapanLastBubbleDestSwitch := apollo.FeatureToggle(ctx, "home_page_dapan_last_bubble_dest_switch", util.ToString(pid), map[string]string{})
	if bizTag == consts.CallCarBizTagDefault && !dapanLastBubbleDestSwitch {
		return "", nil
	}
	if req.LastDestPoiInfoStr == "" {
		return "", nil
	}

	lastDestInfo := &model.LastDestPoiInfo{}
	if err := json.Unmarshal([]byte(req.LastDestPoiInfoStr), &lastDestInfo); err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "LastDestPoiInfoStr json unmarshal err is %v", err)
		return "", nil
	}

	if err := json.Unmarshal([]byte(req.LastDestPoiInfoStr), &lastDestInfo); err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "LastDestPoiInfoStr json unmarshal err is %v", err)
		return "", nil
	}

	if lastDestInfo == nil || lastDestInfo.Lng == 0 || lastDestInfo.Lat == 0 || lastDestInfo.PoiId == "" {
		return "", nil
	}

	maxTimeLimitStr := dcmp.GetDcmpContent(ctx, "home_page-last_dest_max_time_limit", nil)
	maxTimeLimit := util.ToInt64(maxTimeLimitStr)
	if lastDestInfo.UpdateTime > 0 && time.Now().Unix()-lastDestInfo.UpdateTime > maxTimeLimit {
		return "", nil
	}

	return consts.CallCarDestTypeLastBubble, &model.PositionInfo{
		ToLat:             lastDestInfo.Lat,
		ToLng:             lastDestInfo.Lng,
		ToPOIID:           lastDestInfo.PoiId,
		ToPOIType:         lastDestInfo.SrcTag,
		ToAddressAll:      lastDestInfo.AddressAll,
		ToAddress:         lastDestInfo.Address,
		ToName:            lastDestInfo.DisplayName,
		SearchId:          lastDestInfo.SearchId,
		ToPoiCode:         lastDestInfo.DestPoiCode,
		IsRecommendAbsorb: lastDestInfo.IsRecommendAbsorb,
		CityId:            lastDestInfo.CityId,
		CoordinateType:    lastDestInfo.CoordinateType,
		ToCityName:        lastDestInfo.CityName,
	}
}
