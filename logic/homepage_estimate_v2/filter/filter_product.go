package filter

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	PassengerUtil "git.xiaojukeji.com/gulfstream/passenger-common/util"
)

type FilterProducts struct {
	preferenceProduct []int64
}

type FilterProductsAfterAthena struct {
}

func NewFilterProducts(ctx context.Context, pid string, callCarBizTag string) *FilterProducts {
	preferenceProduct := getPreferenceProduct(ctx, pid, callCarBizTag)

	return &FilterProducts{
		preferenceProduct: preferenceProduct,
	}
}

func (f *FilterProducts) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	var (
		allRemoved = make([]models.ProductCategory, 0)
	)
	for _, p := range products {

		if !util.InArrayInt64(p.ProductCategory, f.preferenceProduct) {
			allRemoved = append(allRemoved, models.ProductCategory(p.ProductCategory))
		}
	}
	return allRemoved, ""
}

func getPreferenceProduct(ctx context.Context, pid string, callCarBizTag string) []int64 {
	// 大盘人群走拼成乐
	if callCarBizTag == consts.CallCarBizTagDefault {
		return []int64{PassengerUtil.ProductCategoryLowPriceCarpool}
	}

	// 新流人群默认走快车和特惠
	return []int64{
		PassengerUtil.ProductCategoryFast,
		PassengerUtil.ProductCategoryFastSpecialRate,
		PassengerUtil.ProductCategorySpecialRate,
	}
}

// NewFilterProductsAfterAthena 调用算法后，如果因为异常情况返回了拼车+独乘的混合，优先出拼车
func NewFilterProductsAfterAthena() *FilterProductsAfterAthena {
	return &FilterProductsAfterAthena{}
}

func (f *FilterProductsAfterAthena) HandlerFilter(ctx context.Context, productMap map[int64]*biz_runtime.ProductInfoFull) (filter []models.ProductCategory) {
	var (
		pinCheArr = make([]models.ProductCategory, 0)
		otherArr  = make([]models.ProductCategory, 0)
	)

	for _, p := range productMap {
		if p.Product.ProductCategory == PassengerUtil.ProductCategoryLowPriceCarpool {
			pinCheArr = append(pinCheArr, models.ProductCategory(p.Product.ProductCategory))
		} else {
			otherArr = append(otherArr, models.ProductCategory(p.Product.ProductCategory))
		}
	}

	if len(pinCheArr) > 0 {
		return otherArr
	} else {
		return []models.ProductCategory{}
	}
}

type MismatchPaymentInfo struct {
}

func (f MismatchPaymentInfo) Do(_ context.Context, products []*biz_runtime.ProductInfoFull) []models.ProductCategory {
	var payType, businessConstSet string
	var allProducts []models.ProductCategory
	for _, product := range products {
		allProducts = append(allProducts, models.ProductCategory(product.GetProductCategory()))
	}
	for i, product := range products {
		if i == 0 {
			payType = strconv.Itoa(int(product.GetDefaultPayType()))
			businessConstSet = strconv.Itoa(int(product.GetBusinessConstSetByPayType(product.GetDefaultPayType())))
			continue
		}
		if payType != strconv.Itoa(int(product.GetDefaultPayType())) ||
			businessConstSet != strconv.Itoa(int(product.GetBusinessConstSetByPayType(product.GetDefaultPayType()))) {
			return allProducts
		}
	}
	return nil
}

func NewFilterMismatchPaymentInfo() *MismatchPaymentInfo {
	return &MismatchPaymentInfo{}
}
