package assembler

import (
	"context"
	"fmt"

	"git.xiaojukeji.com/gulfstream/bronze-door-sdk-go/common/utils"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/homepage_estimate_v2/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	PassengerUtil "git.xiaojukeji.com/gulfstream/passenger-common/util"
	"github.com/spf13/cast"
)

type Assembler interface {
	SetParams(ctx context.Context, params Params) error
	AssembleDestinationInfo(ctx context.Context) (*proto.HomePDestinationInfo, error)
	AssembleFeeDescList(ctx context.Context) ([]*proto.HomePFeeDesc, error)
	AssembleLeftTagInfo(ctx context.Context) (*proto.HomePLeftTagInfo, error)
	AssemblePopUp(ctx context.Context) (*proto.NewOrderPopUp, error)
	AssembleButtonInfo(ctx context.Context) (*proto.HomePCallCarButtonInfo, error)
	AssembleFeeMsg(ctx context.Context) (string, error)
	AssembleTitle(ctx context.Context) (string, error)
}

type Params struct {
	Products        []*biz_runtime.ProductInfoFull `json:"-"`
	EstimateTraceId string                         `json:"-"`
	BizTag          string                         `json:"biz_tag"`
	DestType        string                         `json:"-"`
	AccessKeyId     string                         `json:"access_key_id"`
	DestInfo        *model.PositionInfo            `json:"-"`
	material        material
}

func (p *Params) getXengineParams() map[string]string {
	ret := map[string]string{
		"access_key_id": p.AccessKeyId,
		"biz_tag":       p.BizTag,
		"caller":        "mamba",
	}
	if len(p.Products) > 0 {
		ret["pid"] = cast.ToString(p.Products[0].GetUserPID())
		ret["city_id"] = cast.ToString(p.Products[0].GetCityID())
		ret["app_version"] = p.Products[0].GetAppVersion()
		ret["lang"] = p.Products[0].GetLang()
	}
	return ret
}

type DefaultAssembler struct {
	Params
	carpoolPrice float64
}

func (d *DefaultAssembler) SetParams(ctx context.Context, params Params) error {
	if len(params.Products) != 1 || params.Products[0].GetProductCategory() != PassengerUtil.ProductCategoryLowPriceCarpool {
		return fmt.Errorf("invalid product category")
	}
	d.Params = params
	d.carpoolPrice = buildCarpoolPrice(params.Products[0])
	return nil
}

func (d *DefaultAssembler) AssembleDestinationInfo(_ context.Context) (*proto.HomePDestinationInfo, error) {
	return &proto.HomePDestinationInfo{
		SearchId:            d.DestInfo.SearchId,
		ToLng:               d.DestInfo.ToLng,
		ToLat:               d.DestInfo.ToLat,
		ToAddress:           d.DestInfo.ToAddress,
		ToAddressAll:        d.DestInfo.ToAddressAll,
		ToName:              d.DestInfo.ToName,
		ToPoiId:             d.DestInfo.ToPOIID,
		ToCity:              d.DestInfo.CityId,
		ToPoiType:           d.DestInfo.ToPOIType,
		ToCityName:          d.DestInfo.ToCityName,
		ToPoiCode:           d.DestInfo.ToPoiCode,
		ToIsRecommendAbsorb: d.DestInfo.IsRecommendAbsorb,
		ToCoordinateType:    d.DestInfo.CoordinateType,
	}, nil
}

func (d *DefaultAssembler) AssembleFeeDescList(ctx context.Context) ([]*proto.HomePFeeDesc, error) {
	var (
		feeDescList = make([]*proto.HomePFeeDesc, 0)
	)

	saveMoney := util.FormatPrice(d.Products[0].GetPreTotalFee()-d.carpoolPrice, 1)

	feeDesc := &proto.HomePFeeDesc{
		BorderColor: d.material.FeeMaterial.NormalDesc.BorderColor,
		Content: dcmp.TranslateTemplate(d.material.FeeMaterial.NormalDesc.DescTemplate,
			map[string]string{"save_money": saveMoney}),
		Icon:      d.material.FeeMaterial.NormalDesc.Icon,
		TextColor: d.material.FeeMaterial.NormalDesc.TextColor,
	}
	feeDescList = append(feeDescList, feeDesc)
	return feeDescList, nil
}

func (d *DefaultAssembler) AssembleLeftTagInfo(ctx context.Context) (*proto.HomePLeftTagInfo, error) {
	leftTagInfo := &proto.HomePLeftTagInfo{
		Icon: utils.StringPtr(d.material.TagMaterial.Icon),
	}

	sceneTags := make([]*proto.HomePSceneTag, 0)
	if etpInfo := buildEtpInfo(ctx, &d.material.TagMaterial.EtpInfo, d.Products); etpInfo != nil {
		sceneTags = append(sceneTags, etpInfo)
	}

	leftTagInfo.SetSceneTags(sceneTags)
	return leftTagInfo, nil
}

func (d *DefaultAssembler) AssemblePopUp(ctx context.Context) (*proto.NewOrderPopUp, error) {
	return nil, nil
}

func (d *DefaultAssembler) AssembleButtonInfo(ctx context.Context) (*proto.HomePCallCarButtonInfo, error) {
	jumpLink := d.material.ButtonMaterial.JumpLink
	button := buildButton(ctx, &d.material.ButtonMaterial)
	button.CallCarLinkInfo = &proto.CallCarLinkInfo{
		LinkType: callCarLinkTypeInterior,
		Link:     jumpLink,
	}
	return button, nil
}

func (d *DefaultAssembler) AssembleFeeMsg(ctx context.Context) (string, error) {
	fee := util.FormatPrice(d.carpoolPrice, 1)
	feeTemplate := fee_info_render.GetNormalFeeMsgTemplate(ctx, d.Products[0])
	return dcmp.TranslateTemplate(feeTemplate, map[string]string{
		"num": dcmp.TranslateTemplate(d.material.FeeMaterial.SingleRichText,
			map[string]string{"fee": fee}),
	}), nil
}

func (d *DefaultAssembler) AssembleTitle(ctx context.Context) (string, error) {
	return dcmp.TranslateTemplate(d.material.CardMaterial.TitleTemplate, map[string]string{
		"to_name": d.Products[0].BaseReqData.AreaInfo.ToName,
	}), nil
}

func buildCard(ctx context.Context, assembler Assembler) (*proto.HomePageCallCarCard, error) {
	var (
		cardInfo = &proto.HomePageCallCarCard{}
		err      error
	)
	// 构建标题
	cardInfo.Title, err = assembler.AssembleTitle(ctx)
	if err != nil {
		return nil, err
	}

	// 构建费用信息
	cardInfo.FeeMsg, err = assembler.AssembleFeeMsg(ctx)
	if err != nil {
		return nil, err
	}

	// 构建优惠信息
	cardInfo.FeeDescList, err = assembler.AssembleFeeDescList(ctx)
	if err != nil {
		return nil, err
	}

	// 构建左侧标签信息
	cardInfo.LeftTagInfo, err = assembler.AssembleLeftTagInfo(ctx)
	if err != nil {
		return nil, err
	}
	return cardInfo, nil
}

func Assemble(ctx context.Context, params Params) (*proto.HomePageCallCarEstimateDataV2, error) {
	ret := &proto.HomePageCallCarEstimateDataV2{}
	// 新流首页不需要返回一键叫的渲染字段，只需要不为nil就行
	if params.BizTag == consts.CallCarBizTagNewLossHomePage {
		return ret, nil
	}
	var assembler Assembler = &DefaultAssembler{}
	if params.BizTag == consts.CallCarBizTagXinliu {
		assembler = &NewLossAssembler{}
	}

	// 根据传参获取xengin物料
	allow, err := params.getMaterialByXEngine(ctx)
	if !allow {
		return nil, err
	}

	// 检查参数
	err = assembler.SetParams(ctx, params)
	if err != nil {
		return nil, err
	}

	// 构建卡片信息
	cardInfo, err := buildCard(ctx, assembler)
	if err != nil {
		return nil, err
	}
	ret.CardInfo = cardInfo

	// 构建弹窗信息
	ret.NewOrderPopUp, err = assembler.AssemblePopUp(ctx)
	if err != nil {
		return nil, err
	}

	// 构建按钮信息
	ret.ButtonInfo, err = assembler.AssembleButtonInfo(ctx)
	if err != nil {
		return nil, err
	}

	// 构建终点信息
	ret.DestinationInfo, err = assembler.AssembleDestinationInfo(ctx)
	if err != nil {
		return nil, err
	}

	return ret, nil
}
