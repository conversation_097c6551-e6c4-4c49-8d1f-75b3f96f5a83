package anycar_v1

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/dao/ufs_logic"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/nuwa/trace"
)

type BaseParams struct {
	OrderInfo    *dos.OrderInfo
	UserInfo     *passport.UserInfo
	OrderFeature *ufs.OrderFeature
}

func initParams(ctx context.Context, req *proto.AnyCarEstimateReq) (params *BaseParams, errno int) {
	if req == nil || "" == req.OrderId || "" == req.Token || "" == req.AppVersion || 0 == req.AccessKeyId {
		return nil, consts.ErrnoParams
	}

	//请求 passport
	userInfo, err := passport.GetUserInfo(ctx, req.Token, "")
	if err != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "getUserInfo: %v", err)
		return nil, consts.ErrnoGetUserInfo
	}

	orderID, district, err := util.DecodeOrderID(req.OrderId)
	if err != nil {
		log.Trace.Errorf(ctx, trace.DLTagUndefined, "decodeErr: %v", err)
		return nil, consts.ErrnoDecode
	}

	// 请求 dos
	orderInfo, err := dos.GetOrderInfo(ctx, orderID, district)
	if err != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "getOrderInfo: %v", err)
		return nil, consts.ErrnoGetOrderInfo
	}

	orderFeature, err := ufs_logic.GetOrderFeature(ctx, orderInfo, req)
	if err != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "getOrderFeature: %v", err)
		return nil, consts.ErrnoGetOrderFeature
	}

	// 订单归属校验
	if orderInfo.PassengerId != strconv.FormatInt(int64(userInfo.PID), 10) {
		return nil, consts.ErrnoOrderNotBelongUser
	}

	// 设置PageType
	req.PageType = util.Int32Ptr(page_type.PageTypeGuideAnyCar)

	params = &BaseParams{
		OrderInfo:    orderInfo,
		UserInfo:     userInfo,
		OrderFeature: orderFeature,
	}
	return params, consts.NoErr
}

func InitProductGenerator(ctx context.Context, req *proto.AnyCarEstimateReq) (*biz_runtime.ProductsGenerator, int) {
	var (
		errno       int
		err         error
		baseParams  *BaseParams
		productsGen *biz_runtime.ProductsGenerator
	)

	baseParams, errno = initParams(ctx, req)
	if consts.NoErr != errno {
		return nil, errno
	}

	productsGen, err = biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("pAnycarEstimate"),
		biz_runtime.WithOrderInfo(ctx, req, baseParams.OrderInfo, baseParams.UserInfo, baseParams.OrderFeature),
		biz_runtime.WithInfoByRequest(ctx, req),
	)
	if err != nil {
		return nil, consts.GetErrorNum(err)
	}

	productsGen.SetNeedMember(true)

	productsGen.SetSendReqKafka(true)

	// 注册过滤器
	RegisterProductFilter(ctx, baseParams, productsGen)

	// 注册RPC调用
	RegisterRpcProcessWithBasicProducts(ctx, baseParams, productsGen)

	return productsGen, consts.NoErr
}

func GenProducts(ctx context.Context, generator *biz_runtime.ProductsGenerator) ([]*biz_runtime.ProductInfoFull, error) {
	var (
		err      error
		products []*biz_runtime.ProductInfoFull
	)
	if generator == nil {
		return nil, nil
	}
	products, err = generator.GenProducts(ctx)
	if err != nil {
		return nil, err
	}
	return products, nil
}
