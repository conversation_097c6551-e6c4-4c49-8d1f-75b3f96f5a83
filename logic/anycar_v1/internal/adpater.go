package internal

import (
	"fmt"
	"strconv"

	EstimateDecision "git.xiaojukeji.com/dirpc/dirpc-go-http-EstimateDecision"
	"git.xiaojukeji.com/gulfstream/mamba/models/apollo_model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/payment"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/plain_text_render"
	"github.com/spf13/cast"
)

// AnyCarAdapter 追加适配器
type AnyCarAdapter struct {
	*biz_runtime.ProductInfoFull
	apolloParams *plain_text_render.ApolloParams

	_pc *apollo_model.ParamsConnector
}

func (a *AnyCarAdapter) ApolloParamsGen(keyFunc func(param *apollo_model.ParamsConnector) string, paramsFunc ...func(param *apollo_model.ParamsConnector) (key, value string)) (key string, params map[string]string) {
	if a._pc == nil {
		a._pc = &apollo_model.ParamsConnector{
			City:        fmt.Sprintf("%d", a.GetCityID()),
			Phone:       a.GetUserPhone(),
			UID:         fmt.Sprintf("%d", a.GetUID()),
			PID:         fmt.Sprintf("%d", a.GetUserPID()),
			Lang:        a.GetLang(),
			AccessKeyID: fmt.Sprintf("%d", a.GetAccessKeyId()),
			AppVersion:  a.GetAppVersion(),
		}

		a._pc.SetProductCategory(strconv.FormatInt(a.GetProductCategory(), 10)).
			SetCounty(cast.ToString(a.GetFromCounty())).
			SetIsCrossCity(cast.ToString(cast.ToInt64(a.GetCityID() != a.GetToCityID()))).
			SetMenuID("dache_anycar")
	}

	return a._pc.ApolloParamsGen(keyFunc, paramsFunc...)
}

func (a *AnyCarAdapter) GetTimeSpan() []*EstimateDecision.TimeSpanV2 {
	bizInfo := a.GetBizInfo()
	if bizInfo == nil || len(bizInfo.TimeSpan) == 0 {
		return nil
	}
	return bizInfo.TimeSpan
}

func (a *AnyCarAdapter) GetBonus() float64 {
	fee := a.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}
	amount := fee.GetFeeDetail().GetBonusAmount()
	if amount == nil {
		return 0
	}
	return *amount
}

func (a *AnyCarAdapter) GetCoupon() *fee_info_render.FeeItem {
	fee := a.GetDirectEstimatePrice()
	if fee == nil {
		return nil
	}
	coupon := fee.GetFeeDetail().GetCoupon()
	if coupon == nil {
		return nil
	}
	return &fee_info_render.FeeItem{
		Tag:    coupon.Tag,
		Amount: coupon.Amount,
	}
}

func (a *AnyCarAdapter) GetMaxSeatNum() int32 {
	bizInfo := a.GetBizInfo()
	if bizInfo == nil {
		return 1
	}
	return bizInfo.MaxCarpoolSeatNum
}
func (a *AnyCarAdapter) GetUserSelectSeatNum() int32 {
	bizInfo := a.GetBizInfo()
	if bizInfo == nil {
		return 1
	}
	return bizInfo.CarpoolSeatNum
}

func (a *AnyCarAdapter) GetAllPaymentOptions() []payment.PaymentOption {
	stringUnpack := func(s *string) string {
		if s == nil {
			return ""
		}
		return *s
	}

	opts := a.GetAllPaymentOption()
	result := make([]payment.PaymentOption, 0, len(opts))
	for _, opt := range opts {
		if opt.Disabled != nil && *opt.Disabled == 1 {
			continue
		}
		result = append(result, payment.PaymentOption{
			PaymentType:      opt.PayType,
			BusinessConstSet: opt.BusinessConstSet,
			ChannelName:      stringUnpack(opt.ChannelName),
		})
	}
	return result
}

func (a *AnyCarAdapter) GetCurrentPaymentType() int {
	opts := a.GetAllPaymentOption()
	for _, opt := range opts {
		if opt.IsSelected != nil && *opt.IsSelected == 1 {
			return int(opt.PayType)
		}
	}
	return 0
}

func (a *AnyCarAdapter) GetCityID() int {
	area := a.GetAreaInfo()
	return int(area.Area)
}

func (a *AnyCarAdapter) GetPID() int64 {
	user := a.GetUserInfo()
	if user == nil {
		return 0
	}

	return user.PID
}

func (a *AnyCarAdapter) GetMultiRequireProduct() []models.RequireProduct {
	if a.BaseReqData == nil {
		return nil
	}

	return a.BaseReqData.CommonBizInfo.MultiRequireProduct
}

func (a *AnyCarAdapter) GetMenuId() string {
	return a.BaseReqData.CommonInfo.MenuID
}

func (a *AnyCarAdapter) GetUserChoosePayment() int32 {
	if a.BaseReqData == nil {
		return 0
	}
	return a.BaseReqData.CommonInfo.PaymentsType
}

func (a *AnyCarAdapter) GetCarpoolSeatNum() int32 {
	if a.BaseReqData == nil {
		return 0
	}
	return a.BaseReqData.CommonBizInfo.CarpoolSeatNum
}

func (a *AnyCarAdapter) GetProductCheckStatus() int {
	return a.GetBizInfo().CheckStatus
}

func (a *AnyCarAdapter) GetBillFeeDetailInfoFee(key string) (bool, float64) {
	if len(a.GetBillFeeDetailInfo()) < 1 {
		return false, 0
	}
	return true, a.GetBillFeeDetailInfo()[key]
}

func (a *AnyCarAdapter) GetFullProduct() *biz_runtime.ProductInfoFull {
	return a.ProductInfoFull
}
