package homepage_estimate_common

import (
	"context"

	broker "git.xiaojukeji.com/dirpc/dirpc-go-thrift-RouteBrokerService"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"github.com/spf13/cast"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

// 多维度过滤
type CombinePriceFilter struct {
	BizTag string
}

func (f *CombinePriceFilter) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) []models.ProductCategory {
	if len(products) == 0 {
		return nil
	}

	pd := products[0]
	if pd.GetBillDetail() == nil {
		return nil
	}

	allProducts := []models.ProductCategory{}
	for _, product := range products {
		allProducts = append(allProducts, models.ProductCategory(product.GetProductCategory()))
	}

	// 直线距离 单位米
	src := &broker.Base_GeoPoint{Latitude: pd.GetAreaInfo().FromLat, Longitude: pd.GetAreaInfo().FromLng}
	dst := &broker.Base_GeoPoint{Latitude: pd.GetAreaInfo().ToLat, Longitude: pd.GetAreaInfo().ToLng}
	straight_metre := cast.ToInt64(util.GeodesicDistance(src, dst))

	// 行驶距离 单位米
	driver_metre := pd.GetBillDetail().DriverMetre
	// 行驶时间 单位分钟
	driver_minute := pd.GetBillDetail().DriverMinute

	param := ApolloModel.NewUser(cast.ToString(pd.GetUserInfo().PID)).
		With("straight_metre", cast.ToString(straight_metre)).
		With("driver_metre", cast.ToString(driver_metre)).
		With("driver_minute", cast.ToString(driver_minute)).
		With("app_version", pd.GetClientInfo().AppVersion).
		With("access_key_id", cast.ToString(pd.GetClientInfo().AccessKeyID)).
		With("biz_tag", f.BizTag)

	toggle, err := ApolloSDK.FeatureToggle("gs_one_call_car_combine_conf", param)
	if err != nil || !toggle.IsAllow() {
		return allProducts
	}

	return nil
}
