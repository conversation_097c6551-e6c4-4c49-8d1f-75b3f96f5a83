package abroad_estimate

import (
	"context"
	"testing"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/golibs/zerolog/ddlog"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

// 生成Mock数据的辅助函数
func generateMockProductsGenerator(countryIsoCode string) *biz_runtime.ProductsGenerator {
	baseReqData := &models.BaseReqData{
		AreaInfo: models.AreaInfo{
			CountryIsoCode: &countryIsoCode,
		},
	}

	pg := &biz_runtime.ProductsGenerator{
		BaseReqData: baseReqData,
	}
	return pg
}

// 生成Mock的dcmp配置数据
func generateMockDcmpConfig(countryCode, img, bottomText, buttonText, buttonMsg string) string {
	if countryCode == "" {
		return `{
			"img": "` + img + `",
			"bottom_text": "` + bottomText + `",
			"button_text": "` + buttonText + `",
			"button_msg": "` + buttonMsg + `"
		}`
	}

	return `{
		"` + countryCode + `": "` + img + `",
		"img": "default_img",
		"bottom_text": "` + bottomText + `",
		"button_text": "` + buttonText + `",
		"button_msg": "` + buttonMsg + `"
	}`
}

func TestBizLogic_BuildNoCarInfo(t *testing.T) {

	t.Run("成功场景-正常获取无车信息", func(t *testing.T) {
		// 准备测试数据
		countryCode := "US"
		pg := generateMockProductsGenerator(countryCode)
		bizLogic := &BizLogic{
			generator: pg,
		}

		// Mock dcmp配置数据
		mockConfig := generateMockDcmpConfig(countryCode, "us_img_url", "暂无可用车辆", "重新预估", "重新预估消息")
		mockey.Mock(dcmp.GetDcmpContent).Return(mockConfig).Build()
		mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build()

		defer mockey.UnPatchAll()

		// 执行测试
		result := bizLogic.BuildNoCarInfo(context.Background())

		// 断言结果
		assert.NotNil(t, result, "应该返回非nil的无车信息")
		assert.Equal(t, "us_img_url", result.Img, "应该返回正确的图片URL")
		assert.Equal(t, "暂无可用车辆", result.BottomText, "应该返回正确的底部文案")
		assert.Equal(t, "重新预估", result.ButtonText, "应该返回正确的按钮文案")
		assert.Equal(t, "重新预估消息", result.ButtonMsg, "应该返回正确的按钮消息")
	})

	t.Run("成功场景-使用默认图片", func(t *testing.T) {
		// 准备测试数据 - 使用不存在的国家代码
		countryCode := "XX"
		pg := generateMockProductsGenerator(countryCode)
		bizLogic := &BizLogic{
			generator: pg,
		}

		// Mock dcmp配置数据 - 不包含XX国家的配置
		mockConfig := generateMockDcmpConfig("", "default_img", "暂无可用车辆", "重新预估", "重新预估消息")
		mockey.Mock(dcmp.GetDcmpContent).Return(mockConfig).Build()
		mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build()

		defer mockey.UnPatchAll()

		// 执行测试
		result := bizLogic.BuildNoCarInfo(context.Background())

		// 断言结果 - 应该使用默认的img字段
		assert.NotNil(t, result, "应该返回非nil的无车信息")
		assert.Equal(t, "default_img", result.Img, "应该返回默认图片URL")
		assert.Equal(t, "暂无可用车辆", result.BottomText, "应该返回正确的底部文案")
		assert.Equal(t, "重新预估", result.ButtonText, "应该返回正确的按钮文案")
		assert.Equal(t, "重新预估消息", result.ButtonMsg, "应该返回正确的按钮消息")
	})

	t.Run("边界场景-dcmp配置为空", func(t *testing.T) {
		// 准备测试数据
		countryCode := "US"
		pg := generateMockProductsGenerator(countryCode)
		bizLogic := &BizLogic{
			generator: pg,
		}

		// Mock dcmp配置数据为空
		mockey.Mock(dcmp.GetDcmpContent).Return("").Build()
		mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build()

		defer mockey.UnPatchAll()

		// 执行测试
		result := bizLogic.BuildNoCarInfo(context.Background())

		// 断言结果
		assert.Nil(t, result, "当dcmp配置为空时应该返回nil")
	})

	t.Run("边界场景-dcmp配置为无效JSON", func(t *testing.T) {
		// 准备测试数据
		countryCode := "US"
		pg := generateMockProductsGenerator(countryCode)
		bizLogic := &BizLogic{
			generator: pg,
		}

		// Mock dcmp配置数据为无效JSON
		mockey.Mock(dcmp.GetDcmpContent).Return("invalid json").Build()
		mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build()

		defer mockey.UnPatchAll()

		// 执行测试
		result := bizLogic.BuildNoCarInfo(context.Background())

		// 断言结果 - gjson会处理无效JSON，返回空字符串
		assert.NotNil(t, result, "应该返回非nil的无车信息")
		assert.Equal(t, "", result.Img, "无效JSON时图片URL应该为空")
		assert.Equal(t, "", result.BottomText, "无效JSON时底部文案应该为空")
		assert.Equal(t, "", result.ButtonText, "无效JSON时按钮文案应该为空")
		assert.Equal(t, "", result.ButtonMsg, "无效JSON时按钮消息应该为空")
	})

	t.Run("边界场景-generator为nil", func(t *testing.T) {
		// 准备测试数据 - generator为nil
		bizLogic := &BizLogic{
			generator: nil,
		}

		// Mock dcmp配置数据
		mockConfig := generateMockDcmpConfig("", "default_img", "暂无可用车辆", "重新预估", "重新预估消息")
		mockey.Mock(dcmp.GetDcmpContent).Return(mockConfig).Build()
		mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build()

		defer mockey.UnPatchAll()

		// 执行测试 - 这个场景会导致panic，因为代码没有检查generator是否为nil
		// 在实际业务中，generator不应该为nil，所以这个测试用例验证了代码的健壮性
		assert.Panics(t, func() {
			bizLogic.BuildNoCarInfo(context.Background())
		}, "当generator为nil时应该panic")
	})

	t.Run("边界场景-CountryIsoCode为nil", func(t *testing.T) {
		// 准备测试数据 - CountryIsoCode为nil
		pg := &biz_runtime.ProductsGenerator{
			BaseReqData: &models.BaseReqData{
				AreaInfo: models.AreaInfo{
					CountryIsoCode: nil,
				},
			},
		}
		bizLogic := &BizLogic{
			generator: pg,
		}

		// Mock dcmp配置数据
		mockConfig := generateMockDcmpConfig("", "default_img", "暂无可用车辆", "重新预估", "重新预估消息")
		mockey.Mock(dcmp.GetDcmpContent).Return(mockConfig).Build()
		mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build()

		defer mockey.UnPatchAll()

		// 执行测试
		result := bizLogic.BuildNoCarInfo(context.Background())

		// 断言结果 - 当CountryIsoCode为nil时，GetCountryIsoCode会返回空字符串
		assert.NotNil(t, result, "应该返回非nil的无车信息")
		assert.Equal(t, "default_img", result.Img, "应该返回默认图片URL")
	})

	t.Run("成功场景-不同国家代码", func(t *testing.T) {
		// 准备测试数据
		testCases := []struct {
			countryCode string
			expectedImg string
		}{
			{"CN", "cn_img_url"},
			{"HK", "hk_img_url"},
			{"JP", "jp_img_url"},
			{"KR", "kr_img_url"},
			{"SG", "sg_img_url"},
		}

		for _, tc := range testCases {
			t.Run("国家代码_"+tc.countryCode, func(t *testing.T) {
				pg := generateMockProductsGenerator(tc.countryCode)
				bizLogic := &BizLogic{
					generator: pg,
				}

				// Mock dcmp配置数据
				mockConfig := `{
					"CN": "cn_img_url",
					"HK": "hk_img_url",
					"JP": "jp_img_url",
					"KR": "kr_img_url",
					"SG": "sg_img_url",
					"img": "default_img",
					"bottom_text": "暂无可用车辆",
					"button_text": "重新预估",
					"button_msg": "重新预估消息"
				}`
				mockey.Mock(dcmp.GetDcmpContent).Return(mockConfig).Build()
				mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build()

				defer mockey.UnPatchAll()

				// 执行测试
				result := bizLogic.BuildNoCarInfo(context.Background())

				// 断言结果
				assert.NotNil(t, result, "应该返回非nil的无车信息")
				assert.Equal(t, tc.expectedImg, result.Img, "应该返回正确的国家图片URL: %s", tc.countryCode)
				assert.Equal(t, "暂无可用车辆", result.BottomText, "应该返回正确的底部文案")
				assert.Equal(t, "重新预估", result.ButtonText, "应该返回正确的按钮文案")
				assert.Equal(t, "重新预估消息", result.ButtonMsg, "应该返回正确的按钮消息")
			})
		}
	})

	t.Run("成功场景-部分字段为空", func(t *testing.T) {
		// 准备测试数据
		countryCode := "US"
		pg := generateMockProductsGenerator(countryCode)
		bizLogic := &BizLogic{
			generator: pg,
		}

		// Mock dcmp配置数据 - 部分字段为空
		mockConfig := `{
			"US": "us_img_url",
			"img": "default_img",
			"bottom_text": "",
			"button_text": "",
			"button_msg": ""
		}`
		mockey.Mock(dcmp.GetDcmpContent).Return(mockConfig).Build()
		mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build()

		defer mockey.UnPatchAll()

		// 执行测试
		result := bizLogic.BuildNoCarInfo(context.Background())

		// 断言结果
		assert.NotNil(t, result, "应该返回非nil的无车信息")
		assert.Equal(t, "us_img_url", result.Img, "应该返回正确的图片URL")
		assert.Equal(t, "", result.BottomText, "底部文案应该为空")
		assert.Equal(t, "", result.ButtonText, "按钮文案应该为空")
		assert.Equal(t, "", result.ButtonMsg, "按钮消息应该为空")
	})
}
