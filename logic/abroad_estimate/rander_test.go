package abroad_estimate

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/simple_estimate/utils"
	"github.com/stretchr/testify/assert"
	"github.com/tidwall/gjson"
	apolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	"reflect"
	"testing"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/estimate_extra"
	"github.com/agiledragon/gomonkey/v2"
	apolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

//var log = logrus.New()

// 该测试函数由AI自动生成
func TestBizLogic_Render(t *testing.T) {
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Mock dcmp.GetDcmpContent
	patches.ApplyFunc(dcmp.GetDcmpContent, func(ctx context.Context, key string, params map[string]string) string {
		switch key {
		case "oversea-estimate_text":
			return `{"bottom_text":"底部文本","button_default":"默认按钮","button_need_protocol":"需要协议按钮","protocol_text":"协议文本","protocol_link":"协议链接"}`
		case "oversea-estimate_pc_seat_num":
			return `{"pc_1":"4座","ets_icon":"icon_url"}`
		default:
			return ""
		}
	})

	// Mock apollo.GetConfigsByNamespaceAndConditions
	patches.ApplyFunc(apollo.GetConfigsByNamespaceAndConditions, func(ctx context.Context, namespace string, conditions *apolloModel.Condition) ([]byte, error) {
		if namespace == "oversea_recommend_info" {
			return []byte(`[{
                "product_category": 1,
                "theme_data": {
                    "bg_gradients": ["#FF0000", "#00FF00"],
                    "title": "推荐车型",
                    "title_color": "#FFFFFF",
                    "icon": "recommend_icon_url",
                    "border_color": "#000000"
                }
            }]`), nil
		}
		return nil, nil
	})

	// Mock GetExactEstimateFee 方法
	patches.ApplyMethod(reflect.TypeOf(&biz_runtime.ProductInfoFull{}), "GetExactEstimateFee",
		func(_ *biz_runtime.ProductInfoFull) float64 {
			return 100.0
		})

	// Mock GetUserInfo 方法
	patches.ApplyMethod(reflect.TypeOf(&biz_runtime.ProductInfoFull{}), "GetUserInfo",
		func(_ *biz_runtime.ProductInfoFull) *models.PassengerInfo {
			return &models.PassengerInfo{
				PID: 12345,
			}
		})

	// Mock car_info.GetCarName
	patches.ApplyFunc(car_info.GetCarName, func(ctx context.Context, prov car_info.CarNameProvider) string {
		return "测试车型"
	})

	// Mock car_info.GetCarIcon
	patches.ApplyFunc(car_info.GetCarIcon, func(ctx context.Context, prov car_info.CarIconProvider) string {
		return "test_icon_url"
	})

	// Mock GetFeeDetailUrl
	patches.ApplyFunc(estimate_extra.NewFeeDetailUrl().GetFeeDetailUrl, func(ctx context.Context) string {
		return "test_fee_detail_url"
	})

	// Mock fee_desc_engine.NewFeeEngine
	patches.ApplyFunc(fee_desc_engine.NewFeeEngine, func(input *model.FeeInput, env *model.Env) *fee_desc_engine.Engine {
		return &fee_desc_engine.Engine{}
	})

	// Mock GetOverseaEts
	patches.ApplyMethod(reflect.TypeOf(&biz_runtime.ProductInfoFull{}), "GetOverseaEts",
		func(_ *biz_runtime.ProductInfoFull) int64 {
			return 300
		})

	// Mock GetLang
	patches.ApplyMethod(reflect.TypeOf(&biz_runtime.ProductInfoFull{}), "GetLang",
		func(_ *biz_runtime.ProductInfoFull) string {
			return "zh-CN"
		})

	patches.ApplyFunc(util.GetTraceIDFromCtxWithoutCheck, func(ctx context.Context) string {
		return "test_trace_id"
	})

	b := &BizLogic{
		dcmpConfInfo: `{"fee_msg":"费用信息","bottom_text":"底部文本","button_default":"默认按钮"}`,
		pcSeatNum:    `{"pc_1":"4座","ets_icon":"icon_url"}`,
		generator: &biz_runtime.ProductsGenerator{
			BaseReqData: &models.BaseReqData{
				PassengerInfo: models.PassengerInfo{
					PID:   12345,
					Phone: "13800138000",
				},
				CommonInfo: models.CommonInfo{
					AccessKeyID: 1001,
					AppVersion:  "1.0.0",
				},
				AreaInfo: models.AreaInfo{
					Area: 1,
				},
			},
		},
	}
	// Mock apollo feature toggle
	patches.ApplyFunc(apolloSDK.FeatureToggle, func(key string, param *apolloModel.User) (*apolloModel.ToggleResult, error) {
		return &apolloModel.ToggleResult{}, nil
	})

	patches.ApplyMethod(&apolloModel.ToggleResult{}, "IsAllow", func(*apolloModel.ToggleResult) bool {
		return true
	})

	// Mock GetOverseaEts
	patches.ApplyMethod(reflect.TypeOf(&biz_runtime.ProductInfoFull{}), "GetOverseaEts",
		func(_ *biz_runtime.ProductInfoFull) int64 {
			return 300 // 5分钟
		})

	// Mock GetLang
	patches.ApplyMethod(reflect.TypeOf(&biz_runtime.ProductInfoFull{}), "GetLang",
		func(_ *biz_runtime.ProductInfoFull) string {
			return "zh-CN"
		})

	// Mock GetProductCategory
	patches.ApplyMethod(reflect.TypeOf(&biz_runtime.ProductInfoFull{}), "GetProductCategory",
		func(_ *biz_runtime.ProductInfoFull) int64 {
			return 1
		})

	// Mock fee_desc_engine.NewEnv
	patches.ApplyFunc(fee_desc_engine.NewEnv, func(pageType int32) *model.Env {
		return &model.Env{}
	})

	// Mock fee_desc_engine.NewFeeEngine
	patches.ApplyFunc(fee_desc_engine.NewFeeEngine, func(input *model.FeeInput, env *model.Env) *fee_desc_engine.Engine {
		mockEngine := &fee_desc_engine.Engine{}

		// Mock Do 方法
		patches.ApplyMethod(reflect.TypeOf(mockEngine), "Do",
			func(_ *fee_desc_engine.Engine, ctx context.Context) []*model.FeeOutput {
				return []*model.FeeOutput{
					{
						BorderColor:    "#000000",
						Content:        "基础费用",
						Icon:           "base_fee_icon",
						TextColor:      "#333333",
						HighLightColor: "#FF0000",
						Fee: &model.FeeDetail{
							Amount: 100.00,
							Type:   1,
						},
					},
				}
			})

		// Mock SetProductCategory 方法
		patches.ApplyMethod(reflect.TypeOf(mockEngine), "SetProductCategory",
			func(e *fee_desc_engine.Engine, category int32) *fee_desc_engine.Engine {
				return e
			})

		return mockEngine
	})

	// Mock input.BuildNormalFeeInput
	patches.ApplyFunc(input.BuildNormalFeeInput, func(ctx context.Context, prov model.FeeDescEngineInputProvider, pageType int) *model.FeeInput {
		return &model.FeeInput{}
	})

	tests := []struct {
		name     string
		products []*biz_runtime.ProductInfoFull
		want     *proto.OverseaEstimateData
	}{
		{
			name: "正常场景测试",
			products: []*biz_runtime.ProductInfoFull{
				{
					Product: &models.Product{
						ProductCategory: 1,
						EstimateID:      "test_id",
					},
					BaseReqData: &models.BaseReqData{
						AreaInfo: models.AreaInfo{
							Area: 1,
						},
					},
				},
			},
			want: &proto.OverseaEstimateData{
				BottomText:              "底部文本",
				IsSupportMultiSelection: 0,
				RecommendInfo: &proto.OverseaRecommendInfo{
					ThemeData: &proto.OverseaThemeData{
						BgGradients: []string{"#FF0000", "#00FF00"},
						Title:       "推荐车型",
						Icon:        "recommend_icon_url",
						BorderColor: "#000000",
					},
				},
				EstimateData: []*proto.OverseaEstimateCard{
					{
						EstimateId:      "test_id",
						ProductCategory: 1,
						FeeAmount:       "100",
						CarTitle:        "测试车型",
						CarIcon:         "test_icon_url",
						IsSelected:      1,
						CarTagList: []*proto.SubTitle{
							{
								Content: "5分钟",
							},
							{
								Content: "4座",
								IconUrl: "icon_url",
							},
						},
					},
				},
				FeeDetailUrl: "test_fee_detail_url",
				ButtonText:   "默认按钮",
			},
		},
	}

	// Mock renderFeeDescList 方法
	patches.ApplyPrivateMethod(reflect.TypeOf(&BizLogic{}), "renderFeeDescList",
		func(b *BizLogic, ctx context.Context, prov model.FeeDescEngineInputProvider) []*proto.NewFormFeeDesc {
			// 返回模拟的费用描述列表
			return []*proto.NewFormFeeDesc{
				{
					BorderColor: "#000000",
					Content:     "基础费用",
					Icon:        "base_fee_icon",
					//TextColor: proto.String("#333333"),
					//HighlightColor: proto.String("#FF0000"),
					// Amount:    "100.00",
					Type: 1,
				},
			}
		})

	// Mock ufs.GetFeatureV2
	patches.ApplyFunc(ufs.GetFeatureV2, func(ctx context.Context, domain, key string, params map[string]string) (string, error) {
		// 检查参数是否符合预期
		if domain == ufs.DomainPassenger && key == ufs.KeyOverseaProtocol {
			// 返回 "0" 表示需要授权
			// 返回 "1" 或其他非"0"值表示已授权
			// 这里我们返回 "0" 来模拟需要授权的情况
			return "0", nil

			// 如果要模拟已授权的情况，可以返回：
			// return "1", nil

			// 如果要模拟错误情况，可以返回：
			// return "", errors.New("mock error")
		}
		return "", nil
	})

	// Mock utils.GetConfigH5URL
	patches.ApplyFunc(utils.GetConfigH5URL, func(areaID int64, urlType string) (gjson.Result, error) {
		// 检查是否是费用详情页的请求
		if urlType == estimate_extra.FeeDetailH5New {
			// 创建一个模拟的 gjson.Result
			mockResult := gjson.Parse(`"https://example.com/fee-detail"`)
			return mockResult, nil

			// 如果要模拟错误情况，可以返回：
			// return nil, errors.New("mock error")

			// 如果要模拟配置不存在的情况：
			// mockResult := gjson.Parse(`null`)
			// return &mockResult, nil
		}
		return gjson.Result{}, nil
	})

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := b.Render(context.Background(), tt.products)
			assert.NotNil(t, got)
		})
	}
}

func TestGetRecommondConf(t *testing.T) {
	// Mock apollo.GetConfigsByNamespaceAndConditions
	patches := gomonkey.ApplyFunc(apollo.GetConfigsByNamespaceAndConditions, func(ctx context.Context, namespace string, conditions *apolloModel.Condition) ([]byte, error) {
		configs := []RecommendConfig{
			{
				PCID: 123,
				ThemeData: &RecommendData{
					BgGradients: []string{"#fff", "#000"},
					Title:       "Test Title",
					TitleColor:  "#fff",
					Icon:        "test-icon",
					BorderColor: "#000",
				},
			},
		}
		return json.Marshal(configs)
	})
	defer patches.Reset()

	tests := []struct {
		name     string
		bizLogic *BizLogic
		setup    func() *gomonkey.Patches
		validate func(t *testing.T, b *BizLogic)
	}{
		{
			name: "nil generator",
			bizLogic: &BizLogic{
				generator: nil,
			},
			setup: func() *gomonkey.Patches { return nil },
			validate: func(t *testing.T, b *BizLogic) {
				assert.Equal(t, int64(0), b.recommendPCID)
				assert.Nil(t, b.ThemeData)
			},
		},
		{
			name: "nil base request data",
			bizLogic: &BizLogic{
				generator: &biz_runtime.ProductsGenerator{
					BaseReqData: nil,
				},
			},
			setup: func() *gomonkey.Patches { return nil },
			validate: func(t *testing.T, b *BizLogic) {
				assert.Equal(t, int64(0), b.recommendPCID)
				assert.Nil(t, b.ThemeData)
			},
		},
		{
			name: "successful config fetch",
			bizLogic: &BizLogic{
				generator: &biz_runtime.ProductsGenerator{
					BaseReqData: &models.BaseReqData{
						AreaInfo: models.AreaInfo{
							Area: 123,
						},
					},
				},
			},
			setup: func() *gomonkey.Patches { return nil },
			validate: func(t *testing.T, b *BizLogic) {
				assert.Equal(t, int64(123), b.recommendPCID)
				assert.NotNil(t, b.ThemeData)
				assert.Equal(t, "Test Title", b.ThemeData.Title)
			},
		},
		//{
		//	name: "apollo error",
		//	bizLogic: &BizLogic{
		//		generator: &biz_runtime.ProductsGenerator{
		//			BaseReqData: &models.BaseReqData{
		//				AreaInfo: models.AreaInfo{
		//					Area: 123,
		//				},
		//			},
		//		},
		//	},
		//	setup: func() *gomonkey.Patches {
		//		return gomonkey.ApplyFunc(apollo.GetConfigsByNamespaceAndConditions, func(ctx context.Context, namespace string, conditions *apolloModel.Condition) ([]byte, error) {
		//			return nil, errors.New("apollo error")
		//		})
		//	},
		//	validate: func(t *testing.T, b *BizLogic) {
		//		assert.Nil(t, b.recommendPCID)
		//		assert.Nil(t, b.ThemeData)
		//	},
		//},
		//{
		//	name: "invalid json config",
		//	bizLogic: &BizLogic{
		//		generator: &biz_runtime.ProductsGenerator{
		//			BaseReqData: &models.BaseReqData{
		//				AreaInfo: models.AreaInfo{
		//					Area: 123,
		//				},
		//			},
		//		},
		//	},
		//	setup: func() *gomonkey.Patches {
		//		return gomonkey.ApplyFunc(apollo.GetConfigsByNamespaceAndConditions, func(ctx context.Context, namespace string, conditions *apolloModel.Condition) ([]byte, error) {
		//			return []byte("invalid json"), nil
		//		})
		//	},
		//	validate: func(t *testing.T, b *BizLogic) {
		//		assert.Equal(t, int64(0), b.recommendPCID)
		//		assert.Nil(t, b.ThemeData)
		//	},
		//},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if p := tt.setup(); p != nil {
				defer p.Reset()
			}
			ctx := context.Background()
			tt.bizLogic.getRecommendConf(ctx)
			tt.validate(t, tt.bizLogic)
		})
	}
}
