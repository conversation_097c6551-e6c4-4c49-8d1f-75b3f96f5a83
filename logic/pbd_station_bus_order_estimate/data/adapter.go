package data

import (
	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

// AdapterPbdOrderStationBus 批量
type AdapterPbdOrderStationBus struct {
	*biz_runtime.ProductInfoFull
}

func (inter *AdapterPbdOrderStationBus) GetDepartureTime() int64 {
	if inter.GetBizInfo() == nil || inter.GetBizInfo().StationInventoryInfo == nil {
		return time.Now().Unix()
	}
	return inter.GetBizInfo().StationInventoryInfo.SelectInfo.DepartureTime
}

func (inter *AdapterPbdOrderStationBus) GetShiftID() string {
	return inter.Product.ShiftID
}

func (inter *AdapterPbdOrderStationBus) GetFromStationInfo() *Prfs.StationInfo {
	if inter.GetBizInfo() == nil || inter.GetBizInfo().StationInventoryInfo == nil {
		return nil
	}
	return inter.GetBizInfo().StationInventoryInfo.SelectInfo.FromStationInfo
}
func (inter *AdapterPbdOrderStationBus) GetDestStationInfo() *Prfs.StationInfo {
	if inter.GetBizInfo() == nil || inter.GetBizInfo().StationInventoryInfo == nil {
		return nil
	}
	return inter.GetBizInfo().StationInventoryInfo.SelectInfo.DestStationInfo
}
