package intercity_common

import (
	"context"
	"github.com/spf13/cast"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/model"
)

const (
	// 城际换名灰度实验
	IntercityProductExperimentABName = "chengjinametest_ab"
	// 获取实验名称，字符串
	IntercityCarTitle = "car_title"
	// 获取实验名称的图片
	IntercityCarTitleImage = "car_image"
	// 库存模式，carpool_estimate入口
	CarpoolSkuEstimatePrice = "carpool_estimate_price_sku_func"
	// 全模式，carpool_estimate入口
	CarpoolEstimatePrice = "carpool_estimate_price_func"
)

type GeneralParams struct {
	City           int32
	ComboId        int32
	ProductId      int64
	AccessKeyId    int32
	AppVersion     string
	CarpoolType    int64
	ComboType      int64
	PassengerId    int64
	PassengerPhone string
	CallerFunc     string
	ExParamsName   string
}

// 获取城际实验名称
func GetExpProductNameStr(ctx context.Context, params *GeneralParams) string {
	if params == nil {
		return ""
	}
	params.ExParamsName = IntercityCarTitle
	return getExpResult(ctx, params)
}

// 获取城际名称实验的图片链接
func GetExpProductNameImage(ctx context.Context, params *GeneralParams) string {
	if params == nil {
		return ""
	}
	params.ExParamsName = IntercityCarTitleImage
	return getExpResult(ctx, params)
}

func getExpResult(ctx context.Context, params *GeneralParams) string {
	if params.PassengerId == 0 || len(params.ExParamsName) == 0 {
		return ""
	}
	passengerId := cast.ToString(params.PassengerId)
	apolloParams := map[string]string{
		"key":           passengerId,
		"phone":         params.PassengerPhone,
		"city":          cast.ToString(params.City),
		"combo_id":      cast.ToString(params.ComboId),
		"product_id":    cast.ToString(params.ProductId),
		"access_key_id": cast.ToString(params.AccessKeyId),
		"app_version":   params.AppVersion,
		"pid":           passengerId,
		"caller_func":   params.CallerFunc,
		"combo_type":    cast.ToString(params.ComboType),
	}
	user := model.NewUser(passengerId)
	for k, v := range apolloParams {
		user.With(k, v)
	}

	toggle, err := apollo.FeatureToggle(IntercityProductExperimentABName, user)
	if err != nil || !toggle.IsAllow() {
		return ""
	}
	assignment := toggle.GetAssignment()
	if assignment == nil {
		return ""
	}

	return assignment.GetParameter(params.ExParamsName, "")
}
