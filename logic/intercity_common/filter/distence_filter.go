package filter

import (
	"context"
	"sort"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/source_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

const (
	NSMaterial = "inter_carpool_city_conf"
	// 主表单配置
	IntercityDistanceThreshold = "gs_carpool_to_intercity_distance_threshold"
	// 个性化配置
	IntercityDistanceThresholdIndividuation = "gs_carpool_to_intercity_distance_threshold_new"
	// publicLog
	PublicKey = "g_estimate_distance_log"
	// 默认进入时
	InitReadStatus = -2
	// 配置获取成功
	ReadSuccessStatus = 1
	// 配置未获取成功
	ReadFailStatus = -1
)

type OpenFenceDistanceLimit struct {
	OpenFenceDistanceLower string `json:"open_fence_distance_lower_limit"`
	OpenFenceDistanceUpper string `json:"open_fence_distance_upper_limit"`
}

type CrossCityDistanceLimit struct {
	CrossCityDistanceLower string `json:"cross_city_distance_lower"`
	CrossCityDistanceUpper string `json:"cross_city_distance_upper"`
}

type DistanceLimitConf struct {
	OpenFenceDistanceLimit OpenFenceDistanceLimit `json:"open_fence_distance_limit"`
	CrossCityDistanceLimit CrossCityDistanceLimit `json:"cross_city_distance_limit"`
}
type TDistenceFilter struct {
	baseReq *models.BaseReqData
}

type tIntercityDistenceFilterConfig struct {
	UpperLimit        int64 // 上限
	SameCityThreshold int64 // 不跨城的阈值
	DiffCityThreshold int64 // 跨城的阈值
}

type innerDistanceConvert struct {
	FromCityID int
	ToCity     int
	PcID       int
	PageType   int
	Phone      string
	Channel    int64
}

// NewDistanceFilter ...
func NewDistanceFilter(baseReq *models.BaseReqData) *TDistenceFilter {
	return &TDistenceFilter{
		baseReq: baseReq,
	}
}

// 返回需要被remove的pc-id
// 业务逻辑(2022-01-27):
//  0. 如果 只有 拼成乐, 直接出, 不做其他限制
//  1. 如果 同时有 拼成乐和城际, 或者只有城际:
//     a. 如果跨城(起终点城市不同), 预计行驶距离在 20KM-100KM 范围时, 出城际, 距离根据(起点)城市配置,
//     b. 如果不跨城(起终点城市相同), 预计行驶距离在 30KM-100KM 范围时, 出城际, 距离根据(起点)城市配置,
//     c. 其他情况如果有拼成乐, 出拼成乐
//     d. 如果两个都未符合, 显示 拼车未开城
func (filter *TDistenceFilter) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) []models.ProductCategory {

	if len(products) == 0 {
		return nil
	}

	intercityProductCategory := make([]models.ProductCategory, 0)
	intercityThirdProductCategory := make([]models.ProductCategory, 0)
	pinchecheProductCategory := make([]models.ProductCategory, 0)
	// 对products的金额进行排序，后续展示城际三方取最低金额
	sort.Slice(products, func(i, j int) bool {
		return products[i].GetEstimateFee() < products[j].GetEstimateFee()
	})
	for _, prod := range products {
		if carpool.IsIntercityThirdPID(ctx, int(prod.GetProductId())) {
			intercityThirdProductCategory = append(intercityThirdProductCategory, models.ProductCategory(prod.GetProductCategory()))
			continue
		}
		if carpool.IsInterCityCarpool(prod.Product.CarpoolType, prod.Product.ComboType) {
			intercityProductCategory = append(intercityProductCategory, models.ProductCategory(prod.GetProductCategory()))
			continue
		}
		if carpool.IsPinCheChe(prod.Product.ProductCategory) {
			pinchecheProductCategory = append(pinchecheProductCategory, models.ProductCategory(prod.GetProductCategory()))
		}
	}
	intercityProductCategory = append(intercityThirdProductCategory, intercityProductCategory...)
	if len(intercityProductCategory) == 0 {
		return nil
	}
	areaInfo := products[0].GetAreaInfo()
	fromCity, toCity := areaInfo.Area, areaInfo.ToArea
	predictDistance := filter.getPredictDistance(products[0]) //MAYBUG 不同品类的预计行驶距离可能不同
	page_type := int(products[0].BaseReqData.CommonInfo.PageType)
	channel := products[0].BaseReqData.CommonInfo.Channel
	// 距离品类城市维度过滤
	failProductCategory := make([]models.ProductCategory, 0)
	for _, v := range intercityProductCategory {
		conf := filter.loadIntercityConf(ctx, filter.baseReq, int(fromCity), int(toCity), int(v), page_type, products[0].GetUserInfo().Phone, channel)
		if fromCity == toCity {
			if !(predictDistance > conf.SameCityThreshold && predictDistance < conf.UpperLimit) {
				failProductCategory = append(failProductCategory, v)
			}
		} else {
			if !(predictDistance > conf.DiffCityThreshold && predictDistance < conf.UpperLimit) {
				failProductCategory = append(failProductCategory, v)
			}
		}
	}

	// 站点巴士>城际客企>城际自营>拼成乐,返回的是，需要进行过滤的品类。
	if len(failProductCategory) == 0 {
		return append(pinchecheProductCategory, intercityProductCategory[1:]...)
	}
	if len(failProductCategory) == len(intercityProductCategory) {
		return intercityProductCategory
	}
	aliveProductCategory := filter.deleteFailPCID(intercityProductCategory, failProductCategory)
	return append(append(pinchecheProductCategory, failProductCategory...), aliveProductCategory[1:]...)
}

func (filter *TDistenceFilter) getPredictDistance(prod *biz_runtime.ProductInfoFull) int64 {
	if prod == nil {
		return 0
	}
	estimateFee := prod.GetDirectEstimatePrice()
	if estimateFee == nil {
		return 0
	}
	if meter := estimateFee.GetFeeAttributes().GetInt("driver_metre"); meter != nil {
		return *meter
	}
	return 0
}

// 主表单&个性化配置公里数获取
// 0、个性化配置有，读取个性化配置control_group 或者 分流group(关注，主表单配置读取内，还包了一个城际的个性化配置...老逻辑)
// 1、主表单配置有，读取主表单配置control_group 或者 分流group
// 开量过程，个性化配置通过source_id、page_type、channel，划分配置内容，使主表单配置失效。
func (filter *TDistenceFilter) loadIntercityConf(ctx context.Context, baseReq *models.BaseReqData, fromCityID int, toCity int, pcID int, pageType int, phone string, channel int64) *tIntercityDistenceFilterConfig {
	distanceConvert := &innerDistanceConvert{
		FromCityID: fromCityID,
		Phone:      phone,
		PcID:       pcID,
		PageType:   pageType,
		ToCity:     toCity,
		Channel:    channel,
	}

	// 先读取对应的非互斥的配置，如果配置allow，则读取其control-group或者by city group
	intercityDistanceFilterConfig := filter.GetIndividuationDistanceConfig(ctx, baseReq, *distanceConvert)
	if intercityDistanceFilterConfig != nil {
		return intercityDistanceFilterConfig
	}

	// 走原有的互斥逻辑
	return filter.GetMainDistanceConfig(ctx, baseReq, *distanceConvert)
}

// 这里过滤非主表单内，无需互斥，会进行统一的公里，当然，入口也需要by不同渠道进行公里限制配置
func (filter *TDistenceFilter) GetIndividuationDistanceConfig(ctx context.Context, baseReq *models.BaseReqData, distanceConvert innerDistanceConvert) *tIntercityDistenceFilterConfig {
	param := ApolloModel.NewUser("").
		With("city", strconv.Itoa(distanceConvert.FromCityID)).
		With("phone", distanceConvert.Phone).
		With("product_category", strconv.Itoa(distanceConvert.PcID)).
		With("page_type", strconv.Itoa(distanceConvert.PageType)).
		With("source_id", strconv.Itoa(int(baseReq.CommonInfo.SourceID))).
		With("to_city", strconv.Itoa(distanceConvert.ToCity)).
		With("channel", strconv.Itoa(int(distanceConvert.Channel)))
	toggle, err := ApolloSDK.FeatureToggle(IntercityDistanceThresholdIndividuation, param)
	if err != nil || toggle == nil || !toggle.IsAllow() {
		writePublic(ctx, distanceConvert, ReadFailStatus, IntercityDistanceThresholdIndividuation, "")
		return nil
	}

	intercityDistanceFilterConfig := &tIntercityDistenceFilterConfig{
		UpperLimit:        100000,
		SameCityThreshold: 20000,
		DiffCityThreshold: 10000,
	}

	if assign := toggle.GetAssignment(); assign != nil {
		// 记录对应命中的分组。
		intercityDistanceFilterConfig.UpperLimit = util.IntParseWithDefault(assign.GetParameter("upper_limit", "100000"), 100000)
		intercityDistanceFilterConfig.SameCityThreshold = util.IntParseWithDefault(assign.GetParameter("same_city", "30000"), 30000)
		intercityDistanceFilterConfig.DiffCityThreshold = util.IntParseWithDefault(assign.GetParameter("diff_city", "20000"), 20000)
		writePublic(ctx, distanceConvert, ReadSuccessStatus, IntercityDistanceThresholdIndividuation, toggle.GetAssignment().GetGroupName())
		return intercityDistanceFilterConfig
	}

	writePublic(ctx, distanceConvert, ReadSuccessStatus, IntercityDistanceThresholdIndividuation, "")
	return intercityDistanceFilterConfig
}

// 这里，过滤主表单内互斥公里，但需要注意，其下没读到IntercityDistanceThreshold时，会触发兜底城际的老公里配置。
func (filter *TDistenceFilter) GetMainDistanceConfig(ctx context.Context, baseReq *models.BaseReqData, distanceConvert innerDistanceConvert) *tIntercityDistenceFilterConfig {
	param := ApolloModel.NewUser("").
		With("city", strconv.Itoa(distanceConvert.FromCityID)).
		With("phone", distanceConvert.Phone).
		With("product_category", strconv.Itoa(distanceConvert.PcID)).
		With("page_type", strconv.Itoa(distanceConvert.PageType)).
		With("source_id", strconv.Itoa(int(baseReq.CommonInfo.SourceID))).
		With("to_city", strconv.Itoa(distanceConvert.ToCity)).
		With("channel", strconv.Itoa(int(distanceConvert.Channel)))

	toggle, err := ApolloSDK.FeatureToggle(IntercityDistanceThreshold, param)
	writePublic(ctx, distanceConvert, InitReadStatus, IntercityDistanceThreshold, "")
	intercityDistanceFilterConfig := &tIntercityDistenceFilterConfig{
		UpperLimit:        100000,
		SameCityThreshold: 30000,
		DiffCityThreshold: 20000,
	}

	if err != nil || toggle == nil || !toggle.IsAllow() {
		writePublic(ctx, distanceConvert, ReadFailStatus, IntercityDistanceThreshold, "")
		log.Trace.Warnf(ctx, consts.TagApolloLoadErr, "fail to check gs_carpool_to_intercity_distance_threshold with err %v, and toggle %v", err, toggle)
		return intercityDistanceFilterConfig
	}

	if assign := toggle.GetAssignment(); assign != nil &&
		assign.GetGroupName() != "control_group" {
		intercityDistanceFilterConfig.UpperLimit = util.IntParseWithDefault(assign.GetParameter("upper_limit", "100000"), 100000)
		intercityDistanceFilterConfig.SameCityThreshold = util.IntParseWithDefault(assign.GetParameter("same_city", "30000"), 30000)
		intercityDistanceFilterConfig.DiffCityThreshold = util.IntParseWithDefault(assign.GetParameter("diff_city", "20000"), 20000)
		writePublic(ctx, distanceConvert, ReadSuccessStatus, IntercityDistanceThreshold, assign.GetGroupName())
		return intercityDistanceFilterConfig
	}

	writePublic(ctx, distanceConvert, ReadSuccessStatus, IntercityDistanceThreshold, "")
	var sourceID int32
	if baseReq != nil {
		sourceID = baseReq.CommonInfo.SourceID
	}

	pageType := distanceConvert.PageType
	fromCityID := distanceConvert.FromCityID
	if pageType == page_type.PageTypeIntercityEstimate ||
		pageType == page_type.PageTypeCarpoolTabEstimate ||
		pageType == page_type.PageTypeLowCarpoolEstimate ||
		pageType == page_type.PageTypeUndefined ||
		sourceID == source_id.SourceIDPBD {
		confCity := filter.getIntercityConfigByCity(ctx, fromCityID, param)
		if confCity == nil {
			return intercityDistanceFilterConfig
		}
		return confCity
	}
	return intercityDistanceFilterConfig
}

func writePublic(ctx context.Context,
	distanceConvert innerDistanceConvert,
	readStatus int,
	apolloName, groupName string) {
	logInfo := make(map[string]interface{})
	logInfo["from_city"] = distanceConvert.FromCityID
	logInfo["to_city"] = distanceConvert.ToCity
	logInfo["pc_id"] = distanceConvert.PcID
	logInfo["page_type"] = distanceConvert.PageType
	logInfo["phone"] = distanceConvert.Phone
	logInfo["group_name"] = groupName
	logInfo["apollo_name"] = apolloName
	logInfo["read_status"] = readStatus

	log.Public.Public(ctx, PublicKey, logInfo)
}

func (filter *TDistenceFilter) deleteFailPCID(intercityProductCategory []models.ProductCategory, failProductCategory []models.ProductCategory) []models.ProductCategory {
	aliveProductCategory := make([]models.ProductCategory, 0)
	for _, v := range intercityProductCategory {
		status := false
		for _, w := range failProductCategory {
			if v == w {
				status = true
			}
		}
		if !status {
			aliveProductCategory = append(aliveProductCategory, v)
		}
	}
	return aliveProductCategory
}

func (filter *TDistenceFilter) getIntercityConfigByCity(ctx context.Context, cityID int, user *ApolloModel.User) *tIntercityDistenceFilterConfig {
	openFenceDistanceLimit := OpenFenceDistanceLimit{}
	crossCityDistanceLimit := CrossCityDistanceLimit{}

	if tg2, err2 := ApolloSDK.FeatureToggle("gs_intercity_distance_open_rule_by_city", user); err2 == nil && tg2.IsAllow() && tg2.GetAssignment() != nil {
		sameCityLowLimit := tg2.GetAssignment().GetParameter("same_city_low_limit", "30000")
		sameCityUpperLimit := tg2.GetAssignment().GetParameter("same_city_upper_limit", "100000")

		diffCityLowLimit := tg2.GetAssignment().GetParameter("diff_city_low_limit", "10000")
		diffCityUpperLimit := tg2.GetAssignment().GetParameter("diff_city_upper_limit", "100000")

		openFenceDistanceLimit.OpenFenceDistanceLower = sameCityLowLimit
		openFenceDistanceLimit.OpenFenceDistanceUpper = sameCityUpperLimit

		crossCityDistanceLimit.CrossCityDistanceLower = diffCityLowLimit
		crossCityDistanceLimit.CrossCityDistanceUpper = diffCityUpperLimit
	}

	distanceLimitConf := &DistanceLimitConf{
		OpenFenceDistanceLimit: openFenceDistanceLimit,
		CrossCityDistanceLimit: crossCityDistanceLimit,
	}
	upperLimit := int64(0)
	sameCityThreshold := int64(0)
	diffCityThreshold := int64(0)
	crossCityDistanceLower, _ := strconv.Atoi(distanceLimitConf.CrossCityDistanceLimit.CrossCityDistanceLower)
	crossCityDistanceUpper, _ := strconv.Atoi(distanceLimitConf.CrossCityDistanceLimit.CrossCityDistanceUpper)
	if crossCityDistanceLower != 0 || crossCityDistanceUpper != 0 {
		if int64(crossCityDistanceUpper) > upperLimit {
			upperLimit = int64(crossCityDistanceUpper)

		}
		diffCityThreshold = int64(crossCityDistanceLower)
	}
	openFenceDistanceLower, _ := strconv.Atoi(distanceLimitConf.OpenFenceDistanceLimit.OpenFenceDistanceLower)
	openFenceDistanceUpper, _ := strconv.Atoi(distanceLimitConf.OpenFenceDistanceLimit.OpenFenceDistanceUpper)
	if openFenceDistanceLower != 0 || openFenceDistanceUpper != 0 {
		if int64(openFenceDistanceUpper) > upperLimit {
			upperLimit = int64(openFenceDistanceUpper)

		}
		sameCityThreshold = int64(openFenceDistanceLower)
	}
	if upperLimit == 0 {
		upperLimit = 100000
	}
	if diffCityThreshold == 0 {
		diffCityThreshold = 30000
	}
	return &tIntercityDistenceFilterConfig{
		UpperLimit:        upperLimit,
		SameCityThreshold: sameCityThreshold,
		DiffCityThreshold: diffCityThreshold,
	}

}
