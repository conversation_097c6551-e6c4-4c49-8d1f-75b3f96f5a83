package filter

import (
	"context"
	"flag"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/conf"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"github.com/smartystreets/goconvey/convey"
	"path/filepath"
	"runtime"
	"testing"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

func TestMain(m *testing.M) {
	_, filename, _, ok := runtime.Caller(0)
	if !ok {
		fmt.Println("无法获取当前文件路径")
		return
	}
	dir := filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(filename))))
	confPath := filepath.Join(dir, "/conf/app_dev.toml")
	flag.StringVar(&confPath, "c", confPath, "-c set config file path")
	flag.Parse()
	fmt.Printf("confPath is %s\n", confPath)
	conf.InitConf(confPath)
	log.Init()
	fmt.Printf("confPath is %s\n", confPath)
	m.Run()
}

func buildBaseReqData(pageType int32, from, to int32) *models.BaseReqData {
	baseReq := &models.BaseReqData{
		CommonInfo: models.CommonInfo{
			PageType: pageType,
			SourceID: 1,
			Channel:  1,
		},
		AreaInfo: models.AreaInfo{
			Area:   from,
			ToArea: to,
		},
		PassengerInfo: models.PassengerInfo{
			Phone: "1234567890",
		},
	}
	return baseReq
}

func buildProducts(pcids []int64, seatNum, maxSeatNum int32) []*biz_runtime.ProductInfoFull {
	products := make([]*biz_runtime.ProductInfoFull, 0)
	for _, pcid := range pcids {
		products = append(products, &biz_runtime.ProductInfoFull{
			BaseReqData: &models.BaseReqData{
				AreaInfo: models.AreaInfo{
					Area:   1,
					ToArea: 1,
				},
			},
			Product: &models.Product{
				ProductCategory: pcid,
				CarpoolType:     3,
				ComboType:       302,
				BizInfo: &models.PrivateBizInfo{
					CarpoolSeatNum:    seatNum,
					MaxCarpoolSeatNum: maxSeatNum,
					DepartureRange:    []int64{},
				},
			},
		})
	}
	return products
}

func TestDo_Success(t *testing.T) {
	convey.Convey("Test Do function success case", t, func() {
		// 初始化测试数据
		baseReq := buildBaseReqData(page_type.PageTypeIntercityEstimate, 1, 2)
		products := buildProducts([]int64{5, 7501, 7502}, 1, 4)
		filter := NewDistanceFilter(baseReq)

		// 执行测试
		result := filter.Do(context.Background(), products)

		// 验证结果
		convey.So(result, convey.ShouldNotBeNil)
	})
}
