package intercity_common

import (
	passenger_common "git.xiaojukeji.com/gulfstream/passenger-common/dto"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	CarpoolOpenApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolOpenApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_sku"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/payment"
)

// AdapterInterCity 城际库存模式表单渲染适配
type AdapterInterCity struct {
	*biz_runtime.ProductInfoFull
}

func (inter *AdapterInterCity) GetAccessKeyID() int {
	return int(inter.GetClientInfo().AccessKeyID)
}

func (inter *AdapterInterCity) GetEstimateFeeAmount() float64 {
	fee := inter.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}
	return fee.GetFee()
}

func (inter *AdapterInterCity) GetBusinessPayAmount() float64 {
	fee := inter.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}

	// MAYBUG: 某一品类的企业支付抵扣金额, 应该在前面做判断, 不应该在UI层
	paymentType := inter.GetCurrentPaymentType()
	if consts.BusinessPaymentType == paymentType {
		return fee.GetFee()
	}

	return 0
}
func (inter *AdapterInterCity) GetBonus() float64 {
	fee := inter.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}
	amount := fee.GetFeeDetail().GetBonusAmount()
	if amount == nil {
		return 0
	}
	return *amount
}

func (inter *AdapterInterCity) GetCoupon() *intercity_sku.FeeItem {
	fee := inter.GetDirectEstimatePrice()
	if fee == nil {
		return nil
	}
	coupon := fee.GetFeeDetail().GetCoupon()
	if coupon == nil {
		return nil
	}
	return &intercity_sku.FeeItem{
		Tag:    coupon.Tag,
		Amount: coupon.Amount,
	}
}

func (inter *AdapterInterCity) GetMaxSeatNum() int32 {
	bizInfo := inter.GetBizInfo()
	if bizInfo == nil {
		return 1
	}
	if bizInfo.StationInventoryInfo == nil || bizInfo.StationInventoryInfo.SelectInfo.RemainSeats == 0 || bizInfo.StationInventoryInfo.SelectInfo.UserMaxSeatNum == 0 {
		return 1
	}
	return util.MaxInt32(bizInfo.StationInventoryInfo.SelectInfo.RemainSeats, bizInfo.StationInventoryInfo.SelectInfo.UserMaxSeatNum)
}
func (inter *AdapterInterCity) GetUserSelectSeatNum() int32 {
	bizInfo := inter.GetBizInfo()
	if bizInfo == nil {
		return 1
	}
	return bizInfo.CarpoolSeatNum
}

func (inter *AdapterInterCity) GetAllPaymentOptionADP() []payment.PaymentOption {
	stringUnpack := func(s *string) string {
		if s == nil {
			return ""
		}
		return *s
	}

	opts := inter.GetAllPaymentOption()
	result := make([]payment.PaymentOption, 0, len(opts))
	for _, opt := range opts {
		if opt.Disabled != nil && *opt.Disabled == 1 {
			continue
		}
		result = append(result, payment.PaymentOption{
			PaymentType:      opt.PayType,
			BusinessConstSet: opt.BusinessConstSet,
			ChannelName:      stringUnpack(opt.ChannelName),
		})
	}
	return result
}

func (inter *AdapterInterCity) GetCurrentPaymentType() int {
	opts := inter.GetAllPaymentOption()
	for _, opt := range opts {
		if opt.IsSelected != nil && *opt.IsSelected == 1 {
			return int(opt.PayType)
		}
	}
	return 0
}

func (inter *AdapterInterCity) GetDepartureTime() int64 {
	if inter.GetBizInfo() == nil || inter.GetBizInfo().StationInventoryInfo == nil {
		return time.Now().Unix()
	}
	return inter.GetBizInfo().StationInventoryInfo.SelectInfo.DepartureTime
}

func (inter *AdapterInterCity) GetVersion() string {
	return inter.GetClientInfo().AppVersion
}

func (inter *AdapterInterCity) GetAllPaymentOptions() []payment.PaymentOption {
	stringUnpack := func(s *string) string {
		if s == nil {
			return ""
		}
		return *s
	}

	opts := inter.GetAllPaymentOption()
	result := make([]payment.PaymentOption, 0, len(opts))
	for _, opt := range opts {
		if opt.Disabled != nil && *opt.Disabled == 1 {
			continue
		}
		result = append(result, payment.PaymentOption{
			PaymentType:      opt.PayType,
			BusinessConstSet: opt.BusinessConstSet,
			ChannelName:      stringUnpack(opt.ChannelName),
		})
	}
	return result
}

func (inter *AdapterInterCity) GetAllStationInfo() []*Prfs.StationInfo {
	bizInfo := inter.GetBizInfo()
	if bizInfo == nil {
		return nil
	}
	if bizInfo.StationInventoryInfo == nil {
		return nil
	}
	return bizInfo.StationInventoryInfo.StationList
}

func (inter *AdapterInterCity) GetSelectInfo() *models.StationInventorySelectInfo {
	bizInfo := inter.GetBizInfo()
	if bizInfo == nil {
		return nil
	}
	if bizInfo.StationInventoryInfo == nil {
		return nil
	}
	return &bizInfo.StationInventoryInfo.SelectInfo
}

func (inter *AdapterInterCity) GetFromStationIndex() int {
	bizInfo := inter.GetBizInfo()
	if bizInfo == nil {
		return 0
	}
	if bizInfo.StationInventoryInfo == nil {
		return 0
	}
	return bizInfo.StationInventoryInfo.SelectInfo.FromStationIndex
}

func (inter *AdapterInterCity) GetDistance() []int64 {
	bizInfo := inter.GetBizInfo()
	if bizInfo == nil {
		return nil
	}
	if bizInfo.StationInventoryInfo == nil || bizInfo.StationInventoryInfo.RecommendStation == nil {
		return nil
	}
	return []int64{
		bizInfo.StationInventoryInfo.RecommendStation.SrcWalkDist,
		bizInfo.StationInventoryInfo.RecommendStation.DestWalkDist,
	}
}

func (inter *AdapterInterCity) GetStationInventory() []*CarpoolOpenApi.StationInventory {
	bizInfo := inter.GetBizInfo()
	if bizInfo == nil {
		return nil
	}
	if bizInfo.StationInventoryInfo == nil || bizInfo.StationInventoryInfo.StationInventorys == nil {
		return nil
	}
	return bizInfo.StationInventoryInfo.StationInventorys
}

func (inter *AdapterInterCity) GetSourceId() int32 {
	return inter.GetCommonBizInfo().SourceId
}

func (inter *AdapterInterCity) IsStationScan() bool {
	if inter == nil || inter.BaseReqData == nil {
		return false
	}
	return inter.BaseReqData.CommonInfo.IsScanCode == "station_scan"
}

func (inter *AdapterInterCity) ValidScanCode() bool {
	if inter == nil || inter.BaseReqData == nil {
		return false
	}
	return inter.GetCommonBizInfo().ValidScanCode
}

func (inter *AdapterInterCity) GetAppointmentRange() int {
	return int(inter.BaseReqData.CommonBizInfo.OpenAppointmentRange)
}

func (inter *AdapterInterCity) GetPageType() int32 {
	if inter == nil || inter.BaseReqData == nil {
		return -1
	}
	return inter.BaseReqData.CommonInfo.PageType
}

func (inter *AdapterInterCity) GetScene() string {
	if inter == nil || inter.BaseReqData == nil {
		return ""
	}
	return inter.BaseReqData.CommonBizInfo.Scene
}

func (inter *AdapterInterCity) GetPreBusServiceShiftId() string {
	if inter == nil || inter.BaseReqData == nil {
		return ""
	}
	return inter.BaseReqData.CommonBizInfo.PreBusServiceShiftId
}

func (inter *AdapterInterCity) GetPreDepartureTime() int64 {
	if inter == nil || inter.BaseReqData == nil {
		return 0
	}
	return inter.BaseReqData.CommonBizInfo.PreDepartureTime
}

func (inter *AdapterInterCity) GetRebookServiceCharge() float64 {
	if inter == nil {
		return 0
	}

	if inter.ProductInfoFull != nil {
		if rebookServiceFee := inter.ProductInfoFull.GetRebookServiceFee(); rebookServiceFee > 0 {
			return rebookServiceFee
		}
	}

	fees := inter.GetBillFeeDetailInfo()
	if fees == nil || len(fees) == 0 {
		return 0
	}
	if amount, ok := fees["rebook_service_charge"]; ok && amount > 0 {
		return amount
	}

	return 0
}

func (inter *AdapterInterCity) GetSelectedBusServiceShiftId() string {
	if inter == nil || inter.BaseReqData == nil {
		return ""
	}

	return inter.BaseReqData.CommonBizInfo.SeletedBusServiceShiftId
}

func (inter *AdapterInterCity) GetOccupySeatByBus(carryChildOccupySeat string) int32 {
	if inter == nil || inter.BaseReqData == nil {
		return 0
	}

	ret := int32(0)
	details := inter.BaseReqData.CommonBizInfo.SeatDetailInfo
	for _, detail := range details {
		if detail == nil {
			continue
		}
		if detail.IsOccupySeat == 1 || (detail.PassengerType == seat_selection_consts.CarryChildren.ToInt32() && carryChildOccupySeat == "1") {
			ret += detail.PassengerCount
		}
	}
	return ret
}

func (inter *AdapterInterCity) GetCarryChildSeat() int32 {
	if inter == nil || inter.BaseReqData == nil {
		return 0
	}

	ret := int32(0)
	details := inter.BaseReqData.CommonBizInfo.PassengerTickerInfo
	for _, detail := range details {
		if detail == nil {
			continue
		}
		if detail.TicketType == seat_selection_consts.CarryChildren.ToInt32() {
			ret += 1
		}
	}
	return ret
}

func (inter *AdapterInterCity) GetSelectedBusServiceDay() string {
	if inter == nil || inter.BaseReqData == nil {
		return ""
	}

	return inter.BaseReqData.CommonBizInfo.SelectedBusServiceDay
}

func (inter *AdapterInterCity) GetPassengerCount() int32 {
	if inter == nil || inter.GetBizInfo() == nil {
		return 0
	}
	return inter.GetBizInfo().CarpoolSeatNum
}

func (inter *AdapterInterCity) GetPassengerTicketInfo() []*passenger_common.PassengerDetail {
	if inter == nil || inter.BaseReqData == nil || inter.BaseReqData.CommonBizInfo.PassengerTickerInfo == nil {
		return nil
	}
	return inter.BaseReqData.CommonBizInfo.PassengerTickerInfo
}

func (inter *AdapterInterCity) GetCarryChildOccupySeat() int32 {
	if inter == nil || inter.BaseReqData == nil {
		return -1
	}

	details := inter.BaseReqData.CommonBizInfo.PassengerTickerInfo
	for _, detail := range details {
		if detail == nil {
			continue
		}
		if detail.TicketType == seat_selection_consts.CarryChildren.ToInt32() {
			return detail.IsOccupySeat
		}
	}

	return -1
}
