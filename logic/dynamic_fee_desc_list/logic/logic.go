package logic

import (
	"context"
	"encoding/json"
	"errors"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/dynamic_fee_desc_list/data"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type logic struct {
	req *proto.DynamicFeeDescListRequest

	EidList []*data.ProductInfo
}

func NewLogic(req *proto.DynamicFeeDescListRequest) *logic {
	return &logic{
		req: req,
	}
}

func (l *logic) GetProductList(ctx context.Context) []*data.ProductInfo {
	if l.req == nil {
		return nil
	}

	productList := make([]*data.ProductInfo, 0)

	err := json.Unmarshal([]byte(l.req.ProductList), &productList)
	if err != nil {
		log.Trace.Warnf(ctx, "getProductList", "product list unmarshal fail, err:%v", err)
		return nil
	}

	return productList
}

func (l *logic) GetUserInfo(ctx context.Context) (*passport.UserInfo, error) {
	//请求 passport
	userInfo, err := passport.GetUserInfo(ctx, l.req.Token, "")
	if err != nil {
		log.Trace.Infof(ctx, "getUserInfo", "getUserInfo: %v", err)
		return nil, err
	}

	return userInfo, nil
}

func (l *logic) GetQuotationBatch(ctx context.Context, fields []string) (*PriceApi.GetQuotationBatchResp, error) {
	var (
		eidList []string
	)

	productList := l.GetProductList(ctx)

	if len(productList) <= 0 {
		return nil, errors.New("req product list is nil")
	}

	l.EidList = productList
	for _, product := range productList {
		if product == nil || len(product.EstimateID) <= 0 {
			continue
		}

		eidList = append(eidList, product.EstimateID)
	}

	req := &price_api.PriceQuotationBatch{
		EstimateIdList: eidList,
		Fields:         fields,
	}

	resp, err := price_api.GetQuotationBatch(ctx, req)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (l *logic) FormatQuotationResp(resp *PriceApi.GetQuotationBatchResp) map[string]*biz_runtime.Quotation {
	quotations := make(map[string]*biz_runtime.Quotation)
	if resp == nil || resp.Data == nil {
		return nil
	}

	for k, v := range resp.Data {
		quotation := biz_runtime.Quotation(v)
		quotations[k] = &quotation
	}

	return quotations
}

func (l *logic) IsSingleCheck() bool {
	if len(l.EidList) == 1 {
		// 单勾
		return true
	} else if len(l.EidList) > 1 {
		// 多勾
		return false
	}

	return true
}
