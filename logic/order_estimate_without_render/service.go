package order_estimate_without_render

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/dao/ufs_logic"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_filter/after_dds_filter"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_filter/after_price_filter"
	trace "git.xiaojukeji.com/lego/context-go"
)

type EstimateWithRenderStepRuntime struct {
	req          *proto.EstimateOrderWithoutRenderReq
	orderInfo    *dos.OrderInfo
	orderFeature *ufs.OrderFeature
	generator    *biz_runtime.ProductsGenerator
}

func NewRuntime(req *proto.EstimateOrderWithoutRenderReq) *EstimateWithRenderStepRuntime {
	return &EstimateWithRenderStepRuntime{
		req: req,
	}
}

func (r *EstimateWithRenderStepRuntime) Init(ctx context.Context) error {
	err := r.initParams(ctx)
	if err != nil {
		return errors.NewStdBizErr(err, errors.ErrnoSystemError)
	}
	mockAnycarReq := &proto.AnyCarEstimateReq{
		OrderId:     r.req.OrderId,
		Lang:        "zh-CN",
		Channel:     util.ToInt64(r.orderInfo.Channel),
		AccessKeyId: util.ToInt32(r.orderInfo.PAccessKeyId),
		AppVersion:  r.req.GetAppVersion(),
		PageType:    r.req.PageType,
		UserType:    r.req.UserType,
	}
	productsGen, err := biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("EstimateOrderWithoutRender"),
		biz_runtime.WithOrderInfo(ctx, mockAnycarReq, r.orderInfo, &passport.UserInfo{
			UID:   uint64(util.GetUidByPassengerId(r.req.GetPid(), util.Passenger)),
			PID:   uint64(r.req.GetPid()),
			Phone: r.req.GetPhone(),
		}, r.orderFeature),
	)
	if err != nil {
		return errors.NewStdBizErr(err, errors.ErrnoSystemError)
	}

	r.registerProductFilter(ctx, productsGen)
	r.generator = productsGen
	return nil
}

func (r *EstimateWithRenderStepRuntime) registerProductFilter(ctx context.Context, generator *biz_runtime.ProductsGenerator) {
	if cpFilter := after_dds_filter.NewAnyCarFilterSendProducts(ctx,
		r.orderInfo.ExtendFeatureParsed.MultiRequiredProduct, generator.BaseReqData.PassengerInfo.UserType); cpFilter != nil {
		generator.RegisterAfterDdsFilter(cpFilter)
	}

	//如果上游传递了仅需的品类信息，就将其他无效品类过滤
	if cpFilter := after_dds_filter.NewNeedProductCategoryFilterCurrentProducts(ctx, r.req.NeedProductCategory); cpFilter != nil {
		generator.RegisterAfterDdsFilter(cpFilter)
	}

	//针对企业支付 预估额外添加了快车的情况，需要再次过滤已呼叫品类
	if cpFilter := after_price_filter.NewGuideAnyCarFilterCurrentProducts(ctx,
		r.orderInfo.ExtendFeatureParsed.MultiRequiredProduct); cpFilter != nil {
		generator.RegisterAfterPriceFilter(cpFilter)
	}
}

func (r *EstimateWithRenderStepRuntime) initParams(ctx context.Context) error {
	req := r.req
	orderID, district, err := util.DecodeOrderID(req.OrderId)
	if err != nil {
		log.Trace.Errorf(ctx, trace.DLTagUndefined, "decodeErr: %v", err)
		return errors.NewBizError(err, errors.ErrNoGetOrderInfoFailed)
	}

	// 请求 dos
	orderInfo, err := dos.GetOrderInfo(ctx, orderID, district)
	if err != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "getOrderInfo: %v", err)
		return errors.NewBizError(err, errors.ErrNoGetOrderInfoFailed)
	}
	r.orderInfo = orderInfo
	r.orderFeature = ufs_logic.BuildMockOrderFeature(ctx, orderInfo, &proto.AnyCarEstimateReq{})
	r.orderFeature.Channel = orderInfo.Channel

	return nil
}

func (r *EstimateWithRenderStepRuntime) Do(ctx context.Context) (map[string]*proto.WithoutRenderEstimateData, error) {
	products, err := r.generator.GenProducts(ctx)
	if err != nil {
		return nil, errors.NewStdBizErr(err, errors.ErrnoSystemError)
	}
	ret := make(map[string]*proto.WithoutRenderEstimateData)
	for _, product := range products {
		if product.Product == nil {
			continue
		}
		ret[product.Product.EstimateID] = &proto.WithoutRenderEstimateData{
			EstimateId:          product.Product.EstimateID,
			ProductCategory:     product.GetProductCategory(),
			ProductId:           product.GetProductId(),
			BusinessId:          product.GetBusinessID(),
			RequireLevel:        product.GetRequireLevel(),
			ComboType:           product.GetComboType(),
			LevelType:           product.GetLevelType(),
			CarpoolType:         product.GetCarpoolType(),
			CountPriceType:      product.GetCountPriceType(),
			IsSpecialPrice:      util.ToInt32(product.GetIsSpecialPrice()),
			SpaciousCarAlliance: product.Product.SpaciousCarAlliance,
			ComboId:             product.GetComboID(),
			EstimateFee:         product.GetEstimateFee(),
			DynamicTotalFee:     product.GetDynamicTotalFee(),
			CapPrice:            product.GetCapPrice(),
			BasicTotalFee:       product.GetBasicTotalFee(),
			DynamicDiffPrice:    product.GetNewDynamicDiffPrice(),
			TotalFee:            product.GetBillTotalFee(),
			EnergyConsumeFee:    product.GetFeeDetailInfoAmount("energy_consume_fee"),
			RouteIdList:         product.GetBillRouteIdList(),
			StartDestDistance:   product.GetBillDriverMetre(),
			DriverMetre:         product.GetBillDriverMetre(),
			StartDestTime:       product.GetBillDriverMinute(),
			PaymentId:           util.ToString(product.GetPageType()),
			BasicRate:           0,
			PayType:             product.GetDefaultPayType(),
			RedPacket:           product.GetFeeDetailInfoAmount("red_packet"),
		}
	}
	return ret, nil
}
