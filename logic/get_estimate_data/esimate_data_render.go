package get_estimate_data

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_id"
	ProductCategory "git.xiaojukeji.com/gulfstream/biz-common-go/v6/category"
	commonConsts "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/product"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/tripcloud"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input"
	engineModel "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/category_carpool"
)

type BuildResult func(ctx context.Context, quotation *Adapter, res *proto.PGetEstimateDataByEidListData)

func (s *Service) BuildProductCategory(ctx context.Context, adapter *Adapter, res *proto.PGetEstimateDataByEidListData) {
	pcID := int32(adapter.GetProductCategory())
	res.ProductCategory = &pcID
	return
}

func (s *Service) BuildCarInfo(ctx context.Context, adapter *Adapter, res *proto.PGetEstimateDataByEidListData) {
	icon := car_info.GetCarNormalIcon(ctx, adapter)
	res.CarIcon = &icon

	name := car_info.GetCarName(ctx, adapter)
	res.CarTitle = &name
	return
}

func (s *Service) BuildFeeInfo(ctx context.Context, adapter *Adapter, res *proto.PGetEstimateDataByEidListData) {
	feeMsg, feeAmount, _ := fee_info_render.GetFeeInfo(ctx, adapter)
	res.FeeMsg, res.FeeAmount = &feeMsg, &feeAmount
	//特价拼车走单独的方法（极速和特价可以合并在一个接口，后续todo哇）
	if adapter.GetProductCategory() == ProductCategory.ProductCategoryLowPriceCarpool {
		res.MultiPriceList = fee_info_render.GetLowPriceMultiPriceDesc(ctx, adapter)
	} else {
		res.MultiPriceList = fee_info_render.GetMultiPriceDescSation(ctx, adapter)
	}
	return
}

func (s *Service) IsTripCloud(ctx context.Context, adapter *Adapter, res *proto.PGetEstimateDataByEidListData) {
	Tripcloud := false
	if tripcloud.IsTripcloudProductID(commonConsts.ProductID(adapter.GetProductId())) {
		Tripcloud = true
	}

	res.IsTripcloud = &Tripcloud
	return
}

func (s *Service) GetFeeDescList(ctx context.Context, adapter *Adapter, res *proto.PGetEstimateDataByEidListData) {
	if adapter.GetProductCategory() == ProductCategory.ProductCategoryCarpoolStation {
		res.FeeDescList = category_carpool.GetPriceInfoDescListV2(ctx, adapter)
		return
	} else if adapter.GetProductCategory() == ProductCategory.ProductCategoryLowPriceCarpool {
		// todo 先不处理，拼成乐券需要处理@zhiyu
		return
	}

	var fi *engineModel.FeeInput

	env := fee_desc_engine.NewEnv(consts.AnyCarV3Form).SetApolloParams(adapter)
	// 香港品类
	if product_id.ProductIdHkTaxi == adapter.GetProductId() ||
		product.IsHongKongThird(commonConsts.ProductID(adapter.GetProductId())) {
		env.SetDcmpKey(consts.EstimateV3FeeDescHK)
	}

	fi = input.BuildNormalInputByQuotation(ctx, adapter, consts.AnyCarV3Form)

	output := fee_desc_engine.NewFeeEngine(fi, env).SetProductCategory(adapter.GetProductCategory()).Do(ctx)
	for _, f := range output {
		if f == nil || f.Fee == nil {
			continue
		}
		res.FeeDescList = append(res.FeeDescList, &proto.NewFormFeeDesc{
			BorderColor: f.BorderColor,
			Content:     f.Content,
			Icon:        f.Icon,
			Amount:      f.Fee.Amount,
			TextColor:   &f.TextColor,
		})
	}
	return
}

// GetSubsidyPopupFeeInfo 补贴弹窗
func (s *Service) GetSubsidyPopupFeeInfo(ctx context.Context, adapter *Adapter, res *proto.PGetEstimateDataByEidListData) {
	type DCMPConfig struct {
		FeeMsgOrigin           string `json:"fee_msg_origin"`
		FeeMsgFinal            string `json:"fee_msg_final"`
		DiscountDesc           string `json:"discount_desc"`
		DiscountDescSimpleText string `json:"discount_desc_simple_text"`
		FeeDiffMsg             string `json:"fee_diff_msg"`
	}

	var (
		dcmpMap DCMPConfig
	)

	dynamicTotalFee := adapter.GetDynamicTotalFee()
	estimateFee := adapter.GetEstimateFee()
	priceDiffFen := util.FormatYuanToFen(dynamicTotalFee) - util.FormatYuanToFen(estimateFee)
	priceDiffYuan := util.FormatFenToYuan(priceDiffFen, 2)
	priceDiffYuanStr := util.RemoveSuffixZero(priceDiffYuan)

	template := dcmp.GetDcmpPlainContent(ctx, "config_text-subsidy_popup_fee_info")
	if err := json.Unmarshal([]byte(template), &dcmpMap); err != nil {
		return
	}

	feeMsgOrigin := util.ReplaceTag(ctx, dcmpMap.FeeMsgOrigin, map[string]string{"num": util.ToString(dynamicTotalFee)})
	res.SubsidyPopupFeeMsgOrigin = &feeMsgOrigin

	feeMsgFinal := util.ReplaceTag(ctx, dcmpMap.FeeMsgFinal, map[string]string{"num": util.ToString(estimateFee)})
	res.SubsidyPopupFeeMsgFinal = &feeMsgFinal

	discountDesc := util.ReplaceTag(ctx, dcmpMap.DiscountDesc, map[string]string{"num": priceDiffYuanStr})
	res.SubsidyPopupDiscountDesc = &discountDesc

	discountDescSimple := util.ReplaceTag(ctx, dcmpMap.DiscountDescSimpleText, map[string]string{"num": priceDiffYuanStr})
	res.SubsidyPopupDiscountDescSimpleText = &discountDescSimple

	feeDiffMsg := util.ReplaceTag(ctx, dcmpMap.FeeDiffMsg, map[string]string{"num": priceDiffYuanStr})
	res.SubsidyPopupFeeDiffMsg = &feeDiffMsg
}

// GetSubsidyPopupFeeInfo 补贴弹窗
func (s *Service) GetFeeDetailInfo(ctx context.Context, adapter *Adapter, res *proto.PGetEstimateDataByEidListData) {
	res.FeeDetailInfo = adapter.GetFeeDetailInfo()
}
