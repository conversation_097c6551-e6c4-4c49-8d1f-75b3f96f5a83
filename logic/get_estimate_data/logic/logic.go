package logic

import (
	"context"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/dynamic_fee_desc_list/data"
)

type logic struct {
	req *proto.PGetEstimateDataByEidListReq

	EidList []*data.ProductInfo
}

func NewLogic(req *proto.PGetEstimateDataByEidListReq) *logic {
	return &logic{
		req: req,
	}
}

func (l *logic) GetQuotationBatch(ctx context.Context, fields []string, eidLists []string) (*PriceApi.GetQuotationBatchResp, error) {
	req := &price_api.PriceQuotationBatch{
		EstimateIdList: eidLists,
		Fields:         fields,
	}

	resp, err := price_api.GetQuotationBatch(ctx, req)
	if err != nil || resp == nil {
		return nil, err
	}

	return resp, nil
}

func (l *logic) IsSingleCheck() bool {
	if len(l.EidList) == 1 {
		// 单勾
		return true
	} else if len(l.EidList) > 1 {
		// 多勾
		return false
	}

	return true
}
