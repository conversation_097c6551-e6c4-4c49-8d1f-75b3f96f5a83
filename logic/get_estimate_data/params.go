package get_estimate_data

import (
	"context"
	"fmt"
	"strconv"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

const (
	apolloKey = "get_estimate_data_by_eidlist_caller"
)

func CheckReq(ctx context.Context, req *proto.PGetEstimateDataByEidListReq) BizError.BizError {
	validator := req.Validate()
	if validator != nil {
		return BizError.ErrReqNotAllow
	}

	if err := validateReq(req); err != nil {
		return BizError.ErrReqNotAllow
	}

	if !apollo.FeatureToggle(ctx, apolloKey, strconv.FormatInt(req.GetUid(), 10), getApolloParam(req)) {
		return BizError.ErrReqNotAllow
	}

	return nil
}

func validateReq(req *proto.PGetEstimateDataByEidListReq) error {
	for _, eid := range req.EidList {
		if eid == "" {
			return fmt.Errorf("eid empty")
		}
	}

	return nil
}

func getApolloParam(req *proto.PGetEstimateDataByEidListReq) map[string]string {
	return map[string]string{
		"uid":           strconv.FormatInt(req.GetUid(), 10),
		"pid":           strconv.FormatInt(req.GetPid(), 10),
		"app_version":   req.GetAppVersion(),
		"access_key_id": strconv.Itoa(int(req.GetAccessKeyId())),
		"phone":         req.GetPhone(),
		"caller":        req.GetCaller(),
	}
}
