package engage_car_estimate

import (
	"context"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/source_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

const PublicKey = "g_order_cap_multi_estimate_price"

func WritePublicLog(ctx context.Context, req *proto.EngageCarEstimateReq, products []*models.Product, data []*proto.EngageEstimateData, err BizError.BizError) {

	for _, d := range data {
		if d == nil {
			continue
		}

		logInfo := make(map[string]interface{})
		//索引key
		logInfo["estimate_trace_id"] = util.GetTraceIDFromCtxWithoutCheck(ctx)
		logInfo["estimate_id"] = d.EstimateId
		if err != nil {
			logInfo["error_code"] = err.Errno()
			logInfo["success"] = err.Errno() == BizError.Success.Errno()
		}

		//地理位置信息
		logInfo["from_city"] = req.FromCity
		logInfo["from_county"] = req.FromDistrictCode
		//端信息
		logInfo["app_version"] = req.AppVersion
		logInfo["client_type"] = req.ClientType
		logInfo["access_key_id"] = req.AccessKeyId
		logInfo["channel"] = req.Channel
		logInfo["lang"] = req.Lang
		logInfo["source_id"] = source_id.SourceIDCharterCarPlatform

		//产品信息
		logInfo["product_category"] = d.ProductCategory
		logInfo["require_level"] = d.RequireLevel
		for _, p := range products {
			if p.ProductCategory != int64(d.ProductCategory) {
				continue
			}
			logInfo["combo_type"] = p.ComboType
			logInfo["carpool_type"] = p.CarpoolType
			logInfo["product_id"] = p.ProductID
			logInfo["require_level"] = p.RequireLevelInt
		}
		//预估数据
		logInfo["page_source"] = req.PageSource
		logInfo["start_time"] = req.StartTime
		logInfo["fee_amount"] = d.FeeAmount
		logInfo["combo_id"] = d.ComboId
		logInfo["combo_distance"] = d.ComboDistance
		logInfo["combo_time"] = d.ComboTime
		logInfo["over_time_fee"] = d.OverTimeFee
		logInfo["over_time_unit"] = d.OverTimeUnit
		logInfo["over_distance_fee"] = d.OverDistanceFee
		//用户信息
		logInfo["uid"] = req.Uid

		log.Public.Public(ctx, PublicKey, logInfo)
	}
}
