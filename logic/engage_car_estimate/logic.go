package engage_car_estimate

import (
	"context"
	"crypto/md5"
	"errors"
	"fmt"
	"strconv"
	"time"

	plutusSerivce "git.xiaojukeji.com/dirpc/dirpc-go-http-Plutus"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_id"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product_center"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/source_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/reqctx"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/plutus"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	context2 "git.xiaojukeji.com/lego/context-go"
	"git.xiaojukeji.com/nuwa/trace"
	jsoniter "github.com/json-iterator/go"
	"github.com/shopspring/decimal"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

type BizLogic struct {
	generator *biz_runtime.ProductsGenerator
	req       *proto.EngageCarEstimateReq
}

type QuotationParams struct {
	EstimateId      string  `json:"estimate_id" form:"estimate_id"`           //预估id
	ProductCategory int32   `json:"product_category" form:"product_category"` //品类id
	ProductId       int32   `json:"product_id" form:"product_id"`             //产品线id
	ComboType       int32   `json:"combo_type" form:"combo_type"`
	RequireLevelInt int32   `json:"require_level_int" form:"require_level_int"`   //车型分类（经济5座）
	RequireLevelStr string  `json:"require_level_str" form:"require_level_str"`   //车型分类（经济5座）
	FeeAmount       int64   `json:"fee_amount" form:"fee_amount"`                 //预估价格,单位分
	ComboId         int64   `json:"combo_id" form:"combo_id"`                     //套餐id
	ComboDistance   float64 `json:"combo_distance" form:"combo_distance"`         //套餐里程
	ComboTime       int64   `json:"combo_time" form:"combo_time"`                 //套餐时长
	OverTimeFee     int64   `json:"over_time_fee" form:"over_time_fee"`           //超时长费
	OverTimeUnit    int64   `json:"over_time_unit" form:"over_time_unit"`         //超时长计价单位，x分钟
	OverDistanceFee int64   `json:"over_distance_fee" form:"over_distance_fee"`   //超公里费，单位：元/公里
	FeeDescList     *string `json:"fee_desc_list,omitempty" form:"fee_desc_list"` //价格详情
	DepartureTime   int64   `json:"departure_time"`
}

const (
	StrategiesZero    = 0
	StrategiesNormal  = 1 //常规策略
	StrategiesRestDay = 2 //节假日策略
	StrategiesCustom  = 3 //自定义策略
)

func BuildService(ctx context.Context, estimateReq *param_handler.EstimateRequest) (*BizLogic, error) {
	req, ok := estimateReq.ReqFromParams.(*proto.EngageCarEstimateReq)
	if !ok {
		return nil, errors.New("error")
	}
	builder := models.NewBaseReqDataBuilder().
		SetClientInfo(&models.ClientInfo{
			AppVersion:  req.AppVersion,
			AccessKeyID: req.AccessKeyId,
			ClientType:  req.ClientType,
			Lang:        req.Lang,
			Channel:     req.Channel,
			SourceID:    source_id.SourceIDCharterCarPlatform,
			TerminalID:  0,
			OriginID:    0,
			Imei:        "",
			MenuID:      "dache_anycar",
		}).SetAreaInfo(ctx, req.FromCity, req.FromCity).
		SetPassengerInfoV2(&models.PassengerInfoV2{
			UID: req.Uid,
		})

	productsGen, err2 := biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("PEngageCarEstimate"),
		biz_runtime.WithBaseReq(builder.GetBaseReqData()),
	)
	if err2 != nil {
		return nil, err2
	}
	productsGen.SetSendReqKafka(true)

	return &BizLogic{generator: productsGen}, nil
}

func (b *BizLogic) DoBizLogicList(ctx context.Context, req *proto.EngageCarEstimateReq) (*proto.EngageCarEstimateData, BizError.BizError) {
	b.req = req
	products, err := b.GenProducts(ctx)
	if products == nil || err != nil {
		return nil, err
	}
	priceMap, err := b.GenPrice(ctx)
	if err != nil {
		return nil, err
	}
	data, err := b.buildEstimateData(ctx, req, products, priceMap)
	defer func() {
		WritePublicLog(ctx, req, products, data.EstimateData, err)
	}()
	return data, nil
}

func (b *BizLogic) buildEstimateData(ctx context.Context, req *proto.EngageCarEstimateReq, products []*models.Product, priceMap map[string]*plutusSerivce.BillStrategy) (*proto.EngageCarEstimateData, BizError.BizError) {
	var data []*proto.EngageEstimateData
	productMap := map[string]*models.Product{}
	for _, pro := range products {
		if len(pro.RequireLevel) == 0 {
			continue
		}
		productMap[pro.RequireLevel] = pro
	}
	if productMap == nil {
		return nil, BizError.ErrRender
	}
	for _, price := range priceMap {
		product := productMap[price.CarLevel]
		if product == nil {
			continue
		}
		temp := decimal.New(1, 2)
		data = append(data, &proto.EngageEstimateData{
			EstimateId:      b.getEstimateId(ctx, product, price.ComboId),
			ProductCategory: int32(product.ProductCategory),
			ProductId:       int32(product.ProductID),
			RequireLevel:    int32(product.RequireLevelInt),
			FeeAmount:       decimal.NewFromFloat(price.Package).Mul(temp).IntPart(),
			ComboId:         price.ComboId,
			ComboDistance:   price.PackageDistance,
			ComboTime:       int64(price.PackageTime),
			OverTimeFee:     decimal.NewFromFloat(price.TimeUnitPrice).Mul(temp).IntPart(),
			OverDistanceFee: decimal.NewFromFloat(price.NormalUnitPrice).Mul(temp).IntPart(),
			OverTimeUnit:    price.TimeUnit,
			PackageConf:     price.PackageConf,
			TitanId:         price.TitanId,
			ComboType:       price.ComboType,
		})
	}

	// 全局数据
	return &proto.EngageCarEstimateData{
		EstimateTraceId: util.GetTraceIDFromCtxWithoutCheck(ctx),
		EstimateData:    data,
	}, nil
}

func (b *BizLogic) GenProducts(ctx context.Context) ([]*models.Product, BizError.BizError) {
	pcidList := b.LoadSourceIDControlConf(ctx, 17)
	var productMap []*models.Product
	if pcidList != nil {
		list := b.GetProductNTuple(ctx, pcidList)
		for _, p := range list {
			productMap = append(productMap, &models.Product{
				ProductCategory: p.ProductCategory,
				ProductID:       p.ProductId,
				BusinessID:      p.BusinessId,
				ComboType:       p.ComboType,
				CarpoolType:     p.CarpoolType,
				LevelType:       int32(p.LevelType),
				RequireLevel:    strconv.FormatInt(p.RequireLevel, 10),
				RequireLevelInt: p.RequireLevel,
			})
		}
	}

	return productMap, nil
}

func (b *BizLogic) GenPrice(ctx context.Context) (map[string]*plutusSerivce.BillStrategy, BizError.BizError) {
	req := &plutusSerivce.GetStrategiesRequest{
		Area:          b.req.FromCity,
		ProductId:     product_id.ProductIdDiDiChartered,
		District:      b.req.FromDistrictCode,
		ComboType:     0,
		Role:          2, //1司机 2乘客
		DepartureTime: b.req.StartTime,
	}
	if b.req.PageSource == 1 && b.req.ComboId != 0 && b.req.RequireLevel != 0 {
		req.ComboId = int64(b.req.ComboId)
		req.CarLevel = strconv.FormatInt(int64(b.req.RequireLevel), 10)
	}
	resp, err := plutus.GetStrategies(ctx, req)
	if err != nil || resp == nil || resp.Errno != 0 || resp.Data == nil {
		log.Trace.Warnf(ctx, "GenPriceErr", "no price , err=%s", err)
		return nil, BizError.ErrRpcFailed
	}
	strategies, err2 := b.buildProductPrice(ctx, resp.Data)
	if err2 != nil || strategies == nil {
		log.Trace.Warnf(ctx, "buildProductPrice", "no strategies , err=%s", err2)
		return nil, BizError.ErrRpcFailed
	}
	return strategies, nil
}

func (b *BizLogic) buildProductPrice(ctx context.Context, data *plutusSerivce.GetStrategiesResponse) (map[string]*plutusSerivce.BillStrategy, BizError.BizError) {
	var strategies map[string]*plutusSerivce.BillStrategy
	if data.UseStrategies < 0 {
		log.Trace.Warnf(ctx, "plutus_err", "")
		return nil, BizError.ErrRpcFailed
	}
	switch data.UseStrategies {
	case StrategiesRestDay:
		if data.RestDayStrategies != nil {
			strategies = data.RestDayStrategies
		}
	case StrategiesCustom:
		if data.CustomDayStrategies != nil {
			strategies = data.CustomDayStrategies
		}
	default:
		if data.Strategies != nil {
			strategies = data.Strategies
		}
	}
	if strategies == nil {
		return nil, BizError.ErrRpcFailed
	}

	return strategies, nil
}

func (b *BizLogic) getEstimateId(ctx context.Context, product *models.Product, id int64) string {
	traceId := context2.GetTrace(ctx).GetTraceId()
	var randCount = 0
	if ctxValue := reqctx.GetInt(ctx, "#eid_rand_count"); ctxValue >= 0 {
		randCount = ctxValue
	}
	defer func() {
		randCount++
		reqctx.Set(ctx, "#eid_rand_count", randCount)
	}()

	shanghaiLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		log.Trace.Warnf(ctx, "SET QUOTATION DATA ERR:", "err=%s", err)
		return ""
	}
	t := time.Now().In(shanghaiLocation).Unix()
	eidStr := fmt.Sprintf("%s_%d_%s_%d_%d_%d_%d_%d", traceId, product.ProductID, product.RequireLevel, product.ComboType, product.ProductCategory, id, randCount, t)

	hash := md5.Sum([]byte(eidStr))
	return fmt.Sprintf("%x", hash)
}

func (b *BizLogic) LoadSourceIDControlConf(ctx context.Context, sourceID int) []PageSourceProductList {
	//读取Apollo配置内容
	properties := map[string]string{
		"source_id": strconv.Itoa(sourceID),
	}
	conditions := model.NewCondition(properties)
	confResults, err := apollo.GetConfigsByNamespaceAndConditions(ctx, "source_id_open_conf", conditions)
	if err != nil || len(confResults) == 0 {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "load_source_id_src_apollo fail %v", err)
		return nil
	}
	//解析配置文件
	configs := make([]PageSourceConf, 0)
	if err = jsoniter.Unmarshal(confResults, &configs); err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "parse PageSourceConf err with %v and conf %s", err, string(confResults))
		return nil
	}
	oneConfig := configs[0]
	if oneConfig.ProductList != nil {
		return oneConfig.ProductList
	}

	return nil
}

func (b *BizLogic) GetProductNTuple(ctx context.Context, producList []PageSourceProductList) map[int]*product_center.ProductCategoryConfig {
	productMap := make(map[int]*product_center.ProductCategoryConfig)
	for _, p := range producList {
		aNTuple := product_center.GetNTupleByPCId(ctx, int64(p.ProductCategory))
		productMap[p.ProductCategory] = aNTuple
	}
	return productMap
}

type PageSourceConf struct {
	SourceId    string                  `json:"source_id"`
	ProductList []PageSourceProductList `json:"product_list"`
}

type PageSourceProductList struct {
	ProductCategory int    `json:"product_category"`
	ApolloName      string `json:"apollo_name"`
}
