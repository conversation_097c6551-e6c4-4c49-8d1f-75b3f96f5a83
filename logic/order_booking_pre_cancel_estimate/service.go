package order_booking_pre_cancel_estimate

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/logic/anycar_v3"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	trace "git.xiaojukeji.com/lego/context-go"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

func EstimateBookingPreCancel(ctx context.Context, req *proto.AnyCarEstimateReq) (rspData *proto.NewFormEstimateResponse, errno int) {
	var (
		products []*biz_runtime.ProductInfoFull
	)

	defer func() {
		util.Go(ctx, func() {
			anycar_v3.AddPublicLog(ctx, products, rspData)
		})
	}()

	productsGenerator, errno := InitProductGenerator(ctx, req)
	if consts.NoErr != errno {
		return nil, errno
	}

	products, err := productsGenerator.GenProducts(ctx)
	if len(products) == 0 || err != nil {
		return nil, consts.ErrnoNoProductOpen
	}
	log.Trace.Debugf(ctx, trace.DLTagUndefined, "products: %s", util.JustJsonEncode(products))

	rspData, errno = anycar_v3.RenderProductList(ctx, productsGenerator, products, "pOrderBookingPreCancelEstimate")
	if consts.NoErr != errno {
		return nil, errno
	}

	return rspData, consts.NoErr
}
