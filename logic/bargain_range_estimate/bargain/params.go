package bargain_range

import (
	"context"
	"strconv"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

type BaseParams struct {
	OrderInfo    *dos.OrderInfo
	UserInfo     *passport.UserInfo
	OrderFeature *ufs.OrderFeature
	Request      *proto.BargainRangeEstimateReq
}

func InitParams(ctx context.Context, req *proto.BargainRangeEstimateReq) (params *BaseParams, errno int) {
	//校验关键参数
	if req.EstimateId == nil || req.FastCarEstimateFee == nil || req.SpFastCarEstimateFee == nil || req.BasicFee == nil {
		log.Trace.Errorf(ctx, trace.DLTagUndefined, "bargain range check params error")
		return nil, consts.ErrnoParams
	}
	//请求 passport
	tryGetUserInfo := func(ctx context.Context, token string, accessKeyID int32) (*passport.UserInfo, error) {
		if token != "" {
			return passport.GetUserInfo(ctx, token, "")
		}

		param := ApolloModel.NewUser("").With("access_key_id", strconv.Itoa(int(accessKeyID)))
		toggle, err2 := ApolloSDK.FeatureToggle("check_token_pMultiEstimatePriceV2", param)
		if err2 != nil || toggle.IsAllow() {
			return nil, BizError.ErrNotLogin
		}
		return &passport.UserInfo{}, nil
	}
	userInfo, err := tryGetUserInfo(ctx, req.Token, req.AccessKeyId)
	if err != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "getUserInfo: %v", err)
		return nil, consts.ErrnoGetUserInfo
	}

	params = &BaseParams{
		UserInfo: userInfo,
		Request:  req,
	}
	return params, consts.NoErr
}
