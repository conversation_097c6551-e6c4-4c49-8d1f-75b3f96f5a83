package pbd_anycar_estimate

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pbd_estimate"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type BizLogic struct {
	generator *biz_runtime.ProductsGenerator
}

func (b *BizLogic) DoBizLogic(ctx context.Context) (*proto.B2BEstimateData, error) {
	products, err := b.generator.GenProducts(ctx)
	if err != nil {
		return nil, err
	}

	resp, err := pbd_estimate.Render(ctx, b.generator.BaseReqData, products)
	defer func() {
		pbd_estimate.AddPublicLog(ctx, products)
	}()
	// todo sxm 加 写mq 仿照：logic/business_estimate/mq/mq_builder.go 搞v3
	return resp, err
}
