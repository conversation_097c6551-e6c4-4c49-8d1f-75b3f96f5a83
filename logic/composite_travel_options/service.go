package composite_travel_options

import (
	"context"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_options/model"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_options/rpc"
	trace "git.xiaojukeji.com/lego/context-go"
	"runtime/debug"
	"sync"
)

type CompositeTravelOptionsService struct {
	// 前端入参
	*model.Request
	Area *model.WycAreaInfo
	// RPC
	rpcList []TravelPlanRPC
}

type TravelPlanRPC interface {
	// Execute 执行RPC
	Execute(ctx context.Context, request *model.Request)
	// GetModel 获取方案模型
	RenderTimeText(ctx context.Context) (string, string)
}

func NewService(ctx context.Context, req *proto.CompositeTravelOptionsReq) (*CompositeTravelOptionsService, error) {

	baseRequest := model.NewRequest(req)

	var rpcList []TravelPlanRPC
	res := &CompositeTravelOptionsService{}

	// 构造位置信息
	baseRequest.BuildAreaInfo(ctx, req)

	// 构造用户信息
	if e := baseRequest.BuildUserInfo(ctx, req); e != nil {
		return nil, e
	}

	// 构造baseHandle
	baseHandle, err := rpc.NewBaseHandle(ctx)
	if baseHandle == nil || err != nil {
		return nil, err
	}

	// 注册网约车(强依赖)
	if r, err := rpc.NewCarHailingRPC(ctx, baseRequest, baseHandle); err != nil {
		return nil, err
	} else {
		res.Area = r.WycAreaInfo
		rpcList = append(rpcList, r)
	}

	// 注册综合推荐
	if r := rpc.NewCompositeRecommendRPC(ctx, baseRequest, baseHandle); r != nil {
		rpcList = append(rpcList, r)
	}

	// 注册公交
	if r := rpc.NewPublicTransitRPC(ctx, baseRequest, baseHandle); r != nil {
		rpcList = append(rpcList, r)
	}

	// 注册全程骑行
	if r := rpc.NewBicyclingTravelRpc(ctx, baseRequest, baseHandle); r != nil {
		rpcList = append(rpcList, r)
	}

	// 注册全程步行
	if r := rpc.NewWalkingTravelRpc(ctx, baseRequest, baseHandle); r != nil {
		rpcList = append(rpcList, r)
	}

	// 注册代驾
	if r := rpc.NewDaijiaTravelRpc(ctx, baseRequest, baseHandle); r != nil {
		rpcList = append(rpcList, r)
	}

	res.Request = baseRequest
	res.rpcList = rpcList

	return res, nil
}

func (c *CompositeTravelOptionsService) Do(ctx context.Context) *proto.CompositeTravelOptionsData {
	subTypeToTimeText := make(map[string]string)

	wg := sync.WaitGroup{}
	for _, rpcOne := range c.rpcList {
		wg.Add(1)
		go func(ctx context.Context, request *model.Request, rpcOne TravelPlanRPC, wgDone func()) {
			defer wgDone()
			defer InnerRecover(ctx)()

			rpcOne.Execute(ctx, request)
		}(ctx, c.Request, rpcOne, wg.Done)
	}
	wg.Wait()

	for _, rpcOne := range c.rpcList {
		subType, timeText := rpcOne.RenderTimeText(ctx)
		subTypeToTimeText[subType] = timeText
	}

	return &proto.CompositeTravelOptionsData{
		TabExtraData: &proto.TabExtraData{
			CompositeRecommend: buildCompositeRecommend(subTypeToTimeText),
			Classify:           buildClassify(subTypeToTimeText),
			PublicTransit:      buildPublicTransit(subTypeToTimeText),
			Bicycle:            buildBicycle(subTypeToTimeText),
			Walking:            buildWalking(subTypeToTimeText),
			Daijia:             buildDaijia(subTypeToTimeText),
		},
		Bubble: GetBubble(c.Request, ctx),
	}
}

func buildCompositeRecommend(subTypeToTimeText map[string]string) *proto.CompositeRecommend {

	if subTypeToTimeText == nil {
		return nil
	}

	if text, exist := subTypeToTimeText[model.SubType_CompositeRecommend]; exist && text != "" {
		return &proto.CompositeRecommend{
			TimeText: text,
		}

	}

	return nil

}

func buildClassify(subTypeToTimeText map[string]string) *proto.Classify {

	if subTypeToTimeText == nil {
		return nil
	}

	if text, exist := subTypeToTimeText[model.SubType_Classify]; exist && text != "" {
		return &proto.Classify{
			TimeText: text,
		}

	}
	return nil
}

func buildPublicTransit(subTypeToTimeText map[string]string) *proto.PublicTransit {

	if subTypeToTimeText == nil {
		return nil
	}

	if text, exist := subTypeToTimeText[model.SubType_PublicTransit]; exist && text != "" {
		return &proto.PublicTransit{
			TimeText: text,
		}

	}

	return nil
}

func buildBicycle(subTypeToTimeText map[string]string) *proto.Bicycle {

	if subTypeToTimeText == nil {
		return nil
	}

	if text, exist := subTypeToTimeText[model.SubType_Bicycle]; exist && text != "" {
		return &proto.Bicycle{
			TimeText: text,
		}

	}

	return nil
}

func buildWalking(subTypeToTimeText map[string]string) *proto.Walking {

	if subTypeToTimeText == nil {
		return nil
	}

	if text, exist := subTypeToTimeText[model.SubType_Walking]; exist && text != "" {
		return &proto.Walking{
			TimeText: text,
		}

	}

	return nil
}

func buildDaijia(subTypeToTimeText map[string]string) *proto.Daijia {

	if subTypeToTimeText == nil {
		return nil
	}

	if text, exist := subTypeToTimeText[model.SubType_Daijia]; exist && text != "" {
		return &proto.Daijia{
			TimeText: text,
		}

	}

	return nil
}

func InnerRecover(ctx context.Context) func() {
	return func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
		}
	}
}
