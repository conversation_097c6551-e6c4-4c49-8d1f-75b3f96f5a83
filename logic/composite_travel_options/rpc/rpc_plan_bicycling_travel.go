package rpc

import (
	"context"
	route "git.xiaojukeji.com/dirpc/dirpc-go-http-OrderRouteApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/bicycle_route"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_options/model"
	"github.com/spf13/cast"
	"time"
)

type BicyclingTravelRpc struct {
	baseRequest *model.Request
	RouteDetail []*route.BicyclingRouteDetail
	*BaseHandle
}

func NewBicyclingTravelRpc(ctx context.Context, baseRequest *model.Request, baseHandle *BaseHandle) *BicyclingTravelRpc {
	req := baseRequest.CompositeTravelOptionsReq
	// 参数校验
	if req.FromLat == 0 || req.FromLng == 0 || req.ToLat == 0 || req.ToLng == 0 ||
		req.MapType == "" || req.FromPoiId == "" {
		log.Trace.Warnf(ctx, PublicTransitRPCLogTag, "req is lost")
		return nil
	}

	return &BicyclingTravelRpc{
		baseRequest: baseRequest,
		BaseHandle:  baseHandle,
	}
}

func (b *BicyclingTravelRpc) Execute(ctx context.Context, request *model.Request) {
	srcPoint := &route.DoublePoint{
		Lat:  request.FromLat,
		Lng:  request.FromLng,
		Name: util.StringPtr(request.FromName),
		UID:  util.StringPtr(request.FromPoiId),
	}

	if request.FromName == consts.MyLocationFromName {
		srcPoint.UID = util.StringPtr(consts.MyLocationFromPoiId)
	}

	tripReq := &route.RecommendBicyclingTripReq{
		Token:    request.GetToken(),
		UserId:   cast.ToString(request.UserInfo.UID),
		BizType:  85004,
		SrcPoint: srcPoint,
		DstPoint: &route.DoublePoint{
			Lat:  request.ToLat,
			Lng:  request.ToLng,
			Name: util.StringPtr(request.ToName),
			UID:  util.StringPtr(request.ToPoiId),
		},
		SrcCityId:   util.Int32Ptr(request.AreaInfo.FromCityId),
		DstCityId:   util.Int32Ptr(request.AreaInfo.ToCityId),
		VehicleType: nil,
		Caller:      "internal_api",
		ReqType:     route.ReqSourceType_SERVER_API,
		ReqTime:     time.Now().Unix(),
		DidiVersion: util.StringPtr(request.AppVersion),
	}

	res := bicycle_route.RecommendBicyclingTripInfo(ctx, tripReq)

	if res == nil {
		return
	}

	b.RouteDetail = res.RouteDetail
}
func (b *BicyclingTravelRpc) RenderTimeText(ctx context.Context) (string, string) {
	if b.RouteDetail == nil || len(b.RouteDetail) < 1 {
		return model.SubType_Bicycle, ""
	}

	var (
		targetType int32

		totalTime int32
	)

	if b.baseRequest.GetCyclingStyle() == model.CyclingStyle_Bike {
		targetType = 1
	} else if b.baseRequest.GetCyclingStyle() == model.CyclingStyle_Electric_Vehicle {
		targetType = 2
	} else {
		targetType = 1
	}

	for _, route := range b.RouteDetail {

		if route == nil {
			continue
		}

		if route.GetVehicleType() == targetType {
			// 只有一个数据
			item := route.RouteDetail[0]
			if item != nil {
				totalTime = item.GetEtaSec()
				break
			}
		}

	}

	return model.SubType_Bicycle, b.GetEstimateTime(ctx, int64(totalTime))

}
