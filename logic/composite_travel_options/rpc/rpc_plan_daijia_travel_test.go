package rpc

import (
	"context"
	Daijia "git.xiaojukeji.com/dirpc/dirpc-go-http-DaijiaKopService"
	"git.xiaojukeji.com/gulfstream/bronze-door-sdk-go/common/utils"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/daijia"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_options/model"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestNewDaijiaTravelRpc(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		ctx := context.Background()

		baseRequest := &model.Request{}
		baseHandle := &BaseHandle{}
		res := NewDaijiaTravelRpc(ctx, baseRequest, baseHandle)

		assert.NotNil(t, res)
	})
}

func TestExecute(t *testing.T) {
	ctx := context.Background()
	request := &model.Request{
		CompositeTravelOptionsReq: &proto.CompositeTravelOptionsReq{
			Token:          "123",
			StopoverPoints: utils.StringPtr("1234"),
		},
		UserInfo: &passport.UserInfo{},
		AreaInfo: &model.WycAreaInfo{},
	}
	d := &DaijiaTravelRpc{}
	t.Run("1", func(t *testing.T) {
		patches := gomonkey.ApplyFunc(daijia.GetTripEstimation,
			func(ctx context.Context, req *Daijia.GetTripEstimationReq) *Daijia.TripEstimationResult {
				return &Daijia.TripEstimationResult{}
			})

		defer patches.Reset()
		d.Execute(ctx, request)
		assert.NotNil(t, d)
	})
}

func TestRenderTimeText(t *testing.T) {
	ctx := context.Background()
	t.Run("1", func(t *testing.T) {
		d := &DaijiaTravelRpc{}
		_, res := d.RenderTimeText(ctx)
		assert.Equal(t, res, "")

	})

}

func TestGetEstimateTime(t *testing.T) {
	ctx := context.Background()
	t.Run("1", func(t *testing.T) {
		d := &DaijiaTravelRpc{}
		res := d.getEstimateTime(ctx, 0)
		assert.Equal(t, res, "")
	})

	t.Run("2", func(t *testing.T) {
		d := &DaijiaTravelRpc{
			BaseHandle: &BaseHandle{
				conf: BaseHandleDcmp{
					BaseTimeConf: &BaseTimeConf{
						Minutes: "{{minutes}}分钟",
					},
				},
			},
		}
		res := d.getEstimateTime(ctx, 54)
		assert.Equal(t, res, "54分钟")
	})

	t.Run("3", func(t *testing.T) {
		d := &DaijiaTravelRpc{
			BaseHandle: &BaseHandle{
				conf: BaseHandleDcmp{
					BaseTimeConf: &BaseTimeConf{
						Hours: "{{hours}}小时",
					},
				},
			},
		}
		res := d.getEstimateTime(ctx, 60)
		assert.Equal(t, res, "1小时")
	})

	t.Run("4", func(t *testing.T) {
		d := &DaijiaTravelRpc{
			BaseHandle: &BaseHandle{
				conf: BaseHandleDcmp{
					BaseTimeConf: &BaseTimeConf{
						HoursMinutesWithoutMinuteUnit: "{{hours}}小时{{minutes}}",
					},
				},
			},
		}
		res := d.getEstimateTime(ctx, 61)
		assert.Equal(t, res, "1小时1")
	})
}
