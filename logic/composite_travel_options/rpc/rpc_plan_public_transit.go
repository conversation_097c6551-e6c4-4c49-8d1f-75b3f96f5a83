package rpc

import (
	"context"
	metro "git.xiaojukeji.com/dirpc/dirpc-go-http-TransitWind"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/transit_wind"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_options/model"
	"github.com/spf13/cast"
)

type PublicTransitRPC struct {
	baseRequest *model.Request
	Res         *metro.InnerRouteplanQueryRes
	*BaseHandle
}

const (
	PublicTransitRPCLogTag   = "PublicTransitRPC"
	ModeTypeTrans_Walk       = "0"
	ModeTypeTrans_Bus        = "1"
	ModeTypeTrans_Subway     = "2"
	ModeTypeTrans_Bus_Subway = "3"
)

func NewPublicTransitRPC(ctx context.Context, baseRequest *model.Request, baseHandle *BaseHandle) *PublicTransitRPC {
	req := baseRequest.CompositeTravelOptionsReq
	// 参数校验
	if req.FromLat == 0 || req.FromLng == 0 || req.ToLat == 0 || req.ToLng == 0 ||
		req.MapType == "" || req.FromPoiId == "" {
		log.Trace.Warnf(ctx, PublicTransitRPCLogTag, "req is lost")
		return nil
	}

	return &PublicTransitRPC{
		baseRequest: baseRequest,
		BaseHandle:  baseHandle,
	}
}

func (p *PublicTransitRPC) Execute(ctx context.Context, request *model.Request) {
	originPoiId := request.FromPoiId
	if request.FromName == consts.MyLocationFromName {
		originPoiId = consts.MyLocationFromPoiId
	}
	simpleReq := &metro.InnerRouteplanQueryReq{
		Caller: &metro.Caller{
			ProductId: "111",
			CallerId:  "comprehensive_rec",
			AccKey:    "w9mQQxBLInvx4Gm3d1Hv",
		},
		Uid:              cast.ToString(request.UserInfo.UID),
		Ddfp:             request.GetDdfp(),
		OriginLng:        request.GetFromLng(),
		OriginLat:        request.GetFromLat(),
		OriginPoiId:      originPoiId,
		DestinationLng:   request.GetToLng(),
		DestinationLat:   request.GetToLat(),
		DestinationPoiId: request.GetToPoiId(),
	}
	if request.UserInfo != nil {
		simpleReq.Uid = cast.ToString(request.UserInfo.UID)
	}

	res := transit_wind.InnerRouteplanQuery(ctx, simpleReq)
	if res == nil {
		return
	}

	p.Res = res.Data

}

func (p *PublicTransitRPC) RenderTimeText(ctx context.Context) (string, string) {
	if p.Res == nil {
		return model.SubType_PublicTransit, ""
	}
	return model.SubType_PublicTransit, p.Res.DescText
}
