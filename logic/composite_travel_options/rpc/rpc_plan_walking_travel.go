package rpc

import (
	"context"
	walkApi "git.xiaojukeji.com/dirpc/dirpc-go-http-DolphinApiService"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/walking_route"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_options/model"
	"github.com/spf13/cast"
	"time"
)

type WalkingTravelRpc struct {
	baseRequest *model.Request
	res         *walkApi.PassengerWalkRouteRes
	*BaseHandle
}

func NewWalkingTravelRpc(ctx context.Context, baseRequest *model.Request, baseHandle *BaseHandle) *WalkingTravelRpc {
	req := baseRequest.CompositeTravelOptionsReq
	// 参数校验
	if req.FromLat == 0 || req.FromLng == 0 || req.ToLat == 0 || req.ToLng == 0 ||
		req.MapType == "" || req.FromPoiId == "" {
		log.Trace.Warnf(ctx, PublicTransitRPCLogTag, "req is lost")
		return nil
	}

	return &WalkingTravelRpc{
		baseRequest: baseRequest,
		BaseHandle:  baseHandle,
	}
}

func (w *WalkingTravelRpc) Execute(ctx context.Context, request *model.Request) {
	startPoiId := util.StringPtr(request.FromPoiId)
	if request.FromName == consts.MyLocationFromName {
		startPoiId = util.StringPtr(consts.MyLocationFromPoiId)
	}
	tripReq := &walkApi.PassengerWalkRouteReq{
		StartLng:   request.FromLng,
		StartLat:   request.FromLat,
		EndLng:     request.ToLng,
		EndLat:     request.ToLat,
		Timestamp:  time.Now().Unix(),
		BizType:    util.Int32Ptr(90001),
		UserId:     util.Int64Ptr(cast.ToInt64(request.UserInfo.UID)),
		Caller:     util.StringPtr("walk_bubble"),
		Token:      util.StringPtr(request.Token),
		StartPoiId: startPoiId,
		EndPoiId:   util.StringPtr(request.ToPoiId),
	}
	res := walking_route.RecommendWalkingTripInfo(ctx, tripReq)

	w.res = res
}

func (w *WalkingTravelRpc) RenderTimeText(ctx context.Context) (string, string) {
	if w.res == nil || w.res.GetDuration() <= 0 {
		return model.SubType_Walking, ""
	}

	totalTime := w.res.GetDuration()
	return model.SubType_Walking, w.GetEstimateTime(ctx, int64(totalTime))

}
