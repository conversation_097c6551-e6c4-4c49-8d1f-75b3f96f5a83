package rpc

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_options/model"
)

type CompositeRecommendRPC struct {
	baseRequest *model.Request
	conf        *CompositeRecommendConf
	*BaseHandle
}

type CompositeRecommendConf struct {
	TimeText string `json:"time_text"`
}

const (
	CombinedTravelRPCLogTag = "CombinedTravelRPC"
)

func NewCompositeRecommendRPC(ctx context.Context, baseRequest *model.Request, baseHandle *BaseHandle) *CompositeRecommendRPC {
	// 文案配置
	conf, err := initCompositeRecommendDCMPConf(ctx)
	if err != nil || conf == nil {
		log.Trace.Warnf(ctx, CombinedTravelRPCLogTag, "init CombinedDCMPConf err:[%v]", err)
		return nil
	}

	return &CompositeRecommendRPC{
		baseRequest: baseRequest,
		BaseHandle:  baseHandle,
		conf:        conf,
	}
}

func initCompositeRecommendDCMPConf(ctx context.Context) (*CompositeRecommendConf, error) {
	conf := new(CompositeRecommendConf)
	str := dcmp.GetDcmpContent(ctx, "new_one_stop-options_composite_recommend_conf", nil)
	err := json.Unmarshal([]byte(str), conf)
	if err != nil || conf == nil {
		return nil, err
	}
	return conf, nil
}

// 执行rpc
func (c *CompositeRecommendRPC) Execute(ctx context.Context, req *model.Request) {

}

func (c *CompositeRecommendRPC) RenderTimeText(ctx context.Context) (string, string) {

	return model.SubType_CompositeRecommend, c.conf.TimeText

}
