package wyc_transfer

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"strconv"
	"time"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_options/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/selection"
)

type CarEstimateData struct {
	ProductCategory int64  `json:"product_category"`
	EstimateID      string `json:"estimate_id"`
	BusinessID      int64  `json:"business_id"`
	RequireLevel    string `json:"require_level"` // 因为php的nTuple sdk 和 go里的NTuple 都是string
	ComboType       int64  `json:"combo_type"`
	IsSelected      int32  `json:"is_selected"`
	ProductID       int64  `json:"product_id"`
	LevelType       int32  `json:"level_type"`

	CarTitle  string  `json:"car_title"` // 车型数据
	FeeMsg    string  `json:"fee_msg"`
	FeeAmount float64 `json:"fee_amount"` // 价格信息

	EtpInfo      *AthenaApiv3.EstimatedTimeInfo `json:"etp_info"`      // 预期信息
	DriverMeter  int64                          `json:"driver_meter"`  // 打车距离
	RouteIdList  []string                       `json:"route_id_list"` // 路线信息
	DriverMinute int64                          `json:"driver_minute"` // 打车时间
}

func GetWycData(ctx context.Context, request *model.Request, areaInfo *model.WycAreaInfo) (carList []*CarEstimateData) {
	productsGenerator, err := biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("pCompositeTravel"),
		BuildParams(ctx, request, areaInfo),
	)
	if err != nil {
		log.Trace.Warnf(ctx, "GetWycData", "build wyc req err:[%v]", err)
		return
	}
	productsGenerator.SetNeedMember(true)
	productsGenerator.SetSendReqKafka(true)
	RegisterRpcProcessWithBasicProducts(ctx, productsGenerator)

	productFullList, err := productsGenerator.GenProducts(ctx)
	if len(productFullList) == 0 || err != nil {
		// 处理错误情况
		log.Trace.Warnf(ctx, "GetWycData", "GenProducts err:[%v]", err)
		return
	}

	selectedProductFullList := filterProductFullList(ctx, productFullList)
	for _, product := range selectedProductFullList {
		prov := &WycAdapter{
			ProductInfoFull: product,
			WycAreaInfo:     areaInfo,
			Request:         request,
		}
		result := &CarEstimateData{
			ProductCategory: product.GetProductCategory(),
			EstimateID:      product.GetEstimateID(),
			BusinessID:      product.Product.BusinessID,
			RequireLevel:    product.Product.RequireLevel,
			ComboType:       product.Product.ComboType,
			IsSelected:      consts.Checked,
			ProductID:       product.GetProductId(),
			LevelType:       product.Product.LevelType,
		}
		result.CarTitle = car_info.GetCarName(ctx, prov)

		msg, amount, _ := fee_info_render.GetFeeInfo(ctx, prov)
		result.FeeMsg = msg
		f, err1 := strconv.ParseFloat(amount, 64)
		if err1 == nil {
			result.FeeAmount = f
		}
		result.RouteIdList = product.GetRouteIdList()
		if etInfo := prov.GetETInfoByPcId(int32(product.GetProductCategory())); etInfo != nil {
			// 只取大于0的情况
			result.EtpInfo = etInfo
		}

		if product.GetBillInfo() != nil {
			result.DriverMeter = product.GetBillInfo().DriverMetre
			result.DriverMinute = product.GetBillInfo().DriverMinute
		}

		carList = append(carList, result)
	}
	return
}

// 筛选勾选品类, 无默勾返回快车
func filterProductFullList(ctx context.Context, productFullList []*biz_runtime.ProductInfoFull) []*biz_runtime.ProductInfoFull {
	var (
		selectedProductFullList []*biz_runtime.ProductInfoFull
		fastCarProduct          *biz_runtime.ProductInfoFull
	)

	for _, product := range productFullList {
		prov := &WycAdapter{ProductInfoFull: product}

		if product.GetProductCategory() == estimate_pc_id.EstimatePcIdFastCar {
			fastCarProduct = product
		}

		// 判断默勾  非默勾不展示
		IsSelected := selection.GetSelection(ctx, prov)
		if IsSelected != consts.Checked {
			continue
		}
		selectedProductFullList = append(selectedProductFullList, product)
	}

	if len(selectedProductFullList) < 1 && fastCarProduct != nil {
		// 无默勾  兜底快车
		selectedProductFullList = append(selectedProductFullList, fastCarProduct)
	} else if len(selectedProductFullList) < 1 && len(productFullList) > 0 {
		// 无默勾  无快车 兜底第一个车型
		selectedProductFullList = append(selectedProductFullList, productFullList[0])
	}

	return selectedProductFullList
}

func BuildParams(ctx context.Context, request *model.Request, areaInfo *model.WycAreaInfo) biz_runtime.GeneratorOption {
	return func(pg *biz_runtime.ProductsGenerator) error {

		pg.BaseReqData.CommonInfo = models.CommonInfo{
			AppVersion:   request.GetAppVersion(),
			MenuID:       consts.MenuIDDaCheAnyCar,
			PageType:     page_type.PageTypeCompositeTravel, // 限制品类
			AccessKeyID:  request.GetAccessKeyId(),
			ClientType:   request.GetClientType(),
			OriginID:     1, // 滴滴
			Lang:         request.GetLang(),
			OrderType:    request.GetOrderType(),
			PlatformType: request.GetPlatformType(),
			Channel:      request.GetChannel(),
			UserType:     0, // todo 可以处理req  暂时不支持企业付
			From:         request.FromName,

			CallCarType:            0,
			CallCarPhone:           "",
			ScreenPixels:           "",
			ScreenScale:            0,
			Imei:                   "",
			AgentType:              "",
			TerminalID:             0,
			RouteID:                0,
			DepartureRange:         nil,
			ActivityID:             0,
			PaymentsType:           0,
			LuxurySelectCarlevels:  "",
			LuxurySelectDriver:     "",
			AirportType:            0,
			BusinessOption:         "",
			TrafficNumber:          "",
			TrafficDepTime:         "",
			AirportId:              0,
			FlightDepCode:          "",
			FlightDepTerminal:      "",
			CompareEstimateTraceId: "",
			CompareEstimateId:      "",
			TabList:                "",
			StopoverPoints:         request.StopoverPoints,
		}
		if request.GetOrderType() == 0 {
			pg.BaseReqData.CommonInfo.DepartureTime = time.Now().Unix()
		} else {
			pg.BaseReqData.CommonInfo.DepartureTime = util.ToInt64(request.GetDepartureTime())
		}

		pg.BaseReqData.AreaInfo = models.AreaInfo{
			District: areaInfo.FromDistrict,

			FromCounty:  areaInfo.FromCounty,
			FromLat:     request.WycFromLat,
			FromLng:     request.WycFromLng,
			FromPoiID:   request.WycFromPoiId,
			FromPoiType: request.WycFromPoiType,
			Area:        areaInfo.FromCityId,
			City:        areaInfo.FromCityId,
			FromName:    request.WycFromName,
			FromAddress: request.WycFromAddress,

			ToCounty:  areaInfo.ToCounty,
			ToLat:     request.WycToLat,
			ToLng:     request.WycToLng,
			ToAddress: request.WycToAddress,
			ToName:    request.WycToName,
			ToPoiType: request.WycToPoiType,
			ToPoiID:   request.WycToPoiId,
			ToArea:    areaInfo.ToCityId,

			CurLng: request.Lng,
			CurLat: request.Lat,

			MapType:          request.MapType,
			AbstractDistrict: areaInfo.AbstractDistrict,

			StartingName:      "",
			DestName:          "",
			StopoverPointInfo: nil,
			TripCountry:       "",
			MetroFenceID:      0,
		}

		if request.StopoverPoints != nil && *request.StopoverPoints != "" {
			stopoverPoints := []*price_api.StopoverPointInfo{}
			err := json.Unmarshal([]byte(*request.StopoverPoints), &stopoverPoints)
			if err == nil {
				pg.BaseReqData.AreaInfo.StopoverPointInfo = stopoverPoints
			}

		}

		if user := request.UserInfo; request.UserInfo != nil {
			pg.BaseReqData.PassengerInfo = models.PassengerInfo{
				UID:      int64(user.UID),
				PID:      int64(user.PID),
				Phone:    user.Phone,
				Role:     user.Role,
				Channel:  user.Channel,
				AppID:    user.AppID,
				UserType: 1, //     暂时不识别企业付
				OriginId: user.OriginId,
			}
		}

		pg.IsBaseReqInit = true
		return nil
	}
}
func RegisterRpcProcessWithBasicProducts(ctx context.Context, generator *biz_runtime.ProductsGenerator) {

	// 冒泡推荐
	if recommendRPC := rpc_process.NewBubbleRecommendInfoRPC(generator.BaseReqData, rpc_process.EstimateType); recommendRPC != nil {
		generator.RegisterAfterPriceRPCProcess(recommendRPC)
		generator.RegisterFinalFilter(recommendRPC)
	}
}
