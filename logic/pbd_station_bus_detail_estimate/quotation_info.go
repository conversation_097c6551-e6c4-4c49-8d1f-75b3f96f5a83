package pbd_station_bus_detail_estimate

import (
	"context"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type quotationInfo struct {
	EstimateId string
}

func NewQuotationInfo(estimateId string) *quotationInfo {
	return &quotationInfo{
		EstimateId: estimateId,
	}
}

// Do ...
func (q *quotationInfo) Do(ctx context.Context) (*biz_runtime.Quotation, BizError.BizError) {
	resp, err := price_api.GetQuotationBatch(ctx, q.buildRequest())
	if err != nil {
		return nil, BizError.ErrInvalidArgument
	}

	if resp.Errno != 0 {
		return nil, BizError.ErrInvalidArgument
	}

	if len(resp.Data) <= 0 {
		return nil, BizError.ErrInvalidArgument
	}

	return q.formatResp(resp), nil
}

// buildRequest ...
func (q *quotationInfo) buildRequest() *price_api.PriceQuotationBatch {
	if q.EstimateId == "" {
		return nil
	}

	estimateIdList := make([]string, 0)
	estimateIdList = append(estimateIdList, q.EstimateId)

	return &price_api.PriceQuotationBatch{
		EstimateIdList: estimateIdList,
	}
}

// formatResp ...
func (q *quotationInfo) formatResp(resp *PriceApi.GetQuotationBatchResp) *biz_runtime.Quotation {
	if resp == nil || q.EstimateId == "" {
		return nil
	}

	estimateInfo := biz_runtime.Quotation(resp.Data[q.EstimateId])

	return &estimateInfo
}
