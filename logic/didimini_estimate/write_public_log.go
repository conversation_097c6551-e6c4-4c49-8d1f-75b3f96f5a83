package didimini

import (
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/logutil"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
)

const OperaKeyEstimateData = "g_order_estimate_price"

func AddPublicLog(ctx context.Context, product *biz_runtime.ProductInfoFull) {
	// 压测流量不写Public日志
	if util.IsPressureTraffic(ctx) {
		return
	}

	logInfo := logutil.GenerateCommonProductMap(ctx, product)
	if logInfo == nil {
		return
	}
	log.Public.Public(ctx, OperaKeyEstimateData, logInfo)
}
