package didimini

import (
	"context"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/input"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/route_id_list"
	jsoniter "github.com/json-iterator/go"
)

const (
	didiminiCOnfDcmpKey = "didi_mini-config_text"
)

type Render struct {
	product *biz_runtime.ProductInfoFull
}

func NewRender(product *biz_runtime.ProductInfoFull) (r *Render) {
	// 初始化配置
	r = &Render{
		product: product,
	}
	return
}

type didiminiRenderConf struct {
	FeeMsg       string `json:"fee_msg"`
	FeeDetailUrl string `json:"fee_detail_url"`
	AnimationGIF string `json:"animation_gif"`
}

func (r *Render) Render(ctx context.Context) (resp *proto.PDidiMiniEstimateData, err error) {
	defer func() {
		util.Go(ctx, func() {
			AddPublicLog(ctx, r.product)
		})
	}()

	conf := &didiminiRenderConf{}
	if err = jsoniter.UnmarshalFromString(dcmp.GetDcmpPlainContent(ctx, didiminiCOnfDcmpKey), conf); err != nil {
		return nil, err
	}

	resp = &proto.PDidiMiniEstimateData{
		EstimateData: make([]*proto.DidiMiniEstimateData, 0, 1),
	}

	feeDescResp := r.renderFeeDescList(ctx)
	product := r.product.Product
	data := &proto.DidiMiniEstimateData{
		RequireLevel:    util.ToInt32(product.RequireLevel),
		BusinessId:      int32(product.BusinessID),
		ProductId:       int32(product.ProductID),
		ComboType:       int32(product.ComboType),
		ProductCategory: int32(product.ProductCategory),
		LevelType:       product.LevelType,
		CarpoolType:     int32(product.CarpoolType),
		EstimateId:      product.EstimateID,
		FeeAmount:       r.product.GetEstimateFee(),
		FeeMsg: dcmp.TranslateTemplate(conf.FeeMsg, map[string]string{
			"num": fmt.Sprintf("%.1f", r.product.GetEstimateFee()),
		}),
		FeeDescList: feeDescResp,
		RouteIdList: route_id_list.GetRouteIdList(ctx, r.product),
	}
	resp.FeeDetailUrl = conf.FeeDetailUrl
	resp.AnimationGif = conf.AnimationGIF
	resp.EstimateData = append(resp.EstimateData, data)
	return resp, nil
}

func (r *Render) renderFeeDescList(ctx context.Context) []*proto.NewFormFeeDesc {
	env := fee_desc_engine.NewEnv(consts.DidiMiniForm).SetApolloParams(r.product).SetDcmpKey(consts.DidiMiniFeeDesc)
	feeDescList := fee_desc_engine.NewFeeEngine(input.BuildNormalFeeInput(ctx, r.product, consts.DidiMiniForm), env).SetProductCategory(r.product.GetProductCategory()).Do(ctx)
	feeDescResp := make([]*proto.NewFormFeeDesc, 0, len(feeDescList))
	for _, feeDesc := range feeDescList {
		desc := &proto.NewFormFeeDesc{
			BorderColor: feeDesc.BorderColor,
			Content:     feeDesc.Content,
			Icon:        feeDesc.Icon,
			TextColor:   &feeDesc.TextColor,
		}
		if feeDesc.Fee != nil {
			desc.Amount = feeDesc.Fee.Amount
			desc.Type = feeDesc.Fee.Type
		}
		feeDescResp = append(feeDescResp, desc)
	}
	return feeDescResp
}
