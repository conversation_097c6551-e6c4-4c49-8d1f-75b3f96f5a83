package didimini

import (
	"context"
	"errors"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

var tag = "_didimini_estimate"

type Service struct {
	generator *biz_runtime.ProductsGenerator
}

func (s *Service) Do(ctx context.Context) (*proto.PDidiMiniEstimateData, error) {
	products, err := s.generator.GenProducts(ctx)
	if err != nil || len(products) != 1 {
		return nil, err
	}
	render := NewRender(products[0])
	return render.Render(ctx)
}

func InitProductGenerator(ctx context.Context, req *proto.PDidiMiniEstimateReq, commonData *param_handler.CommonData) (*biz_runtime.ProductsGenerator, error) {
	var (
		err         error
		productsGen *biz_runtime.ProductsGenerator
	)

	// 构建入参
	productsGen, err = biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("pDidiMiniEstimate"),
		BuildParams(ctx, req, commonData.AreaInfo, commonData.Passenger),
	)
	if err != nil {
		return nil, err
	}

	productsGen.SetSendReqKafka(true)
	// 会员
	productsGen.SetNeedMember(true)

	return productsGen, nil
}

func NewService(ctx context.Context, req *proto.PDidiMiniEstimateReq) (*Service, error) {
	serviceReq := &param_handler.EstimateRequest{
		ReqFromParams: req,
	}

	handler := param_handler.NewHandler(serviceReq)
	ok := handler.Do(ctx, []param_handler.RequestWrapper{
		CheckParams, // 参数校验
		param_handler.GetUserInfo(ctx, req.Token, req.AccessKeyId), // 用户信息获取 + 校验
		param_handler.GetLocSvrInfo,
	})
	if ok != nil && !errors.Is(ok, BizError.Success) {
		return nil, ok
	}

	productsGenerator, err := InitProductGenerator(ctx, req, &serviceReq.CommonData)
	if err != nil {
		return nil, BizError.ErrSystem
	}

	return &Service{
		generator: productsGenerator,
	}, nil
}
