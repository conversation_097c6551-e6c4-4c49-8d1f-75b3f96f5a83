package data

import (
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

// CallCarEstimateAdapter 一键叫适配器
type CallCarEstimateAdapter struct {
	*biz_runtime.ProductInfoFull
	//apolloParams *plain_text_render.ApolloParams
}

func (a *CallCarEstimateAdapter) GetBonus() float64 {
	fee := a.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}
	amount := fee.GetFeeDetail().GetBonusAmount()
	if amount == nil {
		return 0
	}
	return *amount
}

func (a *CallCarEstimateAdapter) GetFastEstimatePrice() float64 {
	return a.BaseReqData.CommonBizInfo.FastCarPrice
}

// GetMixedDeductPrice 返回企业付 抵扣金额
func (a *CallCarEstimateAdapter) GetMixedDeductPrice() float64 {
	var deductInfo float64

	if !a.IsBusinessPay() {
		return 0.0
	}

	deductInfo = a.GetEstimateFee()

	if payInfo := a.GetPaymentInfo(); payInfo != nil && payInfo.MixedPayDeductInfo != nil && payInfo.MixedPayDeductInfo.DeductFee > 0 {
		deductInfo = payInfo.MixedPayDeductInfo.DeductFee
	}

	return deductInfo
}

func (a *CallCarEstimateAdapter) GetVcard() *PriceApi.EstimateNewFormVCardInfo {
	return a.GetVCard()
}

//func (a *CallCarEstimateAdapter) GetExactEstimateFee() float64 {
//	if a.GetBillInfo() == nil {
//		return 0
//	}
//
//	return a.GetExactEstimateFee()
//}

func (a *CallCarEstimateAdapter) GetCarpoolFailExactEstimateFee() float64 {
	if len(a.GetExtendList()) == 1 {
		return a.GetExtendList()[0].ExactEstimateFee
	}

	return 0
}

func (a *CallCarEstimateAdapter) GetBaseReqData() *models.BaseReqData {
	return a.BaseReqData
}
