package homepage_estimate

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	PassengerUtil "git.xiaojukeji.com/gulfstream/passenger-common/util"
	"git.xiaojukeji.com/nuwa/trace"
)

type FilterProducts struct {
	preferenceProduct []int64
}

type FilterProductsAfterAthena struct {
}

// NewCombinedTravelFilterProducts 组合出行产品过滤器
func NewFilterProducts(ctx context.Context, pid string, callCarBizTag string) *FilterProducts {
	preferenceProduct := getPreferenceProduct(ctx, pid, callCarBizTag)

	return &FilterProducts{
		preferenceProduct: preferenceProduct,
	}
}

func (f *FilterProducts) AfterDdsFilter(ctx context.Context, products []*models.Product) ([]models.ProductCategory, string) {
	var (
		allRemoved = make([]models.ProductCategory, 0)
	)
	for _, p := range products {

		if !util.InArrayInt64(p.ProductCategory, f.preferenceProduct) {
			allRemoved = append(allRemoved, models.ProductCategory(p.ProductCategory))
		}
	}
	return allRemoved, ""
}

func getPreferenceProduct(ctx context.Context, pid string, callCarBizTag string) []int64 {
	// 大盘人群走拼成乐
	if callCarBizTag == consts.CallCarBizTagDefault {
		return []int64{PassengerUtil.ProductCategoryLowPriceCarpool}
	}

	// 加一个限制开关，开关打开时使用athena末次冒泡勾选逻辑
	if apollo.FeatureToggle(ctx, "home_page_xinliu_athena_bubble_car_switch", pid, map[string]string{}) {
		feature, err := ufs.GetFeatureV2(ctx, ufs.DomainPassenger, ufs.KeyAthenaDefaultSelectInfo, map[string]string{"passenger_id": pid})
		if err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "get ufs base.last_bubble_athena_default_select_info feature err = %v", err)
			return []int64{PassengerUtil.ProductCategoryFast}
		}
		log.Trace.Infof(ctx, trace.DLTagUndefined, "ufs feature is = %v", feature)
		var athenaLastSelectedProducts []int64
		err = json.Unmarshal([]byte(feature), &athenaLastSelectedProducts)
		if err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "ufs feature unmarshal err = %v", err)
			return []int64{
				PassengerUtil.ProductCategoryFast,
				PassengerUtil.ProductCategoryFastSpecialRate,
				PassengerUtil.ProductCategorySpecialRate,
			}
		}

		// 默勾数量为空，走兜底快车特惠
		if len(athenaLastSelectedProducts) == 0 {
			return []int64{
				PassengerUtil.ProductCategoryFast,
				PassengerUtil.ProductCategoryFastSpecialRate,
				PassengerUtil.ProductCategorySpecialRate,
			}
		}

		// 默勾数量大于2，不展示一键叫
		if len(athenaLastSelectedProducts) > 2 {
			return []int64{}
		}

		return athenaLastSelectedProducts
	}

	// 新流人群默认走快车和特惠
	return []int64{
		PassengerUtil.ProductCategoryFast,
		PassengerUtil.ProductCategoryFastSpecialRate,
		PassengerUtil.ProductCategorySpecialRate,
	}
}

// NewFilterProductsAfterAthena 调用算法后，如果因为异常情况返回了拼车+独乘的混合，优先出拼车
func NewFilterProductsAfterAthena() *FilterProductsAfterAthena {
	return &FilterProductsAfterAthena{}
}

func (f *FilterProductsAfterAthena) HandlerFilter(ctx context.Context, productMap map[int64]*biz_runtime.ProductInfoFull) (filter []models.ProductCategory) {
	var (
		pinCheArr = make([]models.ProductCategory, 0)
		otherArr  = make([]models.ProductCategory, 0)
	)

	for _, p := range productMap {
		if p.Product.ProductCategory == PassengerUtil.ProductCategoryLowPriceCarpool {
			pinCheArr = append(pinCheArr, models.ProductCategory(p.Product.ProductCategory))
		} else {
			otherArr = append(otherArr, models.ProductCategory(p.Product.ProductCategory))
		}
	}

	if len(pinCheArr) > 0 {
		return otherArr
	} else {
		return []models.ProductCategory{}
	}
}
