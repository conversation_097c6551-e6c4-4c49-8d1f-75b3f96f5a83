package homepage_estimate

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/homepage_estimate/data"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	PassengerUtil "git.xiaojukeji.com/gulfstream/passenger-common/util"
)

var ProductCategoryToNameMap = map[int64]string{
	PassengerUtil.ProductCategoryFast:            "快车",
	PassengerUtil.ProductCategoryFastSpecialRate: "特惠",
	PassengerUtil.ProductCategorySpecialRate:     "特惠",
	PassengerUtil.ProductCategoryLowPriceCarpool: "拼车",
	PassengerUtil.ProductCategoryCarpoolStation:  "拼车",
	PassengerUtil.ProductCategoryUnione:          "出租",
	PassengerUtil.ProductCategoryAplus:           "特快",
	PassengerUtil.ProductCategoryYouxiang:        "优享",
}

type HomePageParam struct {
	ctx                   context.Context
	products              []*biz_runtime.ProductInfoFull
	bizTag                string
	destType              string
	appVersion            string
	accessKeyId           string
	expXinLiuV12GroupName string
	hitDapanJuneExp       bool
	pid                   uint64
	respData              *proto.HomePageCallCarEstimateData
	carpoolPrice          *float64
}

func NewHomePageParam(ctx context.Context, products []*biz_runtime.ProductInfoFull, bizTag, destType, appVersion, accessKeyId, expXinLiuV12GroupName string, pid uint64, hitDapanJuneExp bool) *HomePageParam {
	return &HomePageParam{
		ctx:                   ctx,
		bizTag:                bizTag,
		destType:              destType,
		appVersion:            appVersion,
		accessKeyId:           accessKeyId,
		products:              products,
		hitDapanJuneExp:       hitDapanJuneExp,
		expXinLiuV12GroupName: expXinLiuV12GroupName,
		pid:                   pid,
	}
}

func (param *HomePageParam) assembleResponse() *proto.HomePageCallCarEstimateData {
	ret := &proto.HomePageCallCarEstimateData{}
	param.respData = ret

	if param.bizTag == consts.CallCarBizTagXinliu {
		param.assembleXinliuResponse()
	} else if param.bizTag == consts.CallCarBizTagDefault && param.hitDapanJuneExp {
		param.assembleDefaultMultiProductResponse()
	} else {
		param.assembleDefaultResponse()
	}

	param.assembleOmegaInfo()
	return param.respData
}

func (param *HomePageParam) assembleMultiProductDestinationInfo() {
	if param == nil || len(param.products) == 0 {
		return
	}
	if param.respData == nil {
		param.respData = &proto.HomePageCallCarEstimateData{}
	}

	param.respData.DestinationInfo = &proto.DestinationInfo{
		ToLng:     param.products[0].BaseReqData.AreaInfo.ToLng,
		ToLat:     param.products[0].BaseReqData.AreaInfo.ToLat,
		ToAddress: param.products[0].BaseReqData.AreaInfo.ToAddress,
		ToName:    param.products[0].BaseReqData.AreaInfo.ToName,
		ToPoiId:   param.products[0].BaseReqData.AreaInfo.ToPoiID,
		ToCity:    param.products[0].BaseReqData.AreaInfo.ToArea,
		ToPoiType: param.products[0].BaseReqData.AreaInfo.ToPoiType,
	}

	if len(param.products) == 1 {
		productCategoryStr := strconv.FormatInt(param.products[0].Product.ProductCategory, 10)
		if conf := dcmp.GetJSONMap(param.ctx, "home_page-call_car_product_category_display", "name"); len(conf) > 0 {
			carName := conf[productCategoryStr].String()
			param.respData.DestinationInfo.Text = carName + "去"
		} else {
			param.respData.DestinationInfo.Text = "去"
		}
		//carName = ProductCategoryToNameMap[param.products[0].Product.ProductCategory]
	} else {
		param.respData.DestinationInfo.Text = "去"
	}
}
func (param *HomePageParam) assembleDestinationInfo() {
	if param == nil || len(param.products) == 0 {
		return
	}
	if param.respData == nil {
		param.respData = &proto.HomePageCallCarEstimateData{}
	}

	var carName string
	if len(param.products) == 1 {
		carName = ProductCategoryToNameMap[param.products[0].Product.ProductCategory]
	} else {
		for i, product := range param.products {
			if i != 0 {
				carName += "、"
			}
			carName += ProductCategoryToNameMap[product.Product.ProductCategory]
		}
	}

	param.respData.DestinationInfo = &proto.DestinationInfo{
		ToLng:     param.products[0].BaseReqData.AreaInfo.ToLng,
		ToLat:     param.products[0].BaseReqData.AreaInfo.ToLat,
		ToAddress: param.products[0].BaseReqData.AreaInfo.ToAddress,
		ToName:    param.products[0].BaseReqData.AreaInfo.ToName,
		ToPoiId:   param.products[0].BaseReqData.AreaInfo.ToPoiID,
		ToCity:    param.products[0].BaseReqData.AreaInfo.ToArea,
		ToPoiType: param.products[0].BaseReqData.AreaInfo.ToPoiType,
		Text:      dcmp.GetJSONContentWithPath(param.ctx, dcmp.KeyHomePageCallCarNewXinliu, map[string]string{"car_name": carName, "to_name": param.products[0].BaseReqData.AreaInfo.ToName}, "destination_info_text_default"),
		CityName:  param.products[0].BaseReqData.AreaInfo.ToCityName,
	}

	if param.expXinLiuV12GroupName == consts.ExpXinLiuCallCarShowCarType || param.expXinLiuV12GroupName == consts.ExpXinLiuCallCarNotShowCarType {
		param.respData.DestinationInfo.Text = dcmp.GetJSONContentWithPath(param.ctx, dcmp.KeyHomePageCallCarNewXinliu, map[string]string{}, "destination_info_text_xinliu_v12")
		param.respData.DestinationInfo.ToName = dcmp.GetJSONContentWithPath(param.ctx, dcmp.KeyHomePageCallCarNewXinliu, map[string]string{"to_name": param.products[0].BaseReqData.AreaInfo.ToName}, "destination_info_toname_xinliu_v12")
	} else {
		// 新版
		if isRenderV2(param.ctx, param.accessKeyId, param.appVersion) {
			param.respData.DestinationInfo.Text = dcmp.GetJSONContentWithPath(param.ctx, dcmp.KeyHomePageCallCarNewXinliu, map[string]string{"car_name": carName}, "destination_info_text_default_v2")

		}
	}

}

func (param *HomePageParam) assembleMultiRequireProduct() {
	if param == nil || len(param.products) == 0 {
		return
	}
	var multiProduct []*proto.ProductInfo
	if param.respData == nil {
		param.respData = &proto.HomePageCallCarEstimateData{}
	}
	for _, product := range param.products {
		productInfo := &proto.ProductInfo{
			ProductId:       product.Product.ProductID,
			BusinessId:      product.Product.BusinessID,
			ComboType:       product.Product.ComboType,
			RequireLevel:    product.Product.RequireLevelInt,
			LevelType:       product.Product.LevelType,
			RouteType:       product.Product.RouteType,
			IsSpecialPrice:  product.Product.IsSpecialPrice,
			CountPriceType:  product.GetCountPriceType(),
			PayType:         strconv.Itoa(int(product.GetDefaultPayType())),
			EstimateId:      product.GetEstimateID(),
			ProductCategory: product.GetProductCategory(),
		}
		if product.Product.BizInfo != nil {
			productInfo.ComboId = product.Product.BizInfo.ComboID
		}
		if product.Product.ProductCategory == PassengerUtil.ProductCategoryCarpoolStation {
			carpoolSeatNum := int64(1)
			productInfo.CarpoolSeatNum = &carpoolSeatNum
		}
		multiProduct = append(multiProduct, productInfo)
	}
	param.respData.MultiRequireProduct = multiProduct
}

func (param *HomePageParam) assembleLeftTagInfo() {
	if param == nil || len(param.products) == 0 {
		return
	}
	if param.respData == nil {
		param.respData = &proto.HomePageCallCarEstimateData{}
	}

	if param.expXinLiuV12GroupName == consts.ExpXinLiuCallCarNotShowCarType || param.expXinLiuV12GroupName == consts.ExpXinLiuCallCarShowCarType {
		param.assembleLeftTagInfoXinLiuV12()
		return
	}

	param.assembleLeftTagInfoDefault()
}

func (param *HomePageParam) assembleLeftTagInfoDefault() {
	// 终点为末次冒泡型
	if param.destType == consts.CallCarDestTypeLastBubble {
		leftTagInfo := &proto.LeftTagInfo{
			Type: 1,
			Icon: dcmp.GetDcmpContent(param.ctx, "home_page-left_tag_icon_last_bubble", nil),
			Text: "看过的行程",
		}

		param.respData.LeftTagInfo = leftTagInfo
		return
	}

	// 车型为拼成乐
	if len(param.products) == 1 && param.products[0].Product.ProductCategory == PassengerUtil.ProductCategoryLowPriceCarpool {
		leftTagInfo := &proto.LeftTagInfo{
			Type: 2,
			Icon: dcmp.GetDcmpContent(param.ctx, "home_page-left_tag_icon_pinche", nil),
		}

		param.respData.LeftTagInfo = leftTagInfo
		return
	}
}

func (param *HomePageParam) assembleMultiProductLeftTagInfo() {
	if param == nil || len(param.products) == 0 {
		return
	}
	if param.respData == nil {
		param.respData = &proto.HomePageCallCarEstimateData{}
	}

	if len(param.products) == 1 {
		productCategoryStr := strconv.FormatInt(param.products[0].Product.ProductCategory, 10)
		if conf := dcmp.GetJSONMap(param.ctx, "home_page-call_car_product_category_display", "left_tag_icon"); len(conf) > 0 {
			leftTagInfo := &proto.LeftTagInfo{
				Type: 2,
				Icon: conf[productCategoryStr].String(),
			}
			param.respData.LeftTagInfo = leftTagInfo
		}

		return
	}

	// 多车型、可点击
	if len(param.products) > 1 {
		text := "同时呼叫:"
		var productCategoryNames []string
		for _, product := range param.products {
			productCategoryStr := strconv.FormatInt(product.Product.ProductCategory, 10)
			if conf := dcmp.GetJSONMap(param.ctx, "home_page-call_car_product_category_display", "name"); len(conf) > 0 {
				carTitle := conf[productCategoryStr].String()
				productCategoryNames = append(productCategoryNames, carTitle)
			}
		}
		if len(productCategoryNames) >= 4 {
			text += fmt.Sprintf("%s等%d个车型", productCategoryNames[0], len(productCategoryNames))
		} else {
			text += strings.Join(productCategoryNames, "、")
		}
		arrowPic := dcmp.GetDcmpContent(param.ctx, "home_page-left_tag_arrow_pic", nil)
		leftTagInfo := &proto.LeftTagInfo{
			Type:     3,
			ArrowPic: &arrowPic,
			Text:     text,
		}

		param.respData.LeftTagInfo = leftTagInfo
		return
	}
}

func (param *HomePageParam) assembleNewFormEstimateData() {
	if param == nil || len(param.products) < 2 {
		return
	}
	if param.respData == nil {
		param.respData = &proto.HomePageCallCarEstimateData{}
	}

	renderService := data.NewRender(param.ctx)
	renderService.PreBuilder(param.ctx, param.products)

	var estimateDataList []*proto.CallCarEstimateData
	for _, pFull := range param.products {
		estimateData := renderService.RenderByProduct(param.ctx, pFull)
		if estimateData == nil {
			continue
		}
		isSelected := int32(consts.Checked)
		estimateData.IsSelected = &isSelected
		if estimateData.ProductCategory == PassengerUtil.ProductCategoryCarpoolStation && len(estimateData.CarpoolSeatList) > 0 {
			leftDownIconText := "限1人"
			estimateData.ExtraEstimateData = &proto.CallCarExtraEstimateData{
				LeftDownIconText: &leftDownIconText,
			}
		}
		estimateDataList = append(estimateDataList, estimateData)
	}

	renderService.ExtendDataRender(param.ctx, param.respData)

	param.respData.EstimateDataList = estimateDataList
}

func (param *HomePageParam) checkPayInfoAndAssembleUserPayInfo() error {
	// 一键盘叫不支持多支付方式
	if param == nil || len(param.products) == 0 {
		param.respData = nil
		return nil
	}

	var payType, businessConstSet string
	for i, product := range param.products {
		if i == 0 {
			payType = strconv.Itoa(int(product.GetDefaultPayType()))
			businessConstSet = strconv.Itoa(int(product.GetBusinessConstSetByPayType(product.GetDefaultPayType())))
			continue
		}
		if payType != strconv.Itoa(int(product.GetDefaultPayType())) ||
			businessConstSet != strconv.Itoa(int(product.GetBusinessConstSetByPayType(product.GetDefaultPayType()))) {
			param.respData = nil
			return errors.New("pay_info not fit")
		}
	}

	param.respData.UserPayInfo = &proto.PayInfo{
		PayType:          payType,
		BusinessConstSet: businessConstSet,
	}
	return nil
}
