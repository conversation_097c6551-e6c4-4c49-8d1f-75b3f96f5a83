package homepage_estimate

import (
	"context"
	"sort"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	PassengerUtil "git.xiaojukeji.com/gulfstream/passenger-common/util"
)

func (param *HomePageParam) assembleDefaultResponse() {
	if len(param.products) == 0 {
		return
	}

	if param.checkPayInfoAndAssembleUserPayInfo() != nil {
		return
	}

	// 企业级不出拼车
	payType := int(param.products[0].GetDefaultPayType())
	productCategory := int(param.products[0].Product.ProductCategory)
	if (payType == consts.BusinessPaymentType || payType == consts.BusinessPayByTeam) && productCategory == PassengerUtil.ProductCategoryLowPriceCarpool {
		param.respData = nil
		return
	}

	param.assembleDestinationInfo()
	param.assembleButtonInfo()
	param.assembleFeeDescList()
	param.assembleMultiRequireProduct()
	param.assembleLeftTagInfo()
	// fee_amount/fee_msg字段
	param.tryGetFeeInfo()
	// side_info/car_title字段
	param.tryGetTextInfo()
}

func (param *HomePageParam) assembleButtonInfo() {
	if param.respData == nil {
		param.respData = &proto.HomePageCallCarEstimateData{}
	}

	prefix := ""

	if util.InArrayStr(param.accessKeyId, []string{"1", "2"}) {
		prefix = "na_"
	}

	// 新版小程序&na端，一键叫需要跳转到拼车tab页，替换跳转链接
	allow, params := apollo.GetParameters("one_call_carpool_new_link", strconv.FormatUint(param.pid, 10), map[string]string{
		"key": strconv.FormatUint(param.pid, 10), "app_version": param.appVersion, "access_key_id": param.accessKeyId})

	if allow && len(params) > 0 && params["prefix"] != "" {
		prefix = params["prefix"]
	}

	borderColor := dcmp.GetJSONContentWithPath(param.ctx, dcmp.KeyHomePageCallCarNewDapan, nil, "button_border_coloer")
	param.respData.CallCarButtonInfo = &proto.CallCarButtonInfo{
		BgStartColor: dcmp.GetJSONContentWithPath(param.ctx, dcmp.KeyHomePageCallCarNewDapan, nil, "button_bg_color"),
		BgEndColor:   dcmp.GetJSONContentWithPath(param.ctx, dcmp.KeyHomePageCallCarNewDapan, nil, "button_bg_color"),
		BorderColor:  &borderColor,
		Text:         dcmp.GetJSONContentWithPath(param.ctx, dcmp.KeyHomePageCallCarNewDapan, nil, "button_text"),
		TextColor:    dcmp.GetJSONContentWithPath(param.ctx, dcmp.KeyHomePageCallCarNewDapan, nil, "button_text_color"),
		CallCarLinkInfo: &proto.CallCarLinkInfo{
			LinkType: dcmp.GetJSONContentWithPath(param.ctx, dcmp.KeyHomePageCallCarNewDapan, nil, prefix+"link_type"),
			Link:     dcmp.GetJSONContentWithPath(param.ctx, dcmp.KeyHomePageCallCarNewDapan, nil, prefix+"button_link"),
		},
	}
}

func (param *HomePageParam) assembleFeeDescList() {
	var (
		feeDescList = make([]*proto.FeeDesc, 0)
	)
	if param == nil || len(param.products) == 0 || param.products[0].Product.ProductCategory != PassengerUtil.ProductCategoryLowPriceCarpool {
		return
	}

	if param.respData == nil {
		param.respData = &proto.HomePageCallCarEstimateData{}
	}

	saveMoney := util.FormatPrice(param.products[0].GetPreTotalFee()-param.buildCarpoolPriceOnce(param.products[0]), 1)

	feeDesc := &proto.FeeDesc{
		BorderColor: dcmp.GetJSONContentWithPath(param.ctx, dcmp.KeyHomePageCallCarNewDapan, nil, "fee_desc_border_color"),
		Content:     dcmp.GetJSONContentWithPath(param.ctx, dcmp.KeyHomePageCallCarNewDapan, map[string]string{"save_money": saveMoney}, "fee_desc_content"),
		Icon:        "",
	}
	feeDescList = append(feeDescList, feeDesc)
	param.respData.FeeDescList = feeDescList
}

func (param *HomePageParam) tryGetFeeInfo() []float64 {
	if param == nil || len(param.products) != 1 || param.products[0].Product.ProductCategory != PassengerUtil.ProductCategoryLowPriceCarpool {
		return []float64{}
	}

	pathSuffix := ""
	if isRenderV2(param.ctx, param.accessKeyId, param.appVersion) {
		pathSuffix = "_v2"
	}

	if param.respData == nil {
		param.respData = &proto.HomePageCallCarEstimateData{}
	}

	fee := util.FormatPrice(param.buildCarpoolPriceOnce(param.products[0]), 1)
	param.respData.FeeMsg = dcmp.GetJSONContentWithPath(param.ctx, dcmp.KeyHomePageCallCarNewDapan, map[string]string{"fee": fee}, "fee_msg_text"+pathSuffix)
	param.respData.FeeAmount = util.FormatPrice(param.buildCarpoolPriceOnce(param.products[0]), 1)

	return []float64{param.buildCarpoolPriceOnce(param.products[0])}
}

func (param *HomePageParam) tryGetTextInfo() {
	if param == nil || len(param.products) == 0 {
		return
	}
	if param.respData == nil {
		param.respData = &proto.HomePageCallCarEstimateData{}
	}

	for _, product := range param.products {
		param.respData.CarTitleList = append(param.respData.CarTitleList, ProductCategoryToNameMap[product.Product.ProductCategory])
	}
}

func (param *HomePageParam) tryGetMultiProductTextInfo() {
	if param == nil || len(param.products) == 0 {
		return
	}
	if param.respData == nil {
		param.respData = &proto.HomePageCallCarEstimateData{}
	}

	for _, product := range param.products {
		productCategoryStr := strconv.FormatInt(product.Product.ProductCategory, 10)
		if conf := dcmp.GetJSONMap(param.ctx, "home_page-call_car_product_category_display", "name"); len(conf) > 0 {
			carTitle := conf[productCategoryStr].String()
			param.respData.CarTitleList = append(param.respData.CarTitleList, carTitle)
		}
	}
}

func (param *HomePageParam) buildCarpoolPriceOnce(carpoolProduct *biz_runtime.ProductInfoFull) float64 {
	if param.carpoolPrice != nil {
		return *param.carpoolPrice
	}

	adapter := ViewAdapter{prod: carpoolProduct}
	prov := adapter.MultiPrice()

	if len(prov) == 0 {
		return 0
	}

	// 拼到人数多的在前面
	sort.Slice(prov, func(i, j int) bool {
		return prov[i].GetExpectPoolNum() > prov[j].GetExpectPoolNum()
	})

	price := prov[0].GetEstimateFeeAmount()
	param.carpoolPrice = &price
	return *param.carpoolPrice
}

func (param *HomePageParam) assembleOmegaInfo() {
	if param == nil || param.respData == nil {
		return
	}

	param.respData.DesType = param.destType
	param.respData.UserTag = param.bizTag
}

func isRenderV2(ctx context.Context, accessKeyId, appVersion string) bool {
	return apollo.FeatureToggle(ctx, "one_call_car_rander_new_switch", "", map[string]string{"app_version": appVersion, "access_key_id": accessKeyId})
}

func (param *HomePageParam) assembleDefaultMultiProductResponse() {
	if param.checkPayInfoAndAssembleUserPayInfo() != nil {
		return
	}
	param.assembleMultiProductDestinationInfo()
	if hasPinChe(param.products) {

		// 企业级不出拼车
		payType := int(param.products[0].GetDefaultPayType())
		productCategory := int(param.products[0].Product.ProductCategory)
		if (payType == consts.BusinessPaymentType || payType == consts.BusinessPayByTeam) && productCategory == PassengerUtil.ProductCategoryLowPriceCarpool {
			param.respData = nil
			return
		}

		param.assembleButtonInfo()
		// fee_amount/fee_msg字段
		param.tryGetFeeInfo()
		// side_info/car_title字段
		param.tryGetMultiProductTextInfo()
	} else {
		// 品类中没有拼车，复用新流的按钮
		param.assembleXinLiuButtonInfo()
		// fee_amount/fee_msg字段
		param.tryGetMultiProductFeeInfo()
		// side_info/car_title字段
		param.tryGetMultiProductTextInfo()
	}
	param.assembleMultiProductFeeDescList()
	param.assembleMultiRequireProduct()
	param.assembleMultiProductLeftTagInfo()
	param.assembleNewFormEstimateData()
}

func (param *HomePageParam) assembleMultiProductFeeDescList() {
	var (
		borderColor, content, icon, dcmpKey, amount string
		maxCouponAmountF                            float64
		feeDescList                                 = make([]*proto.FeeDesc, 0)
	)
	if param == nil || len(param.products) == 0 {
		return
	}
	if param.respData == nil {
		param.respData = &proto.HomePageCallCarEstimateData{}
	}

	// 企业级渲染
	canRenderBusinessPayTag := true
	for _, product := range param.products {
		payType := util.ToInt(product.GetDefaultPayType())
		if payType != consts.BusinessPaymentType && payType != consts.BusinessPayByTeam {
			canRenderBusinessPayTag = false
		}
	}

	if canRenderBusinessPayTag {
		feeDesc := &proto.FeeDesc{
			BorderColor: dcmp.GetJSONContentWithPath(param.ctx, dcmp.KeyHomePageCallCarNewXinliu, nil, "business_border"),
			Content:     "",
			Icon:        dcmp.GetJSONContentWithPath(param.ctx, dcmp.KeyHomePageCallCarNewXinliu, nil, "business_icon"),
		}
		feeDescList = append(feeDescList, feeDesc)
		param.respData.FeeDescList = feeDescList
		return
	}

	for _, product := range param.products {
		if product.GetCouponAmount() == "" {
			continue
		}
		couponAmount, err := strconv.ParseFloat(product.GetCouponAmount(), 64)
		if err != nil {
			continue
		}
		if couponAmount > maxCouponAmountF {
			maxCouponAmountF = couponAmount
			dcmpKey = "max_coupon_"
			amount = product.GetCouponAmount()
		}
	}
	if dcmpKey == "" || amount == "" {
		return
	}
	borderColor = dcmp.GetJSONContentWithPath(param.ctx, dcmp.KeyHomePageCallCarNewDapan, map[string]string{"amount": amount}, dcmpKey+"border")
	content = dcmp.GetJSONContentWithPath(param.ctx, dcmp.KeyHomePageCallCarNewDapan, map[string]string{"amount": amount}, dcmpKey+"content")
	icon = dcmp.GetJSONContentWithPath(param.ctx, dcmp.KeyHomePageCallCarNewDapan, map[string]string{"amount": amount}, dcmpKey+"icon")
	feeDesc := &proto.FeeDesc{
		BorderColor: borderColor,
		Content:     content,
		Icon:        icon,
	}
	feeDescList = append(feeDescList, feeDesc)
	param.respData.FeeDescList = feeDescList
}

func (param *HomePageParam) tryGetMultiProductFeeInfo() []float64 {
	feeAmountList := param.getFeeAmountList()
	if len(feeAmountList) == 0 {
		return nil
	}
	if param.respData == nil {
		param.respData = &proto.HomePageCallCarEstimateData{}
	}

	sort.Float64s(feeAmountList)
	param.respData.FeeMsg = param.buildFeeMsg(feeAmountList, param.products, 0)
	param.respData.FeeAmount = buildFeeAmount(feeAmountList, 0)

	return feeAmountList
}

func (param *HomePageParam) getFeeAmountList() []float64 {
	var feeAmountList []float64
	if param == nil || len(param.products) == 0 {
		return nil
	}
	for _, product := range param.products {
		// 企业付
		payType := util.ToInt(product.GetDefaultPayType())
		if payType == consts.BusinessPaymentType || payType == consts.BusinessPayByTeam {
			feeAmount := product.GetEstimateFee() - product.GetBusinessPaymentFee()
			feeAmountList = append(feeAmountList, feeAmount)
		} else {
			feeAmount := product.GetEstimateFee()
			feeAmountList = append(feeAmountList, feeAmount)
		}
	}

	return feeAmountList
}
