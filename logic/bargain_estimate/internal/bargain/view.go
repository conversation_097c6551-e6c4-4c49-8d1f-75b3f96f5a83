package bargain

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	BargainEstimateRender "git.xiaojukeji.com/gulfstream/mamba/render/private/bargain/bargain_estimate"
	BargainSideRender "git.xiaojukeji.com/gulfstream/mamba/render/private/bargain/bargain_side"
)

func AssembleProduct(ctx context.Context, prov *ViewAdapter) (item *proto.BargainEstimateItem) {
	item = new(proto.BargainEstimateItem)
	//渲染预估主要信息，包括基本信息和价格信息
	item.EstimateId = BargainEstimateRender.GetEstimateId(prov)
	item.ProductCategory = BargainEstimateRender.GetProductCategory(prov)
	item.ProductId = BargainEstimateRender.GetProductId(prov)
	item.RequireLevel = BargainEstimateRender.GetRequireLevel(ctx, prov)
	item.ComboType = BargainEstimateRender.GetComboType(prov)
	item.BusinessId = BargainEstimateRender.GetBusinessId(prov)
	item.FeeAmount = BargainEstimateRender.GetFeeAmount(prov)
	item.CouponAmount = BargainEstimateRender.GetCouponAmount(ctx, prov)
	item.IntroMsg = BargainEstimateRender.GetIntroMsg(ctx, prov)
	item.EstimateText = BargainEstimateRender.GetEstimateText(ctx, prov)
	item.SpecialPriceTextV2 = BargainEstimateRender.GetSpecialPriceTextV2(ctx, prov)
	item.FeeMargin = BargainEstimateRender.GetFeeMargin(ctx, prov)
	item.AnycarFeeMargin = BargainEstimateRender.GetAnyCarFeeMargin(ctx, prov)
	item.RecommendData = BargainEstimateRender.GetRecommendData(ctx, prov)
	//渲染沟通感知信息
	item.CommentTags = BargainSideRender.GetCommentTag(ctx)
	item.TutorialInfo = BargainSideRender.GetTutorialInfo(ctx, prov)
	item.TopCommunicateData = BargainSideRender.GetTopCommunicateData(ctx, prov)
	item.RetainFrameData = BargainSideRender.GetRetainFrameData(ctx, prov)
	item.AnycarRecommendData = BargainEstimateRender.GetAnycarRecommendData(prov)
	return item
}
