package shuttle_bus_estimate

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/page_type"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/shuttle_bus_estimate/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_detail_info"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
	"sort"
)

type LandingPageLogic struct {
	CommonLogic *ShuttleBusCommonLogic
}

func LandingPageNewLogic(ctx context.Context, request *proto.ShuttleBusPageEstimateReq) (logic *LandingPageLogic, err error) {
	logic = new(LandingPageLogic)
	var (
		commonParams models.CommonParam
	)
	// 参数校验
	err = logic.CheckParams(ctx, request)
	if err != nil {
		return nil, err
	}

	commonParams = logic.InitCommonParams(ctx, request)
	if err != nil {
		return nil, err
	}
	logic.CommonLogic, err = NewLogic(ctx, commonParams)
	if err != nil {
		return logic, err
	}

	return logic, nil
}

func (logic *LandingPageLogic) Do(ctx context.Context, rsp *proto.ShuttleBusPageEstimateRsp) (err error) {
	product, err := logic.CommonLogic.GetProduct(ctx)
	if err != nil || product == nil {
		return err
	}
	if len(product.GetExtendList()) == 0 {
		log.Trace.Warnf(ctx, "shuttle_bus_estimate", "get price err")
		return errors.New("get price err")
	}
	billInfos := product.GetExtendList()
	if len(billInfos) == 0 {
		log.Trace.Warnf(ctx, "shuttle_bus_estimate", "get price err")
		return errors.New("get price err")
	}
	// 获取价格
	unitPrice, totalPrice, err := GetPriceInfo(billInfos, logic.CommonLogic.request.CarpoolSeatNum)
	if unitPrice <= 0 || totalPrice <= 0 {
		log.Trace.Warnf(ctx, "shuttle_bus_estimate", "price <= 0")
		return errors.New("price < 0")
	}
	if err != nil {
		log.Trace.Warnf(ctx, "shuttle_bus_estimate", "get price err %+v", err)
		return err
	}
	// 最大座位数
	seatMaxNum := getMaxCarpoolSeatNum(billInfos[0])
	// DCMP
	template := dcmp.GetDcmpPlainContent(ctx, models.DCMPShuttleBusPageInfo)
	seatData := &proto.SeatData{
		LeftText:     gjson.Get(template, "seat_data.left_text").String(),
		SeatMaxNum:   int32(seatMaxNum),
		MaxRemindMsg: dcmp.GetJSONContentWithPath(ctx, models.DCMPShuttleBusPageInfo, map[string]string{"num": cast.ToString(seatMaxNum)}, "seat_data.max_remind_msg"),
		DefaultNum:   logic.CommonLogic.request.CarpoolSeatNum,
		MinRemindMsg: gjson.Get(template, "seat_data.min_remind_msg").String(),
	}

	slogan := gjson.Get(template, "distance_type_1").String()
	if logic.CommonLogic.request.SourceType == 1 && logic.CommonLogic.request.DistanceType == 2 {
		slogan = gjson.Get(template, "distance_type_2").String()
	}
	routeId := int32(logic.CommonLogic.RouteId)
	// 构造返回
	ret := &proto.ShuttleBusPageData{
		CommunicationImage: gjson.Get(template, "communication_image").String(),
		StartStationData: &proto.ShuttleBusStationData{
			StationId: int32(logic.CommonLogic.startStationInfo.StationId),
			Name:      logic.CommonLogic.startStationInfo.Name,
			Lat:       logic.CommonLogic.startStationInfo.Lat,
			Lng:       logic.CommonLogic.startStationInfo.Lng,
			Address:   logic.CommonLogic.startStationInfo.Address,
		},
		EndStationData: &proto.ShuttleBusStationData{
			StationId: int32(logic.CommonLogic.endStationInfo.StationId),
			Name:      logic.CommonLogic.endStationInfo.Name,
			Lat:       logic.CommonLogic.endStationInfo.Lat,
			Lng:       logic.CommonLogic.endStationInfo.Lng,
			Address:   logic.CommonLogic.endStationInfo.Address,
		},
		StartAndEndIcon: gjson.Get(template, "start_and_end_icon").String(),
		WalkIcon:        gjson.Get(template, "walk_icon").String(),
		Slogan:          slogan,
		ButtonInfo: &proto.ShuttleBusButton{
			Text:     gjson.Get(template, "button_info.text").String(),
			SubTitle: gjson.Get(template, "button_info.sub_text").String(),
		},
		SeatData:        seatData,
		RegionId:        util.ToInt32(logic.CommonLogic.RegionInfo.RegionId),
		CarpoolType:     util.ToInt32(product.Product.CarpoolType),
		RequireLevel:    util.ToInt32(product.Product.RequireLevel),
		BusinessId:      util.ToInt32(product.Product.BusinessID),
		ProductId:       util.ToInt32(product.Product.ProductID),
		ComboType:       util.ToInt32(product.Product.ComboType),
		ProductCategory: util.ToInt32(product.Product.ProductCategory),
		EstimateId:      product.Product.EstimateID,
		RouteType:       util.ToInt32(product.Product.RouteType),
		RouteIdList:     billInfos[0].BillInfo.RouteIdList,
		RouteId:         &routeId,
		FeeDetailUrl:    fee_detail_info.GetDetailUrl(ctx),
		UnitPrice:       dcmp.GetJSONContentWithPath(ctx, models.DCMPShuttleBusPageInfo, map[string]string{"amount": cast.ToString(unitPrice)}, "unit_price"),
		TotalPrice:      dcmp.GetJSONContentWithPath(ctx, models.DCMPShuttleBusPageInfo, map[string]string{"amount": cast.ToString(totalPrice)}, "total_price"),
	}
	// 是否有可切换起点站点
	if logic.CommonLogic != nil && logic.CommonLogic.SwitchPoint && len(logic.CommonLogic.SwitchPointsInfo) > 1 {
		businessPoiInfo := &proto.BusinessPoiInfo{
			RecPoints: make([]*proto.RecPoints, 0),
		}

		recPoints := make([]*proto.RecPoints, 0)
		extendInfo := &proto.ExtendInfo{
			StartBottomCardInfo: &proto.StartBottomCardInfo{
				CardTop: &proto.ContentInfo{
					Content:      gjson.Get(template, "extend_info.start_bottom_card_info.card_top.content").String(),
					ContentColor: gjson.Get(template, "extend_info.start_bottom_card_info.card_top.content_color").String(),
				},
				CardBottom: &proto.ContentInfo{
					Content:      gjson.Get(template, "extend_info.start_bottom_card_info.card_bottom.content").String(),
					ContentColor: gjson.Get(template, "extend_info.start_bottom_card_info.card_bottom.content_color").String(),
				},
			},
		}
		for _, item := range logic.CommonLogic.SwitchPointsInfo {
			point := &proto.RecPoints{
				BaseInfo: new(proto.BaseInfo),
			}
			if item.StationId == logic.CommonLogic.startStationInfo.StationId {
				point.BaseInfo.IsRecommendAbsorb = 1
			}
			point.BaseInfo.Lat = item.Lat
			point.BaseInfo.Lng = item.Lng
			point.BaseInfo.CityId = int32(item.CityId)
			point.BaseInfo.Displayname = item.Name
			point.BaseInfo.PoiId = models.ShuttleBusPoiIdPrefix + fmt.Sprintf("_%08d", item.StationId)
			point.BaseInfo.StationId = int32(item.StationId)
			point.ExtendInfo = extendInfo
			recPoints = append(recPoints, point)
		}
		businessPoiInfo.RecPoints = recPoints
		ret.BusinessPoiInfo = businessPoiInfo
		switchPointIcon := gjson.Get(template, "switch_point_icon").String()
		ret.SwitchPointIcon = &switchPointIcon
	}

	rsp.Data = ret
	return nil
}

func getMaxCarpoolSeatNum(billInfo *PriceApi.EstimateNewFormExtend) int {
	maxSeatNum := models.DefaultMaxSeatNum
	if billInfo.BillInfo != nil && billInfo.BillInfo.HistoryExtraMap != nil {
		extraMap, ok := billInfo.BillInfo.HistoryExtraMap.(map[string]interface{})
		if !ok || len(extraMap) == 0 {
			return maxSeatNum
		}
		seatConfig, ok := extraMap["carpool_seat_config"].([]interface{})
		if !ok {
			return maxSeatNum
		}
		seatList := make([]int, 0)
		for _, v := range seatConfig {
			num, ok := v.(json.Number)
			if !ok {
				return maxSeatNum
			}
			seatList = append(seatList, util.ToInt(num))
		}
		sort.Ints(seatList)
		if len(seatList) > 0 {
			maxSeatNum = seatList[len(seatList)-1]
		}
	}

	return maxSeatNum
}

func (logic *LandingPageLogic) CheckParams(ctx context.Context, request *proto.ShuttleBusPageEstimateReq) error {
	if request == nil {
		return BizError.ErrInvalidArgument
	}
	if request.GetToken() == "" {
		return BizError.ErrNotLogin
	}
	if request.CityId == 0 || request.GetFromLat() == 0 || request.GetFromLng() == 0 {
		return BizError.ErrInvalidArgument
	}
	// 导流进落地页
	if request.SourceType == 1 {
		if request.GetRegionId() == 0 || request.GetStartStationId() == 0 || request.GetEndStationId() == 0 {
			return BizError.ErrInvalidArgument
		}
	}
	// 扫码进落地页
	if request.SourceType == 2 {
		if request.GetRegionId() == 0 || request.GetEndStationId() == 0 {
			return BizError.ErrInvalidArgument
		}
	}
	return nil
}

func (logic *LandingPageLogic) InitCommonParams(ctx context.Context, request *proto.ShuttleBusPageEstimateReq) models.CommonParam {
	var commonParams models.CommonParam
	commonParams = models.CommonParam{
		Token:         request.Token,
		AppVersion:    request.AppVersion,
		Channel:       request.Channel,
		UserType:      request.UserType,
		MapType:       request.MapType,
		AccessKeyID:   request.AccessKeyId,
		Lang:          request.Lang,
		CityId:        request.CityId,
		FromLat:       request.FromLat,
		FromLng:       request.FromLng,
		SourceType:    request.SourceType,
		RegionId:      request.RegionId,
		EndStationId:  request.EndStationId,
		IsForGuideBar: false,
	}
	if request.PageType != nil {
		commonParams.PageType = *request.PageType
	} else {
		commonParams.PageType = page_type.PageTypePageTypeShuttleBus
	}
	if request.StartStationId != nil {
		commonParams.StartStationId = *request.StartStationId
	}
	if request.CarpoolSeatNum != nil && *request.CarpoolSeatNum > 1 && *request.CarpoolSeatNum < 4 {
		commonParams.CarpoolSeatNum = *request.CarpoolSeatNum
	} else {
		commonParams.CarpoolSeatNum = models.DefaultSeatNum
	}
	if request.RouteId != nil {
		commonParams.RouteId = *request.RouteId
	}
	if request.DistanceType != nil {
		commonParams.DistanceType = *request.DistanceType
	}

	return commonParams
}
