package shuttle_bus_estimate

import (
	"context"
	"errors"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/shuttle_bus_estimate/models"
)

type GuideBarLogic struct {
	CommonLogic *ShuttleBusCommonLogic
}

func GuideBarNewLogic(ctx context.Context, request *proto.ShuttleBusGuideBarEstimateReq) (logic *GuideBarLogic, err error) {
	logic = new(GuideBarLogic)
	var (
		commonParams models.CommonParam
	)
	// 参数校验
	err = logic.CheckParams(ctx, request)
	if err != nil {
		return nil, err
	}
	commonParams = logic.InitCommonParams(ctx, request)
	logic.CommonLogic, err = NewLogic(ctx, commonParams)
	if err != nil {
		log.Trace.Infof(ctx, "guide bar", "err is +%v", err)
		return nil, err
	}

	return logic, nil
}

// 构造返回
func (logic *GuideBarLogic) Do(ctx context.Context, rsp *proto.ShuttleBusGuideBarEstimateRsp) (err error) {
	product, err := logic.CommonLogic.GetProduct(ctx)
	if err != nil || product == nil {
		return err
	}
	if len(product.GetExtendList()) == 0 {
		log.Trace.Warnf(ctx, "shuttle_bus_estimate", "get price err")
		return errors.New("get price err")
	}
	billInfos := product.GetExtendList()
	if len(billInfos) == 0 || billInfos[0].BillInfo == nil || billInfos[0].BillInfo.CapPrice < 0 {
		log.Trace.Warnf(ctx, "shuttle_bus_estimate", "get price err")
		return errors.New("get price err")
	}
	// 获取单价总价
	unitPrice, totalPrice, err := GetPriceInfo(billInfos, logic.CommonLogic.request.CarpoolSeatNum)

	if err != nil {
		log.Trace.Warnf(ctx, "shuttle_bus_estimate", "get price err, err is %+v", err)
		return errors.New("get price err")
	}

	routeId := int32(logic.CommonLogic.RouteId)
	ret := &proto.ShuttleBusGuideBarData{
		RegionId:        util.ToInt32(logic.CommonLogic.RegionInfo.RegionId),
		CarpoolType:     util.ToInt32(product.Product.CarpoolType),
		RequireLevel:    util.ToInt32(product.Product.RequireLevel),
		BusinessId:      util.ToInt32(product.Product.BusinessID),
		ProductId:       util.ToInt32(product.Product.ProductID),
		ComboType:       util.ToInt32(product.Product.ComboType),
		ProductCategory: util.ToInt32(product.Product.ProductCategory),
		EstimateId:      product.Product.EstimateID,
		RouteType:       util.ToInt32(product.Product.RouteType),
		DistanceType:    util.ToInt32(logic.CommonLogic.DistanceType),
		FromArea:        renderAreaInfo(logic.CommonLogic.startStationInfo),
		ToArea:          renderAreaInfo(logic.CommonLogic.endStationInfo),
		RouteId:         &routeId,
		UnitPrice:       unitPrice,
		TotalPrice:      totalPrice,
		EstimateFee:     product.GetEstimateFee(),
		CarpoolSeatNum:  logic.CommonLogic.request.CarpoolSeatNum,
		EstimateTraceId: util.GetTraceIDFromCtxWithoutCheck(ctx),
	}
	rsp.Data = ret

	return nil
}

func renderAreaInfo(stationInfo models.StationInfo) *proto.Area {
	stationId := int32(stationInfo.StationId)
	return &proto.Area{
		DisplayName: stationInfo.Name,
		StationId:   &stationId,
		CityId:      int32(stationInfo.CityId),
		Lat:         stationInfo.Lat,
		Lng:         stationInfo.Lng,
		Address:     stationInfo.Address,
	}
}

func (logic *GuideBarLogic) CheckParams(ctx context.Context, request *proto.ShuttleBusGuideBarEstimateReq) error {
	if request == nil {
		return BizError.ErrInvalidArgument
	}
	if request.GetToken() == "" {
		return BizError.ErrNotLogin
	}
	if request.CityId == 0 || request.GetFromLat() == 0 || request.GetFromLng() == 0 || request.GetToLat() == 0 || request.GetToLng() == 0 {
		return BizError.ErrInvalidArgument
	}
	// 预约，代叫， 企业付，途经点不展示
	if request.OrderType == consts.BookOrder || request.CallCarType == consts.CallCarOrder || request.StopoverPoints != "" {
		return BizError.ErrInvalidArgument
	}

	return nil
}

func (logic *GuideBarLogic) InitCommonParams(ctx context.Context, request *proto.ShuttleBusGuideBarEstimateReq) models.CommonParam {
	carpoolSeatNum := int32(models.DefaultSeatNum)
	if request.CarpoolSeatNum != 0 {
		carpoolSeatNum = request.CarpoolSeatNum
	}
	ret := models.CommonParam{
		Token:          request.Token,
		UserType:       request.UserType,
		Channel:        request.Channel,
		AppVersion:     request.AppVersion,
		MapType:        request.MapType,
		AccessKeyID:    request.AccessKeyId,
		Lang:           request.Lang,
		FromLat:        request.FromLat,
		FromLng:        request.FromLng,
		ToLat:          request.ToLat,
		ToLng:          request.ToLng,
		CityId:         request.CityId,
		CarpoolSeatNum: carpoolSeatNum,
		IsForGuideBar:  true,
	}
	if request.PageType != nil {
		ret.PageType = *request.PageType
	}
	return ret
}
