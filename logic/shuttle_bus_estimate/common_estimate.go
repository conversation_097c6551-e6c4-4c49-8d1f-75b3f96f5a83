package shuttle_bus_estimate

import (
	"context"
	"errors"
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	Geofence "git.xiaojukeji.com/dirpc/dirpc-go-thrift-Geofence"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/route_type"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/product_category"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/source_id"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	Apollo2 "git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/fence"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/logic/shuttle_bus_estimate/config"
	"git.xiaojukeji.com/gulfstream/mamba/logic/shuttle_bus_estimate/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	models2 "git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	legoTrace "git.xiaojukeji.com/lego/context-go"
	"github.com/spf13/cast"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
	"math"
	"strconv"
	"time"
)

type ShuttleBusCommonLogic struct {
	request          models.CommonParam
	IsWhiteAccess    bool                       // 白名单用户
	RegionInfo       *config.RegionInfo         // 用户命中的区域信息，运营时间
	startStationInfo models.StationInfo         // 上车站点信息
	endStationInfo   models.StationInfo         // 下车站点信息
	RouteId          int                        // 路线Id
	RouteInfo        config.RouteInfo           // 路线信息
	SwitchPoint      bool                       // 是否有切换站点入口,true，可以切换站点
	SwitchPointsInfo map[int]models.StationInfo // 可切换起点信息
	DistanceType     int                        // 终点距离类型， 1，<=X米距离近终点匹配到的下车点 2，>X米距离远推荐的下车点
	generator        *biz_runtime.ProductsGenerator
}

func NewLogic(ctx context.Context, req models.CommonParam) (*ShuttleBusCommonLogic, error) {
	logic := &ShuttleBusCommonLogic{
		request: req,
	}
	var (
		err         error
		userInfo    *passport.UserInfo
		productsGen *biz_runtime.ProductsGenerator
		baseInfos   *models.BaseInfos
	)
	userInfo, err = passport.GetUserInfo(ctx, req.Token, "")
	if err != nil {
		return nil, err
	}
	// 开城
	isOpen := OpenCityCheck(req, userInfo)
	if isHitWhiteList(userInfo.Phone) {
		log.Trace.Infof(ctx, "shuttle_bus_estimate", "shuttle_bus hit white list:phone:%+v", userInfo.Phone)
		logic.IsWhiteAccess = true
	}
	if !isOpen && !logic.IsWhiteAccess {
		return nil, BizError.ErrNotInSceneArea
	}

	// 获取用户命中的区域信息
	logic.RegionInfo, err = logic.getStartRegionId(ctx, req)
	if err != nil || logic.RegionInfo == nil {
		return nil, err
	}
	// 区域运营时间校验
	status, reasons := OptionTimeCheck(logic.RegionInfo, logic.IsWhiteAccess)
	if !status {
		log.Trace.Infof(ctx, "shuttle_bus_estimate", "operation time filter reason :%+v", reasons)
		return logic, BizError.ErrInSceneAreaAndWithoutOperationTime
	}

	// 获取上下车站点 及 路线信息
	err = logic.GetStationInfo(ctx, req)
	if err != nil {
		log.Trace.Infof(ctx, "shuttle_bus_estimate", "get station file reason :%+v", err)
		return nil, errors.New("get station file")
	}
	// 导流入口做实验
	if req.IsForGuideBar {
		if !isHitShuttleBusABExp(ctx, req, *userInfo) {
			log.Trace.Infof(ctx, "shuttle_bus_estimate", "filter by exp shuttle_bus")
			return nil, errors.New("filter by exp")
		}
	}
	// gen
	baseInfos, err = logic.initBaseInfos(ctx, req, userInfo)
	if err != nil {
		return nil, err
	}
	if productsGen, err = biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("pShuttleBusEstimate"),
	); err != nil {
		return nil, err
	}

	productsGen.BaseReqData.AreaInfo = baseInfos.AreaInfo
	productsGen.BaseReqData.CommonInfo = baseInfos.CommonInfo
	productsGen.BaseReqData.PassengerInfo = baseInfos.PassengerInfo
	productsGen.BaseReqData.CommonBizInfo = baseInfos.CommonBizInfo

	productsGen.SetNeedMember(false)
	productsGen.SetisBaseReqInit(true)
	productsGen.SetSendReqKafka(false)
	logic.generator = productsGen
	return logic, nil
}

// initBaseInfos 初始化 userInfo、areaInfo、commonInfo、commonBizInfo
func (logic *ShuttleBusCommonLogic) initBaseInfos(ctx context.Context, req models.CommonParam, userInfo *passport.UserInfo) (infos *models.BaseInfos, err error) {
	// 构建 commonInfo
	commonInfo := logic.InitCommonEstimateReq(ctx, req)

	// 构建 passengerInfo
	passengerInfo := models2.PassengerInfo{}
	passengerInfo.BuildByUserInfoWithUserType(ctx, userInfo, req.UserType)

	// 构建 areaInfo
	areaInfo := logic.InitAreaEstimateReq(ctx, req)

	// 构造 commonBizInfo
	commonBizInfo := models2.CommonBizInfo{}
	if req.CarpoolSeatNum != 0 && logic.RouteInfo.RouteDirection != 0 {
		commonBizInfo.CarpoolSeatNum = req.CarpoolSeatNum
		commonBizInfo.RouteDirection = logic.RouteInfo.RouteDirection
		commonBizInfo.FenceId = logic.RegionInfo.RegionFenceId
	}

	baseInfos := &models.BaseInfos{
		Token:         req.Token,
		CommonInfo:    commonInfo,
		PassengerInfo: passengerInfo,
		AreaInfo:      areaInfo,
		CommonBizInfo: commonBizInfo,
	}
	return baseInfos, nil
}

func (logic *ShuttleBusCommonLogic) InitAreaEstimateReq(ctx context.Context, req models.CommonParam) models2.AreaInfo {
	a := models2.AreaInfo{}
	a.FromLng = logic.startStationInfo.Lng
	a.FromLat = logic.startStationInfo.Lat
	a.FromName = logic.startStationInfo.Name
	a.FromAddress = logic.startStationInfo.Address
	a.City = req.CityId
	a.ToLat = logic.endStationInfo.Lat
	a.ToLng = logic.endStationInfo.Lng
	a.ToAddress = logic.endStationInfo.Address
	a.ToName = logic.endStationInfo.Name

	a.MapType = req.MapType
	if err := a.TryFillCityInfo(ctx); err != nil {
		return a
	}
	return a
}

func (logic *ShuttleBusCommonLogic) InitCommonEstimateReq(ctx context.Context, req models.CommonParam) models2.CommonInfo {
	c := models2.CommonInfo{}
	c.AppVersion = req.AppVersion
	c.AccessKeyID = req.AccessKeyID
	c.Channel = util.ToInt64(req.Channel)
	c.DepartureTime = time.Now().Unix()
	c.PaymentsType = 2 // 个人支付
	c.Lang = req.Lang
	c.MenuID = consts.MenuIDDaCheAnyCar
	c.RouteID = int64(logic.RouteId)
	c.SourceID = source_id.SourceIdShuttleBus
	return c
}

func (logic *ShuttleBusCommonLogic) GetProduct(ctx context.Context) (*biz_runtime.ProductInfoFull, error) {
	var (
		err     error
		product *biz_runtime.ProductInfoFull
	)
	defer func() {
		if err == nil {
			util.Go(ctx, func() {
				logic.writePublicLog(ctx, product)
			})
		}
	}()
	// 获取基础品类
	if product = tryGetShuttleBusProduct(ctx, logic.generator); product == nil {
		return nil, BizError.ErrSystem
	}
	return product, nil

}
func tryGetShuttleBusProduct(ctx context.Context, generator *biz_runtime.ProductsGenerator) *biz_runtime.ProductInfoFull {
	var products []*biz_runtime.ProductInfoFull
	var err error
	if products, err = generator.GenProducts(ctx); err != nil {
		return nil
	}

	for _, product := range products {
		if product.GetProductCategory() == product_category.ProductCategoryCarpoolStation && product.GetNTuple().RouteType == route_type.RouteTypeCarpoolShuttleBus {
			return product
		}
	}
	return nil
}

func GetPriceInfo(billInfos []*PriceApi.EstimateNewFormExtend, carpoolSeatNum int32) (float64, float64, error) {
	var (
		unitPrice  float64
		totalPrice float64
	)
	for _, billInfo := range billInfos {
		if len(billInfo.SceneMark) == 0 {
			return unitPrice, totalPrice, errors.New("scene_mark err")
		}
		seatNumStr, ok := billInfo.SceneMark["seat_num"]
		if !ok || seatNumStr == "" {
			return unitPrice, totalPrice, errors.New("scene_mark err")
		}
		// 拼成未拼成一个价，都去未拼成
		isCarpoolSuccess, _ := billInfo.SceneMark["is_carpool_success"]
		seatNum := util.ToInt32(seatNumStr)
		if seatNum == 1 && isCarpoolSuccess == "0" {
			unitPrice = billInfo.BillInfo.CapPrice
		}
		if seatNum == carpoolSeatNum && isCarpoolSuccess == "0" {
			totalPrice = billInfo.BillInfo.CapPrice
		}
	}
	return unitPrice, totalPrice, nil

}

func OpenCityCheck(req models.CommonParam, userInfo *passport.UserInfo) bool {
	param := model.NewUser("").With("phone", userInfo.Phone).
		With("pid", cast.ToString(userInfo.PID)).
		With("city", cast.ToString(req.CityId)).
		With("access_key_id", cast.ToString(req.AccessKeyID)).
		With("app_version", cast.ToString(req.AppVersion))
	toggle, err := apollo.FeatureToggle("gs_shuttle_bus_open_city_switch", param)
	if err == nil && toggle.IsAllow() {
		return true
	}
	return false
}

func isHitWhiteList(phone string) bool {
	param := model.NewUser("").With("phone", phone)
	toggle, err := apollo.FeatureToggle("gs_shuttle_bus_filter_white_list", param)
	if err == nil && toggle.IsAllow() {
		return true
	}

	return false
}

func RPCGetStartFenceIDs(ctx context.Context, req models.CommonParam) (ids []int64) {
	cd := []*Geofence.Coordinate{{
		Lat: req.FromLat,
		Lng: req.FromLng,
	}}
	mapType, err := strconv.Atoi(req.MapType)
	if err != nil {
		log.Trace.Infof(ctx, "shuttle_bus_estimate", "map type :%+v", req.MapType)
	}
	ret, err := fence.MultiInFence(ctx, cd, []int32{21}, mapType)
	if err != nil || len(ret) == 0 {
		return nil
	}
	for _, m := range ret[0] {
		ids = append(ids, m.FenceIds...)
	}
	return
}

// getStartRegionId 经纬度匹配用户所在区域信息
func (logic *ShuttleBusCommonLogic) getStartRegionId(ctx context.Context, req models.CommonParam) (*config.RegionInfo, error) {
	// 获取城市内的所有区域信息
	var (
		regionInfo config.RegionInfo
		err        error
		fenceIDs   []int64
	)
	//白名单默认命中大唐不夜城景区
	if logic.IsWhiteAccess {
		whiteRegionId := 1
		if req.RegionId != 0 {
			whiteRegionId = int(req.RegionId)
		}
		regionInfo, err := config.LoadShuttleBusRegionConfWithRegionId(ctx, int32(whiteRegionId))
		if err != nil {
			return nil, BizError.ErrNotInSceneArea
		}
		return &regionInfo, nil
	}
	// 获取用户所在的围栏ID
	fenceIDs = RPCGetStartFenceIDs(ctx, req)
	if req.IsForGuideBar {
		regionConf, err := config.LoadShuttleBusRegionConf(ctx, cast.ToString(req.CityId))
		if err != nil || len(regionConf) == 0 {
			return nil, BizError.ErrNotInSceneArea
		}
		for _, fenceID := range fenceIDs {
			for _, item := range regionConf {
				if cast.ToInt64(item.RegionFenceId) == fenceID {
					return &item, nil
				}
			}
		}

	} else {
		regionInfo, err = config.LoadShuttleBusRegionConfWithRegionId(ctx, req.RegionId)
		if err != nil {
			return nil, BizError.ErrSystem
		}
		for _, fenceID := range fenceIDs {
			if cast.ToInt64(regionInfo.RegionFenceId) == fenceID {
				return &regionInfo, nil
			}
		}
	}

	return nil, BizError.ErrNotInSceneArea
}

func (logic *ShuttleBusCommonLogic) GetStationInfo(ctx context.Context, req models.CommonParam) error {
	// 获取区域内的路线
	var (
		err            error
		endStationId   int
		startStationId int
	)
	routeInfo := config.LoadShuttleBusRouteConfCache(ctx, logic.RegionInfo.RegionId)

	// 获取终点站点信息
	if req.IsForGuideBar {
		endStationId, err = logic.getEndStationId(ctx, req, routeInfo.EndStationIdList)
		if err != nil {
			return err
		}
		endStationInfo, err := getStationInfo(ctx, endStationId)
		if err != nil {
			return err
		}
		logic.endStationInfo = endStationInfo
	} else {
		endStationId = int(req.EndStationId)
		endStationInfo, err := getStationInfo(ctx, endStationId)
		if err != nil {
			return err
		}
		logic.endStationInfo = endStationInfo
	}
	// 获取起点站点信息
	startStationId, err = logic.getStartStationId(ctx, req, routeInfo.End2StartMap)
	if err != nil {
		return nil
	}
	startStationInfo, err := getStationInfo(ctx, startStationId)
	if err != nil {
		return err
	}
	logic.startStationInfo = startStationInfo

	// 依据上下车点找到路线id
	key := cast.ToString(startStationId) + "_" + cast.ToString(endStationId)
	logic.RouteId = routeInfo.Stations2RouteId[key]
	logic.RouteInfo, err = config.LoadShuttleBusRouteConfWithRouteId(ctx, int32(logic.RouteId))
	if err != nil {
		return err
	}
	return nil
}

func (logic *ShuttleBusCommonLogic) getStartStationId(ctx context.Context, req models.CommonParam, end2StartMap map[int][]int) (int, error) {
	if req.SourceType == 1 && req.StartStationId != 0 {
		startStationList := end2StartMap[logic.endStationInfo.StationId]
		// 终点站点对应多个起点，则起点可切换
		if len(startStationList) > 1 {
			logic.SwitchPoint = true
		}
		switchPointsInfo := make(map[int]models.StationInfo, 0)
		for _, stationId := range startStationList {
			stationInfo, err := getStationInfo(ctx, stationId)
			if err != nil {
				continue
			}
			switchPointsInfo[stationId] = stationInfo
		}
		logic.SwitchPointsInfo = switchPointsInfo
		return int(req.StartStationId), nil
	}
	var startStationId int
	minDistance := math.MaxFloat64
	startStationList := end2StartMap[logic.endStationInfo.StationId]
	// 终点站点对应多个起点，则起点可切换
	if len(startStationList) > 1 {
		logic.SwitchPoint = true
	}
	switchPointsInfo := make(map[int]models.StationInfo, 0)
	for _, stationId := range startStationList {
		stationInfo, err := getStationInfo(ctx, stationId)
		if err != nil {
			continue
		}
		switchPointsInfo[stationId] = stationInfo
		distance := GetDistance(stationInfo.Lat, stationInfo.Lng, req.FromLat, req.FromLng)
		if distance < minDistance {
			minDistance = distance
			startStationId = stationId
		}
	}
	logic.SwitchPointsInfo = switchPointsInfo
	// 起点站点要在区域配置的站点列表内
	if !util.InArrayStr(cast.ToString(startStationId), logic.RegionInfo.StationIdList) {
		return startStationId, errors.New("region Id " + logic.RegionInfo.RegionId + " dont have this station Id " + cast.ToString(startStationId))
	}
	return startStationId, nil
}

func (logic *ShuttleBusCommonLogic) getEndStationId(ctx context.Context, req models.CommonParam, stationList []int) (int, error) {
	recommendDistance := models.RecommendDistance
	if logic.RegionInfo.RecommendDistance != 0 {
		recommendDistance = logic.RegionInfo.RecommendDistance
	}
	endStationId := logic.GetEndStationId(ctx, req, stationList, recommendDistance)
	if endStationId == 0 {
		return endStationId, errors.New("dont have recommend end station")
	}

	return endStationId, nil
}

func getStationInfo(ctx context.Context, stationId int) (models.StationInfo, error) {
	var stationInfo models.StationInfo
	stationConf, ok := config.LoadStationInfoConf(ctx, stationId)
	if !ok {
		return stationInfo, errors.New("get station apollo err")
	}
	stationInfo.StationId = stationConf.StationId
	stationInfo.CityId = stationConf.CityId
	stationInfo.Name = stationConf.Name
	stationInfo.Address = stationConf.Address
	stationInfo.Lat = cast.ToFloat64(stationConf.Lat)
	stationInfo.Lng = cast.ToFloat64(stationConf.Lng)

	return stationInfo, nil
}

func (logic *ShuttleBusCommonLogic) GetEndStationId(ctx context.Context, req models.CommonParam, stationIdList []int, recommendDistance float64) int {
	var endStationId int
	minDistance := math.MaxFloat64
	for _, stationId := range stationIdList {
		stationConf, ok := config.LoadStationInfoConf(ctx, stationId)
		if !ok {
			continue
		}
		distance := GetDistance(cast.ToFloat64(stationConf.Lat), cast.ToFloat64(stationConf.Lng), req.ToLat, req.ToLng)
		if distance <= recommendDistance && distance < minDistance {
			minDistance = distance
			endStationId = stationId
			logic.DistanceType = 1
		}
	}
	if endStationId == 0 && logic.RegionInfo.RecommendEndStationId != "" {
		logic.DistanceType = 2
		endStationId = cast.ToInt(logic.RegionInfo.RecommendEndStationId)
	}
	return endStationId
}

// 做实验
func isHitShuttleBusABExp(ctx context.Context, req models.CommonParam, userInfo passport.UserInfo) bool {
	params := map[string]string{
		"pid":           util.ToString(userInfo.PID),
		"app_version":   req.AppVersion,
		"city":          util.ToString(req.CityId),
		"lang":          req.Lang,
		"access_key_id": util.ToString(req.AccessKeyID),
	}
	toggleAllow, toggleAssignment := Apollo2.FeatureExp(ctx, "shuttle_bus", util.ToString(userInfo.PID), params)

	if toggleAllow && "treatment_group" == toggleAssignment.GetGroupName() {
		return true
	}

	return false
}

// GetDistance 根据两点间经纬度坐标（double值），计算两点间距离，单位为米.
func GetDistance(lat1, lng1, lat2, lng2 float64) float64 {
	radLat1 := lat1 * models.RAD
	radLat2 := lat2 * models.RAD
	a := radLat1 - radLat2
	b := (lng1 - lng2) * models.RAD
	s := 2 * math.Asin(math.Sqrt(math.Pow(math.Sin(a/2), 2)+math.Cos(radLat1)*math.Cos(radLat2)*math.Pow(math.Sin(b/2), 2)))

	return s * models.EarthRadius
}

func (logic *ShuttleBusCommonLogic) writePublicLog(ctx context.Context, product *biz_runtime.ProductInfoFull) {
	// 屏蔽压测流量
	if _trace := legoTrace.GetTrace(ctx); _trace.GetHintCode() == strconv.Itoa(int(consts.PressureHintCode)) {
		return
	}

	logInfo := make(map[string]interface{})
	traceId := util.GetTraceIDFromCtxWithoutCheck(ctx)
	// 索引key
	logInfo["estimate_trace_id"] = traceId
	// 产品信息
	if product == nil {
		return
	}
	logInfo["estimate_id"] = product.Product.EstimateID
	logInfo["product_category"] = product.GetProductCategory()
	logInfo["product_id"] = product.Product.ProductID
	logInfo["business_id"] = product.Product.BusinessID
	logInfo["route_type"] = product.Product.RouteType
	logInfo["require_level"] = product.Product.RequireLevel
	logInfo["level_type"] = product.Product.LevelType
	logInfo["combo_type"] = product.Product.ComboType
	logInfo["carpool_type"] = product.Product.CarpoolType
	logInfo["order_type"] = product.Product.OrderType
	logInfo["source_id"] = source_id.SourceIdShuttleBus
	logInfo["menu_id"] = "dache_anycar"
	// 地理位置信息
	if areaInfo := product.GetAreaInfo(); areaInfo != nil {
		logInfo["area"] = areaInfo.City
		logInfo["to_area"] = areaInfo.ToArea
		logInfo["district"] = areaInfo.District
		logInfo["county"] = areaInfo.FromCounty
		logInfo["to_county"] = areaInfo.ToCounty
		logInfo["from_name"] = util.StringEscape(areaInfo.FromName)
		logInfo["to_name"] = util.StringEscape(areaInfo.ToName)
		logInfo["flat"] = areaInfo.FromLat
		logInfo["flng"] = areaInfo.FromLng
		logInfo["tlat"] = areaInfo.ToLat
		logInfo["tlng"] = areaInfo.ToLng
		logInfo["route_id"] = logic.RouteId
		logInfo["map_type"] = areaInfo.MapType
	}
	// 端信息
	if commonInfo := product.GetClientInfo(); commonInfo != nil {
		logInfo["app_version"] = commonInfo.AppVersion
		logInfo["client_type"] = commonInfo.ClientType
		logInfo["access_key_id"] = commonInfo.AccessKeyID
		logInfo["channel_id"] = commonInfo.Channel
		logInfo["origin_id"] = commonInfo.OriginID
	}
	// 用户信息
	if product.BaseReqData != nil {
		logInfo["pid"] = product.BaseReqData.PassengerInfo.PID
		logInfo["uid"] = product.BaseReqData.PassengerInfo.UID
	}
	if len(product.GetExtendList()) == 0 {
		return
	}
	billInfos := product.GetExtendList()
	if len(billInfos) == 0 || billInfos[0].BillInfo == nil || billInfos[0].BillInfo.CapPrice < 0 {
		return
	}
	// 价格信息
	unitPrice, totalPrice, err := GetPriceInfo(billInfos, logic.request.CarpoolSeatNum)

	logInfo["cap_price"] = totalPrice // 存总价
	logInfo["carpool_seat_num"] = logic.request.CarpoolSeatNum
	// 落地页来源区分 https://work.didi.cn/cx/mfe_gulfstream#/featureDict/featureDetail?featureId=991&dimensionId=4

	logInfo["page_type"] = logic.request.PageType
	if logic.request.SourceType == 1 {
		logInfo["from_type"] = 1
	}
	if logic.request.SourceType == 2 {
		logInfo["from_type"] = 10
	}

	log.Public.Public(ctx, "g_order_estimate_price", logInfo)

	// 配置类数据
	configLogInfo := make(map[string]interface{})
	// 索引key
	configLogInfo["estimate_trace_id"] = traceId
	configLogInfo["estimate_id"] = product.Product.EstimateID
	configLogInfo["city_id"] = logic.RegionInfo.CityId
	configLogInfo["region_id"] = logic.RegionInfo.RegionId
	configLogInfo["fence_id"] = logic.RegionInfo.RegionFenceId
	configLogInfo["region_type"] = logic.RegionInfo.RegionType
	configLogInfo["everyday_time"] = logic.RegionInfo.EveryDayTime
	configLogInfo["region_name"] = logic.RegionInfo.RegionName
	routeInfo, err := config.LoadShuttleBusRouteConfWithRouteId(ctx, int32(logic.RouteId))
	if err != nil {
		return
	}
	configLogInfo["route_id"] = routeInfo.RouteId
	configLogInfo["unit_price"] = unitPrice // 落单价
	configLogInfo["route_name"] = routeInfo.RouteName
	configLogInfo["route_direction"] = routeInfo.RouteDirection
	configLogInfo["start_station_id"] = routeInfo.StartStationId
	configLogInfo["end_station_id"] = routeInfo.EndStationId
	configLogInfo["start_station_name"] = logic.startStationInfo.Name
	configLogInfo["end_station_name"] = logic.endStationInfo.Name
	log.Public.Public(ctx, "g_shuttle_bus_estimate_config_info", configLogInfo)
}
