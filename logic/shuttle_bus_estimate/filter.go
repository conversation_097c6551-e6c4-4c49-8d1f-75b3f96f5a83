package shuttle_bus_estimate

import (
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/logic/shuttle_bus_estimate/config"
	"github.com/spf13/cast"
	"time"
)

const DateTimeLiteLength = 10 // 仅有年月日的日期格式字符串长度小于10

func OptionTimeCheck(regionInfo *config.RegionInfo, isWhitePhone bool) (bool, string) {
	if len(regionInfo.Date) != 0 && !dateCheck(regionInfo.Date) {
		return false, "filter by data"
	}
	if len(regionInfo.EveryDayTime) != 0 && !dayTimeCheck(regionInfo.EveryDayTime) {
		return false, "filer by day times"
	}

	return true, ""
}

// dateCheck 日期校验
func dateCheck(timeList []string) bool {
	if len(timeList) != 2 {
		return false
	}
	startStr := cast.ToString(timeList[0])
	endStr := cast.ToString(timeList[1])
	if startStr == "" || endStr == "" {
		return false
	}
	startTime := ParseAllDate(startStr)
	endTime := ParseAllDate(endStr)
	nowTime := time.Now().Unix()
	if nowTime >= startTime && nowTime <= endTime {
		return true
	}
	return false
}

func weekCheck(weeks []string) bool {
	var effectiveDays []int = make([]int, len(weeks))
	for i, day := range weeks {
		effectiveDays[i] = cast.ToInt(day)
	}
	weekDay := int(time.Now().Weekday())
	isAllow := util.InArrayInt(weekDay, effectiveDays)
	return isAllow
}

func dayTimeCheck(conf []config.EveryDayTime) bool {
	for _, item := range conf {
		// 如果没有配置，默认为true
		if item.OnePeriod == nil {
			return true
		}
		if len(item.OnePeriod) != 2 {
			continue
		}
		timePrefix := time.Now().Format("2006-01-02")
		startTimeStr := timePrefix + " " + item.OnePeriod[0]
		endTimeStr := timePrefix + " " + item.OnePeriod[1]
		if startTimeStr == "" || endTimeStr == "" {
			continue
		}
		startTime := util.ParseAllDate(startTimeStr)
		endTime := util.ParseAllDate(endTimeStr)
		nowTime := time.Now().Unix()
		if nowTime >= startTime && nowTime <= endTime {
			return true
		}
	}
	return false
}

func ParseAllDate(date string) int64 {
	var (
		t      int64
		layout []string
	)

	if len(date) <= DateTimeLiteLength {
		layout = []string{"2006-01-02"}
	} else {
		layout = []string{"2006-01-02 15:04:05", "2006-01-02 15:04"}
	}

	for _, lt := range layout {
		ts, err := time.ParseInLocation(lt, date, time.Local)
		if err == nil {
			t = ts.Unix()
			break
		}
	}

	return t
}
