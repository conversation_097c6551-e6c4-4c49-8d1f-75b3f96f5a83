package combinedtravel

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/source_id"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

func InitProductGenerator(ctx context.Context, req *proto.MultiEstimatePriceRequest) (*biz_runtime.ProductsGenerator, int) {
	var (
		err         error
		productsGen *biz_runtime.ProductsGenerator
	)

	productsGen, err = biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithCommonReq(ctx, req),
		biz_runtime.WithReqFrom(consts.FromTypeCombinedTravel),
		biz_runtime.WithSourceID(ctx, source_id.SourceIDCombinedTravel),
	)
	if err != nil {
		return nil, consts.ErrnoSystemError
	}
	productsGen.SetSendReqKafka(true)

	// 8.0 组合出行才走athena 默勾推荐
	if productsGen.BaseReqData != nil && req.GetCombinedTravelBizType() == consts.CombinedTravelBizTypeUT80 {
		// 冒泡推荐
		if recommendRPC := rpc_process.NewBubbleRecommendInfoRPC(productsGen.BaseReqData, rpc_process.CombinedTravelOrderMatchType); recommendRPC != nil {
			productsGen.RegisterAfterPriceRPCProcess(recommendRPC)
		}
	}

	return productsGen, consts.NoErr
}
