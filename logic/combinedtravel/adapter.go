package combinedtravel

import (
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type CombinedTravelAdapter struct {
	*biz_runtime.ProductInfoFull
}

func (a *CombinedTravelAdapter) GetEstimateStyleType() int32 {
	return a.BaseReqData.CommonInfo.EstimateStyleType
}

func (a *CombinedTravelAdapter) GetBonus() float64 {
	fee := a.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}
	amount := fee.GetFeeDetail().GetBonusAmount()
	if amount == nil {
		return 0
	}
	return *amount
}

func (a *CombinedTravelAdapter) GetFastEstimatePrice() float64 {
	return a.BaseReqData.CommonBizInfo.FastCarPrice
}

func (a *CombinedTravelAdapter) GetTabId() string {
	return a.BaseReqData.CommonInfo.TabId
}

func (a *CombinedTravelAdapter) GetProductCheckStatus() int {
	return a.GetBizInfo().CheckStatus
}

func (a *CombinedTravelAdapter) GetMultiRequireProduct() []models.RequireProduct {
	if a.BaseReqData == nil {
		return nil
	}

	return a.BaseReqData.CommonBizInfo.MultiRequireProduct
}

func (a *CombinedTravelAdapter) GetBaseReqData() *models.BaseReqData {
	return a.BaseReqData
}
