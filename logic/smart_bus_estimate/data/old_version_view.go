package data

import (
	"context"
	"encoding/json"
	"fmt"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/from_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/smart_bus_estimate/data/build_func/map_station"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/smart_bus"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"
	"math"
	"strconv"
)

const (
	SmartBusFeeDetailKey     = "smart_bus-fee_detail"
	SmartBusSeparatePageConf = "smart_bus-separate_page_conf"

	SmartBusPageVersionOld = "smart_bus-old_version"
)

type OldVersionRender struct {
	BaseRender
}

type ExtraInfo struct {
	Icon           string `json:"icon"`
	ShowH5         string `json:"show_h5"`
	ConfirmH5      string `json:"confirm_h5"`
	BackgroundIcon string `json:"back_ground_icon"`
	BaseExtraInfo
}

func (o *OldVersionRender) Init(_ context.Context) {}

func (o *OldVersionRender) RenderByProduct(ctx context.Context, product *biz_runtime.ProductInfoFull) *proto.EstimateFromData {

	if product == nil {
		return nil
	}
	o.adapter = &SmartBusAdapter{ProductInfoFull: product}

	// 是否为候补订单 0:非候补 1:候补 2:起终点过近
	var preMatchType int32
	if product.GetSmartBusEtp() == -1 {
		preMatchType = PreMatchTypeNoCar
	}
	o.preMatchType = preMatchType

	if PreMatchTypePreMatch == preMatchType {
		preMatchInfo := o.adapter.GetSmartBusPreMatch()
		if preMatchInfo != nil && preMatchInfo.ExtMap != nil && preMatchInfo.ExtMap["chosen_station_info"] != "" {
			err := json.Unmarshal([]byte(preMatchInfo.ExtMap["chosen_station_info"]), &o.startStationInfo)
			if err != nil {
				o.startStationInfo = nil
				log.Trace.Warnf(ctx, trace.DLTagUndefined, "smart bus prematch unmarshal duse start station failed:%v", err)
			}
		} else {
			o.startStationInfo = nil
		}

	}

	if o.adapter == nil {
		return nil
	}

	resp := &proto.EstimateFromData{
		EstimateId: o.adapter.GetEstimateID(),
		ExtraMap:   o.buildExtraMap(ctx),
		Tp: &proto.TP{
			FeeAmount:      o.adapter.GetEstimateFee(),
			FeeMsg:         o.getFeeMsg(ctx),
			NoticeTitle:    o.getNoticeTitle(ctx),
			NoticeSubtitle: o.getNoticeSubtitle(ctx),
			FeeDescList:    o.getPriceInfoDescList(ctx),
			Tag:            o.getTag(ctx),
		},
		StartStationInfo: o.buildStartStationInfo(ctx),
		DestStationInfo:  map_station.ScanInCarWithTPDestStation(o.adapter.ProductInfoFull, o.adapter.BaseReqData),
	}
	return resp
}

func (o *OldVersionRender) buildExtraMap(_ context.Context) *proto.MiniBusExtraMap {
	resp := &proto.MiniBusExtraMap{
		ProductId:       o.adapter.GetProductId(),
		BusinessId:      o.adapter.GetBusinessID(),
		ComboType:       o.adapter.GetComboType(),
		LevelType:       o.adapter.GetLevelType(),
		CarpoolType:     o.adapter.GetCarpoolType(),
		Etp:             o.adapter.GetSmartBusEtp(),
		ProductCategory: o.adapter.GetProductCategory(),
	}
	requireLevel, err := strconv.ParseInt(o.adapter.GetRequireLevel(), 10, 64)
	if err == nil {
		resp.RequireLevel = requireLevel
	}
	return resp
}

func (o *OldVersionRender) getFeeMsg(ctx context.Context) string {
	return dcmp.GetJSONContentWithPath(ctx, SmartBusFeeDetailKey, map[string]string{
		"amount": strconv.FormatFloat(o.adapter.GetEstimateFee(), 'f', 2, 32),
	}, "fee_msg")
}

func (o *OldVersionRender) getNoticeTitle(ctx context.Context) string {
	preMatchInfo := o.adapter.GetSmartBusPreMatch()
	if preMatchInfo == nil || preMatchInfo.EtpInfo == nil {
		return ""
	}

	path := func() string {
		suffix := ""
		switch {
		case from_type.FromTypeFromTypeScanInCar == o.adapter.BaseReqData.CommonInfo.FromType:
			suffix = "_scan_in_car"
		case PreMatchTypeNoCar == o.preMatchType:
			suffix = "_notp"
		}
		return "notice_title" + suffix
	}

	return dcmp.GetJSONContentWithPath(ctx, SmartBusFeeDetailKey, map[string]string{
		"amount": strconv.Itoa(int(math.Ceil(((float64)(o.adapter.GetSmartBusEtp())) / 60))),
	}, path())

}

func (o *OldVersionRender) getNoticeSubtitle(ctx context.Context) string {
	preMatchInfo := o.adapter.GetSmartBusPreMatch()
	if preMatchInfo == nil || preMatchInfo.EtpInfo == nil {
		return ""
	}
	path := func() string {
		suffix := ""
		switch {
		case from_type.FromTypeFromTypeScanInCar == o.adapter.BaseReqData.CommonInfo.FromType:
			suffix = "_scan_in_car"
		case PreMatchTypeNoCar == o.preMatchType:
			suffix = "_notp"
		}
		return "notice_subtitle" + suffix
	}

	return dcmp.GetJSONContentWithPath(ctx, SmartBusFeeDetailKey, nil, path())

}

func (o *OldVersionRender) getPriceInfoDescList(ctx context.Context) []*proto.NewFormFeeDesc {
	return smart_bus.GetPriceInfoDescList(ctx, o.adapter)
}

func (o *OldVersionRender) getTag(ctx context.Context) string {
	preMatchInfo := o.adapter.GetSmartBusPreMatch()
	if preMatchInfo == nil || preMatchInfo.EtdInfo == nil {
		return ""
	}
	if PreMatchTypePreMatch == o.preMatchType {
		return dcmp.GetJSONContentWithPath(ctx, SmartBusFeeDetailKey, map[string]string{
			"amount1": formatTime(preMatchInfo.EtdInfo.EtdLeftMargin),
			"amount2": formatTime(preMatchInfo.EtdInfo.EtdRightMargin),
		}, "tag")
	}
	tag := dcmp.GetJSONContentWithPath(ctx, SmartBusFeeDetailKey, nil, "tag_notp")
	return tag
}

func (o *OldVersionRender) buildStartStationInfo(_ context.Context) (startStationInfo *proto.SmartBusStationInfo) {
	//车上扫码场景
	if from_type.FromTypeFromTypeScanInCar == o.adapter.BaseReqData.CommonInfo.FromType {
		return map_station.ScanInCarWithTPStartStation(o.adapter.ProductInfoFull, o.adapter.BaseReqData)
	}

	//default strategy
	if PreMatchTypeNoCar == o.preMatchType || nil == o.startStationInfo {
		return
	}
	startStationInfo = &proto.SmartBusStationInfo{
		PoiId:       cast.ToString(o.startStationInfo["poi_id"]),
		Lat:         cast.ToFloat64(o.startStationInfo["lat"]),
		Lng:         cast.ToFloat64(o.startStationInfo["lng"]),
		DisplayName: cast.ToString(o.startStationInfo["displayname"]),
	}
	return startStationInfo
}

func (o *OldVersionRender) ExtendDataRender(ctx context.Context, resp *proto.MiniBusEstimateData, baseReqData *models.BaseReqData, products []*biz_runtime.ProductInfoFull) {
	dcmpConf := dcmp.GetDcmpContent(ctx, SmartBusSeparatePageConf, nil)
	conf := &ExtraInfo{}
	err := json.Unmarshal([]byte(dcmpConf), &conf)
	if err != nil {
		return
	}
	companyName, estimateTitle, seatRes := getCompanyNameAndSeat(ctx, baseReqData, products, conf.DefaultCompanyName, conf.Title)
	// 本次小巴需要画曲线，直接赋值1，最好加一个枚举
	resp.MapCurveInfo = &proto.MapCurveInfo{
		IsDrawCurve: 1,
		CurveType:   proto.Int32Ptr(2),
	}
	//地图视图类型
	BuildBestViewType(resp, baseReqData)

	//地图视图切换按钮
	BuildBestViewTypeSwitch(resp, baseReqData)

	resp.CarpoolSeatModule = seatRes
	resp.PageType = int16(baseReqData.CommonInfo.PageType)
	//发单按钮构建
	BuildNewOrderButton(resp, baseReqData, &conf.BaseExtraInfo, "")

	if baseReqData.CommonInfo.FromType == ShowBackButton {
		resp.BackButton = &proto.BackButton{
			LeftIcon: conf.BackIcon,
			JumpTo:   conf.JumpLink,
		}
	}

	resp.DynamicEffectParams = &proto.DynamicEffectParams{}
	if len(resp.EstimateData) > 0 {
		resp.Icon = &conf.Icon
		resp.Title = estimateTitle
		resp.Subtitle = fmt.Sprintf(conf.Subtitle, companyName)
		resp.IsSupportMultiSelection = 0
		resp.FeeDetailUrl = conf.FeeDetailUrl
		resp.PluginPageInfo = &proto.MiniBusPluginPageInfo{
			ShowH5:    conf.ShowH5,
			ConfirmH5: conf.ConfirmH5,
		}
		if len(resp.EstimateData) == 1 && resp.EstimateData[0] != nil && resp.EstimateData[0].ExtraMap != nil && resp.EstimateData[0].ExtraMap.Etp == -1 {
			resp.DynamicEffectParams.MarkIcon = conf.MarkIconNotp
		} else {
			resp.DynamicEffectParams.MarkIcon = conf.MarkIcon
		}
		defaultSelected(resp)
		resp.DynamicEffectParams.SelectBgColor = &conf.SelectBgColor
		resp.PreMatchType = &o.preMatchType
	} else if len(resp.EstimateData) == 0 {
		buildRespWithNoTP(resp, baseReqData, &conf.BaseExtraInfo)
	}

	//构建地图站牌：for 无TP
	FillMapStationWithoutTP(resp, baseReqData)

	//地图token
	BuildMapToken(resp, baseReqData)

	resp.OmegaInfo = buildOmegaData(ctx, baseReqData, resp)
	resp.PneworderParams = &proto.SmartBusNewOrderParam{Dchn: &baseReqData.CommonInfo.Dchn}
	resp.SmartBusPageVersion = proto.StrPtr(SmartBusPageVersionOld)
}
