package data

import (
	"context"
	"encoding/json"
	"fmt"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/product/from_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/smart_bus_estimate/data/build_func/map_station"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/smart_bus"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"
	"math"
	"strconv"
)

const (
	SmartBusFeeDetailUnifiedPriceKey        = "smart_bus-fee_detail_unified_price"
	SmartBusSeparatePageConfUnifiedPriceKey = "smart_bus-separate_page_conf_unified_price"

	SmartBusPageVersionUnifiedPrice = "smart_bus-unified_price"

	LogTag = "smart_bus"
)

type UnifiedPriceRender struct {
	FeeDetail    *UnifiedPriceFeeDetail
	SeparatePage *UnifiedPriceSeparatePage
	BaseRender
}

type UnifiedPriceFeeDetail struct {
	FeeMsg                  string `json:"fee_msg"`
	NoticeTitle             string `json:"notice_title"`
	NoticeTitleNotp         string `json:"notice_title_notp"`
	NoticeTitleScanInCar    string `json:"notice_title_scan_in_car"`
	NoticeSubtitle          string `json:"notice_subtitle"`
	NoticeSubtitleNotp      string `json:"notice_subtitle_notp"`
	NoticeSubtitleScanInCar string `json:"notice_subtitle_scan_in_car"`
	ConfirmSubtitle         string `json:"confirm_subtitle"`
	NoticeTextNotp          string `json:"notice_text_notp"`
	NoticeSubtext           string `json:"notice_subtext"`
	WalkDistLeftThreshold   int32  `json:"walk_dist_left_threshold"`
	WalkDistRightThreshold  int32  `json:"walk_dist_right_threshold"`
}

type UnifiedPriceSeparatePage struct {
	BaseExtraInfo
}

func (u *UnifiedPriceRender) Init(ctx context.Context) {
	dcmpFeeDetail := dcmp.GetDcmpContent(ctx, SmartBusFeeDetailUnifiedPriceKey, nil)
	feeDetail := &UnifiedPriceFeeDetail{}
	err := json.Unmarshal([]byte(dcmpFeeDetail), &feeDetail)
	if err != nil {
		log.Trace.Warnf(ctx, LogTag, "Unmarshal dcmp smart_bus-fee_detail_unified_price fail, err:[%v]", err)
		return
	}
	u.FeeDetail = feeDetail
	dcmpSeparatePage := dcmp.GetDcmpContent(ctx, SmartBusSeparatePageConfUnifiedPriceKey, nil)
	separatePage := &UnifiedPriceSeparatePage{}
	err = json.Unmarshal([]byte(dcmpSeparatePage), &separatePage)
	if err != nil {
		log.Trace.Warnf(ctx, LogTag, "Unmarshal dcmp smart_bus-separate_page_conf_unified_price fail, err:[%v]", err)
		return
	}
	u.SeparatePage = separatePage
}

func (u *UnifiedPriceRender) RenderByProduct(ctx context.Context, product *biz_runtime.ProductInfoFull) *proto.EstimateFromData {
	if product == nil || u.FeeDetail == nil {
		return nil
	}
	u.adapter = &SmartBusAdapter{ProductInfoFull: product}

	// 是否为候补订单 0:非候补 1:候补
	var preMatchType int32
	if product.GetSmartBusEtp() == -1 {
		preMatchType = PreMatchTypeNoCar
	}
	u.preMatchType = preMatchType

	if PreMatchTypePreMatch == preMatchType {
		preMatchInfo := u.adapter.GetSmartBusPreMatch()
		if preMatchInfo != nil && preMatchInfo.ExtMap != nil && preMatchInfo.ExtMap["chosen_station_info"] != "" {
			err := json.Unmarshal([]byte(preMatchInfo.ExtMap["chosen_station_info"]), &u.startStationInfo)
			if err != nil {
				u.startStationInfo = nil
				log.Trace.Warnf(ctx, trace.DLTagUndefined, "smart bus prematch unmarshal duse start station failed:%v", err)
			}
		} else {
			u.startStationInfo = nil
		}

	}

	resp := &proto.EstimateFromData{
		EstimateId: u.adapter.GetEstimateID(),
		ExtraMap:   u.buildExtraMap(ctx),
		Tp: &proto.TP{
			FeeAmount:      u.adapter.GetEstimateFee(),
			FeeMsg:         u.getFeeMsg(ctx),
			NoticeTitle:    u.getNoticeTitle(ctx),
			NoticeSubtitle: u.getNoticeSubtitle(ctx),
			FeeDescList:    u.getPriceInfoDescList(ctx),
			Tag:            "",
			NoticeText:     u.getNoticeText(ctx),
			NoticeSubtext:  u.getNoticeSubtext(ctx),
		},
		StartStationInfo: u.buildStartStationInfo(ctx),
		DestStationInfo:  map_station.ScanInCarWithTPDestStation(u.adapter.ProductInfoFull, u.adapter.BaseReqData),
		ConfirmSubtitle:  u.getConfirmSubtitle(ctx),
		SideParams:       u.buildSideParams(ctx),
	}
	return resp
}

func (u *UnifiedPriceRender) buildExtraMap(_ context.Context) *proto.MiniBusExtraMap {
	resp := &proto.MiniBusExtraMap{
		ProductId:       u.adapter.GetProductId(),
		BusinessId:      u.adapter.GetBusinessID(),
		ComboType:       u.adapter.GetComboType(),
		LevelType:       u.adapter.GetLevelType(),
		CarpoolType:     u.adapter.GetCarpoolType(),
		Etp:             u.adapter.GetSmartBusEtp(),
		ProductCategory: u.adapter.GetProductCategory(),
	}
	requireLevel, err := strconv.ParseInt(u.adapter.GetRequireLevel(), 10, 64)
	if err == nil {
		resp.RequireLevel = requireLevel
	}
	return resp
}

func (u *UnifiedPriceRender) getFeeMsg(_ context.Context) string {
	return dcmp.TranslateTemplate(u.FeeDetail.FeeMsg, map[string]string{
		"amount": strconv.FormatFloat(u.adapter.GetEstimateFee(), 'f', 2, 32),
	})
}

func (u *UnifiedPriceRender) getNoticeTitle(ctx context.Context) string {
	preMatchInfo := u.adapter.GetSmartBusPreMatch()
	if preMatchInfo == nil || preMatchInfo.EtpInfo == nil {
		return ""
	}

	if from_type.FromTypeFromTypeScanInCar == u.adapter.BaseReqData.CommonInfo.FromType {
		return u.FeeDetail.NoticeTitleScanInCar
	}

	// 非候补
	if PreMatchTypePreMatch == u.preMatchType {
		return dcmp.TranslateTemplate(u.FeeDetail.NoticeTitle, map[string]string{
			"amount": strconv.Itoa(int(math.Ceil(((float64)(u.adapter.GetSmartBusEtp())) / 60))),
		})
	}

	// 候补状态
	return u.FeeDetail.NoticeTitleNotp
}

func (u *UnifiedPriceRender) getNoticeSubtitle(ctx context.Context) string {
	preMatchInfo := u.adapter.GetSmartBusPreMatch()
	if preMatchInfo == nil || preMatchInfo.EtpInfo == nil {
		return ""
	}

	if from_type.FromTypeFromTypeScanInCar == u.adapter.BaseReqData.CommonInfo.FromType {
		return u.FeeDetail.NoticeSubtitleScanInCar
	}

	// 非候补
	if PreMatchTypePreMatch == u.preMatchType {
		return u.FeeDetail.NoticeSubtitle
	}

	//候补
	return u.FeeDetail.NoticeSubtitleNotp
}

func (u *UnifiedPriceRender) getPriceInfoDescList(ctx context.Context) []*proto.NewFormFeeDesc {
	return smart_bus.GetPriceInfoDescList(ctx, u.adapter)
}

func (u *UnifiedPriceRender) getNoticeText(ctx context.Context) *string {
	if PreMatchTypePreMatch == u.preMatchType {
		return proto.StrPtr(cast.ToString(u.startStationInfo["displayname"]))
	}
	return proto.StrPtr(u.FeeDetail.NoticeTextNotp)
}

func (u *UnifiedPriceRender) getNoticeSubtext(ctx context.Context) *string {
	walkDist := u.adapter.GetWalkDist()
	// 判空操作
	if walkDist == nil || u.FeeDetail.WalkDistLeftThreshold == 0 || u.FeeDetail.WalkDistRightThreshold == 0 {
		return proto.StrPtr("")
	}
	if *walkDist < u.FeeDetail.WalkDistLeftThreshold || *walkDist >= u.FeeDetail.WalkDistRightThreshold {
		return proto.StrPtr("")
	}
	return proto.StrPtr(dcmp.TranslateTemplate(u.FeeDetail.NoticeSubtext, map[string]string{
		"walk_dist": cast.ToString(walkDist),
	}))
}

func (u *UnifiedPriceRender) getConfirmSubtitle(ctx context.Context) *string {
	preMatchInfo := u.adapter.GetSmartBusPreMatch()
	if preMatchInfo == nil || preMatchInfo.EtdInfo == nil {
		return proto.StrPtr("")
	}
	if PreMatchTypePreMatch == u.preMatchType {
		return proto.StrPtr(dcmp.TranslateTemplate(u.FeeDetail.ConfirmSubtitle, map[string]string{
			"amount1": formatTime(preMatchInfo.EtdInfo.EtdLeftMargin),
			"amount2": formatTime(preMatchInfo.EtdInfo.EtdRightMargin),
		}))
	}
	return proto.StrPtr("")
}

func (u *UnifiedPriceRender) buildStartStationInfo(_ context.Context) (startStationInfo *proto.SmartBusStationInfo) {
	//车上扫码场景
	if from_type.FromTypeFromTypeScanInCar == u.adapter.BaseReqData.CommonInfo.FromType {
		return map_station.ScanInCarWithTPStartStation(u.adapter.ProductInfoFull, u.adapter.BaseReqData)
	}

	//default strategy
	if PreMatchTypeNoCar == u.preMatchType || nil == u.startStationInfo {
		return
	}
	startStationInfo = &proto.SmartBusStationInfo{
		PoiId:       cast.ToString(u.startStationInfo["poi_id"]),
		Lat:         cast.ToFloat64(u.startStationInfo["lat"]),
		Lng:         cast.ToFloat64(u.startStationInfo["lng"]),
		DisplayName: cast.ToString(u.startStationInfo["displayname"]),
	}
	return startStationInfo
}

func (u *UnifiedPriceRender) buildSideParams(_ context.Context) (sideParams *proto.SideParam) {
	walkDist := u.adapter.GetWalkDist()
	return &proto.SideParam{
		WalkDist:     walkDist,
		PreMatchType: &u.preMatchType,
	}
}

func (u *UnifiedPriceRender) ExtendDataRender(ctx context.Context, resp *proto.MiniBusEstimateData, baseReqData *models.BaseReqData, products []*biz_runtime.ProductInfoFull) {
	companyName, estimateTitle, seatRes := getCompanyNameAndSeat(ctx, baseReqData, products, u.SeparatePage.DefaultCompanyName, u.SeparatePage.Title)
	// 本次小巴需要画曲线，直接赋值1，最好加一个枚举
	resp.MapCurveInfo = &proto.MapCurveInfo{
		IsDrawCurve: 1,
		CurveType:   proto.Int32Ptr(2),
	}
	//地图视图类型
	BuildBestViewType(resp, baseReqData)

	//地图视图切换按钮
	BuildBestViewTypeSwitch(resp, baseReqData)

	resp.CarpoolSeatModule = seatRes
	resp.PageType = int16(baseReqData.CommonInfo.PageType)

	//发单按钮构建
	BuildNewOrderButton(resp, baseReqData, &u.SeparatePage.BaseExtraInfo, estimateTitle)

	if baseReqData.CommonInfo.FromType == ShowBackButton {
		resp.BackButton = &proto.BackButton{
			LeftIcon: u.SeparatePage.BackIcon,
			JumpTo:   u.SeparatePage.JumpLink,
		}
	}

	resp.DynamicEffectParams = &proto.DynamicEffectParams{}
	if len(resp.EstimateData) > 0 {
		resp.Subtitle = fmt.Sprintf(u.SeparatePage.Subtitle, companyName)
		resp.IsSupportMultiSelection = 0
		resp.FeeDetailUrl = u.SeparatePage.FeeDetailUrl
		if len(resp.EstimateData) == 1 && resp.EstimateData[0] != nil && resp.EstimateData[0].ExtraMap != nil && resp.EstimateData[0].ExtraMap.Etp == -1 {
			resp.DynamicEffectParams.MarkIcon = u.SeparatePage.MarkIconNotp
		} else {
			resp.DynamicEffectParams.MarkIcon = u.SeparatePage.MarkIcon
		}
		defaultSelected(resp)
		resp.DynamicEffectParams.SelectBgColor = &u.SeparatePage.SelectBgColor
		resp.PreMatchType = &u.preMatchType
	}

	if len(resp.EstimateData) == ShowEmptyImage {
		buildRespWithNoTP(resp, baseReqData, &u.SeparatePage.BaseExtraInfo)
	}

	//构建地图站牌：for 无TP
	FillMapStationWithoutTP(resp, baseReqData)

	//地图token
	BuildMapToken(resp, baseReqData)

	resp.OmegaInfo = buildOmegaData(ctx, baseReqData, resp)
	resp.PneworderParams = &proto.SmartBusNewOrderParam{Dchn: &baseReqData.CommonInfo.Dchn}
	resp.SmartBusPageVersion = proto.StrPtr(SmartBusPageVersionUnifiedPrice)
}
