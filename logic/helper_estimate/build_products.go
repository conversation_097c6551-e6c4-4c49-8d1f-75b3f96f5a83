package helper_estimate

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	LegoContext "git.xiaojukeji.com/lego/context-go"
)

func InitProductGenerator(ctx context.Context, req *proto.MultiEstimatePriceRequest) (
	*biz_runtime.ProductsGenerator, int) {
	var (
		err         error
		productsGen *biz_runtime.ProductsGenerator
	)

	productsGen, err = biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithCommonReq(ctx, req),
		biz_runtime.WithDecodeSeatDetailInfo(ctx, req.SeatDetailInfo),
	)
	if err != nil {
		log.Trace.Warnf(ctx, LegoContext.DLTagUndefined, "GeneratorByCommonReq err: %v", err)
		return nil, consts.ErrnoSystemError
	}

	productsGen.SetSendReqKafka(false)

	RegisterRpcProcessWithBasicProducts(ctx, productsGen)
	//RegisterProductFilter(ctx, productsGen) 暂时不需要过滤逻辑
	return productsGen, consts.NoErr
}

func GenProducts(ctx context.Context, generator *biz_runtime.ProductsGenerator) []*biz_runtime.ProductInfoFull {
	var (
		err      error
		products []*biz_runtime.ProductInfoFull
	)
	if generator == nil {
		return nil
	}
	products, err = generator.GenProducts(ctx)
	if err != nil {
		return nil
	}
	return products
}
