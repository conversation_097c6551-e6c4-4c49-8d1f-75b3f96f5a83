package helper_estimate

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/objective_remain_num"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/seat_detail_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/support_select_seat"
	"git.xiaojukeji.com/gulfstream/mamba/view/builder"
	LegoContext "git.xiaojukeji.com/lego/context-go"
)

// BuildResponse 渲染预估返回数据
func BuildResponse(ctx context.Context, products []*biz_runtime.ProductInfoFull) (
	resp *proto.HelperEstimateResData, errno int) {

	estimateDataList, errno := buildEstimateDataList(ctx, products)
	if consts.NoErr != errno {
		return nil, errno
	}

	resp = &proto.HelperEstimateResData{
		EstimateTraceId: LegoContext.GetTrace(ctx).GetTraceId(),
		EstimateData:    estimateDataList,
	}
	return resp, consts.NoErr
}

// buildEstimateDataList 构建预估返回数据
func buildEstimateDataList(ctx context.Context, products []*biz_runtime.ProductInfoFull) (
	estimateDataList []*proto.HelperEstimateData, errno int) {
	for _, productData := range products {
		if productData.BaseReqData.CommonBizInfo.IsRestSeatInfo {
			return nil, consts.ErrnoSeatReset
		}

		// 初始化预估数据
		estimateData := initEstimateData(productData)
		if estimateData == nil {
			continue
		}
		helperConfig, err := builder.NewHelperBuilder()
		if err != nil {
			log.Trace.Warnf(ctx, LegoContext.DLTagUndefined, "get builder fail: %v", err)
			continue
		}

		// 根据json配置渲染字段
		err = helperConfig.Build(ctx, productData)
		if err != nil {
			log.Trace.Warnf(ctx, LegoContext.DLTagUndefined, "build fail: %v", err)
			continue
		}
		estimateData.CarpoolSeatData = helperConfig.GetCarpoolSeatData()
		estimateData.MatchRoutesData = helperConfig.GetMatchRoutesData()
		estimateData.SupportSelectSeat = support_select_seat.GetSupportSelectSeat(ctx, productData)
		estimateData.SeatDetailInfo = seat_detail_info.NewSeatDetailInfo(productData).GetSeatDetailInfo(ctx)
		estimateData.ObjectiveRemainNum = objective_remain_num.NewObjectiveRemainNum(productData, helperConfig.GetCarpoolSeatData()).GetObjectiveRemainNum(ctx)

		estimateDataList = append(estimateDataList, estimateData)
	}

	return estimateDataList, consts.NoErr
}

func initEstimateData(pFull *biz_runtime.ProductInfoFull) *proto.HelperEstimateData {
	if pFull == nil || pFull.Product == nil || pFull.BillDetail == nil || len(pFull.DiscountInfo) == 0 {
		return nil
	}

	data := &proto.HelperEstimateData{}
	data.EstimateId = pFull.Product.EstimateID
	data.BusinessId = pFull.Product.BusinessID
	data.RequireLevel = pFull.Product.RequireLevelInt
	data.ComboType = pFull.Product.ComboType
	data.LevelType = int64(pFull.Product.LevelType)
	data.ProductCategory = pFull.Product.ProductCategory
	data.ProductId = pFull.Product.ProductID
	data.FeeAmount = pFull.DiscountInfo[0].EstimateFee
	data.ComboId = pFull.Product.BizInfo.ComboID

	if pFull.Product.BizInfo != nil {
		data.RouteType = pFull.Product.BizInfo.RouteType
		if pFull.Product.BizInfo.NeedVerified == true {
			data.NeedAuth = 1
		}
	}

	return data
}
