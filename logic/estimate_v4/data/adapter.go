package data

import (
	"context"
	hundunClient "git.xiaojukeji.com/dirpc/dirpc-go-http-Hundun"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"strconv"

	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/hestia_charge"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type EstimateV4Adapter struct {
	*biz_runtime.ProductInfoFull
}

func (a *EstimateV4Adapter) GetAthenaRecommend() int32 {
	if a.GetProductCheckStatus() == consts.Checked {
		return consts.Checked
	}
	return consts.UnChecked
}

//func (a *EstimateV4Adapter) GetSelection() int32 {
//	// athena推荐
//	if a.GetProductCheckStatus() == consts.Checked {
//		return consts.Checked
//	}
//
//	var productInfoRaw string
//	productInfos := make(map[string]int)
//
//	luxuryConfig := apollo.GetConfig(nil, "luxury_carlevel_product_config", "luxury_carlevel_product_config")
//	if luxuryConfig != nil {
//		productInfoRaw = luxuryConfig["product_category"]
//		_ = json.Unmarshal([]byte(productInfoRaw), &productInfos)
//	}
//
//	// 用户勾选
//	multiRequireProduct := a.GetMultiRequireProduct()
//	if len(a.GetMultiRequireProduct()) > 0 {
//		for _, product := range multiRequireProduct {
//			if a.GetProductCategory() == product.ProductCategory && product.IsSelected == consts.Checked {
//				return consts.Checked
//			}
//
//			// 豪华车勾选继承
//			productIdNow, isLuxNow := productInfos[strconv.FormatInt(a.GetProductCategory(), 10)]
//			productIdBefore, isLuxBefore := productInfos[strconv.FormatInt(product.ProductCategory, 10)]
//			if isLuxNow && isLuxBefore && productIdBefore == productIdNow && product.IsSelected == 1 {
//				return consts.Checked
//			}
//
//		}
//	}
//
//	return consts.UnChecked
//}

func (a *EstimateV4Adapter) GetCarpoolSeatNum() int32 {
	if a.BaseReqData == nil {
		return 0
	}
	return a.BaseReqData.CommonBizInfo.CarpoolSeatNum
}

func (a *EstimateV4Adapter) GetMapInfoCacheToken() string {
	if a.GetBizInfo().MiniBusPreMatch != nil && a.GetBizInfo().MiniBusPreMatch.MapinfoCacheToken != nil {
		return *a.GetBizInfo().MiniBusPreMatch.MapinfoCacheToken
	}

	return ""
}

func (a *EstimateV4Adapter) GetMapinfoStartCacheToken() string {
	if a.GetBizInfo().MiniBusPreMatch != nil && a.GetBizInfo().MiniBusPreMatch.MapinfoStartCacheToken != nil {
		return *a.GetBizInfo().MiniBusPreMatch.MapinfoStartCacheToken
	}

	return ""
}

func (a *EstimateV4Adapter) GetMapinfoDestCacheToken() string {
	if a.GetBizInfo().MiniBusPreMatch != nil && a.GetBizInfo().MiniBusPreMatch.MapinfoDestCacheToken != nil {
		return *a.GetBizInfo().MiniBusPreMatch.MapinfoDestCacheToken
	}

	return ""
}

func (a *EstimateV4Adapter) ShowMiniBusStation() bool {
	return true
}

func (a *EstimateV4Adapter) IsOnlyMinibus() bool {
	return a.BaseReqData.CommonBizInfo.MiniBusData.BOnlyMinibus
}

func (a *EstimateV4Adapter) GetEtpTimeDuration() int64 {
	if a.GetBizInfo().MiniBusPreMatch != nil && a.GetBizInfo().MiniBusPreMatch.EtpInfo != nil {
		return a.GetBizInfo().MiniBusPreMatch.EtpInfo.EtpTimeDuration
	}

	return 1
}

func (a *EstimateV4Adapter) GetExtMap() map[string]string {
	if a.GetBizInfo().MiniBusPreMatch != nil && a.GetBizInfo().MiniBusPreMatch.ExtMap != nil {
		return a.GetBizInfo().MiniBusPreMatch.ExtMap
	}

	return nil
}

func (a *EstimateV4Adapter) GetMiniBusDistLimit() int64 {
	return 300
}

func (a *EstimateV4Adapter) GetPassengerCountOption() []int32 {
	if a.BaseReqData.CommonBizInfo.SeatNumInfo != nil {
		return a.BaseReqData.CommonBizInfo.SeatNumInfo.PassengerCountOption
	}
	return nil
}

func (a *EstimateV4Adapter) GetCarpoolFailExactEstimateFee() float64 {
	if len(a.GetExtendList()) == 1 {
		return a.GetExtendList()[0].ExactEstimateFee
	}

	return 0
}

func (a *EstimateV4Adapter) GetComboType() int64 {
	return a.Product.ComboType
}

func (a *EstimateV4Adapter) GetSceneType() int64 {
	return a.Product.SceneType
}

func (a *EstimateV4Adapter) GetVcard() *PriceApi.EstimateNewFormVCardInfo {
	return a.GetVCard()
}

func (a *EstimateV4Adapter) GetBaseReqData() *models.BaseReqData {
	return a.BaseReqData
}

func (a *EstimateV4Adapter) GetPassengerId() string {
	return strconv.FormatInt(a.BaseReqData.PassengerInfo.PID, 10)
}

func (a *EstimateV4Adapter) GetDesignatedDriver() string {
	// 新版本 从multi require取
	pid, params := a.GetApolloParams(biz_runtime.WithPIDKey)
	if apollo.FeatureToggle(nil, "six_seat_lux_toggle", pid, params) {
		multiRequireProduct := a.GetMultiRequireProduct()
		if len(a.GetMultiRequireProduct()) > 0 {
			for _, product := range multiRequireProduct {
				if a.GetProductCategory() == product.ProductCategory {
					return product.LuxurySelectDriver
				}
			}
		}
	}

	// 老版本 从入参中取
	if a.BaseReqData.RawUserSelectOption != nil {
		return a.BaseReqData.RawUserSelectOption.LuxurySelectDriver
	}

	return ""
}

func (a *EstimateV4Adapter) GetRequireLevel() string {
	return a.Product.RequireLevel
}

func (a *EstimateV4Adapter) GetEstimateId() string {
	return a.Product.EstimateID
}

func (a *EstimateV4Adapter) GetTaxiSpsData() *hestia_charge.TaxiSpsData {
	return a.GetBizInfo().TaxiSps
}

func (a *EstimateV4Adapter) GetDepartureTime() int64 {
	return a.BaseReqData.CommonInfo.DepartureTime
}

func (a *EstimateV4Adapter) GetBillTaxiPeakFee() float64 {
	var res float64
	if a.GetBillDetail() != nil && a.GetBillDetail().FeeDetailInfo != nil {
		feeDetailMap := a.GetBillDetail().FeeDetailInfo
		if peakFee, ok := feeDetailMap["taxi_peak_price"]; ok {
			res += peakFee
		}

		if discountFee, ok := feeDetailMap["taxi_peak_discount"]; ok {
			res += discountFee
		}

	}

	return res
}

func (a *EstimateV4Adapter) GetIntelTaxiPeakFee() int64 {
	if a.GetBillDisplayLines() == nil {
		return 0
	}
	for _, displayLine := range a.GetDisplayLines() {
		if "taxi_peak_price" != displayLine.Name || displayLine.ExtraInfo == nil {
			continue
		}
		peakPrice, ok := displayLine.ExtraInfo["show_taxi_peak_price"]
		if ok {
			return cast.ToInt64(peakPrice)
		}
	}

	return 0
}

func (a *EstimateV4Adapter) IsSwitchTaxiPeakIntel() bool {
	return a.GetBizInfo().IsSwitchIntel
}

func (a *EstimateV4Adapter) IsHoliday() bool {
	return a.GetBizInfo().IsHoliday
}

func (a *EstimateV4Adapter) GetServiceList() []*hundunClient.PcServiceData {
	return a.GetBizInfo().CustomFeatureList
}

func (a *EstimateV4Adapter) GetTabId() string {
	return a.BaseReqData.CommonInfo.TabId
}

func (a *EstimateV4Adapter) GetRecResultExtraInfo() map[string]string {
	return a.GetCommonBizInfo().RecommendExtraInfoMap[int32(a.GetProductCategory())]
}

func (a *EstimateV4Adapter) GetCarpoolVCard(ctx context.Context) (string, int32) {
	if !a.HitCarpoolVCardToggle(ctx) {
		return "", -1
	}

	if a.GetVCard() != nil {
		return a.GetVCard().Source, a.GetVCard().PayStatus
	}

	return "", -1
}

func (a *EstimateV4Adapter) HitCarpoolVCardToggle(ctx context.Context) bool {
	params := map[string]string{
		"city":          util.ToString(a.GetCityID()),
		"pid":           util.ToString(a.GetUserPID()),
		"app_version":   a.GetAppVersion(),
		"access_key_id": util.ToString(a.GetAccessKeyId()),
		"menu_id":       a.GetMenuId(),
		"page_type":     "0",
		"is_anycar_est": "0",
	}

	return apollo.FeatureToggle(ctx, "gs_carpool_commute_card_open", params["pid"], params)
}

// func (a *EstimateV4Adapter) IsCarpoolUnSuccessFlatPriceShowAsCapPrice(ctx context.Context) bool {
//	if !carpool.IsCarpoolDualPriceFull(a) {
//		return false
//	}
//
//	if a.GetExtendList() == nil {
//		return false
//	}
//
//	if a.IsHaveCarpoolVCard(ctx) {
//		return true
//	}
//
//	isHit, params := apollo.GetParameters("gs_carpool_v3_price_msg", "", map[string]string{
//		"city":          util.ToString(a.GetCityID()),
//		"phone":         a.GetUserPhone(),
//		"access_key_id": util.ToString(a.GetAccessKeyId()),
//		"app_version":   a.GetAppVersion(),
//		"pid":           util.ToString(a.GetUserPID()),
//	})
//	if !isHit {
//		return false
//	}
//
//	var (
//		carpoolSuccessFee float64
//		carpoolFailFee    float64
//		precision         int
//	)
//	carpoolSuccessFee = a.GetEstimateFee()
//	if carpoolFailFeeTmp, ok := a.GetCarpoolFailEstimateFee(); ok {
//		carpoolFailFee = carpoolFailFeeTmp
//	}
//
//	if carpoolSuccessFee <= 0 || carpoolFailFee <= 0 {
//		return false
//	}
//
//	precisionStr, ok := params["precision"]
//	if ok {
//		precision = util.ToInt(precisionStr)
//	} else {
//		precision = 1
//	}
//
//	return util.RoundAbs(carpoolSuccessFee, precision) == util.RoundAbs(carpoolFailFee, precision)
// }

func (a *EstimateV4Adapter) GetETInfoByPcId(pcId int32) *AthenaApiv3.EstimatedTimeInfo {
	if len(a.GetCommonBizInfo().ETInfoMap) > 0 {
		return a.GetCommonBizInfo().ETInfoMap[pcId]
	}
	return nil
}

// GetMixedDeductPrice 返回企业付 抵扣金额
func (a *EstimateV4Adapter) GetMixedDeductPrice() float64 {
	var deductInfo float64

	if !a.IsBusinessPay() {
		return 0.0
	}

	deductInfo = a.GetEstimateFee()

	if payInfo := a.GetPaymentInfo(); payInfo != nil && payInfo.MixedPayDeductInfo != nil && payInfo.MixedPayDeductInfo.DeductFee > 0 {
		deductInfo = payInfo.MixedPayDeductInfo.DeductFee
	}

	return deductInfo
}

func (a *EstimateV4Adapter) GetBonus() float64 {
	fee := a.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}
	amount := fee.GetFeeDetail().GetBonusAmount()
	if amount == nil {
		return 0
	}
	return *amount
}

func (a *EstimateV4Adapter) GetFastEstimatePrice() float64 {
	return a.BaseReqData.CommonBizInfo.FastCarPrice
}

func (a *EstimateV4Adapter) GetEstimateStyleType() int32 {
	return a.BaseReqData.CommonInfo.EstimateStyleType
}

func (a *EstimateV4Adapter) GetCityId() int {
	return a.GetCityID()
}

func (a *EstimateV4Adapter) GetAthenaTopRecRes() *AthenaApiv3.TopRecRes {
	return a.GetCommonBizInfo().TopRec
}

func (a *EstimateV4Adapter) IsHitDynamicPrice(ctx context.Context) bool {
	isFastNormalCar := util.InArrayInt64(a.GetProductCategory(), []int64{
		estimate_pc_id.EstimatePcIdFastCar,
		estimate_pc_id.EstimatePcIdFastSpecialRate,
		estimate_pc_id.EstimatePcIdSpecialRate,
		estimate_pc_id.EstimatePcIdAplus,
		estimate_pc_id.EstimatePcIdSpaciousCar,
		estimate_pc_id.EstimatePcIdEstimatePcIdTimeLimitSpecialRate,
	})
	return a.GetDynamicDiffPrice() > 0 && (!isFastNormalCar) && (!a.IsTripcloud(ctx))
}
