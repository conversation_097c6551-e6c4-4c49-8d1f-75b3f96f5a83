package estimate_v4

import (
	"context"
	"encoding/json"
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/logutil"
	"strconv"
	"strings"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

const OperaKeyEstimateData = "g_order_estimate_price"
const OperaKeyLayoutData = "g_order_estimate_data_layout"

type ProductShow struct {
	selectType    int
	formShowType  int
	recommendType int
}

func AddPublicLog(ctx context.Context, products []*biz_runtime.ProductInfoFull, rspData *proto.V4Data) {
	// 压测流量不写Public日志
	if util.IsPressureTraffic(ctx) {
		return
	}

	productMap := make(map[string]*biz_runtime.ProductInfoFull, len(products))
	for _, product := range products {
		if product == nil || "" == product.Product.EstimateID {
			continue
		}
		writeProductLog(ctx, product, rspData)
		productMap[strconv.FormatInt(product.GetProductCategory(), 10)] = product
	}

	for curr, layout := range rspData.GetLayout() {
		if layout == nil || layout.Groups == nil || len(layout.Groups) < 1 {
			continue
		}
		writeLayoutLog(ctx, layout, curr+1, productMap)
	}
}

func writeProductLog(ctx context.Context, full *biz_runtime.ProductInfoFull, rspData *proto.V4Data) {
	if full == nil || full.BaseReqData == nil || full.Product == nil ||
		full.GetBillInfo() == nil {
		return
	}
	logInfo := logutil.GenerateCommonProductMap(ctx, full)
	product := full.Product

	// 价格标签曝光埋点
	if rspData != nil && len(rspData.EstimateData) > 0 && rspData.EstimateData[product.ProductCategory] != nil {
		logInfo["select_type"] = rspData.EstimateData[product.ProductCategory].IsSelected
		logInfo["is_support_multi_selection"] = rspData.IsSupportMultiSelection
	}

	if rspData != nil && len(rspData.EstimateData) > 0 && rspData.EstimateData[product.ProductCategory] != nil && len(rspData.EstimateData[product.ProductCategory].FeeDescList) > 0 {
		priceInfoDesc, err := json.Marshal(rspData.EstimateData[product.ProductCategory].FeeDescList)
		if err == nil {
			logInfo["price_info_desc"] = string(priceInfoDesc)
		}
	}
	if estimateFee, ok := full.GetCarpoolFailEstimateFee(); product.IsDualCarpoolPrice && ok {
		logInfo["carpool_fail_discount_fee"] = estimateFee
		if full.GetCarpoolFailRawBill() != nil {
			logInfo["carpool_fail_price"] = full.GetCarpoolFailRawBill().DynamicTotalFee
		}
	}
	if full.GetMiniBusPreMatch() != nil && full.GetMiniBusPreMatch().ExtMap != nil {
		if longTripId, ok := full.GetMiniBusPreMatch().ExtMap["long_trip_id"]; ok {
			logInfo["long_trip_id"] = longTripId
		}
	}

	log.Public.Public(ctx, OperaKeyEstimateData, logInfo)
}

func writeLayoutLog(ctx context.Context, layout *proto.NewFormLayout, curr int, productMap map[string]*biz_runtime.ProductInfoFull) {
	if layout == nil || len(layout.GetGroups()) < 1 {
		return
	}
	param := layout.GetGroups()[0]
	products := param.GetProducts()
	logInfo := make(map[string]interface{})
	logInfo["opera_stat_key"] = "g_order_estimate_data_layout"
	logInfo["sort"] = curr
	logInfo["form_item_type"] = param.GetType()
	logInfo["products"] = getProductsEID(param.GetProducts(), productMap)
	logInfo["form_show_type"] = layout.GetFormShowType()
	logInfo["box_id"] = param.GetBoxId()
	logInfo["sub_group_id"] = productMap[products[0]].GetSubGroupId()
	logInfo["link_product"] = 0
	logInfo["box_price_info"] = param.GetFeeDesc()
	logInfo["group_id"] = param.GetGroupId()
	logInfo["is_vx_form"] = 4
	// logInfo["is_first_screen"] = 0  //是否在首屏
	// logInfo["action_type"] = 0      //盒子点击跳转类型
	// logInfo["style_type"] = 0       //是否高峰期盒子
	// logInfo["dialog_id"] = ""       // form athena
	// logInfo["theme_style"] = ""     // from athena
	// logInfo["category_id"] = ""     // 三方表单分类
	log.Public.Public(ctx, OperaKeyLayoutData, logInfo)
}

func getProductsEID(pcids []string, productMap map[string]*biz_runtime.ProductInfoFull) string {
	var res []string
	for _, pcid := range pcids {
		if productMap[pcid] != nil {
			res = append(res, productMap[pcid].GetEstimateID())
		}
	}
	return strings.Join(res, ",")
}

func getCouponInfoStr(channel int64, couponInfo *PriceApi.EstimateNewFormCouponInfo) string {
	type CouponItem struct {
		Channel int64  `json:"channel"`
		BatchID string `json:"batchid"`
		Amount  string `json:"amount"`
	}

	type CouponInfoRes struct {
		DefaultCoupon  *CouponItem `json:"default_coupon"`
		ActivityCoupon *CouponItem `json:"activity_coupon"`
	}

	if couponInfo == nil {
		return ""
	}

	item := &CouponItem{
		Channel: channel,
		BatchID: couponInfo.BatchId,
		Amount:  couponInfo.Amount,
	}
	res := &CouponInfoRes{}

	if couponInfo.CouponSource == "coupon" {
		res.DefaultCoupon = item
	} else {
		res.ActivityCoupon = item
	}

	resStr, err := json.Marshal(res)
	if err == nil {
		return string(resStr)
	} else {
		return ""
	}
}

func buildFeeDetailInfo(feeDetailInfo map[string]float64) string {
	type FeeDetailInfoRes struct {
		Key    string `json:"key"`
		Amount int64  `json:"amount"`
	}

	var (
		feeDetailList = make([]FeeDetailInfoRes, 0)
		feeDetailRes  FeeDetailInfoRes
	)

	if feeDetailInfo == nil || len(feeDetailInfo) == 0 {
		return ""
	}

	for feeName, feeAmount := range feeDetailInfo {
		feeDetailRes.Key = feeName
		feeDetailRes.Amount = int64(feeAmount * 100)
		feeDetailList = append(feeDetailList, feeDetailRes)
	}

	resStr, err := json.Marshal(feeDetailList)
	if err == nil {
		return string(resStr)
	} else {
		return ""
	}
}
