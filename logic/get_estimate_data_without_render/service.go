package get_estimate_data_without_render

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"time"
)

func EstimateWithoutRender(ctx context.Context, req *proto.MultiEstimatePriceRequest) (rspData map[string]*proto.PGetEstimateDataWithoutRenderEstimateData, errno int) {
	var (
		products []*biz_runtime.ProductInfoFull
	)

	productsGenerator, errno := InitProductGenerator(ctx, req)
	if consts.NoErr != errno {
		return nil, errno
	}

	products, err := productsGenerator.GenProducts(ctx)
	if len(products) == 0 || err != nil {
		return nil, consts.ErrnoNoProductOpen
	}

	rspData = filterResponse(ctx, req.GetField(), products)

	return rspData, consts.NoErr
}

func EstimateWithoutRenderV2(ctx context.Context, req *proto.PGetEstimateDataWithoutRenderReq) (rspData map[string]*proto.PGetEstimateDataWithoutRenderEstimateData, errno int) {
	var (
		products []*biz_runtime.ProductInfoFull
	)

	productsGenerator, err := InitProductGeneratorByUid(ctx, req)
	if err != nil {
		return nil, consts.ErrnoParams
	}

	products, err = productsGenerator.GenProducts(ctx)
	if len(products) == 0 || err != nil {
		return nil, consts.ErrnoNoProductOpen
	}

	rspData = filterResponse(ctx, req.GetField(), products)

	return rspData, consts.NoErr
}

func InitProductGeneratorByUid(ctx context.Context, req *proto.PGetEstimateDataWithoutRenderReq) (*biz_runtime.ProductsGenerator, error) {
	var (
		err         error
		productsGen *biz_runtime.ProductsGenerator
	)

	// 构建入参
	productsGen, err = biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("pGetEstimateDataWithoutRender"),
		BuildParams(ctx, req),
	)
	if err != nil {
		return nil, err
	}

	productsGen.SetSendReqKafka(true)
	productsGen.SetNeedCarAggregation(true)
	productsGen.SetNeedMember(true)
	return productsGen, nil
}

func BuildParams(ctx context.Context, request *proto.PGetEstimateDataWithoutRenderReq) biz_runtime.GeneratorOption {
	return func(pg *biz_runtime.ProductsGenerator) error {
		var departureTime int64
		if request.GetOrderType() == 0 {
			departureTime = time.Now().Unix()
		} else {
			departureTime = util.ToInt64(request.GetDepartureTime())
		}

		builder, err := models.NewBaseReqDataBuilder().
			SetClientInfo(&models.ClientInfo{
				SourceID:    request.GetSourceId(),
				MenuID:      consts.MenuIDDaCheAnyCar,
				PageType:    page_type.PageTypeUndefined,
				OriginID:    1, // 滴滴
				Lang:        request.Lang,
				AccessKeyID: request.AccessKeyId,
				AppVersion:  request.AppVersion,
			}).
			SetPassengerInfoV2(&models.PassengerInfoV2{
				UID:      request.Uid,
				PID:      request.Pid,
				Phone:    request.Phone,
				UserType: request.GetUserType(),
			}).
			SetUserOption(&models.UserOption{
				DepartureTime: departureTime,
			}).
			SetGEOInfo(&models.GEOInfo{
				CurrLat:           request.Lat,
				CurrLng:           request.Lng,
				FromLat:           request.FromLat,
				FromLng:           request.FromLng,
				FromPOIID:         request.FromPoiId,
				FromPOIType:       request.FromPoiType,
				FromAddress:       request.FromAddress,
				FromName:          request.FromName,
				ToLat:             request.ToLat,
				ToLng:             request.ToLng,
				ToPOIID:           request.ToPoiId,
				ToPOIType:         request.ToPoiType,
				ToAddress:         request.ToAddress,
				ToName:            request.ToName,
				StopoverPointInfo: request.GetStopoverPoints(),
			}).TryBuildWithStopover(ctx)
		if err != nil {
			return err
		}

		pg.BaseReqData = builder

		pg.IsBaseReqInit = true
		return nil
	}
}
