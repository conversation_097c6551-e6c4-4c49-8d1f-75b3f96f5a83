package get_estimate_data_without_render

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	trace "git.xiaojukeji.com/lego/context-go"
	"github.com/spf13/cast"
)

const (
	apolloKey = "get_estimate_data_without_render_caller"
)

func InitProductGenerator(ctx context.Context, req *proto.MultiEstimatePriceRequest) (*biz_runtime.ProductsGenerator, int) {
	var (
		errno int
		err   error
		// baseReqData *biz_runtime.BaseReqData
		productsGen *biz_runtime.ProductsGenerator
	)

	errno = initParams(ctx, req)
	if consts.NoErr != errno {
		return nil, errno
	}

	productsGen, err = biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("pGetEstimateDataWithoutRender"),
		biz_runtime.WithCommonReq(ctx, req),
	)

	if productsGen == nil || err != nil {
		return nil, consts.GetErrorNum(err)
	}

	if productsGen.BaseReqData != nil {
		// 用soruce_id过滤品类
		productsGen.BaseReqData.CommonBizInfo.SourceId = req.GetSourceId()
	}

	// 处理默勾,临时方案
	productsGen.SetNeedMember(true)
	productsGen.SetSendReqKafka(true)
	productsGen.SetNeedCarAggregation(true)

	if err != nil {
		return nil, consts.GetErrorNum(err)
	}

	// 没有实现rpc和过滤器注册
	return productsGen, consts.NoErr
}

func initParams(ctx context.Context, req *proto.MultiEstimatePriceRequest) int {
	if req == nil || "" == req.GetCaller() || "" == req.Token || 0 == len(req.GetField()) {
		return consts.ErrnoParams
	}

	// 请求 passport
	userInfo, err := passport.GetUserInfo(ctx, req.Token, "")
	if err != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "getUserInfo: %v", err)
		return consts.ErrnoGetUserInfo
	}

	// 校验caller是否有效
	key := cast.ToString(userInfo.UID)
	apolloParam := map[string]string{
		"uid":           cast.ToString(userInfo.UID),
		"pid":           cast.ToString(userInfo.PID),
		"app_version":   req.GetAppVersion(),
		"access_key_id": cast.ToString(req.GetAccessKeyId()),
		"phone":         userInfo.Phone,
		"caller":        req.GetCaller(),
	}

	if !apollo.FeatureToggle(ctx, apolloKey, key, apolloParam) {
		return consts.ErrnoParams
	}

	return consts.NoErr
}

func filterResponse(ctx context.Context, fields []string, products []*biz_runtime.ProductInfoFull) (resp map[string]*proto.PGetEstimateDataWithoutRenderEstimateData) {

	// 后续可以直接添加 增加复用性
	var funcMap = map[string]filterHandler{
		"product_category":    GetProductCategory,
		"discount_set-coupon": GetCoupon,
		"estimate_fee":        GetEstimateFee,
		"dynamic_total_fee":   GetDynamicTotalFee,
		"fee_detail_info":     GetFeeDetailInfo,
		"driver_price":        GetDriverPrice,
	}

	resp = make(map[string]*proto.PGetEstimateDataWithoutRenderEstimateData, len(products))

	for _, product := range products {
		productEstimate := &proto.PGetEstimateDataWithoutRenderEstimateData{}
		if product.Product == nil {
			continue
		}

		// 用eid作为标识的key
		eid := product.Product.EstimateID
		for _, key := range fields {
			handler, ok := funcMap[key]
			if !ok {
				continue
			}

			handler(ctx, product, productEstimate)
		}

		resp[eid] = productEstimate
	}

	return
}
