package multi_estimate_by_order

import (
	"context"
	"errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/public_log"

	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/multi_estimate_by_order/internal"
	"git.xiaojukeji.com/gulfstream/mamba/logic/multi_estimate_by_order/model"
)

func EstimateByOrder(ctx context.Context, req *proto.PEstimateByOrderRequest) (*proto.PEstimateByOrderData, NewErrors.BizError, public_log.WritePublicFunc) {
	serviceReq, err := buildServiceReq(ctx, req)
	if err != nil {
		log.Trace.Warnf(ctx, "multi_estimate_by_order", "err=%v", err)
		return nil, err, nil
	}

	dispatchList := []func() internal.Dispatcher{
		internal.NewByCacheDispatch,
		internal.NewFavorableDispatch,
		internal.NewCarpoolDispatch,
		internal.NewThirtyProductDispatch,
		internal.NewNormalDispatch,
	}

	var targetInstance internal.Dispatcher

	for _, instanceFunc := range dispatchList {
		instance := instanceFunc()
		if instance == nil {
			continue
		}

		if instance.Selected(ctx, serviceReq) {
			targetInstance = instance
			break
		}
	}

	if targetInstance == nil {
		return nil, NewErrors.NewBizError(errors.New("no target instance is fail"), NewErrors.ErrnoSystemError), nil
	}

	products, err := targetInstance.RunTimeBuild(ctx, serviceReq)
	if err != nil {
		// log.Trace.Warnf(ctx, "multi_estimate_by_order", "err=%v", err)
		return nil, err, nil
	}

	resp, err := targetInstance.Render(ctx, serviceReq, products)
	if err != nil {
		log.Trace.Warnf(ctx, "multi_estimate_by_order", "err=%v", err)
		return nil, err, nil
	}

	targetInstance.AsyncHandle(ctx, serviceReq, products, resp)

	return resp, nil, targetInstance.WritePublicLog(ctx, serviceReq, products, resp)
}

func buildServiceReq(ctx context.Context, req *proto.PEstimateByOrderRequest) (*model.ServiceRequest, NewErrors.BizError) {
	serviceReq := model.NewServiceReq(req)
	if err := serviceReq.Do(ctx,
		model.WithFormatParam(req),
		model.WithTravelQuotation(req),
		model.WithCacheInfo(req),
		model.WithUserInfo(req),
		model.WithOrderInfo(req),
		model.WithQuotationInfo(req),
	); err != nil {
		return nil, err
	}

	return serviceReq, nil
}
