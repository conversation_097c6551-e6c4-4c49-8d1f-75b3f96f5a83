package internal

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/public_log"

	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/multi_estimate_by_order/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type Dispatcher interface {
	Name(ctx context.Context) string
	Selected(ctx context.Context, serviceReq *model.ServiceRequest) bool
	RunTimeBuild(ctx context.Context, req *model.ServiceRequest) ([]*biz_runtime.ProductInfoFull, NewErrors.BizError)
	Render(ctx context.Context, serviceReq *model.ServiceRequest, products []*biz_runtime.ProductInfoFull) (*proto.PEstimateByOrderData, NewErrors.BizError)
	AsyncHandle(ctx context.Context, serviceReq *model.ServiceRequest, products []*biz_runtime.ProductInfoFull, resp *proto.PEstimateByOrderData)
	WritePublicLog(ctx context.Context, serviceReq *model.ServiceRequest, products []*biz_runtime.ProductInfoFull, resp *proto.PEstimateByOrderData) public_log.WritePublicFunc
}

type base struct{}

func (b *base) Name(ctx context.Context) string {
	return "base"
}

func (b *base) Selected(ctx context.Context, serviceReq *model.ServiceRequest) bool {
	return true
}

func (b *base) RunTimeBuild(ctx context.Context, req *model.ServiceRequest) ([]*biz_runtime.ProductInfoFull, NewErrors.BizError) {
	return nil, nil
}

func (b *base) Render(ctx context.Context, serviceReq *model.ServiceRequest, products []*biz_runtime.ProductInfoFull) (*proto.PEstimateByOrderData, NewErrors.BizError) {
	return nil, nil
}

func (b *base) AsyncHandle(ctx context.Context, serviceReq *model.ServiceRequest, products []*biz_runtime.ProductInfoFull, resp *proto.PEstimateByOrderData) {
	return
}

func (b *base) WritePublicLog(ctx context.Context, serviceReq *model.ServiceRequest, products []*biz_runtime.ProductInfoFull, resp *proto.PEstimateByOrderData) public_log.WritePublicFunc {
	return nil
}
