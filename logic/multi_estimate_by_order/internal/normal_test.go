package internal

import (
	"context"
	sdk "git.xiaojukeji.com/dirpc/dirpc-go-http-Dos"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/conf"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/locsvr"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/multi_estimate_by_order/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
	"git.xiaojukeji.com/nuwa/golibs/zerolog/ddlog"
	"io/fs"
	"os"
	"path/filepath"
	"runtime"
	"testing"
)

func Test_NormalDispatch_RuntimeBuild(t *testing.T) {
	base := NewNormalDispatch()
	var err error

	config := ddlog.FileConfig{}
	config.ClearHours = 168
	config.FilePrefix = "didi"
	config.FileDir = "./log"

	_, fn, _, _ := runtime.Caller(0)
	confDir := filepath.Dir(fn)
	confFile := confDir + "/../../../conf/app_dev.toml"
	t.Logf("confFile: %s", confFile)
	if _, err = fs.Stat(os.DirFS("."), confFile); err != nil {
		t.Logf("confFile: %s not exist, err: %v", confFile, err)
	}

	conf.InitConf(confFile)
	log.Trace, err = log.NewNormalLogger()
	if err != nil {
		t.Errorf("init log err: %v", err)
		return
	}

	err = locsvr.Init()
	if err != nil {
		t.Errorf("init locsvr err: %v", err)
		return
	}

	base.RunTimeBuild(context.Background(), &model.ServiceRequest{
		Req: &proto.PEstimateByOrderRequest{},
		OrderInfo: &order_info.OrderInfo{
			OrderInfo: &sdk.OrderInfo{
				Type: "1",
			},
		},
		UserInfo: &passport.UserInfo{},
	})
}
