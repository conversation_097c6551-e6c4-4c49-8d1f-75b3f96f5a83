package internal

import (
	"context"
	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/public_log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/multi_estimate_by_order/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/multi_estimate_by_order"
)

type byCacheDispatch struct {
	base
}

func NewByCacheDispatch() Dispatcher {
	return &byCacheDispatch{}
}

func (b *byCacheDispatch) Name(ctx context.Context) string {
	return "by_cache"
}

func (b *byCacheDispatch) Selected(ctx context.Context, serviceReq *model.ServiceRequest) bool {
	if serviceReq != nil && serviceReq.CacheInfo != nil {
		return true
	}

	return false
}

func (b *byCacheDispatch) Render(ctx context.Context, serviceReq *model.ServiceRequest, products []*biz_runtime.ProductInfoFull) (*proto.PEstimateByOrderData, NewErrors.BizError) {
	return multi_estimate_by_order.RenderByCache(ctx, serviceReq)
}

func (b *byCacheDispatch) WritePublicLog(ctx context.Context, serviceReq *model.ServiceRequest, products []*biz_runtime.ProductInfoFull, resp *proto.PEstimateByOrderData) public_log.WritePublicFunc {
	return func() {
		if serviceReq == nil || serviceReq.CacheInfo == nil || serviceReq.Req == nil {
			return
		}

		logInfo := make(map[string]interface{})
		logInfo["estimate_id"] = serviceReq.CacheInfo.EstimateID
		logInfo["estimate_fee"] = serviceReq.CacheInfo.EstimateFee
		logInfo["product_category"] = serviceReq.CacheInfo.ProductCategory

		logInfo["access_key_id"] = serviceReq.Req.AccessKeyId
		logInfo["app_version"] = serviceReq.Req.AppVersion
		logInfo["oid"] = serviceReq.Req.Oid

		logInfo["fee_type"] = serviceReq.CacheInfo.FeeType

		logInfo["dispatch"] = consts.Cache

		log.Public.Public(ctx, public_log.GEstimateByOrder, logInfo)
	}
}
