package business_anycar_estimate

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/models/product_filter/after_price_filter"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_filter/after_dds_filter"
)

func RegisterProductFilter(ctx context.Context, params *BaseParams, generator *biz_runtime.ProductsGenerator) {
	if generator.BaseReqData == nil {
		return
	}
	//过滤当前已呼叫品类除了企业&快车
	if cpFilter := after_dds_filter.NewBusinessAnyCarFilterCurrentProducts(ctx,
		params.OrderFeature.MultiRequireProduct, generator.BaseReqData.PassengerInfo.UserType); cpFilter != nil {
		generator.RegisterAfterDdsFilter(cpFilter)
	}

	//过滤 A+ 跨城
	if tcFilter := after_dds_filter.NewBusinessAPlusAbFilter(ctx, params.OrderInfo.ExtraType, generator.BaseReqData); tcFilter != nil {
		generator.RegisterAfterDdsFilter(tcFilter)
	}

	//针对企业支付 预估额外添加了快车的情况 请求到价格后再次过滤一下已呼叫品类
	if cpFilter := after_price_filter.NewGuideAnyCarFilterFromNewOrder(ctx,
		params.OrderFeature.MultiRequireProduct); cpFilter != nil {
		generator.RegisterAfterPriceFilter(cpFilter)
	}

}
