package pbd_estimate

import (
	"context"
	"strconv"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_common"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/source_id"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

func InitLogic(ctx context.Context, passenger *passport.UserInfo, req *proto.PBDEstimateReq) (*BizLogic, error) {
	var (
		departureRange         *models.DepartureRange
		intercityDepartureTime *time.Time
		extPID                 string
	)
	departureTime, _ := strconv.ParseInt(req.DepartureTime, 10, 64)
	if departureTime == 0 {
		departureTime = time.Now().Unix()
	}

	// 兼容pbd选城际时间片
	if len(req.DepartureRange) > 0 {
		departureRange = intercity_common.DecodeDepartureRangeV3(req.DepartureRange)
		intercityDepartureTime = intercity_common.DecodeDepartureTime(req.DepartureTime)
		if intercityDepartureTime != nil {
			departureTime = deriveDepartureTimeFromDepartureRange(departureRange, intercityDepartureTime).Unix()
		}
	}
	var carpoolSeatNum int32
	carpoolSeatNum = 1
	if req.CarpoolSeatNum != nil && *req.CarpoolSeatNum >= 1 {
		carpoolSeatNum = req.GetCarpoolSeatNum()
	}

	if req.ExtPid != nil && *(req.ExtPid) != "" {
		extPID = *(req.ExtPid)
	}

	baseReq, err := models.NewBaseReqDataBuilder().
		SetClientInfo(&models.ClientInfo{
			AppVersion:  req.AppVersion,
			AccessKeyID: req.AccessKeyId,
			Channel:     int64(req.Channel),
			Lang:        req.Lang,
			MenuID:      "dache_anycar",
			SourceID:    int32(source_id.SourceIDPBD),
		}).
		SetGEOInfo(&models.GEOInfo{
			FromLat:  req.FromLat,
			FromLng:  req.FromLng,
			FromName: req.FromName,

			ToLat:  req.ToLat,
			ToLng:  req.ToLng,
			ToName: req.ToName,
		}).
		SetPassengerInfoV2(&models.PassengerInfoV2{
			UID:      int64(passenger.UID),
			PID:      int64(passenger.PID),
			Phone:    passenger.Phone,
			Role:     passenger.Role,
			Channel:  passenger.Channel,
			UserType: 1,
			OriginID: passenger.OriginId,
			ExtPID:   extPID,
		}).
		SetUserOption(&models.UserOption{
			CallCarType:    0,
			OrderType:      req.OrderType,
			DepartureTime:  departureTime,
			PaymentsType:   2,
			DepartureRange: departureRange,
			CarpoolSeatNum: carpoolSeatNum,
		}).
		TryBuild(ctx)
	if err != nil {
		return nil, BizError.ErrSystem
	}

	// no-check
	productsGen, _ := biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("pPbdEstimate"),
		biz_runtime.WithBaseReq(baseReq),
	)

	productsGen.SetNeedMember(false)

	productsGen.SetSendReqKafka(true)

	RegisterProductFilter(ctx, req.NeedProductCategory, productsGen)

	RegisterRpcProcessWithBasicProducts(ctx, productsGen)

	return &BizLogic{generator: productsGen}, nil
}

func CheckParams(ctx context.Context, req *proto.PBDEstimateReq) (err error) {
	if req == nil {
		return BizError.ErrInvalidArgument
	}
	if req.GetToLat() == 0 || req.GetToLng() == 0 || req.GetFromLat() == 0 || req.GetFromLng() == 0 {
		return BizError.ErrInvalidArgument
	}

	return nil
}

func deriveDepartureTimeFromDepartureRange(rng *models.DepartureRange, tim *time.Time) time.Time {
	if tim != nil {
		return *tim
	}
	if rng == nil {
		return time.Now()
	}
	return rng.DepartureTime()
}
