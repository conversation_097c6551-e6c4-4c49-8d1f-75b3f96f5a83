package coupon_price

import (
	"context"
	Ferrari "git.xiaojukeji.com/dirpc/dirpc-go-http-Ferrari"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

func BuildResponse(ctx context.Context, baseReq *proto.CouponPriceRequest, coupon *Ferrari.CalcBargainPostCouponPriceData) *proto.CouponEstimateData {
	var (
		feeMsg         string
		couponType     int32
		couponDiscount string
		discountText   string
		discountFee    float64
	)

	if coupon == nil {
		// 无券
		estimateFee := util.ToString(baseReq.FeeAmount)
		tag := map[string]string{"amount": estimateFee}
		feeMsg = dcmp.GetJSONContentWithPath(ctx, "bargain-coupon_price", tag, "fee_msg")

		return &proto.CouponEstimateData{
			EstimateFee:    estimateFee,
			FeeMsg:         feeMsg,
			CouponType:     couponType,
			CouponDiscount: couponDiscount,
			DiscountText:   discountText,
		}
	}

	if coupon.CouponType == consts.DeductionCouponType {
		// 立减券
		couponType = 1
		discountFee = util.ToFloat64(coupon.DeductionFee) / 100       // 根据订单计算出来的 券可抵扣金额
		couponDiscount = strconv.FormatFloat(discountFee, 'f', 2, 64) // 券抵扣价格
		couponDiscount = util.RemoveSuffixZero(couponDiscount)
		tag := map[string]string{"amount": couponDiscount}
		discountText = dcmp.GetJSONContentWithPath(ctx, "bargain-coupon_price", tag, "deduction_coupon_text")
	} else if coupon.CouponType == consts.DiscountCouponType {
		// 折扣券
		couponType = 2
		discount := util.ToFloat64(coupon.Discount) / 10 // 券折扣
		discountAmount := strconv.FormatFloat(discount, 'f', 1, 64)
		discountAmount = util.RemoveSuffixZero(discountAmount)
		couponDiscount = discountAmount
		tag := map[string]string{"amount": discountAmount}
		discountText = dcmp.GetJSONContentWithPath(ctx, "bargain-coupon_price", tag, "discount_coupon_text")
	}

	estimateFee := strconv.FormatFloat(coupon.EstimateFee, 'f', 2, 64)
	estimateFee = util.RemoveSuffixZero(estimateFee)
	tag := map[string]string{"amount": estimateFee}
	feeMsg = dcmp.GetJSONContentWithPath(ctx, "bargain-coupon_price", tag, "hit_fee_msg")

	return &proto.CouponEstimateData{
		EstimateFee:    estimateFee,
		FeeMsg:         feeMsg,
		CouponType:     couponType,
		CouponDiscount: couponDiscount,
		DiscountText:   discountText,
		DiscountAmount: util.ToFloat64(coupon.DeductionFee) / 100,
	}
}
