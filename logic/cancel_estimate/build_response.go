package cancel_estimate

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/view/render/fee_msg"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	context2 "git.xiaojukeji.com/lego/context-go"
)

func initProduct(pFull *biz_runtime.ProductInfoFull) *proto.CancelEstimateProduct {
	if pFull == nil || pFull.Product == nil || pFull.BillDetail == nil || len(pFull.DiscountInfo) <= 0 {
		return nil
	}
	product := &proto.CancelEstimateProduct{}
	product.EstimateId = pFull.Product.EstimateID
	product.ProductCategory = pFull.Product.ProductCategory
	product.BusinessId = pFull.Product.BusinessID
	product.RequireLevel, _ = strconv.ParseInt(pFull.Product.RequireLevel, 10, 64)
	product.ComboType = pFull.Product.ComboType
	product.LevelType = int64(pFull.Product.LevelType)
	product.IsDualCarpoolPrice = pFull.Product.IsDualCarpoolPrice
	product.CarpoolPriceType = int64(pFull.Product.CarpoolPriceType)
	product.CarpoolType = pFull.Product.CarpoolType
	return product
}

func buildProductList(ctx context.Context, products []*biz_runtime.ProductInfoFull) (productList []*proto.CancelEstimateProduct, errno int) {
	feeMsgRender := fee_msg.MultiFeeMsgRender{}
	fnGetMultiFeeMsg := func(ctx context.Context, full *biz_runtime.ProductInfoFull) (ret []*proto.CarpoolDoublePrice) {
		iMultiFeeMsg := feeMsgRender.Do(ctx, full, nil)
		if multiFeeMsg, ok := iMultiFeeMsg.([]*fee_msg.FeeMsgData); ok && len(multiFeeMsg) > 0 {
			for _, feeMsg := range multiFeeMsg {
				ret = append(ret, &proto.CarpoolDoublePrice{EstimateFee: feeMsg.EstimateFee, FeeMsg: feeMsg.FeeMsg})
			}
		}
		return
	}

	for _, pFull := range products {
		if product := initProduct(pFull); product != nil {
			product.MultiFeeMsg = fnGetMultiFeeMsg(ctx, pFull)

			productList = append(productList, product)
		}
	}
	return productList, consts.NoErr
}

func BuildResponse(ctx context.Context, products []*biz_runtime.ProductInfoFull) (resp *proto.CancelEstimateData, errno int) {
	var (
		productList []*proto.CancelEstimateProduct
	)
	productList, errno = buildProductList(ctx, products)
	if consts.NoErr != errno {
		return nil, errno
	}
	resp = &proto.CancelEstimateData{
		EstimateTraceId: context2.GetTrace(ctx).GetTraceId(),
		ProductList:     productList,
	}
	return resp, consts.NoErr
}
