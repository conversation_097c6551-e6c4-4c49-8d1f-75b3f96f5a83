package pbd_station_bus_multi_estimate

import (
	"context"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/pbd_station_bus_estimate_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pbd_station_bus_estimate"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	pbd_station_bus_estimate_render "git.xiaojukeji.com/gulfstream/mamba/render/private/pbd_station_bus_estimate"
	"git.xiaojukeji.com/gulfstream/passenger-common/util/money"
	trace "git.xiaojukeji.com/lego/context-go"
	"github.com/spf13/cast"
)

type render struct {
}

// NewRender ...
func NewRender() *render {
	return &render{}
}

// Do ...
func (r *render) Do(ctx context.Context, products []*biz_runtime.ProductInfoFull) (data *proto.PbdStationBusMultiEstimateData, e BizError.BizError) {
	data = &proto.PbdStationBusMultiEstimateData{
		EstimateTraceId: trace.GetTrace(ctx).GetTraceId(),
	}
	shiftInfoList := []*proto.ShiftInfo{}

	stationPriceMap := formatProducts(products)

	for _, product := range products {
		if product == nil || product.Product == nil {
			continue
		}

		prod := &pbd_station_bus_estimate.AdapterPbdStationBus{ProductInfoFull: product}
		//改签场景，不渲染原班次
		if prod.GetBizSceneType() == pbd_station_bus_estimate_consts.PbdMultiTypeRebook && prod.GetPreBusServiceShiftId() == prod.GetShiftID() {
			continue
		}
		stationInfo := pbd_station_bus_estimate_render.StationInfo(ctx, prod, pbd_station_bus_estimate_render.PbdMultiType)
		shiftInfo := &proto.ShiftInfo{
			EstimateId:        prod.GetEstimateID(),
			EstimateFee:       util.Float64Hundred(prod.GetEstimateFee()),
			DepartureTime:     prod.GetDepartureTime(),
			BusServiceShiftId: prod.GetShiftID(),
			ProductId:         prod.GetProductId(),
			StationInfo:       stationInfo,
			StationPriceList:  buildStationPrice(stationPriceMap, stationInfo, prod.GetShiftID()),
		}
		//改签场景获取订单参数，用于直接发单
		if prod.GetBizSceneType() == pbd_station_bus_estimate_consts.PbdMultiTypeRebook {
			orderParams := pbd_station_bus_estimate_render.GetOrderParams(ctx, prod)
			shiftInfo.OrderParams = orderParams
		}
		shiftInfoList = append(shiftInfoList, shiftInfo)
	}
	//获取分页信息
	LastItem := true
	LastShiftId := ""
	if len(products) != 0 {
		LastItem = products[len(products)-1].Product.BizInfo.StationInventoryInfo.SelectInfo.LastItem
		LastShiftId = products[len(products)-1].Product.BizInfo.StationInventoryInfo.SelectInfo.ShiftID
	}
	// 全局数据
	return &proto.PbdStationBusMultiEstimateData{
		EstimateTraceId: trace.GetTrace(ctx).GetTraceId(),
		LastShiftId:     LastShiftId,
		ShiftInfoList:   shiftInfoList,
		IsLastPage:      LastItem,
	}, nil
}

// 将定价信息按照班次进行聚合
func formatProducts(productsFull []*biz_runtime.ProductInfoFull) map[string][]*pbd_station_bus_estimate.StationPrice {
	stationPriceMap := make(map[string][]*pbd_station_bus_estimate.StationPrice)
	for _, product := range productsFull {
		prodAdapter := &pbd_station_bus_estimate.AdapterPbdStationBus{ProductInfoFull: product}

		stationPriceData := prodAdapter.GetStationPriceData()
		if stationPriceData == nil {
			return stationPriceMap
		}

		if len(stationPriceData.StationPrice) == 0 {
			return stationPriceMap
		}

		for _, priceInfo := range stationPriceData.StationPrice {
			stationPriceList := stationPriceMap[prodAdapter.GetShiftID()]
			if len(stationPriceList) == 0 {
				stationPriceList = make([]*pbd_station_bus_estimate.StationPrice, 0, len(stationPriceData.StationPrice))
			}

			stationPriceList = append(stationPriceList, &pbd_station_bus_estimate.StationPrice{
				StartStationId: cast.ToInt32(priceInfo.From),
				EndStationId:   cast.ToInt32(priceInfo.To),
				Price:          money.Yuan2Fen(priceInfo.Price),
			})

			stationPriceMap[prodAdapter.GetShiftID()] = stationPriceList
		}
	}
	return stationPriceMap
}

func buildStationPrice(priceMap map[string][]*pbd_station_bus_estimate.StationPrice, stationInfo *proto.PbdStationBusStationInfo, shiftId string) []*proto.PbdStationPriceInfo {
	if len(priceMap) == 0 {
		return nil
	}

	stationPrices := priceMap[shiftId]
	if len(stationPrices) == 0 {
		return nil
	}

	if stationInfo == nil || stationInfo.Detail == nil {
		return nil
	}

	stationList := stationInfo.Detail.StationList
	if len(stationList) == 0 {
		return nil
	}

	stationIdMap := make(map[int64]struct{})
	for _, station := range stationList {
		stationIdMap[station.Id] = struct{}{}
	}

	stationPriceList := make([]*proto.PbdStationPriceInfo, 0, len(stationPrices))
	for _, stationPrice := range stationPrices {

		if _, ok := stationIdMap[util.ToInt64(stationPrice.StartStationId)]; !ok {
			continue
		}

		if _, ok := stationIdMap[util.ToInt64(stationPrice.EndStationId)]; !ok {
			continue
		}

		stationPriceList = append(stationPriceList, &proto.PbdStationPriceInfo{
			StartStationId: stationPrice.StartStationId,
			EndStationId:   stationPrice.EndStationId,
			Price:          stationPrice.Price,
		})
	}

	return stationPriceList
}
