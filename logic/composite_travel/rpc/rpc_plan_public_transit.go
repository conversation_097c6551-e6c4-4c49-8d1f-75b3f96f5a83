package rpc

import (
	"context"
	"encoding/json"
	"fmt"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"

	metro "git.xiaojukeji.com/dirpc/dirpc-go-http-TransitWind"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/transit_wind"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel/model"
	"github.com/spf13/cast"
)

type PublicTransitRPC struct {
	baseRequest *model.Request
	Res         []*metro.TransitSimple
	Fid         string
	conf        *TransitDCMPConf
}

const (
	PublicTransitRPCLogTag = "PublicTransitRPC"
)

func NewPublicTransitRPC(ctx context.Context, baseRequest *model.Request) *PublicTransitRPC {
	req := baseRequest.CompositeTravelReq
	// 参数校验
	if req.FromLat == 0 || req.FromLng == 0 || req.ToLat == 0 || req.ToLng == 0 ||
		req.MapType == "" || req.FromPoiId == "" {
		log.Trace.Warnf(ctx, PublicTransitRPCLogTag, "req is lost")
		return nil
	}

	// 文案配置
	conf, err := initPublicTransitDCMPConf(ctx)
	if err != nil || conf == nil {
		log.Trace.Warnf(ctx, CombinedTravelRPCLogTag, "init DCMPConf err:[%v]", err)
		return nil
	}

	return &PublicTransitRPC{
		baseRequest: baseRequest,
		conf:        conf,
	}
}

func (p *PublicTransitRPC) Execute(ctx context.Context, request *model.Request) {
	simpleReq := &metro.TransitInnerSimpleReq{
		ProductId:      "111",
		CallerId:       "comprehensive_rec",
		AccKey:         "w9mQQxBLInvx4Gm3d1Hv",
		BusinessSource: 101,

		Ddfp:             request.GetDdfp(),
		OriginLng:        request.GetFromLng(),
		OriginLat:        request.GetFromLat(),
		OriginName:       request.FromName,
		OriginPoiId:      &request.FromPoiId,
		DestinationLng:   request.GetToLng(),
		DestinationLat:   request.GetToLat(),
		DestinationName:  request.ToName,
		DestinationPoiId: request.GetToPoiId(),
		Token:            &request.Token,
		AppVersion:       &request.AppVersion,
	}
	if request.UserInfo != nil {
		simpleReq.Uid = cast.ToString(request.UserInfo.UID)
	}

	res := transit_wind.TransitInnerSimple(ctx, simpleReq)
	if res == nil {
		return
	}

	p.Res = res.Transits
	p.Fid = res.Fid
}

func (p *PublicTransitRPC) GetCommentResult(ctx context.Context) *proto.Evaluation {
	return nil
}

func (p *PublicTransitRPC) GetRpcResult(ctx context.Context) (planType int, result []*model.PlanFull) {
	planType = model.PublicTransit
	if len(p.Res) < 1 {
		return
	}

	for _, transit := range p.Res {

		if len(transit.Segments) < 1 {
			continue
		}

		minutes := transit.Duration / 60
		if minutes == 0 {
			minutes = 1
		}

		o := proto.Plan{
			EstimateTime: GetEstimateTime(ctx, int64(transit.Duration)),
			FeeMsg:       GetFeeMsg(ctx, int64(transit.Cost), p.conf.FeeMsg),
			PlanType:     model.PublicTransit,
			MapParams:    p.buildMapParams(transit.TransitId),
			LinkType:     model.LinkTypeUrl,
			LinkUrl:      p.buildListUrl(ctx, transit.TransitId),
			RightTitleList: []string{
				util.ReplaceTag(ctx, p.conf.RichTimeMsg, map[string]string{
					"minutes": cast.ToString(minutes),
				}),
				util.ReplaceTag(ctx, p.conf.RichFeeMsg, map[string]string{
					"min": util.Fen2Yuan(int64(transit.Cost)),
				}),
			},
			SceneId: &p.conf.SceneId,
		}

		o.Params = make(map[string]string)
		o.Params["transit_id"] = transit.TransitId
		o.Params["fid"] = p.Fid

		var (
			segmentList     [][]*proto.Segment
			modeIndex             = -1
			lastSegmentMode int32 = -99

			modeCacheMap = make(map[int32]bool) // 记录出行方式

			planFormatDetailData model.PlanFormatDetailData // 方案详情数据模型
		)

		for _, segment := range transit.Segments {
			detailSegmentItem := model.SegmentItem{
				Mode: segment.Mode,
			}

			if segment.Mode == model.ModeWalk || segment.Walking != nil {
				detailSegmentItem.Distance = segment.Walking.Distance
				detailSegmentItem.Eta = segment.Walking.Duration
				detailSegmentItem.Time = segment.Walking.Duration

			}

			if segment.Mode == model.ModeTRANSIT || segment.Metrobus != nil {
				for _, v := range segment.Metrobus {
					// 注意segment.Metrobus的类型是个指针数组，这里不重新赋值的话，用range 有问题
					vv := v
					detailSegmentItem.LineId = util.ToString(vv.Id)
					detailSegmentItem.LineName = util.ToString(vv.Name)
					detailSegmentItem.SubMode = util.ToString(vv.Type)
					detailSegmentItem.Distance = util.Int32Ptr2Int32(vv.Distance)
					detailSegmentItem.Eta = util.Int32Ptr2Int32(vv.Duration)
					detailSegmentItem.Time = util.Int32Ptr2Int32(vv.Duration)
					detailSegmentItem.Cost = util.Int32Ptr2Int32(vv.Price)
					detailSegmentItem.Etp = util.Int32Ptr2Int32(vv.Waittime)
					detailSegmentItem.CanReachStatus = util.Int32Ptr2Int32((*int32)(vv.CanreachStatus))
					detailSegmentItem.LineColor = vv.Color
					// 途径站点信息
					if len(vv.ViaStops) > 0 {
						var viaStops []*model.Stop
						for _, stop := range v.ViaStops {
							viaStops = append(viaStops, &model.Stop{
								Id:       stop.Id,
								Name:     stop.Name,
								Location: stop.Location,
							})
						}

						detailSegmentItem.ViaStops = viaStops
					}

					break
				}
				// 得到要展现的公交名
				for i, v := range segment.Metrobus {
					if i == 0 {
						detailSegmentItem.SubLineName = v.Name
						continue
					}
					if v.Type == model.TypeBus {
						detailSegmentItem.SubLineName = detailSegmentItem.SubLineName + "/" + v.Name
					}
				}
			}

			planFormatDetailData.SegmentDataList = append(planFormatDetailData.SegmentDataList, &detailSegmentItem)
		}

		for _, segment := range transit.Segments {
			// 1. 排除步行==============================
			if segment.Mode == model.ModeWalk || len(segment.Metrobus) < 1 {
				continue
			}

			// 2. 记录包含的出行方式 ==============================
			for _, metroBus := range segment.Metrobus {
				modeCacheMap[metroBus.Type] = true
				break
			}

			// 3. 生成segmentList==============================
			modeIndex++
			if len(segment.Metrobus) > 0 {
				metroType := segment.Metrobus[0].Type

				if metroType == lastSegmentMode && lastSegmentMode == model.TypeSubway {
					// 地铁方案不新增端
					modeIndex--
				}

				lastSegmentMode = metroType
			}

			if len(segmentList) <= modeIndex {
				segmentList = append(segmentList, []*proto.Segment{})
			}
			segmentList[modeIndex] = append(segmentList[modeIndex],
				p.renderSegment(segment.Metrobus),
			)

		}
		o.SegmentList = segmentList

		o.Title = p.getTitle(ctx, modeCacheMap)

		o.DescList = p.buildDescList(ctx, transit.WalkingDistance, transit.DisplayInfo, p.conf.WalkDesc)

		if transit.MissingTip != nil {
			o.TipData = &proto.TipData{
				Text:      *transit.MissingTip, // 末班车提示
				Icon:      &p.conf.ReminderInfo.Icon,
				TextColor: p.conf.ReminderInfo.TextColor,
				BgColor:   p.conf.ReminderInfo.BgColor,
			}
		}

		result = append(result, &model.PlanFull{
			TotalTime:            int64(transit.Duration),
			Cost:                 int64(transit.Cost),
			PlanType:             model.PublicTransit,
			Plan:                 o,
			PlanFormatDetailData: planFormatDetailData,
			PrivateData: &model.PrivateData{
				PublicTransitData: transit,
			},
		})
	}

	return
}

func (p *PublicTransitRPC) buildMapParams(transitId string) map[string]string {
	mapParams := p.conf.MapParams
	mapParams = make(map[string]string)
	mapParams["product_id"] = p.conf.MapParams["productId"]
	mapParams["acc_key"] = p.conf.MapParams["accKey"]
	mapParams["caller_id"] = p.conf.MapParams["callerId"]
	mapParams["fid"] = p.Fid
	mapParams["transit_id"] = transitId
	return mapParams
}

func (p *PublicTransitRPC) buildListUrl(ctx context.Context, transitId string) string {
	if p.baseRequest != nil && p.baseRequest.AccessKeyId == 9 || p.baseRequest.AccessKeyId == 22 {
		return util.ReplaceTag(ctx, p.conf.LinkInfo.MiniProgram, map[string]string{
			"fid":       p.Fid,
			"transitId": transitId,
		})
	}

	return util.ReplaceTag(ctx, p.conf.LinkInfo.Default, map[string]string{
		"fid":        p.Fid,
		"transit_id": transitId,
	})
}

func (p *PublicTransitRPC) renderSegment(data []*metro.MetrobusLineSimple) *proto.Segment {
	segment := &proto.Segment{}

	var content string
	var color string // 地铁线路颜色
	var mode int32
	for i, metroBus := range data {
		mode = metroBus.Type
		if i == 0 {
			content = metroBus.Name
			color = *metroBus.Color
			continue
		}
		if metroBus.Type == model.TypeBus {
			content = content + "/" + metroBus.Name
		}
	}

	switch mode {
	case model.TypeBus:
		segment.Content = content
		segment.TextColor = p.conf.Segment.BusTextColor
		segment.BgColor = p.conf.Segment.BusBgColor
		segment.BorderColor = p.conf.Segment.BusBorderColor
	case model.TypeSubway:
		segment.Content = content
		segment.TextColor = p.conf.Segment.SubwayBorderColor
		segment.BgColor = color
		segment.BorderColor = color

	}
	return segment
}

func (p *PublicTransitRPC) getTitle(ctx context.Context, modeCacheMap map[int32]bool) string {
	if len(modeCacheMap) > 1 {
		return p.conf.Title.Title1
	}

	_, ok := modeCacheMap[model.TypeSubway]
	if ok {
		return p.conf.Title.Title2
	}
	return p.conf.Title.Title3
}

func (p *PublicTransitRPC) buildDescList(ctx context.Context, meter int32, abstractDisplayInfo *metro.AbstractDisplayInfo, walkDesc WalkDesc) []*proto.PlanDesc {

	var descList []*proto.PlanDesc

	if meter > 0 {
		descList = getWalkDesc(ctx, descList, int64(meter), walkDesc)
	}

	if abstractDisplayInfo != nil {

		if len(abstractDisplayInfo.GetDepartureStopName()) > 0 {
			switch abstractDisplayInfo.GetDepartureStopType() {
			case 1:
				descList = append(descList, &proto.PlanDesc{
					Content: fmt.Sprintf(p.conf.DescList.Station1, abstractDisplayInfo.GetDepartureStopName()),
				})
			case 2:
				if len(abstractDisplayInfo.GetDepartureStopEntranceName()) > 0 {
					descList = append(descList, &proto.PlanDesc{
						Content: fmt.Sprintf(p.conf.DescList.Station2, abstractDisplayInfo.GetDepartureStopName(), abstractDisplayInfo.GetDepartureStopEntranceName()),
					})
				}
			}
		}

		if abstractDisplayInfo.GetStopsCount() > 0 {
			descList = append(descList, &proto.PlanDesc{
				Content: util.ReplaceTag(ctx, p.conf.DescList.StationContent, map[string]string{
					"stationCount": cast.ToString(abstractDisplayInfo.StopsCount),
				}),
			})
		}
	}

	return descList
}

type TransitTitle struct {
	Title1 string `json:"title_1"`
	Title2 string `json:"title_2"`
	Title3 string `json:"title_3"`
}
type TransitSegment struct {
	BusTextColor   string `json:"bus_text_color"`
	BusBgColor     string `json:"bus_bg_color"`
	BusBorderColor string `json:"bus_border_color"`

	SubwayBorderColor string `json:"subway_border_color"`
}

type TransitDCMPConf struct {
	Title        TransitTitle      `json:"title"` // 方案标题
	MapParams    map[string]string `json:"map_params"`
	DescList     DescListDCMP2     `json:"desc_list"`
	FeeMsg       string            `json:"fee_msg"`
	ReminderInfo Reminder          `json:"reminder_info"`
	LinkInfo     LinkInfo          `json:"link_url"`
	Segment      TransitSegment    `json:"segment"`
	WalkDesc     WalkDesc          `json:"walk_desc"`
	RichTimeMsg  string            `json:"rich_time_msg"`
	RichFeeMsg   string            `json:"rich_fee_msg"`
	SceneId      string            `json:"scene_id"`
}

func initPublicTransitDCMPConf(ctx context.Context) (*TransitDCMPConf, error) {
	conf := new(TransitDCMPConf)
	str := dcmp.GetDcmpContent(ctx, "one_stop-public_transit_new", nil)
	err := json.Unmarshal([]byte(str), conf)
	if err != nil || conf == nil {
		return nil, err
	}
	return conf, nil
}
