package rpc

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"

	combinedTravel "git.xiaojukeji.com/dirpc/dirpc-go-http-CombinedTravel"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/combined_travel_v2"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel/model"
	"github.com/spf13/cast"
)

type CombinedTravelRPC struct {
	baseRequest *model.Request
	Res         *combinedTravel.UTRecommendRespData
	conf        *CombinedDCMPConf
}

const (
	CombinedTravelRPCLogTag = "CombinedTravelRPC"
)

func NewCombinedTravelRPC(ctx context.Context, baseRequest *model.Request) *CombinedTravelRPC {
	req := baseRequest.CompositeTravelReq
	// 参数校验
	if req.FromLat == 0 || req.FromLng == 0 || req.ToLat == 0 || req.ToLng == 0 ||
		req.MapType == "" || req.FromPoiId == "" {
		log.Trace.Infof(ctx, CombinedTravelRPCLogTag, "req is lost")
		return nil
	}

	// 文案配置
	conf, err := initCombinedTravelDCMPConf(ctx)
	if err != nil || conf == nil {
		log.Trace.Warnf(ctx, CombinedTravelRPCLogTag, "init CombinedDCMPConf err:[%v]", err)
		return nil
	}

	return &CombinedTravelRPC{
		baseRequest: baseRequest,
		conf:        conf,
	}
}

func (c *CombinedTravelRPC) Execute(ctx context.Context, req *model.Request) {
	recommend := combined_travel_v2.UTRecommend(ctx, &combinedTravel.UTRecommendReq{
		Uid:          int64(req.UserInfo.UID),
		Pid:          int64(req.UserInfo.PID),
		Phone:        req.UserInfo.Phone,
		Lang:         req.GetLang(),
		Token:        req.GetToken(),
		AppVersion:   req.AppVersion,
		AccessKeyId:  req.AccessKeyId,
		Channel:      req.Channel,
		ClientType:   req.ClientType,
		PlatformType: req.PlatformType,
		Lat:          req.Lat,
		Lng:          req.Lng,
		Ddfp:         req.GetDdfp(),
		FromLat:      req.FromLat,
		FromLng:      req.FromLng,
		FromName:     req.FromName,
		FromAddress:  req.FromAddress,
		FromPoiId:    req.FromPoiId,
		ToLat:        req.ToLat,
		ToLng:        req.ToLng,
		ToName:       req.ToName,
		ToPoiId:      req.ToPoiId,
		ToAddress:    req.ToAddress,
		MapType:      req.GetMapType(),
		MptExprandom: req.ExpRandom,
		ExtraInfo:    make(map[string]string),
	})

	if recommend == nil || recommend.Data == nil {
		return
	}
	c.Res = recommend.Data
	return
}

func (c *CombinedTravelRPC) GetCommentResult(ctx context.Context) *proto.Evaluation {
	return nil
}

func (c *CombinedTravelRPC) GetRpcResult(ctx context.Context) (planType int, result []*model.PlanFull) {
	planType = model.CombinedTravel
	planList := c.Res.GetPlanList()
	if len(planList) < 1 {
		return
	}

	for _, plan := range planList {

		var (
			// firstSegment bool // 记录非步行的第一个方案类型, 决策Title是打车+地铁还是 地铁+打车

			isFirstTransit          = false // 记录是否第一个地铁
			FirstTransitStationName string  // 第一个地铁站名称

			segmentList     [][]*proto.Segment
			modeIndex       = -1
			lastSegmentMode = new(combinedTravel.UTRecommendSegmentItem) // 最后一个段数据

			walkDistanceSum int64 // 步行距离->渲染"全程步行x米"

			transitCostSum int64 // 地铁/公交总费用-> 渲染"x元"

			planFormatDetailData model.PlanFormatDetailData // 方案详情数据模型

			modeCacheMap = make(map[int32]bool) // 记录transit出行方式
		)

		var modeList = make([]model.ModeItem, 0)

		// 生成模型数据
		for _, segmentItem := range plan.SegmentList {
			detailSegmentItem := model.SegmentItem{
				Mode:     segmentItem.Mode,
				Distance: segmentItem.Distance,
				Time:     segmentItem.Duration,
				Cost:     int32(segmentItem.Cost),
			}

			if segmentItem.Mode == model.ModeTRANSIT && segmentItem.TransitInfo != nil {
				detailSegmentItem.LineId = segmentItem.TransitInfo.Id
				detailSegmentItem.LineName = segmentItem.TransitInfo.Name
				detailSegmentItem.LineColor = &segmentItem.TransitInfo.BgColor
				detailSegmentItem.StationCnt = segmentItem.TransitInfo.StopNum
				detailSegmentItem.CanReachStatus = segmentItem.TransitInfo.CanReachStatus
				detailSegmentItem.SubMode = cast.ToString(segmentItem.TransitInfo.Type)
				// 途径站点信息
				if len(segmentItem.TransitInfo.ViaStops) > 0 {
					var viaStops []*model.Stop
					for _, stop := range segmentItem.TransitInfo.ViaStops {
						viaStops = append(viaStops, &model.Stop{
							Id:       *stop.Id,
							Name:     *stop.Name,
							Location: *stop.Location,
						})
					}

					detailSegmentItem.ViaStops = viaStops
				}
			}

			if segmentItem.Mode == model.ModeCAR && segmentItem.CarInfo != nil {
				detailSegmentItem.Etp = util.Int32Ptr2Int32(segmentItem.CarInfo.Etp)
				detailSegmentItem.Etq = util.Int32Ptr2Int32(segmentItem.CarInfo.Etq)
				detailSegmentItem.Eta = int32(segmentItem.CarInfo.Eta)
				if segmentItem.CarInfo.Etq != nil && *segmentItem.CarInfo.Etq > 0 {
					detailSegmentItem.CarIsQu = 1
				}
			}

			planFormatDetailData.SegmentDataList = append(planFormatDetailData.SegmentDataList, &detailSegmentItem)
		}

		// 生成渲染数据
		for _, segmentItem := range plan.SegmentList {
			// 1. 过滤步行 累加步行距离==============================
			if segmentItem.Mode == model.ModeWalk {
				if segmentItem.WalkingInfo != nil {
					walkDistanceSum = walkDistanceSum + segmentItem.WalkingInfo.Distance
				}
				continue
			}

			if segmentItem.Mode == model.ModeTRANSIT && segmentItem.TransitInfo != nil {
				modeCacheMap[segmentItem.TransitInfo.Type] = true
			}

			// 2. 生成segmentList==============================
			modeIndex++
			if segmentItem.Mode == lastSegmentMode.Mode &&
				lastSegmentMode.Mode == model.ModeTRANSIT &&
				lastSegmentMode.TransitInfo.Type == segmentItem.TransitInfo.Type &&
				lastSegmentMode.TransitInfo.Type == model.TypeSubway {
				// 地铁方案不新增端
				modeIndex--
			}
			if len(segmentList) <= modeIndex {
				segmentList = append(segmentList, []*proto.Segment{})
			}

			segmentList[modeIndex] = append(segmentList[modeIndex],
				c.renderSegment(segmentItem.Mode, segmentItem.TransitInfo, segmentItem.BikingInfo),
			)

			// 3. 记录第一个地铁/公交站==============================
			if !isFirstTransit && segmentItem.Mode == model.ModeTRANSIT {
				isFirstTransit = true
				mode := model.TransitTypeMap[segmentItem.TransitInfo.Type]
				FirstTransitStationName = util.ReplaceTag(ctx, c.conf.TransitConfig[mode].EnterStationName, map[string]string{
					"start_station_name": segmentItem.TransitInfo.StartStationName,
					"entrance_name":      segmentItem.TransitInfo.EntranceName,
				})
			}

			lastSegmentMode = segmentItem

			// 4. 累加公交价格==============================
			if segmentItem.Mode != model.ModeCAR {
				transitCostSum += segmentItem.Cost
			}
			modeList = getModeList(segmentItem, modeList)
		}

		o := proto.Plan{
			EstimateTime: GetEstimateTime(ctx, plan.TotalTime),
			PlanType:     model.CombinedTravel,
			MapParams:    c.buildMapParams(plan),
			LinkType:     model.LinkTypeUrl,
			LinkUrl:      c.buildListUrl(),
			RightTitleList: []string{
				util.ReplaceTag(ctx, c.conf.RichTimeMsg, map[string]string{
					"minutes": cast.ToString(plan.TotalTime / 60),
				}),
				util.ReplaceTag(ctx, c.conf.RichFeeMsg, map[string]string{
					"min": util.Fen2Yuan(plan.TotalCost),
				}),
			},
		}

		o.SegmentList = segmentList

		o.Title = c.getTitle(ctx, modeList)

		o.FeeMsg = GetFeeMsg(ctx, plan.TotalCost, c.conf.FeeMsg)

		o.DescList = c.buildDescList(ctx, FirstTransitStationName, walkDistanceSum, plan.StationCount, transitCostSum, c.conf.WalkDesc, modeCacheMap)

		o.Params = make(map[string]string)
		o.Params["fid"] = c.Res.GetFid()
		o.Params["plan_id"] = plan.PlanId
		o.Params["from_type"] = "1"

		if len(plan.ReminderText) > 0 {
			o.TipData = &proto.TipData{
				Text:      plan.ReminderText,
				Icon:      &c.conf.ReminderInfo.Icon,
				TextColor: c.conf.ReminderInfo.TextColor,
				BgColor:   c.conf.ReminderInfo.BgColor,
			}
		}

		result = append(result, &model.PlanFull{
			TotalTime:            plan.TotalTime, // 秒
			Cost:                 plan.TotalCost, // 分
			PlanType:             model.CombinedTravel,
			PlanFormatDetailData: planFormatDetailData,
			Plan:                 o,
			PrivateData: &model.PrivateData{
				CombinedTravelData: plan,
			},
		})
	}

	return
}

func getModeList(segmentItem *combinedTravel.UTRecommendSegmentItem, modeList []model.ModeItem) []model.ModeItem {
	if segmentItem.Mode == model.ModeWalk {
		return modeList
	}
	lastModeIndex := len(modeList) - 1
	if isNotRepeatMode(segmentItem, lastModeIndex, modeList) {
		modeItem := model.ModeItem{
			Mode: segmentItem.Mode,
			Type: 0,
		}

		if segmentItem.Mode == model.ModeTRANSIT {
			modeItem.Type = segmentItem.TransitInfo.Type
		}

		modeList = append(modeList, modeItem)
	}
	return modeList
}

func isNotRepeatMode(segmentItem *combinedTravel.UTRecommendSegmentItem, lastModeIndex int, modeList []model.ModeItem) bool {
	if lastModeIndex == -1 {
		return true
	}

	if modeList[lastModeIndex].Mode != segmentItem.Mode {
		return true
	}

	// 如果mode都是ModeTRANSIT，需要判断type是否相同
	if modeList[lastModeIndex].Mode == model.ModeTRANSIT && segmentItem.Mode == model.ModeTRANSIT {
		return modeList[lastModeIndex].Type != segmentItem.TransitInfo.Type
	}

	return false
}

func (c *CombinedTravelRPC) buildListUrl() string {
	if c.baseRequest != nil && c.baseRequest.AccessKeyId == 9 || c.baseRequest.AccessKeyId == 22 {
		return c.conf.LinkInfo.MiniProgram
	}
	return c.conf.LinkInfo.Default
}

func (c *CombinedTravelRPC) buildMapParams(plan *combinedTravel.PlanItem) map[string]string {
	mapParams := c.conf.MapParams
	mapParams = make(map[string]string)
	mapParams["product_id"] = c.conf.MapParams["productId"]
	mapParams["acc_key"] = c.conf.MapParams["accKey"]
	mapParams["caller_id"] = c.conf.MapParams["callerId"]
	mapParams["fid"] = c.Res.Fid
	mapParams["transit_id"] = plan.TransitId
	mapParams["map_info"] = plan.MapInfo
	return mapParams
}

func (c *CombinedTravelRPC) buildDescList(ctx context.Context, FirstTransitStationName string,
	meter int64, stationCount int32, transitCostSum int64, walkDesc WalkDesc, modeCacheMap map[int32]bool) []*proto.PlanDesc {

	var descList []*proto.PlanDesc

	if meter > 0 {
		descList = getWalkDesc(ctx, descList, meter, walkDesc)
	}

	descList = append(descList, &proto.PlanDesc{
		Content: FirstTransitStationName, // 西二旗(A口)进站
	})

	if stationCount > 0 {

		transitList := make([]string, 0)
		var mode string
		// 获取transit出行方式
		for i, _ := range modeCacheMap {
			transitList = append(transitList, model.TransitTypeMap[i])
		}

		if len(transitList) > 1 {
			mode = model.TransitTypeMap[model.TypeDefault]
		} else {
			mode = transitList[0]
		}

		descList = append(descList, &proto.PlanDesc{
			Content: util.ReplaceTag(ctx, c.conf.TransitConfig[mode].DescList.StationContent, map[string]string{
				"stationCount":   cast.ToString(stationCount),
				"transitCostSum": cast.ToString(util.Fen2Yuan(transitCostSum)),
			}),
		})
	}

	return descList
}

func getWalkDesc(ctx context.Context, descList []*proto.PlanDesc, meter int64, walkDesc WalkDesc) []*proto.PlanDesc {
	var content string
	if meter >= 1000 {
		content = util.ReplaceTag(ctx, walkDesc.KM, map[string]string{
			"kilometer": util.FormatPrice(float64(meter)/1000, 1),
		})
	} else {
		content = util.ReplaceTag(ctx, walkDesc.M, map[string]string{
			"meter": cast.ToString(meter), // 步行673米
		})
	}

	str := "https://img-hxy021.didistatic.com/static/starimg/img/dgMm5RgmTQ1666276313247.png"
	descList = append(descList, &proto.PlanDesc{
		Icon:    &str,
		Content: content,
	})
	return descList
}

func (c *CombinedTravelRPC) renderSegment(segmentMode string, transitData *combinedTravel.UTTransitInfo, bikeData *combinedTravel.UTBikingInfo) *proto.Segment {
	segment := &proto.Segment{}

	switch segmentMode {
	case model.ModeCAR:
		segment.Content = c.conf.Segment.CarInfo.Content
		segment.TextColor = c.conf.Segment.CarInfo.TextColor
		segment.BgColor = c.conf.Segment.CarInfo.BgColor
		segment.BorderColor = c.conf.Segment.CarInfo.BorderColor
	case model.ModeTRANSIT:
		if transitData.Type == model.TypeBus {
			segment.Content = transitData.Name
			segment.TextColor = c.conf.Segment.BusInfo.TextColor
			segment.BgColor = c.conf.Segment.BusInfo.BgColor
			segment.BorderColor = c.conf.Segment.BusInfo.BorderColor
		} else {
			segment.Content = transitData.Name
			segment.TextColor = transitData.TextColor
			segment.BgColor = transitData.BgColor
			segment.BorderColor = transitData.BgColor
		}
	case model.ModeBike:
		segment.Content = bikeData.SegmentName
		segment.TextColor = c.conf.Segment.BikeInfo.TextColor
		segment.BgColor = c.conf.Segment.BikeInfo.BgColor
		segment.BorderColor = c.conf.Segment.BikeInfo.BorderColor
	}
	return segment
}

func GetEstimateTime(ctx context.Context, time int64) string {
	// time : 秒
	minutes := time / 60
	var estimateTime string

	if minutes == 0 {
		minutes = 1
	}
	if minutes > 0 {
		estimateTime = util.ReplaceTag(ctx, "{{{minutes}}}分钟", map[string]string{
			"minutes": cast.ToString(minutes),
		})
	}

	return estimateTime
}

func (c *CombinedTravelRPC) getTitle(ctx context.Context, modeList []model.ModeItem) string {
	var title string
	for i, mode := range modeList {
		var modeTitle string
		switch mode.Mode {
		case model.ModeCAR:
			modeTitle = c.conf.Title.Car
		case model.ModeTRANSIT:
			switch mode.Type {
			case model.TypeBus:
				modeTitle = c.conf.Title.Bus
			case model.TypeSubway:
				modeTitle = c.conf.Title.Subway
			}
		case model.ModeBike:
			modeTitle = c.conf.Title.Bike
		}
		if i == 0 {
			title = modeTitle
		} else {
			title = title + "+" + modeTitle
		}
	}
	return title
}

func GetFeeMsg(ctx context.Context, amount int64, msg string) string {
	var feeMsg string
	feeMsg = util.ReplaceTag(ctx, msg, map[string]string{
		"min": util.Fen2Yuan(amount),
	})
	return feeMsg
}

type Title struct {
	Car    string `json:"car"`
	Subway string `json:"subway"`
	Bike   string `json:"bike"`
	Bus    string `json:"bus"`
}
type DescListDCMP2 struct {
	Station1       string `json:"station_1"`
	Station2       string `json:"station_2"`
	StationContent string `json:"station_content"`
}
type Reminder struct {
	Icon      string `json:"icon"`
	TextColor string `json:"text_color"`
	BgColor   string `json:"bg_color"`
}
type LinkInfo struct {
	MiniProgram string `json:"mini_program"`
	Default     string `json:"default"`
}
type Segment struct {
	CarInfo  Info `json:"car_info"`
	BikeInfo Info `json:"bike_info"`
	BusInfo  Info `json:"bus_info"`
}
type Info struct {
	Content     string `json:"content"`
	TextColor   string `json:"text_color"`
	BgColor     string `json:"bg_color"`
	BorderColor string `json:"border_color"`
}
type WalkDesc struct {
	KM string `json:"km"`
	M  string `json:"m"`
}

type CombinedDCMPConf struct {
	Title            Title                  `json:"title"` // 方案标题
	MapParams        map[string]string      `json:"map_params"`
	DescList         DescListDCMP2          `json:"desc_list"`
	FeeMsg           string                 `json:"fee_msg"`
	WalkDesc         WalkDesc               `json:"walk_desc"`
	EnterStationName string                 `json:"enter_station_name"`
	ReminderInfo     Reminder               `json:"reminder_info"`
	LinkInfo         LinkInfo               `json:"link_url"`
	Segment          Segment                `json:"segment"`
	TransitConfig    map[string]TransitItem `json:"transit_config"`
	RichTimeMsg      string                 `json:"rich_time_msg"`
	RichFeeMsg       string                 `json:"rich_fee_msg"`
}

type TransitItem struct {
	EnterStationName string        `json:"enter_station_name"`
	DescList         DescListDCMP2 `json:"desc_list"`
}

func initCombinedTravelDCMPConf(ctx context.Context) (*CombinedDCMPConf, error) {
	conf := new(CombinedDCMPConf)
	str := dcmp.GetDcmpContent(ctx, "one_stop-combined_travel_new", nil)
	err := json.Unmarshal([]byte(str), conf)
	if err != nil || conf == nil {
		return nil, err
	}
	return conf, nil
}
