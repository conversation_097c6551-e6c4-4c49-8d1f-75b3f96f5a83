package business_estimate

import (
	"context"
	"encoding/json"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/render/public/estimate_extra"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/view/common_view_logic"
	"git.xiaojukeji.com/gulfstream/mamba/view/render/price_info_desc_list"
	LegoContext "git.xiaojukeji.com/lego/context-go"
	"git.xiaojukeji.com/nuwa/trace"
)

func CheckParams(ctx context.Context, req *proto.B2BEstimateReq) (error int) {
	// todo 增加必要参数判断 sxm
	if req == nil {
		return consts.ErrnoParams
	}
	if req.GetToken() == "" || req.GetToLat() == 0 || req.GetToLng() == 0 || req.GetFromLat() == 0 || req.GetFromLng() == 0 {
		return consts.ErrnoParams
	}

	return consts.NoErr
}

func BuildResponse(ctx context.Context, baseReqData *models.BaseReqData,
	productInfoFulls []*biz_runtime.ProductInfoFull) (rsp *proto.B2BEstimateData, err int) {

	if len(productInfoFulls) == 0 {
		return nil, consts.ErrnoNoProductOpen
	}

	rsp = &proto.B2BEstimateData{
		Products:        []*proto.B2BBizItem{},
		EstimateTraceId: LegoContext.GetTrace(ctx).GetTraceId(),
	}

	if baseReqData.CommonInfo.OrderType != 1 {
		// 预约单不支持多选
		rsp.IsSupportMultiSelection = true
	}

	var holidayFee float64
	for _, product := range productInfoFulls {
		b2BProduct, errno := buildB2BProduct(product)
		if errno != consts.NoErr {
			continue
		}
		b2BBill, errno := buildB2BBill(product)
		if errno != consts.NoErr {
			continue
		}

		if fee := GetFeeFromFeeDetail(b2BBill, price_info_desc_list.RedPacketFee); fee > 0 {
			holidayFee = fee
		}

		b2BMember, errno := buildB2BMember(product)
		if errno != consts.NoErr && errno != consts.ErronoGetMemberInfo {
			continue
		}

		b2BFee := buildB2BFee(product)
		if b2BFee == nil {
			continue
		}

		b2BBiz := buildB2BBiz(ctx, product)
		rsp.Products = append(rsp.Products, &proto.B2BBizItem{
			Product:    b2BProduct,
			Bill:       b2BBill,
			Member:     b2BMember,
			Fee:        b2BFee,
			Biz:        b2BBiz,
			EstimateId: product.Product.EstimateID,
		})
	}

	rsp.PluginPageInfo = estimate_extra.GetPluginPageInfo(ctx, baseReqData, holidayFee, false)
	return rsp, consts.NoErr
}

// GetFeeFromFeeDetail ...
func GetFeeFromFeeDetail(billInfo *proto.B2BBill, key string) float64 {
	if billInfo != nil && len(billInfo.FeeDetailInfo) > 0 {
		return billInfo.FeeDetailInfo[key]
	}
	return 0
}

func buildB2BProduct(product *biz_runtime.ProductInfoFull) (b2BProduct *proto.B2BProduct, errno int) {
	if product == nil || product.Product == nil {
		return nil, consts.ErrnoNoProductOpen
	}
	b2BProduct = &proto.B2BProduct{
		ProductCategory: int32(product.Product.ProductCategory),
		OrderType:       int32(product.Product.OrderType),
		ProductId:       int32(product.Product.ProductID),
		BusinessId:      int32(product.Product.BusinessID),
		RequireLevel:    int32(product.Product.RequireLevelInt),
		LevelType:       product.Product.LevelType,
		ComboType:       int32(product.Product.ComboType),
		IsSpecialPrice:  product.Product.IsSpecialPrice,
		AirportType:     &product.Product.AirportType,
		RailwayType:     &product.Product.RailwayType,
		LongRentType:    int32(product.Product.LongRentType),
	}

	if product.Product.BizInfo != nil {
		b2BProduct.CarpoolSeatNum = &product.Product.BizInfo.CarpoolSeatNum
	}

	return b2BProduct, consts.NoErr
}

func buildB2BBill(product *biz_runtime.ProductInfoFull) (b2BBill *proto.B2BBill, errno int) {
	if product == nil || product.BillDetail == nil {
		return nil, consts.ErrnoGetNoBillInfo
	}
	b2BBill = &proto.B2BBill{
		DriverMetre:                product.BillDetail.DriverMetre,
		DriverMinute:               product.BillDetail.DriverMinute,
		DynamicTotalFee:            product.BillDetail.DynamicTotalFee,
		TotalFee:                   product.BillDetail.TotalFee,
		CapPrice:                   product.BillDetail.CapPrice,
		HighwayFee:                 float64(product.BillDetail.HighwayFee),
		CrossCityFee:               product.BillDetail.CrossCityFee,
		LimitFee:                   product.BillDetail.LimitFee,
		FeeDetailInfo:              product.BillDetail.FeeDetailInfo,
		CarpoolFailDynamicTotalFee: *product.BillDetail.CarpoolFailDynamicTotalFee,
		IsHasDynamic:               product.BillDetail.IsHasDynamic,
		DynamicDiffPrice:           product.BillDetail.DynamicDiffPrice,
		CountPriceType:             int64(product.BillDetail.CountPriceType),
		DynamicTimes:               product.BillDetail.DynamicTimes,
		DynamicPrice:               int64(product.BillDetail.DynamicPrice),
		PreTotalFee:                product.BillDetail.PreTotalFee,
		BasicTotalFee:              product.BillDetail.BasicTotalFee,
		DriverRealtimeTotalFee:     product.BillDetail.DriverRealtimeTotalFee,
	}
	routeIdList := product.BillDetail.RouteIdList
	if len(routeIdList) != 0 {
		var routeIds []string
		for _, v := range routeIdList {
			routeIds = append(routeIds, strconv.FormatInt(v, 10))
		}
		b2BBill.RouteIdList = routeIds
	}
	carpoolSeatConfig := product.BillDetail.CarpoolSeatConfig
	if len(carpoolSeatConfig) != 0 {
		b2BBill.CarpoolSeatConfig = []int64{}
		for _, item := range carpoolSeatConfig {
			b2BBill.CarpoolSeatConfig = append(b2BBill.CarpoolSeatConfig, int64(item))
		}
	}

	return b2BBill, consts.NoErr
}

func buildB2BMember(product *biz_runtime.ProductInfoFull) (b2BBMember *proto.B2BMember, errno int) {
	if product == nil || product.Product == nil || product.Product.BizInfo == nil || product.Product.BizInfo.MemberProfile == nil {
		return nil, consts.ErronoGetMemberInfo
	}

	protectionFee := 0.0
	if product.BillDetail != nil {
		dynamicPriceWithoutMemberCapping := product.BillDetail.DynamicPriceWithoutMemberCapping
		memberDynamicCapping := product.BillDetail.MemberDynamicCapping
		if dynamicPriceWithoutMemberCapping != nil && memberDynamicCapping != nil && *memberDynamicCapping > 0.0 {
			protectionFee = *dynamicPriceWithoutMemberCapping - *memberDynamicCapping
		}
	}

	if protectionFee < 0 {
		protectionFee = 0.0
	}

	// 保留小数点后两位
	protectionFee = util.RoundAbs(protectionFee, 2)
	b2BBMember = &proto.B2BMember{
		UseDpaSelected: product.Product.BizInfo.UseDpaSelected,
		ProtectionFee:  protectionFee,
	}

	level_id := product.Product.BizInfo.MemberProfile.LevelId
	if level_id != nil {
		b2BBMember.LevelId, _ = level_id.(int32)
	}

	levelName := product.Product.BizInfo.MemberProfile.LevelName
	if levelName != nil {
		b2BBMember.LevelName, _ = levelName.(string)
	}

	return b2BBMember, consts.NoErr
}

func buildB2BFee(product *biz_runtime.ProductInfoFull) (b2BFee []*proto.B2BFee) {
	if product == nil || len(product.DiscountInfo) == 0 {
		return nil
	}
	b2BFee = []*proto.B2BFee{}
	for _, item := range product.DiscountInfo {
		if item == nil {
			continue
		}
		b2BFee = append(b2BFee, &proto.B2BFee{
			EstimateFee:             item.EstimateFee,
			WithoutMemberProtectFee: item.WithoutMemberProtectFee,
		})
	}
	return b2BFee
}

func buildB2BBiz(ctx context.Context, product *biz_runtime.ProductInfoFull) (b2BBiz *proto.B2BBiz) {
	b2BBiz = &proto.B2BBiz{}
	if product == nil || product.Product == nil || product.Product.BizInfo == nil || product.BaseReqData == nil {
		return b2BBiz
	}

	bizInfo := product.Product.BizInfo

	//填充custom_feature
	if bizInfo.CustomFeatureList != nil {
		customFeatureList := make([]*CustomFeature, 0)
		for _, feature := range bizInfo.CustomFeatureList {
			customFeature := &CustomFeature{
				Id: feature.ServiceId,
			}
			customFeatureList = append(customFeatureList, customFeature)
		}

		if customFeatureStr, err := json.Marshal(customFeatureList); err == nil {
			feature := string(customFeatureStr)
			b2BBiz.CustomFeature = &feature
		}

	}

	// taxi 填充 IsShowTaxiFee 字段
	isShowTaxiFee := common_view_logic.CheckTaxiAppendShowEstimateFee(ctx, product)
	b2BBiz.IsShowTaxiFee = &isShowTaxiFee

	// 豪华车 填充 display_tags 字段
	if int(product.Product.ProductID) != product_id.ProductIdBusinessLuxCar {
		return b2BBiz
	}

	if bizInfo.DisplayTags != "" {
		b2BBiz.DisplayTags = &(product.Product.BizInfo.DisplayTags)
	}

	// 豪华车 填充 designated_driver_text 字段
	dirver := product.BaseReqData.CommonInfo.LuxurySelectDriver
	if dirver == "" || dirver == "0" {
		return
	}
	conf := make(map[string]string, 0)
	dcmpStr := dcmp.GetDcmpContent(ctx, "prefer_info-designated_driver_config", nil)
	err := json.Unmarshal([]byte(dcmpStr), &conf)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "b2b buildB2BBiz dcmp prefer_info-designated_driver_config unmarshal error: %s", err.Error())
	}
	driverName := conf[dirver]
	if driverName != "" {
		b2BBiz.DesignatedDriverText = &driverName
	}

	return b2BBiz
}
