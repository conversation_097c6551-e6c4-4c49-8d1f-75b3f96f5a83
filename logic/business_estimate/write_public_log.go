package business_estimate

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	LegoContext "git.xiaojukeji.com/lego/context-go"
)

const OperaKeyEstimateData = "g_business_estimate_price"

func AddPublicLog(ctx context.Context, products []*biz_runtime.ProductInfoFull) {
	for _, product := range products {
		if "" == product.Product.EstimateID {
			continue
		}
		writeProductLog(ctx, product)
	}
}

// ...
func writeProductLog(ctx context.Context, full *biz_runtime.ProductInfoFull) {
	if full == nil || full.BaseReqData == nil || full.Product == nil || full.BillDetail == nil || len(full.DiscountInfo) == 0 {
		return
	}
	reqData, product, bill, discount, payInfo := full.BaseReqData, full.Product, full.BillDetail, full.DiscountInfo[0], full.PayInfo
	orderNTuple, _ := json.Marshal(product)
	logInfo := make(map[string]interface{})

	// ..... 基础信息 .....
	logInfo["estimate_trace_id"] = LegoContext.GetTrace(ctx).GetTraceId()
	logInfo["estimate_id"] = product.EstimateID
	logInfo["client_type"] = reqData.CommonInfo.ClientType
	logInfo["access_key_id"] = reqData.CommonInfo.AccessKeyID
	logInfo["appversion"] = reqData.CommonInfo.AppVersion
	logInfo["imei"] = reqData.CommonInfo.Imei
	logInfo["pLang"] = reqData.CommonInfo.Lang
	logInfo["channel"] = reqData.CommonInfo.Channel
	logInfo["menu_id"] = reqData.CommonInfo.MenuID
	logInfo["page_type"] = reqData.CommonInfo.PageType
	logInfo["origin_page_type"] = reqData.CommonInfo.PageType
	logInfo["xpsid"] = reqData.CommonInfo.Xpsid
	logInfo["xpsid_root"] = reqData.CommonInfo.XpsidRoot
	logInfo["source_id"] = reqData.CommonInfo.SourceID

	logInfo["product_category"] = product.ProductCategory
	logInfo["product_id"] = product.ProductID
	logInfo["require_level"] = product.RequireLevel
	logInfo["airport_type"] = product.AirportType
	logInfo["is_special_price"] = product.IsSpecialPrice
	logInfo["carpool_type"] = product.CarpoolType
	logInfo["order_n_tuple"] = string(orderNTuple)
	logInfo["order_type"] = product.OrderType
	logInfo["scene_type"] = product.SceneType
	logInfo["combo_type"] = product.ComboType

	logInfo["from_name"] = util.StringEscape(reqData.AreaInfo.FromName)
	logInfo["to_name"] = util.StringEscape(reqData.AreaInfo.ToName)
	logInfo["area"] = reqData.AreaInfo.Area
	logInfo["district"] = reqData.AreaInfo.District
	logInfo["county"] = reqData.AreaInfo.FromCounty
	logInfo["to_county"] = reqData.AreaInfo.ToCounty
	logInfo["to_area"] = reqData.AreaInfo.ToArea
	logInfo["flng"] = reqData.AreaInfo.FromLng
	logInfo["flat"] = reqData.AreaInfo.FromLat
	logInfo["tlng"] = reqData.AreaInfo.ToLng
	logInfo["tlat"] = reqData.AreaInfo.ToLat
	logInfo["current_lng"] = reqData.AreaInfo.CurLng
	logInfo["current_lat"] = reqData.AreaInfo.CurLat

	logInfo["biz_user_type"] = reqData.PassengerInfo.UserType
	logInfo["phone"] = reqData.PassengerInfo.Phone
	logInfo["pid"] = reqData.PassengerInfo.PID

	logInfo["call_car_type"] = reqData.CommonInfo.CallCarType
	if payInfo != nil {
		logInfo["default_pay_type"] = payInfo.DefaultPayType
	}

	// ..... 价格信息 .....
	logInfo["estimate_fee"] = discount.EstimateFee
	logInfo["discount_info"] = util.JustJsonEncode(discount)
	logInfo["driver_metre"] = bill.DriverMetre //nolint
	logInfo["time_cost"] = bill.DriverMinute
	logInfo["dynamic_total_fee"] = bill.DynamicTotalFee
	logInfo["total_fee"] = bill.TotalFee
	logInfo["pre_total_fee"] = bill.PreTotalFee
	logInfo["cap_price"] = bill.CapPrice
	logInfo["is_dynamic"] = bill.IsHasDynamic
	logInfo["dynamic_diff_price"] = bill.DynamicDiffPrice
	logInfo["count_price_type"] = bill.CountPriceType
	logInfo["dynamic_times"] = bill.DynamicTimes
	logInfo["is_carpool_open"] = bill.IsCarpoolOpen

	log.Public.Public(ctx, OperaKeyEstimateData, logInfo)
}
