package business_tailor_service

import (
	"context"
	"fmt"
	Sps "git.xiaojukeji.com/dirpc/dirpc-go-http-Sps"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"
	"runtime/debug"
)

type TailorServiceData struct {
	baseReq     *BaseInfos
	_rpcProcess []RpcProcess //需要并发执行的rpc
	RpcInfo     *RpcResultInfo
}

func NewTailorService(ctx context.Context, req *proto.LuxMultiEstimatePriceRequest) (*TailorServiceData, int) {
	baseInfo, errno := InitBaseInfos(ctx, req)
	if consts.NoErr != errno {
		return nil, errno
	}

	return &TailorServiceData{
		baseReq: baseInfo,
		RpcInfo: &RpcResultInfo{},
	}, 0
}

func GetTailorService(ctx context.Context, req *proto.LuxMultiEstimatePriceRequest) (rspData *proto.BusinessTailorServiceData, errno int) {
	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
		}
	}()

	// 0.参数校验
	if errno = CheckParams(req); consts.NoErr != errno {
		return nil, errno
	}

	//1. 函数初始化
	tailor, errno := NewTailorService(ctx, req)
	if consts.NoErr != errno {
		return nil, errno
	}

	// 2. 构建 rpc 并发
	tailor.RegisterRpc(ctx)
	tailor.multiExec(ctx)

	//3. 渲染
	rspData = tailorRender(ctx, tailor)
	return rspData, consts.NoErr
}

func (t *TailorServiceData) RegisterRpc(ctx context.Context) {
	//偏好页tailor service，本期先不接
	//if tailorServiceRpc := TailorServiceRPC(ctx, t.baseReq); tailorServiceRpc != nil {
	//	t.RegisterRpcProcess(tailorServiceRpc)
	//}

	//会员信息
	if memberRpc := MemberRPC(ctx, t.baseReq); memberRpc != nil {
		t.RegisterRpcProcess(memberRpc)
	}

	//增值服务
	if additionalServiceRpc := AdditionServiceRPC(ctx, t.baseReq); additionalServiceRpc != nil {
		t.RegisterRpcProcess(additionalServiceRpc)
	}
}

func (t *TailorServiceData) RegisterRpcProcess(filter RpcProcess) {
	if t._rpcProcess == nil {
		t._rpcProcess = make([]RpcProcess, 0)
	}
	t._rpcProcess = append(t._rpcProcess, filter)
}

func (t *TailorServiceData) multiExec(ctx context.Context) {
	if len(t._rpcProcess) <= 0 {
		return
	}

	finishedRpc := multiExecWithRpc(ctx, t._rpcProcess, 5000)
	for _, rpc := range finishedRpc {
		rpc.BuildResultInfo(ctx, t.RpcInfo)
	}
}

func tailorRender(ctx context.Context, tailorService *TailorServiceData) *proto.BusinessTailorServiceData {
	rspData := &proto.BusinessTailorServiceData{
		PreferInfo: tailorService.RpcInfo.HundunTailorData,
	}

	//有订单号，说明不是预估进来的
	if tailorService.baseReq.OrderId != "" {
		return rspData
	}

	//专车个性化服务准入
	rspData.UpgradeInfo = GetUpgradeInfo(ctx, tailorService)
	return rspData
}

func GetUpgradeInfo(ctx context.Context, tailorService *TailorServiceData) (upgradeInfos []*proto.BusinessServiceData) {
	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
		}
	}()

	//获得个性化服务对应价格
	spsResult := getSpsInfo(ctx, tailorService.baseReq, tailorService.RpcInfo.AdditionalServiceData, tailorService.RpcInfo.MemberPrivileges)
	memberSource := tailorService.RpcInfo.MemberSource

	//构建渲染
	for _, serviceItem := range tailorService.RpcInfo.AdditionalServiceData {
		if serviceItem.ServiceId == 0 {
			// 异常情况
			continue
		}

		//服务信息
		upgradeInfo := &proto.BusinessServiceData{
			Id:          cast.ToInt32(serviceItem.ServiceId),
			ServiceDesc: serviceItem.GetTips(),
			Status:      serviceItem.Status,
		}

		if spsResult != nil {
			//构建服务价格信息
			SetAdditionalPrice(ctx, spsResult[serviceItem.ServiceId], upgradeInfo, memberSource)
		}

		upgradeInfos = append(upgradeInfos, upgradeInfo)
	}

	return upgradeInfos
}

func SetAdditionalPrice(ctx context.Context, feeInfo *Sps.FeeItem, upgradeInfo *proto.BusinessServiceData, memberSource string) {
	defer func() {
		if r := recover(); r != nil {
			errMsg := fmt.Errorf("PANIC=%v", r)
			log.Trace.Errorf(ctx, trace.DLTagUndefined, "stack: %s \n %v", errMsg, string(debug.Stack()))
		}
	}()

	if feeInfo == nil {
		//无价格 不构建数据
		return
	}

	//基础价格信息
	upgradeInfo.Price = feeInfo.GetFeePrice()
	upgradeInfo.FinalPrice = feeInfo.GetFeePrice()
	if len(feeInfo.GetDiscountInfo()) != 0 {
		//折扣信息
		for _, discountItem := range feeInfo.GetDiscountInfo() {
			upgradeInfo.FinalPrice = feeInfo.GetDiscountPrice()
			upgradeInfo.DiscountPrice = discountItem.GetFeePrice()
			upgradeInfo.DiscountName = discountItem.GetFeeName()
			upgradeInfo.DiscountTag = memberSource
			break //后面截断
		}
	}

	return
}
