package render

import (
	"context"

	combinedTravel "git.xiaojukeji.com/dirpc/dirpc-go-http-CombinedTravel"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v2/model"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

type CombinedTravelRender struct {
	*BaseRender
	startStationName string // 第一个地铁站名称
	entranceName     string
	startSubMode     int32
	transitCostSum   int64 // 地铁/公交总费用-> 渲染"x元"
	conf             CombinedDCMPConf
	planData         *combinedTravel.PlanItem
}

func NewCombinedTravelRender(ctx context.Context, base *BaseRender) *CombinedTravelRender {

	conf, err := initCombinedTravelDCMPConf(ctx)
	if err != nil {
		log.Trace.Warnf(ctx, PlanRenderEngineTag, "init combined travel service dcmp error || err = %v", err)
	}

	return &CombinedTravelRender{BaseRender: base, conf: conf}
}

type CombinedDCMPConf struct {
	StationCountDesc string            `json:"station_count_desc"`
	MapParams        map[string]string `json:"map_params"`
	LinkUrl          map[string]string `json:"link_url"`
}

func initCombinedTravelDCMPConf(ctx context.Context) (CombinedDCMPConf, error) {
	conf := CombinedDCMPConf{}
	str := dcmp.GetDcmpContent(ctx, "one_stop_85-combined_travel", nil)
	err := jsoniter.UnmarshalFromString(str, &conf)
	if err != nil {
		return conf, err
	}
	return conf, nil
}

func (c *CombinedTravelRender) Render(ctx context.Context, plan *model.PlanFull, rec *AthenaApiv3.UTRecItem) *proto.NormalPlanItem {
	data := plan.GetPrivateData()
	if data == nil || data.CombinedTravelData == nil {
		log.Trace.Warnf(ctx, PlanRenderEngineTag, "combined travel plan render err || data =%v", data)
		return nil
	}
	var (
		bIsFirstTransit  = true // 记录是否第一个地铁
		StartStationName string // 第一个地铁站名称
		EntranceName     string
		StartSubMode     int32
		transitCostSum   int64 // 地铁/公交总费用-> 渲染"x元"
	)

	for _, segmentItem := range data.CombinedTravelData.SegmentList {

		if bIsFirstTransit && segmentItem.Mode == model.ModeTRANSIT && segmentItem.TransitInfo != nil {
			bIsFirstTransit = false
			StartStationName = segmentItem.TransitInfo.StartStationName
			EntranceName = segmentItem.TransitInfo.EntranceName
			StartSubMode = segmentItem.TransitInfo.Type
		}

		if segmentItem.Mode != model.ModeCAR {
			transitCostSum += segmentItem.Cost
		}
	}

	c.planData = data.CombinedTravelData
	c.startSubMode = StartSubMode
	c.entranceName = EntranceName
	c.startStationName = StartStationName
	c.transitCostSum = transitCostSum

	return &proto.NormalPlanItem{
		PlanType:       model.CombinedTravel,
		PlanTitle:      c.renderPlanTitle(plan),
		TipData:        c.renderTipData(&data.CombinedTravelData.ReminderText),
		SegmentList:    c.renderSegmentList(plan),
		DescList:       c.renderDescList(ctx, plan, c.buildWalkDesc, c.buildStationDesc, c.buildStationCountDesc),
		RightTitleList: c.renderRightTitleList(ctx, plan),
		LinkType:       model.LinkTypeUrl,
		LinkUrl:        c.buildLinkUrl(),
		Params:         c.buildParams(plan),
		MapParams:      c.buildMapParams(plan),
		ExtraData:      nil,
		RecommendTag:   c.renderRecommendTag(rec),
	}
}

// 上站描述渲染
func (c *CombinedTravelRender) buildStationDesc(ctx context.Context, plan *model.PlanFull) *proto.PlanDesc {
	if c.startStationName == "" {
		return nil
	}

	tag := map[string]string{
		"station_name":  c.startStationName,
		"entrance_name": c.entranceName,
	}

	if c.startSubMode == model.TypeBus {
		return &proto.PlanDesc{
			Content: util.ReplaceTag(ctx, c.baseConfig.StationDesc.Station1, tag),
		}
	}

	return &proto.PlanDesc{
		Content: util.ReplaceTag(ctx, c.baseConfig.StationDesc.Station2, tag),
	}
}

// 站点数量渲染
func (c *CombinedTravelRender) buildStationCountDesc(ctx context.Context, plan *model.PlanFull) *proto.PlanDesc {
	if c.planData == nil || c.planData.StationCount <= 0 {
		return nil
	}

	return &proto.PlanDesc{
		Content: util.ReplaceTag(ctx, c.conf.StationCountDesc, map[string]string{
			"stationCount":   cast.ToString(c.planData.StationCount),
			"transitCostSum": cast.ToString(util.Fen2Yuan(c.transitCostSum)),
		}),
	}
}

func (c *CombinedTravelRender) buildMapParams(plan *model.PlanFull) map[string]string {
	return map[string]string{
		"product_id": c.conf.MapParams["productId"],
		"acc_key":    c.conf.MapParams["accKey"],
		"caller_id":  c.conf.MapParams["callerId"],
		"fid":        plan.GetPrivateData().Fid,
		"transitId":  c.planData.TransitId,
		"map_info":   c.planData.MapInfo,
	}
}

func (c *CombinedTravelRender) buildParams(plan *model.PlanFull) map[string]string {
	return map[string]string{
		"fid":       plan.GetPrivateData().Fid,
		"transitId": c.planData.TransitId,
		"from_type": "1",
		"plan_id":   c.planData.PlanId,
	}
}

func (c *CombinedTravelRender) buildLinkUrl() string {
	return c.conf.LinkUrl[cast.ToString(c.baseRequest.AccessKeyId)]
}
