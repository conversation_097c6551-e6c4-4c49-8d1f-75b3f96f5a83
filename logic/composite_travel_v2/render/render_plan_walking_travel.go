package render

import (
	"context"
	"strings"

	walkApi "git.xiaojukeji.com/dirpc/dirpc-go-http-DolphinApiService"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v2/model"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

type WalkPlanRender struct {
	*BaseRender

	planData *walkApi.PassengerWalkRouteRes
	conf     WalkingDCMPConf
}

func NewWalkPlanRender(ctx context.Context, base *BaseRender) *WalkPlanRender {
	conf, err := initWalkingDCMPConf(ctx)
	if err != nil {
		log.Trace.Warnf(ctx, PlanRenderEngineTag, "init walk service dcmp error || err = %v", err)
	}
	return &WalkPlanRender{BaseRender: base, conf: conf}
}

type WalkingDCMPConf struct {
	Duration string            `json:"duration"`
	DescTip  string            `json:"desc_tip"`
	LinkUrl  map[string]string `json:"link_url"`
}

func initWalkingDCMPConf(ctx context.Context) (WalkingDCMPConf, error) {
	conf := WalkingDCMPConf{}
	str := dcmp.GetDcmpContent(ctx, "one_stop_85-walk_travel", nil)
	err := jsoniter.UnmarshalFromString(str, &conf)
	if err != nil {
		return conf, err
	}
	return conf, nil
}

func (w *WalkPlanRender) Render(ctx context.Context, plan *model.PlanFull, rec *AthenaApiv3.UTRecItem) *proto.NormalPlanItem {
	data := plan.GetPrivateData()
	if data == nil || data.WalkData == nil {
		log.Trace.Warnf(ctx, PlanRenderEngineTag, "walk plan render err || data =%v", data)
		return nil
	}

	w.planData = data.WalkData
	return &proto.NormalPlanItem{
		PlanType:       model.WalkingTravel,
		PlanTitle:      w.renderPlanTitle(plan),
		TipData:        nil,
		SegmentList:    w.renderSegmentList(plan),
		DescList:       w.renderDescList(ctx, plan, w.buildWalkDisDesc, w.buildWalkTipDesc),
		RightTitleList: w.renderRightTitleList(ctx, plan, w.buildEstimateTime),
		LinkType:       model.LinkTypeUrl,
		LinkUrl:        w.buildLinkUrl(ctx),
		Params:         nil,
		MapParams:      w.buildMapParams(),
		ExtraData:      nil,
		RecommendTag:   w.renderRecommendTag(rec),
	}
}

func (w *WalkPlanRender) buildEstimateTime(ctx context.Context, plan *model.PlanFull) string {
	if w.planData.GetDurationString() != "" {
		split := strings.Split(w.planData.GetDurationString(), " ")
		if len(split) == 2 {
			return util.ReplaceTag(ctx, w.conf.Duration, map[string]string{
				"duration": split[0],
				"unit":     split[1],
			})
		}
	}

	return ""
}

func (w *WalkPlanRender) buildWalkDisDesc(ctx context.Context, plan *model.PlanFull) *proto.PlanDesc {
	disDes := strings.ReplaceAll(w.planData.GetDistString(), " ", "")
	if disDes == "" {
		return nil
	}
	return &proto.PlanDesc{
		Icon:    &w.BaseRender.baseConfig.WalkDesc.WalkIcon,
		Content: disDes,
	}
}

func (w *WalkPlanRender) buildWalkTipDesc(ctx context.Context, plan *model.PlanFull) *proto.PlanDesc {
	return &proto.PlanDesc{
		Content: w.conf.DescTip,
	}
}

func (w *WalkPlanRender) buildLinkUrl(ctx context.Context) string {
	url := w.conf.LinkUrl[cast.ToString(w.baseRequest.AccessKeyId)]

	return util.ReplaceTag(ctx, url, map[string]string{
		"from_lng":    cast.ToString(w.baseRequest.FromLng),
		"from_lat":    cast.ToString(w.baseRequest.FromLat),
		"from_name":   w.baseRequest.FromName,
		"from_poi_id": w.baseRequest.FromPoiId,
		"to_lng":      cast.ToString(w.baseRequest.ToLng),
		"to_lat":      cast.ToString(w.baseRequest.ToLat),
		"to_name":     w.baseRequest.ToName,
		"to_poi_id":   w.baseRequest.ToPoiId,
	})
}

func (w *WalkPlanRender) buildMapParams() map[string]string {

	return map[string]string{
		"route_id": cast.ToString(w.planData.GetRouteId()),
	}
}
