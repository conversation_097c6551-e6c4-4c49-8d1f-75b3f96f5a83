package rpc

import (
	"context"

	combinedTravel "git.xiaojukeji.com/dirpc/dirpc-go-http-CombinedTravel"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/combined_travel_v2"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v2/model"
	"github.com/spf13/cast"
)

type CombinedTravelRPC struct {
	baseRequest *model.Request
	Res         *combinedTravel.UTRecommendRespData
}

const (
	CombinedTravelRPCLogTag = "CombinedTravelRPC"
)

func NewCombinedTravelRPC(ctx context.Context, baseRequest *model.Request) *CombinedTravelRPC {
	req := baseRequest.CompositeTravelV2Req
	// 参数校验
	if req.FromLat == 0 || req.FromLng == 0 || req.ToLat == 0 || req.ToLng == 0 ||
		req.MapType == "" || req.FromPoiId == "" {
		log.Trace.Infof(ctx, CombinedTravelRPCLogTag, "req is lost")
		return nil
	}

	// 开城校验
	if !model.GetTravelTypeIsOpen(ctx, baseRequest, model.CombinedTravel) {
		return nil
	}

	return &CombinedTravelRPC{
		baseRequest: baseRequest,
	}
}

func (c *CombinedTravelRPC) Execute(ctx context.Context, req *model.Request) {
	recommend := combined_travel_v2.UTRecommend(ctx, &combinedTravel.UTRecommendReq{
		Uid:          int64(req.UserInfo.UID),
		Pid:          int64(req.UserInfo.PID),
		Phone:        req.UserInfo.Phone,
		Lang:         req.GetLang(),
		Token:        req.GetToken(),
		AppVersion:   req.AppVersion,
		AccessKeyId:  req.AccessKeyId,
		Channel:      req.Channel,
		ClientType:   req.ClientType,
		PlatformType: req.PlatformType,
		Lat:          req.Lat,
		Lng:          req.Lng,
		Ddfp:         req.GetDdfp(),
		FromLat:      req.FromLat,
		FromLng:      req.FromLng,
		FromName:     req.FromName,
		FromAddress:  req.FromAddress,
		FromPoiId:    req.FromPoiId,
		ToLat:        req.ToLat,
		ToLng:        req.ToLng,
		ToName:       req.ToName,
		ToPoiId:      req.ToPoiId,
		ToAddress:    req.ToAddress,
		MapType:      req.GetMapType(),
		ExtraInfo: map[string]string{
			"version_tag": model.Version85,
		},
	})

	if recommend == nil || recommend.Data == nil {
		return
	}
	c.Res = recommend.Data
	return
}

func (c *CombinedTravelRPC) GetModel(ctx context.Context) (result []*model.PlanFull) {
	planList := c.Res.GetPlanList()
	if len(planList) < 1 {
		return
	}

	for _, plan := range planList {
		// 生成模型数据
		var planFormatDetailData model.PlanFormatDetailData // 方案详情数据模型
		for _, segmentItem := range plan.SegmentList {
			var detailSegmentItem *model.SegmentItem
			switch segmentItem.Mode {
			case model.ModeCAR:
				detailSegmentItem = c.buildCarSegment(segmentItem)
			case model.ModeTRANSIT:
				detailSegmentItem = c.buildTransitSegment(segmentItem)
			default:
				detailSegmentItem = c.buildNormalSegment(segmentItem)
			}

			planFormatDetailData.SegmentDataList = append(planFormatDetailData.SegmentDataList, detailSegmentItem)
		}

		result = append(result, &model.PlanFull{
			TotalTime:            plan.TotalTime, // 秒
			Cost:                 plan.TotalCost, // 分
			PlanType:             model.CombinedTravel,
			PlanFormatDetailData: planFormatDetailData,
			TipData:              &plan.ReminderText,
			PrivateData: &model.PrivateData{
				Fid:                c.Res.GetFid(),
				CombinedTravelData: plan,
				PlanId:             plan.PlanId,
			},
		})
	}

	return
}

func (c *CombinedTravelRPC) buildNormalSegment(segmentItem *combinedTravel.UTRecommendSegmentItem) *model.SegmentItem {
	return &model.SegmentItem{
		Mode:     segmentItem.Mode,
		Distance: segmentItem.Distance,
		Time:     segmentItem.Duration,
		Cost:     segmentItem.Cost,
	}
}

func (c *CombinedTravelRPC) buildCarSegment(segmentItem *combinedTravel.UTRecommendSegmentItem) *model.SegmentItem {
	if segmentItem.CarInfo == nil {
		return c.buildNormalSegment(segmentItem)
	}

	var CarIsQu int32
	if segmentItem.CarInfo.Etq != nil && *segmentItem.CarInfo.Etq > 0 {
		CarIsQu = 1
	}
	// @TODO etq len缺失,CarIsQu 通过*etq判断
	return &model.SegmentItem{
		Mode:     segmentItem.Mode,
		Distance: segmentItem.Distance,
		Time:     segmentItem.Duration,
		Cost:     segmentItem.Cost,
		ExpectedInfo: model.ExpectedInfo{
			Etp:       segmentItem.CarInfo.Etp,
			Eta:       segmentItem.CarInfo.Eta,
			Etq:       segmentItem.CarInfo.Etq,
			CarIsQu:   CarIsQu,
			CarEtqLen: 0,
		},
	}
}

func (c *CombinedTravelRPC) buildTransitSegment(segmentItem *combinedTravel.UTRecommendSegmentItem) *model.SegmentItem {
	if segmentItem.TransitInfo == nil {
		return c.buildNormalSegment(segmentItem)
	}

	// 途径站点信息
	var viaStops []*model.Stop
	for _, stop := range segmentItem.TransitInfo.ViaStops {
		viaStops = append(viaStops, &model.Stop{
			Id:       stop.GetId(),
			Name:     stop.GetName(),
			Location: stop.GetLocation(),
		})
	}

	return &model.SegmentItem{
		Mode:     segmentItem.Mode,
		SubMode:  cast.ToString(segmentItem.TransitInfo.Type),
		Distance: segmentItem.Distance,
		Time:     segmentItem.Duration,
		Cost:     segmentItem.Cost,
		StationInfo: model.StationInfo{
			StationCnt:     segmentItem.TransitInfo.StopNum,
			ViaStops:       viaStops,
			LineId:         segmentItem.TransitInfo.Id,
			LineName:       segmentItem.TransitInfo.Name,
			CanReachStatus: segmentItem.TransitInfo.CanReachStatus,
		},
	}
}
