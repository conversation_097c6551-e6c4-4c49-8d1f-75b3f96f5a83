package model

import (
	combinedTravel "git.xiaojukeji.com/dirpc/dirpc-go-http-CombinedTravel"
	walkApi "git.xiaojukeji.com/dirpc/dirpc-go-http-DolphinApiService"
	route "git.xiaojukeji.com/dirpc/dirpc-go-http-OrderRouteApi"
	metro "git.xiaojukeji.com/dirpc/dirpc-go-http-TransitWind"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v4"
)

type PlanFull struct {
	PlanType  int   // api下游的方案类型枚举
	TotalTime int64 // 耗时 单位:秒
	Cost      int64 // 花费 单位:分
	Distance  int32 // 距离
	PlanFormatDetailData

	PrivateData *PrivateData
	TipData     *string // 历史遗留的坑，需要传给athena
}

type PlanFormatDetailData struct {
	SegmentDataList []*SegmentItem
}

type SegmentItem struct {
	Mode    string // 路线段类型
	SubMode string // 目前只有Mode 为 TRANSIT时使用，0: 地铁 , 1: 公交 , 2: 火车， 3, 班车 4, 快线

	/** common **/
	Distance int32 // 距离
	Time     int32 // 时间
	Cost     int64 // 价格

	/** 详细接驾信息，打车，公共交通独有**/
	ExpectedInfo

	/** 站点信息，公共交通独有 **/
	StationInfo
}

type Stop struct {
	Id       string `json:"id"`       // 站点id
	Name     string `json:"name"`     // 站点名称
	Location string `json:"location"` // 站点位置
}

type ExpectedInfo struct {
	Eta       int32
	Etp       *int32
	Etq       *int32
	CarIsQu   int32 // 打车段是否排队
	CarEtqLen int32 // 排队人数
}

type StationInfo struct {
	StationCnt     int32   // 站点数量
	ViaStops       []*Stop // 途径站点数据
	LineId         string  // 路线id
	LineName       string  // 路线名字
	CanReachStatus int32   // 是否可达
}

type PrivateData struct {
	Fid                string
	PlanId             string
	BicycleData        []*route.BicyclingRouteDetail
	WalkData           *walkApi.PassengerWalkRouteRes
	CombinedTravelData *combinedTravel.PlanItem
	PublicTransitData  *metro.TransitSimple
	CarData            *EstimateData
}

// api下游方案类型枚举：planType
// 新增类型需要同步增加 athena 枚举
const (
	CarHailing      = 1 // 网约车
	PublicTransit   = 2 // 公交地铁
	CombinedTravel  = 3 // 组合出行
	BicyclingTravel = 4 // 全程骑行
	WalkingTravel   = 5 // 全程步行
)

const (
	LinkTypeTab = 1 // tab_id
	LinkTypeUrl = 2 // 原生地址
)

// api方案路线段类型枚举：mode
const (
	ModeWalk    = "WALKING" // 步行
	ModeTRANSIT = "TRANSIT" // 公交/地铁：公共交通
	ModeCAR     = "CAR"     // 打车
	ModeBike    = "BICYCLE" // 骑行
)

// api方案路线段类型为公共交通时的子类型枚举：subMode
const (
	TypeSubway  = 0 //  0: 地铁 , 1: 公交    (2: 火车， 3, 班车 4, 快线)
	TypeBus     = 1 //  0: 地铁 , 1: 公交    (2: 火车， 3, 班车 4, 快线)
	TypeDefault = -1
)

const (
	GroupTypeCar         = "1" // 打车
	GroupTypeComboCar    = "2" // 打车组合
	GroupTypeBus         = "3" // 公共交通;(包含公交/地铁/骑行组合)
	GroupTypeWalkBicycle = "4" // 骑行/步行
	GroupTypeControl     = "5" // 兜底策略
)

const (
	GroupTypeCarI         = iota + 1 // 打车
	GroupTypeComboCarI               // 打车组合
	GroupTypeBusI                    // 公共交通;(包含公交/地铁/骑行组合)
	GroupTypeWalkBicycleI            // 骑行/步行
	GroupTypeControlI                // 兜底策略
)

const (
	Version85 = "8.5"
)

type EstimateData struct {
	CarList []*estimate_v4.CarEstimateData
	V4Rsp   *proto.V4Data
}
