// Package sfc_estimate
package sfc_estimate

import (
	"context"
	"errors"
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/beatles-common/model"
	beatlesModel "git.xiaojukeji.com/gulfstream/beatles-common/model"
	beatlesApollo "git.xiaojukeji.com/gulfstream/beatles-common/model/apollo"
	"git.xiaojukeji.com/gulfstream/beatles-common/model/notes"
	"git.xiaojukeji.com/gulfstream/beatles-common/model/sfc_inter_city"
	"git.xiaojukeji.com/gulfstream/beatles-common/model/ufs"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	apolloUtil "git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/baichuanRpc"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/golibs/zerolog/ddlog"
	"github.com/bytedance/mockey"
	. "github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

//func TestGetPolicyApolloSwitch(t *testing.T) {
//	ctx := context.Background()
//	testPID := uint64(123456)
//
//	t.Run("开关正常开启时应返回true", func(t *testing.T) {
//		m := ApolloModel.NewToggleResult("", true, nil, nil, "", 0, "", "")
//		mock := Mock(ApolloSDK.FeatureToggle).
//			Return(m, nil).
//			Build()
//		defer mock.UnPatch()
//
//		mockIsAllow := Mock((*ApolloModel.ToggleResult).IsAllow).Return(true).Build()
//		defer mockIsAllow.UnPatch()
//
//		result := GetPolicyApolloSwitch(ctx, testPID)
//		if !result {
//			t.Fatal("Expected true but got false")
//		}
//	})
//
//	t.Run("配置关闭时应返回false", func(t *testing.T) {
//		m := ApolloModel.NewToggleResult("", false, nil, nil, "", 0, "", "")
//		mock := Mock(ApolloSDK.FeatureToggle).
//			Return(m, nil).
//			Build()
//		defer mock.UnPatch()
//
//		mockIsAllow := Mock((*ApolloModel.ToggleResult).IsAllow).Return(false).Build()
//		defer mockIsAllow.UnPatch()
//
//		result := GetPolicyApolloSwitch(ctx, testPID)
//		if result {
//			t.Fatal("Expected false but got true")
//		}
//	})
//
//	t.Run("Apollo服务异常时应返回false", func(t *testing.T) {
//		mock := Mock(ApolloSDK.FeatureToggle).
//			Return(nil, errors.New("service unavailable")).
//			Build()
//		defer mock.UnPatch()
//
//		log.Trace = &ddlog.DiLogHandle{}
//		mockLog := Mock((*ddlog.DiLogHandle).Warnf).Return().Build()
//		defer mockLog.UnPatch()
//
//		result := GetPolicyApolloSwitch(ctx, testPID)
//		if result {
//			t.Fatal("Expected false but got true")
//		}
//	})
//
//	t.Run("用户未命中实验时应返回false", func(t *testing.T) {
//		m := ApolloModel.NewToggleResult("", false, nil, nil, "", 0, "", "")
//		mock := Mock(ApolloSDK.FeatureToggle).
//			Return(m, nil).
//			Build()
//		defer mock.UnPatch()
//
//		mockIsAllow := Mock((*ApolloModel.ToggleResult).IsAllow).Return(false).Build()
//		defer mockIsAllow.UnPatch()
//
//		result := GetPolicyApolloSwitch(ctx, testPID)
//		if result {
//			t.Fatal("Expected false but got true")
//		}
//	})
//}

func TestGetPrivacyNotSignFromBaiChuan(t *testing.T) {
	Convey("测试百川隐私协议获取", t, func() {
		Convey("无效的AccessKeyID应返回nil", func() {
			mockey.Mock(consts.ConvertAccessKeyID2BaiChuanAppID).Return(int32(0)).Build()
			defer mockey.UnPatchAll()

			result := GetPrivacyNotSignFromBaiChuan(context.Background(), 999, 123, "456")
			So(result, ShouldBeNil)
		})

		Convey("策略开关关闭应返回nil", func() {
			mockey.Mock(consts.ConvertAccessKeyID2BaiChuanAppID).Return(int32(1001)).Build()
			mockey.Mock(getSwitch).Return(false).Build()
			defer mockey.UnPatchAll()

			result := GetPrivacyNotSignFromBaiChuan(context.Background(), 1, 123, "456")
			So(result, ShouldBeNil)
		})

		Convey("RPC调用出错应返回nil", func() {
			mockey.Mock(consts.ConvertAccessKeyID2BaiChuanAppID).Return(int32(1001)).Build()
			mockey.Mock(getSwitch).Return(true).Build()
			mockey.Mock(baichuanRpc.GetPrivacyNotSign).Return(nil, errors.New("rpc error")).Build()
			defer mockey.UnPatchAll()
			log.Trace = &ddlog.DiLogHandle{}
			mockLog := Mock((*ddlog.DiLogHandle).Warnf).Return().Build()
			defer mockLog.UnPatch()

			result := GetPrivacyNotSignFromBaiChuan(context.Background(), 1, 123, "456")
			So(result, ShouldBeNil)
		})

		Convey("成功获取隐私协议", func() {
			expected := &baichuanRpc.Privacy{DocId: 123}
			mockey.Mock(consts.ConvertAccessKeyID2BaiChuanAppID).Return(int32(1001)).Build()
			mockey.Mock(getSwitch).Return(true).Build()
			mockey.Mock(baichuanRpc.GetPrivacyNotSign).Return(expected, nil).Build()
			defer mockey.UnPatchAll()

			result := GetPrivacyNotSignFromBaiChuan(context.Background(), 1, 123, "456")
			So(result, ShouldResemble, expected)
		})
	})
}

func TestFillBottomInfo(t *testing.T) {

	t.Run("When policy enabled and has policy data", func(t *testing.T) {
		// Mock策略开关
		mockey.Mock(getSwitch).Return(true).Build()
		defer mockey.UnPatchAll()

		logic := &Logic{
			policy: &baichuanRpc.Privacy{
				DocId:   123,
				LinkUrl: "https://policy.example.com",
			},
			userInfo: passport.UserInfo{PID: 1001},
			request:  proto.SFCEstimateRequest{AccessKeyId: 1},
			dcmpData: proto.SFCEstimateDcmp{
				SubmitButton: &proto.SfcSubmitButton{},
				BottomInfo:   &proto.SfcBottomInfo{},
			},
		}
		ret := &proto.SFCEstimateResponse{
			SubmitButton: &proto.SfcSubmitButton{},
			BottomInfo:   &proto.SfcBottomInfo{},
		}

		_ = fillBottomInfo(context.Background(), logic, ret)
		assert.Equal(t, int64(123), logic.dcmpData.BottomInfo.PolicyId)
		assert.Equal(t, "https://policy.example.com", logic.dcmpData.BottomInfo.JumpUrl)
	})

	t.Run("When policy enabled but no policy data", func(t *testing.T) {
		mockey.Mock(getSwitch).Return(false).Build()
		mockey.Mock(ufs.GetUserAuthorizedInsuranceTime).Return(0).Build()
		defer mockey.UnPatchAll()

		logic := &Logic{
			policy:   nil, // 无策略数据
			userInfo: passport.UserInfo{PID: 1003},
			dcmpData: proto.SFCEstimateDcmp{
				SubmitButton: &proto.SfcSubmitButton{},
				BottomInfo:   &proto.SfcBottomInfo{},
			},
		}
		ret := &proto.SFCEstimateResponse{
			SubmitButton: &proto.SfcSubmitButton{},
			BottomInfo:   &proto.SfcBottomInfo{},
		}

		result := fillBottomInfo(context.Background(), logic, ret)
		assert.Equal(t, "", result.BottomInfo.Title)
	})

}

func TestGetSwitch(t *testing.T) {
	Convey("Test getSwitch", t, func() {
		ctx := context.Background()
		uid := int64(123)
		accessKeyId := int32(456)
		appVersion := "1.0.0"

		// Mock清理
		defer mockey.UnPatchAll()

		Convey("when apollo accessKeyID not contain current accessKey", func() {
			mockey.Mock(apolloUtil.GetConfigItem).Return("111,222").Build()
			mockey.Mock(beatlesApollo.GetSwitchInsurance).Return(true).Build()
			mockey.Mock(beatlesModel.CheckInsuranceVersion).Return(true).Build()

			result := getSwitch(ctx, uid, accessKeyId, appVersion)
			So(result, ShouldBeFalse)
		})

		Convey("when insurance switch is off", func() {
			mockey.Mock(apolloUtil.GetConfigItem).Return("456").Build()
			mockey.Mock(beatlesApollo.GetSwitchInsurance).Return(false).Build()
			mockey.Mock(beatlesModel.CheckInsuranceVersion).Return(true).Build()

			result := getSwitch(ctx, uid, accessKeyId, appVersion)
			So(result, ShouldBeFalse)
		})

		Convey("when version check failed", func() {
			mockey.Mock(apolloUtil.GetConfigItem).Return("456").Build()
			mockey.Mock(beatlesApollo.GetSwitchInsurance).Return(true).Build()
			mockey.Mock(beatlesModel.CheckInsuranceVersion).Return(false).Build()

			result := getSwitch(ctx, uid, accessKeyId, appVersion)
			So(result, ShouldBeFalse)
		})
	})
}

func TestComparePrices(t *testing.T) {
	ctx := context.Background()
	Mock((*ddlog.DiLogHandle).Warnf).Return().Build()
	Convey("测试价格比较逻辑", t, func() {
		// 每个用例独立测试，不共享状态
		Convey("顺风车价格为0且城际价格也为0", func() {
			So(comparePrices(ctx, 0, 0), ShouldBeTrue)
		})

		Convey("顺风车价格为0但城际价格非0", func() {
			So(comparePrices(ctx, 100, 0), ShouldBeFalse)
		})

		Convey("价格比例刚好等于1.1倍", func() {
			// 使用精确值避免浮点误差
			So(comparePrices(ctx, 110, 100), ShouldBeTrue)
		})

		Convey("价格比例小于1.1倍", func() {
			So(comparePrices(ctx, 109.999999, 100), ShouldBeTrue)
		})

		Convey("极小价格场景校验", func() {
			Convey("极小非零值通过校验", func() {
				So(comparePrices(ctx, 0.0000011, 0.000001), ShouldBeTrue)
			})

			Convey("极小非零值不通过校验", func() {
				So(comparePrices(ctx, 0.0000012, 0.000001), ShouldBeFalse)
			})
		})
	})
	mockey.UnPatchAll()
}

func TestCalInterCitySelectStatus(t *testing.T) {

	Mock((*ddlog.DiLogHandle).Infof).Return().Build()
	Mock((*ddlog.DiLogHandle).Warnf).Return().Build()
	Convey("测试城际拼车勾选逻辑", t, func() {
		// 准备公共测试数据

		ctx := context.Background()
		passenger := models.PassengerInfo{PID: 123, UID: 456, Phone: "13800000000"}
		validRoute := "route_1001"
		validPrice := 110.0

		Convey("用户历史已勾选-直接返回1", func() {
			mock1 := mockey.Mock(ufs.GetSfcInterCitySelect).Return(1).Build()
			defer mock1.UnPatch()
			mock2 := mockey.Mock(checkRouteHit).Return(true).Build()
			defer mock2.UnPatch()

			So(calInterCitySelectStatus(ctx, 1, "", passenger, validRoute, "1", validPrice, "100"), ShouldBeTrue)
		})

		Convey("用户历史已勾选-直接返回0", func() {

			mock1 := mockey.Mock(ufs.GetSfcInterCitySelect).Return(0).Build()
			defer mock1.UnPatch()
			mock2 := mockey.Mock(checkRouteHit).Return(true).Build()
			defer mock2.UnPatch()

			So(calInterCitySelectStatus(ctx, 1, "", passenger, validRoute, "1", validPrice, "100"), ShouldBeFalse)
		})

		Convey("路线未命中-返回false", func() {

			mock2 := mockey.Mock(checkRouteHit).Return(false).Build()
			defer mock2.UnPatch()

			So(calInterCitySelectStatus(ctx, 1, "", passenger, "invalid_route", "1", validPrice, "100"), ShouldBeFalse)
		})

		Convey("个性化推荐关闭-返回false", func() {
			mock1 := mockey.Mock(ufs.GetSfcInterCitySelect).Return(-1).Build()
			defer mock1.UnPatch()
			mock2 := mockey.Mock(checkRouteHit).Return(true).Build()
			defer mock2.UnPatch()
			mock3 := mockey.Mock(sfc_inter_city.GetPersonalSwitch).Return(false).Build()
			defer mock3.UnPatch()

			So(calInterCitySelectStatus(ctx, 1, "", passenger, validRoute, "1", validPrice, "100"), ShouldBeFalse)
		})

		Convey("无高速费-返回false", func() {
			mock1 := mockey.Mock(ufs.GetSfcInterCitySelect).Return(-1).Build()
			defer mock1.UnPatch()
			mock2 := mockey.Mock(checkRouteHit).Return(true).Build()
			defer mock2.UnPatch()
			mock3 := mockey.Mock(sfc_inter_city.GetPersonalSwitch).Return(true).Build()
			defer mock3.UnPatch()

			So(calInterCitySelectStatus(ctx, 1, "", passenger, validRoute, notes.TollFeeNone, validPrice, "100"), ShouldBeFalse)
		})

		Convey("价格校验通过-返回true", func() {
			mock1 := mockey.Mock(ufs.GetSfcInterCitySelect).Return(-1).Build()
			defer mock1.UnPatch()
			mock2 := mockey.Mock(checkRouteHit).Return(true).Build()
			defer mock2.UnPatch()
			mock3 := mockey.Mock(sfc_inter_city.GetPersonalSwitch).Return(true).Build()
			defer mock3.UnPatch()

			So(calInterCitySelectStatus(ctx, 1, "", passenger, validRoute, "1", 110.0, "100"), ShouldBeTrue)
		})

		Convey("价格校验失败-返回false", func() {
			mock1 := mockey.Mock(ufs.GetSfcInterCitySelect).Return(-1).Build()
			defer mock1.UnPatch()
			mock2 := mockey.Mock(checkRouteHit).Return(true).Build()
			defer mock2.UnPatch()
			mock3 := mockey.Mock(sfc_inter_city.GetPersonalSwitch).Return(true).Build()
			defer mock3.UnPatch()

			So(calInterCitySelectStatus(ctx, 1, "", passenger, validRoute, "1", 120.0, "100"), ShouldBeFalse)
		})

		Convey("顺风车价格无效-返回false", func() {
			mock1 := mockey.Mock(ufs.GetSfcInterCitySelect).Return(-1).Build()
			defer mock1.UnPatch()
			mock2 := mockey.Mock(checkRouteHit).Return(true).Build()
			defer mock2.UnPatch()
			mock3 := mockey.Mock(sfc_inter_city.GetPersonalSwitch).Return(true).Build()
			defer mock3.UnPatch()

			So(calInterCitySelectStatus(ctx, 1, "", passenger, validRoute, "1", validPrice, "invalid_price"), ShouldBeFalse)
		})
	})
	mockey.UnPatchAll()
}

func TestGetLinkListCard(t *testing.T) {

	Convey("测试城际拼车卡片生成逻辑", t, func() {
		ctx := context.Background()
		logic := &Logic{request: proto.SFCEstimateRequest{AccessKeyId: 123, AppVersion: "1.0.0"}}

		// 准备基础测试数据
		product := &biz_runtime.ProductInfoFull{
			Product: &models.Product{EstimateID: "1001"},
			BaseReqData: &models.BaseReqData{
				PassengerInfo: models.PassengerInfo{PID: 123},
				CommonInfo: models.CommonInfo{
					DepartureRange: []int64{time.Now().Unix()},
				},
			},
		}
		dcmpConfig := &IntercityDcmp{
			Caricon:  "icon_url",
			Subtitle: "推荐使用",
			Cartitle: []string{"标题1", "标题2"},
		}

		Convey("空参数直接返回nil", func() {
			So(logic.getLinkListCard(ctx, nil, dcmpConfig, "", ""), ShouldBeNil)
			So(logic.getLinkListCard(ctx, product, nil, "", ""), ShouldBeNil)
		})

		Convey("入参带城际勾选状态", func() {
			mockProduct := *product
			mockProduct.BaseReqData.CommonBizInfo = models.CommonBizInfo{
				MultiRequireProduct: []models.RequireProduct{
					{ProductCategory: 1001, IsSelected: 1},
				},
			}
			mockProduct.Product.ProductCategory = 1001
			mockProduct.BaseReqData.CommonInfo.DepartureRange = []int64{time.Now().Unix() + 1000}
			mockey.Mock(getCarTitle).Return(1, 1, "test").Build()
			mockey.Mock(sfc_inter_city.CheckSfcInterCityABHit).Return(true).Build()
			mockey.Mock(calInterCitySelectStatus).Return(true).Build()
			defer mockey.UnPatchAll()
			res := logic.getLinkListCard(ctx, &mockProduct, dcmpConfig, "icon", "100")
			So(res.IsSelected, ShouldEqual, 1)

		})

		Convey("实验未命中不勾选", func() {
			mockey.Mock(getCarTitle).Return(1, 1, "test").Build()
			mockey.Mock(sfc_inter_city.CheckSfcInterCityABHit).Return(false).Build()
			mockey.Mock(calInterCitySelectStatus).Return(true).Build()
			Mock((*ddlog.DiLogHandle).Infof).Return().Build()
			Mock((*ddlog.DiLogHandle).Warnf).Return().Build()
			defer mockey.UnPatchAll()

			res := logic.getLinkListCard(ctx, product, dcmpConfig, "icon", "100")
			So(res.IsSelected, ShouldEqual, 0)
		})

		Convey("实验命中但条件不满足", func() {

			mockey.Mock(getCarTitle).Return(1, 1, "test").Build()
			mockey.Mock(sfc_inter_city.CheckSfcInterCityABHit).Return(true).Build()
			mockey.Mock(calInterCitySelectStatus).Return(false).Build()
			Mock((*ddlog.DiLogHandle).Infof).Return().Build()
			Mock((*ddlog.DiLogHandle).Warnf).Return().Build()
			defer mockey.UnPatchAll()

			res := logic.getLinkListCard(ctx, product, dcmpConfig, "icon", "100")
			So(res.IsSelected, ShouldEqual, 0)
		})

		Convey("实验命中且条件满足", func() {

			mockey.Mock(getCarTitle).Return(1, 1, "test").Build()
			mockey.Mock(sfc_inter_city.CheckSfcInterCityABHit).Return(true).Build()
			mockey.Mock(calInterCitySelectStatus).Return(true).Build()
			Mock((*ddlog.DiLogHandle).Infof).Return().Build()
			Mock((*ddlog.DiLogHandle).Warnf).Return().Build()
			defer mockey.UnPatchAll()

			res := logic.getLinkListCard(ctx, product, dcmpConfig, "icon", "100")
			So(res.IsSelected, ShouldEqual, 1)
		})

		Convey("时间因素计算异常处理", func() {
			mockey.Mock(getIntercityTimeFactors).Return(int64(0), int64(0)).Build()
			mockey.Mock(getCarTitle).Return(1, 1, "test").Build()
			mockey.Mock(sfc_inter_city.CheckSfcInterCityABHit).Return(true).Build()
			mockey.Mock(calInterCitySelectStatus).Return(true).Build()
			Mock((*ddlog.DiLogHandle).Infof).Return().Build()
			Mock((*ddlog.DiLogHandle).Warnf).Return().Build()
			defer mockey.UnPatchAll()

			res := logic.getLinkListCard(ctx, product, dcmpConfig, "icon", "100")
			So(res, ShouldNotBeNil)
		})
	})
}

func TestCheckRouteHit(t *testing.T) {
	Convey("测试城际路线实验命中逻辑", t, func() {
		ctx := context.Background()
		testPID := int64(12345)
		testRouteID := "route_2023"
		testAccessKeyID := int32(2001)
		testVersion := "3.8.0"

		Convey("高版本命中实验", func() {
			mock1 := mockey.Mock(model.CheckSfcInterCityVersion).Return(true).Build()
			defer mock1.UnPatch()

			mock2 := mockey.Mock(apolloUtil.FeatureToggle).When(func(ctx context.Context, toggleName string, userID string, params map[string]string) bool {
				return toggleName == consts.IntercitySelectRouteIDHighToggle &&
					userID == "12345" &&
					params["pid"] == "12345" &&
					params["route_id"] == testRouteID
			}).Return(true).Build()
			defer mock2.UnPatch()

			result := checkRouteHit(ctx, testAccessKeyID, testVersion, testPID, testRouteID)
			So(result, ShouldBeTrue)
		})

		Convey("高版本未命中实验", func() {
			mock1 := mockey.Mock(model.CheckSfcInterCityVersion).Return(true).Build()
			defer mock1.UnPatch()

			mock2 := mockey.Mock(apolloUtil.FeatureToggle).Return(false).Build()
			defer mock2.UnPatch()

			result := checkRouteHit(ctx, testAccessKeyID, testVersion, testPID, testRouteID)
			So(result, ShouldBeFalse)
		})

		Convey("低版本命中实验", func() {
			mock1 := mockey.Mock(model.CheckSfcInterCityVersion).Return(false).Build()
			defer mock1.UnPatch()

			mock2 := mockey.Mock(apolloUtil.FeatureToggle).When(func(ctx context.Context, toggleName string, userID string, params map[string]string) bool {
				return toggleName == consts.IntercitySelectRouteIDToggle
			}).Return(true).Build()
			defer mock2.UnPatch()

			result := checkRouteHit(ctx, testAccessKeyID, testVersion, testPID, testRouteID)
			So(result, ShouldBeTrue)
		})

		Convey("低版本未命中实验", func() {
			mock1 := mockey.Mock(model.CheckSfcInterCityVersion).Return(false).Build()
			defer mock1.UnPatch()

			mock2 := mockey.Mock(apolloUtil.FeatureToggle).Return(false).Build()
			defer mock2.UnPatch()

			result := checkRouteHit(ctx, testAccessKeyID, testVersion, testPID, testRouteID)
			So(result, ShouldBeFalse)
		})

	})
}

func TestWritePublicLog(t *testing.T) {
	Convey("测试日志记录逻辑", t, func() {
		ctx := context.Background()
		logic := &Logic{
			userInfo: passport.UserInfo{PID: 1001},
			request: proto.SFCEstimateRequest{
				FromName:       "北京南站",
				ToName:         "首都机场",
				DepartureRange: []int64{1672502400, 1672506000},
				PassengerNums:  util.Int32Ptr(2),
			},
			generator: &biz_runtime.ProductsGenerator{
				BaseReqData: &models.BaseReqData{
					AreaInfo: models.AreaInfo{},
					CommonInfo: models.CommonInfo{
						DepartureRange: []int64{1672502400, 1672506000},
					},
				},
			},
		}

		mockPublic := mockey.Mock((*ddlog.PubLog).Public).Return().Build()
		defer mockPublic.UnPatch()
		mockLog := mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build()
		defer mockLog.UnPatch()
		Convey("正常记录日志", func() {
			product := &biz_runtime.ProductInfoFull{

				Product: &models.Product{
					ProductCategory: estimate_pc_id.EstimatePcIdCarpoolSFCar,
					EstimateID:      "EST_123",
					CarpoolType:     consts.CarPoolTypeInterCity,
				},
			}
			product.SetHu(&PriceApi.EstimateNewFormData{
				BillInfo: &PriceApi.EstimateNewFormBillInfo{},
			})
			products := []*biz_runtime.ProductInfoFull{
				product,
			}

			logic.writePublicLog(ctx, products, 1, 0, 0, 1, "SP_001")

			So(mockPublic.Times(), ShouldEqual, 1)
		})

		Convey("不满足四要素不记录日志", func() {
			logic.request.FromName = "" // 破坏四要素条件
			products := []*biz_runtime.ProductInfoFull{{}}

			logic.writePublicLog(ctx, products, 1, 0, 0, 0, "")

			So(mockPublic.Times(), ShouldEqual, 0)
		})

		Convey("产品信息不完整跳过记录", func() {
			products := []*biz_runtime.ProductInfoFull{nil}

			logic.writePublicLog(ctx, products, 1, 0, 0, 0, "")

			So(mockPublic.Times(), ShouldEqual, 0)
		})

		//Convey("panic恢复测试", func() {
		//	mockPanic := mockey.Mock((*biz_runtime.ProductInfoFull).GetEstimateID).Panic("test panic").Build()
		//	defer mockPanic.UnPatch()
		//
		//	products := []*biz_runtime.ProductInfoFull{{}}
		//
		//	So(func() {
		//		logic.writePublicLog(ctx, products, 1, 0, 0, 0, "")
		//	}, ShouldNotPanic)
		//})
	})
}
