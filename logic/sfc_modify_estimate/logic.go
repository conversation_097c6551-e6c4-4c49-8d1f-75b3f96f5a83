package sfc_modify_estimate

import (
	"context"
	"encoding/json"
	DirpcDcmp "git.xiaojukeji.com/dirpc/dirpc-go-dcmp/v2"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
	"git.xiaojukeji.com/gulfstream/mamba/models/option_gen_product"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"
	"time"
)

const (
	LOG_TAG = "sfc"
)

type SFCarAdapter struct {
	*biz_runtime.ProductInfoFull
	//apolloParams *plain_text_render.ApolloParams
}

// Logic 数据
type Logic struct {
	generator     *biz_runtime.ProductsGenerator
	passengerNums int32
	maxNum        int32 //可选择最大座位数
	dcmpData      proto.SFCEstimateDcmp
	userInfo      passport.UserInfo
	traceID       string
	request       proto.SFCModifyEstimateRequest
	extraParams   *proto.ExtraParams
	login         bool
}

func NewLogic(ctx context.Context, request *proto.SFCModifyEstimateRequest, traceId string) (*Logic, error) {
	var (
		err            error
		userInfo       *passport.UserInfo
		dcmpData       proto.SFCEstimateDcmp
		departureTime  int64
		departureRange models.DepartureRange
	)

	//参数校验
	checkParams := func(request *proto.SFCModifyEstimateRequest) error {
		if request.Oid == "" {
			log.Trace.Warnf(ctx, LOG_TAG, "参数校验")
			return BizError.ErrInvalidArgument
		}
		return nil
	}
	if err = checkParams(request); err != nil {
		return nil, err
	}

	//用户信息获取
	userInfo = &passport.UserInfo{
		PID:      0,
		UID:      0,
		Phone:    "",
		Role:     1,
		Channel:  "",
		OriginId: "",
	}
	login := true
	res, err := passport.GetUserInfo(ctx, request.Token, "")
	if err != nil || res == nil {
		login = false
		return nil, BizError.ErrSystem
	} else {
		userInfo = res
	}

	//文案获取
	dcmpStr := DirpcDcmp.GetContent(ctx, "sfc-estimate_text", "zh-CN", nil)
	errJSON := json.Unmarshal([]byte(dcmpStr), &dcmpData)
	if errJSON != nil {
		log.Trace.Warnf(ctx, "sfc-dcmp", "err=%+v", errJSON)
		return nil, errJSON
	}

	//出发时间获取
	timeRange := request.GetDepartureRange()
	if len(timeRange) > 1 && timeRange[0] > 0 && timeRange[1] > 0 {
		departureTime = timeRange[0]
		departureRange = models.DepartureRange{
			From: time.Unix(timeRange[0], 0),
			To:   time.Unix(timeRange[1], 0),
		}
	} else if len(timeRange) == 1 && timeRange[0] > 0 { //目前时间相等也会传两个值，以下代码需验证
		departureTime = timeRange[0]
		departureRange = models.DepartureRange{
			From: time.Unix(timeRange[0], 0),
			To:   time.Unix(timeRange[0], 0),
		}
	} else {
		departureTime = time.Now().Unix()
		departureRange = models.DepartureRange{
			From: time.Now(),
			To:   time.Now(),
		}
	}

	//品类查询
	productsGen, err := getProductInfo(ctx, departureRange, userInfo, request, departureTime, dcmpData)
	if err != nil {
		return nil, err
	}

	return &Logic{
		generator:   productsGen,
		dcmpData:    dcmpData,
		userInfo:    *userInfo,
		traceID:     traceId,
		request:     *request,
		extraParams: nil,
		login:       login,
	}, nil
}

func getProductInfo(ctx context.Context, departureRange models.DepartureRange, userInfo *passport.UserInfo, request *proto.SFCModifyEstimateRequest, departureTime int64, dcmpData proto.SFCEstimateDcmp) (*biz_runtime.ProductsGenerator, error) {
	var (
		err         error
		baseReqData *models.BaseReqData
	)

	orderID, district, err := util.DecodeOrderID(request.Oid)
	if err != nil {
		log.Trace.Errorf(ctx, trace.DLTagUndefined, "decodeErr: %v", err)
		return nil, err
	}

	// 请求 dos
	order, err := dos.GetOrderInfo(ctx, orderID, district)
	if err != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "getOrderInfo: %v", err)
		return nil, err
	}

	orderInfo := order_info.OrderInfo2OrderInfo(order)
	//passengerNum := 0
	//for _, val := range orderInfo.ExtendFeatureParsed.MultiRequiredProduct {
	//	passengerNum = val.PassengerCount
	//}

	// platformType代表端类型，1ios，2安卓，3小程序
	platformType := request.AccessKeyId
	if request.AccessKeyId == consts.AccessKeyIDDiDiIos || request.AccessKeyId == consts.AccessKeyIDDiDiAndroid {
		platformType = request.AccessKeyId
	} else {
		platformType = consts.AccessKeyIDDiDiQuickApp
	}
	pageType := page_type.PageTypeSFCEstimate
	if baseReqData, err = models.NewBaseReqDataBuilder().
		SetPassengerInfoV2(&models.PassengerInfoV2{
			UID:      int64(userInfo.UID),
			PID:      int64(userInfo.PID),
			Phone:    userInfo.Phone,
			Role:     userInfo.Role,
			Channel:  userInfo.Channel,
			UserType: 1, //默认成人
			OriginID: userInfo.OriginId,
		}).
		SetClientInfo(&models.ClientInfo{
			AppVersion:  request.AppVersion,
			AccessKeyID: request.AccessKeyId,
			MenuID:      "dache_anycar",
			PageType:    cast.ToInt32(pageType),
			//Channel:      int64(2000001),
			PlatformType: platformType,
			Lang:         "zh-CN",
		}).
		SetGEOInfo(&models.GEOInfo{
			//MapType:     request.MapType,
			CurrLat:   orderInfo.GetCurrentLat(),
			CurrLng:   orderInfo.GetCurrentLng(),
			FromLat:   orderInfo.GetFromLat(),
			FromLng:   orderInfo.GetFromLng(),
			FromPOIID: orderInfo.StartingPoiId,
			//FromPOIType: orderInfo.startingpoi,
			FromAddress: orderInfo.FromAddress,
			FromName:    orderInfo.FromName,
			ToLat:       orderInfo.GetToLat(),
			ToLng:       orderInfo.GetToLng(),
			ToPOIID:     orderInfo.DestPoiId,
			//ToPOIType:   request.GetToPoiType(),
			ToAddress: orderInfo.ToAddress,
			ToName:    orderInfo.ToName,
		}).
		SetUserOption(&models.UserOption{
			OrderType:      cast.ToInt32(orderInfo.GetType()),
			DepartureTime:  departureTime,   // 简化版账单计价规则不区分时间，但还是传给账单departure_time
			DepartureRange: &departureRange, // 简化版未处理端上传的departure_range，看后续计价规则和产品方案
			//CarpoolSeatNum: cast.ToInt32(1),
			//MultiRequireProduct: orderInfo,
		}).TryBuild(ctx); err != nil {
		log.Trace.Warnf(ctx, LOG_TAG, "new logic1 err=%+v", err)
		return nil, err
	}

	var productsGen *biz_runtime.ProductsGenerator
	if productsGen, err = biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("pSFCEstimate"),
		biz_runtime.WithBaseReq(baseReqData),
	); err != nil {
		log.Trace.Warnf(ctx, LOG_TAG, "new logic2 err=%+v", err)
		return nil, err
	}

	productsGen.SetNeedMember(true)
	productsGen.SetSendReqKafka(false)

	productsGen.RegisterOptionProcessProduct(option_gen_product.NewSfcModifyTimeGenernator(orderInfo))

	return productsGen, nil
}

// Do 执行方法
func (logic *Logic) Do(ctx context.Context) (*proto.SFCModifyEstimateData, error) {
	var (
		err      error
		products []*biz_runtime.ProductInfoFull
	)
	if products, err = logic.generator.GenProducts(ctx); err != nil {
		log.Trace.Warnf(ctx, LOG_TAG, "genProducts err=%+v", err)
		return nil, err
	}
	data := &proto.SFCModifyEstimateData{}
	for _, product := range products {

		data.EstimateId = product.GetEstimateID()
		//if item, _, _, _, _ := Render(ctx, product, logic.dcmpData, isCrossCity, isNewUser,
		//	isNewVersion, isCouponMarketingSwicth, abGroup, newPriceSwitch, doubleSwitch, logic.passengerNums); item != nil {
		//
		//}
		prov := &SFCarAdapter{product}
		estimateFeeList := prov.GetMultiPrice()
		if estimateFeeList != nil {
			data.PriceList = estimateFeeList
		}
	}

	return data, nil
}

// GetMultiPrice 获取金额
func (a *SFCarAdapter) GetMultiPrice() []float64 {
	var (
		estimateFeeList = make([]float64, 0)
	)

	prices := a.GetSceneEstimatePrice()
	for _, price := range prices {
		estimateFeeList = append(estimateFeeList, price.GetFeeDetail().GetCapPrice())
	}

	return estimateFeeList
}
