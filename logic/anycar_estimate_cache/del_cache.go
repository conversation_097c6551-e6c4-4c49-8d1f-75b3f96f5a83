package anycar_estimate_cache

import (
	"context"
	"errors"

	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/redis"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/anycar_v4"
)

func DelAnycarEstimateCache(ctx context.Context, request *proto.AnycarEstimateCacheReq) error {
	if request == nil || request.GetOid() == "" {
		return NewErrors.NewBizError(errors.New("request is nil"), NewErrors.ErrnoInvalidArgument)
	}

	// check caller
	params := map[string]string{
		"caller": request.GetCaller(),
		"type":   "del_cache",
	}

	if !apollo.FeatureToggle(ctx, "anycar_estimate_cache_caller_toggle", request.GetOid(), params) {
		return NewErrors.ErrReqNotAllow
	}

	return DelEstimateCache(ctx, request.GetOid())
}

func DelEstimateCache(ctx context.Context, orderID string) error {
	redisKey := anycar_v4.GetEstimateCacheKey(orderID)
	reply, err := redis.GetEstimateClient().Del(ctx, redisKey)
	if err != nil {
		log.Trace.Infof(ctx, "redis_error", "redisErr=%v", err)
		return errors.New("del redis error")
	}

	if reply == 0 {
		log.Trace.Infof(ctx, "redis_error", "redis key not exist")
		return errors.New("redis key not exists")
	}

	return nil
}
