package anycar_estimate_cache

import (
	"context"
	"encoding/json"
	"errors"
	"strings"

	"git.xiaojukeji.com/nuwa/golibs/metrics"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/logic/anycar_v4"

	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

func GetAnycarEstimateCache(ctx context.Context, request *proto.AnycarEstimateCacheReq) (*proto.NewFormEstimateResponse, NewErrors.BizError) {
	var (
		cacheErr    error
		cacheRetStr string
	)

	defer func() {
		if cacheErr != nil {
			metrics.SendCounterMetrics(&metrics.CountInfo{
				Metric:   "get_anycar_estimate_cache_error",
				NumCount: metrics.NewInt(1),
				Tags: map[string]string{"caller": request.GetCaller(),
					"error": strings.ReplaceAll(cacheErr.Error(), " ", "_")},
			})
		}
	}()

	estimateInfo := &proto.NewFormEstimateResponse{}

	if request == nil {
		return nil, NewErrors.NewBizError(errors.New("request is nil"), NewErrors.ErrnoInvalidArgument)
	}

	// check caller
	params := map[string]string{
		"caller": request.GetCaller(),
		"type":   "get_cache",
	}
	if !apollo.FeatureToggle(ctx, "anycar_estimate_cache_caller_toggle", request.GetOid(), params) {
		return nil, NewErrors.ErrReqNotAllow
	}

	cacheRetStr, cacheErr = anycar_v4.ReadEstimateCache(ctx, request.GetOid())
	if cacheErr == nil {
		if parseErr := json.Unmarshal([]byte(cacheRetStr), estimateInfo); parseErr == nil {
			return estimateInfo, nil
		}
		cacheErr = errors.New("cache unmarshal failed")
	}

	return nil, nil
}
