package anycar_estimate_cache

import (
	"context"
	"encoding/json"
	"errors"
	"testing"

	"git.xiaojukeji.com/nuwa/golibs/metrics"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"

	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/anycar_v4"
)

// 该测试函数由AI自动生成
func TestGetAnycarEstimateCache(t *testing.T) {
	validResponse := &proto.NewFormEstimateResponse{
		EstimateTraceId: "test_trace_id",
	}
	validResponseBytes, _ := json.Marshal(validResponse)

	tests := []struct {
		name     string
		req      *proto.AnycarEstimateCacheReq
		mock     func() *gomonkey.Patches
		wantResp *proto.NewFormEstimateResponse
		wantErr  NewErrors.BizError
	}{
		{
			name:     "请求参数为空",
			req:      nil,
			mock:     func() *gomonkey.Patches { return nil },
			wantResp: nil,
			wantErr: NewErrors.NewBizError(
				errors.New("request is nil"),
				NewErrors.ErrnoInvalidArgument,
			),
		},
		{
			name: "caller权限校验失败",
			req: &proto.AnycarEstimateCacheReq{
				Oid:    "123",
				Caller: "test",
			},
			mock: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				patches.ApplyFunc(apollo.FeatureToggle, func(ctx context.Context, name string, userID string, params map[string]string) bool {
					return false
				})
				patches.ApplyFunc(metrics.SendCounterMetrics, func(countInfo *metrics.CountInfo) {})
				return patches
			},
			wantResp: nil,
			wantErr:  NewErrors.ErrReqNotAllow,
		},
		{
			name: "读取缓存失败",
			req: &proto.AnycarEstimateCacheReq{
				Oid:    "123",
				Caller: "test",
			},
			mock: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				patches.ApplyFunc(apollo.FeatureToggle, func(ctx context.Context, name string, userID string, params map[string]string) bool {
					return true
				})
				patches.ApplyFunc(anycar_v4.ReadEstimateCache, func(ctx context.Context, orderID string) (string, error) {
					return "", errors.New("read cache error")
				})
				patches.ApplyFunc(metrics.SendCounterMetrics, func(countInfo *metrics.CountInfo) {})
				return patches
			},
			wantResp: nil,
			wantErr:  nil,
		},
		{
			name: "读取缓存成功且解析成功",
			req: &proto.AnycarEstimateCacheReq{
				Oid:    "123",
				Caller: "test",
			},
			mock: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				patches.ApplyFunc(apollo.FeatureToggle, func(ctx context.Context, name string, userID string, params map[string]string) bool {
					return true
				})
				patches.ApplyFunc(anycar_v4.ReadEstimateCache, func(ctx context.Context, orderID string) (string, error) {
					return string(validResponseBytes), nil
				})
				patches.ApplyFunc(metrics.SendCounterMetrics, func(countInfo *metrics.CountInfo) {})
				return patches
			},
			wantResp: validResponse,
			wantErr:  nil,
		},
		{
			name: "读取缓存成功但解析失败",
			req: &proto.AnycarEstimateCacheReq{
				Oid:    "123",
				Caller: "test",
			},
			mock: func() *gomonkey.Patches {
				patches := gomonkey.NewPatches()
				patches.ApplyFunc(apollo.FeatureToggle, func(ctx context.Context, name string, userID string, params map[string]string) bool {
					return true
				})
				patches.ApplyFunc(anycar_v4.ReadEstimateCache, func(ctx context.Context, orderID string) (string, error) {
					return "invalid json", nil
				})
				patches.ApplyFunc(metrics.SendCounterMetrics, func(countInfo *metrics.CountInfo) {})
				return patches
			},
			wantResp: nil,
			wantErr:  nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			patches := tt.mock()
			if patches != nil {
				defer patches.Reset()
			}

			resp, err := GetAnycarEstimateCache(context.Background(), tt.req)
			assert.Equal(t, tt.wantResp, resp)
			assert.Equal(t, tt.wantErr, err)
		})
	}
}
