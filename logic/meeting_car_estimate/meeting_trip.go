package meetingcarestimate

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/locsvr"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/plutus"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/anycar_v3/data"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_filter/before_price_filter"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"

	plutusbp "git.xiaojukeji.com/dirpc/dirpc-go-http-Plutus"
)

const (
	meetingCarChannel = 49916
	meetingSourceID   = 21
)

type MeetingTrip struct {
	productsGenerator   *biz_runtime.ProductsGenerator
	TripList            *proto.EstimateEntry // 行程列表
	EstimateId          string               // 预估报价单id
	ServiceFeePassenger int64                // 乘客服务费
	ServiceFeeDriver    int64                // 平台服务费
	EstimateFeeTotal    int64                // 预估总价
	CharterFeeDriver    int64                // 包车b端预估费用
	DepartureTime       int64                // 出发时间
	RequireLevel        string
	BusinessID          int32
	StartCity           int32
	StartDistrict       string

	FeeMsg     string // 价格描述
	CarName    string // 品类名称
	FailReason proto.FailReason
	filter     *before_price_filter.MeetingCarComboFilter
}

type CommonInfo struct {
	Token       string
	AccessKeyId int32
	AppVersion  string
	Channel     int64
}

func NewMeetingTrip() *MeetingTrip {
	return &MeetingTrip{
		productsGenerator: biz_runtime.NewProductsGenerator(),
	}
}

// Init 初始化
func (m *MeetingTrip) Init(ctx context.Context,
	userInfo *passport.UserInfo,
	commonInfo *CommonInfo,
	trip *proto.EstimateEntry) error {
	m.TripList = trip
	orderType := 0
	if trip.DepartureTime-time.Now().Unix() > 15*60 {
		orderType = 1
	}
	baseReq, err := models.NewBaseReqDataBuilder().
		SetClientInfo(&models.ClientInfo{
			AppVersion:  commonInfo.AppVersion,
			AccessKeyID: commonInfo.AccessKeyId,
			Channel:     meetingCarChannel,
			Lang:        "zh-CN",
			SourceID:    meetingSourceID,
		}).
		SetPassengerInfoV2(&models.PassengerInfoV2{
			UID:      int64(userInfo.UID),
			PID:      int64(userInfo.PID),
			Phone:    userInfo.Phone,
			Role:     userInfo.Role,
			Channel:  userInfo.Channel,
			OriginID: userInfo.OriginId,
		}).
		SetGEOInfo(&models.GEOInfo{
			FromLat:  trip.Flat,
			FromLng:  trip.Flng,
			FromName: trip.FromName,
			ToLat:    trip.Tlat,
			ToLng:    trip.Tlng,
			ToName:   trip.ToName,
		}).
		SetUserOption(&models.UserOption{
			DepartureTime: trip.DepartureTime,
			ComboId:       trip.ComboId,
			OrderType:     int32(orderType),
		}).
		TryBuild(ctx)
	if err != nil {
		return err
	}

	productsGen, err := biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("meetingCarEstimate"),
		biz_runtime.WithBaseReq(baseReq),
		biz_runtime.WithCommericalType(),
	)

	if err != nil {
		return err
	}

	// 获取会议用车包车套餐，过滤掉不存在的套餐
	coords := make([]map[string]float64, 0, 1)
	coords = append(coords, map[string]float64{"lat": trip.Flat, "lng": trip.Flng})
	cityInfo, err := locsvr.MultiAreaInfoByCoords(ctx, coords, "soso")
	if err != nil {
		return err
	}
	if len(cityInfo) == 0 {
		return fmt.Errorf("city err")
	}
	m.StartCity = cityInfo[0].Cityid
	m.StartDistrict = *cityInfo[0].DistrictCode
	filter := &before_price_filter.MeetingCarComboFilter{
		ProductCategory:     trip.GetProductCategory(),
		ProductId:           trip.GetProductId(),
		ComboId:             trip.GetComboId(),
		ComboType:           trip.GetComboType(),
		Area:                cityInfo[0].Cityid,
		ServiceFeePassenger: trip.ServiceFeePassenger,
		ServiceFeeDriver:    trip.ServiceFeeDriver,
		District:            *cityInfo[0].DistrictCode,
	}
	productsGen.RegisterBeforePriceRpcProcess(filter)
	productsGen.RegisterBeforePriceFilter(filter)
	m.productsGenerator = productsGen
	m.filter = filter
	return nil
}

func (m *MeetingTrip) Estimate(ctx context.Context) {
	products, err := m.productsGenerator.GenProducts(ctx)
	if err != nil {
		log.Trace.Warnf(ctx, "meeting_car", "GenProducts err %s", err)
		if m.filter.IsOpenCity {
			m.FailReason = proto.FailReason_NOT_PRICE_RULE
		} else {
			m.FailReason = proto.FailReason_NOT_OPEN_CITY
		}
		return
	}
	if len(products) == 0 {
		if m.filter.IsOpenCity {
			m.FailReason = proto.FailReason_NOT_PRICE_RULE
		} else {
			m.FailReason = proto.FailReason_NOT_OPEN_CITY
		}
		return
	}
	if len(products) > 1 {
		log.Trace.Warnf(ctx, "meeting_car", "GenProducts products len %d", len(products))
	}
	p := products[0]
	m.EstimateId = p.GetEstimateID()
	m.EstimateFeeTotal = int64(p.GetBillInfo().DynamicTotalFee * 100)
	m.RequireLevel = p.GetRequireLevel()
	m.BusinessID = int32(p.GetBusinessID())
	m.render(ctx, p)
	// b端预估价格，只有包车有b端价格
	if m.TripList.ComboId == 0 {
		return
	}
	orderType := 0
	if m.DepartureTime-time.Now().Unix() > 15*60 {
		orderType = 1
	}
	params := &plutusbp.DriverEstimateRequest{
		ComboId:           int32(p.GetComboID()),
		ProductId:         int32(p.GetProductId()),
		DepartureTime:     m.DepartureTime,
		FromLat:           m.TripList.Flat,
		FromLng:           m.TripList.Flng,
		FromName:          m.TripList.FromName,
		ToLat:             m.TripList.Tlat,
		ToLng:             m.TripList.Tlng,
		ToName:            m.TripList.ToName,
		ComboType:         int32(p.GetComboType()),
		District:          m.StartDistrict,
		Lang:              "zh-CN",
		RequireLevels:     []string{p.GetRequireLevel()},
		StartDestDistance: 1,
		EstimateTime:      1,
		OrderType:         int32(orderType),
		OrderNTuple: &plutusbp.OrderNTuple{
			CarpoolType:        p.Product.CarpoolType,
			RouteType:          p.GetRouteType(),
			IsDualCarpoolPrice: p.IsDualCarpoolPrice(),
			PayType:            int64(p.GetPaymentType()),
			IsSpecialPrice:     p.IsSpecialPrice(),
			LongRentType:       &p.Product.LongRentType,
			CommercialType:     &p.Product.CommercialtType,
			ProductId:          p.Product.ProductID,
			RequireLevel:       p.Product.RequireLevel,
			BusinessId:         p.Product.BusinessID,
		},
	}
	req := &plutusbp.MultilDriverEstimateRequest{
		Params: []*plutusbp.DriverEstimateRequest{params},
	}
	rsp, err := plutus.DriverEstimate(ctx, req)
	if err != nil {
		log.Trace.Errorf(ctx, "meeting_car", "driver estimate err %s", err)
		return
	}
	if rsp != nil && len(rsp.Data) > 0 {
		estimateData := rsp.Data[0]
		if estimateData != nil && estimateData.Bill != nil {
			m.CharterFeeDriver = int64(estimateData.Bill.DynamicTotalFee * 100)
		}
	}
}

// render 渲染
func (m *MeetingTrip) render(ctx context.Context, product *biz_runtime.ProductInfoFull) {
	prov := &data.AnyCarV3Adapter{ProductInfoFull: product}
	msg, _, _ := fee_info_render.GetFeeInfo(ctx, prov)
	// anycarV3返回给端上的带 {}，会议用车不需要，需要去掉
	msg = strings.ReplaceAll(msg, "{", "")
	msg = strings.ReplaceAll(msg, "}", "")
	m.CarName = car_info.GetCarName(ctx, prov)
	m.FeeMsg = msg
	if m.TripList.ComboId != 0 && m.filter != nil && m.filter.Combo != nil {
		combo := m.filter.Combo
		m.FeeMsg = combo.Desc + " " + m.FeeMsg
	}
}
