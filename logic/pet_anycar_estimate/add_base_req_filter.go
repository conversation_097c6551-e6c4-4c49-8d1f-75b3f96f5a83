package pet_anycar_estimate

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_filter/after_dds_filter"
)

func RegisterProductFilter(ctx context.Context, params *BaseParams, generator *biz_runtime.ProductsGenerator) {
	if generator.BaseReqData == nil {
		return
	}
	if cpFilter := after_dds_filter.NewPetFilterSendProducts(ctx,
		params.OrderInfo.ExtendFeatureParsed.MultiRequiredProduct); cpFilter != nil {
		generator.RegisterAfterDdsFilter(cpFilter)
	}

}
