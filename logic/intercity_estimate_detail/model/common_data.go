package model

import (
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
)

type CommonData struct {
	UserInfo      *passport.UserInfo
	Quotation     *biz_runtime.Quotation
	PassengerInfo *models.PassengerDetailInfo

	NTuple *price_api.QuotationNTuple
}

type IdentityDiscountInfo struct {
	HasDiscountSupport bool
	DiscountRatio      float64
}
