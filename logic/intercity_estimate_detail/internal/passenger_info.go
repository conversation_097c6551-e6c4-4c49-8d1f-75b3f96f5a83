package internal

import (
	"context"
	"encoding/json"
	"errors"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts/seat_selection_consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/security"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

type passengerInfo struct {
	req *proto.IntercityEstimateDetailRequest
}

func NewPassengerInfo(req *proto.IntercityEstimateDetailRequest) *passengerInfo {
	return &passengerInfo{
		req: req,
	}
}

// Do ...
func (p *passengerInfo) Do(ctx context.Context) (*models.PassengerDetailInfo, error) {
	if p.req == nil {
		return nil, errors.New("req is nil")
	}

	var (
		logTag = "passenger_info_do"
	)

	if p.req.PassengerInfo == "" {
		return nil, nil
	}

	info := new(models.PassengerDetailInfo)
	err := json.Unmarshal([]byte(p.req.PassengerInfo), info)
	if err != nil {
		log.Trace.Warnf(ctx, logTag, "unmarshal fail, err:%v", err)
		return nil, err
	}

	if info.Mode == models.RealName.ToInt32() || info.Mode == models.RealNameNoHistory.ToInt32() {
		for _, passengerItem := range info.PassengerList {
			if passengerItem == nil {
				continue
			}

			if passengerItem.TicketType == seat_selection_consts.Undefined.ToInt32() {
				passengerItem.TicketType = seat_selection_consts.Adult.ToInt32()
			}

			if passengerItem.TicketType == seat_selection_consts.CarryChildren.ToInt32() {
				continue
			}

			if passengerItem.IdentityID == "" {
				log.Trace.Warnf(ctx, logTag, "identity id is nil, origin:%v", util.JustJsonEncode(passengerItem))
				continue
			}

			passengerItem.DeIdentityID, err = security.AesDecryptMsg(passengerItem.IdentityID)
			if err != nil {
				log.Trace.Warnf(ctx, logTag, "decrypt fail, origin:%v, err:%v", passengerItem.IdentityID, err)
				return nil, err
			}
		}
	}

	return info, nil
}
