package internal

import (
	"context"
	"errors"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type quotationInfo struct {
	req *proto.IntercityEstimateDetailRequest
}

func NewQuotationInfo(req *proto.IntercityEstimateDetailRequest) *quotationInfo {
	return &quotationInfo{
		req: req,
	}
}

// Do ...
func (q *quotationInfo) Do(ctx context.Context) (*biz_runtime.Quotation, error) {
	resp, err := price_api.GetQuotationBatch(ctx, q.buildRequest())
	if err != nil {
		return nil, err
	}

	if resp.Errno != 0 {
		return nil, errors.New("err no is not equal zero")
	}

	if len(resp.Data) <= 0 {
		return nil, errors.New("data is nil")
	}

	return q.formatResp(resp), nil
}

// buildRequest ...
func (q *quotationInfo) buildRequest() *price_api.PriceQuotationBatch {
	if q.req == nil {
		return nil
	}

	estimateIdList := make([]string, 0)
	estimateIdList = append(estimateIdList, q.req.EstimateId)

	return &price_api.PriceQuotationBatch{
		EstimateIdList: estimateIdList,
	}
}

// formatResp ...
func (q *quotationInfo) formatResp(resp *PriceApi.GetQuotationBatchResp) *biz_runtime.Quotation {
	if resp == nil || q.req == nil {
		return nil
	}

	estimateInfo := biz_runtime.Quotation(resp.Data[q.req.EstimateId])

	return &estimateInfo
}
