package blank

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"github.com/tidwall/gjson"
)

const (
	RecCarpoolTab string = "rec_carpool"
)

type Tab struct {
	TabID         string `json:"tab_id"`
	DefaultSelect bool   `json:"default_select"`
}

func BuildEstimateData(ctx context.Context, tabListStr string, scene string) *proto.TabBlank {
	template := dcmp.GetDcmpPlainContent(ctx, "carpool_tab_pincheche-tab_blank")
	textKey := "text"
	if scene != "" {
		textKey = scene + "_" + textKey
	}
	tabBlank := &proto.TabBlank{
		Icon: gjson.Get(template, "icon").String(),
		Text: gjson.Get(template, textKey).String(),
		Button: &proto.SimpleJumpButton{
			Text:   gjson.Get(template, "button.text").String(),
			JumpTo: gjson.Get(template, "button.jump_to").String(),
		},
	}
	if tabListStr == "" {
		return tabBlank
	}
	tabList := getTabList(&tabListStr)
	if tabList != nil && len(tabList) == 2 {
		for _, v := range tabList {
			if v.TabID == RecCarpoolTab {
				tabBlank.Button.JumpTo = gjson.Get(template, "button.jump_to_rec_carpool").String()
				break
			}
		}
	}
	return tabBlank
}

// BuildStopoverPointTabBlank 带途经点请求时下发的数据
func BuildStopoverPointTabBlank(ctx context.Context, req *proto.CarpoolEstimateRequest) *proto.TabBlank {
	template := dcmp.GetDcmpPlainContent(ctx, "carpool_tab_pincheche-stopoverpoint_blank")
	tabBlank := &proto.TabBlank{
		Icon: gjson.Get(template, "icon").String(),
		Text: gjson.Get(template, "text").String(),
		Button: &proto.SimpleJumpButton{
			Text:   gjson.Get(template, "button.text").String(),
			JumpTo: gjson.Get(template, "button.jump_to").String(),
		}}

	tabList := getTabList(req.TabList)
	if tabList != nil && len(tabList) == 2 {
		for _, v := range tabList {
			if v.TabID == RecCarpoolTab {
				tabBlank.Button.JumpTo = gjson.Get(template, "button.jump_to_rec_carpool").String()
				break
			}
		}
	}

	return tabBlank
}

func getTabList(tabListStr *string) []Tab {
	tabList := []Tab{}
	if tabListStr == nil {
		return tabList
	}
	_ = json.Unmarshal([]byte(*tabListStr), &tabList)
	return tabList
}
