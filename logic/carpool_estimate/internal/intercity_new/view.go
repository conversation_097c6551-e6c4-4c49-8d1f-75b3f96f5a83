package intercity_new

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_new"
	intercity_sku2 "git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_sku"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/carpool_booking_time_module"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/carpool_seat_module_render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/carpool_seat_module_with_children_ticket"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/estimate_extra"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/intercity_head_url"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/payment"

	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type RenderPage struct {
}

func (r *RenderPage) SetTabType(data *proto.CarpoolEstimateResponseData) {
	data.TabType = proto.TabType_InterCitySku
}

func BuildEstimateData(ctx context.Context, product *biz_runtime.ProductInfoFull) *proto.IntercitySkuStationData {
	if product == nil || product.Product == nil {
		return nil
	}
	prod := &AdapterInterCity{product}
	businessName := car_info.GetBusinessName(ctx, product)

	resp := initEstimateData(product)
	// render 渲染
	resp.CarpoolBooking = carpool_booking_time_module.InterCityCarpoolBookingTime(ctx, prod, 0)
	resp.FeeMsg = fee_info_render.FeeMsg(ctx, prod)
	resp.FeeDescList = intercity_sku2.FeeDescList(ctx, prod, prod)
	resp.IntroImage = dcmp.GetDcmpContent(ctx, "intercity_new-intro_image", nil)
	resp.CarIcon = dcmp.GetDcmpContent(ctx, "intercity_new-car_icon", nil)
	resp.FeeDetailUrl = dcmp.GetDcmpContent(ctx, "common-fee_detail_url_v4", nil)
	resp.SubTitle = intercity_new.SubTitle(ctx, prod, businessName)
	resp.ConfirmButton = dcmp.GetDcmpContent(ctx, "intercity_new-new_order_button", nil)
	resp.UserPayInfo = payment.UserPayInfo(ctx, prod)
	resp.IsSelected = true // 暂时
	resp.StyleType = proto.StyleType_InterCitySku
	resp.BorderColor = dcmp.GetDcmpContent(ctx, "intercity_estimate-border_color", nil)
	resp.CornerImage = dcmp.GetDcmpContent(ctx, "intercity_estimate-corner_image", nil)
	resp.CarpoolBookingOuter = intercity_new.CarpoolBookingOuter(ctx, prod, resp.CarpoolBooking)
	resp.HitShowH5Type = estimate_extra.GetHitShowH5Type(ctx, product)
	resp.CarpoolSeatModule = carpool_seat_module_render.InterCityCarpool(ctx, prod)
	return resp
}

func (r *RenderPage) RenderPageData(ctx context.Context, product *biz_runtime.ProductInfoFull, res *proto.CarpoolEstimateResponseData) {
	res.IntercitySku = BuildEstimateData(ctx, product)
}
func (r *RenderPage) BuildExTraData(ctx context.Context, data *models.BaseReqData, p *biz_runtime.ProductInfoFull, res *proto.CarpoolEstimateResponseData) {
	if res == nil {
		res = &proto.CarpoolEstimateResponseData{}
	}
	if data != nil {
		res.PluginPageInfo = estimate_extra.GetPluginPageInfo(ctx, data, p.GetHolidayFee(), true)
		if data.CommonBizInfo.IsRestSeatInfo {
			toast := dcmp.GetDcmpPlainContent(ctx, "intercity_sku-seat_reset")
			res.ForceNoticeToast = &toast
		}
	}
}

func initEstimateData(product *biz_runtime.ProductInfoFull) *proto.IntercitySkuStationData {
	data := &proto.IntercitySkuStationData{
		EstimateId: product.Product.EstimateID,
		ExtraMap: &proto.IntercityNewOrderParam{
			ProductCategory: product.Product.ProductCategory,
			ComboType:       product.Product.ComboType,
			RequireLevel:    product.Product.RequireLevelInt,
			BusinessId:      product.Product.BusinessID,
			PageType:        product.BaseReqData.CommonInfo.PageType,
			RouteType:       int32(product.Product.RouteType),
		},
	}
	if product.Product.BizInfo != nil {
		data.ExtraMap.ComboId = product.Product.BizInfo.ComboID
	}
	return data
}

func BuildOutSideEstimateData(ctx context.Context, product *biz_runtime.ProductInfoFull) (resp *proto.IntercityEstimateData) {
	if product == nil || product.Product == nil {
		return nil
	}
	prod := &AdapterInterCity{product}

	headImgUrl, headDetailUrl := intercity_head_url.GetHeadInfo(ctx, prod)
	ret := &proto.IntercityEstimateData{
		HeadImgUrl:        headImgUrl,
		HeadDetailUrl:     headDetailUrl,
		EstimateCardData:  []*proto.IntercitySkuStationData{},
		UserPayInfo:       payment.UserPayInfo(ctx, prod),
		FeeDetailUrl:      dcmp.GetDcmpContent(ctx, "common-fee_detail_url_v4", nil),
		CarpoolSeatModule: carpool_seat_module_with_children_ticket.NewCarpoolSeatModule(product).GetCarpoolSeatModule(ctx, prod),
		PluginPageInfo:    estimate_extra.GetPluginPageInfo(ctx, product.BaseReqData, product.GetHolidayFee(), true),
	}
	return ret
}

func getHeadImgUrl(ctx context.Context, product *biz_runtime.ProductInfoFull) string {
	headImg := dcmp.GetDcmpContent(ctx, "intercity_estimate-head_img_url", nil)
	if product.GetAccessKeyId() == consts.AccessKeyIDDiDiIos || product.GetAccessKeyId() == consts.AccessKeyIDDiDiAndroid {
		headImg = dcmp.GetDcmpContent(ctx, "intercity_estimate-head_img_url_app", nil)
	}

	return headImg
}
