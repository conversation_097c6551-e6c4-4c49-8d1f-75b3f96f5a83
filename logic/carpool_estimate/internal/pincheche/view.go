package pincheche

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/pincheche"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/estimate_extra"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_detail_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/payment"
)

type RenderPage struct {
}

func (r *RenderPage) SetTabType(data *proto.CarpoolEstimateResponseData) {
	data.TabType = proto.TabType_Pincheche
}

func (r *RenderPage) RenderPageData(ctx context.Context, product *biz_runtime.ProductInfoFull, res *proto.CarpoolEstimateResponseData) {

	if product == nil || product.Product == nil {
		return
	}

	prod := &ViewAdapter{product}

	res.Pincheche = &proto.PinchecheData{
		EstimateId:        prod.GetEstimateID(),
		IntroMsg:          pincheche.IntroMsg(ctx, prod),
		ExtraIntroTag:     pincheche.ExtraIntroTag(ctx, prod, false),
		MultiPriceDesc:    pincheche.MultiPriceDesc(ctx, prod, prod.MultiPrice()...), // MAYBUG 空指针 unpack
		ExtraPriceDesc:    pincheche.ExtraPriceDesc(ctx, prod.ExtraPrice(ctx)),
		CarpoolSeatModule: pincheche.CarpoolSeatModule(ctx, prod),
		CarpoolBooking:    pincheche.CarpoolBooking(ctx, prod),
		UserPayInfo:       payment.UserPayInfo(ctx, prod),
		ExtraMap:          pincheche.ExtraMap(ctx, prod),
		ExtraPriceTag:     pincheche.ExtraPriceTag(ctx, prod.ExtraPriceTag(ctx)...),
		FeeDetailUrl:      fee_detail_info.GetDetailUrlV2(ctx, prod),
		ConfirmButton:     pincheche.ConfirmButtonContent(ctx, prod),
		// IntroMsgTag:       pincheche.IntroMsgTag(ctx, prod),
	}
}
func (r *RenderPage) BuildExTraData(ctx context.Context, req *models.BaseReqData, p *biz_runtime.ProductInfoFull, res *proto.CarpoolEstimateResponseData) {

	res.PluginPageInfo = estimate_extra.GetPluginPageInfo(ctx, req, p.GetHolidayFee(), true)
}
