package mq

import (
	"encoding/json"
	"fmt"
	"strconv"
	"testing"
)

func BenchmarkFmt(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_ = fmt.Sprintf("%d.business_id", i)
	}
}

func BenchmarkConvert(b *testing.B) {
	const y = ".business_id"
	for i := 0; i < b.N; i++ {
		x := strconv.Itoa(i)
		_ = x + y
	}
}

type TaoWa struct{}

func (t *TaoWa) MarshalJSON() ([]byte, error) {
	return json.Marshal(t)
}

func TestMarshalPointer_1(test *testing.T) {
	t := TaoWa{}
	if b, e := json.Marshal(t); e != nil {
		test.Error(e)
	} else {
		test.Log(b)
	}
}
func TestMarshalPointer_2(test *testing.T) {
	t := TaoWa{}
	if b, e := json.Marshal(t); e != nil {
		test.Error(e)
	} else {
		test.Log(b)
	}
}

type TaoWa2 struct{}

func (t TaoWa2) MarshalJSON() ([]byte, error) {
	return json.Marshal(t)
}

//func TestMarshalStruct_2(test *testing.T) {
//	t := TaoWa2{}
//	if b, e := json.Marshal(t); e != nil {
//		test.Error(e)
//	} else {
//		test.Log(b)
//	}
//}
