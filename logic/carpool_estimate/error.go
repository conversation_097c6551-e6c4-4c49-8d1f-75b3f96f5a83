package carpool_estimate

import (
	"context"
	"strconv"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
)

var _ BizError.BizError = Error{}

const (
	ErrnoServiceUnavailable BizError.BizErrno = 400
)

var (
	ErrServiceUnavailable = Error{no: ErrnoServiceUnavailable}
)

type Error struct {
	no BizError.BizErrno
}

func (e Error) toStrNo() string {
	return strconv.Itoa(e.Errno())
}

func (e Error) Error() string {
	return dcmp.GetJSONContentWithPath(context.Background(), "carpool_tab_pincheche-error_msg", nil, e.toStrNo())
}
func (e Error) Errno() BizError.BizErrno {
	return e.no
}
