package carpool_estimate

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/view/render/price_info_desc_list"
	LegoContext "git.xiaojukeji.com/lego/context-go"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/carpool_estimate/internal/intercity_sku"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

const PublicKey = "g_order_cap_multi_estimate_price"

const (
	FromTypeAnyCar          = 1
	FromTypeDiamondPosition = 2
)

type busTransData struct {
	PageType     int32  `json:"page_type"`
	GuideTraceId string `json:"guide_trace_id"`
	Dchn         string `json:"dchn"`
}

func WritePublicLog(ctx context.Context, products []*biz_runtime.ProductInfoFull, req *proto.CarpoolEstimateRequest, tabType int32) {

	transData := busTransData{}
	var err error
	if req.TransData != nil {
		err = json.Unmarshal([]byte(*req.TransData), &transData)
	}

	for _, product := range products {
		if product == nil || product.Product == nil || product.GetSceneEstimatePrice() == nil {
			continue
		}

		logInfo := make(map[string]interface{})
		// 索引key
		logInfo["estimate_trace_id"] = util.GetTraceIDFromCtxWithoutCheck(ctx)
		logInfo["estimate_id"] = product.GetEstimateID()

		if req != nil {
			logInfo["xpsid"] = req.Xpsid
			logInfo["xpsid_root"] = req.XpsidRoot
			logInfo["dchn"] = req.Dchn
		}

		// 地理位置信息
		areaInfo := product.GetAreaInfo()
		logInfo["area"] = areaInfo.City
		logInfo["to_area"] = areaInfo.ToArea
		logInfo["flat"] = areaInfo.FromLat
		logInfo["flng"] = areaInfo.FromLng
		logInfo["tlat"] = areaInfo.ToLat
		logInfo["tlng"] = areaInfo.ToLng
		logInfo["district"] = areaInfo.District
		logInfo["current_lat"] = areaInfo.CurLat
		logInfo["current_lng"] = areaInfo.CurLng
		logInfo["county"] = areaInfo.FromCounty
		logInfo["to_county"] = areaInfo.ToCounty
		logInfo["from_name"] = util.StringEscape(areaInfo.FromName)
		logInfo["to_name"] = util.StringEscape(areaInfo.ToName)

		// 端信息
		commonInfo := product.GetClientInfo()
		logInfo["app_version"] = commonInfo.AppVersion
		logInfo["client_type"] = commonInfo.ClientType
		logInfo["access_key_id"] = commonInfo.AccessKeyID
		logInfo["channel"] = commonInfo.Channel
		logInfo["page_type"] = commonInfo.PageType
		logInfo["lang"] = commonInfo.Lang
		if req.IsGuide != nil {
			logInfo["is_guide"] = *(req.IsGuide)
		}
		if req.AgentType != nil {
			logInfo["agent_type"] = *(req.AgentType)
		}
		if req.BizSceneType != nil {
			logInfo["biz_scene_type"] = *(req.BizSceneType)
		}
		// 产品信息
		logInfo["product_category"] = product.GetProductCategory()
		logInfo["require_level"] = product.Product.RequireLevel
		logInfo["combo_type"] = product.Product.ComboType
		logInfo["carpool_type"] = product.Product.CarpoolType
		logInfo["product_id"] = product.Product.ProductID
		if product.GetBizInfo() != nil {
			logInfo["combo_id"] = product.GetBizInfo().ComboID
		}

		// 用户信息
		logInfo["pid"] = product.GetUserInfo().PID

		if product.Product.BizInfo != nil {
			logInfo["carpool_seat_num"] = product.Product.BizInfo.CarpoolSeatNum
		}
		// 大车数据埋点使用
		if carpool.IsIntercityStation(ctx, int(product.Product.CarpoolType)) {
			if len(req.GetDepartureTime()) > 0 { // 首次预估该值为空；二次预估有值，且值为用户选择的时间片
				logInfo["is_cart_second_estimate"] = 1
				logInfo["departure_time"] = req.GetDepartureTime()
			} else {
				logInfo["is_cart_second_estimate"] = 0
				if product.GetBizInfo() != nil {
					logInfo["departure_time"] = strconv.Itoa(int(product.GetBizInfo().DepartureTime))
				}

			}
		}
		if product != nil && product.GetBizInfo() != nil && product.GetBizInfo().RouteInfo != nil && product.GetBizInfo().RouteInfo.RouteGroup != nil {
			if intercity_sku.IsSkuModel(product) {
				logInfo["is_sku_model"] = 1
			}
		}

		if product != nil && product.BaseReqData != nil && (product.BaseReqData.CommonBizInfo.IsAllowHomeOwnerTicket() || product.BaseReqData.CommonBizInfo.GetChildTicket() != nil) {
			logInfo["support_select_seat"] = 1
		}

		// 大巴改签
		if req.SelectedBusServiceShiftId != nil {
			logInfo["selected_bus_service_shift_id"] = *req.SelectedBusServiceShiftId
		}

		if req.SelectedBusServiceDay != nil {
			logInfo["selected_bus_service_shift_day"] = *req.SelectedBusServiceDay
		}

		if req.PreOrderId != nil {
			logInfo["pre_order_id"] = *req.PreOrderId
		}

		logInfo["from_type"] = buildFromType(product.BaseReqData)
		//导流位trace_id
		if req.TransData != nil && err == nil {
			logInfo["guide_trace_id"] = transData.GuideTraceId
			if req.Dchn == nil {
				logInfo["dchn"] = transData.Dchn
			}
		}

		if tabType != 0 {
			logInfo["tab_type"] = tabType
		}

		log.Public.Public(ctx, PublicKey, logInfo)
	}
}

func WritePublicLogV2(ctx context.Context, operaKey string, products []*biz_runtime.ProductInfoFull, req *proto.CarpoolEstimateRequest, baseReq *models.BaseReqData) {
	for _, product := range products {
		if product == nil || product.Product == nil || product.GetSceneEstimatePrice() == nil {
			continue
		}

		writePublicLogCore(ctx, operaKey, product, req, baseReq)
	}
}

func writePublicLogCore(ctx context.Context, operaKey string, product *biz_runtime.ProductInfoFull, req *proto.CarpoolEstimateRequest, reqData *models.BaseReqData) {
	if product == nil || product.Product == nil || reqData == nil || product.GetBillInfo() == nil {
		return
	}

	transData := busTransData{}
	var err error
	if req.TransData != nil {
		err = json.Unmarshal([]byte(*req.TransData), &transData)
	}

	bill := product.GetBillInfo()

	logInfo := make(map[string]interface{})

	logInfo["xpsid"] = reqData.CommonInfo.Xpsid
	logInfo["xpsid_root"] = reqData.CommonInfo.XpsidRoot
	logInfo["dchn"] = req.Dchn

	// ..... 基础信息 .....
	logInfo["estimate_trace_id"] = LegoContext.GetTrace(ctx).GetTraceId()
	logInfo["estimate_id"] = product.GetEstimateID()
	logInfo["pLang"] = reqData.CommonInfo.Lang
	logInfo["lang"] = reqData.CommonInfo.Lang
	logInfo["member_level_id"] = product.GetLevelID()
	logInfo["page_type"] = reqData.CommonInfo.PageType
	logInfo["menu_id"] = reqData.CommonInfo.MenuID
	logInfo["access_key_id"] = reqData.CommonInfo.AccessKeyID
	logInfo["channel"] = reqData.CommonInfo.Channel
	logInfo["appversion"] = reqData.CommonInfo.AppVersion
	logInfo["app_version"] = reqData.CommonInfo.AppVersion
	logInfo["agent_type"] = "both_call_anycar"
	logInfo["channel_id"] = reqData.CommonInfo.Channel
	logInfo["client_type"] = reqData.CommonInfo.ClientType
	logInfo["biz_user_type"] = reqData.PassengerInfo.UserType
	logInfo["call_car_type"] = reqData.CommonInfo.CallCarType

	logInfo["pid"] = reqData.PassengerInfo.PID
	logInfo["uid"] = reqData.PassengerInfo.UID
	logInfo["phone"] = reqData.PassengerInfo.Phone

	logInfo["scene_type"] = product.GetSceneType()
	logInfo["product_id"] = product.GetProductId()
	logInfo["business_id"] = product.GetBusinessID()
	logInfo["require_level"] = product.GetRequireLevel()
	logInfo["combo_type"] = product.GetComboType()
	logInfo["product_category"] = product.GetProductCategory()
	logInfo["count_price_type"] = bill.CountPriceType
	logInfo["carpool_type"] = product.GetCarpoolType()
	logInfo["is_special_price"] = product.GetIsSpecialPrice()
	logInfo["order_type"] = product.GetOrderType()
	logInfo["exam_type"] = product.GetExamType()
	logInfo["level_type"] = product.GetLevelType()
	logInfo["airport_type"] = product.GetAirType()
	logInfo["railway_type"] = product.GetRailType()
	logInfo["estimate_pc_id"] = product.GetProductCategory()

	logInfo["default_pay_type"] = product.GetDefaultPayType()

	logInfo["origin_page_type"] = reqData.CommonInfo.PageType
	logInfo["source_id"] = reqData.CommonInfo.SourceID
	logInfo["combo_id"] = product.GetComboID()

	logInfo["order_n_tuple"] = util.JustJsonEncode(product.Product)

	logInfo["current_lng"] = reqData.AreaInfo.CurLng
	logInfo["current_lat"] = reqData.AreaInfo.CurLat
	logInfo["flng"] = reqData.AreaInfo.FromLng
	logInfo["flat"] = reqData.AreaInfo.FromLat
	logInfo["tlng"] = reqData.AreaInfo.ToLng
	logInfo["tlat"] = reqData.AreaInfo.ToLat
	logInfo["county"] = reqData.AreaInfo.FromCounty
	logInfo["to_county"] = reqData.AreaInfo.ToCounty
	logInfo["area"] = reqData.AreaInfo.Area
	logInfo["to_area"] = reqData.AreaInfo.ToArea
	logInfo["from_name"] = util.StringEscape(reqData.AreaInfo.FromName)
	logInfo["to_name"] = util.StringEscape(reqData.AreaInfo.ToName)
	logInfo["stopover_points"] = util.JustJsonEncode(reqData.AreaInfo.StopoverPointInfo)
	logInfo["district"] = reqData.AreaInfo.District
	logInfo["starting_poi_id"] = reqData.AreaInfo.FromPoiID
	logInfo["dest_poi_id"] = reqData.AreaInfo.ToPoiID

	logInfo["carpool_seat_num"] = product.GetExactSeatNum()
	logInfo["departure_time"] = req.GetDepartureTime()

	// ..... 价格信息 .....
	logInfo["driver_metre"] = bill.DriverMetre
	logInfo["driver_minute"] = bill.DriverMinute
	logInfo["time_cost"] = bill.DriverMinute
	logInfo["total_fee"] = bill.TotalFee
	logInfo["pre_total_fee"] = bill.PreTotalFee
	logInfo["dynamic_total_fee"] = bill.DynamicTotalFee
	logInfo["estimate_fee"] = product.GetEstimateFee()
	logInfo["dynamic_diff_price"] = bill.DynamicDiffPrice
	// 开平新动调
	logInfo["dups_dynamic_times"] = bill.DupsDynamicTimes
	logInfo["dups_dynamic_raise"] = bill.DupsDynamicRaise
	logInfo["cap_price"] = bill.CapPrice
	logInfo["highway_fee"] = bill.HighwayFee
	logInfo["red_packet"] = price_info_desc_list.GetValueFromDisplayLines(product, price_info_desc_list.RedPacketFee)

	logInfo["dynamic_times"] = bill.DynamicTimes
	if bill.DynamicInfo != nil {
		logInfo["dynamic_price_id"] = bill.DynamicInfo.DynamicPriceId
	}

	logInfo["is_guide"] = util.Int32Ptr2Int32(req.IsGuide)

	logInfo["from_type"] = buildFromType(reqData)
	logInfo["form_version"] = buildPinchecheFormVersion(product.BaseReqData)
	//导流位trace_id
	if req.TransData != nil && err == nil {
		logInfo["guide_trace_id"] = transData.GuideTraceId
		if req.Dchn == nil {
			logInfo["dchn"] = transData.Dchn
		}
	}

	log.Public.Public(ctx, operaKey, logInfo)
}

func buildFromType(reqData *models.BaseReqData) interface{} {
	if reqData == nil {
		return 0
	}

	if reqData.CommonInfo.FromType > 0 {
		return reqData.CommonInfo.FromType
	}

	return 0
}

func buildPinchecheFormVersion(reqData *models.BaseReqData) interface{} {
	if reqData == nil {
		return ""
	}

	if reqData.CommonInfo.PageType != page_type.PageTypeLowCarpoolEstimate {
		return ""
	}

	if reqData.CommonBizInfo.PinCheCheFormInfo.FormSytle == 1 {
		return "2.0"
	}

	return "1.0"
}
