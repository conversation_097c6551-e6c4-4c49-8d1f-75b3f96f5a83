package multi_estimate_carpool_order

import (
	"context"
	//"errors"
	Dos "git.xiaojukeji.com/dirpc/dirpc-go-http-Dos"
	//"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
	//"git.xiaojukeji.com/gulfstream/passenger-common/biz/carpoolintercity"
	//"github.com/agiledragon/gomonkey/v2"
	"github.com/smartystreets/goconvey/convey"
	"testing"
)

//func TestServiceRequest_buildOrderInfo(t *testing.T) {
//	convey.Convey("Test buildOrderInfo", t, func() {
//		ctx := context.Background()
//		req := &ServiceRequest{
//			DriverId: "1001",
//			TravelId: "2001",
//		}
//
//		convey.Convey("when get travel order failed", func() {
//			patches := gomonkey.ApplyFunc(carpoolintercity.GetTravelOrderInfo, func(_ context.Context, _ *Dos.GetTravelOrderInfoReq) ([]*Dos.OrderInfo, error) {
//				return nil, errors.New("rpc error")
//			})
//			defer patches.Reset()
//
//			err := req.buildOrderInfo(ctx, []string{"order_123"})
//			convey.So(err, convey.ShouldNotBeNil)
//		})
//
//		convey.Convey("when process valid orders", func() {
//			testOrders := []*Dos.OrderInfo{
//				{
//					OrderId:      "123",
//					CarpoolType:  "10", // mini bus
//					EstimatePcId: util.StringPtr("300"),
//					OrderStatus:  "2", // begin charge
//					IsDualCarpoolPrice: "false",
//				},
//			}
//
//			patches := gomonkey.NewPatches()
//			defer patches.Reset()
//
//			patches.ApplyFunc(carpoolintercity.GetTravelOrderInfo, func(_ context.Context, _ *Dos.GetTravelOrderInfoReq) ([]*Dos.OrderInfo, error) {
//				return testOrders, nil
//			})
//			patches.ApplyFunc(util.String2int64, func(_ context.Context, _ string) int64 { return 123 })
//			patches.ApplyFunc(util.DecodeOrderID, func(string) (int64, string, error) { return 123, "010", nil })
//
//			convey.Convey("should process begin charge order", func() {
//				testOrders[0].CarpoolType = "12" // smart bus
//				err := req.buildOrderInfo(ctx, []string{"order_123"})
//				convey.So(err, convey.ShouldNotBeNil)
//				convey.So(req.OrderInfoList, convey.ShouldBeEmpty)
//			})
//		})
//	})
//}

func TestBuildOrderUsedCard(t *testing.T) {
	convey.Convey("Test buildOrderUsedCard", t, func() {
		ctx := context.Background()
		sr := &ServiceRequest{
			OrderInfoList: make(map[int64]*order_info.OrderInfo),
		}

		convey.Convey("when no orders", func() {
			sr.buildOrderUsedCard(ctx)
			convey.So(sr.OrderUsedCard, convey.ShouldBeEmpty)
		})

		convey.Convey("when filter mini bus", func() {
			// 初始化小巴订单
			sr.OrderInfoList[173628766947] = &order_info.OrderInfo{}
			sr.OrderInfoList[173628766947].OrderInfo = &Dos.OrderInfo{
				OrderId:     "173628766947",
				CarpoolType: "10",
			}

			sr.buildOrderUsedCard(ctx)
			convey.So(sr.OrderUsedCard, convey.ShouldBeEmpty)
		})

		convey.Convey("when filter smart bus", func() {
			// 初始化公交小巴订单
			sr.OrderInfoList[173628766888] = &order_info.OrderInfo{}
			sr.OrderInfoList[173628766888].OrderInfo = &Dos.OrderInfo{
				OrderId:     "173628766888",
				CarpoolType: "12",
			}

			sr.buildOrderUsedCard(ctx)
			convey.So(sr.OrderUsedCard, convey.ShouldBeEmpty)
		})
	})
}
