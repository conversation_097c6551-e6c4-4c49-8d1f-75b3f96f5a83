package logic

import (
	"context"
	"encoding/json"
	"sort"
	"strconv"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"

	get_dynamic_fee_desc_list "git.xiaojukeji.com/gulfstream/mamba/render/private/getDynamicFeeDescList"
	rec_carpool2 "git.xiaojukeji.com/gulfstream/mamba/render/private/rec_carpool"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_detail_info"

	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"github.com/tidwall/gjson"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/rec_carpool_estimate/logic/internal"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	engine_model "git.xiaojukeji.com/gulfstream/mamba/render/pkg/fee_desc_engine/model"
	trace "git.xiaojukeji.com/lego/context-go"
)

const NSPayment = "payment_type_component"
const ConfigName = "payment_type_list_info"

func Render(ctx context.Context, products []*biz_runtime.ProductInfoFull) (resp *proto.RecCarpoolEstimateResponse, selectTypeMap map[string]int32, err error) {
	//获取快车预估价
	SetFastCarEstimateFee(ctx, products)
	// 非拼车品类预估价格最小值
	minNonCarpoolEstimateFee := GetMinNonCarpool(ctx, products)
	// 获取表单样式
	formStyle := GetFormStyle(ctx, products)
	// 是否有拼车品类
	haveCarpool := IsHaveCarpool(ctx, products)
	// mq todo 迁移
	// 二次预估 todo
	hitNewStyle := isHitNewStyle(ctx, products)

	estimateData := []*proto.RecCarpoolEstimateData{}
	noCarpoolSelectFeeDescMap := make(map[int64][]*engine_model.FeeOutput, 0)
	for _, product := range products {
		if product == nil || product.Product == nil {
			continue
		}

		prod := &internal.ViewAdapter{ProductInfoFull: product}
		estimateData = append(estimateData, &proto.RecCarpoolEstimateData{
			EstimateId:  prod.GetEstimateID(),
			IntroMsg:    rec_carpool2.IntroMsg(ctx, prod, hitNewStyle),
			CategoryId:  rec_carpool2.CategoryID(ctx, prod),
			FeeMsg:      rec_carpool2.FeeMsg(ctx, prod),
			FeeAmount:   rec_carpool2.FeeAmount(ctx, prod),
			SeatNum:     &prod.GetBizInfo().CarpoolSeatNum,
			ExtraMap:    rec_carpool2.ExtraMap(ctx, prod),
			UserPayInfo: rec_carpool2.UserPayInfo(ctx, prod),
			RouteIdList: rec_carpool2.RouteIdList(ctx, prod),

			FeeDescList:     rec_carpool2.FeeDescList(ctx, prod, noCarpoolSelectFeeDescMap, prod.GetPageType()),
			MultiPriceList:  rec_carpool2.MultiPriceList(ctx, prod),
			BubbleTip:       rec_carpool2.BubbleTip(ctx, prod, minNonCarpoolEstimateFee),
			SelectType:      rec_carpool2.SelectType(ctx, prod),
			RecIcon:         prod.GetRecIcon(),
			ProductCategory: int32(prod.GetProductCategory()),
			ExtraPriceDesc:  rec_carpool2.ExtraPriceDesc(ctx, prod),
		})
	}
	// 选中返回 for: log
	selectTypeMapForLog := getSelectTypeMap(estimateData)

	recInfo := GetRecInfo(ctx, products, estimateData, formStyle)
	// 排序 聚合样式
	estimateData = AssembleGroupData(ctx, estimateData, formStyle, hitNewStyle, noCarpoolSelectFeeDescMap)
	// 排序不拼座顺序
	SortGroupData(ctx, estimateData, formStyle)
	// 全局数据
	return &proto.RecCarpoolEstimateResponse{
		EstimateData:            estimateData,
		EstimateTraceId:         util.GetTraceIDFromCtxWithoutCheck(ctx),
		FeeDetailUrl:            fee_detail_info.GetDetailUrl(ctx),
		IsSupportMultiSelection: 1,
		FormStyle:               int32(formStyle),
		CategoryInfoList:        GetCategoryInfoList(ctx, haveCarpool, hitNewStyle),
		SeatPrefixDesc:          GetSeatPrefixDesc(ctx),
		UserPayInfo:             GetUserPayInfo(ctx, products),
		RecInfo:                 recInfo,
	}, selectTypeMapForLog, nil
}

func getSelectTypeMap(estimateData []*proto.RecCarpoolEstimateData) map[string]int32 {
	res := map[string]int32{}
	for _, v := range estimateData {
		res[v.EstimateId] = v.SelectType
	}
	return res
}

func SetFastCarEstimateFee(ctx context.Context, products []*biz_runtime.ProductInfoFull) {
	estimateFee := float64(-1)
	for _, product := range products {
		if product.GetProductCategory() == estimate_pc_id.EstimatePcIdFastCar {
			estimateFee = product.GetEstimateFee()
			break
		}
	}
	for _, pFull := range products {
		pFull.BaseReqData.CommonBizInfo.FastCarPrice = estimateFee
	}
}

func GetMinNonCarpool(ctx context.Context, products []*biz_runtime.ProductInfoFull) float64 {
	minEstimateFee := float64(-1)
	if len(products) == 0 {
		return minEstimateFee
	}

	for _, item := range products {
		if item.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolStation || item.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolFlatRateBySeat {
			continue
		}
		if minEstimateFee == -1 || item.GetEstimateFee() < minEstimateFee {
			minEstimateFee = item.GetEstimateFee()
		}
	}
	return minEstimateFee
}

func IsHaveCarpool(ctx context.Context, products []*biz_runtime.ProductInfoFull) bool {
	if len(products) == 0 {
		return false
	}
	for _, product := range products {
		if product == nil || product.Product == nil {
			continue
		}
		if product.Product.CarpoolType != 0 {
			return true
		}
	}
	return false
}

func GetCategoryInfoList(ctx context.Context, haveCarpool bool, isNewStyle bool) []*proto.Category {
	category := []*proto.Category{}

	dcmpConfStr := dcmp.GetDcmpPlainContent(ctx, "rec_carpool-category_info")

	key := "category_info_list"
	if isNewStyle {
		key = "category_info_list_new"
	}

	err := json.Unmarshal([]byte(gjson.Get(dcmpConfStr, key).String()), &category)
	if err != nil {
		log.Trace.Warnf(ctx, consts.TagErrJsonUnMarshal, "dcmp key %s with string %s", "rec_carpool-category_info", dcmpConfStr)
		return nil
	}

	for _, item := range category {
		if item.SeatNum == 0 || (item.SeatNum != 0 && haveCarpool) {
			item.EnableDisplay = 1
		}
	}

	return category

}

func GetSeatPrefixDesc(ctx context.Context) *string {

	dcmpConfStr := dcmp.GetDcmpPlainContent(ctx, "rec_carpool-category_info")
	res := gjson.Get(dcmpConfStr, "seat_prefix_desc").String()
	if res != "" {
		return &res
	}
	return nil
}

type SortAllPaymentInfo struct {
	AllPaymentInfo []*proto.UserPayInfoItem
	WeightMap      map[string]int32
}
type PaymentType struct {
	PaymentType string `json:"payment_type"`
	SortScore   string `json:"sort_score"`
}

type PaymentTypeList struct {
	PaymentTypeList []*PaymentType `json:"payment_type_list"`
	Lang            string         `json:"__language"`
}

func (s SortAllPaymentInfo) Len() int {
	return len(s.AllPaymentInfo)
}

func (s SortAllPaymentInfo) Swap(i, j int) {
	s.AllPaymentInfo[i], s.AllPaymentInfo[j] = s.AllPaymentInfo[j], s.AllPaymentInfo[i]
}

func (s SortAllPaymentInfo) Less(i, j int) bool {
	return s.WeightMap[s.AllPaymentInfo[i].Tag] > s.WeightMap[s.AllPaymentInfo[j].Tag]

}
func GetUserPayInfo(ctx context.Context, products []*biz_runtime.ProductInfoFull) *proto.RecCarpoolUserPayInfo {
	defaultPayType := make(map[int32]struct{}, 0)
	allPaymentInfoMap := make(map[int32]*proto.UserPayInfoItem, 0)
	allPaymentInfo := []*proto.UserPayInfoItem{}
	sortAllPaymentInfo := SortAllPaymentInfo{
		AllPaymentInfo: allPaymentInfo,
		WeightMap:      getPaymentWeight(ctx),
	}
	dcmpConfStr := dcmp.GetDcmpPlainContent(ctx, "rec_carpool-user_pay_info")
	userPayInfo := &proto.RecCarpoolUserPayInfo{
		Title:    gjson.Get(dcmpConfStr, "title").String(),
		SubTitle: gjson.Get(dcmpConfStr, "sub_title").String(),
	}
	for _, product := range products {
		if product == nil {
			continue
		}
		defaultPayType[product.GetDefaultPayType()] = struct{}{}
		for _, item := range product.GetAllPaymentOption() {
			if item == nil {
				continue
			}
			if allPaymentInfoMap[item.PayType] == nil {
				allPaymentInfoMap[item.PayType] = &proto.UserPayInfoItem{
					Tag:        strconv.Itoa(int(item.PayType)),
					Msg:        *item.ChannelName,
					Disabled:   item.Disabled,
					IsSelected: 0,
				}
			}

		}
	}
	if products[0].BaseReqData.CommonInfo.PaymentsType != 0 && products[0].BaseReqData.CommonInfo.PaymentsType != -1 {
		if _, status := defaultPayType[products[0].BaseReqData.CommonInfo.PaymentsType]; status {
			defaultPayType = map[int32]struct{}{}
			defaultPayType[products[0].BaseReqData.CommonInfo.PaymentsType] = struct{}{}
		}
	}
	for k := range defaultPayType {
		if allPaymentInfoMap[k] != nil {
			allPaymentInfoMap[k].IsSelected = 1
		}
	}
	disabledDefault := int32(0)
	if len(allPaymentInfoMap) == 0 {
		allPaymentInfoMap[2] = &proto.UserPayInfoItem{
			Tag:        "2",
			Msg:        "个人支付",
			Disabled:   &disabledDefault,
			IsSelected: 1,
		}
	}
	for _, v := range allPaymentInfoMap {
		allPaymentInfo = append(allPaymentInfo, v)
	}
	if len(defaultPayType) > 1 {
		for _, v := range allPaymentInfo {
			v.IsSelected = 0
		}
	}
	sortAllPaymentInfo.AllPaymentInfo = allPaymentInfo
	sort.Sort(sortAllPaymentInfo)
	userPayInfo.PaymentList = sortAllPaymentInfo.AllPaymentInfo
	return userPayInfo
}

func getPaymentWeight(ctx context.Context) map[string]int32 {
	configs, erro := apollo.GetConfigsByNamespace(ctx, NSPayment)
	if configs == nil || erro != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "apollo payment_type_component nil or %v", erro)
		return nil
	}
	paymentTypeList := []*PaymentTypeList{}
	err := json.Unmarshal(configs, &paymentTypeList)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "json.Unmarshal payment_type_list err with %v and conf %v", err, string(configs))
		return nil
	}
	paymentType := []*PaymentType{}
	for _, v := range paymentTypeList {
		if v != nil && v.Lang == "zh-CN" {
			paymentType = v.PaymentTypeList
			continue
		}
	}
	res := make(map[string]int32, 0)
	for _, v := range paymentType {
		if v == nil {
			continue
		}
		scoreInt, _ := strconv.Atoi(v.SortScore)
		if scoreInt == 0 {
			continue
		}

		res[v.PaymentType] = int32(scoreInt)
	}
	return res
}

func GetFormStyle(ctx context.Context, products []*biz_runtime.ProductInfoFull) int64 {
	if len(products) == 0 || products[0].BaseReqData == nil {
		return 1
	}
	return products[0].BaseReqData.CommonBizInfo.RecCarpoolFormSytle
}

func GetRecInfo(ctx context.Context, products []*biz_runtime.ProductInfoFull, estimateData []*proto.RecCarpoolEstimateData, formStyle int64) *proto.RecCarpoolInfo {

	if formStyle != 1 {
		// 本期只样式1出车型推荐能力 其余样式之后提需求再做
		return nil
	}
	if len(products) == 0 || products[0] == nil {
		return nil
	}

	recCarpoolOther := products[0].BaseReqData.CommonBizInfo.RecCarpoolInfo.RecCarpoolOther

	if recCarpoolOther == nil {
		return nil
	}
	if recCarpoolOther.ExtraInfo == nil {
		return nil
	}
	dcmpConfStr := dcmp.GetDcmpPlainContent(ctx, "rec_carpool-rec_info")
	res := &proto.RecCarpoolInfo{
		SwitchLink: gjson.Get(dcmpConfStr, "switch_link").String(),
		SwitchName: gjson.Get(dcmpConfStr, "switch_name").String(),
	}
	if recCarpoolOther.ExtraInfo["turn_on_rec"] == "0" {
		// 推荐管理关闭
		res.RecExplain = gjson.Get(dcmpConfStr, "close_rec_explain").String()
		return res
	} else if recCarpoolOther.ExtraInfo["turn_on_rec"] == "1" {
		// 推荐管理打开
		// 推荐品类
		recPC := recCarpoolOther.AddProduct
		resPCIntroMsg := []string{}
		for _, item := range recPC {
			for _, v := range estimateData {
				if item.PropertyID == v.GetExtraMap().ProductCategory {
					resPCIntroMsg = append(resPCIntroMsg, v.GetIntroMsg())
				}
			}
		}
		if len(resPCIntroMsg) == 0 {
			return nil
		}
		recExplain := gjson.Get(dcmpConfStr, "open_rec_explain").String()
		for index, v := range resPCIntroMsg {
			if index == 0 {
				recExplain = recExplain + v
				continue
			}
			recExplain = recExplain + "/" + v
		}
		if recCarpoolOther.RecReason != nil && recCarpoolOther.RecValue != nil {
			recReason := strconv.Itoa(int(*recCarpoolOther.RecReason))
			recValue := strconv.Itoa(int(*recCarpoolOther.RecValue))
			if recReason != "" && recValue != "0" {
				if recReason == "2" {
					recValue = util.Jiao2YUan(*recCarpoolOther.RecValue)
				}
				recExplain = recExplain + "," + dcmp.TranslateTemplate(gjson.Get(dcmpConfStr, "rec_type."+recReason).String(),
					map[string]string{"rec_value": recValue},
				)
			}
		}
		res.RecExplain = recExplain
		res.RecIcon = recCarpoolOther.GetRecIcon()
		return res
	}
	return nil
}

func AssembleGroupData(ctx context.Context, estimateData []*proto.RecCarpoolEstimateData, formStyle int64, isNewForm bool, noCarpoolSelectFeeDescMap map[int64][]*engine_model.FeeOutput) (estimateResData []*proto.RecCarpoolEstimateData) {
	estimateResData = []*proto.RecCarpoolEstimateData{}
	carpoolSelectType := int32(0)
	nonCarpoolEstimateResData := []*proto.RecCarpoolEstimateData{}
	nonCarpoolSelectType := int32(0)
	for _, v := range estimateData {
		if v.CategoryId == 1 || v.CategoryId == 2 { // 拼车
			estimateResData = append(estimateResData, v)
			if v.SelectType == 1 {
				carpoolSelectType = v.SelectType
			}
		} else {
			// 不拼车
			nonCarpoolEstimateResData = append(nonCarpoolEstimateResData, v)
			if v.SelectType == 1 {
				nonCarpoolSelectType = v.SelectType
			}
		}
	}
	nowSelectType := int32(0)
	if carpoolSelectType == 0 && nonCarpoolSelectType == 1 {
		nowSelectType = 1
	}
	if formStyle == 1 || formStyle == 3 {
		key := "rec_carpool-intro_msg"
		if isNewForm {
			key = "rec_carpool-intro_msg_new"
		}
		nonCarpoolEstimateData := &proto.RecCarpoolEstimateData{
			FeeDescList: GetNoCarpoolFeeDescList(ctx, noCarpoolSelectFeeDescMap),
			IntroMsg:    dcmp.GetJSONContentWithPath(ctx, key, nil, "0_0"),
			CategoryId:  3,
			SelectType:  nowSelectType,
			SubProduct:  GetSubProduct(nonCarpoolEstimateResData),
		}
		estimateResData = append(estimateResData, nonCarpoolEstimateData)
		return estimateResData
	} else if formStyle == 2 {
		// 拆分出出租车
		var taxiEstimateData *proto.RecCarpoolEstimateData
		nowSelectTypeNoCarpoolNoTaxi := int32(0)
		nonCarpoolEstimateData := &proto.RecCarpoolEstimateData{
			IntroMsg:   dcmp.GetJSONContentWithPath(ctx, "rec_carpool-intro_msg", nil, "0_0"),
			CategoryId: 3,
			SelectType: nowSelectType,
			// MultiProduct
		}
		multiProduct := []*proto.MultiAggregationRecCarpoolEstimateData{}
		nonCarpoolEstimateResDataNoTaxi := []*proto.RecCarpoolEstimateData{}
		for _, v := range nonCarpoolEstimateResData {
			if v.ExtraMap.GetProductCategory() == estimate_pc_id.EstimatePcIdTaxiMarketisationPutong {
				taxiEstimateData = v
			}
		}
		for _, v := range nonCarpoolEstimateResData {
			if v.ExtraMap.GetProductCategory() == estimate_pc_id.EstimatePcIdUnioneSpecialPrice {
				taxiEstimateData = v
			}
		}
		for _, v := range nonCarpoolEstimateResData {
			if v.ExtraMap.GetProductCategory() == estimate_pc_id.EstimatePcIdUnione {
				taxiEstimateData = v
				continue
			}
		}
		for _, v := range nonCarpoolEstimateResData {
			if taxiEstimateData != nil && v.ExtraMap.GetProductCategory() == taxiEstimateData.GetExtraMap().ProductCategory {
				continue
			}
			nonCarpoolEstimateResDataNoTaxi = append(nonCarpoolEstimateResDataNoTaxi, v)
		}
		for _, v := range nonCarpoolEstimateResDataNoTaxi {
			if v.SelectType == 1 {
				nowSelectTypeNoCarpoolNoTaxi = v.SelectType
				continue
			}
		}
		multiProduct = append(multiProduct, &proto.MultiAggregationRecCarpoolEstimateData{
			IntroMsg:   dcmp.GetJSONContentWithPath(ctx, "rec_carpool-intro_msg", nil, "0_0_0"),
			CategoryId: GetMultiCategoryId(ctx, 0),
			SelectType: nowSelectTypeNoCarpoolNoTaxi,
			SubProduct: GetSubProduct(nonCarpoolEstimateResDataNoTaxi),
		})
		if taxiEstimateData != nil {
			multiProduct = append(multiProduct, GetMultiProduct(ctx, taxiEstimateData))
		}
		nonCarpoolEstimateData.MultiProduct = multiProduct
		estimateResData = append(estimateResData, nonCarpoolEstimateData)
		return estimateResData
	}
	return nil
}

func GetNoCarpoolFeeDescList(ctx context.Context, noCarpoolSelectFeeDescMap map[int64][]*engine_model.FeeOutput) []*proto.NewFormFeeDesc {
	if noCarpoolSelectFeeDescMap == nil {
		return nil
	}
	isSingleCheck := false
	if len(noCarpoolSelectFeeDescMap) == 1 {
		isSingleCheck = true
	}
	dynamicFeeDescListData := get_dynamic_fee_desc_list.NewDynamicFeeDescList(nil, isSingleCheck).Battle(ctx, noCarpoolSelectFeeDescMap)
	if dynamicFeeDescListData == nil {
		return nil
	}
	return dynamicFeeDescListData.GetFeeDescList()
}

func SortGroupData(ctx context.Context, estimateData []*proto.RecCarpoolEstimateData, formStyle int64) {
	dcmpConfStr := dcmp.GetDcmpPlainContent(ctx, "rec_carpool-multi_vehicle_sorting")
	sortConfig := make(map[string]int, 0)
	err := json.Unmarshal([]byte(dcmpConfStr), &sortConfig)
	if err != nil {
		return
	}
	var nonCarpoolEstimateData *proto.RecCarpoolEstimateData
	for _, v := range estimateData {
		if v.CategoryId == 3 {
			nonCarpoolEstimateData = v
		}
	}
	if nonCarpoolEstimateData == nil {
		return
	}
	sortMap := make(map[int32]int, 0)
	for k, v := range sortConfig {
		pcID, _ := strconv.Atoi(k)
		if pcID == 0 || v == 0 {
			continue
		}
		sortMap[int32(pcID)] = v
	}
	var multiProductSort *MultiProductSort
	if formStyle == 1 || formStyle == 3 {
		multiProductSort = &MultiProductSort{
			MultiProduct: nonCarpoolEstimateData.SubProduct,
			SortMap:      sortMap,
		}
		sort.Sort(multiProductSort)
		nonCarpoolEstimateData.SubProduct = multiProductSort.MultiProduct
	} else if formStyle == 2 {
		multiProductSort = &MultiProductSort{
			MultiProduct: nonCarpoolEstimateData.MultiProduct[0].SubProduct,
			SortMap:      sortMap,
		}
		sort.Sort(multiProductSort)
		nonCarpoolEstimateData.MultiProduct[0].SubProduct = multiProductSort.MultiProduct
	}
}

func GetSubProduct(nonCarpoolEstimateResData []*proto.RecCarpoolEstimateData) []*proto.AggregationRecCarpoolEstimateData {
	aggregationRecCarpoolEstimateData := []*proto.AggregationRecCarpoolEstimateData{}
	for _, v := range nonCarpoolEstimateResData {
		aggregationRecCarpoolEstimateData = append(aggregationRecCarpoolEstimateData, &proto.AggregationRecCarpoolEstimateData{
			IntroMsg:        v.IntroMsg,
			FeeMsg:          v.FeeMsg,
			EstimateId:      v.EstimateId,
			CategoryId:      v.CategoryId,
			SelectType:      v.SelectType,
			SeatNum:         v.SeatNum,
			FeeAmount:       v.FeeAmount,
			RouteIdList:     v.RouteIdList,
			FeeDescList:     v.FeeDescList,
			ExtraPriceDesc:  v.ExtraPriceDesc,
			ExtraMap:        v.ExtraMap,
			UserPayInfo:     v.UserPayInfo,
			RecIcon:         v.RecIcon,
			ProductCategory: v.ProductCategory,
		})
	}
	return aggregationRecCarpoolEstimateData
}

func GetMultiProduct(ctx context.Context, taxiEstimateData *proto.RecCarpoolEstimateData) *proto.MultiAggregationRecCarpoolEstimateData {
	multiAggregationRecCarpoolEstimateData := &proto.MultiAggregationRecCarpoolEstimateData{
		IntroMsg:        taxiEstimateData.IntroMsg,
		FeeMsg:          taxiEstimateData.FeeMsg,
		EstimateId:      taxiEstimateData.EstimateId,
		CategoryId:      GetMultiCategoryId(ctx, taxiEstimateData.ProductCategory),
		SelectType:      taxiEstimateData.SelectType,
		SeatNum:         taxiEstimateData.SeatNum,
		FeeAmount:       taxiEstimateData.FeeAmount,
		RouteIdList:     taxiEstimateData.RouteIdList,
		FeeDescList:     taxiEstimateData.FeeDescList,
		ExtraPriceDesc:  taxiEstimateData.ExtraPriceDesc,
		ExtraMap:        taxiEstimateData.ExtraMap,
		UserPayInfo:     taxiEstimateData.UserPayInfo,
		RecIcon:         taxiEstimateData.RecIcon,
		ProductCategory: taxiEstimateData.ProductCategory,
	}
	return multiAggregationRecCarpoolEstimateData
}

type MultiProductSort struct {
	MultiProduct []*proto.AggregationRecCarpoolEstimateData
	SortMap      map[int32]int
}

func (s MultiProductSort) Len() int {
	return len(s.MultiProduct)
}

func (s MultiProductSort) Swap(i, j int) {
	s.MultiProduct[i], s.MultiProduct[j] = s.MultiProduct[j], s.MultiProduct[i]
}

func (s MultiProductSort) Less(i, j int) bool {
	if s.SortMap[s.MultiProduct[i].ProductCategory] == 0 {
		return false
	}
	if s.SortMap[s.MultiProduct[j].ProductCategory] == 0 {
		return true
	}
	return s.SortMap[s.MultiProduct[i].ProductCategory] < s.SortMap[s.MultiProduct[j].ProductCategory]
}

func GetMultiCategoryId(ctx context.Context, productCategory int32) int32 {
	idString := dcmp.GetJSONContentWithPath(
		ctx,
		"rec_carpool-category_id_style_2",
		nil,
		strconv.FormatInt(int64(productCategory), 10))
	id, _ := strconv.Atoi(idString)
	return int32(id)
}

func isHitNewStyle(ctx context.Context, products []*biz_runtime.ProductInfoFull) bool {
	var product *biz_runtime.ProductInfoFull

	for _, prod := range products {
		if prod != nil && prod.GetUID() != 0 && prod.GetCityID() != 0 && prod.GetUserPhone() != "" {
			product = prod
			break
		}
	}

	if product == nil {
		return false
	}

	param := ApolloModel.NewUser(strconv.FormatInt(product.GetUID(), 10)).
		With("city", strconv.Itoa(product.GetCityID())).
		With("phone", product.GetUserPhone())

	toggle, err := ApolloSDK.FeatureToggle("ykj_pinzuo_title_test", param)

	if err != nil || toggle == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "apollo ykj_pinzuo_title_test fail. err %+v, toggle %+v", err, util.JustJsonEncode(toggle))
		return false
	}

	return toggle.IsAllow() && toggle.GetAssignment().GetGroupName() == "treatment_group"
}
