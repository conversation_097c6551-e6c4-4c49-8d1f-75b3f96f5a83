package internal

import (
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"strconv"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"

	constsCommon "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

const (
	UpdateFeeDescListAppVersionNA     string = "6.6.12"
	UpdateFeeDescListAppVersionWebApp string = "6.6.55"
)

type ViewAdapter struct {
	*biz_runtime.ProductInfoFull
}

func (v *ViewAdapter) GetProductCategoryString() string {

	return strconv.FormatInt(v.GetProductCategory(), 10)
}

func (v *ViewAdapter) GetSeatNum() int32 {
	return v.GetBizInfo().CarpoolSeatNum
}

func (v *ViewAdapter) GetSeatNumString() string {
	return strconv.FormatInt(int64(v.GetBizInfo().CarpoolSeatNum), 10)
}

func (v *ViewAdapter) IsTaxiPC() bool {
	if v.GetProductCategory() == estimate_pc_id.EstimatePcIdUnione {
		return true
	}
	return false
}
func (v *ViewAdapter) IsBusinessPay() bool {
	if 21 == v.GetCurrentPaymentType() {
		return true
	}
	return false
}

func (v *ViewAdapter) GetCurrentPaymentType() int {
	opts := v.GetAllPaymentOption()
	for _, opt := range opts {
		if opt.IsSelected != nil && *opt.IsSelected == 1 {
			return int(opt.PayType)
		}
	}
	return 0
}

func (v *ViewAdapter) TaxiNameApolloParam() *ApolloModel.User {
	user := ApolloModel.NewUser(v.GetUserInfo().Phone).
		With("city", strconv.Itoa(int(v.GetAreaInfo().City))).
		With("phone", v.GetUserInfo().Phone).
		With("access_key_id", strconv.Itoa(int(v.GetClientInfo().AccessKeyID))).
		With("car_level", v.GetNTuple().RequireLevel).
		With("require_level", v.GetNTuple().RequireLevel).
		With("product_id", strconv.Itoa(int(v.GetNTuple().ProductID))).
		With("product_category", strconv.Itoa(int(v.GetNTuple().ProductCategory))).
		With("menu_id", "dache_anycar").
		With("combo_type", strconv.Itoa(int(v.GetNTuple().ComboType)))
	isSpecialPriceStatus := 0
	if v.GetNTuple().IsSpecialPrice {
		isSpecialPriceStatus = 1

	}
	user.With("is_special_price", strconv.Itoa(isSpecialPriceStatus))
	isCrossCityStatus := 0
	if v.BaseReqData.AreaInfo.ToArea != v.BaseReqData.AreaInfo.City {
		isCrossCityStatus = 1
	}
	user.With("is_cross_city", strconv.Itoa(isCrossCityStatus))
	return user
}

func (v *ViewAdapter) GetCurrentPaymentTypeWithBusiness() (int32, int32) {
	opts := v.GetAllPaymentOption()
	for _, opt := range opts {
		if opt.IsSelected != nil && *opt.IsSelected == 1 {
			return opt.PayType, opt.BusinessConstSet
		}
	}
	return 2, 0
}

func (v *ViewAdapter) IsCarpool() bool {
	if v.GetCarpoolType() == 0 {
		return false
	}
	return true
}

func (v *ViewAdapter) IsCarrpoolOnePrice() bool {
	if !v.IsCarpool() {
		return false
	}
	sceneEstimateFee := v.GetSceneEstimatePrice()
	if len(sceneEstimateFee) == 0 {
		return false
	}
	if len(sceneEstimateFee) == 1 {
		return true
	}
	if len(sceneEstimateFee) == 2 && sceneEstimateFee[0].GetFee() == sceneEstimateFee[1].GetFee() {
		return true
	}

	return false
}

func (v *ViewAdapter) GetFailEstimateFee() float64 {
	if v.GetProductCategory() != estimate_pc_id.EstimatePcIdCarpoolStation {
		return 0
	}
	sceneEstimateFee := v.GetSceneEstimatePrice()
	if len(sceneEstimateFee) != 2 {
		return 0
	}
	for _, item := range sceneEstimateFee {
		carpoolStatus := item.GetFeeAttributes().GetInt("is_carpool_success")
		if carpoolStatus != nil && *carpoolStatus == 0 {
			// 未拼成
			return item.GetFee()
		}
	}
	return 0
}

//func (a *ViewAdapter) GetExactEstimateFee() float64 {
//	if a.GetBillInfo() == nil {
//		return 0
//	}
//
//	return a.GetExactEstimateFee()
//}

func (a *ViewAdapter) GetCarpoolFailExactEstimateFee() float64 {
	if len(a.GetExtendList()) == 1 {
		return a.GetExtendList()[0].ExactEstimateFee
	}

	return 0
}

func (v *ViewAdapter) IsCarpoolOneSeat() bool {
	if v.GetProductCategory() != estimate_pc_id.EstimatePcIdCarpoolStation && v.GetProductCategory() != estimate_pc_id.EstimatePcIdCarpoolFlatRateBySeat {
		return false
	}
	if v.GetBizInfo().CarpoolSeatNum == 1 {
		return true
	}
	return false
}

func (v *ViewAdapter) IsSelected() int32 {
	if v.BaseReqData.CommonBizInfo.RecCarpoolInfo.RecCarpoolDefaultNum == 0 {
		recCarpoolMultiRequireProduct := v.BaseReqData.CommonBizInfo.RecCarpoolInfo.RecCarpoolMultiRequireProduct
		if recCarpoolMultiRequireProduct != nil {
			if _, flag := recCarpoolMultiRequireProduct[int32(v.GetProductCategory())]; flag {
				return 1
			}
			return 0
		}
	} else if v.BaseReqData.CommonBizInfo.RecCarpoolInfo.RecCarpoolDefaultNum == -1 {
		if v.IsSelectedDefault() != 1 {
			return 0
		}
		if !v.IsCarpool() {
			return v.IsSelectedDefault()
		}
		return v.IsSelectedCarpool()
	} else {
		if v.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolStation || v.GetProductCategory() == estimate_pc_id.EstimatePcIdCarpoolFlatRateBySeat {
			if int32(v.BaseReqData.CommonBizInfo.RecCarpoolInfo.RecCarpoolDefaultNum) == v.GetBizInfo().CarpoolSeatNum {
				return 1
			}
			return 0
		}
		return v.IsSelectedDefault()
	}
	return 0
}

func (v *ViewAdapter) IsSelectedDefault() int32 {
	recCarpoolInfo := v.BaseReqData.CommonBizInfo.RecCarpoolInfo.RecCarpoolInfoMap[int32(v.GetProductCategory())]
	if recCarpoolInfo == nil {
		return 0
	}
	if recCarpoolInfo.IsSelected {
		return 1
	}
	return 0
}

func (v *ViewAdapter) IsSelectedCarpool() int32 {
	if v.GetProductCategory() != estimate_pc_id.EstimatePcIdCarpoolStation && v.GetProductCategory() != estimate_pc_id.EstimatePcIdCarpoolFlatRateBySeat {
		return 0
	}

	recCarpoolInfo := v.BaseReqData.CommonBizInfo.RecCarpoolInfo.RecCarpoolInfoMap[int32(v.GetProductCategory())]
	if recCarpoolInfo == nil {
		return 0
	}
	if recCarpoolInfo.IsSelected {
		if v.BaseReqData.CommonBizInfo.RecCarpoolInfo.RecCarpoolOther == nil {
			return 0
		}
		if v.GetBizInfo().CarpoolSeatNum == v.BaseReqData.CommonBizInfo.RecCarpoolInfo.RecCarpoolOther.SeatNum {
			return 1
		}
	}
	return 0
}

func (v *ViewAdapter) GetRecIcon() string {
	if len(v.BaseReqData.CommonBizInfo.RecommendInfoMap) < 1 {
		return ""
	}

	recCarpoolInfo := v.BaseReqData.CommonBizInfo.RecommendInfoMap[int32(v.GetProductCategory())]

	if recCarpoolInfo == nil {
		return ""
	}
	return recCarpoolInfo.Icon
}

func (a *ViewAdapter) GetBonus() float64 {
	fee := a.GetDirectEstimatePrice()
	if fee == nil {
		return 0
	}
	amount := fee.GetFeeDetail().GetBonusAmount()
	if amount == nil {
		return 0
	}
	return *amount
}
func (a *ViewAdapter) GetFastEstimatePrice() float64 {
	return a.BaseReqData.CommonBizInfo.FastCarPrice
}

// GetMixedDeductPrice 返回企业付 抵扣金额
func (a *ViewAdapter) GetMixedDeductPrice() float64 {
	var deductInfo float64

	if !a.IsBusinessPay() {
		return 0.0
	}

	deductInfo = a.GetEstimateFee()

	if payInfo := a.GetPaymentInfo(); payInfo != nil && payInfo.MixedPayDeductInfo != nil && payInfo.MixedPayDeductInfo.DeductFee > 0 {
		deductInfo = payInfo.MixedPayDeductInfo.DeductFee
	}

	return deductInfo
}

func (a *ViewAdapter) IsUpdateFeeDescList() bool {
	if util.CompareAppVersion(a.GetAppVersion(), UpdateFeeDescListAppVersionNA) >= 0 && (a.GetAccessKeyId() == constsCommon.AccessKeyIDDiDiAndroid || a.GetAccessKeyId() == constsCommon.AccessKeyIDDiDiIos) {
		return true
	}
	if util.CompareAppVersion(a.GetAppVersion(), UpdateFeeDescListAppVersionWebApp) >= 0 && (a.GetAccessKeyId() == constsCommon.AccessKeyIDDiDiWechatMini || a.GetAccessKeyId() == constsCommon.AccessKeyIDDiDiAlipayMini) {
		return true
	}
	return false
}

func (v *ViewAdapter) GetApolloParam() (string, map[string]string) {
	apolloParam := map[string]string{
		"key":              strconv.Itoa(int(v.GetUID())),
		"pid":              strconv.Itoa(int(v.GetUserPID())),
		"product_category": v.GetProductCategoryString(),
		"lang":             v.GetLang(),
		"city":             strconv.Itoa(v.GetCityID()),
		"phone":            v.GetUserPhone(),
		"app_version":      v.GetAppVersion(),
		"access_key_id":    strconv.Itoa(int(v.GetAccessKeyId())),
	}

	return strconv.Itoa(int(v.GetUID())), apolloParam
}

func (a *ViewAdapter) GetBaseReqData() *models.BaseReqData {
	return a.BaseReqData
}
