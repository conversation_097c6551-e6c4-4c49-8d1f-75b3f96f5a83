package rec_carpool_estimate

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

const OperaKeyEstimateData = "g_order_cap_multi_estimate_price"

func AddPublicLog(ctx context.Context, products []*biz_runtime.ProductInfoFull, selectTypeMap map[string]int32, formStyle int32) {
	// 压测流量不写Public日志
	if util.IsPressureTraffic(ctx) {
		return
	}
	for _, product := range products {
		if "" == product.Product.EstimateID {
			continue
		}
		WritePublicLog(ctx, product, selectTypeMap[product.Product.EstimateID], formStyle)
	}
}

// ...
func WritePublicLog(ctx context.Context, product *biz_runtime.ProductInfoFull, selectType int32, formStyle int32) {
	if product == nil || product.BaseReqData == nil || product.Product == nil {
		return
	}

	logInfo := make(map[string]interface{})
	//索引key
	logInfo["estimate_trace_id"] = util.GetTraceIDFromCtxWithoutCheck(ctx)
	logInfo["estimate_id"] = product.GetEstimateID()

	//地理位置信息
	areaInfo := product.GetAreaInfo()
	logInfo["area"] = areaInfo.City
	logInfo["to_area"] = areaInfo.ToArea
	logInfo["flat"] = areaInfo.FromLat
	logInfo["flng"] = areaInfo.FromLng
	logInfo["tlat"] = areaInfo.ToLat
	logInfo["tlng"] = areaInfo.ToLng
	logInfo["district"] = areaInfo.District
	logInfo["current_lat"] = areaInfo.CurLat
	logInfo["current_lng"] = areaInfo.CurLng
	logInfo["county"] = areaInfo.FromCounty
	logInfo["to_county"] = areaInfo.ToCounty
	logInfo["from_name"] = util.StringEscape(areaInfo.FromName)
	logInfo["to_name"] = util.StringEscape(areaInfo.ToName)

	//端信息
	commonInfo := product.GetClientInfo()
	logInfo["app_version"] = commonInfo.AppVersion
	logInfo["client_type"] = commonInfo.ClientType
	logInfo["access_key_id"] = commonInfo.AccessKeyID
	logInfo["channel"] = commonInfo.Channel
	logInfo["page_type"] = commonInfo.PageType
	logInfo["lang"] = commonInfo.Lang
	logInfo["xpsid"] = commonInfo.Xpsid
	logInfo["xpsid_root"] = commonInfo.XpsidRoot

	//产品信息
	logInfo["product_category"] = product.GetProductCategory()
	logInfo["require_level"] = product.Product.RequireLevel
	logInfo["combo_type"] = product.Product.ComboType
	logInfo["carpool_type"] = product.Product.CarpoolType
	logInfo["product_id"] = product.Product.ProductID

	//用户信息
	logInfo["pid"] = product.GetUserInfo().PID

	// 勾选和形态展示
	logInfo["seat_num"] = product.GetBizInfo().CarpoolSeatNum
	logInfo["select_type"] = selectType
	logInfo["form_style"] = formStyle
	logInfo["new_form"] = 1

	log.Public.Public(ctx, OperaKeyEstimateData, logInfo)
}
