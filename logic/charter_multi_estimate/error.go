package charter_multi_estimate

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gobiz/logger"
	"git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/nuwa/trace"
)

const (
	CharterMultiEstimateErrMsg = "charter-multi_estimate_err_msg"

	DefaultErrMsg = "unknown error"

	ErrnoSuccess = errors.ErrnoSuccess

	ErrnoSystem         = errors.ErrnoSystemError
	ErrnoNotOpenService = 800
)

func GetErrMsg(ctx context.Context, errno int) string {
	msgMap := map[int]string{}
	raw := dcmp.GetDcmpContent(ctx, CharterMultiEstimateErrMsg, nil)
	if raw == "" {
		return DefaultErrMsg
	}

	err := json.Unmarshal([]byte(raw), &msgMap)
	if err != nil {
		logger.Warnf(ctx, trace.DLTagUndefined, "GetErrMsg unmarshal failed. err=%s, raw=%s", err.Error(), raw)
		return DefaultErrMsg
	}

	if msg, ok := msgMap[errno]; ok && msg != "" {
		return msg
	}

	return msgMap[ErrnoSystem]
}
