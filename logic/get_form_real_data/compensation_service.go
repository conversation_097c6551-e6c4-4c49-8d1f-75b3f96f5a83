package get_form_real_data

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

const (
	IsOverLimit = 1

	ActionTypeNone  = 0
	ActionTypePopUp = 1

	ExtraInfoStyleAppendTitle = 1

	StatusNoCompensation = 0 // 未命中策略 应该会被前置过滤
	StatusUnderThres     = 1 // 未达到赔付门槛
	StatusSatisfiedThres = 2 // 已达到赔付门槛
	StatusOverLimit      = 3 // 达到领取上

	DcmpKey                             = "config_text-normal_compensation_form_real_data"
	ExpectLastText                      = 0             // 行为前-固定文案
	ExpectLastEts                       = 1             // 行为前-ets
	ExpectLastAnswerRate                = 2             // 行为前-应答率
	ExpectNowText                       = "text"        // 行为后-固定文案
	ExpectNowEts                        = "ets"         // 行为后-ets
	ExpectNowAnswerRate                 = "answer_rate" // 行为后-应答率
	SelectedTypeUnchanged               = 0             // 勾选数量没变化
	SelectedTypeAdd                     = 1             // 加勾
	SelectedTypeDelete                  = 2             // 减勾
	IconTypeNotShow                     = 0             //不展示icon
	IconTypeHappy                       = 1             // 开心
	IconTypeSad                         = 2             // 沮丧
	ComponentTypeNo                     = 0             // 组价: 无组件
	ComponentTypePriceAxle              = 1             // 组件: 价格轴
	Price_Axle_Default_Text             = "多勾更快出发"
	PrivilegeSourceLossPassenger        = "loss_passenger"         // 流失
	PrivilegeSourceNewPassenger         = "new_passenger"          // 新客
	PrivilegeSourceNewDevelopPassenger  = "new_develop_passenger"  // 新养成
	PrivilegeSourceLossDevelopPassenger = "loss_develop_passenger" // 流养成
	FromTypeAnyCarEstimate              = 2                        // 追加车型请求来源
)

type CouponInfo struct {
	Amount              int64  `json:"amount"`
	BeforeAmount        int64  `json:"before_amount"`
	BeforeInflateAmount int64  `json:"before_inflate_amount"`
	Discount            string `json:"discount"`
	CustomTag           string `json:"custom_tag"`
	CouponType          string `json:"coupon_type"`
	Cap                 int64  `json:"cap"`
	MultiSelectAmount   string `json:"multi_select_amount"`
}

type NormalCompensation struct {
	WaitTime                 int64      `json:"wait_time"`
	MemberLevel              int32      `json:"member_level"`
	InflateRatio             int32      `json:"inflate_ratio"`               // 膨胀系数，可能<0
	ProductCategoryList      []string   `json:"product_category_list"`       // 策略配置品类
	ProductCategoryNum       int32      `json:"product_category_num"`        // 起赔线
	CalledProductCategoryNum int32      `json:"called_product_category_num"` // 已呼叫的配置品类
	PrivilegeSource          string     `json:"privilege_source"`
	CouponInfo               CouponInfo `json:"coupon_info"`
	FrequencyStatus          int32      `json:"frequency_status"`
	Status                   int32      `json:"status"` // 0 表示未符合赔付条件， 1 表示符合条件已投保成功未赔付， 5表示赔付成功，6表示赔付失败
}

type DefaultSelectedCompensation struct {
	CouponAmount          int64   `json:"coupon_amount"`
	CompensationTimeStr   string  `json:"compensation_time_str"`
	ProductCategoryList   []int64 `json:"product_category_list"`
	CompensationTimeRound int64   `json:"compensation_time_round"`
}

type ImbalancedInfo struct {
	IconData       IconData       `json:"icon_data"`
	MapData        MapData        `json:"map_data"`
	PriceAxleTitle PriceAxleTitle `json:"price_axle_title"`
}
type IconData struct {
	ButtonHappyIcon    string `json:"button_happy_icon"`
	ButtonSadIcon      string `json:"button_sad_icon"`
	PriceAxleHappyIcon string `json:"price_axle_happy_icon"`
	PriceAxleSadIcon   string `json:"price_axle_sad_icon"`
}
type MapData struct {
	NormalText      string `json:"normal_text"`
	SpecialText     string `json:"special_text"`
	Icon            string `json:"icon"`
	BackgroundColor string `json:"background_color"`
	FontColor       string `json:"font_color"`
}

type PriceAxleTitle struct {
	EtsSecTitle     string `json:"ets_sec_title"`
	EtsMinuteTitle  string `json:"ets_minute_title"`
	AnswerRateTitle string `json:"answer_rate_title"`
}

func (c *Service) getSelectedType() int {
	lastSelectedCount := int(c.req.GetLastSelectedCount())
	nowSelectedCount := len(c.selectPcIds)
	if lastSelectedCount == nowSelectedCount {
		return SelectedTypeUnchanged
	} else if nowSelectedCount > lastSelectedCount {
		return SelectedTypeAdd
	} else {
		return SelectedTypeDelete
	}

}

func (c *Service) getFromHeadRule4NormalCompensation(ctx context.Context, sourceType int32) *proto.FormHeadRule {
	curStatus := c._getCurStatus()
	if c.NormalCompensationInfo == nil {
		return nil
	}

	if c.NormalCompensationInfo.Status != 0 && c.NormalCompensationInfo.Status != 1 {
		return nil
	}

	// 如果所有品类中赔付的数量都不满足起赔线，不展示赔付感知
	if util.ToInt32(c._getAllCompensationNum()) < (c.NormalCompensationInfo.ProductCategoryNum - c.NormalCompensationInfo.CalledProductCategoryNum) {
		return nil
	}

	// 之前得未触发赔付。如果已触发赔付（针对追加场景），展示追加文案，不下发赔付位
	if c.NormalCompensationInfo.CalledProductCategoryNum >= c.NormalCompensationInfo.ProductCategoryNum {
		return nil
	}

	// 只有触发赔付才会有优惠信息（属于异常case)
	if curStatus == StatusSatisfiedThres {
		if c.NormalCompensationInfo.CouponInfo.Amount <= 0 {
			return nil
		}
	}

	if curStatus == StatusOverLimit {
		if sourceType != consts.SourceTypeNormalTab && sourceType != consts.SourceTypeClassifyTab {
			return nil
		}
	}

	res := &proto.FormHeadRule{
		ActionType: ActionTypeNone,
		LeftIcon:   c._getLeftIcon(ctx, curStatus, sourceType),
		Title:      c._getTitle(ctx, curStatus, sourceType),
		Content:    c._getContent(ctx, curStatus, sourceType),
		HasArrow:   false,
	}

	omegaParameter := map[string]string{
		"compensation_type": util.ToString(c._getOmegaStatus(curStatus)),
	}

	if c._checkIsNewLoss(c.NormalCompensationInfo.PrivilegeSource) {
		omegaParameter["act_type"] = "xinliu"
	}
	res.OmegaData = &proto.Omega{
		OmegaEventId:   "wyc_compensation_connect_sw",
		OmegaParameter: omegaParameter,
	}

	res.OmegaDataCk = &proto.Omega{
		OmegaEventId: "wyc_compensation_connect_ck",
		OmegaParameter: map[string]string{
			"compensation_type": util.ToString(c._getOmegaStatus(curStatus)),
		},
	}

	if (sourceType == consts.SourceTypeNormalTab || sourceType == consts.SourceTypeClassifyTab) && (curStatus == StatusSatisfiedThres) && (c.NormalCompensationInfo.InflateRatio > 0) {
		res.HasArrow = true
		res.ActionType = ActionTypePopUp

		amount := util.RemoveSuffixZero(util.FormatPriceFloor(float64(c.NormalCompensationInfo.CouponInfo.Amount)/float64(100), 1))
		beforeAmount := util.RemoveSuffixZero(util.FormatPrice(float64(c.NormalCompensationInfo.CouponInfo.BeforeInflateAmount)/float64(100), 1))
		level := util.ToString(c.NormalCompensationInfo.MemberLevel)
		ratio := util.ToString(c.NormalCompensationInfo.InflateRatio)
		minute := util.ToString(int(c.NormalCompensationInfo.WaitTime / 60))

		res.PopUp = &proto.PopUp{
			Title: dcmp.GetJSONContentWithPath(ctx, DcmpKey, map[string]string{"level": level}, "pop_up.title"),
			Content: dcmp.GetJSONContentWithPath(ctx, DcmpKey, map[string]string{
				"level":           level,
				"ratio":           ratio,
				"minute":          minute,
				"amount":          amount,
				"original_amount": beforeAmount,
			}, "pop_up.content"),
		}
	}

	return res
}

func (c *Service) _getCurCompensationNum() int {
	cnt := 0
	for _, pcId := range c.selectPcIds {
		id := strconv.FormatInt(pcId, 10)
		if util.InArrayStr(id, c.NormalCompensationInfo.ProductCategoryList) {
			cnt += 1
		}
	}

	return cnt
}

func (c *Service) _getAllCompensationNum() int {
	cnt := 0
	for _, pcId := range append(c.selectPcIds, c.unselectPcIds...) {
		id := strconv.FormatInt(pcId, 10)
		if util.InArrayStr(id, c.NormalCompensationInfo.ProductCategoryList) {
			cnt += 1
		}
	}

	return cnt
}

func (c *Service) _getContent(ctx context.Context, curStatus int, sourceType int32) string {
	pathPrefix := "style_" + util.ToString(sourceType) + ".status_" + util.ToString(curStatus)

	switch curStatus {
	case StatusUnderThres:
		num := util.ToString(c.NormalCompensationInfo.ProductCategoryNum - c.NormalCompensationInfo.CalledProductCategoryNum - util.ToInt32(c._getCurCompensationNum()))
		return dcmp.GetJSONContentWithPath(ctx, DcmpKey, map[string]string{"num": num}, pathPrefix+".text")
	case StatusSatisfiedThres:
		// 追加车型表单，新流不同文案
		if sourceType == consts.SourceTypeAnyCarEstimate && c._checkIsNewLoss(c.NormalCompensationInfo.PrivilegeSource) {
			return dcmp.GetJSONContentWithPath(ctx, DcmpKey, nil, pathPrefix+".text_new_loss")
		}
		amount := util.RemoveSuffixZero(util.FormatPriceFloor(float64(c.NormalCompensationInfo.CouponInfo.Amount)/float64(100), 1))
		content := dcmp.GetJSONContentWithPath(ctx, DcmpKey, map[string]string{"amount": amount}, pathPrefix+".text_1")
		if c.NormalCompensationInfo.InflateRatio > 0 {
			level := util.ToString(c.NormalCompensationInfo.MemberLevel)
			ratio := util.ToString(c.NormalCompensationInfo.InflateRatio)
			content += dcmp.GetJSONContentWithPath(ctx, DcmpKey, map[string]string{"level": level, "ratio": ratio}, pathPrefix+".text_2")
		}
		return content
	case StatusOverLimit:
		return dcmp.GetJSONContentWithPath(ctx, DcmpKey, nil, pathPrefix+".text")
	}

	return ""
}

func (c *Service) _getLeftIcon(ctx context.Context, curStatus int, sourceType int32) *string {
	path := "style_" + util.ToString(sourceType) + ".left_icon." + util.ToString(c.NormalCompensationInfo.WaitTime/60)
	// 等待应答追加车型表单, 新流展示不同文案
	if sourceType == consts.SourceTypeAnyCarEstimate && c._checkIsNewLoss(c.NormalCompensationInfo.PrivilegeSource) {
		path = "style_" + util.ToString(sourceType) + ".left_icon." + c.NormalCompensationInfo.PrivilegeSource
	}
	return util.String2PtrString(dcmp.GetJSONContentWithPath(ctx, DcmpKey, nil, path))
}

func (c *Service) _getTitle(ctx context.Context, curStatus int, sourceType int32) *string {
	return util.String2PtrString(dcmp.GetJSONContentWithPath(ctx, DcmpKey, map[string]string{"min": util.ToString(c.NormalCompensationInfo.WaitTime / 60)}, "title"))
}

func (c *Service) _getCurStatus() int {
	if c.NormalCompensationInfo == nil {
		return StatusNoCompensation
	} else if c.NormalCompensationInfo.FrequencyStatus == IsOverLimit {
		return StatusOverLimit
	} else if util.ToInt(c.NormalCompensationInfo.ProductCategoryNum-c.NormalCompensationInfo.CalledProductCategoryNum) <= c._getCurCompensationNum() {
		return StatusSatisfiedThres
	} else {
		return StatusUnderThres
	}
}

func (c *Service) _getOmegaStatus(status int) int {
	switch status {
	case StatusUnderThres:
		return 0
	case StatusSatisfiedThres:
		return 1
	case StatusOverLimit:
		return 2
	}

	return 0
}

func (c *Service) _checkIsNewLoss(privilegeSource string) bool {
	return util.InArrayStr(privilegeSource, []string{
		PrivilegeSourceLossPassenger,
		PrivilegeSourceNewPassenger,
		PrivilegeSourceNewDevelopPassenger,
		PrivilegeSourceLossDevelopPassenger,
	})
}
