package get_form_real_data

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_form_real_data/handler"
	"git.xiaojukeji.com/nuwa/golibs/zerolog/ddlog"
)

// Mock dcmp相关方法
func mockDcmpGetJSONContentWithPath() *mockey.MockBuilder {
	return mockey.Mock(dcmp.GetJSONContentWithPath).To(func(ctx context.Context, dcmpKey string, params map[string]string, contentKey string) string {
		switch contentKey {
		case "unchecked":
			return "请至少勾选1种车型"
		case "default_content":
			return "多选车型更快出发"
		case "over_15_content":
			return "叫车高峰，多选车型更快出发"
		case "less_than_10_content":
			return "{{second}}秒内有车应答"
		case "seconds_content":
			return "{{second}}秒内有车应答"
		case "one_minute_content":
			return "1分钟内有车应答"
		case "minutes_content":
			return "{{min}}分钟内有车应答"
		case "less_than_2minutes_content":
			return "{{min}}分钟内有车应答"
		case DefaultLeftIcon:
			return "default_icon"
		case EnoughLeftIcon:
			return "enough_icon"
		default:
			return "mock_content"
		}
	})
}

// Mock handlerFormHeadRule方法
func mockHandlerFormHeadRule() *mockey.MockBuilder {
	return mockey.Mock(handlerFormHeadRule).To(func(ctx context.Context, res *proto.FormHeadRule, caseData etsHeadRuleData, formStyleExp int32) *proto.FormHeadRule {
		if formStyleExp == 1 {
			res.LeftIcon = nil
		}
		return res
	})
}

// Mock日志方法
func mockLogTrace() *mockey.MockBuilder {
	return mockey.Mock((*ddlog.DiLogHandle).Warnf).Return()
}

// Mock util相关方法
func mockUtilString2PtrString() *mockey.MockBuilder {
	return mockey.Mock(util.String2PtrString).To(func(s string) *string {
		return &s
	})
}

// 生成测试用的Service实例
func generateMockService(selectPcIds []int64, req *proto.PGetFormRealDataReq) *Service {
	return &Service{
		selectPcIds: selectPcIds,
		req:         req,
	}
}

// 生成测试用的请求数据
func generateMockRequest(formStyleExp int32) *proto.PGetFormRealDataReq {
	return &proto.PGetFormRealDataReq{
		FormStyleExp: &formStyleExp,
	}
}

// TestGetFromHeadRule4Ets 测试ETS头部规则获取逻辑的主要功能
func TestGetFromHeadRule4Ets(t *testing.T) {
	// 前置准备 - 清理所有Mock
	mockey.UnPatchAll()

	// Mock所有外部依赖
	dcmpMock := mockDcmpGetJSONContentWithPath().Build()
	handlerMock := mockHandlerFormHeadRule().Build()
	logMock := mockLogTrace().Build()
	utilMock := mockUtilString2PtrString().Build()

	defer func() {
		dcmpMock.UnPatch()
		handlerMock.UnPatch()
		logMock.UnPatch()
		utilMock.UnPatch()
	}()

	tests := []struct {
		name              string
		setupService      func() *Service
		ets               int
		expectedContent   string
		expectedStyle     int32
		expectedOmegaId   string
		expectLeftIconNil bool
	}{
		{
			name: "未选择车型场景 - ETS逻辑验证未选择状态处理",
			setupService: func() *Service {
				return generateMockService([]int64{}, generateMockRequest(0))
			},
			ets:               30,
			expectedContent:   "请至少勾选1种车型",
			expectedStyle:     1,
			expectedOmegaId:   "wyc_didiapp_biaodan_yuqi_sw",
			expectLeftIconNil: false,
		},
		{
			name: "ETS为0的默认场景 - ETS逻辑验证默认状态处理",
			setupService: func() *Service {
				return generateMockService([]int64{1001, 1002}, generateMockRequest(0))
			},
			ets:               0,
			expectedContent:   "多选车型更快出发",
			expectedStyle:     1,
			expectedOmegaId:   "wyc_didiapp_biaodan_yuqi_sw",
			expectLeftIconNil: false,
		},
		{
			name: "ETS超过15分钟的高峰场景 - ETS逻辑验证高峰期处理",
			setupService: func() *Service {
				return generateMockService([]int64{1001, 1002}, generateMockRequest(0))
			},
			ets:               16 * 60, // 16分钟
			expectedContent:   "叫车高峰，多选车型更快出发",
			expectedStyle:     1,
			expectedOmegaId:   "wyc_didiapp_biaodan_yuqi_sw",
			expectLeftIconNil: false,
		},
		{
			name: "ETS小于等于10秒的充足时间场景 - ETS逻辑验证EnoughSecondsThreshold阈值处理",
			setupService: func() *Service {
				return generateMockService([]int64{1001, 1002}, generateMockRequest(0))
			},
			ets:               8,
			expectedContent:   "{{second}}秒内有车应答",
			expectedStyle:     1,
			expectedOmegaId:   "wyc_didiapp_biaodan_yuqi_sw",
			expectLeftIconNil: false,
		},
		{
			name: "10秒 < ETS <= 30秒的秒数显示场景 - ETS逻辑验证SecondsThreshold阈值处理",
			setupService: func() *Service {
				return generateMockService([]int64{1001, 1002}, generateMockRequest(0))
			},
			ets:               25,
			expectedContent:   "{{second}}秒内有车应答",
			expectedStyle:     1,
			expectedOmegaId:   "wyc_didiapp_biaodan_yuqi_sw",
			expectLeftIconNil: false,
		},
		{
			name: "30秒 < ETS <= 60秒的一分钟显示场景 - ETS逻辑验证OneMinuteThreshold阈值处理",
			setupService: func() *Service {
				return generateMockService([]int64{1001, 1002}, generateMockRequest(0))
			},
			ets:               45,
			expectedContent:   "1分钟内有车应答",
			expectedStyle:     1,
			expectedOmegaId:   "wyc_didiapp_biaodan_yuqi_sw",
			expectLeftIconNil: false,
		},
		{
			name: "ETS大于60秒的分钟数显示场景 - ETS逻辑验证分钟数计算处理",
			setupService: func() *Service {
				return generateMockService([]int64{1001, 1002}, generateMockRequest(0))
			},
			ets:               180, // 3分钟
			expectedContent:   "{{min}}分钟内有车应答",
			expectedStyle:     1,
			expectedOmegaId:   "wyc_didiapp_biaodan_yuqi_sw",
			expectLeftIconNil: false,
		},
		{
			name: "FormStyleExp为1的新样式场景 - ETS逻辑验证样式切换处理",
			setupService: func() *Service {
				return generateMockService([]int64{1001, 1002}, generateMockRequest(1))
			},
			ets:               45,
			expectedContent:   "1分钟内有车应答",
			expectedStyle:     1,
			expectedOmegaId:   "wyc_didiapp_biaodan_yuqi_sw",
			expectLeftIconNil: true, // 新样式下左图标为nil
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 准备Service和上下文
			service := tt.setupService()
			ctx := context.Background()

			// 执行测试方法
			result := service.getFromHeadRule4Ets(ctx, tt.ets)

			// 验证基本结果
			assert.NotNil(t, result, "返回结果不应为空")
			assert.Equal(t, tt.expectedContent, result.Content, "Content应该匹配预期值")
			assert.Equal(t, tt.expectedStyle, result.Style, "Style应该匹配预期值")

			// 验证OmegaData
			assert.NotNil(t, result.OmegaData, "OmegaData不应为空")
			assert.Equal(t, tt.expectedOmegaId, result.OmegaData.OmegaEventId, "OmegaEventId应该匹配预期值")
			assert.NotNil(t, result.OmegaData.OmegaParameter, "OmegaParameter不应为空")
			assert.Contains(t, result.OmegaData.OmegaParameter, "content", "OmegaParameter应包含content字段")

			// 验证LeftIcon
			if tt.expectLeftIconNil {
				assert.Nil(t, result.LeftIcon, "新样式下LeftIcon应为nil")
			} else {
				assert.NotNil(t, result.LeftIcon, "默认样式下LeftIcon不应为nil")
			}
		})
	}
}

// TestEtsThresholdBoundaries 测试ETS阈值边界值处理
func TestEtsThresholdBoundaries(t *testing.T) {
	// 前置准备 - 清理所有Mock
	mockey.UnPatchAll()

	// Mock所有外部依赖
	dcmpMock := mockDcmpGetJSONContentWithPath().Build()
	handlerMock := mockHandlerFormHeadRule().Build()
	logMock := mockLogTrace().Build()
	utilMock := mockUtilString2PtrString().Build()

	defer func() {
		dcmpMock.UnPatch()
		handlerMock.UnPatch()
		logMock.UnPatch()
		utilMock.UnPatch()
	}()

	service := generateMockService([]int64{1001, 1002}, generateMockRequest(0))
	ctx := context.Background()

	// 测试边界值
	boundaryTests := []struct {
		name            string
		ets             int
		expectedContent string
		description     string
	}{
		{
			name:            "ETS=10秒边界值测试 - 验证EnoughSecondsThreshold边界",
			ets:             EnoughSecondsThreshold,
			expectedContent: "{{second}}秒内有车应答",
			description:     "正好等于EnoughSecondsThreshold的边界情况",
		},
		{
			name:            "ETS=11秒边界值测试 - 验证EnoughSecondsThreshold之后逻辑",
			ets:             EnoughSecondsThreshold + 1,
			expectedContent: "{{second}}秒内有车应答",
			description:     "超过EnoughSecondsThreshold的情况",
		},
		{
			name:            "ETS=30秒边界值测试 - 验证SecondsThreshold边界",
			ets:             SecondsThreshold,
			expectedContent: "{{second}}秒内有车应答",
			description:     "正好等于SecondsThreshold的边界情况",
		},
		{
			name:            "ETS=31秒边界值测试 - 验证SecondsThreshold之后逻辑",
			ets:             SecondsThreshold + 1,
			expectedContent: "1分钟内有车应答",
			description:     "超过SecondsThreshold的情况",
		},
		{
			name:            "ETS=60秒边界值测试 - 验证OneMinuteThreshold边界",
			ets:             OneMinuteThreshold,
			expectedContent: "1分钟内有车应答",
			description:     "正好等于OneMinuteThreshold的边界情况",
		},
		{
			name:            "ETS=61秒边界值测试 - 验证OneMinuteThreshold之后逻辑",
			ets:             OneMinuteThreshold + 1,
			expectedContent: "{{min}}分钟内有车应答",
			description:     "超过OneMinuteThreshold的情况",
		},
		{
			name:            "ETS=15分钟边界值测试 - 验证MaxEts边界",
			ets:             MaxEts,
			expectedContent: "{{min}}分钟内有车应答",
			description:     "正好等于MaxEts的边界情况",
		},
		{
			name:            "ETS=16分钟边界值测试 - 验证MaxEts之后逻辑",
			ets:             MaxEts + 1,
			expectedContent: "叫车高峰，多选车型更快出发",
			description:     "超过MaxEts的情况",
		},
	}

	for _, tt := range boundaryTests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.getFromHeadRule4Ets(ctx, tt.ets)
			assert.Equal(t, tt.expectedContent, result.Content, tt.description)
		})
	}
}

// TestEtsConstantValues 测试新增的ETS常量值
func TestEtsConstantValues(t *testing.T) {
	t.Run("验证新增的ETS相关常量值", func(t *testing.T) {
		// 验证新增的常量值与预期一致
		assert.Equal(t, 10, EnoughSecondsThreshold, "EnoughSecondsThreshold应该为10秒")
		assert.Equal(t, 30, SecondsThreshold, "SecondsThreshold应该为30秒")
		assert.Equal(t, 60, OneMinuteThreshold, "OneMinuteThreshold应该为60秒")
		assert.Equal(t, 2*60, MinutesThreshold, "MinutesThreshold应该为2分钟")
		assert.Equal(t, 15*60, MaxEts, "MaxEts应该为15分钟")
	})

	t.Run("验证ETS常量之间的逻辑关系", func(t *testing.T) {
		// 验证常量之间的大小关系
		assert.True(t, EnoughSecondsThreshold < SecondsThreshold, "EnoughSecondsThreshold应小于SecondsThreshold")
		assert.True(t, SecondsThreshold < OneMinuteThreshold, "SecondsThreshold应小于OneMinuteThreshold")
		assert.True(t, OneMinuteThreshold < MinutesThreshold, "OneMinuteThreshold应小于MinutesThreshold")
		assert.True(t, MinutesThreshold < MaxEts, "MinutesThreshold应小于MaxEts")
	})
}

// TestEtsHeadRuleDataStruct 测试etsHeadRuleData结构体
func TestEtsHeadRuleDataStruct(t *testing.T) {
	t.Run("验证etsHeadRuleData结构体字段", func(t *testing.T) {
		// 创建测试用的etsHeadRuleData实例
		testData := etsHeadRuleData{
			contentKey:   "test_content",
			omegaContent: "test_omega",
			leftIconKey:  "test_icon",
			params:       map[string]string{"key": "value"},
		}

		// 验证结构体字段能正确赋值和获取
		assert.Equal(t, "test_content", testData.contentKey, "contentKey字段应正确赋值")
		assert.Equal(t, "test_omega", testData.omegaContent, "omegaContent字段应正确赋值")
		assert.Equal(t, "test_icon", testData.leftIconKey, "leftIconKey字段应正确赋值")
		assert.NotNil(t, testData.params, "params字段不应为空")
		assert.Equal(t, "value", testData.params["key"], "params字段应正确赋值")
	})
}

// TestGetFromHeadRule4Etp 测试ETP相关逻辑，包括GlobalSceneFlag处理
func TestGetFromHeadRule4Etp(t *testing.T) {
	// 前置准备 - 清理所有Mock
	mockey.UnPatchAll()

	// Mock dcmp相关方法
	dcmpMock := mockey.Mock(dcmp.GetJSONContentWithPath).To(func(ctx context.Context, dcmpKey string, params map[string]string, contentKey string) string {
		switch contentKey {
		case "less_than_threshold":
			return `[{"low_bound":0,"up_bound":120,"fix_value":120,"content":"测试内容"}]`
		case "default_content":
			return "多选车型更快出发"
		case "over_15_content":
			return "叫车高峰，多选车型更快出发"
		case "unchecked":
			return "请至少勾选1种车型"
		case "expect_scene_over_600_not_queue_scene":
			return "最快10分钟"
		default:
			return "mock_content"
		}
	}).Build()

	// Mock handler.GetETPThresholdConfig
	handlerMock := mockey.Mock(handler.GetETPThresholdConfig).Return(&handler.FormDataETPThresholdConfig{
		LowBound: 0,
		UpBound:  120,
		FixValue: 120,
		Content:  "测试ETP内容",
	}).Build()

	// Mock其他依赖
	logMock := mockLogTrace().Build()
	utilMock := mockUtilString2PtrString().Build()

	// Mock getCompensationInfo
	compensationMock := mockey.Mock((*Service).getCompensationInfo).Return(&proto.FormHeadCompensationRule{}).Build()

	defer func() {
		dcmpMock.UnPatch()
		handlerMock.UnPatch()
		logMock.UnPatch()
		utilMock.UnPatch()
		compensationMock.UnPatch()
	}()

	tests := []struct {
		name               string
		setupService       func() *Service
		etp                int32
		expectedContent    string
		expectedOmega      string
		expectFormStyleNil bool
	}{
		{
			name: "未选择车型场景 - ETP逻辑验证未选择状态处理",
			setupService: func() *Service {
				return &Service{
					selectPcIds: []int64{},
					req:         generateMockRequest(0),
				}
			},
			etp:                60,
			expectedContent:    "请至少勾选1种车型",
			expectedOmega:      "请至少勾选1种车型",
			expectFormStyleNil: false,
		},
		{
			name: "ETP为0的默认场景 - ETP逻辑验证默认状态处理",
			setupService: func() *Service {
				return &Service{
					selectPcIds: []int64{1001, 1002},
					req:         generateMockRequest(0),
				}
			},
			etp:                0,
			expectedContent:    "多选车型更快出发",
			expectedOmega:      "多选车型更快出发",
			expectFormStyleNil: false,
		},
		{
			name: "GlobalSceneFlag为1的场景 - ETP逻辑验证GlobalSceneFlag处理",
			setupService: func() *Service {
				return &Service{
					selectPcIds: []int64{1001, 1002},
					req:         generateMockRequest(0),
					AthenaExpectInfoResult: AthenaExpectInfoResult{
						GlobalSceneExpect: &AthenaApiv3.GlobalExpectInfo{
							GlobalSceneFlag: util.Int32Ptr(1),
						},
					},
				}
			},
			etp:                100,
			expectedContent:    "多选车型更快出发",
			expectedOmega:      "多选车型更快出发",
			expectFormStyleNil: false,
		},
		{
			name: "ETP超过15分钟的高峰场景 - ETP逻辑验证高峰期处理",
			setupService: func() *Service {
				return &Service{
					selectPcIds: []int64{1001, 1002},
					req:         generateMockRequest(0),
					AthenaExpectInfoResult: AthenaExpectInfoResult{
						GlobalSceneExpect: &AthenaApiv3.GlobalExpectInfo{
							GlobalSceneFlag: util.Int32Ptr(0),
						},
					},
				}
			},
			etp:                16 * 60, // 16分钟
			expectedContent:    "叫车高峰，多选车型更快出发",
			expectedOmega:      "叫车高峰，多选车型更快出发",
			expectFormStyleNil: false,
		},
		{
			name: "ETP在阈值范围内的场景 - ETP逻辑验证阈值配置处理",
			setupService: func() *Service {
				return &Service{
					selectPcIds: []int64{1001, 1002},
					req:         generateMockRequest(0),
					AthenaExpectInfoResult: AthenaExpectInfoResult{
						GlobalSceneExpect: &AthenaApiv3.GlobalExpectInfo{
							GlobalSceneFlag: util.Int32Ptr(0),
						},
					},
				}
			},
			etp:                100,
			expectedContent:    "测试ETP内容",
			expectedOmega:      "2分钟",
			expectFormStyleNil: false,
		},
		{
			name: "FormStyleExp为1的新样式场景 - ETP逻辑验证样式切换处理",
			setupService: func() *Service {
				return &Service{
					selectPcIds: []int64{1001, 1002},
					req:         generateMockRequest(1),
					AthenaExpectInfoResult: AthenaExpectInfoResult{
						GlobalSceneExpect: &AthenaApiv3.GlobalExpectInfo{
							GlobalSceneFlag: util.Int32Ptr(0),
						},
					},
				}
			},
			etp:                100,
			expectedContent:    "测试ETP内容",
			expectedOmega:      "2分钟",
			expectFormStyleNil: true, // 新样式下左图标为nil
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 准备Service和上下文
			service := tt.setupService()
			ctx := context.Background()

			// 执行测试方法
			result := service.getFromHeadRule4Etp(ctx, tt.etp)

			// 验证基本结果
			assert.NotNil(t, result, "返回结果不应为空")
			assert.Equal(t, tt.expectedContent, result.Content, "Content应该匹配预期值")
			assert.Equal(t, int32(1), result.Style, "Style应该为1")

			// 验证OmegaData
			assert.NotNil(t, result.OmegaData, "OmegaData不应为空")
			assert.Equal(t, "wyc_didiapp_biaodan_yuqi_sw", result.OmegaData.OmegaEventId, "OmegaEventId应该匹配预期值")
			assert.NotNil(t, result.OmegaData.OmegaParameter, "OmegaParameter不应为空")
			assert.Contains(t, result.OmegaData.OmegaParameter, "content", "OmegaParameter应包含content字段")
			assert.Equal(t, tt.expectedOmega, result.OmegaData.OmegaParameter["content"], "OmegaParameter的content应该匹配")

			// 验证CompensationInfo
			assert.NotNil(t, result.CompensationInfo, "CompensationInfo不应为空")

			// 验证LeftIcon
			if tt.expectFormStyleNil {
				assert.Nil(t, result.LeftIcon, "新样式下LeftIcon应为nil")
			} else {
				assert.NotNil(t, result.LeftIcon, "默认样式下LeftIcon不应为nil")
			}
		})
	}
}

// TestEtpLogicEdgeCases 测试ETP逻辑的边界情况
func TestEtpLogicEdgeCases(t *testing.T) {
	// 前置准备 - 清理所有Mock
	mockey.UnPatchAll()

	// Mock所有外部依赖
	dcmpMock := mockDcmpGetJSONContentWithPath().Build()
	logMock := mockLogTrace().Build()
	utilMock := mockUtilString2PtrString().Build()
	compensationMock := mockey.Mock((*Service).getCompensationInfo).Return(&proto.FormHeadCompensationRule{}).Build()

	// Mock handler.GetETPThresholdConfig返回nil表示没有匹配的配置
	handlerMock := mockey.Mock(handler.GetETPThresholdConfig).Return(nil).Build()

	defer func() {
		dcmpMock.UnPatch()
		logMock.UnPatch()
		utilMock.UnPatch()
		compensationMock.UnPatch()
		handlerMock.UnPatch()
	}()

	t.Run("ETP没有匹配配置的场景 - 验证默认10分钟处理", func(t *testing.T) {
		service := &Service{
			selectPcIds: []int64{1001, 1002},
			req:         generateMockRequest(0),
			AthenaExpectInfoResult: AthenaExpectInfoResult{
				GlobalSceneExpect: &AthenaApiv3.GlobalExpectInfo{
					GlobalSceneFlag: util.Int32Ptr(0),
				},
			},
		}
		ctx := context.Background()

		// 执行测试方法
		result := service.getFromHeadRule4Etp(ctx, 300) // 5分钟

		// 验证结果 - 应该使用默认的10分钟逻辑
		assert.NotNil(t, result, "返回结果不应为空")
		assert.Equal(t, "最快10分钟", result.OmegaData.OmegaParameter["content"], "应该返回默认的10分钟内容")
	})
}
