package get_form_real_data

import (
	"context"
	"math"
	"time"

	"github.com/spf13/cast"
	"github.com/thoas/go-funk"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	trace "git.xiaojukeji.com/lego/context-go"
	"git.xiaojukeji.com/s3e/x-engine/condition"
	"git.xiaojukeji.com/s3e/x-engine/material"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/fence"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout/sort_strategy/exp"
)

const (
	GlobalSceneShort     = 1 //短时
	GlobalSceneMedium    = 2 //中时
	GlobalSceneLong      = 3 //长时
	GlobalSceneSuperLong = 4 //超长时
)

const DCMPKeyFixedOutlet = "guide_anycar-fixed_outlet"

type MatchCardTitleStruct struct {
	Text string `json:"text"`
}

// 构建等待应答弹框推荐标题
func (c *Service) buildBoxRecMatchCardTitle(ctx context.Context, sourceType int) *string {
	if title := c.getCustomTitleInfo(ctx, "anycar_estimate_rec_pop_card_title"); len(title) > 0 {
		return &title
	}
	checkStatus := "no_check"
	if len(c.selectPcIds) > 0 {
		checkStatus = "checked"
	}
	etsGap, etsShowType := c.calculateEtsInfo(ctx)
	materialConfig := map[string]string{
		"etsShowType": util.ToString(etsShowType),
		"ets_gap":     util.ToString(etsGap),
		"checked_num": util.ToString(len(c.selectPcIds)),
	}

	mC := material.NewDefaultComponentMaterial("anycar_estimate_ets_communicate_tag", "rec_pop", "anycar_guide", checkStatus)
	mC.SetCondition(materialConfig)

	var conf = &MatchCardTitleStruct{}
	err := mC.RenderContent(conf)
	if err != nil || conf == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to get material anycar_estimate_ets_communicate_tag with err %v and conf = nil :%v", err, conf == nil)
	}

	conf.Text = *c.appendNormalCompensationInfo(ctx, conf.Text, sourceType)
	return &conf.Text
}

// 构建等待应答标题：后半部分
func (c *Service) buildAppendMatchCardTitle(ctx context.Context, sourceType int) (*string, *string) {
	var emptyStr string
	if title := c.getCustomTitleInfo(ctx, "anycar_estimate_card_title"); len(title) > 0 {
		return &title, nil
	}
	if c.CityInfo == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "buildAppendMatchCardTitle fail to get cityInfo ")
		return &emptyStr, nil
	}
	apolloParams := map[string]string{
		"phone": c.UserInfo.Phone,
		"city":  cast.ToString(c.CityInfo.FromCityId),
		"pid":   cast.ToString(c.UserInfo.PID),
	}
	group := "control"
	if ok, assign := apollo.FeatureExp(ctx, "athena_wait_anycar_ets_top_info_ab", cast.ToString(c.UserInfo.PID), apolloParams); ok {
		if assign.GetGroupName() == "treatment_group" {
			group = "experiment"
		}
	}
	etsGap, etsShowType := c.calculateEtsInfo(ctx)
	materialConfig := map[string]string{
		"etsShowType": cast.ToString(etsShowType),
		"ets_gap":     cast.ToString(etsGap),
	}

	mC := material.NewDefaultComponentMaterial("anycar_estimate_ets_communicate_tag", "wait_answer", "anycar_guide", group)
	mC.SetCondition(materialConfig)

	var conf = &MatchCardTitleStruct{}
	err := mC.RenderContent(conf)
	if err != nil || conf == nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "fail to get material anycar_estimate_ets_communicate_tag with err %v and conf = nil :%v", err, conf == nil)
		return &emptyStr, nil
	}

	conf.Text = *c.appendNormalCompensationInfo(ctx, conf.Text, sourceType)

	// 对于高版本替换标题文本
	if (util.IsNA(cast.ToInt32(c.req.AccessKeyId)) && util.VersionCompare(c.req.Appversion, "6.9.9") >= 0) ||
		(util.IsMini(cast.ToInt32(c.req.AccessKeyId)) && util.VersionCompare(c.req.Appversion, "6.9.35") >= 0) {
		// 如果命中了全局ets
		if c.IsHitPredictGlobalEts() {
			text, leftIcon := c.getFixedOutletTextV2(ctx)
			return text, leftIcon
		}
	} else {
		// 不再开量
		text, done := c.getFixedOutletText(ctx, conf)
		if done {
			log.Trace.Infof(ctx, trace.DLTagUndefined, "fixed outlet exp hit getFixedOutletText, text = %s", *text)
			return text, nil
		}
	}

	return &conf.Text, nil
}

func (c *Service) IsHitPredictGlobalEts() bool {
	return c.isHitPredictGlobalEts
}

func (c *Service) IsPredictGlobalEtsTimeout() bool {
	return c.isPredictGlobalEtsTimeout
}

func (c *Service) getFixedOutletText(ctx context.Context, conf *MatchCardTitleStruct) (*string, bool) {
	if exp.IsFixedOutletExp(
		ctx,
		cast.ToString(c.UserInfo.PID),
		c.UserInfo.Phone,
		cast.ToString(c.req.FromArea),
		cast.ToString(c.req.AccessKeyId),
		c.req.Appversion,
	) {
		if conf.Text != "" {
			log.Trace.Infof(ctx, trace.DLTagUndefined, "fixed outlet exp hit, but title is not empty")
			return &conf.Text, true
		}

		bottomText := dcmp.GetJSONContentWithPath(ctx, DCMPKeyFixedOutlet, nil, "comma") +
			dcmp.GetJSONContentWithPath(ctx, DCMPKeyFixedOutlet, nil, "bottom_text")

		if c.AthenaExpectInfoResult.OrderMatchGlobalSceneExpect == nil {
			log.Trace.Infof(ctx, trace.DLTagUndefined, "fixed outlet exp hit, but no orderMatchGlobalSceneExpect")
			return &bottomText, true
		}

		orderMatchGlobalSceneExpect := c.AthenaExpectInfoResult.OrderMatchGlobalSceneExpect

		// 运力不足场景
		fixedOutLet, ok := c.TransDataMap["fixed_outlet_scene"].(float64)
		if !ok {
			log.Trace.Infof(ctx, trace.DLTagUndefined, "fixed outlet exp hit, but no fixedOutLet")
		}

		if fixedOutLet == 1 {
			conf.Text = dcmp.GetJSONContentWithPath(ctx, DCMPKeyFixedOutlet, nil, "comma") +
				dcmp.GetJSONContentWithPath(ctx, DCMPKeyFixedOutlet, nil, "insufficient_capacity")
			log.Trace.Infof(ctx, trace.DLTagUndefined, "fixed outlet exp hit, fixedOutLet == 1")
			return &conf.Text, true
		}

		// 运力充足场景
		if orderMatchGlobalSceneExpect.TimeEnd == 0 {
			log.Trace.Infof(ctx, trace.DLTagUndefined, "fixed outlet exp hit, but no orderMatchGlobalSceneExpect.TimeEnd")
			return &bottomText, true
		}

		if orderMatchGlobalSceneExpect.TimeEnd > time.Now().Unix() &&
			util.InArrayInt32(orderMatchGlobalSceneExpect.Scene, []int32{GlobalSceneMedium, GlobalSceneLong}) {
			log.Trace.Infof(ctx, trace.DLTagUndefined, "fixed outlet exp hit, orderMatchGlobalSceneExpect.TimeEnd > time.Now().Unix() && inArrayInt32")
			if orderMatchGlobalSceneExpect.Scene == GlobalSceneMedium {
				log.Trace.Infof(ctx, trace.DLTagUndefined, "fixed outlet exp hit, orderMatchGlobalSceneExpect.Scene == GlobalSceneMedium")
				minETS := findMinETS(c.ProductExpectInfo, c.selectPcIds)
				if minETS > 0 {
					log.Trace.Infof(ctx, trace.DLTagUndefined, "fixed outlet exp hit, minETS > 0, =%d", minETS)
					minMinutes := convertETSToMinutes(minETS)
					text := dcmp.GetJSONContentWithPath(ctx, DCMPKeyFixedOutlet, nil, "comma") +
						dcmp.GetJSONContentWithPath(ctx, DCMPKeyFixedOutlet, map[string]string{"minutes": util.ToString(minMinutes)}, "except_in_minutes")
					return &text, true
				}
			}
		} else {
			log.Trace.Infof(ctx, trace.DLTagUndefined, "fixed outlet exp hit, orderMatchGlobalSceneExpect.TimeEnd <= time.Now().Unix() || !inArrayInt32")
			return &bottomText, true
		}

		log.Trace.Infof(ctx, trace.DLTagUndefined, "fixed outlet exp hit, but no matched condition")
		conf.Text = bottomText
	}

	return nil, false
}

// 命中全局ets是展示的列表title标签
func (c *Service) getFixedOutletTextV2(ctx context.Context) (title *string, icon *string) {
	// 如果已超时，直接返回
	if c.IsPredictGlobalEtsTimeout() {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "fixed outlet exp hit, but IsPredictGlobalEtsTimeout")
		return nil, nil
	}

	// 仅考虑运力充足场景
	showThroldSec := 15 * 60 // 15分钟
	minETS := findMinETS(c.ProductExpectInfo, c.selectPcIds)
	if minETS <= 0 || minETS > showThroldSec {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "fixed outlet exp hit, but minETS <= 0 || minETS > showThroldSec, minETS: %v, showThroldSec: %v", minETS, showThroldSec)
		return nil, nil
	}
	// 15分钟以内，展示「预计X秒/分钟应答」
	log.Trace.Infof(ctx, trace.DLTagUndefined, "fixed outlet exp hit, minETS = %d", minETS)

	// 填充时间和单位
	timeValue, timeUnit := c.fillFixedOutletTimeText(ctx, minETS)
	text := dcmp.GetJSONContentWithPath(
		ctx,
		DCMPKeyFixedOutlet,
		map[string]string{
			"time_value": util.ToString(timeValue),
			"time_unit":  timeUnit,
		},
		"except_in_time_v2")
	leftIcon := dcmp.GetJSONContentWithPath(ctx, DCMPKeyFixedOutlet, nil, "left_icon")

	log.Trace.Infof(ctx, trace.DLTagUndefined, "fixed outlet exp hit, tltle: %v, leftIcon: %v", text, leftIcon)
	return &text, &leftIcon
}

func (c *Service) fillFixedOutletTimeText(ctx context.Context, minETS int) (timeValue int, timeUnit string) {
	timeValue = minETS // 秒数
	timeUnit = "秒"
	if c.req.Lang == "en-US" {
		timeUnit = "sec"
	} else if c.req.Lang == "zh-HK" {
		timeUnit = "秒"
	}

	if minETS >= 60 {
		timeValue = convertETSToMinutes(minETS) // 分钟数
		timeUnit = "分钟"
		if c.req.Lang == "en-US" {
			timeUnit = "min"
		} else if c.req.Lang == "zh-HK" {
			timeUnit = "分鐘"
		}
	}

	log.Trace.Infof(ctx, trace.DLTagUndefined, "fillFixedOutletTimeText, minETS = %d, timeValue: %v, timeUnit: %v",
		minETS, timeValue, timeUnit)

	return timeValue, timeUnit
}

// 获取ets信息
func (c *Service) calculateEtsInfo(ctx context.Context) (int, int) {
	etsGap := 0
	if c.OrderMatchSaveTimeExpect != nil && c.OrderMatchSaveTimeExpect.WaitTimeSaved != nil {
		etsGap = int(*c.OrderMatchSaveTimeExpect.WaitTimeSaved)
	}
	//etsgap<=0或者大于上限阈值的情况下，不展示ets信息
	etsShowType := 1
	etsShowUpperLimit := 10
	if upperLimit := dcmp.GetJSONContentWithPath(ctx, "anycar_estimate-ets_communicate_show_limit", nil, "upper_limit"); len(upperLimit) > 0 {
		etsShowUpperLimit = cast.ToInt(upperLimit)
	}
	if etsGap <= 0 || etsGap > etsShowUpperLimit {
		etsShowType = 0
	}
	return etsGap, etsShowType
}

// 获取特殊化配置对应的标题信息
func (c *Service) getCustomTitleInfo(ctx context.Context, confName string) string {
	if c.CityInfo == nil {
		return ""
	}
	params := new(MatchCardTitleConditionParams)
	params.FromLng = c.req.FromLng
	params.FromLat = c.req.FromLat
	params.CityId = int(c.CityInfo.FromCityId)
	params.ToLng = c.req.ToLng
	params.ToLat = c.req.ToLat
	params.FenceToken = fence.TOKEN

	if isAllow, materialInfo := Check(ctx, "anycar_car_title", params, confName); isAllow && materialInfo != nil {
		return materialInfo.Title
	}
	return ""
}

func (c *Service) appendNormalCompensationInfo(ctx context.Context, origin string, sourceType int) *string {
	var ret string
	if sourceType != consts.SourceTypeAnyCarRecBox && sourceType != consts.SourceTypeAnyCarRecBoxSingle && sourceType != consts.SourceTypeAnyCarEstimate {
		return &ret
	}

	if c.NormalCompensationInfo == nil {
		return &ret
	}

	if c.NormalCompensationInfo.Status != 0 && c.NormalCompensationInfo.Status != 1 {
		return &ret
	}

	if c.NormalCompensationInfo.FrequencyStatus == 1 { // 兜底，达到赔付次数上限不感知
		return &ret
	}

	// 保证本次有赔付车型
	if c._getCurCompensationNum() <= 0 {
		return &ret
	}

	// 保证之前已触发赔付
	if c.NormalCompensationInfo.CalledProductCategoryNum < c.NormalCompensationInfo.ProductCategoryNum {
		return &ret
	}
	// 新流人群，追加车型弹窗不需要展示文案
	if c._checkIsNewLoss(c.NormalCompensationInfo.PrivilegeSource) &&
		(sourceType == consts.SourceTypeAnyCarRecBox || sourceType == consts.SourceTypeAnyCarRecBoxSingle) {
		return &ret
	}

	amountDiff := c.NormalCompensationInfo.CouponInfo.MultiSelectAmount
	if amountDiff == "" || amountDiff == "0" {
		return &ret
	}

	path := "style_" + util.ToString(sourceType) + ".match_title.text"
	ret = dcmp.GetJSONContentWithPath(ctx, DcmpKey, map[string]string{"amount": amountDiff}, path)
	if origin != "" {
		ret = origin + "·" + ret
	}
	return &ret
}

type MatchCardTitleMaterials struct {
	Title string `json:"title"`
}
type MatchCardTitleConditionParams struct {
	CityId     int     `json:"city_id"`
	FromLng    float64 `json:"from_lng"`
	FromLat    float64 `json:"from_lat"`
	ToLng      float64 `json:"to_lng"`
	ToLat      float64 `json:"to_lat"`
	FenceToken string  `json:"fence_token"`
}

func Check(ctx context.Context, namespace string, params *MatchCardTitleConditionParams, confName string) (bool, *MatchCardTitleMaterials) {
	res, checkErr := condition.Check(ctx, namespace, params, condition.WithConfName(confName))
	if checkErr != nil || res == nil || !res.IsAllow {
		return false, nil
	}
	materials := &MatchCardTitleMaterials{}
	err := res.GetMaterial(materials)
	if nil != err {
		return false, nil
	}
	return true, materials
}

func findMinETS(productSceneExpect *AthenaApiv3.ProductExpectInfo, selectPcIds []int64) int {
	minETS := math.MaxInt

	if productSceneExpect == nil {
		log.Trace.Infof(context.Background(), trace.DLTagUndefined, "findMinETS fail, productSceneExpect is nil")
		return -1
	}

	for _, product := range productSceneExpect.ProductInfos {

		if product.ShowType == nil || product.ProductCategory == nil || product.ExpectInfo == nil || product.ExpectInfo.Ets == nil {
			continue
		}

		if !funk.ContainsInt64(selectPcIds, int64(*product.ProductCategory)) || *product.ShowType != "ets" || *product.ExpectInfo.Ets <= 0 {
			continue
		}

		etsValue := int(*product.ExpectInfo.Ets)
		if etsValue < minETS {
			minETS = etsValue
		}
	}

	if minETS == math.MaxInt {
		return -1 // 如果没有找到符合条件的ETS，返回-1
	}

	return minETS
}

func convertETSToMinutes(ets int) int {
	// 计算总分钟数
	minutes := ets / 60

	// 如果有余数，则舍入到下一个整分钟
	if ets%60 > 0 {
		minutes++
	}

	return minutes
}
