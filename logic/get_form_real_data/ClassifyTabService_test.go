package get_form_real_data

import (
	"context"
	"testing"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_form_real_data/handler"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

// Mock的请求参数
func mockPGetFormRealDataReq() *proto.PGetFormRealDataReq {
	return &proto.PGetFormRealDataReq{
		EstimateTraceId: "test_trace_id",
		AccessKeyId:     12345,
		Appversion:      "1.0.0",
		Lang:            "zh-CN",
		Token:           "test_token",
		FromLng:         116.397128,
		FromLat:         39.916527,
		ToLng:           116.315925,
		ToLat:           39.982718,
		ClientType:      1,
		// 关键字段：确保不会触发前面的条件
		FontScaleType: proto.Int32Ptr(0), // 不是大字版
		RecForm:       proto.Int32Ptr(0), // 不是推荐tab
		FormStyleExp:  proto.Int32Ptr(1), // 不是7.1表单
	}
}

// Mock的全局预期信息
func mockGlobalExpectInfo() *AthenaApiv3.GlobalExpectInfo {
	return &AthenaApiv3.GlobalExpectInfo{
		ShowType: "ets",
		ExpectInfo: &AthenaApiv3.ExpectInfo{
			Ets: proto.Int32Ptr(30),
		},
	}
}

// Mock的Service实例
func mockService() *Service {
	return &Service{
		req:                    mockPGetFormRealDataReq(),
		AthenaExpectInfoResult: AthenaExpectInfoResult{GlobalSceneExpect: mockGlobalExpectInfo()},
		IsToEtp:                false,
	}
}

func TestGetClassifyTabFilterTitle(t *testing.T) {
	// 设置mock
	mockey.PatchConvey("TestGetClassifyTabFilterTitle", t, func() {

		t.Run("正常情况下调用handler.GetButtonTitle", func(t *testing.T) {
			// Mock handler.GetButtonTitle 方法
			expectedTitle := "预期标题"
			handlerPatch := mockey.Mock(handler.GetButtonTitle).Return(expectedTitle).Build()
			defer handlerPatch.UnPatch()

			// 创建Service实例
			service := mockService()
			ctx := context.Background()

			// 调用被测试方法
			result := service.getClassifyTabFilterTitle(ctx)

			// 验证结果
			assert.Equal(t, expectedTitle, result, "应该返回handler.GetButtonTitle的结果")
		})

		t.Run("大字版场景返回空字符串", func(t *testing.T) {
			// 创建Service实例
			service := mockService()
			service.req.FontScaleType = proto.Int32Ptr(1) // 设置为大字版
			ctx := context.Background()

			// 调用被测试方法
			result := service.getClassifyTabFilterTitle(ctx)

			// 验证结果
			assert.Equal(t, "", result, "大字版场景应该返回空字符串")
		})

		t.Run("推荐tab场景返回空字符串", func(t *testing.T) {
			// 创建Service实例
			service := mockService()
			service.req.RecForm = proto.Int32Ptr(1) // 设置为推荐tab
			ctx := context.Background()

			// 调用被测试方法
			result := service.getClassifyTabFilterTitle(ctx)

			// 验证结果
			assert.Equal(t, "", result, "推荐tab场景应该返回空字符串")
		})

		t.Run("7.1表单场景返回空字符串", func(t *testing.T) {
			// 创建Service实例
			service := mockService()
			service.req.FormStyleExp = proto.Int32Ptr(2) // 设置为7.1表单
			ctx := context.Background()

			// 调用被测试方法
			result := service.getClassifyTabFilterTitle(ctx)

			// 验证结果
			assert.Equal(t, "", result, "7.1表单场景应该返回空字符串")
		})
	})
}
