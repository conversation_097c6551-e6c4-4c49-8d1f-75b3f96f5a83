package get_form_real_data

import (
	"context"
	"encoding/json"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_form_real_data/handler"
	"math"
	"time"

	"git.xiaojukeji.com/gulfstream/passenger-common/model/price"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

const (
	EtsDcmpKey                         = "config_text-ets_communicate_form_real_data"
	EtsDcmpKeyNewStyle                 = "config_text-ets_communicate_form_real_data_new"
	EtpDcmpKey                         = "config_text-etp_communicate_form_real_data_v2"
	DefaultSelectedCompensationDcmpKey = "config_text-default_selected_compensation_form_real_data"
	DefaultLeftIcon                    = "default_left_icon"
	EnoughLeftIcon                     = "enough_left_icon"
	MaxEts                             = 15 * 60
	EnoughSecondsThreshold             = 10
	SecondsThreshold                   = 30
	OneMinuteThreshold                 = 60
	MinutesThreshold                   = 2 * 60
	CompensationShowEventId            = "wyc_compensation_connect_sw"
	CompensationClickEventId           = "wyc_compensation_connect_ck"
	HitDefaultSelectedCompensation     = 1
)

type etsHeadRuleData struct {
	contentKey   string
	omegaContent string
	leftIconKey  string
	params       map[string]string
}

type compensationHeadRuleInfo struct {
	NoRightIcon   string `json:"no_right_icon"`
	NoRightText   string `json:"no_right_text"`
	HasRightIcon  string `json:"has_right_icon"`
	HasRightText  string `json:"has_right_text"`
	HasRightBgImg string `json:"has_right_bg_img"`
	NoRightBgImg  string `json:"no_right_bg_img"`
}

func (c *Service) getFromHeadRule4RecForm(ctx context.Context, sourceType int32) (*proto.FormHeadRule, int32) {
	recType := c.req.GetRecType()
	ets := c.getEtsFromGlobalSceneExpect()
	switch recType {
	case FirstCall:
		if ets > 0 && ets <= MinutesThreshold {
			return c.getFromHeadRuleExpect(ctx), EtsResult
		}
		compensation := c.getFromHeadRule4NormalCompensation(ctx, sourceType)
		if compensation == nil {
			return c.getFromHeadRuleExpect(ctx), EtsResult
		}
		return compensation, CompensationResult
	case CompensationResult:
		return c.getFromHeadRule4NormalCompensation(ctx, sourceType), CompensationResult
	case EtsResult:
		return c.getFromHeadRuleExpect(ctx), EtsResult
	default:
		return nil, 0
	}
}

func (c *Service) getFromHeadRuleExpect(ctx context.Context) *proto.FormHeadRule {
	ets := c.getEtsFromGlobalSceneExpect()
	etp := c.getEtpFromGlobalSceneExpect()

	if c.IsToEtp {
		return c.getFromHeadRule4Etp(ctx, etp)
	}
	return c.getFromHeadRule4Ets(ctx, ets)
}

func (c *Service) getFromHeadRule4Ets(ctx context.Context, ets int) *proto.FormHeadRule {
	getEtsCase := func(ets int) etsHeadRuleData {
		switch {
		case len(c.selectPcIds) == 0:
			return etsHeadRuleData{"unchecked", "请至少勾选1种车型", DefaultLeftIcon, nil}
		case ets <= 0:
			return etsHeadRuleData{"default_content", "多选车型更快出发", DefaultLeftIcon, nil}
		case ets > MaxEts:
			return etsHeadRuleData{"over_15_content", "叫车高峰，多选车型更快出发", DefaultLeftIcon, nil}
		case ets <= EnoughSecondsThreshold:
			return etsHeadRuleData{"less_than_10_content", fmt.Sprintf("%d秒", ets), EnoughLeftIcon, map[string]string{"second": cast.ToString(ets)}}
		case ets <= SecondsThreshold:
			return etsHeadRuleData{"seconds_content", fmt.Sprintf("%d秒", ets), DefaultLeftIcon, map[string]string{"second": cast.ToString(ets)}}
		case ets <= OneMinuteThreshold:
			return etsHeadRuleData{"one_minute_content", "1分钟", DefaultLeftIcon, map[string]string{"min": "1"}}
		default:
			minutes := cast.ToString(int(math.Ceil(float64(ets) / 60)))
			return etsHeadRuleData{"minutes_content", fmt.Sprintf("%s分钟", minutes), DefaultLeftIcon, map[string]string{"min": minutes}}
		}
	}
	caseData := getEtsCase(ets)
	res := &proto.FormHeadRule{
		Content:  dcmp.GetJSONContentWithPath(ctx, EtsDcmpKey, caseData.params, caseData.contentKey),
		LeftIcon: util.String2PtrString(dcmp.GetJSONContentWithPath(ctx, EtsDcmpKey, nil, caseData.leftIconKey)),
		Style:    1,
		OmegaData: &proto.Omega{
			OmegaEventId: "wyc_didiapp_biaodan_yuqi_sw",
			OmegaParameter: map[string]string{
				"content": caseData.omegaContent,
			},
		},
	}
	return handlerFormHeadRule(ctx, res, caseData, c.req.GetFormStyleExp())
}

func (c *Service) getFromHeadRule4Etp(ctx context.Context, etp int32) *proto.FormHeadRule {
	var etpThreshold []handler.FormDataETPThresholdConfig
	thres := dcmp.GetJSONContentWithPath(ctx, EtpDcmpKey, nil, "less_than_threshold")
	_ = json.Unmarshal([]byte(thres), &etpThreshold)

	getEtpCase := func(etp int32) etsHeadRuleData {
		switch {
		case len(c.selectPcIds) == 0:
			return etsHeadRuleData{dcmp.GetJSONContentWithPath(ctx, EtpDcmpKey, nil, "unchecked"), "请至少勾选1种车型", DefaultLeftIcon, nil}
		case etp <= 0 || c.GlobalSceneExpect != nil && c.GlobalSceneExpect.GetGlobalSceneFlag() == 1:
			return etsHeadRuleData{dcmp.GetJSONContentWithPath(ctx, EtpDcmpKey, nil, "default_content"), "多选车型更快出发", DefaultLeftIcon, nil}
		case etp > MaxEts:
			return etsHeadRuleData{dcmp.GetJSONContentWithPath(ctx, EtpDcmpKey, nil, "over_15_content"), "叫车高峰，多选车型更快出发", DefaultLeftIcon, nil}
		default:
			config := handler.GetETPThresholdConfig(etp, etpThreshold)
			if config != nil {
				return etsHeadRuleData{config.Content, fmt.Sprintf("%d分钟", config.FixValue/60), DefaultLeftIcon, nil}
			}
			return etsHeadRuleData{dcmp.GetJSONContentWithPath(ctx, EtpDcmpKey, nil, "expect_scene_over_600_not_queue_scene"), fmt.Sprintf("最快10分钟"), DefaultLeftIcon, nil}
		}
	}

	caseData := getEtpCase(etp)
	compensationInfo := c.getCompensationInfo(ctx)
	res := &proto.FormHeadRule{
		Content:  caseData.contentKey,
		LeftIcon: util.String2PtrString(dcmp.GetJSONContentWithPath(ctx, EtsDcmpKey, nil, caseData.leftIconKey)),
		Style:    1,
		OmegaData: &proto.Omega{
			OmegaEventId: "wyc_didiapp_biaodan_yuqi_sw",
			OmegaParameter: map[string]string{
				"content": caseData.omegaContent,
			},
		},
		CompensationInfo: compensationInfo,
	}

	if c.req.GetFormStyleExp() != 0 {
		res.LeftIcon = nil
	}

	return res
}

func (c *Service) getEtsFromGlobalSceneExpect() int {
	if info := c.AthenaExpectInfoResult.GlobalSceneExpect; info != nil && info.ExpectInfo != nil {
		return int(info.ExpectInfo.GetEts())
	}
	return 0
}

func (c *Service) getEtpFromGlobalSceneExpect() int32 {
	if info := c.AthenaExpectInfoResult.GlobalSceneExpect; info != nil && info.ExpectInfo != nil {
		return info.ExpectInfo.GetEtp()
	}
	return 0
}

func (c *Service) getCompensationInfo(ctx context.Context) *proto.FormHeadCompensationRule {
	if c.DefaultSelectedCompensationInfo == nil {
		return nil
	}
	dcmpInfo := &compensationHeadRuleInfo{}
	raw := dcmp.GetDcmpContent(ctx, DefaultSelectedCompensationDcmpKey, nil)
	if err := json.Unmarshal([]byte(raw), dcmpInfo); err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "json unmarshal fail with compensationHeadRuleInfo with err %v", err)
		return nil
	}

	// 数据准备
	compensationCategoryPriceList := c.buildCompensationCategoryPriceList(ctx)
	amount := util.FormatPriceFloor(float64(c.DefaultSelectedCompensationInfo.CouponAmount)/float64(100), 1)
	price.CheckSingle(ctx, "getCompensationInfo", "defaultSelectedCompensation", "CouponAmount", util.ToFloat64(amount))
	timeDiff := c.DefaultSelectedCompensationInfo.CompensationTimeRound - time.Now().Unix()
	tag := map[string]string{"compensation_time": c.DefaultSelectedCompensationInfo.CompensationTimeStr, "amount": amount}

	res := &proto.FormHeadCompensationRule{
		SelectedData:            c.buildCompensationRightContent(dcmp.TranslateTemplate(dcmpInfo.HasRightText, tag), dcmpInfo.HasRightIcon, dcmpInfo.HasRightBgImg),
		UnselectedData:          c.buildCompensationRightContent(dcmpInfo.NoRightText, dcmpInfo.NoRightIcon, dcmpInfo.NoRightBgImg),
		CompensationProductList: c.DefaultSelectedCompensationInfo.ProductCategoryList,
		OmegaData:               c.buildCompensationOmegaData(CompensationShowEventId, amount, timeDiff, compensationCategoryPriceList),
		OmegaDataCk:             c.buildCompensationOmegaData(CompensationClickEventId, amount, timeDiff, compensationCategoryPriceList),
	}
	return res
}

func handlerFormHeadRule(ctx context.Context, res *proto.FormHeadRule, caseData etsHeadRuleData, formStyleExp int32) *proto.FormHeadRule {
	if formStyleExp == 0 {
		return res
	}

	// 修改icon and content
	res.LeftIcon = nil
	res.Content = dcmp.GetJSONContentWithPath(ctx, EtsDcmpKeyNewStyle, caseData.params, caseData.contentKey)
	return res
}

func (c *Service) buildCompensationCategoryPriceList(ctx context.Context) string {
	type ProductInfo struct {
		ProductCategory int64   `json:"product_category"`
		EstimateId      string  `json:"estimate_id"`
		FeeAmount       float64 `json:"fee_amount"`
	}

	productInfoMap := make(map[int64]ProductInfo)
	for _, product := range c.reqMultiProduct {
		productInfoMap[product.ProductCategory] = ProductInfo{
			ProductCategory: product.ProductCategory,
			EstimateId:      product.EstimateID,
			FeeAmount:       product.FeeAmount,
		}
	}

	var compensationCategoryPriceList []ProductInfo
	for _, productCategory := range c.DefaultSelectedCompensationInfo.ProductCategoryList {
		if productInfo, ok := productInfoMap[productCategory]; ok {
			compensationCategoryPriceList = append(compensationCategoryPriceList, productInfo)
		}
	}

	res, err := json.Marshal(compensationCategoryPriceList)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "json marshal fail with compensationCategoryPriceList with err %v", err)
		return ""
	}

	return string(res)
}

func (c *Service) buildCompensationRightContent(text, icon, bgImg string) *proto.CompensationRightContent {
	return &proto.CompensationRightContent{
		RightText:  util.String2PtrString(text),
		RightIcon:  util.String2PtrString(icon),
		RightBgImg: util.String2PtrString(bgImg),
	}
}

func (c *Service) buildCompensationOmegaData(eventId, amount string, timeDiff int64, compensationProductCategoryPriceList string) *proto.Omega {
	return &proto.Omega{
		OmegaEventId: eventId,
		OmegaParameter: map[string]string{
			"estimate_trace_id":          c.req.EstimateTraceId,
			"is_etp_compensation":        util.ToString(HitDefaultSelectedCompensation),
			"time_diff":                  util.ToString(timeDiff),
			"compensation_amount":        amount,
			"compensation_category_list": compensationProductCategoryPriceList,
		},
	}
}
