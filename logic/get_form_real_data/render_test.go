package get_form_real_data

import (
	"context"
	"errors"
	"testing"

	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	Apollo "git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_form_real_data/handler"
	"git.xiaojukeji.com/nuwa/golibs/zerolog/ddlog"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2"
	"go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/model"
)

// setupCommonMocks sets up common mocks needed across multiple tests
func setupCommonMocks() []*mockey.Mocker {
	mocks := []*mockey.Mocker{}

	// Mock util.GetTraceIDFromCtxWithoutCheck to prevent nil pointer dereference
	mockGetTraceID := mockey.Mock(util.GetTraceIDFromCtxWithoutCheck).Return("test_trace_id").Build()
	mocks = append(mocks, mockGetTraceID)

	// Mock ddlog.DiLogHandle.Errorf to prevent logging issues
	mockDiLogErrorf := mockey.Mock((*ddlog.DiLogHandle).Errorf).Return().Build()
	mocks = append(mocks, mockDiLogErrorf)

	// Mock log.Public.Public to prevent logging issues
	mockLogPublic := mockey.Mock((*ddlog.PubLog).Public).Return().Build()
	mocks = append(mocks, mockLogPublic)

	// Mock util.Go to prevent goroutine issues
	mockUtilGo := mockey.Mock(util.Go).Return().Build()
	mocks = append(mocks, mockUtilGo)

	// Mock trace.Warnf to prevent logging issues
	mockTraceWarnf := mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build()
	mocks = append(mocks, mockTraceWarnf)

	return mocks
}

// 模拟请求参数和依赖
func mockRenderSourceTypeClassifyTabDependencies(t *testing.T) (*mockey.Mocker, *mockey.Mocker, *mockey.Mocker, *mockey.Mocker) {
	// 模拟 getClassifyTabFilterTitle 方法
	mockGetClassifyTabFilterTitle := mockey.Mock((*Service).getClassifyTabFilterTitle).Return("模拟过滤标题").Build()

	// 模拟 getFromHeadRule 方法，返回HeadInfo和headRuleResult
	mockHeadInfo := &proto.FormHeadRule{
		Title: proto.StrPtr("模拟标题"),
	}
	mockHeadRuleResult := int32(123)
	mockGetFromHeadRule := mockey.Mock((*Service).getFromHeadRule).Return(mockHeadInfo, mockHeadRuleResult).Build()

	// 模拟 getBargainRangeAnswerRateInfoByBubble 方法
	mockBargainRangeInfo := &proto.BargainRangeAnswerRateInfo{
		AnswerRateText: "模拟价格范围应答率",
	}
	mockGetBargainRangeAnswerRateInfoByBubble := mockey.Mock((*Service).getBargainRangeAnswerRateInfoByBubble).Return(mockBargainRangeInfo).Build()

	// 模拟 getProductExpectInfo 方法
	mockProductInfo := map[string]*proto.FormRealProductInfo{
		"1": {
			ProductExpectText:  proto.StrPtr("模拟产品期望文本"),
			ProductExpectValue: proto.Int32Ptr(456),
		},
	}
	mockGetProductExpectInfo := mockey.Mock((*Render).getProductExpectInfo).Return(mockProductInfo).Build()

	return mockGetClassifyTabFilterTitle, mockGetFromHeadRule, mockGetBargainRangeAnswerRateInfoByBubble, mockGetProductExpectInfo
}

func TestRenderSourceTypeClassifyTab(t *testing.T) {
	defer mockey.PatchConvey("测试RenderSourceTypeClassifyTab方法", t, func() {
		// 准备测试数据
		ctx := context.Background()
		service := &Service{}
		render := NewRender(service)
		data := &proto.FormRealData{}

		// 模拟依赖方法
		mockGetClassifyTabFilterTitle, mockGetFromHeadRule, mockGetBargainRangeAnswerRateInfoByBubble, mockGetProductExpectInfo := mockRenderSourceTypeClassifyTabDependencies(t)
		defer mockGetClassifyTabFilterTitle.UnPatch()
		defer mockGetFromHeadRule.UnPatch()
		defer mockGetBargainRangeAnswerRateInfoByBubble.UnPatch()
		defer mockGetProductExpectInfo.UnPatch()

		// 执行测试方法
		result := render.RenderSourceTypeClassifyTab(ctx, data)

		// 验证结果
		t.Run("验证FilterTitle设置正确", func(t *testing.T) {
			assert.Equal(t, "模拟过滤标题", result.FilterTitle, "FilterTitle应该被正确设置")
		})

		t.Run("验证HeadInfo设置正确", func(t *testing.T) {
			assert.Equal(t, "模拟标题", *result.HeadInfo.Title, "HeadInfo.Title应该被正确设置")
		})

		t.Run("验证BargainRangeAnswerRateInfo设置正确", func(t *testing.T) {
			assert.Equal(t, "模拟价格范围应答率", result.BargainRangeAnswerRateInfo.AnswerRateText, "BargainRangeAnswerRateInfo.AnswerRateText应该被正确设置")
		})

		t.Run("验证ExtraParams设置正确", func(t *testing.T) {
			assert.NotNil(t, result.ExtraParams, "ExtraParams不应该为nil")
			assert.Equal(t, int32(123), result.ExtraParams["rec_type"], "ExtraParams[\"rec_type\"]应该被正确设置")
		})

		t.Run("验证ProductInfo设置正确", func(t *testing.T) {
			assert.NotNil(t, result.ProductInfo, "ProductInfo不应该为nil")
			productInfo, exists := result.ProductInfo["1"]
			assert.True(t, exists, "ProductInfo应该包含键\"1\"")
			assert.Equal(t, "模拟产品期望文本", *productInfo.ProductExpectText, "ProductExpectText应该被正确设置")
			assert.Equal(t, int32(456), *productInfo.ProductExpectValue, "ProductExpectValue应该被正确设置")
		})
	})
}

// 准备测试数据
func prepareRenderData() (*Render, *proto.PGetFormRealDataReq, context.Context) {
	ctx := context.Background()
	req := &proto.PGetFormRealDataReq{
		AccessKeyId:     1234,
		Appversion:      "1.0.0",
		FromArea:        int64(1),
		EstimateTraceId: "trace_123",
		SourceType:      util.Int32Ptr(consts.SourceTypeNormalTab),
		FontScaleType:   util.Int32Ptr(0),
	}

	service := &Service{
		req: req,
		UserInfo: &passport.UserInfo{
			Phone: "13800138000",
		},
		IsToEtp: false,
		AthenaExpectInfoResult: AthenaExpectInfoResult{
			V3GlobalSceneExpect: &AthenaApiv3.GlobalExpectInfo{},
		},
	}

	render := &Render{
		service: service,
	}

	return render, req, ctx
}

// Mock依赖项
func mockRenderDependencies() []*mockey.Mocker {
	mocks := []*mockey.Mocker{}

	// Mock apollo FeatureToggle
	toggleMock := mockey.Mock(apollo.FeatureToggle).Return(&model.ToggleResult{}, nil).Build()
	mocks = append(mocks, toggleMock)

	// Mock IsAllow
	isAllowMock := mockey.Mock((*model.ToggleResult).IsAllow).Return(true).Build()
	mocks = append(mocks, isAllowMock)

	// Mock GetButtonTitle
	getButtonTitleMock := mockey.Mock(handler.GetButtonTitle).Return("新标题").Build()
	mocks = append(mocks, getButtonTitleMock)

	// Mock GetNewTitle
	getNewTitleMock := mockey.Mock(handler.GetNewTitle).Return("默认标题").Build()
	mocks = append(mocks, getNewTitleMock)

	// Mock GetDcmpPlainContent
	getDcmpPlainContentMock := mockey.Mock(dcmp.GetDcmpPlainContent).Return("{}").Build()
	mocks = append(mocks, getDcmpPlainContentMock)

	// Mock GetJSONContentWithPath
	getJSONContentWithPathMock := mockey.Mock(dcmp.GetJSONContentWithPath).Return("JSON内容").Build()
	mocks = append(mocks, getJSONContentWithPathMock)

	return mocks
}

// Mock Service方法
func mockServiceMethods(s *Service) []*mockey.Mocker {
	mocks := []*mockey.Mocker{}

	// Mock getFromHeadRule
	getFromHeadRuleMock := mockey.Mock((*Service).getFromHeadRule).Return(&proto.FormHeadRule{
		Title: util.StringPtr("头部标题"),
	}, int32(1)).Build()
	mocks = append(mocks, getFromHeadRuleMock)

	// Mock getBargainRangeAnswerRateInfoByBubble
	getBargainRangeAnswerRateInfoByBubbleMock := mockey.Mock((*Service).getBargainRangeAnswerRateInfoByBubble).Return(&proto.BargainRangeAnswerRateInfo{
		AnswerRateText:  "90%应答率",
		AnswerRateColor: "#FF0000",
		AnswerRate:      util.Int32Ptr(90),
	}).Build()
	mocks = append(mocks, getBargainRangeAnswerRateInfoByBubbleMock)

	return mocks
}

// Mock Render方法
func mockRenderMethods(r *Render) []*mockey.Mocker {
	mocks := []*mockey.Mocker{}

	// Mock getProductExpectInfo
	productInfoMap := make(map[string]*proto.FormRealProductInfo)
	productInfoMap["1"] = &proto.FormRealProductInfo{
		ProductExpectText:  util.StringPtr("期望文本"),
		ProductExpectValue: util.Int32Ptr(10),
	}

	getProductExpectInfoMock := mockey.Mock((*Render).getProductExpectInfo).Return(productInfoMap).Build()
	mocks = append(mocks, getProductExpectInfoMock)

	return mocks
}

func TestRenderSourceTypeNormalTab(t *testing.T) {
	// 准备测试数据
	render, req, ctx := prepareRenderData()

	// Mock依赖项
	mocks := mockRenderDependencies()
	defer func() {
		for _, mock := range mocks {
			mock.UnPatch()
		}
	}()

	// Mock Service方法
	serviceMocks := mockServiceMethods(render.service)
	mocks = append(mocks, serviceMocks...)

	// Mock Render方法
	renderMocks := mockRenderMethods(render)
	mocks = append(mocks, renderMocks...)

	// 测试用例1: 使用新的FilterTitle
	t.Run("使用新的FilterTitle", func(t *testing.T) {
		// 模拟isNewFilterTitle返回true
		isNewFilterTitleMock := mockey.Mock((*Render).isNewFilterTitle).Return(true).Build()
		defer isNewFilterTitleMock.UnPatch()

		data := &proto.FormRealData{}
		result := render.RenderSourceTypeNormalTab(ctx, data)

		// 断言
		assert.Equal(t, "新标题", result.FilterTitle, "应该设置为新标题")
		assert.NotNil(t, result.HeadInfo, "HeadInfo不应为空")
		assert.Equal(t, "头部标题", *result.HeadInfo.Title, "HeadInfo.Title应该正确设置")
		assert.NotNil(t, result.ProductInfo, "ProductInfo不应为空")
		assert.NotNil(t, result.BargainRangeAnswerRateInfo, "BargainRangeAnswerRateInfo不应为空")
		assert.Equal(t, "90%应答率", result.BargainRangeAnswerRateInfo.AnswerRateText, "BargainRangeAnswerRateInfo应该正确设置")
	})

	// 测试用例2: 使用默认的FilterTitle
	t.Run("使用默认的FilterTitle", func(t *testing.T) {
		// 模拟isNewFilterTitle返回false
		isNewFilterTitleMock := mockey.Mock((*Render).isNewFilterTitle).Return(false).Build()
		defer isNewFilterTitleMock.UnPatch()

		// 模拟getDefaultTitle
		getDefaultTitleMock := mockey.Mock((*Render).getDefaultTitle).Return("默认标题").Build()
		defer getDefaultTitleMock.UnPatch()

		data := &proto.FormRealData{}
		result := render.RenderSourceTypeNormalTab(ctx, data)

		// 断言
		assert.Equal(t, "默认标题", result.FilterTitle, "应该设置为默认标题")
		assert.NotNil(t, result.HeadInfo, "HeadInfo不应为空")
		assert.NotNil(t, result.ProductInfo, "ProductInfo不应为空")
		assert.NotNil(t, result.BargainRangeAnswerRateInfo, "BargainRangeAnswerRateInfo不应为空")
	})

	// 测试用例3: 大字版屏蔽FilterTitle
	t.Run("大字版屏蔽FilterTitle", func(t *testing.T) {
		// 修改FontScaleType
		req.FontScaleType = util.Int32Ptr(1)

		data := &proto.FormRealData{}
		result := render.RenderSourceTypeNormalTab(ctx, data)

		// 断言
		assert.Equal(t, "", result.FilterTitle, "大字版时FilterTitle应该为空")
		assert.NotNil(t, result.HeadInfo, "HeadInfo不应为空")
		assert.NotNil(t, result.ProductInfo, "ProductInfo不应为空")
		assert.NotNil(t, result.BargainRangeAnswerRateInfo, "BargainRangeAnswerRateInfo不应为空")

		// 恢复FontScaleType
		req.FontScaleType = util.Int32Ptr(0)
	})
}

// 测试getBoxExpectInfo方法
func TestGetBoxExpectInfo(t *testing.T) {
	defer mockey.UnPatchAll()

	ctx := context.Background()

	tests := []struct {
		name        string
		setupRender func() *Render
		setupMocks  func() []*mockey.Mocker
		productInfo map[string]*proto.FormRealProductInfo
		expectNil   bool
		expectCount int
	}{
		{
			name: "service为nil时返回nil",
			setupRender: func() *Render {
				return &Render{service: nil}
			},
			setupMocks:  func() []*mockey.Mocker { return nil },
			productInfo: make(map[string]*proto.FormRealProductInfo),
			expectNil:   true,
		},
		{
			name: "reqBoxInfo为nil时返回nil",
			setupRender: func() *Render {
				return &Render{
					service: &Service{
						reqBoxInfo: nil,
						IsToEtp:    true,
					},
				}
			},
			setupMocks:  func() []*mockey.Mocker { return nil },
			productInfo: make(map[string]*proto.FormRealProductInfo),
			expectNil:   true,
		},
		{
			name: "IsToEtp为false时返回nil",
			setupRender: func() *Render {
				return &Render{
					service: &Service{
						reqBoxInfo: map[string][]string{"box1": {"pc1", "pc2"}},
						IsToEtp:    false,
					},
				}
			},
			setupMocks:  func() []*mockey.Mocker { return nil },
			productInfo: make(map[string]*proto.FormRealProductInfo),
			expectNil:   true,
		},
		{
			name: "配置不允许展示时返回空map",
			setupRender: func() *Render {
				mockCfg := &Apollo.Config{}
				return &Render{
					service: &Service{
						reqBoxInfo: map[string][]string{"box1": {"pc1", "pc2"}},
						IsToEtp:    true,
						Cfg:        mockCfg,
					},
				}
			},
			setupMocks: func() []*mockey.Mocker {
				return []*mockey.Mocker{
					mockey.Mock((*Apollo.Config).FeatureConfigAllow).Return(false).Build(),
				}
			},
			productInfo: make(map[string]*proto.FormRealProductInfo),
			expectNil:   false,
			expectCount: 0,
		},
		{
			name: "正常情况返回box信息",
			setupRender: func() *Render {
				mockCfg := &Apollo.Config{}
				return &Render{
					service: &Service{
						reqBoxInfo: map[string][]string{
							"box1": {"1001", "1002"},
							"box2": {"2001", "2002"},
						},
						IsToEtp:       true,
						Cfg:           mockCfg,
						selectPcIdSet: map[int64]struct{}{1001: {}},
					},
				}
			},
			setupMocks: func() []*mockey.Mocker {
				return []*mockey.Mocker{
					mockey.Mock((*Apollo.Config).FeatureConfigAllow).Return(true).Build(),
					mockey.Mock((*Render).shouldHideETPForBox).Return(false).Build(),
					mockey.Mock((*Render).findMinETPProduct).Return(&proto.FormRealProductInfo{
						ProductExpectText:  util.StringPtr("5分钟"),
						ProductExpectValue: util.Int32Ptr(5),
					}).Build(),
				}
			},
			productInfo: map[string]*proto.FormRealProductInfo{
				"1001": {
					ProductExpectText:  util.StringPtr("5分钟"),
					ProductExpectValue: util.Int32Ptr(5),
				},
				"1002": {
					ProductExpectText:  util.StringPtr("10分钟"),
					ProductExpectValue: util.Int32Ptr(10),
				},
			},
			expectNil:   false,
			expectCount: 2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			render := tt.setupRender()
			var mocks []*mockey.Mocker
			if tt.setupMocks != nil {
				mocks = tt.setupMocks()
			}

			result := render.getBoxExpectInfo(ctx, tt.productInfo)

			if tt.expectNil {
				assert.Nil(t, result, "应返回nil")
			} else {
				assert.NotNil(t, result, "不应返回nil")
				assert.Equal(t, tt.expectCount, len(result), "返回的map长度应匹配")
			}

			// 清理mocks
			for _, m := range mocks {
				if m != nil {
					m.UnPatch()
				}
			}
		})
	}
}

// 测试findMinETPProduct方法
func TestFindMinETPProduct(t *testing.T) {
	defer mockey.UnPatchAll()

	tests := []struct {
		name           string
		setupRender    func() *Render
		pcIds          []string
		productInfoMap map[string]*proto.FormRealProductInfo
		expectNil      bool
		expectEtp      int32
	}{
		{
			name: "空的pcIds列表",
			setupRender: func() *Render {
				return &Render{
					service: &Service{
						selectPcIdSet: map[int64]struct{}{},
						reqBoxInfo:    map[string][]string{},
					},
				}
			},
			pcIds:          []string{},
			productInfoMap: make(map[string]*proto.FormRealProductInfo),
			expectNil:      true,
		},
		{
			name: "找到最小ETP的产品",
			setupRender: func() *Render {
				return &Render{
					service: &Service{
						selectPcIdSet: map[int64]struct{}{},
						reqBoxInfo:    map[string][]string{},
					},
				}
			},
			pcIds: []string{"1001", "1002", "1003"},
			productInfoMap: map[string]*proto.FormRealProductInfo{
				"1001": {
					ProductExpectValue: util.Int32Ptr(10),
					ProductExpectText:  util.StringPtr("10分钟"),
				},
				"1002": {
					ProductExpectValue: util.Int32Ptr(5), // 最小值
					ProductExpectText:  util.StringPtr("5分钟"),
				},
				"1003": {
					ProductExpectValue: util.Int32Ptr(15),
					ProductExpectText:  util.StringPtr("15分钟"),
				},
			},
			expectNil: false,
			expectEtp: 5,
		},
		{
			name: "优先返回已选择的产品",
			setupRender: func() *Render {
				return &Render{
					service: &Service{
						selectPcIdSet: map[int64]struct{}{1002: {}}, // 1002被选中
						reqBoxInfo:    map[string][]string{},
					},
				}
			},
			pcIds: []string{"1001", "1002", "1003"},
			productInfoMap: map[string]*proto.FormRealProductInfo{
				"1001": {
					ProductExpectValue: util.Int32Ptr(3), // 全局最小值
					ProductExpectText:  util.StringPtr("3分钟"),
				},
				"1002": {
					ProductExpectValue: util.Int32Ptr(8), // 被选中的产品
					ProductExpectText:  util.StringPtr("8分钟"),
				},
				"1003": {
					ProductExpectValue: util.Int32Ptr(15),
					ProductExpectText:  util.StringPtr("15分钟"),
				},
			},
			expectNil: false,
			expectEtp: 8, // 应返回被选中的产品
		},
		{
			name: "处理嵌套的box信息",
			setupRender: func() *Render {
				return &Render{
					service: &Service{
						selectPcIdSet: map[int64]struct{}{},
						reqBoxInfo: map[string][]string{
							"1001": {"2001", "2002"}, // 嵌套的box
						},
					},
				}
			},
			pcIds: []string{"1001"},
			productInfoMap: map[string]*proto.FormRealProductInfo{
				"1001": {
					ProductExpectValue: util.Int32Ptr(10),
					ProductExpectText:  util.StringPtr("10分钟"),
				},
				"2001": {
					ProductExpectValue: util.Int32Ptr(6), // 嵌套中的最小值
					ProductExpectText:  util.StringPtr("6分钟"),
				},
				"2002": {
					ProductExpectValue: util.Int32Ptr(12),
					ProductExpectText:  util.StringPtr("12分钟"),
				},
			},
			expectNil: false,
			expectEtp: 6,
		},
		{
			name: "忽略ETP值小于等于0的产品",
			setupRender: func() *Render {
				return &Render{
					service: &Service{
						selectPcIdSet: map[int64]struct{}{},
						reqBoxInfo:    map[string][]string{},
					},
				}
			},
			pcIds: []string{"1001", "1002", "1003"},
			productInfoMap: map[string]*proto.FormRealProductInfo{
				"1001": {
					ProductExpectValue: util.Int32Ptr(0), // 应被忽略
					ProductExpectText:  util.StringPtr("0分钟"),
				},
				"1002": {
					ProductExpectValue: util.Int32Ptr(-5), // 应被忽略
					ProductExpectText:  util.StringPtr("-5分钟"),
				},
				"1003": {
					ProductExpectValue: util.Int32Ptr(8), // 有效值
					ProductExpectText:  util.StringPtr("8分钟"),
				},
			},
			expectNil: false,
			expectEtp: 8,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			render := tt.setupRender()

			result := render.findMinETPProduct(tt.pcIds, tt.productInfoMap)

			if tt.expectNil {
				assert.Nil(t, result, "应返回nil")
			} else {
				assert.NotNil(t, result, "不应返回nil")
				assert.Equal(t, tt.expectEtp, *result.ProductExpectValue, "ETP值应匹配")
			}
		})
	}
}

// 测试buildProductExpectETPContent方法
func TestBuildProductExpectETPContent(t *testing.T) {
	defer mockey.UnPatchAll()

	ctx := context.Background()

	tests := []struct {
		name          string
		setupRender   func() *Render
		setupMocks    func() []*mockey.Mocker
		etp           int32
		sceneFlag     int32
		queueLen      int32
		pcID          int32
		expectValue   int32
		expectContent string
		expectEmpty   bool
	}{
		{
			name: "ETP小于等于0时返回空",
			setupRender: func() *Render {
				return &Render{service: &Service{}}
			},
			setupMocks:    func() []*mockey.Mocker { return nil },
			etp:           0,
			sceneFlag:     1,
			queueLen:      5,
			pcID:          1001,
			expectValue:   0,
			expectContent: "",
			expectEmpty:   true,
		},
		{
			name: "产品应该隐藏ETP时返回空",
			setupRender: func() *Render {
				return &Render{service: &Service{}}
			},
			setupMocks: func() []*mockey.Mocker {
				return []*mockey.Mocker{
					mockey.Mock((*Render).shouldHideETPForProduct).Return(true).Build(),
				}
			},
			etp:           300,
			sceneFlag:     1,
			queueLen:      5,
			pcID:          1001,
			expectValue:   0,
			expectContent: "",
			expectEmpty:   true,
		},
		{
			name: "缺B场景返回特定内容",
			setupRender: func() *Render {
				return &Render{service: &Service{}}
			},
			setupMocks: func() []*mockey.Mocker {
				return []*mockey.Mocker{
					mockey.Mock((*Render).shouldHideETPForProduct).Return(false).Build(),
					mockey.Mock(dcmp.GetDcmpPlainContent).Return(`{
						"lack_b_360_600": "缺B场景内容360-600",
						"lack_b_over_600": "缺B场景内容600+"
					}`).Build(),
					mockey.Mock((*Render).getLackBSceneContent).Return("缺B场景内容360-600").Build(),
				}
			},
			etp:           450,
			sceneFlag:     1,
			queueLen:      5,
			pcID:          1001,
			expectValue:   450,
			expectContent: "缺B场景内容360-600",
			expectEmpty:   false,
		},
		{
			name: "正常ETP场景返回配置内容",
			setupRender: func() *Render {
				return &Render{service: &Service{}}
			},
			setupMocks: func() []*mockey.Mocker {
				mockConfig := &handler.FormDataETPThresholdConfig{
					FixValue: 5,
					Content:  "约5分钟",
				}
				return []*mockey.Mocker{
					mockey.Mock((*Render).shouldHideETPForProduct).Return(false).Build(),
					mockey.Mock(dcmp.GetDcmpPlainContent).Return(`{
						"less_than_threshold": [{"fix_value": 5, "content": "约5分钟"}],
						"expect_threshold": 300,
						"queue_threshold": 3
					}`).Build(),
					mockey.Mock((*Render).getLackBSceneContent).Return("").Build(),
					mockey.Mock(handler.GetETPThresholdConfig).Return(mockConfig).Build(),
				}
			},
			etp:           300,
			sceneFlag:     1,
			queueLen:      5,
			pcID:          1001,
			expectValue:   5,
			expectContent: "约5分钟",
			expectEmpty:   false,
		},
		{
			name: "超时无排队场景",
			setupRender: func() *Render {
				return &Render{service: &Service{}}
			},
			setupMocks: func() []*mockey.Mocker {
				return []*mockey.Mocker{
					mockey.Mock((*Render).shouldHideETPForProduct).Return(false).Build(),
					mockey.Mock(dcmp.GetDcmpPlainContent).Return(`{
						"expect_threshold": 300,
						"queue_threshold": 3,
						"over_expect_threshold_no_queue_content": "较长时间"
					}`).Build(),
					mockey.Mock((*Render).getLackBSceneContent).Return("").Build(),
					mockey.Mock(handler.GetETPThresholdConfig).Return(nil).Build(),
				}
			},
			etp:           500, // 超过阈值
			sceneFlag:     0,   // 无场景标识
			queueLen:      2,   // 排队少
			pcID:          1001,
			expectValue:   500,
			expectContent: "较长时间",
			expectEmpty:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			render := tt.setupRender()
			var mocks []*mockey.Mocker
			if tt.setupMocks != nil {
				mocks = tt.setupMocks()
			}

			value, content := render.buildProductExpectETPContent(ctx, tt.etp, tt.sceneFlag, tt.queueLen, tt.pcID)

			if tt.expectEmpty {
				assert.Equal(t, int32(0), value, "值应为0")
				assert.Equal(t, "", content, "内容应为空")
			} else {
				assert.Equal(t, tt.expectValue, value, "返回值应匹配")
				assert.Equal(t, tt.expectContent, content, "返回内容应匹配")
			}

			// 清理mocks
			for _, m := range mocks {
				if m != nil {
					m.UnPatch()
				}
			}
		})
	}
}

// 测试shouldHideETPForProduct方法
func TestShouldHideETPForProduct(t *testing.T) {
	defer mockey.UnPatchAll()

	tests := []struct {
		name        string
		setupRender func() *Render
		setupMocks  func() []*mockey.Mocker
		pcID        int32
		expectHide  bool
	}{
		{
			name: "service为nil时返回false",
			setupRender: func() *Render {
				return &Render{service: nil}
			},
			setupMocks: func() []*mockey.Mocker { return nil },
			pcID:       1001,
			expectHide: false,
		},
		{
			name: "Cfg为nil时返回false",
			setupRender: func() *Render {
				return &Render{
					service: &Service{Cfg: nil},
				}
			},
			setupMocks: func() []*mockey.Mocker { return nil },
			pcID:       1001,
			expectHide: false,
		},
		{
			name: "配置允许显示ETP时返回false",
			setupRender: func() *Render {
				mockCfg := &Apollo.Config{}
				return &Render{
					service: &Service{Cfg: mockCfg},
				}
			},
			setupMocks: func() []*mockey.Mocker {
				return []*mockey.Mocker{
					mockey.Mock((*Apollo.Config).FeatureInConfigArray).Return(false).Build(),
				}
			},
			pcID:       1001,
			expectHide: false,
		},
		{
			name: "配置不允许显示ETP时返回true",
			setupRender: func() *Render {
				mockCfg := &Apollo.Config{}
				return &Render{
					service: &Service{Cfg: mockCfg},
				}
			},
			setupMocks: func() []*mockey.Mocker {
				return []*mockey.Mocker{
					mockey.Mock((*Apollo.Config).FeatureInConfigArray).Return(true).Build(),
				}
			},
			pcID:       1001,
			expectHide: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			render := tt.setupRender()
			var mocks []*mockey.Mocker
			if tt.setupMocks != nil {
				mocks = tt.setupMocks()
			}

			result := render.shouldHideETPForProduct(tt.pcID)

			assert.Equal(t, tt.expectHide, result, "隐藏ETP的判断应匹配")

			// 清理mocks
			for _, m := range mocks {
				if m != nil {
					m.UnPatch()
				}
			}
		})
	}
}

// 测试shouldHideETPForBox方法
func TestShouldHideETPForBox(t *testing.T) {
	defer mockey.UnPatchAll()

	tests := []struct {
		name        string
		setupRender func() *Render
		setupMocks  func() []*mockey.Mocker
		groupId     string
		expectHide  bool
	}{
		{
			name: "service为nil时返回false",
			setupRender: func() *Render {
				return &Render{service: nil}
			},
			setupMocks: func() []*mockey.Mocker { return nil },
			groupId:    "box1",
			expectHide: false,
		},
		{
			name: "Cfg为nil时返回false",
			setupRender: func() *Render {
				return &Render{
					service: &Service{Cfg: nil},
				}
			},
			setupMocks: func() []*mockey.Mocker { return nil },
			groupId:    "box1",
			expectHide: false,
		},
		{
			name: "配置允许显示ETP时返回false",
			setupRender: func() *Render {
				mockCfg := &Apollo.Config{}
				return &Render{
					service: &Service{Cfg: mockCfg},
				}
			},
			setupMocks: func() []*mockey.Mocker {
				return []*mockey.Mocker{
					mockey.Mock((*Apollo.Config).FeatureInConfigArray).Return(false).Build(),
				}
			},
			groupId:    "box1",
			expectHide: false,
		},
		{
			name: "配置不允许显示ETP时返回true",
			setupRender: func() *Render {
				mockCfg := &Apollo.Config{}
				return &Render{
					service: &Service{Cfg: mockCfg},
				}
			},
			setupMocks: func() []*mockey.Mocker {
				return []*mockey.Mocker{
					mockey.Mock((*Apollo.Config).FeatureInConfigArray).Return(true).Build(),
				}
			},
			groupId:    "box1",
			expectHide: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			render := tt.setupRender()
			var mocks []*mockey.Mocker
			if tt.setupMocks != nil {
				mocks = tt.setupMocks()
			}

			result := render.shouldHideETPForBox(tt.groupId)

			assert.Equal(t, tt.expectHide, result, "隐藏ETP的判断应匹配")

			// 清理mocks
			for _, m := range mocks {
				if m != nil {
					m.UnPatch()
				}
			}
		})
	}
}

// 测试getLackBSceneContent方法
func TestGetLackBSceneContent(t *testing.T) {
	defer mockey.UnPatchAll()

	ctx := context.Background()

	tests := []struct {
		name          string
		setupRender   func() *Render
		setupMocks    func() []*mockey.Mocker
		etp           int32
		sceneFlag     int32
		etpConfig     FormDataETPConfig
		expectContent string
	}{
		{
			name: "缺B场景未启用时返回空字符串",
			setupRender: func() *Render {
				return &Render{service: &Service{}}
			},
			setupMocks: func() []*mockey.Mocker {
				return []*mockey.Mocker{
					mockey.Mock((*Render).isLackBSceneEnabled).Return(false).Build(),
				}
			},
			etp:           450,
			sceneFlag:     0,
			etpConfig:     FormDataETPConfig{},
			expectContent: "",
		},
		{
			name: "ETP在360-600区间时返回对应内容",
			setupRender: func() *Render {
				return &Render{service: &Service{}}
			},
			setupMocks: func() []*mockey.Mocker {
				return []*mockey.Mocker{
					mockey.Mock((*Render).isLackBSceneEnabled).Return(true).Build(),
				}
			},
			etp:       450,
			sceneFlag: 0,
			etpConfig: FormDataETPConfig{
				LackB360To600: "预计6~10分钟",
				LackBOver600:  "预计10分钟以上",
			},
			expectContent: "预计6~10分钟",
		},
		{
			name: "ETP大于600时返回对应内容",
			setupRender: func() *Render {
				return &Render{service: &Service{}}
			},
			setupMocks: func() []*mockey.Mocker {
				return []*mockey.Mocker{
					mockey.Mock((*Render).isLackBSceneEnabled).Return(true).Build(),
				}
			},
			etp:       700,
			sceneFlag: 0,
			etpConfig: FormDataETPConfig{
				LackB360To600: "预计6~10分钟",
				LackBOver600:  "预计10分钟以上",
			},
			expectContent: "预计10分钟以上",
		},
		{
			name: "ETP小于等于360时返回空字符串",
			setupRender: func() *Render {
				return &Render{service: &Service{}}
			},
			setupMocks: func() []*mockey.Mocker {
				return []*mockey.Mocker{
					mockey.Mock((*Render).isLackBSceneEnabled).Return(true).Build(),
				}
			},
			etp:       300,
			sceneFlag: 0,
			etpConfig: FormDataETPConfig{
				LackB360To600: "预计6~10分钟",
				LackBOver600:  "预计10分钟以上",
			},
			expectContent: "",
		},
		{
			name: "边界值ETP=360时返回空字符串",
			setupRender: func() *Render {
				return &Render{service: &Service{}}
			},
			setupMocks: func() []*mockey.Mocker {
				return []*mockey.Mocker{
					mockey.Mock((*Render).isLackBSceneEnabled).Return(true).Build(),
				}
			},
			etp:       360,
			sceneFlag: 0,
			etpConfig: FormDataETPConfig{
				LackB360To600: "预计6~10分钟",
				LackBOver600:  "预计10分钟以上",
			},
			expectContent: "",
		},
		{
			name: "边界值ETP=600时返回360-600区间内容",
			setupRender: func() *Render {
				return &Render{service: &Service{}}
			},
			setupMocks: func() []*mockey.Mocker {
				return []*mockey.Mocker{
					mockey.Mock((*Render).isLackBSceneEnabled).Return(true).Build(),
				}
			},
			etp:       600,
			sceneFlag: 0,
			etpConfig: FormDataETPConfig{
				LackB360To600: "预计6~10分钟",
				LackBOver600:  "预计10分钟以上",
			},
			expectContent: "预计6~10分钟",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			render := tt.setupRender()
			var mocks []*mockey.Mocker
			if tt.setupMocks != nil {
				mocks = tt.setupMocks()
			}

			result := render.getLackBSceneContent(ctx, tt.etp, tt.sceneFlag, tt.etpConfig)

			assert.Equal(t, tt.expectContent, result, "返回的内容应匹配")

			// 清理mocks
			for _, m := range mocks {
				if m != nil {
					m.UnPatch()
				}
			}
		})
	}
}

// 测试isLackBSceneEnabled方法
func TestIsLackBSceneEnabled(t *testing.T) {
	defer mockey.UnPatchAll()

	tests := []struct {
		name          string
		setupRender   func() *Render
		setupMocks    func() []*mockey.Mocker
		expectEnabled bool
	}{
		{
			name: "service为nil时返回false",
			setupRender: func() *Render {
				return &Render{service: nil}
			},
			setupMocks:    func() []*mockey.Mocker { return nil },
			expectEnabled: false,
		},
		{
			name: "Cfg为nil时返回false",
			setupRender: func() *Render {
				return &Render{
					service: &Service{Cfg: nil},
				}
			},
			setupMocks:    func() []*mockey.Mocker { return nil },
			expectEnabled: false,
		},
		{
			name: "配置启用缺B场景时返回true",
			setupRender: func() *Render {
				mockCfg := &Apollo.Config{}
				return &Render{
					service: &Service{Cfg: mockCfg},
				}
			},
			setupMocks: func() []*mockey.Mocker {
				return []*mockey.Mocker{
					mockey.Mock((*Apollo.Config).FeatureConfigAllow).Return(true).Build(),
				}
			},
			expectEnabled: true,
		},
		{
			name: "配置未启用缺B场景时返回false",
			setupRender: func() *Render {
				mockCfg := &Apollo.Config{}
				return &Render{
					service: &Service{Cfg: mockCfg},
				}
			},
			setupMocks: func() []*mockey.Mocker {
				return []*mockey.Mocker{
					mockey.Mock((*Apollo.Config).FeatureConfigAllow).Return(false).Build(),
				}
			},
			expectEnabled: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			render := tt.setupRender()
			var mocks []*mockey.Mocker
			if tt.setupMocks != nil {
				mocks = tt.setupMocks()
			}

			result := render.isLackBSceneEnabled()

			assert.Equal(t, tt.expectEnabled, result, "缺B场景启用状态应匹配")

			// 清理mocks
			for _, m := range mocks {
				if m != nil {
					m.UnPatch()
				}
			}
		})
	}
}

// 测试getProductExpectInfo方法的新增逻辑
func TestGetProductExpectInfo_GlobalSceneExpect(t *testing.T) {
	defer mockey.UnPatchAll()

	ctx := context.Background()

	tests := []struct {
		name        string
		setupRender func() *Render
		setupMocks  func() []*mockey.Mocker
		expectEmpty bool
	}{
		{
			name: "缺B场景启用且全局场景标识为1时返回空map",
			setupRender: func() *Render {
				return &Render{
					service: &Service{
						req: &proto.PGetFormRealDataReq{
							Lang:      consts.LangZhCN,
							OrderType: nil,
						},
						AthenaExpectInfoResult: AthenaExpectInfoResult{
							ProductExpectInfo: &AthenaApiv3.ProductExpectInfo{
								ProductInfos: []*AthenaApiv3.ProductExpectInfoItem{
									{
										ProductCategory: util.Int32Ptr(1001),
										ExpectInfo: &AthenaApiv3.ExpectInfo{
											Ets: util.Int32Ptr(300),
											Etp: util.Int32Ptr(300),
										},
									},
								},
							},
							GlobalSceneExpect: &AthenaApiv3.GlobalExpectInfo{
								GlobalSceneFlag: util.Int32Ptr(1),
							},
						},
						IsToEtp: true,
					},
				}
			},
			setupMocks: func() []*mockey.Mocker {
				mocks := setupCommonMocks()
				mocks = append(mocks, mockey.Mock((*Render).isLackBSceneEnabled).Return(true).Build())
				return mocks
			},
			expectEmpty: true,
		},
		{
			name: "缺B场景启用但全局场景标识不为1时正常返回",
			setupRender: func() *Render {
				return &Render{
					service: &Service{
						req: &proto.PGetFormRealDataReq{
							Lang:      consts.LangZhCN,
							OrderType: nil,
						},
						AthenaExpectInfoResult: AthenaExpectInfoResult{
							ProductExpectInfo: &AthenaApiv3.ProductExpectInfo{
								ProductInfos: []*AthenaApiv3.ProductExpectInfoItem{
									{
										ProductCategory: util.Int32Ptr(1001),
										ExpectInfo: &AthenaApiv3.ExpectInfo{
											Ets: util.Int32Ptr(300),
											Etp: util.Int32Ptr(300),
										},
									},
								},
							},
							GlobalSceneExpect: &AthenaApiv3.GlobalExpectInfo{
								GlobalSceneFlag: util.Int32Ptr(0),
							},
						},
						IsToEtp: true,
					},
				}
			},
			setupMocks: func() []*mockey.Mocker {
				mocks := setupCommonMocks()
				mocks = append(mocks, mockey.Mock((*Render).isLackBSceneEnabled).Return(true).Build())
				mocks = append(mocks, mockey.Mock((*Render).buildProductExpectETPContent).Return(int32(5), "约5分钟").Build())
				return mocks
			},
			expectEmpty: false,
		},
		{
			name: "缺B场景未启用时正常返回",
			setupRender: func() *Render {
				return &Render{
					service: &Service{
						req: &proto.PGetFormRealDataReq{
							Lang:      consts.LangZhCN,
							OrderType: nil,
						},
						AthenaExpectInfoResult: AthenaExpectInfoResult{
							ProductExpectInfo: &AthenaApiv3.ProductExpectInfo{
								ProductInfos: []*AthenaApiv3.ProductExpectInfoItem{
									{
										ProductCategory: util.Int32Ptr(1001),
										ExpectInfo: &AthenaApiv3.ExpectInfo{
											Ets: util.Int32Ptr(300),
											Etp: util.Int32Ptr(300),
										},
									},
								},
							},
							GlobalSceneExpect: &AthenaApiv3.GlobalExpectInfo{
								GlobalSceneFlag: util.Int32Ptr(1),
							},
						},
						IsToEtp: true,
					},
				}
			},
			setupMocks: func() []*mockey.Mocker {
				mocks := setupCommonMocks()
				mocks = append(mocks, mockey.Mock((*Render).isLackBSceneEnabled).Return(false).Build())
				mocks = append(mocks, mockey.Mock((*Render).buildProductExpectETPContent).Return(int32(5), "约5分钟").Build())
				return mocks
			},
			expectEmpty: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			render := tt.setupRender()
			var mocks []*mockey.Mocker
			if tt.setupMocks != nil {
				mocks = tt.setupMocks()
			}

			result := render.getProductExpectInfo(ctx)

			if tt.expectEmpty {
				assert.Equal(t, 0, len(result), "应返回空map")
			} else {
				assert.Greater(t, len(result), 0, "应返回非空map")
			}

			// 清理mocks
			for _, m := range mocks {
				if m != nil {
					m.UnPatch()
				}
			}
		})
	}
}

// 测试Render方法 - 新增的测试用例
func TestRender(t *testing.T) {
	defer mockey.PatchConvey("测试Render方法", t, func() {
		ctx := context.Background()

		// 设置通用mocks
		mocks := setupCommonMocks()
		defer func() {
			for _, mock := range mocks {
				mock.UnPatch()
			}
		}()

		t.Run("service为nil时返回错误", func(t *testing.T) {
			render := &Render{service: nil}

			result, err := render.Render(ctx)

			assert.Nil(t, result, "结果应该为nil")
			assert.NotNil(t, err, "应该返回错误")
		})

		t.Run("req为nil时返回错误", func(t *testing.T) {
			service := &Service{req: nil}
			render := &Render{service: service}

			result, err := render.Render(ctx)

			assert.Nil(t, result, "结果应该为nil")
			assert.NotNil(t, err, "应该返回错误")
		})

		t.Run("SourceTypeNormalTab正常渲染", func(t *testing.T) {
			req := &proto.PGetFormRealDataReq{
				SourceType: util.Int32Ptr(consts.SourceTypeNormalTab),
			}
			service := &Service{req: req}
			render := &Render{service: service}

			// Mock render方法
			mockRenderSourceTypeNormalTab := mockey.Mock((*Render).RenderSourceTypeNormalTab).Return(&proto.FormRealData{
				FilterTitle: "测试标题",
			}).Build()
			defer mockRenderSourceTypeNormalTab.UnPatch()

			result, err := render.Render(ctx)

			assert.Nil(t, err, "不应该返回错误")
			assert.NotNil(t, result, "结果不应该为nil")
			assert.Equal(t, "测试标题", result.FilterTitle, "FilterTitle应该正确设置")
		})

		t.Run("SourceTypeClassifyTab正常渲染", func(t *testing.T) {
			req := &proto.PGetFormRealDataReq{
				SourceType: util.Int32Ptr(consts.SourceTypeClassifyTab),
			}
			service := &Service{req: req}
			render := &Render{service: service}

			// Mock render方法
			mockRenderSourceTypeClassifyTab := mockey.Mock((*Render).RenderSourceTypeClassifyTab).Return(&proto.FormRealData{
				FilterTitle: "分类标题",
			}).Build()
			defer mockRenderSourceTypeClassifyTab.UnPatch()

			result, err := render.Render(ctx)

			assert.Nil(t, err, "不应该返回错误")
			assert.NotNil(t, result, "结果不应该为nil")
			assert.Equal(t, "分类标题", result.FilterTitle, "FilterTitle应该正确设置")
		})

		t.Run("SourceTypeAnyCarEstimate正常渲染", func(t *testing.T) {
			req := &proto.PGetFormRealDataReq{
				SourceType: util.Int32Ptr(consts.SourceTypeAnyCarEstimate),
			}
			service := &Service{req: req}
			render := &Render{service: service}

			// Mock render方法
			mockRenderSourceTypeAnyCarEstimate := mockey.Mock((*Render).RenderSourceTypeAnyCarEstimate).Return(&proto.FormRealData{
				MatchCardTitle: util.StringPtr("任意车型标题"),
			}).Build()
			defer mockRenderSourceTypeAnyCarEstimate.UnPatch()

			result, err := render.Render(ctx)

			assert.Nil(t, err, "不应该返回错误")
			assert.NotNil(t, result, "结果不应该为nil")
			assert.Equal(t, "任意车型标题", *result.MatchCardTitle, "MatchCardTitle应该正确设置")
		})

		t.Run("默认情况渲染", func(t *testing.T) {
			req := &proto.PGetFormRealDataReq{
				SourceType: util.Int32Ptr(999), // 未知的source type
			}
			service := &Service{req: req}
			render := &Render{service: service}

			// Mock render方法
			mockRenderDefault := mockey.Mock((*Render).RenderDefault).Return(&proto.FormRealData{
				FilterTitle: "默认标题",
			}).Build()
			defer mockRenderDefault.UnPatch()

			result, err := render.Render(ctx)

			assert.Nil(t, err, "不应该返回错误")
			assert.NotNil(t, result, "结果不应该为nil")
			assert.Equal(t, "默认标题", result.FilterTitle, "FilterTitle应该正确设置")
		})
	})
}

// 测试RenderSourceTypeClassifyTab的BoxEtpInfo赋值 - 这是diff中的新增功能
func TestRenderSourceTypeClassifyTab_BoxEtpInfo(t *testing.T) {
	defer mockey.PatchConvey("测试RenderSourceTypeClassifyTab的BoxEtpInfo赋值", t, func() {
		ctx := context.Background()

		// 设置通用mocks
		mocks := setupCommonMocks()
		defer func() {
			for _, mock := range mocks {
				mock.UnPatch()
			}
		}()

		service := &Service{}
		render := NewRender(service)
		data := &proto.FormRealData{}

		// Mock 相关方法
		mockGetClassifyTabFilterTitle := mockey.Mock((*Service).getClassifyTabFilterTitle).Return("分类标题").Build()
		defer mockGetClassifyTabFilterTitle.UnPatch()

		mockGetFromHeadRule := mockey.Mock((*Service).getFromHeadRule).Return(&proto.FormHeadRule{}, int32(1)).Build()
		defer mockGetFromHeadRule.UnPatch()

		mockGetBargainRangeAnswerRateInfoByBubble := mockey.Mock((*Service).getBargainRangeAnswerRateInfoByBubble).Return(&proto.BargainRangeAnswerRateInfo{}).Build()
		defer mockGetBargainRangeAnswerRateInfoByBubble.UnPatch()

		productInfo := map[string]*proto.FormRealProductInfo{
			"1": {ProductExpectText: util.StringPtr("测试产品"), ProductExpectValue: util.Int32Ptr(60)},
		}
		mockGetProductExpectInfo := mockey.Mock((*Render).getProductExpectInfo).Return(productInfo).Build()
		defer mockGetProductExpectInfo.UnPatch()

		boxInfo := map[string]*proto.FormRealProductInfo{
			"box1": {ProductExpectText: util.StringPtr("测试box"), ProductExpectValue: util.Int32Ptr(50)},
		}
		mockGetBoxExpectInfo := mockey.Mock((*Render).getBoxExpectInfo).Return(boxInfo).Build()
		defer mockGetBoxExpectInfo.UnPatch()

		t.Run("正确设置BoxEtpInfo", func(t *testing.T) {
			result := render.RenderSourceTypeClassifyTab(ctx, data)

			assert.NotNil(t, result.BoxEtpInfo, "BoxEtpInfo不应该为nil")
			assert.Equal(t, boxInfo, result.BoxEtpInfo, "BoxEtpInfo应该正确设置")
		})
	})
}

// 测试其他render方法的基本功能
func TestRenderMethods(t *testing.T) {
	defer mockey.PatchConvey("测试各种render方法", t, func() {
		ctx := context.Background()

		// 设置通用mocks
		mocks := setupCommonMocks()
		defer func() {
			for _, mock := range mocks {
				mock.UnPatch()
			}
		}()

		service := &Service{}
		render := NewRender(service)
		data := &proto.FormRealData{}

		t.Run("RenderSourceTypeAnyCarEstimate", func(t *testing.T) {
			// Mock service方法
			mockBuildDiffPriceTag := mockey.Mock((*Service).buildDiffPriceTag).Return(&proto.PriceDiffInfo{Text: "价格差异"}).Build()
			defer mockBuildDiffPriceTag.UnPatch()

			mockBuildAppendMatchCardTitle := mockey.Mock((*Service).buildAppendMatchCardTitle).Return(util.StringPtr("匹配卡片标题"), util.StringPtr("icon_url")).Build()
			defer mockBuildAppendMatchCardTitle.UnPatch()

			mockGetFromHeadRule := mockey.Mock((*Service).getFromHeadRule).Return(&proto.FormHeadRule{}, int32(1)).Build()
			defer mockGetFromHeadRule.UnPatch()

			mockGetRecBubble := mockey.Mock((*Service).getRecBubble).Return(&proto.RecBubble{}).Build()
			defer mockGetRecBubble.UnPatch()

			result := render.RenderSourceTypeAnyCarEstimate(ctx, data)

			assert.NotNil(t, result.PriceDiffInfo, "PriceDiffInfo应该被设置")
			assert.Equal(t, "匹配卡片标题", *result.MatchCardTitle, "MatchCardTitle应该正确设置")
		})

		t.Run("RenderSourceTypeAnyCarRecBox", func(t *testing.T) {
			// Mock service方法
			mockBuildDiffPriceTag := mockey.Mock((*Service).buildDiffPriceTag).Return(&proto.PriceDiffInfo{Text: "价格差异"}).Build()
			defer mockBuildDiffPriceTag.UnPatch()

			mockDcmpGetJSONContentWithPath := mockey.Mock(dcmp.GetJSONContentWithPath).Return("bubble_icon").Build()
			defer mockDcmpGetJSONContentWithPath.UnPatch()

			mockBuildBoxRecMatchCardTitle := mockey.Mock((*Service).buildBoxRecMatchCardTitle).Return(util.StringPtr("box推荐标题")).Build()
			defer mockBuildBoxRecMatchCardTitle.UnPatch()

			mockGetFromHeadRule := mockey.Mock((*Service).getFromHeadRule).Return(&proto.FormHeadRule{}, int32(1)).Build()
			defer mockGetFromHeadRule.UnPatch()

			mockGetRecBubble := mockey.Mock((*Service).getRecBubble).Return(&proto.RecBubble{}).Build()
			defer mockGetRecBubble.UnPatch()

			result := render.RenderSourceTypeAnyCarRecBox(ctx, data)

			assert.Equal(t, "box推荐标题", *result.MatchCardTitle, "MatchCardTitle应该正确设置")
		})

		t.Run("RenderSourceTypeAnyCarRecBoxSingle", func(t *testing.T) {
			mockAppendNormalCompensationInfo := mockey.Mock((*Service).appendNormalCompensationInfo).Return(util.StringPtr("单一车型标题")).Build()
			defer mockAppendNormalCompensationInfo.UnPatch()

			mockGetFromHeadRule := mockey.Mock((*Service).getFromHeadRule).Return(&proto.FormHeadRule{}, int32(1)).Build()
			defer mockGetFromHeadRule.UnPatch()

			mockGetRecBubble := mockey.Mock((*Service).getRecBubble).Return(&proto.RecBubble{}).Build()
			defer mockGetRecBubble.UnPatch()

			result := render.RenderSourceTypeAnyCarRecBoxSingle(ctx, data)

			assert.Equal(t, "单一车型标题", *result.MatchCardTitle, "MatchCardTitle应该正确设置")
		})

		t.Run("RenderSourceTypeOneStop85", func(t *testing.T) {
			mockBuildEstimateDurationInfo := mockey.Mock((*Service).buildEstimateDurationInfo).Return(util.StringPtr("估计时长")).Build()
			defer mockBuildEstimateDurationInfo.UnPatch()

			mockGetProductExpectInfo := mockey.Mock((*Render).getProductExpectInfo).Return(map[string]*proto.FormRealProductInfo{}).Build()
			defer mockGetProductExpectInfo.UnPatch()

			result := render.RenderSourceTypeOneStop85(ctx, data)

			assert.NotNil(t, result.EstimateDurationInfo, "EstimateDurationInfo应该被设置")
			assert.NotNil(t, result.ProductInfo, "ProductInfo应该被设置")
		})

		t.Run("RenderSourceTypeBargainRangeBubble", func(t *testing.T) {
			mockGetBargainRangeAnswerRateInfoByBubble := mockey.Mock((*Service).getBargainRangeAnswerRateInfoByBubble).Return(&proto.BargainRangeAnswerRateInfo{}).Build()
			defer mockGetBargainRangeAnswerRateInfoByBubble.UnPatch()

			result := render.RenderSourceTypeBargainRangeBubble(ctx, data)

			assert.NotNil(t, result.BargainRangeAnswerRateInfo, "BargainRangeAnswerRateInfo应该被设置")
		})

		t.Run("RenderSourceTypeBargainRangeAnyCar", func(t *testing.T) {
			mockGetBargainRangeAnswerRateInfoByAnycar := mockey.Mock((*Service).getBargainRangeAnswerRateInfoByAnycar).Return(&proto.BargainRangeAnswerRateInfo{}).Build()
			defer mockGetBargainRangeAnswerRateInfoByAnycar.UnPatch()

			result := render.RenderSourceTypeBargainRangeAnyCar(ctx, data)

			assert.NotNil(t, result.BargainRangeAnswerRateInfo, "BargainRangeAnswerRateInfo应该被设置")
		})

		t.Run("RenderDefault", func(t *testing.T) {
			mockGetDefaultTitle := mockey.Mock((*Render).getDefaultTitle).Return("默认标题").Build()
			defer mockGetDefaultTitle.UnPatch()

			result := render.RenderDefault(ctx, data)

			assert.Equal(t, "默认标题", result.FilterTitle, "FilterTitle应该正确设置")
		})
	})
}

// 测试getProductExpectInfo的全局缺B场景处理 - 这是diff中的新增功能
func TestGetProductExpectInfo_LackBScene(t *testing.T) {
	defer mockey.PatchConvey("测试getProductExpectInfo的全局缺B场景处理", t, func() {
		ctx := context.Background()

		// 设置通用mocks
		mocks := setupCommonMocks()
		defer func() {
			for _, mock := range mocks {
				mock.UnPatch()
			}
		}()

		t.Run("缺B场景启用且全局场景标识为1时返回空map", func(t *testing.T) {
			service := &Service{
				req: &proto.PGetFormRealDataReq{
					Lang: consts.LangZhCN,
				},
				AthenaExpectInfoResult: AthenaExpectInfoResult{
					GlobalSceneExpect: &AthenaApiv3.GlobalExpectInfo{
						GlobalSceneFlag: util.Int32Ptr(1),
					},
					ProductExpectInfo: &AthenaApiv3.ProductExpectInfo{
						ProductInfos: []*AthenaApiv3.ProductExpectInfoItem{
							{
								ProductCategory: util.Int32Ptr(1),
								ExpectInfo: &AthenaApiv3.ExpectInfo{
									Ets: util.Int32Ptr(100),
									Etp: util.Int32Ptr(120),
								},
							},
						},
					},
				},
			}
			render := NewRender(service)

			// Mock isLackBSceneEnabled返回true
			mockIsLackBSceneEnabled := mockey.Mock((*Render).isLackBSceneEnabled).Return(true).Build()
			defer mockIsLackBSceneEnabled.UnPatch()

			result := render.getProductExpectInfo(ctx)

			assert.NotNil(t, result, "结果不应该为nil")
			assert.Empty(t, result, "结果应该为空map")
		})

		t.Run("缺B场景启用但全局场景标识不为1时正常返回", func(t *testing.T) {
			service := &Service{
				req: &proto.PGetFormRealDataReq{
					Lang: consts.LangZhCN,
				},
				AthenaExpectInfoResult: AthenaExpectInfoResult{
					GlobalSceneExpect: &AthenaApiv3.GlobalExpectInfo{
						GlobalSceneFlag: util.Int32Ptr(0),
					},
					ProductExpectInfo: &AthenaApiv3.ProductExpectInfo{
						ProductInfos: []*AthenaApiv3.ProductExpectInfoItem{
							{
								ProductCategory: util.Int32Ptr(1),
								ExpectInfo: &AthenaApiv3.ExpectInfo{
									Ets: util.Int32Ptr(100),
									Etp: util.Int32Ptr(120),
								},
							},
						},
					},
				},
				IsToEtp: false,
			}
			render := NewRender(service)

			// Mock相关方法
			mockIsLackBSceneEnabled := mockey.Mock((*Render).isLackBSceneEnabled).Return(true).Build()
			defer mockIsLackBSceneEnabled.UnPatch()

			mockBuildProductExpectContent := mockey.Mock(buildProductExpectContent).Return("预期内容").Build()
			defer mockBuildProductExpectContent.UnPatch()

			result := render.getProductExpectInfo(ctx)

			assert.NotNil(t, result, "结果不应该为nil")
			assert.NotEmpty(t, result, "结果不应该为空")
			assert.Contains(t, result, "1", "应该包含产品ID为1的信息")
		})

		t.Run("缺B场景未启用时正常返回", func(t *testing.T) {
			service := &Service{
				req: &proto.PGetFormRealDataReq{
					Lang: consts.LangZhCN,
				},
				AthenaExpectInfoResult: AthenaExpectInfoResult{
					GlobalSceneExpect: &AthenaApiv3.GlobalExpectInfo{
						GlobalSceneFlag: util.Int32Ptr(1),
					},
					ProductExpectInfo: &AthenaApiv3.ProductExpectInfo{
						ProductInfos: []*AthenaApiv3.ProductExpectInfoItem{
							{
								ProductCategory: util.Int32Ptr(1),
								ExpectInfo: &AthenaApiv3.ExpectInfo{
									Ets: util.Int32Ptr(100),
									Etp: util.Int32Ptr(120),
								},
							},
						},
					},
				},
				IsToEtp: false,
			}
			render := NewRender(service)

			// Mock isLackBSceneEnabled返回false
			mockIsLackBSceneEnabled := mockey.Mock((*Render).isLackBSceneEnabled).Return(false).Build()
			defer mockIsLackBSceneEnabled.UnPatch()

			mockBuildProductExpectContent := mockey.Mock(buildProductExpectContent).Return("预期内容").Build()
			defer mockBuildProductExpectContent.UnPatch()

			result := render.getProductExpectInfo(ctx)

			assert.NotNil(t, result, "结果不应该为nil")
			assert.NotEmpty(t, result, "结果不应该为空")
			assert.Contains(t, result, "1", "应该包含产品ID为1的信息")
		})
	})
}

// 测试isNewFilterTitle和getDefaultTitle方法
func TestTitleMethods(t *testing.T) {
	defer mockey.PatchConvey("测试标题相关方法", t, func() {
		ctx := context.Background()

		// 设置通用mocks
		mocks := setupCommonMocks()
		defer func() {
			for _, mock := range mocks {
				mock.UnPatch()
			}
		}()

		t.Run("isNewFilterTitle - apollo返回允许", func(t *testing.T) {
			service := &Service{
				req: &proto.PGetFormRealDataReq{
					Appversion:  "1.0.0",
					AccessKeyId: 123,
				},
				UserInfo: &passport.UserInfo{
					Phone: "13800138000",
				},
			}
			render := NewRender(service)

			// Mock apollo相关方法
			mockFeatureToggle := mockey.Mock(apollo.FeatureToggle).Return(&model.ToggleResult{}, nil).Build()
			defer mockFeatureToggle.UnPatch()

			mockIsAllow := mockey.Mock((*model.ToggleResult).IsAllow).Return(true).Build()
			defer mockIsAllow.UnPatch()

			result := render.isNewFilterTitle(ctx)

			assert.True(t, result, "应该返回true")
		})

		t.Run("isNewFilterTitle - apollo返回不允许", func(t *testing.T) {
			service := &Service{
				req: &proto.PGetFormRealDataReq{
					Appversion:  "1.0.0",
					AccessKeyId: 123,
				},
				UserInfo: &passport.UserInfo{
					Phone: "13800138000",
				},
			}
			render := NewRender(service)

			// Mock apollo相关方法
			mockFeatureToggle := mockey.Mock(apollo.FeatureToggle).Return(&model.ToggleResult{}, nil).Build()
			defer mockFeatureToggle.UnPatch()

			mockIsAllow := mockey.Mock((*model.ToggleResult).IsAllow).Return(false).Build()
			defer mockIsAllow.UnPatch()

			result := render.isNewFilterTitle(ctx)

			assert.False(t, result, "应该返回false")
		})

		t.Run("isNewFilterTitle - apollo返回错误", func(t *testing.T) {
			service := &Service{
				req: &proto.PGetFormRealDataReq{
					Appversion:  "1.0.0",
					AccessKeyId: 123,
				},
				UserInfo: &passport.UserInfo{
					Phone: "13800138000",
				},
			}
			render := NewRender(service)

			// Mock apollo返回错误
			mockFeatureToggle := mockey.Mock(apollo.FeatureToggle).Return(nil, errors.New("apollo error")).Build()
			defer mockFeatureToggle.UnPatch()

			result := render.isNewFilterTitle(ctx)

			assert.False(t, result, "发生错误时应该返回false")
		})

		t.Run("getDefaultTitle - 正常返回标题", func(t *testing.T) {
			service := &Service{
				req: &proto.PGetFormRealDataReq{},
				AthenaExpectInfoResult: AthenaExpectInfoResult{
					V3GlobalSceneExpect: &AthenaApiv3.GlobalExpectInfo{},
				},
			}
			render := NewRender(service)

			// Mock handler.GetNewTitle
			mockGetNewTitle := mockey.Mock(handler.GetNewTitle).Return("新标题").Build()
			defer mockGetNewTitle.UnPatch()

			result := render.getDefaultTitle(ctx)

			assert.Equal(t, "新标题", result, "应该返回正确的标题")
		})
	})
}

// 测试logPublic函数
func TestLogPublic(t *testing.T) {
	defer mockey.PatchConvey("测试logPublic函数", t, func() {
		ctx := context.Background()

		// 设置通用mocks
		mocks := setupCommonMocks()
		defer func() {
			for _, mock := range mocks {
				mock.UnPatch()
			}
		}()

		t.Run("空的productInfoMap时直接返回", func(t *testing.T) {
			service := &Service{}
			productInfoMap := map[string]*proto.FormRealProductInfo{}

			// 这个函数应该直接返回而不做任何操作
			logPublic(ctx, service, productInfoMap)

			// 没有断言，因为函数会直接返回
		})

		t.Run("正常情况下记录日志", func(t *testing.T) {
			service := &Service{
				req: &proto.PGetFormRealDataReq{
					AccessKeyId:     123,
					FromArea:        456,
					Appversion:      "1.0.0",
					EstimateTraceId: "trace123",
				},
				UserInfo: &passport.UserInfo{
					Phone: "13800138000",
				},
				reqMultiProduct: []*MultiProduct{
					{
						EstimateID:      "est123",
						ProductCategory: 1,
					},
				},
			}
			productInfoMap := map[string]*proto.FormRealProductInfo{
				"1": {
					ProductExpectText:  util.StringPtr("60秒"),
					ProductExpectValue: util.Int32Ptr(60),
				},
			}

			// Mock相关方法 - 不需要重复mock，因为setupCommonMocks已经mock了

			// 验证log.Public.Public被调用 - 不需要重复mock，因为setupCommonMocks已经mock了

			logPublic(ctx, service, productInfoMap)

			// 由于mock了log.Public.Public，这里只验证函数能正常执行不报错即可
		})
	})
}

// 测试buildProductExpectContent函数
func TestBuildProductExpectContent(t *testing.T) {
	defer mockey.PatchConvey("测试buildProductExpectContent函数", t, func() {
		ctx := context.Background()

		// Mock dcmp.GetJSONContentWithPath
		mockGetJSONContentWithPath := mockey.Mock(dcmp.GetJSONContentWithPath).Return("测试内容").Build()
		defer mockGetJSONContentWithPath.UnPatch()

		t.Run("ets小于等于0时返回空字符串", func(t *testing.T) {
			result := buildProductExpectContent(ctx, 0, 0, 0)
			assert.Equal(t, "", result, "应该返回空字符串")

			result = buildProductExpectContent(ctx, -10, 0, 0)
			assert.Equal(t, "", result, "应该返回空字符串")
		})

		t.Run("ets在0-30秒范围内", func(t *testing.T) {
			result := buildProductExpectContent(ctx, 25, 0, 0)
			assert.Equal(t, "测试内容", result, "应该返回dcmp配置的内容")
		})

		t.Run("ets在30-60秒范围内", func(t *testing.T) {
			result := buildProductExpectContent(ctx, 45, 0, 0)
			assert.Equal(t, "测试内容", result, "应该返回dcmp配置的内容")
		})

		t.Run("ets在60-180秒范围内", func(t *testing.T) {
			result := buildProductExpectContent(ctx, 120, 0, 0)
			assert.Equal(t, "测试内容", result, "应该返回dcmp配置的内容")
		})

		t.Run("ets在180-300秒范围内", func(t *testing.T) {
			result := buildProductExpectContent(ctx, 240, 0, 0)
			assert.Equal(t, "测试内容", result, "应该返回dcmp配置的内容")
		})

		t.Run("ets在300-600秒范围内", func(t *testing.T) {
			result := buildProductExpectContent(ctx, 450, 0, 0)
			assert.Equal(t, "测试内容", result, "应该返回dcmp配置的内容")
		})

		t.Run("ets大于600秒且为排队场景无排队", func(t *testing.T) {
			result := buildProductExpectContent(ctx, 700, int32(consts.QueueSceneFlag), 0)
			assert.Equal(t, "测试内容", result, "应该返回dcmp配置的内容")
		})

		t.Run("ets大于600秒且为排队场景有排队1-200人", func(t *testing.T) {
			result := buildProductExpectContent(ctx, 700, int32(consts.QueueSceneFlag), 150)
			assert.Equal(t, "测试内容", result, "应该返回dcmp配置的内容")
		})

		t.Run("ets大于600秒且为排队场景排队超过200人", func(t *testing.T) {
			result := buildProductExpectContent(ctx, 700, int32(consts.QueueSceneFlag), 250)
			assert.Equal(t, "测试内容", result, "应该返回dcmp配置的内容")
		})

		t.Run("ets大于600秒且非排队场景", func(t *testing.T) {
			result := buildProductExpectContent(ctx, 700, 0, 0)
			assert.Equal(t, "测试内容", result, "应该返回dcmp配置的内容")
		})
	})
}
