package get_form_real_data

import (
	"context"
	"encoding/json"

	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_form_real_data/handler"
	"github.com/spf13/cast"
)

const (
	ETS          = "ets"
	ETP          = "etp"
	AnswerRate   = "answer_rate"
	DEFAULT_TEXT = "多勾更快出发"
)

func (c *Service) getClassifyTabFilterTitle(ctx context.Context) string {
	// 是大字版或者命中推荐tab或者7.1表单不返回
	if c.req.GetFontScaleType() != 0 || c.req.GetRecForm() != 0 || c.req.GetFormStyleExp() == 2 {
		return ""
	}

	return handler.GetButtonTitle(ctx, c.req, c.GlobalSceneExpect, c.IsToEtp)
}

func (c *Service) getClassifyTabPriceAxleTitle(ctx context.Context) string {
	var (
		dcmpMap map[string]string
	)

	template := dcmp.GetDcmpPlainContent(ctx, "config_text-classify_price_axle_info")
	if err := json.Unmarshal([]byte(template), &dcmpMap); err != nil {
		return DEFAULT_TEXT
	}

	if c.GlobalSceneExpect == nil {
		return ""
	}

	expectInfo := c.GlobalSceneExpect.GetExpectInfo()
	showType := c.GlobalSceneExpect.GetShowType()
	if expectInfo == nil || showType == "" {
		return ""
	}

	switch showType {
	case ETS:
		if expectInfo.Ets != nil && *expectInfo.Ets > 0 {
			ets := *expectInfo.Ets
			if ets >= 60 {
				newEts := float64(ets) / 60
				if newEts > 30 {
					return dcmpMap["ets_long_time_classify_price_axle_title"]
				} else {
					return util.ReplaceTag(ctx, dcmpMap["ets_minute_classify_price_axle_title"], map[string]string{
						"num": util.FormatPriceCeil(float64(ets)/60, 0),
					})
				}

			} else {
				return util.ReplaceTag(ctx, dcmpMap["ets_sec_classify_price_axle_title"], map[string]string{
					"num": cast.ToString(ets),
				})
			}
		}
		break
	case AnswerRate:
		if expectInfo.AnswerRate != nil && *expectInfo.AnswerRate > 0 {
			answerRate := *expectInfo.AnswerRate * 100
			return util.ReplaceTag(ctx, dcmpMap["answer_rate_classify_price_axle_title"], map[string]string{
				"num": cast.ToString(util.FormatPriceForRound(answerRate, 0)),
			})
		}
		break
	}

	return DEFAULT_TEXT

}
