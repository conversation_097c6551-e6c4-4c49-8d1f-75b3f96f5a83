package model

import (
	"context"

	Ferrari "git.xiaojukeji.com/dirpc/dirpc-go-http-Ferrari"
	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
)

type Opts func(context.Context, *ServiceRequest) NewErrors.BizError

type ServiceRequest struct {
	Provider
	UpgradeInfo

	DecodeOrderID int64

	TravelQuotation *Ferrari.TravelQuotation
}

type UpgradeInfo struct {
	UpgradeType int
	PriceItem   *Ferrari.PriceItem
}

type Provider interface {
	GetOid() string
	GetEstimateId() string
}

func NewServiceReq(provider Provider) *ServiceRequest {
	return &ServiceRequest{
		Provider: provider,
	}
}

func (s *ServiceRequest) Do(ctx context.Context, opts ...Opts) NewErrors.BizError {
	if len(opts) == 0 {
		return nil
	}

	for _, o := range opts {
		if o == nil {
			continue
		}

		err := o(ctx, s)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *ServiceRequest) GetTravelQuotation() *Ferrari.TravelQuotation {
	return s.TravelQuotation
}
