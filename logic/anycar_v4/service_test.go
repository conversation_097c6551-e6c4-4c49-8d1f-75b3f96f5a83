package anycar_v4

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/golibs/zerolog/ddlog"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestServiceRun(t *testing.T) {
	service := Service{
		req:       &proto.AnyCarEstimateV4Req{},
		OrderInfo: &dos.OrderInfo{},
	}
	mockey.Mock((*Service).AppendExtraResp).Return(&proto.NewFormEstimateResponse{
		EstimateTraceId: "123",
		EstimateData: map[int64]*proto.NewFormEstimateData{
			68: &proto.NewFormEstimateData{
				EstimateId: "123",
			},
		},
		RawEstimateData: map[int64]*proto.RawEstimateData{
			68: {EstimateId: "123",
				DiscountSet: &proto.DiscountSet{
					Coupon: &proto.EstimateCouponInfo{
						CustomTag: "123",
					},
				}},
		},
	}).Build()
	mockey.Mock((*Service).GetRenderMaterial).Return(&models.AnyCarEstimateAppendCarFormMaterial{
		AuthButton: &models.AuthButton{},
		TopButton:  &models.TopButton{},
		Button: &models.Button{
			Style: &models.Style{},
		},
	}, nil).Build()

	mockey.Mock((*ddlog.DiLogHandle).Warnf).Return().Build()
	mockey.Mock((*Service).buildFeeMsgTemplate).Return("123").Build()

	render := service.Render(context.TODO())

	assert.NotNil(t, render)

	mockey.UnPatchAll()
}
