package pbd_station_bus_multi_station_price

import (
	"context"
	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/pbd_station_bus_multi_station_price/data"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/multi_station_price"
	"git.xiaojukeji.com/nuwa/trace"
	"github.com/spf13/cast"
	"math"
)

type render struct {
}

// NewRender ...
func NewRender() *render {
	return &render{}
}

// Do ...
func (r *render) Do(ctx context.Context, productsFull []*biz_runtime.ProductInfoFull, baseReq *models.BaseReqData) (data *proto.StationBusMultiStationPriceData, e BizError.BizError) {
	data = &proto.StationBusMultiStationPriceData{}
	productsFull = filterProducts(ctx, productsFull)
	//将所有班次按照路线维度进行分组、处理站点价格信息
	routeGroup, shiftId2StationPrice := formatProducts(ctx, productsFull)
	lineSchedules := make([]*proto.LineSchedule, 0)
	for _, products := range routeGroup {
		lineSchedule := new(proto.LineSchedule)
		line, firstStationSumMinutes := multi_station_price.BuildLine(ctx, products)
		if line == nil {
			continue
		}
		lineSchedule.Line = line
		lineSchedule.Schedule = multi_station_price.BuildSchedules(products, shiftId2StationPrice, firstStationSumMinutes)
		line.Stations[0].OffsetMinutes = 0
		lineSchedules = append(lineSchedules, lineSchedule)
	}
	data.LineSchedule = lineSchedules
	data.FromCity = baseReq.AreaInfo.FromCityName
	data.ToCity = baseReq.AreaInfo.ToCityName
	return data, nil
}
func filterProducts(ctx context.Context, productsFull []*biz_runtime.ProductInfoFull) (productsFullRes []*biz_runtime.ProductInfoFull) {
	products := make([]*biz_runtime.ProductInfoFull, 0)
	for _, product := range productsFull {
		if product == nil {
			continue
		}
		prodAdapter := &data.AdapterPbdStationBus{ProductInfoFull: product}
		//将没有价格的班次进行过滤
		stationPriceData := prodAdapter.GetStationPriceData()
		if stationPriceData == nil || len(stationPriceData.StationPrice) <= 0 {
			log.Trace.Infof(ctx, trace.DLTagUndefined, "StationPrice is nil shiftId=%v", product.Product.ShiftID)
			continue
		}
		//将没有获取到路线明细的班次过滤
		routeId := prodAdapter.GetComboID()
		if prodAdapter.GetRouteBasicInfo() == nil || len(prodAdapter.GetRouteBasicInfo().StationList) <= 0 {
			log.Trace.Infof(ctx, trace.DLTagUndefined, "route detail is nil routeId=%v", routeId)
			continue
		}
		//携程强制要求最低价格>=1元，将小于1的班次直接过滤
		for _, priceInfo := range stationPriceData.StationPrice {
			if priceInfo.Price < 1 {
				log.Trace.Infof(ctx, trace.DLTagUndefined, "bottomPrice is under 1 shiftId=%v", product.Product.ShiftID)
				continue
			}
		}
		products = append(products, product)
	}
	return products
}

// 格式化数据：将所有班次按照路线维度进行分组&处理站点价格信息
func formatProducts(ctx context.Context, productsFull []*biz_runtime.ProductInfoFull) (map[int64][]*biz_runtime.ProductInfoFull, map[string]*multi_station_price.StationPriceRender) {
	routeGroup := make(map[int64][]*biz_runtime.ProductInfoFull)
	shiftId2StationPrice := make(map[string]*multi_station_price.StationPriceRender)
	for _, product := range productsFull {
		prodAdapter := &data.AdapterPbdStationBus{ProductInfoFull: product}
		//路线维度分组
		routeId := prodAdapter.GetComboID()
		routeList := routeGroup[routeId]
		if len(routeList) > 0 {
			routeList = append(routeList, product)
		} else {
			routeList = make([]*biz_runtime.ProductInfoFull, 0)
			routeList = append(routeList, product)
		}
		routeGroup[routeId] = routeList
		//将站点价格信息进行转换
		stationPriceData := prodAdapter.GetStationPriceData()
		stationPriceInfo := new(multi_station_price.StationPriceRender)
		bottomPrice := math.MaxFloat64
		for _, priceInfo := range stationPriceData.StationPrice {
			if priceInfo.Price < bottomPrice {
				bottomPrice = priceInfo.Price
			}
		}
		segmentPriceList := make([]*proto.SegmentPrice, 0)
		for _, priceInfo := range stationPriceData.StationPrice {
			if priceInfo.Price != bottomPrice {
				segmentPrice := new(proto.SegmentPrice)
				segmentPrice.Price = priceInfo.Price
				segmentPrice.FromStationCode = cast.ToString(priceInfo.From)
				segmentPrice.ToStationCode = cast.ToString(priceInfo.To)
				segmentPriceList = append(segmentPriceList, segmentPrice)
			}
		}
		stationPriceInfo.BottomPrice = bottomPrice
		stationPriceInfo.SegmentPrices = segmentPriceList
		shiftId2StationPrice[prodAdapter.GetShiftID()] = stationPriceInfo
	}
	return routeGroup, shiftId2StationPrice
}
