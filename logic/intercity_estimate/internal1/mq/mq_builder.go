package mq

import (
	"bytes"
	"context"
	"encoding/json"
	"strconv"
	"time"

	context2 "git.xiaojukeji.com/lego/context-go"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type MsgBuilder struct {
	msg *Message
}

func NewMQMsgBuilder() *MsgBuilder {
	return &MsgBuilder{
		msg: &Message{
			No:   0,
			Type: 21030,
			Ct:   time.Now().Unix(),
		},
	}
}

func (bd *MsgBuilder) SetTraceFromCtx(ctx context.Context) *MsgBuilder {
	if trace := context2.GetTrace(ctx); trace != nil {
		bd.msg.Trace = &Trace{
			TraceID:     trace.GetTraceId(),
			SpanID:      trace.GetCSpanId(),
			HintCode:    trace.GetHintCode(),
			HintContent: trace.GetHintContent(),
		}
	}
	return bd
}

func (bd *MsgBuilder) SetDataByProduct(product *biz_runtime.ProductInfoFull) *MsgBuilder {
	nilToZero := func(i *int64) int {
		if i == nil {
			return 0
		}
		return int(*i)
	}
	encodeNoCheck := func(x interface{}) string {
		var buf bytes.Buffer
		encoder := json.NewEncoder(&buf)
		_ = encoder.Encode(x)
		return buf.String()
	}

	passenger := product.GetUserInfo()
	area := product.GetAreaInfo()
	client := product.GetClientInfo()
	nTuple := product.GetNTuple()
	estimateFee := product.GetDirectEstimatePrice()
	attr := estimateFee.GetFeeAttributes()
	options := product.GetUserOption()

	bd.msg.Data = &Data{
		CreateTime: strconv.FormatInt(bd.msg.Ct, 10),

		Biztype: productIDToBizType(int(product.Product.ProductID)),

		PassengerPhone: passenger.Phone,
		UID:            passenger.UID,
		Gpid:           strconv.FormatInt(passenger.PID, 10),

		AppVersion:   client.AppVersion,
		Channel:      client.Channel,
		ClientType:   int(client.ClientType),
		PlatformType: int(client.PlatformType),
		OriginID:     int(client.OriginID),

		CurrentLng: area.CurLng,
		CurrentLat: area.CurLat,

		District: area.District,
		Area:     int(area.City),

		StartingName: area.FromName,
		StartingLng:  area.FromLng,
		StartingLat:  area.FromLat,
		County:       strconv.FormatInt(int64(area.FromCounty), 10),
		FromPoiID:    area.FromPoiID,

		DestLng:  area.ToLng,
		DestLat:  area.ToLat,
		DestName: area.ToName,
		ToPoiID:  area.ToPoiID,

		NTuple:          encodeNoCheck(nTuple),
		ProductCategory: int(product.GetProductCategory()),
		ComboType:       int(nTuple.ComboType),
		CarType:         product.Product.RequireLevel,
		SceneType:       int(product.Product.SceneType),
		ProductID:       int(product.Product.ProductID),

		IsFastCar:     isFastCar(int(product.Product.ProductID)),
		OType:         int(product.Product.OType),
		DepartureTime: options.DepartureTime,

		EstimateID: product.GetEstimateID(),
		BubbleID:   product.GetEstimateID(),

		EstimateDistanceMetre: nilToZero(attr.GetInt("driver_metre")),
		EstimateTimeMinutes:   nilToZero(attr.GetInt("driver_minute")),
		EstimateFee:           estimateFee.GetFee(),
		//FinalCouponValue:      0,
		PayType: int(estimateFee.GetPayment().PayType),
		//DynamicTotalFee:       0,

		// 下面的字段, 用字面默认值
		BasicTotalFee:        0,
		DynamicDiffPrice:     0,
		DynamicInfo:          "",
		Role:                 2,
		EstimateType:         "0",
		IsAnycar:             0,
		MultiRequireProduct:  "",
		PreferenceProduct:    "",
		MenuID:               "",
		FormShowType:         0,
		RecommendType:        0,
		SelectType:           0,
		OffPeakPushTimestamp: -1,
	}

	detail := estimateFee.GetFeeDetail()
	if detail == nil {
		return bd
	}
	if coupon := detail.GetCoupon(); coupon != nil {
		bd.msg.Data.FinalCouponValue = int(coupon.Amount * 100)
	}
	bd.msg.Data.DynamicTotalFee = detail.GetTotalFeeWithoutCouponSome()

	return bd
}

func (bd *MsgBuilder) Build() *Message {
	return bd.msg
}
