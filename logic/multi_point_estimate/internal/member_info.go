package internal

import (
	"context"
	"errors"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	GoMember "git.xiaojukeji.com/dirpc/dirpc-go-http-GoMember"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/member"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/multi_point_estimate/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/member_info"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/order_info"
)

type memberInfo struct {
	req *proto.MultiPointEstimateRequest
}

// NewMemberInfo ...
func NewMemberInfo(req *proto.MultiPointEstimateRequest) *memberInfo {
	return &memberInfo{
		req: req,
	}
}

// Access 准入
func (m *memberInfo) Access(ctx context.Context, data *model.CommonData) bool {
	if data == nil || data.OrderInfo == nil {
		return false
	}

	// 接驾调用
	if data.OrderInfo.GetOrderStatus() == order_info.DriverGrabbing.ToInt() {
		return true
	}

	return false
}

// GetProductZero 获取品类第0个
func (m *memberInfo) getProductZero(ctx context.Context, products []*models.Product) *models.Product {
	if len(products) > 0 {
		return products[0]
	}

	return nil
}

// Do 获取会员信息
func (m *memberInfo) Do(ctx context.Context, products []*models.Product, data *model.CommonData) error {
	tag := "get_member_info"

	if len(products) <= 0 || data == nil || data.UserInfo == nil || data.OrderInfo == nil || data.AreaInfo == nil || m.req == nil {
		return errors.New("data is nil")
	}

	product := m.getProductZero(ctx, products)
	if product == nil {
		return errors.New("member info product is nil")
	}

	resp, err := member.MultiProductQuery(ctx, int64(data.UserInfo.UID), data.OrderInfo.GetArea(), m.buildProductList(product), member.EnableNewWaitReply(ctx, m.req.AccessKeyId), nil)
	if err != nil {
		return err
	}

	if resp == nil {
		log.Trace.Warnf(ctx, tag, "member response is nil")
		return errors.New("member info response is nil")
	}

	if resp.Errno != 0 {
		log.Trace.Warnf(ctx, tag, "member response errno not equal zero")
		return errors.New("member response errno not equal zero")
	}

	data.MemberInfo = m.formatData(resp.Data)
	return nil
}

// formatData 格式化数据
func (m *memberInfo) formatData(memberInfo *GoMember.MultiQueryInfoV2) map[string]*member_info.MemberInfo {
	if memberInfo == nil {
		return nil
	}

	productList := memberInfo.ProductList
	if len(productList) <= 0 {
		return nil
	}

	dataMap := make(map[string]*member_info.MemberInfo, 0)
	for _, productItem := range productList {
		if productItem == nil || productItem.ProductInfo == nil {
			continue
		}

		key := ""
		if key = member_info.GenKey(productItem.ProductInfo); key == "" {
			continue
		}

		dataMap[key] = member_info.MemberInfo2MemberInfo(productItem)
	}

	return dataMap
}

// buildProductList 构建品类列表
func (m *memberInfo) buildProductList(product *models.Product) []member.Product {
	if product == nil {
		return nil
	}

	productList := make([]member.Product, 0)
	productList = append(productList, member.Product{
		BusinessId:   product.BusinessID,
		RequireLevel: product.RequireLevelInt,
		ComboType:    product.ComboType,
		CarpoolType:  product.CarpoolType,
	})

	return productList
}
