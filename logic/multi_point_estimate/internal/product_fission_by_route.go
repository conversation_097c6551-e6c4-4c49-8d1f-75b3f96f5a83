package internal

import (
	"context"
	"errors"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/multi_point_estimate/model"
)

type productFissionByRoute struct {
	req *proto.MultiPointEstimateRequest

	products []*models.Product
}

func NewProductFissionByRoute(req *proto.MultiPointEstimateRequest) *productFissionByRoute {
	return &productFissionByRoute{
		req: req,
	}
}

// Do 裂变品类
func (p *productFissionByRoute) Do(ctx context.Context, data *model.CommonData) error {
	if data == nil {
		return errors.New("data is nil")
	}

	products := make([]*models.Product, 0)
	areaMap := make(map[string]*proto.Point)
	pointList := p.req.PointList
	if len(pointList) <= 0 {
		return errors.New("route id list is nil")
	}

	pcID := getProductCategory(ctx, data.OrderInfo)

	for _, pointItem := range pointList {
		product := p.buildProduct(ctx, data, pcID, pointItem)
		if product == nil {
			continue
		}

		products = append(products, product)
		areaMap[product.EstimateID] = pointItem
	}

	data.AreaMap = areaMap
	p.products = products

	return nil
}

func (p *productFissionByRoute) buildProduct(ctx context.Context, data *model.CommonData, pcID int64, point *proto.Point) *models.Product {
	if data == nil || data.OrderInfo == nil || point == nil {
		return nil
	}

	product := &models.Product{}

	// 基础信息
	product.ProductCategory = pcID
	product.ProductID = data.OrderInfo.GetProductID()
	product.BusinessID = data.OrderInfo.GetBusinessID()
	product.RequireLevelInt = data.OrderInfo.GetRequireLevel()
	product.RequireLevel = data.OrderInfo.RequireLevel
	product.CarpoolType = data.OrderInfo.GetCarpoolType()
	product.LevelType = data.OrderInfo.GetLevelType()
	product.ComboType = data.OrderInfo.GetComboType()
	product.CarpoolPriceType = data.OrderInfo.GetCarpoolPriceType()
	product.IsSpecialPrice = data.OrderInfo.GetIsSpecialPrice()
	product.IsDualCarpoolPrice = data.OrderInfo.GetIsDualCarpoolPrice()

	// 场景标识
	product.AirportType = data.OrderInfo.GetAirportType()
	product.RailwayType = data.OrderInfo.GetRailwayType()
	product.HotelType = data.OrderInfo.GetHotelType()
	product.StationServiceControl = data.OrderInfo.GetStationServiceControl()
	product.LongRentType = data.OrderInfo.GetLongRentType()
	product.EmergencyServiceType = data.OrderInfo.GetEmergencyServiceType()
	product.PaymentsType = data.OrderInfo.GetPayType()
	product.RouteType = data.OrderInfo.GetRouteType()
	product.IsPickOnTime = data.OrderInfo.GetIsPickOnTime()
	// product.SceneType = data.OrderInfo.Scene // dds代码写死赋值为0 理论无用
	// product.ExamType = data.OrderInfo.GetExamType() // 高考场景标识，dos没存，跟价格计算无关，只涉及呼返高考券，行中不涉及呼返理论上无用

	product.RouteID = point.RouteId

	product.BuildEstimateByOption(ctx, []string{
		util.Float64ToString(point.FromLat),
		util.Float64ToString(point.FromLng),
		util.Float64ToString(point.ToLat),
		util.Float64ToString(point.ToLng),
		point.RouteId,
	})

	return product
}

// GetProducts 获取品类列表
func (p *productFissionByRoute) GetProducts() []*models.Product {
	return p.products
}
