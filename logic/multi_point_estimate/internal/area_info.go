package internal

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/multi_point_estimate/model"
)

type areaInfo struct {
	req *proto.MultiPointEstimateRequest

	city             int32
	district         string
	area             int32
	fromCounty       int32
	tripCountry      string
	fromCityName     string
	toArea           int32
	toCounty         int32
	toCityName       string
	abstractDistrict string
}

// NewAreaInfo 初始化
func NewAreaInfo(req *proto.MultiPointEstimateRequest) *areaInfo {
	return &areaInfo{
		req: req,
	}
}

// Do 获取用户信息
func (a *areaInfo) Do(ctx context.Context, products []*models.Product, data *model.CommonData) error {
	if data == nil {
		return errors.New("data is nil")
	}

	var areaInfoMap = make(map[string]*models.AreaInfo)

	for _, product := range products {
		if product == nil {
			continue
		}

		areaInfoMap[product.EstimateID] = a.buildAreaInfo(ctx, data, product)
	}

	data.AreaInfo = areaInfoMap

	return nil
}

// buildAreaInfo 构建地点信息
func (a *areaInfo) buildAreaInfo(ctx context.Context, data *model.CommonData, product *models.Product) *models.AreaInfo {
	if data == nil || data.OrderInfo == nil {
		return nil
	}

	curAreaInfo := data.AreaMap[product.EstimateID]

	fromAddress, fromName := getAddressAndNameByName(data.OrderInfo.StartingName)
	toAddress, toName := getAddressAndNameByName(data.OrderInfo.DestName)

	res := &models.AreaInfo{
		City:              data.OrderInfo.GetArea(),
		District:          data.OrderInfo.District,
		FromCounty:        data.OrderInfo.GetCounty(),
		ToCounty:          data.OrderInfo.GetToCounty(),
		FromCountyName:    data.OrderInfo.ExtendFeatureParsed.AddrInfo.StartCountyName,
		ToCountyName:      data.OrderInfo.ExtendFeatureParsed.AddrInfo.DestCountyName,
		FromCityName:      data.OrderInfo.ExtendFeatureParsed.AddrInfo.StartCityName,
		ToCityName:        data.OrderInfo.ExtendFeatureParsed.AddrInfo.DestCityName,
		FromLat:           curAreaInfo.FromLat,
		FromLng:           curAreaInfo.FromLng,
		FromAddress:       fromAddress,
		FromName:          fromName,
		FromPoiID:         data.OrderInfo.StartingPoiId,
		FromPoiType:       "",
		ToLat:             curAreaInfo.ToLat,
		ToLng:             curAreaInfo.ToLng,
		ToAddress:         toAddress,
		ToName:            toName,
		ToPoiID:           data.OrderInfo.DestPoiId,
		ToPoiType:         "",
		Area:              data.OrderInfo.GetArea(),
		ToArea:            data.OrderInfo.GetToArea(),
		MapType:           util.StringPtr2String(a.req.MapType),
		AbstractDistrict:  fmt.Sprintf("%s,%s", data.OrderInfo.District, data.OrderInfo.County),
		StartingName:      data.OrderInfo.StartingName,
		DestName:          data.OrderInfo.DestName,
		StopoverPointInfo: nil,
		TripCountry:       data.OrderInfo.GetCountryIsoCode(),
		MetroFenceID:      0,
	}

	return res
}

// getAddressAndNameByName 解析name
func getAddressAndNameByName(name string) (string, string) {
	res := strings.Split(name, "|")
	if len(res) < 2 {
		return "", ""
	}

	return res[0], res[1]
}
