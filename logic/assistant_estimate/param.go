package assistant_estimate

import (
	"context"
	"github.com/spf13/cast"
	"strconv"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/source_id"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
)

func InitLogic(ctx context.Context, passenger *passport.UserInfo, req *proto.PBDEstimateReq) (*BizLogic, error) {
	departureTime, _ := strconv.ParseInt(req.DepartureTime, 10, 64)
	if departureTime == 0 {
		departureTime = time.Now().Unix()
	}
	baseReq, err := models.NewBaseReqDataBuilder().
		SetClientInfo(&models.ClientInfo{
			AppVersion:     req.AppVersion,
			AccessKeyID:    req.AccessKeyId,
			Channel:        int64(req.Channel),
			SourceID:       source_id.SourceIDAssistant,
			Lang:           req.Lang,
			MenuID:         "dache_anycar",
			StopoverPoints: req.StopoverPoints,
		}).
		SetGEOInfo(&models.GEOInfo{
			FromLat:           req.FromLat,
			FromLng:           req.FromLng,
			FromName:          req.FromName,
			ToLat:             req.ToLat,
			ToLng:             req.ToLng,
			ToName:            req.ToName,
			RouteId:           req.RouteId,
			StopoverPointInfo: req.GetStopoverPoints(),
		}).
		SetPassengerInfoV2(&models.PassengerInfoV2{
			UID:      int64(passenger.UID),
			PID:      int64(passenger.PID),
			Phone:    passenger.Phone,
			Role:     passenger.Role,
			Channel:  passenger.Channel,
			UserType: req.UserType,
			OriginID: passenger.OriginId,
		}).
		SetUserOption(&models.UserOption{
			CallCarType:      0,
			OrderType:        req.OrderType,
			DepartureTime:    departureTime,
			PaymentsType:     req.PaymentsType,
			PreferredRouteId: cast.ToString(req.RouteId),
		}).
		TryBuild(ctx)
	if err != nil {
		return nil, BizError.ErrSystem
	}

	// no-check
	productsGen, _ := biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("pAssistantEstimate"),
		biz_runtime.WithBaseReq(baseReq),
	)

	productsGen.SetNeedMember(true)

	productsGen.SetSendReqKafka(true)

	RegisterProductFilter(ctx, req.NeedProductCategory, productsGen)

	RegisterRpcProcessWithBasicProducts(ctx, productsGen)

	// 冒泡推荐
	if recommendRPC := rpc_process.NewBubbleRecommendInfoRPC(productsGen.BaseReqData, int32(rpc_process.CombinedTravelOrderMatchType)); recommendRPC != nil {
		productsGen.RegisterAfterPriceRPCProcess(recommendRPC)
	}

	return &BizLogic{generator: productsGen}, nil
}

func CheckParams(ctx context.Context, req *proto.PBDEstimateReq) (err error) {
	if req == nil {
		return BizError.ErrInvalidArgument
	}
	if req.GetToLat() == 0 || req.GetToLng() == 0 || req.GetFromLat() == 0 || req.GetFromLng() == 0 {
		return BizError.ErrInvalidArgument
	}

	return nil
}
