package assistant_estimate

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type BizLogic struct {
	generator *biz_runtime.ProductsGenerator
}

func (b *BizLogic) DoBizLogic(ctx context.Context) (*proto.B2BEstimateData, error) {
	products, err := b.generator.GenProducts(ctx)
	if err != nil {
		return nil, err
	}
	resp, err := Render(ctx, b.generator.BaseReqData, products)
	defer func() {
		AddPublicLog(ctx, products, resp.Products)
	}()
	return resp, err
}
