package rpc

import (
	"context"
	"crypto/md5"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"github.com/spf13/cast"
	"strconv"
	"sync/atomic"
)

var counter = int64(10)

func GenPlanId(ctx context.Context, planType int32) string {
	traceId := util.GetTraceIDFromCtxWithoutCheck(ctx)
	newID := atomic.AddInt64(&counter, 1)
	metaKey := fmt.Sprintf("%s_%s_%s", traceId, strconv.FormatInt(newID, 10), cast.ToString(planType))
	hash := md5.Sum([]byte(metaKey))
	return fmt.Sprintf("%x", hash)

}
