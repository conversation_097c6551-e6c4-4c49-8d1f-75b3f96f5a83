package rpc

import (
	"context"
	"errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v3/model"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v3/wyc_transfer"
	"sort"
)

const (
	CarHailingLogTag = "CarHailingRPC"
)

type CarHailingRPC struct {
	// 基础数据
	Request     *model.Request
	WycAreaInfo *model.WycAreaInfo

	// 数据存储
	CarList []*wyc_transfer.CarEstimateData
}

func NewCarHailingRPC(ctx context.Context, baseRequest *model.Request) (*CarHailingRPC, error) {
	req := baseRequest.CompositeTravelV3Req
	// 参数校验
	if req.WycFromLat == 0 || req.WycFromLng == 0 || req.WycToLat == 0 || req.WycToLng == 0 ||
		req.MapType == "" || req.WycFromPoiId == "" {
		return nil, errors.New("wyc area params lost")
	}

	if baseRequest.AreaInfo == nil {
		log.Trace.Warnf(ctx, CarHailingLogTag, "baseRequest.AreaInfo is nil")
		return nil, errors.New("baseRequest.AreaInfo is nil")
	}

	return &CarHailingRPC{
		Request:     baseRequest,
		WycAreaInfo: baseRequest.AreaInfo,
	}, nil
}

func (c *CarHailingRPC) Execute(ctx context.Context, request *model.Request) {
	c.CarList = wyc_transfer.GetWycData(ctx, request, c.WycAreaInfo)
	if len(c.CarList) < 1 {
		log.Trace.Warnf(ctx, CarHailingLogTag, "wyc plan is nil")
	}
}

func (c *CarHailingRPC) GetModel(ctx context.Context) (planType int32, result []*model.PlanFull) {
	planType = model.CarHailing
	if c.CarList == nil || len(c.CarList) < 1 {
		return
	}

	carList := c.CarList
	// 按照价格排序
	sort.Slice(carList, func(i, j int) bool {
		car1 := carList[i]
		car2 := carList[j]
		return car1.FeeAmount < car2.FeeAmount
	})

	firstCar := carList[0]
	if firstCar == nil {
		return
	}

	result = append(result, c.buildModel(ctx, firstCar))
	return

}

func (c *CarHailingRPC) buildModel(ctx context.Context, car *wyc_transfer.CarEstimateData) *model.PlanFull {
	var (
		etp                  int32
		etd                  int32
		etq                  int32
		CarIsQu              int32
		CarEtqLen            int32
		planFormatDetailData model.PlanFormatDetailData // 方案详情数据模型
	)

	if car.EtpInfo != nil {
		etd = car.EtpInfo.Etd * 60
		etp = car.EtpInfo.Etp * 60
	}

	if car.EtpInfo != nil && car.EtpInfo.EtQueueInfo != nil {
		etq = car.EtpInfo.EtQueueInfo.WaitTime * 60
		CarIsQu = 1
		CarEtqLen = car.EtpInfo.EtQueueInfo.QueueLength
	}

	detailSegmentItem := model.SegmentItem{
		Mode:     model.ModeCAR,
		Distance: int32(car.DriverMeter),
		Time:     etd,
		Cost:     int32(car.FeeAmount * 100),
		ExpectedInfo: model.ExpectedInfo{
			Eta:       etd - etp,
			Etp:       &etp,
			Etq:       &etq,
			CarIsQu:   CarIsQu,
			CarEtqLen: CarEtqLen,
		},
	}
	planFormatDetailData.SegmentDataList = append(planFormatDetailData.SegmentDataList, &detailSegmentItem)
	return &model.PlanFull{
		PlanType:             model.CarHailing,
		TotalTime:            int64(etd),
		Cost:                 int64(car.FeeAmount * 100),
		Distance:             int32(car.DriverMeter),
		PlanFormatDetailData: planFormatDetailData,
		RenderMetaData: &model.RenderMetaData{
			NewPlanId: GenPlanId(ctx, model.CarHailing),
			CarData: &model.CarData{
				ProductCategory: car.ProductCategory,
				EstimateID:      car.EstimateID,
				BusinessID:      car.BusinessID,
				RequireLevel:    car.RequireLevel,
				ComboType:       car.ComboType,
				IsSelected:      car.IsSelected,
				ProductID:       car.ProductID,
				LevelType:       car.LevelType,
				CarTitle:        car.CarTitle,
				FeeMsg:          car.FeeMsg,
				FeeAmount:       car.FeeAmount,
				EtpInfo:         car.EtpInfo,
				DriverMeter:     car.DriverMeter,
				RouteIdList:     car.RouteIdList,
				DriverMinute:    car.DriverMinute,
			},
		},
	}
}
