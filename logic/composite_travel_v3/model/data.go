package model

import (
	combinedTravel "git.xiaojukeji.com/dirpc/dirpc-go-http-CombinedTravel"
	walkApi "git.xiaojukeji.com/dirpc/dirpc-go-http-DolphinApiService"
	route "git.xiaojukeji.com/dirpc/dirpc-go-http-OrderRouteApi"
	metro "git.xiaojukeji.com/dirpc/dirpc-go-http-TransitWind"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
)

const (
	HomePageVerTagV1 = "homepageonestop"
	Version90        = "9.0"
)

const (
	CarHailing      = 1 // 网约车
	PublicTransit   = 2 // 公交地铁
	CombinedTravel  = 3 // 组合出行
	BicyclingTravel = 4 // 全程骑行
	WalkingTravel   = 5 // 全程步行
	DaijiaTravel    = 6 // 代驾

	LinkTypeTab = 1 // tab_id
	LinkTypeUrl = 2 // 原生地址
)

const (
	OmegaCarHailing          = 1 // 网约车
	OmegaPublicTransit       = 2 // 公交地铁
	OmegaCarHailingPlus      = 3 // 打车组合
	OmegaBicyclingTravelPlus = 4 // 骑行组合
	OmegaWalkingTravel       = 5 // 全程步行
	OmegaBicyclingTravel     = 6 // 全程骑行
)

const (
	SubType_Composite_Recommend = "composite_recommend"
	SubType_Classify            = "classify"
	SubType_Public_Transit      = "public_transit"
	SubType_Bicycle             = "bicycle"
	SubType_Walking             = "walking"
)

const (
	ModeWalk               = "WALKING" // 步行方案
	ModeTRANSIT            = "TRANSIT" // 公交/地铁
	ModeCAR                = "CAR"     // 打车
	ModeBike               = "BICYCLE" // 骑行
	ModeTRANSIT_BUS        = "TRANSIT_BUS"
	ModeTRANSIT_SUBWAY     = "TRANSIT_SUBWAY"
	ModeTRANSIT_BUS_SUBWAY = "TRANSIT_BUS_SUBWAY"

	TypeSubway = 0 //  0: 地铁 , 1: 公交    (2: 火车， 3, 班车 4, 快线)
	TypeBus    = 1 //  0: 地铁 , 1: 公交    (2: 火车， 3, 班车 4, 快线)

	CanReachStatusYes            = 0 //一定可达
	CanReachStatusFirstNotDepart = 1 //首班车未发车
	CanReachStatusMayMisPre      = 2 //可能错过上一班车
	CanReachStatusNextNotDepart  = 3 //下一班车未发车
	CanReachStatusMayMisLast     = 4 //可能错过末班车
	CanReachStatusNot            = 5 //不可达

	TypeDefault = -1
)

const PointSymbol = "·"
const (
	CyclingStyle_Bike             = "bike"             // 自行车
	CyclingStyle_Electric_Vehicle = "electric_vehicle" // 电动车
)

const RedisKeyPrefix = "new_one_stop_travel_90"

type HomePageVerTagInfo struct {
	Native string `json:"native"`
	Wechat string `json:"wechat"`
	Alipay string `json:"alipay"`
}

type PlanFull struct {
	TotalTime int64 // 耗时 单位:秒
	Cost      int64 // 花费 单位:分
	Distance  int32 // 距离
	PlanFormatDetailData

	Xn           float64 // 节省时间比例
	Yn           float64 // 节省预算比例
	Zn           float64 // 性价比比值
	FormShowType int32   // 表单位置(1:置顶)

	PlanType     int32
	RemoveReason string
	proto.PlanV3
	//PrivateData    *PrivateData
	RecommendTag   *proto.RecommendTag
	RenderMetaData *RenderMetaData
}

type RenderMetaData struct {
	NewPlanId          string
	Fid                string
	PlanId             string
	BicycleData        *BicycleData
	WalkData           *walkApi.PassengerWalkRouteRes
	CombinedTravelData *combinedTravel.PlanItem
	PublicTransitData  *PublicTransitData
	CarData            *CarData
}

type PublicTransitData struct {
	TransitData *metro.TransitSimple
	SubTimeData *metro.TransitInnerSimpleData
}

type BicycleData struct {
	BicycleDataList       []*route.BicyclingRouteDetail
	TargetBicycleDataItem *route.MpRouteDetail
	TargetDataType        int32
}

type PlanFormatDetailData struct {
	SegmentDataList []*SegmentItem
}

type CarData struct {
	ProductCategory int64  `json:"product_category"`
	EstimateID      string `json:"estimate_id"`
	BusinessID      int64  `json:"business_id"`
	RequireLevel    string `json:"require_level"` // 因为php的nTuple sdk 和 go里的NTuple 都是string
	ComboType       int64  `json:"combo_type"`
	IsSelected      int32  `json:"is_selected"`
	ProductID       int64  `json:"product_id"`
	LevelType       int32  `json:"level_type"`

	CarTitle  string  `json:"car_title"` // 车型数据
	FeeMsg    string  `json:"fee_msg"`
	FeeAmount float64 `json:"fee_amount"` // 价格信息

	EtpInfo      *AthenaApiv3.EstimatedTimeInfo `json:"etp_info"`      // 预期信息
	DriverMeter  int64                          `json:"driver_meter"`  // 打车距离
	RouteIdList  []string                       `json:"route_id_list"` // 路线信息
	DriverMinute int64                          `json:"driver_minute"` // 打车时间
}

type SegmentItem struct {
	Mode           string
	SubMode        string // 目前只有Mode 为 TRANSIT时使用，0: 地铁 , 1: 公交 , 2: 火车， 3, 班车 4, 快线
	Distance       int32
	Time           int32
	Cost           int32
	StationCnt     int32
	Etp            int32
	Eta            int32
	Etq            int32
	CarIsQu        int32
	CarEtqLen      int32
	ViaStops       []*Stop
	LineId         string
	LineName       string
	SubLineName    string
	LineColor      *string
	CanReachStatus int32

	/** 详细接驾信息，打车，公共交通独有**/
	ExpectedInfo

	/** 站点信息，公共交通独有 **/
	StationInfo
}

type ExpectedInfo struct {
	Eta       int32
	Etp       *int32
	Etq       *int32
	CarIsQu   int32 // 打车段是否排队
	CarEtqLen int32 // 排队人数
}

type StationInfo struct {
	StationCnt     int32   // 站点数量
	ViaStops       []*Stop // 途径站点数据
	LineId         string  // 路线id
	LineName       string  // 路线名字
	CanReachStatus int32   // 是否可达
}

type Stop struct {
	Id       string `json:"id"`       //站点id
	Name     string `json:"name"`     //站点名称
	Location string `json:"location"` //站点位置
}

type PrivateData struct {
	BicycleData        []*route.BicyclingRouteDetail
	WalkData           *walkApi.PassengerWalkRouteRes
	CombinedTravelData *combinedTravel.PlanItem
	PublicTransitData  *metro.TransitSimple
	CarData            *PrivateDataCarData
}

type PrivateDataCarData struct {
	DriverMeter  int64
	CarNameList  []string
	EtpInfo      *AthenaApiv3.EstimatedTimeInfo
	MinFeeAmount float64
	MaxFeeAmount float64
	SingleFeeMsg string
}
