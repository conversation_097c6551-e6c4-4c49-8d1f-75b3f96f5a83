package render

import (
	"context"
	"fmt"
	combinedTravel "git.xiaojukeji.com/dirpc/dirpc-go-http-CombinedTravel"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v3/model"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

type CombinedTravelRender struct {
	*BaseRender
	startStationName string // 第一个地铁站名称
	entranceName     string
	startSubMode     int32
	transitCostSum   int64 // 地铁/公交总费用-> 渲染"x元"
	conf             CombinedDCMPConf
	baseData         *combinedTravel.PlanItem
	fid              string
}

type CombinedDCMPConf struct {
	Title        Title             `json:"title"`
	MapParams    map[string]string `json:"map_params"`
	SubTitle     SubTitle          `json:"sub_title"`
	DescList     DescList          `json:"desc_list"`
	RightDesc    RightDesc         `json:"right_desc"`
	ReminderInfo ReminderInfo      `json:"reminder_info"`
	LinkUrl      LinkUrl           `json:"link_url"`
	Segment      Segment           `json:"segment"`
	SubTimeText  string            `json:"sub_time_text"`
}

type Title struct {
	Car    string `json:"car"`
	Subway string `json:"subway"`
	Cycle  string `json:"cycle"`
	Bus    string `json:"bus"`
	Walk   string `json:"walk"`
}

type SubTitle struct {
	Icon string `json:"icon"`
}

type TimeUnit struct {
	Hour   string `json:"hour"`
	Minute string `json:"minute"`
}

type DescList struct {
	StationContent string `json:"station_content"`
	BusDesc        string `json:"bus_desc"`
	SubwayDesc     string `json:"subway_desc"`
}

type RightDesc struct {
	FeeMsg string `json:"fee_msg"`
}

type ReminderInfo struct {
	Icon      string `json:"icon"`
	TextColor string `json:"text_color"`
	BgColor   string `json:"bg_color"`
}
type LinkUrl struct {
	MiniProgram string `json:"mini_program"`
	Default     string `json:"default"`
}

type Segment struct {
	CarInfo    CarInfo    `json:"car_info"`
	CycleInfo  CycleInfo  `json:"cycle_info"`
	BusInfo    BusInfo    `json:"bus_info"`
	SubwayInfo SubwayInfo `json:"subway_info"`
	WalkInfo   WalkInfo   `json:"walk_info"`
}

type CarInfo struct {
	Icon        string `json:"icon"`
	Content     string `json:"content"`
	TextColor   string `json:"text_color"`
	BgColor     string `json:"bg_color"`
	BorderColor string `json:"border_color"`
}

type CycleInfo struct {
	Icon        string `json:"icon"`
	Content     string `json:"content"`
	TextColor   string `json:"text_color"`
	BgColor     string `json:"bg_color"`
	BorderColor string `json:"border_color"`
}

type BusInfo struct {
	Icon        string `json:"icon"`
	TextColor   string `json:"text_color"`
	BgColor     string `json:"bg_color"`
	BorderColor string `json:"border_color"`
}

type SubwayInfo struct {
	Icon        string `json:"icon"`
	TextColor   string `json:"text_color"`
	BgColor     string `json:"bg_color"`
	BorderColor string `json:"border_color"`
}

type WalkInfo struct {
	Icon      string `json:"icon"`
	TextColor string `json:"text_color"`
}

const (
	SegmentType_Walk   = "walk"
	SegmentType_Cycle  = "cycle"
	SegmentType_Bus    = "bus"
	SegmentType_Subway = "subway"
	SegmentType_Car    = "car"
	ShowWalkThreshold  = 300 // 步行展示时间阈值 5分钟
)

func NewCombinedTravelRender(ctx context.Context, base *BaseRender) *CombinedTravelRender {

	conf, err := initCombinedTravelDCMPConf(ctx)
	if err != nil {
		log.Trace.Warnf(ctx, PlanRenderEngineTag, "init combined travel service dcmp error || err = %v", err)
		return nil
	}

	return &CombinedTravelRender{BaseRender: base, conf: conf}
}

func initCombinedTravelDCMPConf(ctx context.Context) (CombinedDCMPConf, error) {
	conf := CombinedDCMPConf{}
	str := dcmp.GetDcmpContent(ctx, "new_one_stop-v3_combined_travel_conf", nil)
	err := jsoniter.UnmarshalFromString(str, &conf)
	if err != nil {
		return conf, err
	}
	return conf, nil
}

func (c *CombinedTravelRender) GetSubTimeText(ctx context.Context, plan *model.PlanFull) string {

	return c.conf.SubTimeText
}

func (c *CombinedTravelRender) Render(ctx context.Context, plan *model.PlanFull, rec *AthenaApiv3.UTRecItem, index int, canTag bool) *proto.PlanV3 {

	renderData := plan.GetRenderMetaData()
	if renderData == nil || renderData.CombinedTravelData == nil {
		return nil
	}
	combinedTravelData := renderData.CombinedTravelData

	if combinedTravelData.SegmentList == nil || len(combinedTravelData.SegmentList) < 1 {
		return nil
	}
	c.NewPlanId = renderData.NewPlanId
	c.baseData = combinedTravelData
	c.fid = renderData.Fid

	var (
		segmentList         []*proto.Segment
		modeList            []string
		walkTotalDistance   int32
		carTotalDistance    int32
		firstTransitSegment *combinedTravel.UTRecommendSegmentItem // 第一个公交/地铁
	)
	modeMap := make(map[string]string)

	for _, segmentItem := range combinedTravelData.SegmentList {
		var (
			item     *proto.Segment
			modeType string
			segTitle string
		)

		if segmentItem == nil {
			continue
		}

		// 渲染不同的出行方案
		switch segmentItem.Mode {
		case model.ModeWalk:
			item, modeType, segTitle = c.rendModelWalk(ctx, segmentItem)
			walkTotalDistance = walkTotalDistance + segmentItem.GetDistance()
		case model.ModeBike:
			item, modeType, segTitle = c.rendModelCycle(ctx, segmentItem)
		case model.ModeCAR:
			item, modeType, segTitle = c.rendModelCar(ctx, segmentItem)
			carTotalDistance = carTotalDistance + segmentItem.GetDistance()
		case model.ModeTRANSIT:
			item, modeType, segTitle = c.rendModelTransit(ctx, segmentItem)
			// 记录第一个公共交通
			if firstTransitSegment == nil {
				firstTransitSegment = segmentItem
			}
		}

		if item == nil {
			continue
		}

		segmentList = append(segmentList, item)

		modeList = append(modeList, modeType)
		if _, exist := modeMap[modeType]; !exist {
			modeMap[modeType] = segTitle
		}

	}

	recommendTag, recommendContent := c.buildRecommendTag(rec, canTag)
	recommendReason, recommendReasonContent := c.buildRecommendReason(rec, canTag)
	title := c.getTitle(modeMap, modeList)
	o := &proto.PlanV3{
		Title:           title,
		SegmentList:     segmentList,
		DescList:        c.getDescList(ctx, carTotalDistance, combinedTravelData.GetStationCount(), firstTransitSegment, modeMap, modeList),
		RightTitle:      c.getRightTitle(ctx, combinedTravelData.GetTotalTime()),
		SubTitle:        c.getSubTitle(ctx, walkTotalDistance),
		RightDesc:       c.getRightDesc(ctx, combinedTravelData.GetTotalCost()),
		Params:          c.buildParams(),
		TipData:         c.buildTipData(&combinedTravelData.ReminderText),
		LinkUrl:         c.buildLinkUrl(),
		MapParams:       c.buildMapParams(),
		LinkType:        model.LinkTypeUrl,
		PlanType:        model.CombinedTravel,
		RecommendTag:    recommendTag,
		RecommendReason: recommendReason,
		OmegaInfo:       c.buildOmegaInfo(index, title, recommendContent, recommendReasonContent, combinedTravelData.GetTotalTime(), combinedTravelData.GetTotalCost(), modeMap),
	}

	return o
}

func (c *CombinedTravelRender) buildPlanType(modeMap map[string]string) int32 {
	if modeMap == nil || len(modeMap) < 1 {
		return 0
	}

	_, existBike := modeMap[model.ModeBike]
	_, existCar := modeMap[model.ModeCAR]
	_, existTransitBus := modeMap[model.ModeTRANSIT_BUS]
	_, existTransitSubway := modeMap[model.ModeTRANSIT_SUBWAY]

	if existBike && (existTransitBus || existTransitSubway) {
		return model.OmegaBicyclingTravelPlus
	}

	if existCar && (existTransitBus || existTransitSubway) {
		return model.OmegaCarHailingPlus
	}

	if !existBike && !existCar && (existTransitBus || existTransitSubway) {
		return model.OmegaPublicTransit
	}

	return 0

}

func (c *CombinedTravelRender) buildOmegaInfo(index int, title, recommendContent, recommendReasonContent string, totalDuration int64, totalFee int64, modeMap map[string]string) map[string]interface{} {

	omegaInfo := map[string]interface{}{
		"index":         index,
		"plan_type":     c.buildPlanType(modeMap),
		"plan_title":    title,
		"tag_value":     recommendContent,
		"top_recommend": recommendReasonContent,
		"plan_id":       c.NewPlanId,
	}

	// 总耗时
	if totalDuration <= 0 {
		omegaInfo["time"] = ""
	} else {
		omegaInfo["time"] = fmt.Sprintf("{%s}分钟", cast.ToString(totalDuration/60))
	}

	// 总费用
	omegaInfo["price"] = fmt.Sprintf("{%s}元", util.Fen2Yuan(totalFee))

	return omegaInfo

}

func (c *CombinedTravelRender) buildTipData(content *string) *proto.TipData {
	if content == nil || *content == "" {
		return nil
	}

	return &proto.TipData{
		Text:      *content,
		Icon:      &c.conf.ReminderInfo.Icon,
		TextColor: c.conf.ReminderInfo.TextColor,
		BgColor:   c.conf.ReminderInfo.BgColor,
	}
}

func (c *CombinedTravelRender) buildMapParams() map[string]string {
	mapParams := c.conf.MapParams
	mapParams = make(map[string]string)
	mapParams["product_id"] = c.conf.MapParams["productId"]
	mapParams["acc_key"] = c.conf.MapParams["accKey"]
	mapParams["caller_id"] = c.conf.MapParams["callerId"]
	mapParams["fid"] = c.fid
	mapParams["transit_id"] = c.baseData.TransitId
	mapParams["map_info"] = c.baseData.MapInfo
	return mapParams
}

func (c *CombinedTravelRender) buildParams() map[string]string {
	return map[string]string{
		"fid":       c.fid,
		"from_type": "1",
		"plan_id":   c.baseData.PlanId,
	}
}

func (c *CombinedTravelRender) rendModelWalk(ctx context.Context, data *combinedTravel.UTRecommendSegmentItem) (*proto.Segment, string, string) {

	if data == nil || data.GetDuration() < ShowWalkThreshold {
		return nil, data.Mode, c.conf.Title.Walk
	}
	segment := &proto.Segment{
		Icon:      &c.conf.Segment.WalkInfo.Icon,
		Content:   c.GetEstimateTime(ctx, int64(data.Duration)),
		TextColor: c.conf.Segment.WalkInfo.TextColor,
	}

	return segment, data.Mode, c.conf.Title.Walk

}

func (c *CombinedTravelRender) rendModelCycle(ctx context.Context, data *combinedTravel.UTRecommendSegmentItem) (*proto.Segment, string, string) {

	if data == nil {
		return nil, data.Mode, c.conf.Title.Cycle
	}
	segment := &proto.Segment{
		Icon:        &c.conf.Segment.CycleInfo.Icon,
		Content:     c.GetDistance(ctx, data.GetDistance()),
		TextColor:   c.conf.Segment.CycleInfo.TextColor,
		BgColor:     c.conf.Segment.CycleInfo.BgColor,
		BorderColor: c.conf.Segment.CycleInfo.BorderColor,
	}

	return segment, data.Mode, c.conf.Title.Cycle

}

func (c *CombinedTravelRender) rendModelCar(ctx context.Context, data *combinedTravel.UTRecommendSegmentItem) (*proto.Segment, string, string) {

	if data == nil {
		return nil, data.Mode, c.conf.Title.Car
	}
	segment := &proto.Segment{
		Icon:        &c.conf.Segment.CarInfo.Icon,
		Content:     c.conf.Segment.CarInfo.Content,
		BgColor:     c.conf.Segment.CarInfo.BgColor,
		TextColor:   c.conf.Segment.CarInfo.TextColor,
		BorderColor: c.conf.Segment.CarInfo.BorderColor,
	}

	return segment, data.Mode, c.conf.Title.Car
}

func (c *CombinedTravelRender) rendModelTransit(ctx context.Context, data *combinedTravel.UTRecommendSegmentItem) (*proto.Segment, string, string) {
	var (
		modeType string
		segTitle string
	)

	if data == nil {
		return nil, modeType, ""
	}

	segment := &proto.Segment{}
	if data.TransitInfo.Type == model.TypeSubway {
		modeType = model.ModeTRANSIT_SUBWAY
		segTitle = c.conf.Title.Subway
		segment.Icon = &c.conf.Segment.SubwayInfo.Icon
		segment.Content = data.TransitInfo.Name
		segment.TextColor = data.TransitInfo.TextColor
		segment.BgColor = data.TransitInfo.BgColor
	}

	if data.TransitInfo.Type == model.TypeBus {
		modeType = model.ModeTRANSIT_BUS
		segTitle = c.conf.Title.Bus
		segment.Icon = &c.conf.Segment.BusInfo.Icon
		segment.Content = data.TransitInfo.Name
		segment.TextColor = c.conf.Segment.BusInfo.TextColor
		segment.BorderColor = c.conf.Segment.BusInfo.BorderColor
	}

	return segment, modeType, segTitle

}

func (c *CombinedTravelRender) buildLinkUrl() string {

	if c.BaseRequest != nil && (c.BaseRequest.AccessKeyId == 9 || c.BaseRequest.AccessKeyId == 22) {
		return c.conf.LinkUrl.MiniProgram
	}

	return c.conf.LinkUrl.Default
}

func (c *CombinedTravelRender) getTitle(modeMap map[string]string, modeList []string) string {

	if len(modeMap) < 1 || len(modeList) < 1 {
		return ""
	}

	// 只有一种方式
	if len(modeMap) == 1 {
		return modeMap[modeList[0]]
	}

	// 多种
	var title string
	usedModeMap := make(map[string]bool)
	for _, modeType := range modeList {
		if modeType == model.ModeWalk {
			continue
		}
		if _, used := usedModeMap[modeType]; !used {
			if title == "" {
				title = modeMap[modeType]
			} else {
				title = title + "+" + modeMap[modeType]
			}
			usedModeMap[modeType] = true
		}
	}

	return title

}

func (c *CombinedTravelRender) getSubTitle(ctx context.Context, walkTotalDistance int32) *proto.SubTitleV3 {

	if walkTotalDistance <= 0 {
		return nil
	}

	subTitle := &proto.SubTitleV3{
		Icon:    c.conf.SubTitle.Icon,
		Content: c.GetDistance(ctx, walkTotalDistance),
	}

	return subTitle

}

func (c *CombinedTravelRender) getRightTitle(ctx context.Context, totalDuration int64) string {
	if totalDuration <= 0 {
		return ""
	}

	return c.getEstimateTime(ctx, totalDuration)

}

func (c *CombinedTravelRender) getEstimateTime(ctx context.Context, time int64) string {
	if time <= 0 {
		return ""
	}

	hours := time / 3600          // 获取小时数
	minutes := (time % 3600) / 60 // 获取分钟

	if hours == 0 {
		return util.ReplaceTag(ctx, c.baseConfig.BaseTimeConf.HighLightMinutes, map[string]string{
			"minutes": cast.ToString(minutes),
		})
	}

	if minutes == 0 {
		return util.ReplaceTag(ctx, c.baseConfig.BaseTimeConf.HighLightHours, map[string]string{
			"hours": cast.ToString(hours),
		})
	}

	return util.ReplaceTag(ctx, c.baseConfig.BaseTimeConf.HighLightHoursMinutes, map[string]string{
		"hours":   cast.ToString(hours),
		"minutes": cast.ToString(minutes),
	})
}

func (c *CombinedTravelRender) getRightDesc(ctx context.Context, totalFee int64) string {
	if totalFee <= 0 {
		return ""
	}
	return util.ReplaceTag(ctx, c.conf.RightDesc.FeeMsg, map[string]string{
		"fee": util.Fen2Yuan(totalFee),
	})

}

func (c *CombinedTravelRender) getDescList(ctx context.Context, carTotalDistance, stationTotalCnt int32, firstTransitSegment *combinedTravel.UTRecommendSegmentItem, modeMap map[string]string, modeList []string) []string {
	descList := make([]string, 0)
	var entranceInfo string

	// 只有打车
	if len(modeMap) == 1 && modeList[0] == model.ModeCAR {
		if carTotalDistance > 0 {
			descList = append(descList, c.GetDistance(ctx, carTotalDistance), c.BaseRequest.FromName)
		}

		return descList
	}

	// 公共交通+
	// 共xx站
	if stationTotalCnt > 0 {
		descList = append(descList, util.ReplaceTag(ctx, c.conf.DescList.StationContent, map[string]string{
			"station_count": cast.ToString(stationTotalCnt),
		}))
	}

	if firstTransitSegment != nil && firstTransitSegment.TransitInfo != nil && firstTransitSegment.TransitInfo.StartStationName != "" {
		// 公交
		if firstTransitSegment.TransitInfo.Type == model.TypeBus {
			// xxx站上车
			entranceInfo = util.ReplaceTag(ctx, c.conf.DescList.BusDesc, map[string]string{
				"start_station": firstTransitSegment.TransitInfo.StartStationName,
			})

		} else if firstTransitSegment.TransitInfo.Type == model.TypeSubway {
			// 地铁
			// xxx(xxx口)进站
			entranceInfo = fmt.Sprintf(c.conf.DescList.SubwayDesc, firstTransitSegment.TransitInfo.StartStationName, firstTransitSegment.TransitInfo.EntranceName)
		}

		descList = append(descList, entranceInfo)
	}

	return descList

}
