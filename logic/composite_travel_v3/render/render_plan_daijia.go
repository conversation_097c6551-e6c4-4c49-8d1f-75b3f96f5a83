package render

import (
	"context"
	Daijia "git.xiaojukeji.com/dirpc/dirpc-go-http-DaijiaKopService"
	AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"
	"git.xiaojukeji.com/gulfstream/bronze-door-sdk-go/common/utils"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/conf"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/daijia"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel_v3/model"
	"github.com/spf13/cast"
)

type DaijiaPlanRender struct {
	*BaseRender
}

func NewDaijiaPlanRender(ctx context.Context, base *BaseRender) *DaijiaPlanRender {
	return &DaijiaPlanRender{
		BaseRender: base,
	}
}

func (d *DaijiaPlanRender) Render(ctx context.Context, plan *model.PlanFull, rec *AthenaApiv3.UTRecItem, index int, canTag bool) *proto.PlanV3 {
	return nil
}

func (d *DaijiaPlanRender) GetSubTimeText(ctx context.Context, plan *model.PlanFull) string {
	request := d.BaseRequest
	if request == nil {
		return ""
	}
	req := &Daijia.GetTripEstimationReq{
		Token:            utils.StringPtr(request.Token),
		UserId:           int64(request.UserInfo.UID),
		StartAddress:     request.WycFromAddress,
		StartName:        request.WycFromName,
		StartLat:         request.WycFromLat,
		StartLng:         request.WycFromLng,
		StartPoiId:       request.WycFromPoiId,
		EndAddress:       request.WycToAddress,
		EndLat:           request.WycToLat,
		EndLng:           request.WycToLng,
		EndName:          request.WycToName,
		EndPoiId:         request.WycToPoiId,
		StartDidiCityId:  request.AreaInfo.FromCityId,
		UserStartLat:     request.FromLat,
		UserStartLng:     request.FromLng,
		UserStartPoiId:   request.FromPoiId,
		UserStartName:    request.FromName,
		UserStartAddress: request.FromAddress,
		UserEndLat:       request.ToLat,
		UserEndLng:       request.ToLng,
		UserEndPoiId:     request.ToPoiId,
		UserEndName:      request.ToName,
		UserEndAddress:   request.ToAddress,
		AppKey:           conf.Viper.GetString("dai_jia.app_key"),
		WayPointList:     utils.StringPtr(request.GetStopoverPoints()),
	}

	res := daijia.GetTripEstimation(ctx, req)

	if res == nil || res.GetDuration() <= 0 {
		return ""
	}
	return d.getEstimateTimeWithoutMinutesUnit(ctx, res.GetDuration()/60)
}

func (d *DaijiaPlanRender) getEstimateTimeWithoutMinutesUnit(ctx context.Context, etd int64) string {

	if etd <= 0 {
		return ""
	}

	hours := etd / 60
	minutes := etd % 60

	if hours == 0 {
		return util.ReplaceTag(ctx, d.baseConfig.BaseTimeConf.Minutes, map[string]string{
			"minutes": cast.ToString(minutes),
		})
	}

	if minutes == 0 {
		return util.ReplaceTag(ctx, d.baseConfig.BaseTimeConf.Hours, map[string]string{
			"hours": cast.ToString(hours),
		})
	}

	return util.ReplaceTag(ctx, d.baseConfig.BaseTimeConf.HoursMinutesWithoutMinuteUnit, map[string]string{
		"hours":   cast.ToString(hours),
		"minutes": cast.ToString(minutes),
	})

}
