package pets_travel_estimate

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

const PublicKey = "g_order_cap_multi_estimate_price"

func WritePublicLog(ctx context.Context, products []*biz_runtime.ProductInfoFull, req *proto.PetsTravelEstimateReq, data *proto.PetsTravelEstimateData) {

	for _, product := range products {
		if product == nil || product.Product == nil || product.GetSceneEstimatePrice() == nil {
			continue
		}

		logInfo := make(map[string]interface{})
		//索引key
		logInfo["estimate_trace_id"] = util.GetTraceIDFromCtxWithoutCheck(ctx)
		logInfo["estimate_id"] = product.GetEstimateID()

		if req.Xpsid != nil {
			logInfo["xpsid"] = req.Xpsid
		}
		if req.XpsidRoot != nil {
			logInfo["xpsid_root"] = req.XpsidRoot
		}

		//地理位置信息
		areaInfo := product.GetAreaInfo()
		logInfo["area"] = areaInfo.City
		logInfo["to_area"] = areaInfo.ToArea
		logInfo["flat"] = areaInfo.FromLat
		logInfo["flng"] = areaInfo.FromLng
		logInfo["tlat"] = areaInfo.ToLat
		logInfo["tlng"] = areaInfo.ToLng
		logInfo["district"] = areaInfo.District
		logInfo["current_lat"] = areaInfo.CurLat
		logInfo["current_lng"] = areaInfo.CurLng
		logInfo["county"] = areaInfo.FromCounty
		logInfo["to_county"] = areaInfo.ToCounty
		logInfo["from_name"] = util.StringEscape(areaInfo.FromName)
		logInfo["to_name"] = util.StringEscape(areaInfo.ToName)

		//端信息
		commonInfo := product.GetClientInfo()
		logInfo["app_version"] = commonInfo.AppVersion
		logInfo["client_type"] = commonInfo.ClientType
		logInfo["access_key_id"] = commonInfo.AccessKeyID
		logInfo["channel"] = commonInfo.Channel
		logInfo["page_type"] = commonInfo.PageType
		logInfo["from_type"] = product.BaseReqData.CommonInfo.FromType
		logInfo["lang"] = commonInfo.Lang
		logInfo["guide_trace_id"] = req.GuideTraceId
		if req.Dchn != nil {
			logInfo["dchn"] = req.Dchn
		}

		//产品信息
		logInfo["product_category"] = product.GetProductCategory()
		logInfo["require_level"] = product.Product.RequireLevel
		logInfo["combo_type"] = product.Product.ComboType
		logInfo["carpool_type"] = product.Product.CarpoolType
		logInfo["product_id"] = product.Product.ProductID
		logInfo["business_id"] = product.Product.BusinessID
		if product.GetBizInfo() != nil {
			logInfo["combo_id"] = product.GetBizInfo().ComboID
		}

		//用户信息
		logInfo["pid"] = product.GetUserInfo().PID
		logInfo["uid"] = product.GetUserInfo().UID

		//宠物信息
		if petInfo := product.BaseReqData.CommonBizInfo.PetInfo; petInfo != nil {
			logInfo["pet_no"] = petInfo.PetNo
			logInfo["pet_type"] = petInfo.PetType
			logInfo["pet_type_desc"] = petInfo.PetTypeDesc
			logInfo["nick_name"] = petInfo.NickName
			logInfo["avatar"] = petInfo.Avatar
			logInfo["weight_category"] = petInfo.WeightCategory
			logInfo["weight_category_desc"] = petInfo.WeightCategoryDesc
			logInfo["is_default"] = petInfo.IsDefault
			logInfo["pet_service_tag"] = petInfo.PetServiceTag
			logInfo["personalized_custom_option"] = petInfo.PersonalizedCustomOption
		}

		log.Public.Public(ctx, PublicKey, logInfo)
	}

}
