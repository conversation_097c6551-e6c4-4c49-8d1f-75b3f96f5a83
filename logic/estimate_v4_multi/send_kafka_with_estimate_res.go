package estimate_v4_multi

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/ddmq"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	context2 "git.xiaojukeji.com/lego/context-go"
	"git.xiaojukeji.com/nuwa/golibs/json"
	"git.xiaojukeji.com/nuwa/trace"
	"git.xiaojukeji.com/s3e/common-lib/v2/component/diff"
	"math/rand"
	"time"
)

type EstimateResData struct {
	EstimateTraceId       string              `json:"estimate_trace_id"`
	PassengerPhone        string              `json:"passenger_phone"`
	PassengerId           int64               `json:"passenger_id"`
	District              string              `json:"district"`
	Area                  int32               `json:"area"`
	Channel               int64               `json:"channel"`
	StartingLng           float64             `json:"starting_lng"`
	StartingLat           float64             `json:"starting_lat"`
	County                int32               `json:"county"`
	DestLng               float64             `json:"dest_lng"`
	DestLat               float64             `json:"dest_lat"`
	ToCity                int32               `json:"to_city"`
	FromPoiId             string              `json:"from_poi_id"`
	ToPoiId               string              `json:"to_poi_id"`
	CreateTime            int64               `json:"create_time"`
	ProductId             int64               `json:"product_id"`
	SceneType             int64               `json:"scene_type"`
	CarType               string              `json:"car_type"`
	MultiRequireProduct   string              `json:"multi_require_product"`
	PreferenceProduct     string              `json:"preference_product"`
	IsAnycar              int                 `json:"is_anycar"`
	NTuple                *biz_runtime.NTuple `json:"n_tuple"`
	CallCar               int32               `json:"call_car"`
	CurrentLng            float64             `json:"current_lng"`
	CurrentLat            float64             `json:"current_lat"`
	ClientType            int32               `json:"client_type"`
	PlatformType          int32               `json:"platform_type"`
	StartingName          string              `json:"starting_name"`
	DestName              string              `json:"dest_name"`
	DepartureTime         int64               `json:"departure_time"`
	IsFastCar             int                 `json:"is_fast_car"`
	OType                 int32               `json:"oType"`
	AppVersion            string              `json:"app_version"`
	OriginId              int64               `json:"origin_id"`
	MenuId                string              `json:"menu_id"`
	BubbleId              string              `json:"bubble_id"`
	EstimateId            string              `json:"estimate_id"`
	ComboType             int64               `json:"combo_type"`
	EstimateDistanceMetre int64               `json:"estimate_distance_metre"`
	EstimateTimeMinutes   int64               `json:"estimate_time_minutes"`
	DynamicTotalFee       float64             `json:"dynamic_total_fee"`
	BasicTotalFee         float64             `json:"basic_total_fee"`
	DynamicDiffPrice      float64             `json:"dynamic_diff_price"`
	EstimateFee           float64             `json:"estimate_fee"`
	CapPrice              float64             `json:"cap_price"`
	FinalCouponValue      string              `json:"final_coupon_value"`
	CouponInfo            string              `json:"coupon_info"`
	PayType               int32               `json:"pay_type"`
	DynamicInfo           string              `json:"dynamic_info"`
	SelectType            int32               `json:"select_type"`
	RecommendType         int32               `json:"recommend_type"`
	FormShowType          int32               `json:"form_show_type"`
	RouteId               string              `json:"route_id"`
}

func SendEstimateResKafka(ctx context.Context, products []*biz_runtime.ProductInfoFull, rspData *proto.NewFormEstimateV4Response) {
	var params []*EstimateResData
	for _, product := range products {
		if product == nil || product.Product == nil || product.GetPriceInfo() == nil ||
			product.GetBillInfo() == nil || rspData == nil {
			continue
		}

		if estimateData := rspData.EstimateData[product.GetProductCategory()]; estimateData != nil {
			param := buildMQDataForProduct(ctx, product, estimateData)
			if param != nil {
				params = append(params, param)
			}
		}
	}

	hashKey := rand.Int63n(16383)
	if diff.CheckDiffStatus(ctx) {
		diff.CheckDiffAndLog(ctx, diff.DownStreamDirpcType, "sendSync/"+ddmq.TopicWanliuPassengerEstimate, params)
		return
	}

	err := ddmq.Send(ctx, ddmq.TopicWanliuPassengerEstimate, params, hashKey, ddmq.TopicTypeWanliuPassengerEstimate)
	if err != nil {
		log.Trace.Warnf(ctx, consts.TagErrDDmq, "send req kafka msg fail with %v", err)
		return
	}
}

func buildMQDataForProduct(ctx context.Context, product *biz_runtime.ProductInfoFull, estimateData *proto.V3EstimateData) *EstimateResData {
	if product == nil || product.Product == nil || product.BaseReqData == nil || product.GetBillInfo() == nil || product.GetPaymentInfo() == nil {
		return nil
	}

	passengerInfo, areaInfo, commonInfo, billInfo, paymentInfo := product.BaseReqData.PassengerInfo, product.BaseReqData.AreaInfo, product.BaseReqData.CommonInfo, product.GetBillInfo(), product.GetPaymentInfo()
	estimateResData := EstimateResData{
		EstimateTraceId:       context2.GetTrace(ctx).GetTraceId(),
		PassengerPhone:        passengerInfo.Phone,
		PassengerId:           passengerInfo.PID,
		District:              areaInfo.District,
		Area:                  areaInfo.Area,
		Channel:               commonInfo.Channel,
		StartingLng:           areaInfo.FromLng,
		StartingLat:           areaInfo.FromLat,
		County:                areaInfo.FromCounty,
		DestLng:               areaInfo.ToLng,
		DestLat:               areaInfo.ToLat,
		ToCity:                areaInfo.ToArea,
		FromPoiId:             areaInfo.FromPoiID,
		ToPoiId:               areaInfo.ToPoiID,
		CreateTime:            time.Now().Unix(), // 这个得豁免
		ProductId:             product.Product.ProductID,
		SceneType:             product.Product.SceneType,
		CarType:               product.Product.RequireLevel,
		MultiRequireProduct:   "",
		PreferenceProduct:     "",
		IsAnycar:              0,
		NTuple:                product.GetNTuple(), // 假设有序列化好的json
		CallCar:               commonInfo.CallCarType,
		CurrentLng:            areaInfo.CurLng,
		CurrentLat:            areaInfo.CurLat,
		ClientType:            commonInfo.ClientType,
		PlatformType:          commonInfo.PlatformType,
		StartingName:          areaInfo.StartingName,
		DestName:              areaInfo.DestName,
		DepartureTime:         commonInfo.DepartureTime,
		OType:                 product.Product.OType,
		AppVersion:            commonInfo.AppVersion,
		OriginId:              commonInfo.OriginID,
		MenuId:                commonInfo.MenuID,
		BubbleId:              product.Product.EstimateID,
		EstimateId:            product.Product.EstimateID,
		ComboType:             product.Product.ComboType,
		EstimateDistanceMetre: billInfo.DriverMetre,
		EstimateTimeMinutes:   billInfo.DriverMinute,
		DynamicTotalFee:       billInfo.DynamicTotalFee,
		BasicTotalFee:         billInfo.BasicTotalFee,
		DynamicDiffPrice:      billInfo.DynamicDiffPrice,
		EstimateFee:           product.GetEstimateFee(),
		CapPrice:              billInfo.CapPrice,
		PayType:               paymentInfo.DefaultPayType,
		SelectType:            estimateData.IsSelected,
	}

	if len(estimateData.GetRouteIdList()) > 0 {
		estimateResData.RouteId = estimateData.GetRouteIdList()[0]
	}

	if product_id.IsFastCar(product.GetProductId()) {
		estimateResData.IsFastCar = 1
	}

	if product.GetDiscountSet() != nil && product.GetDiscountSet().Coupon != nil {
		estimateResData.FinalCouponValue = product.GetDiscountSet().Coupon.Amount
		bytes, err := json.Marshal(product.GetDiscountSet().Coupon)
		if err != nil {
			log.Trace.Warnf(ctx, trace.DLTagUndefined, "json marshal coupon err: %v", err)
		}
		estimateResData.CouponInfo = string(bytes)
	}

	bytes, err := json.Marshal(billInfo.DynamicInfo)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "json marshal dynamic_info err: %v", err)
	}
	estimateResData.DynamicInfo = string(bytes)

	return &estimateResData
}
