package estimate_v4_multi

import (
	"context"
	"errors"
	"git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v4_multi/experiment"
	"github.com/spf13/cast"
	"strconv"
	"time"

	BizError "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/page_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/controller/param_handler"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/uranus"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v4_multi/processor_registry"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/golibs/json"
)

type Service struct {
	Request           *proto.PMultiEstimatePriceV4Request
	productsGenerator *biz_runtime.ProductsGenerator
	productList       []*biz_runtime.ProductInfoFull // 中间数据
}

func NewService(ctx context.Context, req *proto.PMultiEstimatePriceV4Request) (*Service, BizError.BizError) {

	serviceReq := &param_handler.EstimateRequest{
		ReqFromParams: req,
	}
	handler := param_handler.NewHandler(serviceReq)
	bizErr := handler.Do(ctx, []param_handler.RequestWrapper{
		// param_handler.CheckRisk(ctx, carpoolRiskParamV2),    // 预估安全  todo:后续需要校验安全
		param_handler.GetUserInfo(ctx, req.Token, req.AccessKeyId), // 用户信息获取 + 校验
	})
	if bizErr != nil {
		return nil, bizErr
	}

	productsGenerator, err := InitProductGenerator(ctx, serviceReq)
	if err != nil {
		return nil, BizError.ErrSystem
	}

	return &Service{
		Request:           req,
		productsGenerator: productsGenerator,
	}, BizError.Success
}

func InitProductGenerator(ctx context.Context, estimateReq *param_handler.EstimateRequest) (*biz_runtime.ProductsGenerator, error) {
	var (
		err         error
		baseReqData *models.BaseReqData
		productsGen *biz_runtime.ProductsGenerator
	)

	request, ok := estimateReq.ReqFromParams.(*proto.PMultiEstimatePriceV4Request)
	if !ok {
		return nil, errors.New("request type error")
	}

	passenger := estimateReq.GetPassengerInfo()
	var departureTime int64
	if request.GetOrderType() == 0 {
		departureTime = time.Now().Unix()
	} else {
		departureTime = util.ToInt64(request.GetDepartureTime())
	}

	var userChangeTime bool
	if request.GetDepartureTime() == "" {
		userChangeTime = true
	}

	builder := models.NewBaseReqDataBuilder()
	if baseReqData, err = builder.
		SetClientInfo(&models.ClientInfo{
			AppVersion:        request.GetAppVersion(),
			AccessKeyID:       request.GetAccessKeyId(),
			Channel:           request.GetChannel(),
			SourceID:          0,
			Lang:              request.GetLang(),
			MenuID:            consts.MenuIDDaCheAnyCar,
			PageType:          page_type.PageTypeUndefined,
			ClientType:        request.GetClientType(),
			OriginID:          request.GetOriginId(), // 滴滴
			PlatformType:      request.GetPlatformType(),
			ScreenPixels:      request.GetPixels(),
			ScreenScale:       request.GetScreenScale(),
			TerminalID:        request.GetTerminalId(),
			EstimateStyleType: request.GetEstimateStyleType(),
			TabList:           request.GetTabList(),
			TabId:             request.GetTabId(),
			Xpsid:             request.GetXpsid(),
			XpsidRoot:         request.GetXpsidRoot(),
			Imei:              request.GetImei(),
			FormHeight:        request.GetFormHeight(),
			//BargainFrom:       "",
			//IsScanCode:        "",
			//ScanCodeShiftId:   "",
			//Dchn:              ,
			FromType: int16(request.GetFromType()),
			//GuideTraceId:   "",
			//IsBestShift:    0,
			//IsEstimateV2:   false,
			StopoverPoints: &request.StopoverPoints,
			OneStopVersion: request.GetOneStopVersion(),
			//IsDRN:           false,
			CategoryInfo: buildCategoryInfo(ctx, request.GetCategoryInfo()),
		}).
		SetUserOption(&models.UserOption{
			CallCarType:    request.GetCallCarType(),
			CallCarPhone:   request.GetCallCarPhone(),
			CarpoolSeatNum: request.GetCarpoolSeatNum(),
			OrderType:      request.GetOrderType(),
			DepartureTime:  departureTime,
			//DepartureRange:        nil,
			PaymentsType:        request.GetPaymentsType(),
			StopoverPoints:      &request.StopoverPoints,
			MultiRequireProduct: request.GetMultiRequireProduct(),
			//IsGuide:               nil,
			LuxurySelectCarlevels: request.GetLuxurySelectCarlevels(),
			LuxurySelectDriver:    request.GetLuxurySelectDriver(), // 豪华车偏好设置
			PreferredRouteId:      request.GetPreferredRouteId(),
			AdditionalService:     request.GetAdditionalService(),
			IsFemaleDriverFirst:   request.GetIsFemaleDriverFirst(),
			UserChangeTime:        userChangeTime,
		}).
		SetPassengerInfoV2(&models.PassengerInfoV2{
			UID:      int64(passenger.UID),
			PID:      int64(passenger.PID),
			Phone:    passenger.Phone,
			Role:     passenger.Role,
			Channel:  passenger.Channel,
			AppID:    passenger.AppID,
			UserType: request.GetUserType(),
			OriginID: passenger.OriginId,
			Token:    request.Token,
			//ExtPID:   "",
		}).
		SetGEOInfo(&models.GEOInfo{
			MapType:           request.GetMaptype(),
			CurrLat:           request.Lat,
			CurrLng:           request.Lng,
			Lat:               request.Lat,
			Lng:               request.Lng,
			FromLat:           request.FromLat,
			FromLng:           request.FromLng,
			FromPOIID:         request.FromPoiId,
			FromPOIType:       request.FromPoiType,
			FromAddress:       request.FromAddress,
			FromName:          request.FromName,
			ToLat:             request.ToLat,
			ToLng:             request.ToLng,
			ToPOIID:           request.GetToPoiId(),
			ToPOIType:         request.GetToPoiType(),
			ToAddress:         request.GetToAddress(),
			ToName:            request.GetToName(),
			StopoverPointInfo: request.GetStopoverPoints(),
			// MapInfoCacheToken:      "",
			// MapInfoStartCacheToken: "",
			// MapInfoDestCacheToken:  "",
			//RouteId:                request.GetPreferredRouteId(),
			ChooseFSearchid: request.GetChooseFSearchid(),
			ChooseTSearchid: request.GetChooseTSearchid(),
		}).
		TryBuild(ctx); err != nil {
		return nil, err
	}
	builder.Apply(ctx,
		applyFormABExperiments,
		buildLuxRequireProducts,
	)

	// 构建入参
	productsGen, err = biz_runtime.NewProductsGeneratorWithOption(
		biz_runtime.WithReqFrom("pEstimateV4"),
		biz_runtime.WithBaseReq(baseReqData),
	)
	if err != nil {
		return nil, err
	}

	productsGen.SetNeedMember(true)
	productsGen.SetNeedDegrade(true)
	productsGen.SetSendReqKafka(true)
	productsGen.SetNeedCarAggregation(true)
	productsGen.SetTriggerAthena(true)

	// 配置主流程
	if productsGen.BaseReqData != nil {
		// 通过处理器注册中心注册所有处理器
		processor_registry.RegisterAllProcessors(ctx, productsGen)
	}

	return productsGen, nil
}

// applyFormABExperiments 表单AB实验适配器（适配Apply方法）
func applyFormABExperiments(ctx context.Context, data *models.BaseReqData) {
	formABParam := experiment.GetNewStyleFormABParamFromBaseReqData(ctx, data)
	if formABParam.IsHitNewStyleForm() {
		// 设置新样式表单相关参数
		data.CommonBizInfo.FormStyleExp = formABParam.GetHitNewStyleForm()
	}
}

// buildCategoryInfo 构建分栏折叠状态信息
func buildCategoryInfo(ctx context.Context, categoryInfoStr string) map[int32]int32 {
	if categoryInfoStr == "" {
		return nil
	}

	var categoryInfoArray []map[string]interface{}
	err := json.Unmarshal([]byte(categoryInfoStr), &categoryInfoArray)
	if err != nil {
		return nil
	}

	categoryInfo := make(map[int32]int32)
	for _, info := range categoryInfoArray {
		if categoryId, ok := info["category_id"]; ok && categoryId != nil {
			categoryIdInt := cast.ToInt32(categoryId)
			if categoryIdInt != 0 {
				isFold := cast.ToInt32(info["is_fold"])
				categoryInfo[categoryIdInt] = util.GetOrDefault(isFold == 1, int32(1), int32(0))
			}
		}
	}
	return categoryInfo
}

func buildLuxRequireProducts(ctx context.Context, data *models.BaseReqData) {
	multiRequireProducts := data.CommonBizInfo.MultiRequireProduct
	if len(multiRequireProducts) == 0 {
		return
	}

	luxuryConfig := apollo.GetConfig(ctx, "luxury_carlevel_product_config", "luxury_carlevel_product_config")
	if luxuryConfig == nil {
		return
	}

	productInfoRaw := luxuryConfig["product_category"]
	carLevelInfoRaw := luxuryConfig["car_level"]
	productInfos := make(map[string]int)
	carLevelInfos := make(map[string]map[string]int64)

	err := json.Unmarshal([]byte(productInfoRaw), &productInfos)
	if err != nil {
		return
	}

	err = json.Unmarshal([]byte(carLevelInfoRaw), &carLevelInfos)
	if err != nil {
		return
	}

	for i := 0; i < len(multiRequireProducts); i++ {
		requireProduct := multiRequireProducts[i]
		if productId, ok := productInfos[strconv.FormatInt(requireProduct.ProductCategory, 10)]; ok {
			carLevels := carLevelInfos[strconv.Itoa(productId)]
			luxurySelectCarlevels := getLuxSelectInfos(ctx, data, &requireProduct)
			for carLevel, pcid := range carLevels {
				if len(luxurySelectCarlevels) > 0 && carLevel == luxurySelectCarlevels[0] {
					requireProduct.ProductCategory = pcid
					data.CommonBizInfo.MultiRequireProduct[i] = requireProduct
					break
				}
			}
		}
	}

}

func getLuxSelectInfos(ctx context.Context, data *models.BaseReqData, product *models.RequireProduct) []string {
	var luxSelectCarlevel []string
	var selectedDriver string
	var selectCarlevel string

	// 老版本 从入参获取选项
	if !isSupport(ctx, data) {
		selectedDriver = data.CommonInfo.LuxurySelectDriver
		selectCarlevel = data.CommonInfo.LuxurySelectCarlevels
	} else {
		// 新版 豪华车
		selectedDriver = product.LuxurySelectDriver
		selectCarlevel = product.LuxurySelectCarlevel
	}

	// 任意司务员 传给下游会计费 需置空
	if selectedDriver == "0" {
		data.CommonInfo.LuxurySelectDriver = ""
	}

	if !isSupport(ctx, data) || !isSixLux(ctx, product.ProductCategory) {
		// 有选择司务员 且不是任意司务员（"0"）
		if "" != selectedDriver && "0" != selectedDriver {
			if isSixLux(ctx, product.ProductCategory) {
				data.CommonInfo.SixSeatDesignatedDriver = selectedDriver
			} else {
				data.CommonInfo.DesignatedDriver = selectedDriver
			}
			// driverId小于0是通用司务员，不用在意其车型
			if driverId, err := strconv.ParseInt(selectedDriver, 10, 64); err == nil && driverId > 0 {
				driverBizInfo, err := uranus.GetDriverBizInfo(ctx, driverId)
				if err == nil && driverBizInfo != nil {
					if "" != driverBizInfo.CarLevel {
						data.CommonInfo.SelectedCarlevel = driverBizInfo.CarLevel
						luxSelectCarlevel = []string{driverBizInfo.CarLevel}
						// 使用司务员车型 更新 已选车型
						if level, err := strconv.Atoi(driverBizInfo.CarLevel); err == nil {
							if carlevel, err := json.Marshal([]int{level}); err == nil {
								data.CommonInfo.LuxurySelectCarlevels = string(carlevel)
							}
						}
						return luxSelectCarlevel
					}

				}
			}
		}
		// 没有选择司务员 使用勾选车型level
		if !isSupport(ctx, data) {
			// 老版本是数组 "[1405]"
			var tmp []int
			_ = json.Unmarshal([]byte(selectCarlevel), &tmp)
			if len(tmp) > 0 {
				luxSelectCarlevel = []string{strconv.Itoa(tmp[0])}
			}

		} else {
			// 新版是字符串 "1405"
			luxSelectCarlevel = []string{selectCarlevel}
		}

		if len(luxSelectCarlevel) > 0 {
			data.CommonInfo.SelectedCarlevel = luxSelectCarlevel[0]
		}

		return luxSelectCarlevel

	} else {
		// 如果新版六座豪华车
		// 有选择司务员 且不是任意司务员（"0"）
		if "" != selectedDriver && "0" != selectedDriver {
			data.CommonInfo.SixSeatDesignatedDriver = selectedDriver
			// driverId小于0是通用司务员，不用在意其车型
			if driverId, err := strconv.ParseInt(selectedDriver, 10, 64); err == nil && driverId > 0 {
				driverBizInfo, err := uranus.GetDriverBizInfo(ctx, driverId)
				if err == nil && driverBizInfo != nil {
					if "" != driverBizInfo.CarLevel {
						data.CommonInfo.SixSeatSelectedCarlevel = driverBizInfo.CarLevel
						luxSelectCarlevel = []string{driverBizInfo.CarLevel}
						return luxSelectCarlevel
					}
				}
			}
		}
		// 没有选择司务员 使用勾选车型level
		data.CommonInfo.SixSeatSelectedCarlevel = selectCarlevel
		luxSelectCarlevel = []string{selectCarlevel}

		return luxSelectCarlevel
	}

}

func isSupport(ctx context.Context, data *models.BaseReqData) bool {
	// 参数未完全初始化，拿不全，只拿到（city, appversion, accesskeyid, lang）此处够用
	ABparam := data.GetApolloParam()
	return apollo.FeatureToggle(ctx, "six_seat_lux_toggle", "", ABparam)
}

func isSixLux(ctx context.Context, pcid int64) bool {
	return apollo.FeatureToggle(ctx, "six_seat_lux_judge", "", map[string]string{"product_category": strconv.FormatInt(pcid, 10)})
}

func (s *Service) Estimate(ctx context.Context) error {
	var (
		products []*biz_runtime.ProductInfoFull
	)

	products, err := s.productsGenerator.GenProducts(ctx)
	if len(products) == 0 || err != nil {
		if errors.Is(err, consts.ErrorGetFromDDSFail) {
			return BizError.ErrEstimateDegradeProductsFail
		}

		if errors.Is(err, consts.ErrorEmptyPriceReq) {
			return BizError.ErrEstimateDegradePriceEmpty
		}

		return err
	}

	// 用于mock异常场景，提供给端QA回归的能力
	if bizError := s.mockExceptionReturn(ctx, products); bizError != nil {
		return bizError
	}

	s.productList = products
	return BizError.Success
}

func (s *Service) mockExceptionReturn(ctx context.Context, products []*biz_runtime.ProductInfoFull) BizError.BizError {
	apolloKey, apolloParams := products[0].GetApolloParams(biz_runtime.WithPIDKey)
	isHit, params := apollo.GetParameters("gs_mock_596_toggle", apolloKey, apolloParams)
	if isHit {
		mockErrno := cast.ToInt(params["mock_errno"])
		if mockErrno == BizError.EstimateDegradeProductsFail {
			return BizError.ErrEstimateDegradeProductsFail
		}
	}

	return nil
}

func (s *Service) GetProductList() []*biz_runtime.ProductInfoFull {
	return s.productList
}

func (s *Service) GetBaseReq() *models.BaseReqData {
	return s.productsGenerator.BaseReqData
}
