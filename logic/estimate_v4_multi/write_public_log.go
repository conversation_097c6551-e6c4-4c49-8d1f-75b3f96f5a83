package estimate_v4_multi

import (
	"context"
	"encoding/json"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/s3e/common-lib/v2/component/diff"
	"strconv"
	"strings"

	"git.xiaojukeji.com/gulfstream/mamba/common/logutil"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	LegoContext "git.xiaojukeji.com/lego/context-go"
)

const OperaKeyEstimateData = "g_order_estimate_price"
const OperaKeyLayoutData = "g_order_estimate_data_layout"
const OperaKeyRealTimeMonitor = "g_realtime_estimate_price"
const OperaKeyEstimateInfo = "g_order_estimate_info"
const OperaKeyEstimateOverallPrice = "g_estimate_overall_price"

// ClassifyFirstScreen UI元素类型常量
const (
	CarpoolTwoLine      = 1 // 拼车2行
	CarpoolThreeLine    = 2 // 拼车3行
	NormalTwoLine       = 3 // 车型2行
	NormalThreeLine     = 4 // 车型3行
	NormalTwoLineMore   = 5 // 车型2行带下挂
	NormalThreeLineMore = 6 // 车型3行带下挂
	FormSectionTitle    = 7 // 分框标题
	ThemeRecommendation = 8 // 推荐置顶包框
)

// 设备类型常量
const (
	DidiIOSPassengerApp     = 10000
	DidiAndroidPassengerApp = 20000
	DidiWechatMiniProgram   = 90000
	DidiAlipayMiniProgram   = 90001
)

// 屏幕尺寸常量
const (
	MediumScreen = "medium"
	LargeScreen  = "large"
)

type ProductShow struct {
	selectType    int
	formShowType  int
	recommendType int
}

// ClassifyFirstScreen 首屏产品计算器
type ClassifyFirstScreen struct {
	ctx         context.Context
	layouts     []*proto.NewFormLayout
	productMap  map[string]*biz_runtime.ProductInfoFull
	pixels      string
	screenScale float64
	accessKeyId int64

	// 不同设备的表单占比
	iosFormRatio     map[string]float64
	androidFormRatio map[string]float64
	miniFormRatio    map[string]float64

	// 小程序底部菜单高度
	miniBottomMenu map[string]int

	// NA元素高度（原版）
	naElementsHeight map[int]int

	// NA元素高度（新版）
	naElementsHeightNew map[int]int

	// 小程序元素高度（原版）
	miniElementsHeight map[int]int

	// 小程序元素高度（新版）
	miniElementsHeightNew map[int]int

	// 选中带下挂的品类
	normalSelectWithBottom []int64

	// 带下挂的品类
	normalWithBottom []int64
}

// NewClassifyFirstScreen 创建首屏产品计算器
func NewClassifyFirstScreen(ctx context.Context, layouts []*proto.NewFormLayout, productMap map[string]*biz_runtime.ProductInfoFull) *ClassifyFirstScreen {
	var pixels string
	var screenScale float64
	var accessKeyId int32

	// 从产品信息中获取设备信息
	for _, product := range productMap {
		if product != nil && product.BaseReqData != nil {
			pixels = product.BaseReqData.CommonInfo.ScreenPixels
			screenScale = product.BaseReqData.CommonInfo.ScreenScale
			accessKeyId = product.BaseReqData.CommonInfo.AccessKeyID
			break
		}
	}

	fs := &ClassifyFirstScreen{
		ctx:         ctx,
		layouts:     layouts,
		productMap:  productMap,
		pixels:      pixels,
		screenScale: screenScale,
		accessKeyId: int64(accessKeyId), // 转换为int64
	}

	fs.initFormRatios()
	fs.initElementHeights()
	fs.initProductCategories()

	return fs
}

// initFormRatios 初始化表单占比
func (fs *ClassifyFirstScreen) initFormRatios() {
	fs.iosFormRatio = map[string]float64{
		MediumScreen: 0.48,
		LargeScreen:  0.58,
	}

	fs.androidFormRatio = map[string]float64{
		MediumScreen: 0.48,
		LargeScreen:  0.58,
	}

	fs.miniFormRatio = map[string]float64{
		MediumScreen: 0.64,
		LargeScreen:  0.71,
	}

	fs.miniBottomMenu = map[string]int{
		MediumScreen: 110,
		LargeScreen:  134,
	}
}

// initElementHeights 初始化元素高度
func (fs *ClassifyFirstScreen) initElementHeights() {
	// NA元素高度（原版）
	fs.naElementsHeight = map[int]int{
		CarpoolTwoLine:      50,
		CarpoolThreeLine:    60,
		NormalTwoLine:       50,
		NormalThreeLine:     56,
		NormalTwoLineMore:   88,
		NormalThreeLineMore: 98,
		FormSectionTitle:    32,
		ThemeRecommendation: 54,
	}

	// NA元素高度（新版）
	fs.naElementsHeightNew = map[int]int{
		CarpoolTwoLine:      50,
		CarpoolThreeLine:    60,
		NormalTwoLine:       50,
		NormalTwoLineMore:   88,
		FormSectionTitle:    32,
		ThemeRecommendation: 54,
	}

	// 小程序元素高度（原版）
	fs.miniElementsHeight = map[int]int{
		CarpoolTwoLine:      50,
		CarpoolThreeLine:    67,
		NormalTwoLine:       50,
		NormalThreeLine:     56,
		NormalTwoLineMore:   86,
		NormalThreeLineMore: 94,
		FormSectionTitle:    32,
		ThemeRecommendation: 54,
	}

	// 小程序元素高度（新版）
	fs.miniElementsHeightNew = map[int]int{
		CarpoolTwoLine:      50,
		CarpoolThreeLine:    65,
		NormalTwoLine:       50,
		NormalTwoLineMore:   86,
		FormSectionTitle:    32,
		ThemeRecommendation: 54,
	}
}

// initProductCategories 初始化产品分类
func (fs *ClassifyFirstScreen) initProductCategories() {
	// 选中带下挂的品类（对应PHP中的PRODUCT_CATEGORY_PREMIUM_*等）
	fs.normalSelectWithBottom = []int64{102, 103, 104, 105}

	// 带下挂的品类
	fs.normalWithBottom = []int64{106}
}

// GetFirstScreenProducts 获取首屏产品（主要方法）
func (fs *ClassifyFirstScreen) GetFirstScreenProducts() []*proto.NewFormLayout {
	width, height := fs.getHeightWidth()
	if fs.screenScale == 0 {
		return fs.layouts
	}

	// 计算屏幕高度
	screenHeight := float64(height) / fs.screenScale
	if screenHeight == 0 {
		return fs.layouts
	}

	// 判断屏幕尺寸
	screenSize := fs.getScreenSize(width, height)

	// 根据设备类型获取表单占比和底部菜单高度
	var formRatio float64
	var bottom int

	switch fs.accessKeyId {
	case DidiIOSPassengerApp:
		formRatio = fs.iosFormRatio[screenSize]
		bottom = 0
	case DidiAndroidPassengerApp:
		formRatio = fs.androidFormRatio[screenSize]
		bottom = 0
	case DidiWechatMiniProgram, DidiAlipayMiniProgram:
		formRatio = fs.miniFormRatio[screenSize]
		bottom = fs.miniBottomMenu[screenSize]
	default:
		formRatio = 0.48
		bottom = 0
	}

	// 计算表单高度
	formHeight := screenHeight*formRatio - float64(bottom)

	// 获取元素高度配置
	elementHeight := fs.getElementHeight()

	// 获取默认选中的分框ID（简化实现，默认为1）
	selectedCategoryID := int32(1)

	// 计算首屏产品
	return fs.calculateFirstScreen(formHeight, elementHeight, selectedCategoryID)
}

// getHeightWidth 获取屏幕宽高
func (fs *ClassifyFirstScreen) getHeightWidth() (int, int) {
	if fs.pixels == "" {
		return 0, 0
	}

	parts := strings.Split(fs.pixels, "*")
	if len(parts) != 2 {
		return 0, 0
	}

	width, _ := strconv.Atoi(strings.TrimSpace(parts[0]))
	height, _ := strconv.Atoi(strings.TrimSpace(parts[1]))

	return width, height
}

// getScreenSize 获取屏幕尺寸类型
func (fs *ClassifyFirstScreen) getScreenSize(width, height int) string {
	// 简化的屏幕尺寸判断逻辑，实际应该根据具体业务规则
	if width >= 414 && height >= 896 {
		return LargeScreen
	}
	return MediumScreen
}

// getElementHeight 获取元素高度配置
func (fs *ClassifyFirstScreen) getElementHeight() map[int]int {
	// 简化实现，实际应该根据Apollo开关判断是否使用新版高度
	isMini := fs.accessKeyId == DidiWechatMiniProgram || fs.accessKeyId == DidiAlipayMiniProgram

	// 这里简化处理，实际应该调用Apollo开关判断
	isNewForm := false // 应该从Apollo获取 TRIPCLOUD_NEW_FORM 开关状态

	if isMini {
		if isNewForm {
			return fs.miniElementsHeightNew
		}
		return fs.miniElementsHeight
	}

	if isNewForm {
		return fs.naElementsHeightNew
	}
	return fs.naElementsHeight
}

// calculateFirstScreen 计算首屏产品
func (fs *ClassifyFirstScreen) calculateFirstScreen(formHeight float64, elementHeight map[int]int, selectedCategoryID int32) []*proto.NewFormLayout {
	threshold := formHeight
	hitSelected := false
	totalHeight := 0.0
	screenFull := false
	var currentID int32

	// 按顺序计算高度（置顶情况）
	for layoutIdx, layout := range fs.layouts {
		// 简化的分框ID获取，实际应该从layout中获取
		layoutCategoryID := int32(layoutIdx + 1) // 简化处理

		if !hitSelected && layoutCategoryID == selectedCategoryID {
			currentID = layoutCategoryID
			hitSelected = true
		}

		// 非默勾且在排序在前的分框不用看
		if !hitSelected {
			continue
		}

		// 分框高度计算
		if currentID != layoutCategoryID {
			totalHeight += float64(elementHeight[FormSectionTitle])
			currentID = layoutCategoryID
		}

		// 增加推荐置顶包框高度
		if fs.getThemeType(layout) == ThemeRecommendation {
			totalHeight += float64(elementHeight[ThemeRecommendation])
		}

		if totalHeight >= threshold {
			screenFull = true
			break
		}

		// 处理groups - 简化实现，不设置IsFirstScreen字段
		for _, group := range layout.Groups {
			if len(group.Products) == 0 {
				continue
			}

			pcID, _ := strconv.ParseInt(group.Products[0], 10, 64)
			groupHeight := fs.calcHeight(elementHeight, pcID, group.IsSelected, group.Type)
			totalHeight += float64(groupHeight)

			if totalHeight >= threshold {
				screenFull = true
				break
			}
		}

		if screenFull {
			break
		}
	}

	if screenFull {
		return fs.layouts
	}

	// 交互为置底情况 - 从底部向上铺
	fs.calculateFromBottom(threshold, elementHeight)

	return fs.layouts
}

// calculateFromBottom 从底部向上计算
func (fs *ClassifyFirstScreen) calculateFromBottom(threshold float64, elementHeight map[int]int) {
	totalHeightFromBottom := 0.0
	layoutLength := len(fs.layouts)

	if layoutLength == 0 {
		return
	}

	currentID := int32(layoutLength) // 简化处理

	// 倒序遍历
	for i := layoutLength - 1; i >= 0; i-- {
		layoutCategoryID := int32(i + 1) // 简化处理

		// 分框高度计算
		if layoutCategoryID != currentID {
			totalHeightFromBottom += float64(elementHeight[FormSectionTitle])
			currentID = layoutCategoryID
		}

		if totalHeightFromBottom >= threshold {
			break
		}

		groupLength := len(fs.layouts[i].Groups)
		for j := groupLength - 1; j >= 0; j-- {
			group := fs.layouts[i].Groups[j]
			if len(group.Products) == 0 {
				continue
			}

			pcID, _ := strconv.ParseInt(group.Products[0], 10, 64)
			groupHeight := fs.calcHeight(elementHeight, pcID, group.IsSelected, group.Type)
			totalHeightFromBottom += float64(groupHeight)

			if totalHeightFromBottom >= threshold {
				return
			}
		}

		// 增加推荐置顶包框高度
		if fs.getThemeType(fs.layouts[i]) == ThemeRecommendation {
			totalHeightFromBottom += float64(elementHeight[ThemeRecommendation])
		}
	}
}

// calcHeight 计算单个元素高度
func (fs *ClassifyFirstScreen) calcHeight(elementHeight map[int]int, pcID int64, isSelected int32, groupType int32) int {
	// 对应PHP中的各种GROUP_TYPE常量，这里简化处理
	singleGroupTypes := []int32{1, 99, 100, 101} // 简化的组类型判断

	if isSelected == 1 && !fs.contains(singleGroupTypes, groupType) {
		// 盒子选中带下挂
		return elementHeight[NormalTwoLineMore]
	} else if isSelected == 1 && fs.containsInt64(fs.normalSelectWithBottom, pcID) {
		// 选中带下挂
		return elementHeight[NormalTwoLineMore]
	} else if fs.containsInt64(fs.normalWithBottom, pcID) {
		// 带下挂
		return elementHeight[NormalTwoLineMore]
	}

	// 兜底车型两行
	return elementHeight[NormalTwoLine]
}

// getThemeType 获取主题类型
func (fs *ClassifyFirstScreen) getThemeType(layout *proto.NewFormLayout) int {
	if layout == nil || layout.ThemeData == nil {
		return 0
	}

	// 这里需要根据实际的ThemeData结构获取theme_type
	// 简化处理，返回0
	return 0
}

// contains 检查int32切片是否包含指定值
func (fs *ClassifyFirstScreen) contains(slice []int32, item int32) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// containsInt64 检查int64切片是否包含指定值
func (fs *ClassifyFirstScreen) containsInt64(slice []int64, item int64) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// processFirstScreenProducts 处理首屏产品逻辑（更新版本）
func processFirstScreenProducts(ctx context.Context, layouts []*proto.NewFormLayout, productMap map[string]*biz_runtime.ProductInfoFull) []*proto.NewFormLayout {
	if len(layouts) == 0 {
		return layouts
	}

	// 创建ClassifyFirstScreen实例
	classifier := NewClassifyFirstScreen(ctx, layouts, productMap)

	// 使用完整的首屏计算逻辑
	return classifier.GetFirstScreenProducts()
}

func AddPublicLog(ctx context.Context, products []*biz_runtime.ProductInfoFull, rspData *proto.NewFormEstimateV4Response) {
	// 压测流量不写Public日志
	if util.IsPressureTraffic(ctx) {
		return
	}

	productMap := make(map[string]*biz_runtime.ProductInfoFull, len(products))
	for _, product := range products {
		if product == nil || "" == product.Product.EstimateID {
			continue
		}
		productMap[strconv.FormatInt(product.GetProductCategory(), 10)] = product
	}

	// 记录预估阶段最终会展示的产品线 (分品类记录)
	writeOrderEstimateInfo(ctx, products, rspData)

	// 写预估public日志 (分品类记录)
	writeOrderEstimatePrice(ctx, products, rspData)

	// 监控日志
	writeLogForRealTimeMonitor(ctx, products)

	// 写预估样式信息, 品类外信息
	writeEstimateExtraInfo(ctx, products, rspData)

	// 写表单盒子、推荐数据
	writeLayoutInfo(ctx, rspData, productMap)
}

// writeOrderEstimateInfo 记录预估阶段最终会展示的产品线 (分品类记录)
func writeOrderEstimateInfo(ctx context.Context, products []*biz_runtime.ProductInfoFull, rspData *proto.NewFormEstimateV4Response) {
	diffRequestMap := make(map[int64]map[string]interface{})
	for _, product := range products {
		if product == nil || product.BaseReqData == nil || product.Product == nil {
			continue
		}

		logInfo := make(map[string]interface{})
		logInfo["opera_stat_key"] = OperaKeyEstimateInfo
		logInfo["pLang"] = product.BaseReqData.CommonInfo.Lang
		logInfo["estimate_id"] = product.Product.ProductCategory
		logInfo["area"] = product.BaseReqData.AreaInfo.Area
		logInfo["product_id"] = product.Product.ProductID
		logInfo["require_level"] = product.Product.RequireLevel

		// 获取select_type
		if rspData != nil && len(rspData.EstimateData) > 0 && rspData.EstimateData[product.Product.ProductCategory] != nil {
			logInfo["select_type"] = rspData.EstimateData[product.Product.ProductCategory].IsSelected
		}

		// 获取sub_group_id
		logInfo["sub_group_id"] = product.GetSubGroupId()

		// 如果是比diff，不落实际日志，只比对内容
		if diff.CheckDiffStatus(ctx) {
			diffRequestMap[product.Product.ProductCategory] = logInfo
			continue
		}

		log.Public.Public(ctx, OperaKeyEstimateInfo, logInfo)
	}

	diff.CheckDiffAndLog(ctx, diff.DownStreamPublicType, "log/Public/g_order_estimate_info", diffRequestMap)
	return
}

// writeOrderEstimatePrice 写预估public日志 (分品类记录)
func writeOrderEstimatePrice(ctx context.Context, products []*biz_runtime.ProductInfoFull, rspData *proto.NewFormEstimateV4Response) {
	diffRequestMap := make(map[int64]map[string]interface{})
	for _, product := range products {
		if product == nil || product.BaseReqData == nil || product.Product == nil ||
			product.GetBillInfo() == nil {
			continue
		}

		logInfo := logutil.GenerateCommonProductMap(ctx, product)
		logInfo["opera_stat_key"] = OperaKeyEstimateData

		// 价格标签曝光埋点
		if rspData != nil && len(rspData.EstimateData) > 0 && rspData.EstimateData[product.Product.ProductCategory] != nil {
			logInfo["select_type"] = rspData.EstimateData[product.Product.ProductCategory].IsSelected
			logInfo["is_support_multi_selection"] = rspData.IsSupportMultiSelection
		}

		if rspData != nil && len(rspData.EstimateData) > 0 && rspData.EstimateData[product.Product.ProductCategory] != nil && len(rspData.EstimateData[product.Product.ProductCategory].FeeDescList) > 0 {
			priceInfoDesc, err := json.Marshal(rspData.EstimateData[product.Product.ProductCategory].FeeDescList)
			if err == nil {
				logInfo["price_info_desc"] = string(priceInfoDesc)
			}
		}

		if estimateFee, ok := product.GetCarpoolFailEstimateFee(); product.Product.IsDualCarpoolPrice && ok {
			logInfo["carpool_fail_discount_fee"] = estimateFee
			if product.GetCarpoolFailRawBill() != nil {
				logInfo["carpool_fail_price"] = product.GetCarpoolFailRawBill().DynamicTotalFee
			}
		}

		if product.GetMiniBusPreMatch() != nil && product.GetMiniBusPreMatch().ExtMap != nil {
			if longTripId, ok := product.GetMiniBusPreMatch().ExtMap["long_trip_id"]; ok {
				logInfo["long_trip_id"] = longTripId
			}

			if ets, ok := product.GetMiniBusPreMatch().ExtMap["wait_answer_time"]; ok {
				logInfo["candidate_ets"] = ets
			}
		}

		if product.GetMiniBusPreMatch() != nil && product.GetMiniBusPreMatch().EtpInfo != nil {
			logInfo["etp_time_duration"] = product.GetMiniBusPreMatch().EtpInfo.EtpTimeDuration
		}

		logInfo["tab_product_count"] = len(products)

		// 如果是比diff，不落实际日志，只比对内容
		if diff.CheckDiffStatus(ctx) {
			diffRequestMap[product.Product.ProductCategory] = logInfo
			continue
		}

		log.Public.Public(ctx, OperaKeyEstimateData, logInfo)
	}

	diff.CheckDiffAndLog(ctx, diff.DownStreamPublicType, "log/Public/g_order_estimate_price", diffRequestMap)
}

// writeLogForRealTimeMonitor
func writeLogForRealTimeMonitor(ctx context.Context, products []*biz_runtime.ProductInfoFull) {
	diffRequestMap := make(map[int64]map[string]interface{})
	for _, product := range products {
		if product == nil || product.BaseReqData == nil || product.Product == nil || product.GetBillInfo() == nil {
			continue
		}

		logInfo := make(map[string]interface{})
		logInfo["opera_stat_key"] = OperaKeyRealTimeMonitor
		logInfo["app_version"] = product.BaseReqData.CommonInfo.AppVersion
		logInfo["client_type"] = product.BaseReqData.CommonInfo.ClientType
		logInfo["access_key_id"] = product.BaseReqData.CommonInfo.AccessKeyID
		logInfo["channel"] = product.BaseReqData.CommonInfo.Channel
		logInfo["page_type"] = product.BaseReqData.CommonInfo.PageType
		logInfo["origin_page_type"] = product.BaseReqData.CommonInfo
		logInfo["call_car_type"] = product.BaseReqData.CommonInfo.CallCarType
		logInfo["product_id"] = product.Product.ProductID
		logInfo["combo_type"] = product.Product.ComboType
		logInfo["require_level"] = product.Product.RequireLevel
		logInfo["area"] = product.BaseReqData.AreaInfo.Area
		logInfo["to_area"] = product.BaseReqData.AreaInfo.ToArea
		logInfo["county"] = product.BaseReqData.AreaInfo.FromCounty
		logInfo["to_county"] = product.BaseReqData.AreaInfo.ToCounty
		logInfo["order_type"] = product.Product.OrderType
		logInfo["estimate_id"] = product.GetEstimateID()
		logInfo["estimate_trace_id"] = LegoContext.GetTrace(ctx).GetTraceId()
		logInfo["is_special_price"] = product.Product.IsSpecialPrice
		logInfo["product_category"] = product.Product.ProductCategory
		logInfo["carpool_type"] = product.Product.CarpoolType

		if carpool.IsCarpoolUnSuccessFlatPrice(product) {
			dualCarpoolDiscount := logutil.GetDualCarpoolDiscount(ctx, product)
			if dualCarpoolDiscount != nil {
				// 拼成实付折扣
				logInfo["dual_success_actual_discount"] = util.FormatPrice(product.GetEstimateFee()/product.GetPreTotalFee(), 2)
				// 拼成应付折扣
				logInfo["dual_success_need_discount"] = util.FormatPrice(product.GetDynamicTotalFee()/product.GetPreTotalFee(), 2)
				// 拼不成实付折扣
				logInfo["dual_fail_actual_discount"] = util.FormatPrice(dualCarpoolDiscount.EstimateFee/dualCarpoolDiscount.BillInfo.PreTotalFee, 2)
				// 拼不成应付折扣
				logInfo["dual_fail_need_discount"] = util.FormatPrice(dualCarpoolDiscount.BillInfo.DynamicTotalFee/dualCarpoolDiscount.BillInfo.PreTotalFee, 2)
			}
		}

		// 如果是比diff，不落实际日志，只比对内容
		if diff.CheckDiffStatus(ctx) {
			diffRequestMap[product.Product.ProductCategory] = logInfo
			continue
		}

		log.Public.Public(ctx, OperaKeyRealTimeMonitor, logInfo)
	}

	diff.CheckDiffAndLog(ctx, diff.DownStreamPublicType, "log/Public/g_realtime_estimate_price", diffRequestMap)
}

// writeEstimateExtraInfo 写预估样式信息, 品类外信息
func writeEstimateExtraInfo(ctx context.Context, products []*biz_runtime.ProductInfoFull, rsp *proto.NewFormEstimateV4Response) {
	logInfo := make(map[string]interface{})
	logInfo["opera_stat_key"] = OperaKeyEstimateOverallPrice
	logInfo["area"] = products[0].BaseReqData.AreaInfo.Area
	logInfo["access_key_id"] = products[0].BaseReqData.CommonInfo.AccessKeyID
	logInfo["app_version"] = products[0].BaseReqData.CommonInfo.AppVersion
	logInfo["page_type"] = products[0].BaseReqData.CommonInfo.PageType
	logInfo["trace_id"] = LegoContext.GetTrace(ctx).GetTraceId()

	if rsp.OperationList != nil {
		for _, operationItem := range rsp.OperationList {
			if operationItem != nil && operationItem.Key == "ride_preference" {
				logInfo["ride_preference"] = 1
			}
		}
	}

	// 如果是比diff，不落实际日志，只比对内容
	if diff.CheckDiffStatus(ctx) {
		diff.CheckDiffAndLog(ctx, diff.DownStreamPublicType, "log/Public/g_estimate_overall_price", logInfo)
		return
	}

	log.Public.Public(ctx, OperaKeyEstimateOverallPrice, logInfo)
}

// writeLayoutInfo 写表单盒子、推荐数据
func writeLayoutInfo(ctx context.Context, rspData *proto.NewFormEstimateV4Response, productMap map[string]*biz_runtime.ProductInfoFull) {
	layouts := rspData.GetLayout()

	// 检查是否命中分类Tab，如果命中则处理首屏产品逻辑
	if len(productMap) > 0 {
		// 从任意一个产品中获取tabId
		var tabId string
		for _, product := range productMap {
			if product != nil && product.BaseReqData != nil {
				tabId = product.BaseReqData.CommonInfo.TabId
				break
			}
		}

		// 如果命中分类Tab，处理首屏产品
		if tab.IsClassifyTab(tabId) {
			layouts = processFirstScreenProducts(ctx, layouts, productMap)
		}
	}

	for curr, layout := range layouts {
		if layout == nil || layout.Groups == nil || len(layout.Groups) < 1 {
			continue
		}
		writeLayoutLog(ctx, layout, curr+1, productMap)
	}
}

func writeLayoutLog(ctx context.Context, layout *proto.NewFormLayout, curr int, productMap map[string]*biz_runtime.ProductInfoFull) {
	if layout == nil || len(layout.GetGroups()) < 1 {
		return
	}
	param := layout.GetGroups()[0]
	products := param.GetProducts()
	logInfo := make(map[string]interface{})
	logInfo["opera_stat_key"] = "g_order_estimate_data_layout"
	logInfo["sort"] = curr
	logInfo["form_item_type"] = param.GetType()
	logInfo["products"] = getProductsEID(param.GetProducts(), productMap)
	logInfo["form_show_type"] = layout.GetFormShowType()
	logInfo["box_id"] = param.GetBoxId()
	if len(products) > 0 && productMap[products[0]] != nil {
		logInfo["sub_group_id"] = productMap[products[0]].GetSubGroupId()
	} else {
		logInfo["sub_group_id"] = 0
	}
	logInfo["link_product"] = 0
	logInfo["box_price_info"] = param.GetFeeDesc()
	logInfo["group_id"] = param.GetGroupId()
	logInfo["is_vx_form"] = 4

	// 实现 is_first_screen 字段
	// 基本逻辑：当前排序位置是否在首屏显示范围内
	// 这里使用默认值6作为首屏显示数量，实际应该从配置或业务逻辑中获取
	showCarNum := 6 // 默认首屏显示数量，可以根据实际业务调整
	logInfo["is_first_screen"] = 0
	if curr <= showCarNum {
		logInfo["is_first_screen"] = 1
	}

	logInfo["action_type"] = 0  // 盒子点击跳转类型
	logInfo["style_type"] = 0   // 是否高峰期盒子
	logInfo["dialog_id"] = ""   // form athena
	logInfo["theme_style"] = "" // from athena
	logInfo["category_id"] = "" // 三方表单分类

	log.Public.Public(ctx, OperaKeyLayoutData, logInfo)
}

func getProductsEID(pcids []string, productMap map[string]*biz_runtime.ProductInfoFull) string {
	var res []string
	for _, pcid := range pcids {
		if productMap[pcid] != nil {
			res = append(res, productMap[pcid].GetEstimateID())
		}
	}
	return strings.Join(res, ",")
}
