package experiment

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"strconv"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/terminal/access_key_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/golibs/knife"
	"github.com/spf13/cast"
)

// 缓存键常量
const (
	GaiaFactor = "form_v7_new_style" // Gaia实验因子
)

// NewStyleFormABParam 新样式表单AB实验参数
type NewStyleFormABParam struct {
	HitNewStyleForm            int32 `json:"hit_new_style_form"`              // 是否命中新样式表单
	HideRecRightTag            bool  `json:"hide_rec_right_tag"`              // 是否隐藏推荐tag右标签
	HitPhoneAdaption           bool  `json:"hit_phone_adaption"`              // 是否大屏适配
	HitNewOrderButtonTitle     bool  `json:"hit_new_order_button_title"`      // 发单按钮文案
	HitNewNavigationBarNewSort bool  `json:"hit_new_navigation_bar_new_sort"` // 是否使用操作台特殊排序逻辑
}

// VERSION_REQUIREMENTS 版本要求映射
var NewStyleFormVersionRequirements = map[int]string{
	access_key_id.AccessKeyIdDidiIosPassengerApp:     "6.9.17",
	access_key_id.AccessKeyIdDidiAndroidPassengerApp: "6.9.17",
	access_key_id.AccessKeyIdDidiWeChatMiniProgram:   "6.9.75",
	access_key_id.AccessKeyIdDidiAlipayMiniProgram:   "6.9.75",
}

// GaiaConfig Gaia配置结构
type GaiaConfig struct {
	Param map[string]interface{} `yaml:"param" json:"param,omitempty"`
}

// getNewStyleFormABParam 获取新样式表单AB实验参数（支持请求级缓存）
func getNewStyleFormABParam(ctx context.Context, userId string, params map[string]string) *NewStyleFormABParam {

	// 执行实际的实验逻辑
	result := getNewStyleFormABParamInternal(ctx, userId, params)

	// 存储到context缓存
	knife.Set(ctx, consts.NewStyleFormAbKey, result)

	return result
}

// getNewStyleFormABParamInternal 内部实现，执行实际的实验逻辑
func getNewStyleFormABParamInternal(ctx context.Context, userId string, params map[string]string) *NewStyleFormABParam {
	result := &NewStyleFormABParam{
		HitNewStyleForm: 0,
		HideRecRightTag: false,
	}

	// 屏蔽非7.0表单 - 检查tab_id
	if tabId, ok := params["tab_id"]; !ok || !tab.IsClassifyTab(tabId) {
		return result
	}

	// 屏蔽大字版
	if fontScaleType, ok := params["font_scale_type"]; ok && cast.ToInt(fontScaleType) != 0 {
		return result
	}

	// 屏蔽page_type非0
	if pageType, ok := params["page_type"]; ok && cast.ToInt(pageType) != 0 {
		return result
	}

	// 版本限制
	var accessKeyID int
	var appVersion string
	if accessKeyIDS, ok := params["access_key_id"]; ok {
		accessKeyID = cast.ToInt(accessKeyIDS)
	}
	if appVersionS, ok := params["app_version"]; ok {
		appVersion = appVersionS
	}

	if !checkVersionRequirement(accessKeyID, appVersion) {
		return result
	}

	// 降级灰度
	if !apollo.FeatureToggle(ctx, "form_new_style_toggle", userId, params) {
		return result
	}

	// Gaia实验调用
	_, gaiaResult := apollo.FeatureGaia(ctx, GaiaFactor, params, "hit_new_style")
	if gaiaResult != nil {
		result.HitNewStyleForm = util.ToInt32(gaiaResult.GetParameter("hit_new_style", "0"))
		result.HideRecRightTag = gaiaResult.GetParameter("hit_hide_right_tag", "0") == "1"
		result.HitPhoneAdaption = gaiaResult.GetParameter("hit_phone_adaption", "0") == "1"
		result.HitNewOrderButtonTitle = gaiaResult.GetParameter("hit_new_order_button_title", "0") == "1"
		result.HitNewNavigationBarNewSort = gaiaResult.GetParameter("hit_new_navigation_bar_new_sort", "0") == "1"
	}

	return result
}

// checkVersionRequirement 检查版本要求
func checkVersionRequirement(accessKeyID int, appVersion string) bool {
	requiredVersion, exists := NewStyleFormVersionRequirements[accessKeyID]
	if !exists {
		return false
	}
	return util.CompareAppVersion(appVersion, requiredVersion) >= 0
}

// GetHitNewStyleForm 是否命中新样式表单
func (n *NewStyleFormABParam) GetHitNewStyleForm() int32 {
	return n.HitNewStyleForm
}

func (n *NewStyleFormABParam) IsHitNewStyleForm() bool {
	return n.HitNewStyleForm != 0
}

// IsHideRecRightTag 是否隐藏推荐右标签
func (n *NewStyleFormABParam) IsHideRecRightTag() bool {
	return n.HideRecRightTag
}

// IsHitPhoneAdaption 是否大屏适配
func (n *NewStyleFormABParam) IsHitPhoneAdaption() bool {
	return n.HitPhoneAdaption
}

// IsHitNewOrderButtonTitle 是否api下发发单按钮文案
func (n *NewStyleFormABParam) IsHitNewOrderButtonTitle() bool {
	return n.HitNewOrderButtonTitle
}

// IsHitNewNavigationBarNewSort 是否使用操作台特殊排序
func (n *NewStyleFormABParam) IsHitNewNavigationBarNewSort() bool {
	return n.HitNewNavigationBarNewSort
}

// GetNewStyleFormABParamFromBaseReqData 从BaseReqData获取新样式表单AB参数
func GetNewStyleFormABParamFromBaseReqData(ctx context.Context, baseReq *models.BaseReqData) *NewStyleFormABParam {
	if baseReq == nil {
		return &NewStyleFormABParam{
			HitNewStyleForm:            0,
			HideRecRightTag:            false,
			HitPhoneAdaption:           false,
			HitNewOrderButtonTitle:     false,
			HitNewNavigationBarNewSort: false,
		}
	}

	// 先从context缓存中获取
	if v := knife.Get(ctx, consts.NewStyleFormAbKey); v != nil {
		return v.(*NewStyleFormABParam)
	}

	// 构建参数map
	userId := strconv.FormatInt(baseReq.PassengerInfo.UID, 10)
	params := map[string]string{
		"pid":             strconv.FormatInt(baseReq.PassengerInfo.UID, 10),
		"uid":             strconv.FormatInt(baseReq.PassengerInfo.UID, 10),
		"phone":           baseReq.PassengerInfo.Phone,
		"city":            strconv.FormatInt(int64(baseReq.AreaInfo.City), 10),
		"access_key_id":   strconv.FormatInt(int64(baseReq.CommonInfo.AccessKeyID), 10),
		"app_version":     baseReq.CommonInfo.AppVersion,
		"lang":            baseReq.CommonInfo.Lang,
		"tab_id":          baseReq.CommonInfo.TabId,
		"page_type":       strconv.FormatInt(int64(baseReq.CommonInfo.PageType), 10),
		"order_type":      strconv.FormatInt(int64(baseReq.CommonInfo.OrderType), 10),
		"font_scale_type": strconv.FormatInt(int64(baseReq.CommonInfo.FontScaleType), 10),
	}

	return getNewStyleFormABParam(ctx, userId, params)
}
