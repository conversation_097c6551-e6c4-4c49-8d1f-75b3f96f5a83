package experiment

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/tab"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/tag"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/terminal/access_key_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/nuwa/golibs/knife"
	"github.com/spf13/cast"
)

// 缓存键常量
const (
	RecFormGaiaFactor = "form_v7_rec_tab" // Gaia实验因子
)

// ClassifyRecFormABParam 推荐区表单AB实验参数
type ClassifyRecFormABParam struct {
	HitClassifyRecTab bool `json:"hit_classify_rec_tab"` // 是否命中推荐表单
	MoveAllFoldTab    bool `json:"move_all_fold_tab"`    // 是否下移全部折叠tab
	ClickNeedExpand   bool `json:"click_need_expand"`    // 点击是否展开
}

// RecFormVersionRequirements 版本要求映射
var RecFormVersionRequirements = map[int]string{
	access_key_id.AccessKeyIdDidiIosPassengerApp:     "6.9.9",
	access_key_id.AccessKeyIdDidiAndroidPassengerApp: "6.9.9",
	access_key_id.AccessKeyIdDidiWeChatMiniProgram:   "6.9.35",
	access_key_id.AccessKeyIdDidiAlipayMiniProgram:   "6.9.35",
}

// GetClassifyRecFormABParam 获取推荐区表单AB实验参数（支持请求级缓存）
func GetClassifyRecFormABParam(ctx context.Context, baseReq *models.BaseReqData) *ClassifyRecFormABParam {
	// 先从context缓存中获取
	if v := knife.Get(ctx, consts.ClassifyRecFormAbKey); v != nil {
		return v.(*ClassifyRecFormABParam)
	}

	if baseReq == nil {
		return getDefaultRecFormABParam()
	}

	// 构建参数map
	userId := strconv.FormatInt(baseReq.PassengerInfo.UID, 10)
	params := map[string]string{
		"pid":             strconv.FormatInt(baseReq.PassengerInfo.PID, 10),
		"uid":             strconv.FormatInt(baseReq.PassengerInfo.UID, 10),
		"phone":           baseReq.PassengerInfo.Phone,
		"city":            strconv.FormatInt(int64(baseReq.AreaInfo.City), 10),
		"access_key_id":   strconv.FormatInt(int64(baseReq.CommonInfo.AccessKeyID), 10),
		"app_version":     baseReq.CommonInfo.AppVersion,
		"lang":            baseReq.CommonInfo.Lang,
		"tab_id":          baseReq.CommonInfo.TabId,
		"page_type":       strconv.FormatInt(int64(baseReq.CommonInfo.PageType), 10),
		"order_type":      strconv.FormatInt(int64(baseReq.CommonInfo.OrderType), 10),
		"font_scale_type": strconv.FormatInt(int64(baseReq.CommonInfo.FontScaleType), 10),
		"person_tag":      tag.GetPersonTag(ctx),
	}

	// 执行实际的实验逻辑
	result := getClassifyRecFormABParamInternal(ctx, userId, params)

	// 存储到context缓存
	knife.Set(ctx, consts.ClassifyRecFormAbKey, result)

	return result
}

// getClassifyRecFormABParamInternal 内部实现，执行实际的实验逻辑
func getClassifyRecFormABParamInternal(ctx context.Context, userId string, params map[string]string) *ClassifyRecFormABParam {
	result := getDefaultRecFormABParam()

	// 屏蔽page_type非0
	if pageType, ok := params["page_type"]; ok && cast.ToInt(pageType) != 0 {
		return result
	}

	// 屏蔽非7.0表单 - 检查tab_id
	if tabId, ok := params["tab_id"]; !ok || !tab.IsClassifyTab(tabId) {
		return result
	}

	// 屏蔽大字版
	if fontScaleType, ok := params["font_scale_type"]; ok && cast.ToInt(fontScaleType) != 0 {
		return result
	}

	// 屏蔽预约单
	if orderType, ok := params["order_type"]; ok && cast.ToInt(orderType) == 1 { // OrderSystem::TYPE_ORDER_BOOKING == 1
		return result
	}

	// 版本限制
	var accessKeyID int
	var appVersion string
	if accessKeyIDS, ok := params["access_key_id"]; ok {
		accessKeyID = cast.ToInt(accessKeyIDS)
	}
	if appVersionS, ok := params["app_version"]; ok {
		appVersion = appVersionS
	}

	if !checkRecFormVersionRequirement(accessKeyID, appVersion) {
		return result
	}

	// Gaia实验调用
	_, gaiaResult := apollo.FeatureGaia(ctx, RecFormGaiaFactor, params, "hit_rec_tab")
	if gaiaResult != nil {
		result.HitClassifyRecTab = gaiaResult.GetParameter("hit_rec_tab", "0") == "1"
		result.MoveAllFoldTab = gaiaResult.GetParameter("hit_move_all_fold_tab", "0") == "1"
		result.ClickNeedExpand = gaiaResult.GetParameter("click_need_expand", "0") == "1"
	}

	return result
}

// getDefaultRecFormABParam 获取默认的推荐区表单AB参数
func getDefaultRecFormABParam() *ClassifyRecFormABParam {
	return &ClassifyRecFormABParam{
		HitClassifyRecTab: false,
		MoveAllFoldTab:    false,
		ClickNeedExpand:   false,
	}
}

// checkRecFormVersionRequirement 检查版本要求
func checkRecFormVersionRequirement(accessKeyID int, appVersion string) bool {
	requiredVersion, exists := RecFormVersionRequirements[accessKeyID]
	if !exists {
		return false
	}
	return util.CompareAppVersion(appVersion, requiredVersion) >= 0
}

// IsHitClassifyRecTab 是否命中推荐表单
func (c *ClassifyRecFormABParam) IsHitClassifyRecTab() bool {
	return c.HitClassifyRecTab
}

// IsMoveAllFoldTab 是否下移全部折叠tab
func (c *ClassifyRecFormABParam) IsMoveAllFoldTab() bool {
	return c.MoveAllFoldTab
}

// IsClickNeedExpand 点击是否展开
func (c *ClassifyRecFormABParam) IsClickNeedExpand() bool {
	return c.ClickNeedExpand
}
