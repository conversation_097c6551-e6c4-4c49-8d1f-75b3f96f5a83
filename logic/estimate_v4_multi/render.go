package estimate_v4_multi

import (
	"context"
	"errors"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v4_multi/experiment"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/category_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/expect_params"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_msg_template"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/group_data"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/navigation_bar"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/operation_list"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/real_params"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/side_params"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/tab_extra_data"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/toast_tip"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/travel_fore_cast"
	"time"

	"git.xiaojukeji.com/gulfstream/biz-common-go/errcode"
	ProductCategory "git.xiaojukeji.com/gulfstream/biz-common-go/v6/category"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/carpool"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	pool2 "git.xiaojukeji.com/gulfstream/mamba/common/handlers/pool"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/redis"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/estimate_v4_multi/data"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/anycar_v3"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/estimate_v3"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/estimate_v4"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/carpool_seat_num"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/depart_tag"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/estimate_extra"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_detail_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/category_bargain"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_info_render/common_logic/fee_desc_list"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/fee_prefix_icon"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/hit_dynamic_price"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/is_hide_price"
	LayoutConsts "git.xiaojukeji.com/gulfstream/mamba/render/public/layout/consts"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/layout_v2"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/map_curve_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/map_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/multi_route_tip_type"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/multi_route_tips_data"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/new_order_params"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/notice_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/order_button_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/pay_info"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/prefer_data"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/radio_setting"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/route_id_list"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/selection_style_type"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/single_route"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/user_pay_info"
	Trace "git.xiaojukeji.com/nuwa/trace"
)

const EstimateIdCacheExpireTime = 6 * 60 * 60

type Render struct {
	req      *models.BaseReqData
	products []*biz_runtime.ProductInfoFull
}

func NewRender(list []*biz_runtime.ProductInfoFull, req *models.BaseReqData) (r *Render) {
	// 初始化配置
	r = &Render{
		req:      req,
		products: list,
	}
	r.preBuilder()
	return
}

func (r *Render) preBuilder() {
	var (
		fastPrice           float64
		pricingByMeterPrice float64
	)

	for _, pFull := range r.products {
		if pFull.GetProductCategory() == ProductCategory.ProductCategoryFast {
			fastPrice = pFull.GetEstimateFee()
		} else if pFull.GetProductCategory() == ProductCategory.ProductCategoryUnione {
			pricingByMeterPrice = pFull.GetEstimateFee()
		}
	}

	peakFee := models.NewPeakFee()
	for _, pFull := range r.products {
		pFull.BaseReqData.CommonBizInfo.FastCarPrice = fastPrice
		pFull.BaseReqData.CommonBizInfo.PricingByMeterPrice = pricingByMeterPrice
		pFull.BaseReqData.CommonBizInfo.PeakFee = peakFee
	}
}

func (r *Render) Render(ctx context.Context) (resp *proto.NewFormEstimateV4Response, err error) {
	var (
		estimateData  = make(map[int64]*proto.V3EstimateData)
		productMap    = make(map[int64]*biz_runtime.ProductInfoFull) // 存储已渲染的品类数据
		recProductMap = make(map[int64]*biz_runtime.ProductInfoFull)
	)

	defer func() {
		util.Go(ctx, func() {
			SendEstimateResKafka(ctx, r.products, resp)
			AddPublicLog(ctx, r.products, resp)
		})
	}()

	resp = &proto.NewFormEstimateV4Response{
		EstimateData: make(map[int64]*proto.V3EstimateData),
	}

	// 渲染单品类
	pool := pool2.NewPoolByTimeout(ctx, 500*time.Millisecond)
	for _, full := range r.products {
		pool.AddTask(&pool2.Task{
			Req:     full,
			RunTask: RenderByProduct,
		})
	}
	// 并行渲染
	_, err = pool.MultiProc()
	if err != nil {
		return resp, err
	}

	for _, task := range pool.GetResult() {
		if task.GetErr() != nil || task.Resp == nil {
			continue
		}
		pFull, ok1 := task.Req.(*biz_runtime.ProductInfoFull)
		if !ok1 {
			continue
		}

		if product, ok := task.Resp.(*proto.V3EstimateData); ok {
			pFull.Product.BizInfo.CheckStatus = int(product.IsSelected)
			productMap[product.ProductCategory] = pFull
			estimateData[product.ProductCategory] = product

			if pFull.GetBizInfo().IsRec {
				recProductMap[product.ProductCategory] = pFull
			}
		}
	}
	// 缓存多预估中各车型关联拼车的预估id
	r.setCarpoolCommendEstimateId(ctx)

	resp.EstimateData = estimateData

	// 渲染额外数据
	BuildExtraInfo(ctx, r.req, r.products, resp)

	resp.CategoryInfo = category_info.NewCategoryInfo(ctx, r.req, productMap).GetCategoryInfo(ctx)
	var simpleEstimateData = make(map[int64]*LayoutConsts.SimpleEstimateData)
	for product, item := range estimateData {
		simpleEstimateData[product] = &LayoutConsts.SimpleEstimateData{
			FeeDescList: item.FeeDescList,
		}
	}
	// 渲染layout
	resp.Layout = layout_v2.NewLayout(r.req, productMap, simpleEstimateData).BuildLayout(ctx)

	resp.RecLayout = layout_v2.NewLayout(r.req, recProductMap, simpleEstimateData).BuildLayout(ctx)

	resp.GroupData = group_data.NewGroupData(ctx).GetGroupData()

	return resp, nil
}
func RenderByProduct(ctx context.Context, input interface{}) (interface{}, error) {

	product, ok := input.(*biz_runtime.ProductInfoFull)
	if !ok {
		return nil, errors.New("product req error ok")
	}

	renderProvider := &data.EstimateV4Adapter{ProductInfoFull: product}

	feeInfo := estimate_v3.GetEstimateFeeInfo(ctx, renderProvider) // 对齐v3 && 大致修改好了
	// 基础信息
	productAfterRender := &proto.V3EstimateData{
		EstimateId:      product.GetEstimateID(),
		ProductCategory: product.GetProductCategory(),

		HitDynamicPrice:  hit_dynamic_price.IsHitDynamicPrice(ctx, renderProvider),
		HitShowH5Type:    estimate_extra.GetHitShowH5Type(ctx, product),
		IsTripcloud:      product.IsTripcloud(ctx),
		CarTitle:         car_info.GetCarNameV2(ctx, renderProvider),
		CarIcon:          car_info.GetCarIconV2(ctx, renderProvider),
		FeeMsgTemplate:   feeInfo.FeeMsgTemplate,
		FeeAmount:        feeInfo.FeeAmount,
		FeeMsg:           fee_info_render.GetEstimateFeeInfo(ctx, renderProvider).FeeMsg,
		MultiPriceList:   estimate_v3.GetMultiPriceList(ctx, renderProvider),
		NeedPayFeeAmount: category_bargain.GetNeedPayFeeAmount(ctx, renderProvider),

		UserPayInfo: pay_info.GetPayInfo(ctx, renderProvider),

		CarpoolSeatList:        carpool_seat_num.GetCarpoolSeatNumV3(ctx, renderProvider),
		CarpoolSeatModule:      nil, // 本次下线
		RouteIdList:            route_id_list.GetRouteIdList(ctx, renderProvider),
		IsSingleRoute:          single_route.GetIsSingleRoute(ctx, renderProvider),
		AutoDrivingAddressInfo: estimate_v4.GetAutoDrivingAddressInfo(ctx, renderProvider),
		RadioSetting:           radio_setting.GetOrderOption(ctx, renderProvider),
		IsHidePrice:            is_hide_price.GetResult(ctx, renderProvider),
		MinFeeAmount:           nil, // 特快range, 本次下线
		// FeeRangeTemplate:       &feeInfo.FeeRangeTemplate,                                  // 惠选车
		ExtraEstimateData: nil,
		SubIntroIcon:      nil, // 已下线
		OrderOption:       nil, // 已下线
		CarSubTitle:       nil, // 已下线
		TipsIcon:          nil, // 已下线
		SubTitle:          nil, // 已下线
		CarTag:            nil, // 已下线

		DepartTag: depart_tag.GetDepartTag(ctx, renderProvider),
		ExtraMap:  estimate_v3.GetExtraMap(ctx, renderProvider),

		MapInfo:           map_info.GetMapInfo(ctx, renderProvider),
		MapCurveInfo:      map_curve_info.GetMapCurveInfo(ctx, renderProvider),
		NoticeInfo:        notice_info.GetNoticeInfo(ctx, renderProvider),
		MultiRouteTipType: multi_route_tip_type.GetMultiRouteTipType(ctx, renderProvider),
		BargainRangePopup: anycar_v3.GetFastRangePopup(ctx, renderProvider),
		FeeDescList:       fee_desc_list.GetEstimateFeeDescListV3(ctx, renderProvider, 0),
		FeeMsgPrefixIcon:  fee_prefix_icon.GetFeeMsgPrefixIcon(ctx, renderProvider),
		IsSelected:        estimate_v4.GetSelection(ctx, renderProvider),
	}

	// 依赖前面的数据
	productAfterRender.PreferData = prefer_data.GetPreferData(ctx, renderProvider, productAfterRender)
	return productAfterRender, nil
}

func BuildExtraInfo(ctx context.Context, req *models.BaseReqData, products []*biz_runtime.ProductInfoFull, res *proto.NewFormEstimateV4Response) {
	renderProvider := &data.EstimateV4Adapter{ProductInfoFull: products[0]}
	// 基本信息
	res.EstimateTraceId = util.GetTraceIDFromCtxWithoutCheck(ctx)
	res.IsSupportMultiSelection = 1
	res.FeeDetailUrl = fee_detail_info.GetDetailUrl(ctx)
	res.SelectionStyleType = selection_style_type.GetSelectionStyleType(ctx, renderProvider)
	res.ShowCategory = util.Int32Ptr(1)

	// 支付方式
	res.UserPayInfo = user_pay_info.GetUserPayInfo(ctx, req, products)
	// 拦截页
	res.PluginPageInfo = estimate_extra.GetFullPluginPageInfo(ctx, products)

	// 操作区
	res.OperationList = operation_list.GetOperationListFormCache(ctx, renderProvider)
	res.NavigationBar = navigation_bar.GetNavigationBar(ctx, renderProvider, res.UserPayInfo)
	res.TravelForecast = travel_fore_cast.GetTravelForeCastFromCache(ctx, renderProvider) // 这个必须在NavigationBar后面构建
	res.IsCallcarDisabled = util.Int32Ptr(navigation_bar.NewCallCar(ctx, renderProvider).IsDisableCallCar)

	// 提示消息
	res.ToastTip = util.StringPtr(toast_tip.GetToastTip(ctx, renderProvider, products, res.EstimateData, res.UserPayInfo))
	res.MoreToastTip = toast_tip.GetMoreToastTip(ctx, renderProvider)

	// 参数，端上透传or使用
	res.TabExtraData = tab_extra_data.GetTabExtraData(ctx, products)
	res.PNewOrderParams = new_order_params.GetNewOrderParams(ctx, renderProvider, products, res.EstimateData)
	res.RealParams = real_params.GetRealParams(ctx, renderProvider)
	res.SideParams = side_params.GetSideParams(ctx, renderProvider, products)
	res.ExpectParams = expect_params.GetExpectParams(ctx, renderProvider)
	res.RecForm = util.Int32Ptr(renderProvider.GetRecForm())
	res.FormStyleExp = util.Int32Ptr(renderProvider.GetFormStyleExp())
	res.PhoneAdaptation = util.Int32Ptr(util.ToInt32(experiment.GetNewStyleFormABParamFromBaseReqData(ctx, req).IsHitPhoneAdaption()))

	// 发单按钮信息
	res.OrderButtonInfo = order_button_info.GetOrderButtonInfo(ctx, renderProvider)

	// 其他
	res.MultiRouteTipsData = multi_route_tips_data.GetMultiRouteTipsData(ctx)
	res.FeeMsgTemplate = util.StringPtr(fee_msg_template.GetFeeMsgTemplate(ctx, products))
}

// 缓存多预估中各车型关联拼车的预估id
func (r *Render) setCarpoolCommendEstimateId(ctx context.Context) {
	// TODO 原代码 _setEstimateIdCache 中其他逻辑已关量
	carPoolEstimateId := getCarpoolDualPriceEstimateID(r.products)
	fastCarEstimateId := getFastCarEstimateID(r.products)
	if carPoolEstimateId == "" || fastCarEstimateId == "" {
		return
	}

	key := getCarpoolEstimateCacheKey(fastCarEstimateId)
	//todo 确认下是不是这个 redis
	// 原代码:app/logics/v3Estimate/multiResponse/MainRender.php:746

	//  redis前缀值: P_ESTIMATE_ID_JOIN_CARPOOL_
	//  存储位置: (无显式声明, 默认)forever
	//  使用位置: 仅一处
	//      1. 等待接驾阶段, 乘客选择快车转拼车, 调用 dExchangeOrder 时读取
	//  快转拼相关wiki: http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=351386068
	_, err := redis.GetMultiEstimateClient().SetEx(ctx, key, EstimateIdCacheExpireTime, carPoolEstimateId)
	if err != nil {
		log.Trace.Warnf(ctx, Trace.DLTagUndefined, "errno=%s||errmsg=[err=%s,key=%s,value=%s]", errcode.ErrCommonRedisSetFail, err, key, carPoolEstimateId)
	}

}

func getCarpoolEstimateCacheKey(eid string) string {
	return fmt.Sprintf("p_estimate_id_join_carpool%s", eid)
}

func getCarpoolDualPriceEstimateID(products []*biz_runtime.ProductInfoFull) string {
	for _, product := range products {
		if product.GetProductCategory() == ProductCategory.ProductCategoryDefault {
			continue
		}

		if carpool.IsCarpoolDualPrice(int32(product.GetCarpoolType()), product.GetIsDualCarpoolPrice()) {
			return product.GetEstimateID()
		}
	}
	return ""
}

func getFastCarEstimateID(products []*biz_runtime.ProductInfoFull) string {
	for _, product := range products {
		if product.GetProductCategory() == ProductCategory.ProductCategoryDefault {
			continue
		}
		if product.GetProductCategory() == ProductCategory.ProductCategoryFast {
			return product.GetEstimateID()
		}
	}
	return ""
}
