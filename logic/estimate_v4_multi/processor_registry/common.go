package processor_registry

import (
	"context"
	rpc_process_v4 "git.xiaojukeji.com/gulfstream/mamba/models/product_filter/fill_product_info"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/classify"

	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common"

	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/athena_bubble_recommend"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process/common/tag"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
)

// registerCommonProcessors 注册通用逻辑处理器
// 所有车型都需要的基础处理逻辑
func registerCommonProcessors(ctx context.Context, productsGen *biz_runtime.ProductsGenerator) {
	// 人群判定
	if r := tag.NewPersonTag(productsGen.BaseReqData); r != nil {
		productsGen.RegisterAfterDdsNoBaseProductsRpcProcess(r)
	}

	/**--------------------------------dds之后带品类执行-----------------------------------------------------**/

	// 导流位赋值
	if r := rpc_process_v4.NewFillProductMetaInfo(ctx, productsGen.BaseReqData); r != nil {
		productsGen.RegisterAfterDdsFilter(r)
	}

	// 增值服务
	if ssseRPC := rpc_process.NewAdditionalServiceHundun(ctx, productsGen.BaseReqData); ssseRPC != nil {
		productsGen.RegisterBeforePriceRpcProcess(ssseRPC)
		productsGen.RegisterBeforePriceFilter(ssseRPC)
	}

	if r := rpc_process.NewGuideSceneRPC(ctx, productsGen.BaseReqData); r != nil {
		productsGen.RegisterBeforePriceRpcProcess(r)
	}

	// SPS 增值服务价格
	if spsRPC := rpc_process.NewSpsAddServicePrice(ctx, productsGen.BaseReqData); spsRPC != nil {
		productsGen.RegisterBeforePriceRpcProcess(spsRPC)
	}

	// Hilda权益卡
	if hildaRPC := common.NewHildaPrivilege(ctx, productsGen.BaseReqData); hildaRPC != nil {
		productsGen.RegisterBeforePriceRpcProcess(hildaRPC)
	}

	/**--------------------------------priceApi之后执行Athena之前-----------------------------------------------------**/

	// compensation
	if compensationRpc := rpc_process.NewCompensationTriggerTack(ctx, &productsGen.BaseReqData.CommonInfo, &productsGen.BaseReqData.AreaInfo, &productsGen.BaseReqData.PassengerInfo); compensationRpc != nil {
		productsGen.RegisterBeforeAthenaRPCProcess(compensationRpc)
	}
	// DDS Decision
	if r := rpc_process.NewDdsDecision(ctx, &productsGen.BaseReqData.CommonInfo, &productsGen.BaseReqData.AreaInfo, &productsGen.BaseReqData.PassengerInfo); r != nil {
		productsGen.RegisterBeforeAthenaRPCProcess(r)
		productsGen.RegisterAfterPriceFilter(r)
	}

	// 7.0分框逻辑
	if c := classify.NewClassifyCategoryConfig(ctx, productsGen.BaseReqData); c != nil {
		productsGen.RegisterBeforeAthenaRPCProcess(c)
	}

	/**--------------------------------冒泡推荐-----------------------------------------------------**/
	// 冒泡推荐
	//if recommendRPC := rpc_process.NewBubbleRecommendInfoRPC(productsGen.BaseReqData, rpc_process.EstimateOrderMatchType); recommendRPC != nil {
	//	productsGen.RegisterAfterPriceRPCProcess(recommendRPC)
	//	productsGen.RegisterFinalFilter(recommendRPC)
	//}

	if recommendRPC := athena_bubble_recommend.NewBubbleRecommendAdapter(productsGen.BaseReqData, rpc_process.EstimateOrderMatchType); recommendRPC != nil {
		productsGen.RegisterAfterPriceRPCProcess(recommendRPC)
		productsGen.RegisterFinalFilter(recommendRPC)
	}
}
