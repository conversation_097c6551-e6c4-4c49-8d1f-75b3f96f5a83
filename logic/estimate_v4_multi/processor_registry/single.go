package processor_registry

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_filter/after_dds_filter"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_filter/after_price_filter"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
)

// registerSingleProcessors 注册独乘逻辑处理器
// 独乘车型（专车、出租车等）专属的处理逻辑
func registerSingleProcessors(ctx context.Context, productsGen *biz_runtime.ProductsGenerator) {
	// 司乘议价补充信息
	if lFilter := rpc_process.NewBargainInfoLogic(ctx, "", productsGen.BaseReqData, "v3"); lFilter != nil {
		productsGen.RegisterAfterDdsFilter(lFilter)
		productsGen.RegisterBeforePriceRpcProcess(lFilter)
	}

	// 车大，香港品类
	if r := after_price_filter.NewAfterPriceFilter(ctx, productsGen.BaseReqData); r != nil {
		productsGen.RegisterAfterPriceFilter(r)
	}

	// === 出租车专属处理器 ===
	// 出租车峰期计价
	if taxiSps := rpc_process.NewTaxiPeakFee(ctx, productsGen.BaseReqData); taxiSps != nil {
		productsGen.RegisterBeforePriceRpcProcess(taxiSps)
	}
	// 出租车计价盒子实验
	if r := after_dds_filter.NewTaxiPricingEstimateFilter(ctx, productsGen.BaseReqData); r != nil {
		productsGen.RegisterAfterDdsFilter(r)
	}
	// 出租车处理互斥品类
	if mutexFilter := after_dds_filter.NewDealMutexFilter(ctx, productsGen.BaseReqData); mutexFilter != nil {
		productsGen.RegisterAfterDdsFilter(mutexFilter)
	}

	// === 独乘专属过滤器 ===
	// 远必省车型和普通三方车型的实验过滤
	if fpFilter := after_price_filter.NewFarMustCheapAgainstTCFilter(productsGen); fpFilter != nil {
		productsGen.RegisterAfterPriceFilter(fpFilter)
	}
	// 超值达盒子过滤
	if cpFilter := after_price_filter.NewChaoZhiDaFilter(); cpFilter != nil {
		productsGen.RegisterAfterPriceFilter(cpFilter)
	}

	if intercityFilter := after_price_filter.NewIntercityFilterForV4(ctx, productsGen); intercityFilter != nil {
		productsGen.RegisterAfterPriceFilter(intercityFilter)
	}
}
