package processor_registry

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_common/filter"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/product_filter/after_price_filter"
	rpc_process_v4 "git.xiaojukeji.com/gulfstream/mamba/models/product_filter/fill_product_info"
	"git.xiaojukeji.com/gulfstream/mamba/models/rpc_process"
)

// registerCarpoolProcessors 注册拼车相关逻辑处理器
// 主要针对拼车业务的特殊处理逻辑
func registerCarpoolProcessors(ctx context.Context, productsGen *biz_runtime.ProductsGenerator) {
	// CarpoolEtx 拼车ETS处理器
	if carpoolEtxRPC := rpc_process.NewCarpoolEtx(productsGen.BaseReqData, rpc_process.Sync); carpoolEtxRPC != nil {
		productsGen.RegisterBeforeAthenaRPCProcess(carpoolEtxRPC)
		productsGen.RegisterAfterPriceFilter(carpoolEtxRPC)
	}

	// 城际惊喜独享标识处理器
	if intercitySurpriseAlone := rpc_process.NewIntercitySurpriseAloneProcessor(ctx, productsGen.BaseReqData); intercitySurpriseAlone != nil {
		productsGen.RegisterBeforePriceRpcProcess(intercitySurpriseAlone)
	}

	// DUSE班次逻辑处理器
	if duseProcessor := rpc_process.NewDuseBusShiftProcessor(ctx, productsGen.BaseReqData); duseProcessor != nil {
		productsGen.RegisterBeforePriceRpcProcess(duseProcessor)
		productsGen.RegisterBeforePriceFilter(duseProcessor)
	}

	// === 拼车专属过滤器 ===
	// 拼车类导流位品类筛选
	if cgFilter := after_price_filter.NewCarpoolGuideBarFilter(productsGen.BaseReqData); cgFilter != nil {
		productsGen.RegisterAfterPriceFilter(cgFilter)
	}

	// 城际修改实时单
	if r := rpc_process_v4.NewIntercityPreferential(ctx, productsGen.BaseReqData); r != nil {
		productsGen.RegisterAfterDdsFilter(r)
	}

	// 拼车距离过滤
	productsGen.RegisterAfterPriceFilter(filter.NewDistanceFilter(productsGen.BaseReqData))
}
