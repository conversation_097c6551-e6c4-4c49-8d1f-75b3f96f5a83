package processor_registry

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
)

type ProcessorsRegistryFunc func(ctx context.Context, productsGen *biz_runtime.ProductsGenerator)

// ProcessorsRegistryItem 品类注册项
type ProcessorsRegistryItem struct {
	Name             string                 // 注册器名称
	TargetCategories []string               // 目标品类集合
	RegistryFunc     ProcessorsRegistryFunc // 注册函数
	IsEnabled        bool                   // 是否启用
}

// 全局注册器与品类集映射关系
var ProcessorsRegistryMapping = map[string]*ProcessorsRegistryItem{
	"common": {
		Name:             "common",
		TargetCategories: []string{"all"},
		RegistryFunc:     registerCommonProcessors,
		IsEnabled:        true,
	},
	"carpool": {
		Name:             "carpool",
		TargetCategories: []string{"carpool"},
		RegistryFunc:     registerCarpoolProcessors,
		IsEnabled:        true,
	},
	"single": {
		Name:             "single",
		TargetCategories: []string{"taxi", "express", "premium"},
		RegistryFunc:     registerSingleProcessors,
		IsEnabled:        true,
	},
}

// RegisterAllProcessors 注册所有启用的处理器
func RegisterAllProcessors(ctx context.Context, productsGen *biz_runtime.ProductsGenerator) {
	for _, registryItem := range ProcessorsRegistryMapping {
		if registryItem.IsEnabled && registryItem.RegistryFunc != nil {
			// TODO: 后续可以根据品类标签过滤，现在先全部注册
			registryItem.RegistryFunc(ctx, productsGen)
		}
	}
}
