package model

import AthenaApiv3 "git.xiaojukeji.com/dirpc/dirpc-go-thrift-AthenaApiv3"

// RunTimeData 运行时数据
type RunTimeData struct {
	EnterpriseGlobalSceneExpect    *AthenaApiv3.TotalExpectInfo
	EnterpriseProductSceneExpect   *AthenaApiv3.TotalExpectInfo
	EnterpriseGlobalEtsSceneExpect *AthenaApiv3.TotalExpectInfo

	GlobalSceneExpect   *AthenaApiv3.GlobalOrderMatchExpectInfo
	ProductSceneExpect  *AthenaApiv3.ProductExpectInfo
	SaveTimeSceneExpect *AthenaApiv3.SaveTimeExpectInfo
}
