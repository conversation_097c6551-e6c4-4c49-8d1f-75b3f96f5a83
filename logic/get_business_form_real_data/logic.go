package get_business_form_real_data

import (
	"context"
	"errors"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/dos"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"

	NewErrors "git.xiaojukeji.com/gulfstream/mamba/biz/errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts/public_log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/passport"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_business_form_real_data/internal/athena_info"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_business_form_real_data/internal/write_log"
	"git.xiaojukeji.com/gulfstream/mamba/logic/get_business_form_real_data/model"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/get_form_real_expect_info"
)

// GetFormRealExpectInfo ...
func GetFormRealExpectInfo(ctx context.Context, request *proto.GetBusinessFormRealDataRequest) (*proto.RealExpectInfoData, public_log.WritePublicFunc, NewErrors.BizError) {
	if request == nil {
		return nil, nil, NewErrors.NewBizError(errors.New("request is nil"), NewErrors.ErrnoInvalidArgument)
	}

	var (
		runTimeData = &model.RunTimeData{}

		resp *proto.RealExpectInfoData
	)

	baseReq, err := buildBaseReq(ctx, request)
	if err != nil || baseReq == nil {
		return nil, nil, err
	}

	athenaInfo := athena_info.NewAthenaHandle(&athena_info.AthenaData{
		MultiProduct:  request.GetMultiProduct(),
		ExpectScene:   request.GetExpectScene(),
		SourceType:    request.GetSourceType(),
		BubbleTraceID: buildBubbleTraceID(request, baseReq),
	})
	err = athenaInfo.Do(ctx, baseReq, runTimeData)
	if err != nil {
		return nil, nil, err
	}

	resp, err = render(ctx, baseReq, runTimeData)
	if err != nil {
		return nil, nil, err
	}

	return resp, write_log.WritePublicLog(ctx, baseReq, runTimeData), err
}

func buildBubbleTraceID(request *proto.GetBusinessFormRealDataRequest, baseReq *model.BaseReq) *string {
	if request.GetSourceType() == consts.SourceTypeBusinessAnycarEstimate {
		if baseReq != nil && baseReq.OrderInfo != nil {
			return &baseReq.OrderInfo.ExtendFeatureParsed.EstimateTraceID
		}

		return nil
	} else {
		return request.BubbleTraceId
	}
}

func buildBaseReq(ctx context.Context, request *proto.GetBusinessFormRealDataRequest) (*model.BaseReq, NewErrors.BizError) {
	baseReq := model.NewBaseReq()
	opts := []model.Opts{
		setUserInfo(request),
		setParams(request),
		setAreaInfo(request),
		setOrderInfo(request), // 有依赖顺序
		setQuotationInfo(request),
	}

	err := baseReq.Do(ctx, opts...)
	if err != nil {
		log.Trace.Warnf(ctx, "GetFormRealExpectInfo", "err=%v", err)
		return nil, err
	}

	return baseReq, err
}

func setOrderInfo(request *proto.GetBusinessFormRealDataRequest) model.Opts {
	return func(ctx context.Context, data *model.BaseReq) NewErrors.BizError {
		if data == nil {
			return NewErrors.NewBizError(errors.New("data is nil"), NewErrors.ErrnoInvalidArgument)
		}

		if request == nil {
			return NewErrors.NewBizError(errors.New("request is nil"), NewErrors.ErrnoInvalidArgument)
		}

		if request.GetSourceType() != consts.SourceTypeBusinessAnycarEstimate {
			return nil
		}

		if request.GetOid() == "" {
			return NewErrors.NewBizError(errors.New("oid is nil"), NewErrors.ErrnoInvalidArgument)
		}

		orderID, district, err := util.DecodeOrderID(request.GetOid())
		if err != nil {
			return NewErrors.NewBizError(errors.New("oid is nil"), NewErrors.ErrnoInvalidArgument)
		}

		orderInfo, err := dos.GetOrderInfo(ctx, orderID, district)
		if err != nil {
			return NewErrors.NewBizError(err, NewErrors.ErrnoRpcFailed)
		}

		if orderInfo == nil {
			return NewErrors.NewBizError(errors.New("order info is nil"), NewErrors.ErrnoSystemError)
		}

		data.OrderInfo = orderInfo

		return nil
	}
}

func setParams(request *proto.GetBusinessFormRealDataRequest) model.Opts {
	return func(ctx context.Context, data *model.BaseReq) NewErrors.BizError {
		if data == nil {
			return NewErrors.NewBizError(errors.New("data is nil"), NewErrors.ErrnoInvalidArgument)
		}

		if request == nil {
			return NewErrors.NewBizError(errors.New("request is nil"), NewErrors.ErrnoInvalidArgument)
		}

		data.Params = &model.CommonParam{
			AppVersion:  request.GetAppVersion(),
			AccessKeyID: request.GetAccessKeyId(),
			Lang:        request.GetLang(),
			ClientType:  request.GetClientType(),
			Channel:     request.GetChannel(),
		}

		return nil
	}
}

func setQuotationInfo(request *proto.GetBusinessFormRealDataRequest) model.Opts {
	return func(ctx context.Context, data *model.BaseReq) NewErrors.BizError {
		if data == nil {
			return NewErrors.NewBizError(errors.New("data is nil"), NewErrors.ErrnoInvalidArgument)
		}

		if request == nil {
			return NewErrors.NewBizError(errors.New("request is nil"), NewErrors.ErrnoInvalidArgument)
		}

		quotationMap := make(map[string]*biz_runtime.QuotationV2)

		quotationInfo, err := price_api.GetQuotationBatch(ctx, buildQuotationRequest(ctx, request, data))
		if err != nil {
			return NewErrors.NewBizError(err, NewErrors.ErrnoRpcFailed)
		}

		if quotationInfo == nil || len(quotationInfo.Data) <= 0 {
			return NewErrors.NewBizError(errors.New("quotation is nil"), NewErrors.ErrnoSystemError)
		}

		for _, quotation := range quotationInfo.Data {
			quotationMap[quotation.EstimateId] = biz_runtime.EstimateQuotation2V2(quotation)
		}

		data.Quotation = quotationMap

		return nil
	}
}

func setAreaInfo(request *proto.GetBusinessFormRealDataRequest) model.Opts {
	return func(ctx context.Context, data *model.BaseReq) NewErrors.BizError {
		if data == nil {
			return NewErrors.NewBizError(errors.New("data is nil"), NewErrors.ErrnoSystemError)
		}

		if request == nil {
			return NewErrors.NewBizError(errors.New("request is nil"), NewErrors.ErrnoSystemError)
		}

		data.AreaInfo = &models.AreaInfo{
			City:     int32(request.City),
			Area:     int32(request.City),
			FromLat:  request.FromLat,
			FromLng:  request.FromLng,
			FromName: request.GetFromName(),
			ToLat:    request.ToLat,
			ToLng:    request.ToLng,
			ToName:   request.GetToName(),
		}

		return nil
	}
}

func setUserInfo(request *proto.GetBusinessFormRealDataRequest) model.Opts {
	return func(ctx context.Context, data *model.BaseReq) NewErrors.BizError {
		if data == nil {
			return NewErrors.NewBizError(errors.New("data is nil"), NewErrors.ErrnoSystemError)
		}

		if request == nil {
			return NewErrors.NewBizError(errors.New("request is nil"), NewErrors.ErrnoInvalidArgument)
		}

		if request.Token == "" {
			return NewErrors.NewBizError(errors.New("token is nil"), NewErrors.ErrnoInvalidArgument)
		}

		userInfo, err := passport.GetUserInfo(ctx, request.Token, "")
		if err != nil {
			if err == passport.ErrOffline {
				return NewErrors.ErrNotLogin
			}
			return NewErrors.ErrSystem
		}

		data.UserInfo = userInfo
		return nil
	}
}

// render ...
func render(ctx context.Context, baseReq *model.BaseReq, data *model.RunTimeData) (*proto.RealExpectInfoData, NewErrors.BizError) {
	if data == nil {
		return nil, NewErrors.NewBizError(errors.New("runtime data is nil"), NewErrors.ErrnoSystemError)
	}

	var respData = &proto.RealExpectInfoData{
		EnterpriseGlobalSceneExpect:    get_form_real_expect_info.BuildExpectInfo(ctx, baseReq, data.EnterpriseGlobalSceneExpect),
		EnterpriseProductSceneExpect:   get_form_real_expect_info.BuildExpectInfo(ctx, baseReq, data.EnterpriseProductSceneExpect),
		EnterpriseGlobalEtsSceneExpect: get_form_real_expect_info.BuildExpectInfo(ctx, baseReq, data.EnterpriseGlobalEtsSceneExpect),
		GlobalSceneExpect:              get_form_real_expect_info.BuildGlobalSceneExpect(ctx, baseReq, data.GlobalSceneExpect),
		ProductSceneExpect:             get_form_real_expect_info.BuildProductExpect(ctx, baseReq, data.ProductSceneExpect),
		SaveTimeSceneExpect:            get_form_real_expect_info.BuildSaveTimeSceneExpect(ctx, baseReq, data.SaveTimeSceneExpect),
	}

	return respData, nil
}

// buildQuotationRequest ...
func buildQuotationRequest(ctx context.Context, request *proto.GetBusinessFormRealDataRequest, data *model.BaseReq) *price_api.PriceQuotationBatch {
	multiProduct := request.GetMultiProduct()
	if len(multiProduct) <= 0 {
		return nil
	}

	eidList := make([]string, 0)
	for _, product := range multiProduct {
		if product == nil {
			continue
		}

		eidList = append(eidList, product.EstimateId)
	}

	if request.GetSourceType() == consts.SourceTypeBusinessAnycarEstimate {
		if data != nil && data.OrderInfo != nil {
			for _, product := range data.OrderInfo.ExtendFeatureParsed.MultiRequiredProduct {
				eidList = append(eidList, product.EstimateID)
			}
		}
	}

	return &price_api.PriceQuotationBatch{
		EstimateIdList: eidList,
	}
}
