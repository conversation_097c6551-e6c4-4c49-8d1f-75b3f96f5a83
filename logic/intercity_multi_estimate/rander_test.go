package intercity_multi_estimate

import (
	"context"
	"encoding/json"
	"fmt"
	Prfs "git.xiaojukeji.com/dirpc/dirpc-go-http-Prfs"
	CarpoolOpenApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolOpenApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	proto "git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/intercity_multi_estimate/internal"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_multi_station"
	"git.xiaojukeji.com/gulfstream/mamba/render/private/intercity_sku"
	"git.xiaojukeji.com/gulfstream/mamba/render/public/car_info"
	"git.xiaojukeji.com/nuwa/golibs/zerolog/ddlog"
	"github.com/agiledragon/gomonkey/v2"
	. "github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
	"testing"
)

func Test_Rander(t *testing.T) {
	PatchConvey("Test_Rander", t, func() {
		Convey("Case1: true ", func() {
			bizLogic := &BizLogic{
				generator: &biz_runtime.ProductsGenerator{
					BaseReqData: &models.BaseReqData{
						CommonBizInfo: models.CommonBizInfo{
							StationInfo: models.StationInfo{
								RouteId:   111,
								StartCity: 1,
							},
						},
						CommonInfo: models.CommonInfo{
							AccessKeyID: 22,
							PageType:    39,
						},
						PassengerInfo: models.PassengerInfo{
							PID: 1234567890,
						},
					},
				},
			}
			products := make([]*biz_runtime.ProductInfoFull, 0)
			product := &biz_runtime.ProductInfoFull{
				Product: &models.Product{
					BizInfo: &models.PrivateBizInfo{
						IntercityData: models.IntercityData{
							StationInventoryInfo: &models.StationInventoryInfo{
								SelectInfo: models.StationInventorySelectInfo{
									ShiftID:  "1",
									LastItem: true,
									Refresh:  1,
								}},
						},
					},
				},
			}
			products = append(products, product)
			req := &proto.IntercityMultiEstimateRequest{}

			mockTrace := Mock((*ddlog.DiLogHandle).Infof).Return().Build()
			defer mockTrace.UnPatch()

			mockFillBusModeInfo := Mock(intercity_multi_station.FillBusModeInfo).Return().Build()
			defer mockFillBusModeInfo.UnPatch()
			var context context.Context

			mockGetBusinessNameAndIcon := Mock((car_info.GetBusinessNameAndIcon)).Return("", "").Build()
			defer mockGetBusinessNameAndIcon.UnPatch()

			propertyInfos := make([]*Prfs.PropertyInfo, 0)
			propertyInfo := &Prfs.PropertyInfo{}
			propertyInfos = append(propertyInfos, propertyInfo)

			mockStationInfo := Mock((intercity_multi_station.StationInfo)).Return(&proto.IntercityStationInfo{
				Detail: &proto.Detail{},
			}, "", propertyInfos).Build()
			defer mockStationInfo.UnPatch()

			mockTimeMsg := Mock((intercity_multi_station.TimeMsg)).Return("").Build()
			defer mockTimeMsg.UnPatch()

			mockFeeMsg := Mock((intercity_multi_station.FeeMsg)).Return("").Build()
			defer mockFeeMsg.UnPatch()

			// []*proto.TagWithIconAndBorder
			feeDescList := make([]*proto.TagWithIconAndBorder, 0)
			tagWithIconAndBorder := &proto.TagWithIconAndBorder{}
			feeDescList = append(feeDescList, tagWithIconAndBorder)
			mockFeeDescList := Mock((intercity_sku.FeeDescList)).Return(feeDescList).Build()
			defer mockFeeDescList.UnPatch()
			mockButtonInfo := Mock((intercity_multi_station.ButtonInfo)).Return(&proto.ButtonInfo{}).Build()
			defer mockButtonInfo.UnPatch()

			mockAdapterMultiInterCity := Mock(((*internal.AdapterMultiInterCity).GetSelectInfo)).Return(&models.StationInventorySelectInfo{}).Build()
			defer mockAdapterMultiInterCity.UnPatch()
			feeSubMsgs := make([]*proto.SubMsg, 0)
			tagSubMsg := &proto.SubMsg{}
			feeSubMsgs = append(feeSubMsgs, tagSubMsg)
			mockScreenData := Mock(intercity_multi_station.ScreenData).Return(&proto.ScreenData{}).Build()
			defer mockScreenData.UnPatch()

			mockSubTitle := Mock((intercity_multi_station.SubTitle)).Return("").Build()
			defer mockSubTitle.UnPatch()
			mockSubMsg := Mock((intercity_multi_station.SubMsg)).Return(&proto.SubMsg{}).Build()
			defer mockSubMsg.UnPatch()

			mockPageTitle := Mock(intercity_multi_station.PageTitle).Return("").Build()
			defer mockPageTitle.UnPatch()

			mockDisabledInfo := Mock(intercity_multi_station.DisabledInfo).Return(&proto.DisabledInfo{}).Build()
			defer mockDisabledInfo.UnPatch()

			estimateCards := make([]*proto.EstimateCard, 0)
			estimateCard := &proto.EstimateCard{}
			estimateCards = append(estimateCards, estimateCard)
			//[]*proto.EstimateCard
			mockgetEstimateCardRecommend := Mock(bizLogic.getEstimateCardRecommend).Return(estimateCards).Build()
			defer mockgetEstimateCardRecommend.UnPatch()

			mockGetTraceIDFromCtxWithoutCheck := Mock(util.GetTraceIDFromCtxWithoutCheck).Return("").Build()
			defer mockGetTraceIDFromCtxWithoutCheck.UnPatch()

			mockTitle := Mock(intercity_multi_station.Title).Return("").Build()
			defer mockTitle.UnPatch()

			mockgetInstructions := Mock(bizLogic.getInstructions).Return(&proto.GuideData{}).Build()
			defer mockgetInstructions.UnPatch()

			//[]*proto.CalendarItem
			CalendarItems := make([]*proto.CalendarItem, 0)
			CalendarItem := &proto.CalendarItem{}
			CalendarItems = append(CalendarItems, CalendarItem)
			mockGetCalendar := Mock(bizLogic.getCalendar).Return(CalendarItems).Build()
			defer mockGetCalendar.UnPatch()

			//  *proto.SearchBox
			mockSearchBox := Mock(bizLogic.getSearchBox).Return(&proto.SearchBox{}).Build()
			defer mockSearchBox.UnPatch()
			sortLists := make([]*proto.SortList, 0)
			sortList := &proto.SortList{}
			sortLists = append(sortLists, sortList)

			mockGetSortBy := Mock(bizLogic.getSortBy).Return(sortLists).Build()
			defer mockGetSortBy.UnPatch()

			mockGetDcmpContent := Mock(dcmp.GetDcmpContent).Return("{\"sort_list\": [{\"text\": \"综合排序\",\"sort_type\": \"1\"},{\"text\": \"距离最近\",\"sort_type\": \"2\"}]}").Build()
			defer mockGetDcmpContent.UnPatch()
			subMsgList := make([]*proto.SubMsg, 0)
			subMsg := &proto.SubMsg{
				Color: "color",
			}
			subMsgList = append(subMsgList, subMsg)

			mockSubTagList := Mock(intercity_multi_station.SubTagList).Return(subMsgList).Build()
			defer mockSubTagList.UnPatch()

			mockUnmarshal := Mock(json.Unmarshal).Return(
				fmt.Errorf("not found")).Build()
			defer mockUnmarshal.UnPatch()

			text := "text"
			mockSubBackground := Mock(intercity_multi_station.SubBackground).Return(&text).Build()
			defer mockSubBackground.UnPatch()

			intercityMultiEstimateData := bizLogic.Render(context, req, products)
			for _, value := range intercityMultiEstimateData.EstimateCard {
				if value.Background != nil {
					assert.Equal(t, "text", *value.Background)
				}
			}
		})

		Convey("Case2: true ", func() {
			bizLogic := &BizLogic{
				generator: &biz_runtime.ProductsGenerator{
					BaseReqData: &models.BaseReqData{
						CommonBizInfo: models.CommonBizInfo{
							StationInfo: models.StationInfo{
								RouteId:   111,
								StartCity: 1,
							},
						},
						CommonInfo: models.CommonInfo{
							AccessKeyID: 22,
							PageType:    39,
						},
						PassengerInfo: models.PassengerInfo{
							PID: 1234567890,
						},
					},
				},
			}
			products := make([]*biz_runtime.ProductInfoFull, 0)
			product := &biz_runtime.ProductInfoFull{
				Product: &models.Product{
					BizInfo: &models.PrivateBizInfo{
						IntercityData: models.IntercityData{
							StationInventoryInfo: &models.StationInventoryInfo{
								SelectInfo: models.StationInventorySelectInfo{
									ShiftID:  "1",
									LastItem: true,
									Refresh:  1,
								}},
						},
					},
				},
			}
			products = append(products, product)
			var sort int32 = 1
			req := &proto.IntercityMultiEstimateRequest{
				Sort: &sort,
			}
			mockTrace := Mock((*ddlog.DiLogHandle).Infof).Return().Build()
			defer mockTrace.UnPatch()

			mockFillBusModeInfo := Mock(intercity_multi_station.FillBusModeInfo).Return().Build()
			defer mockFillBusModeInfo.UnPatch()
			var context context.Context

			mockGetBusinessNameAndIcon := Mock((car_info.GetBusinessNameAndIcon)).Return("", "").Build()
			defer mockGetBusinessNameAndIcon.UnPatch()

			propertyInfos := make([]*Prfs.PropertyInfo, 0)
			propertyInfo := &Prfs.PropertyInfo{}
			propertyInfos = append(propertyInfos, propertyInfo)

			mockStationInfo := Mock((intercity_multi_station.StationInfo)).Return(&proto.IntercityStationInfo{
				Detail: &proto.Detail{},
			}, "", propertyInfos).Build()
			defer mockStationInfo.UnPatch()

			mockTimeMsg := Mock((intercity_multi_station.TimeMsg)).Return("").Build()
			defer mockTimeMsg.UnPatch()

			mockFeeMsg := Mock((intercity_multi_station.FeeMsg)).Return("").Build()
			defer mockFeeMsg.UnPatch()

			// []*proto.TagWithIconAndBorder
			feeDescList := make([]*proto.TagWithIconAndBorder, 0)
			tagWithIconAndBorder := &proto.TagWithIconAndBorder{}
			feeDescList = append(feeDescList, tagWithIconAndBorder)
			mockFeeDescList := Mock((intercity_sku.FeeDescList)).Return(feeDescList).Build()
			defer mockFeeDescList.UnPatch()
			mockButtonInfo := Mock((intercity_multi_station.ButtonInfo)).Return(&proto.ButtonInfo{}).Build()
			defer mockButtonInfo.UnPatch()

			mockAdapterMultiInterCity := Mock(((*internal.AdapterMultiInterCity).GetSelectInfo)).Return(&models.StationInventorySelectInfo{}).Build()
			defer mockAdapterMultiInterCity.UnPatch()
			feeSubMsgs := make([]*proto.SubMsg, 0)
			tagSubMsg := &proto.SubMsg{}
			feeSubMsgs = append(feeSubMsgs, tagSubMsg)

			mockScreenData := Mock(intercity_multi_station.ScreenData).Return(&proto.ScreenData{}).Build()
			defer mockScreenData.UnPatch()

			mockSubTitle := Mock((intercity_multi_station.SubTitle)).Return("").Build()
			defer mockSubTitle.UnPatch()
			mockSubMsg := Mock((intercity_multi_station.SubMsg)).Return(&proto.SubMsg{}).Build()
			defer mockSubMsg.UnPatch()
			subMsgList := make([]*proto.SubMsg, 0)
			subMsg := &proto.SubMsg{
				Color: "color",
			}
			subMsgList = append(subMsgList, subMsg)

			mockSubTagList := Mock(intercity_multi_station.SubTagList).Return(subMsgList).Build()
			defer mockSubTagList.UnPatch()
			mockPageTitle := Mock(intercity_multi_station.PageTitle).Return("").Build()
			defer mockPageTitle.UnPatch()

			mockDisabledInfo := Mock(intercity_multi_station.DisabledInfo).Return(&proto.DisabledInfo{}).Build()
			defer mockDisabledInfo.UnPatch()

			estimateCards := make([]*proto.EstimateCard, 0)
			estimateCard := &proto.EstimateCard{}
			estimateCards = append(estimateCards, estimateCard)
			//[]*proto.EstimateCard
			mockgetEstimateCardRecommend := Mock(bizLogic.getEstimateCardRecommend).Return(estimateCards).Build()
			defer mockgetEstimateCardRecommend.UnPatch()

			mockGetTraceIDFromCtxWithoutCheck := Mock(util.GetTraceIDFromCtxWithoutCheck).Return("").Build()
			defer mockGetTraceIDFromCtxWithoutCheck.UnPatch()

			mockTitle := Mock(intercity_multi_station.Title).Return("").Build()
			defer mockTitle.UnPatch()

			mockgetInstructions := Mock(bizLogic.getInstructions).Return(&proto.GuideData{}).Build()
			defer mockgetInstructions.UnPatch()

			//[]*proto.CalendarItem
			CalendarItems := make([]*proto.CalendarItem, 0)
			CalendarItem := &proto.CalendarItem{}
			CalendarItems = append(CalendarItems, CalendarItem)
			mockGetCalendar := Mock(bizLogic.getCalendar).Return(CalendarItems).Build()
			defer mockGetCalendar.UnPatch()

			//  *proto.SearchBox
			mockSearchBox := Mock(bizLogic.getSearchBox).Return(&proto.SearchBox{}).Build()
			defer mockSearchBox.UnPatch()

			sortLists := make([]*proto.SortList, 0)
			sortList := &proto.SortList{}
			sortLists = append(sortLists, sortList)

			mockGetSortBy := Mock(bizLogic.getSortBy).Return(sortLists).Build()
			defer mockGetSortBy.UnPatch()

			mockGetDcmpContent := Mock(dcmp.GetDcmpContent).Return("{\"sort_list\": [{\"text\": \"综合排序\",\"sort_type\": \"1\"},{\"text\": \"距离最近\",\"sort_type\": \"2\"}]}").Build()
			defer mockGetDcmpContent.UnPatch()

			mockUnmarshal := Mock(json.Unmarshal).Return(
				fmt.Errorf("not found")).Build()
			defer mockUnmarshal.UnPatch()

			text := "text"
			mockSubBackground := Mock(intercity_multi_station.SubBackground).Return(&text).Build()
			defer mockSubBackground.UnPatch()

			bizLogic.Render(context, req, products)
		})
	})
}

func Test_GetSortBy(t *testing.T) {
	bizLogic := &BizLogic{}
	PatchConvey("Test_GetSortBy", t, func() {
		Convey("Case1: Unmarshal error", func() {
			mockGetDcmpContent := Mock(dcmp.GetDcmpContent).Return("{\"sort_list\": [{\"text\": \"综合排序\",\"sort_type\": \"1\"},{\"text\": \"距离最近\",\"sort_type\": \"2\"}]}").Build()
			defer mockGetDcmpContent.UnPatch()

			mockUnmarshal := Mock(json.Unmarshal).Return(
				fmt.Errorf("not found")).Build()
			defer mockUnmarshal.UnPatch()
			mockTrace := Mock((*ddlog.DiLogHandle).Infof).Return().Build()
			defer mockTrace.UnPatch()
			var context context.Context
			bizLogic.getSortBy(context)
		})

		Convey("Case1: Unmarshal nil", func() {
			mockGetDcmpContent := Mock(dcmp.GetDcmpContent).Return("{\"sort_list\": [{\"text\": \"综合排序\",\"sort_type\": \"1\"},{\"text\": \"距离最近\",\"sort_type\": \"2\"}]}").Build()
			defer mockGetDcmpContent.UnPatch()

			mockUnmarshal := Mock(json.Unmarshal).Return(nil).Build()
			defer mockUnmarshal.UnPatch()
			var context context.Context
			bizLogic.getSortBy(context)
		})

	})
}

func TestGetOptimizedData(t *testing.T) {
	convey.Convey("Given getOptimizedData method", t, func() {
		bl := &BizLogic{
			generator: &biz_runtime.ProductsGenerator{
				BaseReqData: &models.BaseReqData{
					PassengerInfo: models.PassengerInfo{
						Phone: "13800138000",
						PID:   10001,
					},
				},
			},
		}

		convey.Convey("1 With precise and recommended estimates", func() {
			patches := gomonkey.NewPatches()
			patches.ApplyPrivateMethod(bl, "getDcmpText", func(_ context.Context, _ string) *proto.OptimizedData {
				return &proto.OptimizedData{
					LineText: "文本",
				}

			})
			defer patches.Reset()
			var isEstimateV2 = true
			req := &proto.IntercityMultiEstimateRequest{
				IsEstimateV2: &isEstimateV2,
			}
			estimateData := []*proto.EstimateCard{{}}
			estimateDataRecommend := []*proto.EstimateCard{{}}

			actualData, actualOmegaParams := bl.getOptimizedData(context.Background(), req, estimateData, estimateDataRecommend, nil)
			convey.So(actualData.LineText, convey.ShouldEqual, "文本")
			convey.So(actualOmegaParams.PageStatus, convey.ShouldEqual, "1")
			convey.So(actualOmegaParams.RecommendedStrategy, convey.ShouldEqual, "1")
		})

		convey.Convey("2 With only precise estimates", func() {
			patches := gomonkey.NewPatches()
			patches.ApplyPrivateMethod(bl, "getDcmpText", func(_ context.Context, _ string) *proto.OptimizedData {
				return &proto.OptimizedData{
					LineText: "文本",
				}
			})
			defer patches.Reset()

			var isEstimateV2 = true
			req := &proto.IntercityMultiEstimateRequest{
				IsEstimateV2: &isEstimateV2,
			}
			estimateData := []*proto.EstimateCard{{}}

			actualData, actualOmegaParams := bl.getOptimizedData(context.Background(), req, estimateData, nil, nil)
			convey.So(actualData, convey.ShouldBeNil)
			convey.So(actualOmegaParams.PageStatus, convey.ShouldEqual, "2")
		})

		convey.Convey("3 With only recommended estimates", func() {
			patches := gomonkey.NewPatches()
			patches.ApplyPrivateMethod(bl, "getDcmpText", func(_ context.Context, _ string) *proto.OptimizedData {
				return &proto.OptimizedData{
					LineText: "文本",
				}
			})
			patches.ApplyFunc(apollo.GetParameters, func(feature string, passengerPhone string, params map[string]string) (bool, map[string]string) {
				return false, nil
			})
			defer patches.Reset()

			var isEstimateV2 = true
			req := &proto.IntercityMultiEstimateRequest{
				IsEstimateV2: &isEstimateV2,
			}
			estimateDataRecommend := []*proto.EstimateCard{{}}

			actualData, actualOmegaParams := bl.getOptimizedData(context.Background(), req, nil, estimateDataRecommend, nil)
			convey.So(actualData.LineText, convey.ShouldEqual, "文本")
			convey.So(actualOmegaParams.PageStatus, convey.ShouldEqual, "3")
			convey.So(actualOmegaParams.RecommendedStrategy, convey.ShouldEqual, "1")
		})

		convey.Convey("4 With optional estimates", func() {
			patches := gomonkey.NewPatches()
			patches.ApplyPrivateMethod(bl, "getDcmpText", func(_ context.Context, _ string) *proto.OptimizedData {
				return &proto.OptimizedData{
					LineText: "文本",
				}
			})
			patches.ApplyPrivateMethod(bl, "getCandidate", func(_ context.Context, _ int64, _ []*CarpoolOpenApi.BusRegionInventory, _ *proto.IntercityMultiEstimateRequest) int64 {
				return 0
			})
			estimateDataWholeCityRecommend := []*proto.EstimateCard{{}}

			defer patches.Reset()
			var isEstimateV2 = true
			req := &proto.IntercityMultiEstimateRequest{
				IsEstimateV2: &isEstimateV2,
			}
			actualData, actualOmegaParams := bl.getOptimizedData(context.Background(), req, nil, nil, estimateDataWholeCityRecommend)
			convey.So(actualData.LineText, convey.ShouldEqual, "文本")
			convey.So(actualOmegaParams.PageStatus, convey.ShouldEqual, "3")
			convey.So(actualOmegaParams.RecommendedStrategy, convey.ShouldEqual, "2")

		})

		convey.Convey("5 With no estimates", func() {
			patches := gomonkey.NewPatches()
			patches.ApplyPrivateMethod(bl, "getDcmpText", func(_ context.Context, _ string) *proto.OptimizedData {
				return &proto.OptimizedData{
					LineText: "文本",
				}
			})
			patches.ApplyPrivateMethod(bl, "getCandidate", func(_ context.Context, _ int64, _ []*CarpoolOpenApi.BusRegionInventory, _ *proto.IntercityMultiEstimateRequest) int64 {
				return 0
			})

			defer patches.Reset()
			var isEstimateV2 = true
			req := &proto.IntercityMultiEstimateRequest{
				IsEstimateV2: &isEstimateV2,
			}
			actualData, actualOmegaParams := bl.getOptimizedData(context.Background(), req, nil, nil, nil)
			convey.So(actualData.LineText, convey.ShouldEqual, "文本")
			convey.So(actualOmegaParams.PageStatus, convey.ShouldEqual, "6")
		})

		convey.Convey("6 With precise and recommended estimates", func() {
			patches := gomonkey.NewPatches()
			patches.ApplyPrivateMethod(bl, "getDcmpText", func(_ context.Context, _ string) *proto.OptimizedData {
				return &proto.OptimizedData{
					LineText: "文本",
					IconCard: &proto.IconCard{
						Text: []string{"车票暂未开始售卖，请多多关注", "3月13日"},
					},
				}
			})
			patches.ApplyPrivateMethod(bl, "getCandidate", func(_ context.Context, _ int64, _ []*CarpoolOpenApi.BusRegionInventory, _ *proto.IntercityMultiEstimateRequest) int64 {
				return 1741852123
			})
			patches.ApplyFunc(dcmp.TranslateTemplate, func(_ string, _ map[string]string) string {
				return "3月13日"
			})
			defer patches.Reset()
			var isEstimateV2 = true
			req := &proto.IntercityMultiEstimateRequest{
				IsEstimateV2: &isEstimateV2,
			}
			actualData, _ := bl.getOptimizedData(context.Background(), req, nil, nil, nil)
			convey.So(actualData.IconCard.Text[1], convey.ShouldEqual, "3月13日")
			//convey.So(actualOmegaParams.PageStatus, convey.ShouldEqual, "5")
		})

		convey.Convey("7 With precise and recommended estimates", func() {
			patches := gomonkey.NewPatches()
			patches.ApplyPrivateMethod(bl, "getDcmpText", func(_ context.Context, _ string) *proto.OptimizedData {
				return &proto.OptimizedData{
					LineText: "文本",
					IconCard: &proto.IconCard{
						Text: []string{"车票暂未开始售卖，请多多关注", "3月13日"},
					},
				}
			})
			patches.ApplyPrivateMethod(bl, "getCandidate", func(_ context.Context, _ int64, _ []*CarpoolOpenApi.BusRegionInventory, _ *proto.IntercityMultiEstimateRequest) int64 {
				return 1741852123
			})
			patches.ApplyFunc(dcmp.TranslateTemplate, func(_ string, _ map[string]string) string {
				return "3月13日"
			})
			estimateDataWholeCityRecommend := []*proto.EstimateCard{{}}
			defer patches.Reset()
			var isEstimateV2 = true
			req := &proto.IntercityMultiEstimateRequest{
				IsEstimateV2: &isEstimateV2,
			}
			actualData, actualOmegaParams := bl.getOptimizedData(context.Background(), req, nil, nil, estimateDataWholeCityRecommend)
			convey.So(actualData.IconCard.Text[1], convey.ShouldEqual, "3月13日")
			//convey.So(actualOmegaParams.PageStatus, convey.ShouldEqual, "4")
			convey.So(actualOmegaParams.RecommendedStrategy, convey.ShouldEqual, "2")
		})
	})
}

func TestGetEstimateCardRecommend(t *testing.T) {

	// Case 1: 精确班次和推荐班次都不为空
	t.Run("Both Accurate and Recommend Non-empty", func(t *testing.T) {
		estimateData := []*proto.EstimateCard{{}, {}}
		estimateDataRecommend := []*proto.EstimateCard{{}, {}}
		estimateDataWholeCityRecommend := []*proto.EstimateCard{}

		expectedResult := estimateDataRecommend
		actualResult := (&BizLogic{}).getEstimateCardRecommend(estimateData, estimateDataRecommend, estimateDataWholeCityRecommend)

		assert.Equal(t, expectedResult, actualResult)
	})

	// Case 2: 精确班次为空，推荐班次不为空
	t.Run("Accurate Empty, Recommend Non-empty", func(t *testing.T) {
		estimateData := []*proto.EstimateCard{}
		estimateDataRecommend := []*proto.EstimateCard{{}, {}}
		estimateDataWholeCityRecommend := []*proto.EstimateCard{}

		expectedResult := estimateDataRecommend
		actualResult := (&BizLogic{}).getEstimateCardRecommend(estimateData, estimateDataRecommend, estimateDataWholeCityRecommend)

		assert.Equal(t, expectedResult, actualResult)
	})

	// Case 3: 精确班次不为空，推荐班次为空
	t.Run("Accurate Non-empty, Recommend Empty", func(t *testing.T) {
		estimateData := []*proto.EstimateCard{{}, {}}
		estimateDataRecommend := []*proto.EstimateCard{}
		estimateDataWholeCityRecommend := []*proto.EstimateCard{}

		expectedResult := estimateDataRecommend
		actualResult := (&BizLogic{}).getEstimateCardRecommend(estimateData, estimateDataRecommend, estimateDataWholeCityRecommend)

		assert.Equal(t, expectedResult, actualResult)
	})

	// Case 4: 精确班次和推荐班次都为空，全程推荐班次不为空
	t.Run("Both Accurate and Recommend Empty, Whole City Recommend Non-empty", func(t *testing.T) {
		estimateData := []*proto.EstimateCard{}
		estimateDataRecommend := []*proto.EstimateCard{}
		estimateDataWholeCityRecommend := []*proto.EstimateCard{{}}

		expectedResult := estimateDataWholeCityRecommend
		actualResult := (&BizLogic{}).getEstimateCardRecommend(estimateData, estimateDataRecommend, estimateDataWholeCityRecommend)

		assert.Equal(t, expectedResult, actualResult)
	})

	// Case 5: 所有班次都为空
	t.Run("All Empty", func(t *testing.T) {
		estimateData := []*proto.EstimateCard{}
		estimateDataRecommend := []*proto.EstimateCard{}
		estimateDataWholeCityRecommend := []*proto.EstimateCard{}

		expectedResult := estimateDataRecommend
		actualResult := (&BizLogic{}).getEstimateCardRecommend(estimateData, estimateDataRecommend, estimateDataWholeCityRecommend)

		assert.Equal(t, expectedResult, actualResult)
	})
}

func TestCalenderStatusChecker(t *testing.T) {
	// Case 1: CalendarAccurateAndRecommend, 含有精确班次
	t.Run("CalendarAccurateAndRecommend_WithAccurate", func(t *testing.T) {
		status := AVALIABLEACCURATE
		mode := CalendarAccurateAndRecommend
		result := CalenderStatusChecker(int32(status), mode)
		assert.True(t, result)
	})

	// Case 2: CalendarAccurateAndRecommend, 含有推荐班次
	t.Run("CalendarAccurateAndRecommend_WithRecommend", func(t *testing.T) {
		status := AVALIABLERecommend
		mode := CalendarAccurateAndRecommend
		result := CalenderStatusChecker(int32(status), mode)
		assert.True(t, result)
	})

	// Case 3: CalendarAccurateAndRecommend, 含有全城推荐班次
	t.Run("CalendarAccurateAndRecommend_WithWholeCityRecommend", func(t *testing.T) {
		status := AvailableWholeCityRecommend
		mode := CalendarAccurateAndRecommend
		result := CalenderStatusChecker(int32(status), mode)
		assert.True(t, result)
	})

	// Case 4: CalendarAccurateAndRecommend, 无任何有效标志
	t.Run("CalendarAccurateAndRecommend_NoValidFlags", func(t *testing.T) {
		status := 0
		mode := CalendarAccurateAndRecommend
		result := CalenderStatusChecker(int32(status), mode)
		assert.False(t, result)
	})

	// Case 5: CalendarAccurateAndRecommendWithoutDoor2Station, 含有多点到门班次和精确班次
	t.Run("CalendarAccurateAndRecommendWithoutDoor2Station_WithDoor2StationAndAccurate", func(t *testing.T) {
		status := AVALIABLEACCURATE | AvailableNormalStationBus
		mode := CalendarAccurateAndRecommendWithoutDoor2Station
		result := CalenderStatusChecker(int32(status), mode)
		assert.True(t, result)
	})

	// Case 6: CalendarAccurateAndRecommendWithoutDoor2Station, 含有多点到门班次和推荐班次
	t.Run("CalendarAccurateAndRecommendWithoutDoor2Station_WithDoor2StationAndRecommend", func(t *testing.T) {
		status := AVALIABLERecommend | AvailableNormalStationBus
		mode := CalendarAccurateAndRecommendWithoutDoor2Station
		result := CalenderStatusChecker(int32(status), mode)
		assert.True(t, result)
	})

	// Case 7: CalendarAccurateAndRecommendWithoutDoor2Station, 含有多点到门班次和全城推荐班次
	t.Run("CalendarAccurateAndRecommendWithoutDoor2Station_WithDoor2StationAndWholeCityRecommend", func(t *testing.T) {
		status := AvailableWholeCityRecommend | AvailableNormalStationBus
		mode := CalendarAccurateAndRecommendWithoutDoor2Station
		result := CalenderStatusChecker(int32(status), mode)
		assert.True(t, result)
	})

	// Case 8: CalendarAccurateAndRecommendWithoutDoor2Station, 无多点到门班次但含有精确班次
	t.Run("CalendarAccurateAndRecommendWithoutDoor2Station_WithoutDoor2StationButWithAccurate", func(t *testing.T) {
		status := AVALIABLEACCURATE
		mode := CalendarAccurateAndRecommendWithoutDoor2Station
		result := CalenderStatusChecker(int32(status), mode)
		assert.False(t, result)
	})

	// Case 9: CalendarAccurateAndRecommendWithoutDoor2Station, 无多点到门班次但含有推荐班次
	t.Run("CalendarAccurateAndRecommendWithoutDoor2Station_WithoutDoor2StationButWithRecommend", func(t *testing.T) {
		status := AVALIABLERecommend
		mode := CalendarAccurateAndRecommendWithoutDoor2Station
		result := CalenderStatusChecker(int32(status), mode)
		assert.False(t, result)
	})

	// Case 10: CalendarAccurateAndRecommendWithoutDoor2Station, 无多点到门班次但含有全城推荐班次
	t.Run("CalendarAccurateAndRecommendWithoutDoor2Station_WithoutDoor2StationButWithWholeCityRecommend", func(t *testing.T) {
		status := AvailableWholeCityRecommend
		mode := CalendarAccurateAndRecommendWithoutDoor2Station
		result := CalenderStatusChecker(int32(status), mode)
		assert.False(t, result)
	})

	// Case 11: CalendarAccurateAndRecommendWithoutDoor2Station, 无任何有效标志
	t.Run("CalendarAccurateAndRecommendWithoutDoor2Station_NoValidFlags", func(t *testing.T) {
		status := 0
		mode := CalendarAccurateAndRecommendWithoutDoor2Station
		result := CalenderStatusChecker(int32(status), mode)
		assert.False(t, result)
	})

	// Case 12: ShiftPageNavigationAccurateAndRecommend, 含有精确班次
	t.Run("ShiftPageNavigationAccurateAndRecommend_WithAccurate", func(t *testing.T) {
		status := AVALIABLEACCURATE
		mode := ShiftPageNavigationAccurateAndRecommend
		result := CalenderStatusChecker(int32(status), mode)
		assert.True(t, result)
	})

	// Case 13: ShiftPageNavigationAccurateAndRecommend, 含有推荐班次
	t.Run("ShiftPageNavigationAccurateAndRecommend_WithRecommend", func(t *testing.T) {
		status := AVALIABLERecommend
		mode := ShiftPageNavigationAccurateAndRecommend
		result := CalenderStatusChecker(int32(status), mode)
		assert.True(t, result)
	})

	// Case 14: ShiftPageNavigationAccurateAndRecommend, 无任何有效标志
	t.Run("ShiftPageNavigationAccurateAndRecommend_NoValidFlags", func(t *testing.T) {
		status := 0
		mode := ShiftPageNavigationAccurateAndRecommend
		result := CalenderStatusChecker(int32(status), mode)
		assert.False(t, result)
	})

	// Case 15: ShiftPageNavigationAccurateAndRecommendWithoutDoor2Station, 含有多点到门班次和精确班次
	t.Run("ShiftPageNavigationAccurateAndRecommendWithoutDoor2Station_WithDoor2StationAndAccurate", func(t *testing.T) {
		status := AVALIABLEACCURATE | AvailableNormalStationBus
		mode := ShiftPageNavigationAccurateAndRecommendWithoutDoor2Station
		result := CalenderStatusChecker(int32(status), mode)
		assert.True(t, result)
	})

	// Case 16: ShiftPageNavigationAccurateAndRecommendWithoutDoor2Station, 含有多点到门班次和推荐班次
	t.Run("ShiftPageNavigationAccurateAndRecommendWithoutDoor2Station_WithDoor2StationAndRecommend", func(t *testing.T) {
		status := AVALIABLERecommend | AvailableNormalStationBus
		mode := ShiftPageNavigationAccurateAndRecommendWithoutDoor2Station
		result := CalenderStatusChecker(int32(status), mode)
		assert.True(t, result)
	})

	// Case 17: ShiftPageNavigationAccurateAndRecommendWithoutDoor2Station, 无多点到门班次但含有精确班次
	t.Run("ShiftPageNavigationAccurateAndRecommendWithoutDoor2Station_WithoutDoor2StationButWithAccurate", func(t *testing.T) {
		status := AVALIABLEACCURATE
		mode := ShiftPageNavigationAccurateAndRecommendWithoutDoor2Station
		result := CalenderStatusChecker(int32(status), mode)
		assert.False(t, result)
	})

	// Case 18: ShiftPageNavigationAccurateAndRecommendWithoutDoor2Station, 无多点到门班次但含有推荐班次
	t.Run("ShiftPageNavigationAccurateAndRecommendWithoutDoor2Station_WithoutDoor2StationButWithRecommend", func(t *testing.T) {
		status := AVALIABLERecommend
		mode := ShiftPageNavigationAccurateAndRecommendWithoutDoor2Station
		result := CalenderStatusChecker(int32(status), mode)
		assert.False(t, result)
	})

	// Case 19: ShiftPageNavigationAccurateAndRecommendWithoutDoor2Station, 无任何有效标志
	t.Run("ShiftPageNavigationAccurateAndRecommendWithoutDoor2Station_NoValidFlags", func(t *testing.T) {
		status := 0
		mode := ShiftPageNavigationAccurateAndRecommendWithoutDoor2Station
		result := CalenderStatusChecker(int32(status), mode)
		assert.False(t, result)
	})

	// Case 20: 默认模式，无效模式号
	t.Run("DefaultMode_InvalidModeNumber", func(t *testing.T) {
		status := AVALIABLEACCURATE
		mode := 999
		result := CalenderStatusChecker(int32(status), mode)
		assert.False(t, result)
	})
}
func TestBizLogic_MultiPointToDoorAvailableVersion_Patch(t *testing.T) {
	// Initialize BizLogic instance
	biz := &BizLogic{
		generator: &biz_runtime.ProductsGenerator{
			BaseReqData: &models.BaseReqData{
				PassengerInfo: models.PassengerInfo{
					Phone: "13800138000",
					PID:   10001,
				},
			},
		},
	}

	// Prepare context and request object
	ctx := context.Background()
	req := &proto.IntercityMultiEstimateRequest{StartCity: 10}

	// Create patches
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	// Apply patch to MultiPointToDoorAvailableVersion method
	patches.ApplyMethod(biz, "MultiPointToDoorAvailableVersion",
		func(_ *BizLogic, _ context.Context, _ *proto.IntercityMultiEstimateRequest, _ string) bool {
			return true
		})

	// Verify that the patch is applied correctly
	result := biz.MultiPointToDoorAvailableVersion(ctx, req, "")
	assert.True(t, result, "Expected MultiPointToDoorAvailableVersion to return true")
}

func TestAvaliableInventory(t *testing.T) {
	convey.Convey("Given avaliableInventory method", t, func() {
		bl := &BizLogic{
			generator: &biz_runtime.ProductsGenerator{
				BaseReqData: &models.BaseReqData{
					PassengerInfo: models.PassengerInfo{
						Phone: "13800138000",
						PID:   10001,
					},
				},
			},
		}
		patches := gomonkey.NewPatches()
		defer patches.Reset()

		convey.Convey("Testing different scenarios", func() {
			convey.Convey("With new version and accurate shift type", func() {
				patches := gomonkey.ApplyMethodFunc(bl, "MultiPointToDoorAvailableVersion", func(_ context.Context, _ *proto.IntercityMultiEstimateRequest, _ string) bool {
					return true
				})
				defer patches.Reset()

				data := &CarpoolOpenApi.BusRegionInventory{
					Status: 1,
				}
				req := &proto.IntercityMultiEstimateRequest{}
				actual := bl.avaliableInventory(context.Background(), data, req)
				convey.So(actual, convey.ShouldBeTrue)
			})

			convey.Convey("With old version and no normal station bus", func() {
				patches := gomonkey.ApplyMethodFunc(bl, "MultiPointToDoorAvailableVersion", func(_ context.Context, _ *proto.IntercityMultiEstimateRequest, _ string) bool {
					return false
				})
				defer patches.Reset()

				data := &CarpoolOpenApi.BusRegionInventory{
					Status: 1,
				}
				req := &proto.IntercityMultiEstimateRequest{}
				actual := bl.avaliableInventory(context.Background(), data, req)
				convey.So(actual, convey.ShouldBeFalse)
			})

			convey.Convey("With old version and normal station bus and accurate shift type", func() {
				patches := gomonkey.ApplyMethodFunc(bl, "MultiPointToDoorAvailableVersion", func(_ context.Context, _ *proto.IntercityMultiEstimateRequest, _ string) bool {
					return false
				})
				defer patches.Reset()

				data := &CarpoolOpenApi.BusRegionInventory{
					Status: 1 | 4,
				}
				req := &proto.IntercityMultiEstimateRequest{}
				actual := bl.avaliableInventory(context.Background(), data, req)
				convey.So(actual, convey.ShouldBeTrue)
			})

			convey.Convey("With invalid status code", func() {
				patches := gomonkey.ApplyMethodFunc(bl, "MultiPointToDoorAvailableVersion", func(_ context.Context, _ *proto.IntercityMultiEstimateRequest, _ string) bool {
					return true
				})
				defer patches.Reset()

				data := &CarpoolOpenApi.BusRegionInventory{
					Status: 0,
				}
				req := &proto.IntercityMultiEstimateRequest{}
				actual := bl.avaliableInventory(context.Background(), data, req)
				convey.So(actual, convey.ShouldBeFalse)
			})

			convey.Convey("With nil BusRegionInventory", func() {
				patches := gomonkey.ApplyMethodFunc(bl, "MultiPointToDoorAvailableVersion", func(_ context.Context, _ *proto.IntercityMultiEstimateRequest, _ string) bool {
					return true
				})
				defer patches.Reset()

				var data *CarpoolOpenApi.BusRegionInventory
				req := &proto.IntercityMultiEstimateRequest{}
				actual := bl.avaliableInventory(context.Background(), data, req)
				convey.So(actual, convey.ShouldBeFalse)
			})
		})
	})
}
