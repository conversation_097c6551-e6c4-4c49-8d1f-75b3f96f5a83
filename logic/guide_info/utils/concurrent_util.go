package utils

import (
	"context"
	"errors"
	"git.xiaojukeji.com/nuwa/golibs/goutils"
	"sync/atomic"
	"time"
)

var runningMultiGoNum int64

type ActionFunc func(ctx context.Context, req interface{}) (err error)

type Process struct {
	Service interface{}
	Action  ActionFunc
	err     error
}

// MultiProc ...
func MultiProc(ctx context.Context, processList []*Process, timeout time.Duration) (int, error) {
	if processList == nil || len(processList) == 0 {
		return 0, nil
	}
	ch := make(chan struct{}, len(processList))
	closeCh := make(chan struct{})
	for index := range processList {

		goutils.Go(ctx, func(ctx context.Context, args ...interface{}) {
			atomic.AddInt64(&runningMultiGoNum, 1)
			proc, ok := args[0].(*Process)
			if ok {
				proc.err = proc.Action(ctx, proc.Service)
			}
			ch <- struct{}{}
		}, processList[index])

		//go func(proc *Process) {
		//	atomic.AddInt64(&runningMultiGoNum, 1)
		//	defer func() {
		//		if err := recover(); err != nil {
		//			stack := make([]byte, 8192)
		//			stack = stack[:runtime.Stack(stack, false)]
		//			log.Trace.Errorf(ctx, "concurrentBuildData", "multi_call panic:%v\n%s", err, stack)
		//		}
		//		atomic.AddInt64(&runningMultiGoNum, -1)
		//	}()
		//	proc.err = proc.Action(ctx, proc.Service)
		//	ch <- struct{}{}
		//}(processList[index])
	}
	var returnNum int
	tk := time.NewTicker(timeout)
	defer tk.Stop()
	for {
		select {
		case <-ch:
			returnNum++
			if returnNum >= len(processList) {
				return len(processList), nil
			}
		case <-tk.C:
			close(closeCh)
			return returnNum, errors.New("multi process timeout")
		}
	}
}
