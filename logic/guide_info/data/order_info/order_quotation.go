package order_info

import (
	"context"
	"encoding/json"
	"time"

	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/redis"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/estimate/price_api"
	"git.xiaojukeji.com/nuwa/trace"
)

const (
	QuotationService = "QuotationService"
	RedisKeyPre      = "quotation_cache_"
)

func (o *OrderInfoService) getQuotationData(ctx context.Context) {

	o.estimateQuotationMap = make(map[string]PriceApi.EstimateQuotation)

	if o.orderInfo == nil {
		return
	}

	if o.orderInfo.IsAnycar == "0" {
		if len(o.orderInfo.EstimateId) < 1 {
			return
		}
		// 非anycar :预约/596
		if data, err := redis.GetClient().Get(ctx, RedisKeyPre+o.orderInfo.EstimateId); err == nil {
			var q = new(PriceApi.EstimateQuotation)
			UnmarshalErr := json.Unmarshal([]byte(data), &q)
			if UnmarshalErr != nil {
				log.Trace.Warnf(ctx, QuotationService, "get quotation UnmarshalErr=%v ", err)
			} else {
				o.estimateQuotationMap[o.orderInfo.EstimateId] = *q
				return
			}
		}

		// 实时获取
		o.fetchQuotationData(ctx, []string{o.orderInfo.EstimateId})
	} else {
		// anycar
		var eidList []string
		for _, ProductInfo := range o.orderInfo.ExtendFeatureParsed.MultiRequiredProduct {
			if data, err := redis.GetClient().Get(ctx, RedisKeyPre+ProductInfo.EstimateID); err == nil {
				var q = new(PriceApi.EstimateQuotation)
				UnmarshalErr := json.Unmarshal([]byte(data), &q)
				if UnmarshalErr != nil {
					log.Trace.Warnf(ctx, QuotationService, "get quotation UnmarshalErr=%v ", err)
				} else {
					o.estimateQuotationMap[ProductInfo.EstimateID] = *q
					continue // 截断
				}
			}

			// 没取到需要实时获取
			eidList = append(eidList, ProductInfo.EstimateID)
		}

		// 实时获取
		o.fetchQuotationData(ctx, eidList)
	}

}

func (o *OrderInfoService) fetchQuotationData(ctx context.Context, eidList []string) {

	if len(eidList) <= 0 {
		return
	}

	req := &price_api.PriceQuotationBatch{
		EstimateIdList: eidList,
		Fields:         []string{},
	}

	resp, err := price_api.GetQuotationBatch(ctx, req)
	if err != nil {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "GetQuotationBatch: %v", err)
		return
	}
	if resp == nil || len(resp.Data) <= 0 {
		log.Trace.Infof(ctx, trace.DLTagUndefined, "resp is nil")
		return
	}

	for eid, data := range resp.Data {
		o.estimateQuotationMap[eid] = data

		// 异步存储
		go o.CacheQuotation(ctx, data, eid)
	}

	return
}

func (o *OrderInfoService) CacheQuotation(ctx context.Context, data PriceApi.EstimateQuotation, eid string) {
	marshal, _ := json.Marshal(data)
	if len(marshal) <= 0 {
		return
	}

	if _, err := redis.GetClient().SetEx(ctx, RedisKeyPre+eid, 20*time.Second, string(marshal)); err != nil {
		log.Trace.Warnf(ctx, QuotationService, "set redis failed %s", err)
	}
}
