package anycar_card

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/guide_info/card"
)

type AnyCarV3Card struct {
	Res *proto.PredictManageCard
}

func (p *AnyCarV3Card) RegisterRPC() []string {
	// 注册product_gen
	return []string{}
}

func (p *AnyCarV3Card) IsCanRender(context.Context, *card.Input) bool {
	return true
}

func (p *AnyCarV3Card) RenderResponse(ctx context.Context, input *card.Input, data *proto.GuideInfoData) {

}

func (p *AnyCarV3Card) GetCardLog(ctx context.Context, input *card.Input, data *proto.GuideInfoData) map[string]interface{} {
	return nil
}
