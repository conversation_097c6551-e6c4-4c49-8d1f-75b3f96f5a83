package predict_manage_card

import (
	"context"
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/guide_info/card"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	carpool3 "git.xiaojukeji.com/gulfstream/passenger-common/biz/carpool"
	carpool2 "git.xiaojukeji.com/s3e/pts/carpool"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"testing"
)

func TestBuildFeeInfo_LowPriceCarpool(t *testing.T) {
	convey.Convey("Test buildFeeInfo for EstimatePcIdLowPriceCarpool", t, func() {
		ctx := context.Background()
		p := &PredictManageCard{}

		convey.Convey("When carpool scene price has 1 and 2 seat options", func() {
			adapter := &Adapter{
				Quotation: &biz_runtime.Quotation{
					Area:            1,
					ProductCategory: estimate_pc_id.EstimatePcIdLowPriceCarpool,
					CarpoolSceneBill: &PriceApi.CarpoolSceneBill{
						ScenePrice: []*PriceApi.CarpoolScenePrice{
							{
								Option: &PriceApi.CarpoolPriceSceneOption{
									IsCarpoolSuccess: true,
									SeatNum:          1,
									PoolNum:          2,
								},
								EstimateFee: 100,
							},
							{
								Option: &PriceApi.CarpoolPriceSceneOption{
									IsCarpoolSuccess: true,
									SeatNum:          1,
									PoolNum:          1,
								},
								EstimateFee: 110,
							},
							{
								Option: &PriceApi.CarpoolPriceSceneOption{
									IsCarpoolSuccess: false,
								},
								EstimateFee: 125,
							},
							{
								Option: nil,
							},
						},
					},
				},
				input: &card.Input{
					Req: &proto.PGetGuideInfoReq{
						Pid: 1,
					},
				},
			}

			boolMock := mockey.Mock(carpool2.IsPoolInTripCarpool).Return(true).Build()
			defer boolMock.UnPatch()

			dcmpMock := mockey.Mock(dcmp.GetDcmpContent).
				Return("{\n  \"seat_1\": \"拼2人{{carpool_2}}元 | 拼1人{{carpool_1}}元\",\n  \"seat_2\": \"拼1人{{carpool_1}}元\",\n  \"seat_1_same\": \"拼成{{carpool_1}}元\",\n  \"pool_in_trip\": \"未拼成{{no_carpool}}元 | 拼成最高{{carpool_1}}元\"\n}").Build()
			defer dcmpMock.UnPatch()

			formatPriceMock := mockey.Mock(carpool3.FormatPrice).To(func(estimateFee float64, pageName string, cityId, pid string, defaultFee float64) (resp carpool3.FormatedPrice) {
				return carpool3.FormatedPrice{
					FloatVal: estimateFee,
				}
			}).Build()
			defer formatPriceMock.UnPatch()

			feeMsg, feeAmount, multiPrice := p.buildFeeInfo(ctx, adapter)

			convey.So(feeMsg, convey.ShouldEqual, "未拼成125元 | 拼成最高110元")
			convey.So(feeAmount, convey.ShouldEqual, "100")
			convey.So(multiPrice, convey.ShouldBeEmpty)
		})
	})
}
