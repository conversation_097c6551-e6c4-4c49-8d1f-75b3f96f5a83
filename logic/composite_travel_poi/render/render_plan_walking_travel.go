package render

import (
	"context"
	"strings"

	walkApi "git.xiaojukeji.com/dirpc/dirpc-go-http-DolphinApiService"
	"git.xiaojukeji.com/gulfstream/mamba/common/dcmp"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"git.xiaojukeji.com/gulfstream/mamba/logic/composite_travel/model"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
)

const (
	DcmpWalkTravelKey = "one_stop-walk_travel_poi"
)

type WalkPlanRender struct {
	*BaseRender

	planData *walkApi.PassengerWalkRouteRes
	conf     WalkingDCMPConf
}

type WalkingDCMPConf struct {
	Duration        string            `json:"duration"`
	DescTip         string            `json:"desc_tip"`
	LinkUrl         map[string]string `json:"link_url"`
	LinkUrlV2       map[string]string `json:"link_url_v2"`
	ButtonContent   string            `json:"button_content"`
	ButtonColor     string            `json:"button_color"`
	ButtonTextColor string            `json:"button_text_color"`
	LightDesc       LightDesc         `json:"light_desc"`
}

func NewWalkPlanRender(ctx context.Context, base *BaseRender) *WalkPlanRender {
	conf, err := initWalkingDCMPConf(ctx)
	if err != nil {
		log.Trace.Warnf(ctx, PlanRenderEngineTag, "init walk service dcmp error || err = %v", err)
	}
	return &WalkPlanRender{BaseRender: base, conf: conf}
}

func initWalkingDCMPConf(ctx context.Context) (WalkingDCMPConf, error) {
	conf := WalkingDCMPConf{}
	str := dcmp.GetDcmpContent(ctx, DcmpWalkTravelKey, nil)
	err := jsoniter.UnmarshalFromString(str, &conf)
	if err != nil {
		return conf, err
	}
	return conf, nil
}

func (w *WalkPlanRender) Render(ctx context.Context, plan *model.PlanFull) *proto.PlanInfo {
	data := plan.PrivateData
	if data == nil || data.WalkData == nil {
		log.Trace.Warnf(ctx, PlanRenderEngineTag, "walk plan render err || data =%v", data)
		return nil
	}
	w.planData = data.WalkData
	buttonInfo := &proto.PlanButtonInfo{
		BgGradients: strings.Split(w.conf.ButtonColor, ","),
		Content:     w.conf.ButtonContent,
		FontColor:   w.conf.ButtonTextColor,
	}
	return &proto.PlanInfo{
		Title:          w.renderPlanTitle(ctx, plan),
		PlanType:       model.WalkingTravel,
		EstimateTime:   plan.EstimateTime,
		FeeMsg:         plan.FeeMsg,
		TipData:        nil,
		ButtonInfo:     buttonInfo,
		SegmentList:    w.renderSegmentList(ctx, plan),
		DescList:       w.renderDescList(ctx, plan, w.buildWalkLightDesc, w.buildWalkTipDesc),
		RightTitleList: w.renderRightTitleList(ctx, plan),
		LinkType:       model.LinkTypeUrl,
		LinkUrl:        w.buildLinkUrl(ctx, w.conf.LinkUrl, w.conf.LinkUrlV2),
		SceneId:        plan.SceneId,
		Params:         plan.Params,
		MapParams:      plan.MapParams,
		ExtraData:      plan.ExtraData,
		RecommendTag:   plan.RecommendTag,
	}
}

func (w *WalkPlanRender) buildWalkTipDesc(ctx context.Context, plan *model.PlanFull) *proto.PlanDesc {
	return &proto.PlanDesc{
		Content: w.conf.DescTip,
	}
}

func (w *WalkPlanRender) buildWalkLightDesc(ctx context.Context, plan *model.PlanFull) *proto.PlanDesc {
	if w.planData.LightNum == nil {
		return nil
	}
	lightCount := cast.ToString(*w.planData.LightNum)
	lightDesc := util.ReplaceTag(ctx, w.conf.LightDesc.Content, map[string]string{
		"light_count": lightCount,
	})
	return &proto.PlanDesc{
		Icon:    w.conf.LightDesc.Icon,
		Content: lightDesc,
	}
}
