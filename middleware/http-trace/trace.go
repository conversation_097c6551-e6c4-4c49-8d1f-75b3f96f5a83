package trace

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	legoTrace "git.xiaojukeji.com/lego/context-go"
)

type ctxKey struct {
	name string
}

var (
	// RequestTimeKey ...
	RequestTimeKey = ctxKey{"requestTime"}
	// ExtraInfoReqOut ...
	ExtraInfoReqOut = ctxKey{"extraReqOut"}
)

// TraceConfig 是 Trace 的配置
type TraceConfig struct { // nolint: golint
	Log log.CommonLog
	// MaxMemory 为 parseMultiFrom时限制的内存使用大小
	MaxMemory int64
	// MaxBody 限制request in中打印输出的body长度最大值
	MaxBody int64
}

// TraceWithConfig 打印access log, 采用把脉日志格式，request_in
func TraceWithConfig(c TraceConfig) func(next http.Handler) http.Handler { // nolint: golint
	if c.MaxMemory == 0 {
		c.Max<PERSON>emory = 1 << 20
	}
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, req *http.Request) {
			body := "null"
			tracer := legoTrace.New(req)

			req = req.WithContext(context.WithValue(req.Context(), RequestTimeKey, time.Now()))
			req = req.WithContext(legoTrace.SetCtxTrace(req.Context(), tracer))

			var parseErr error
			switch req.Method {
			case "POST", "PUT", "PATCH":
				b, err := ioutil.ReadAll(req.Body)
				if err != nil && c.Log != nil {
					c.Log.Warnf(req.Context(), legoTrace.DLTagUndefined,
						"errmsg=Trace middleware read request body error:%s", err)
				}
				req.Body.Close()
				bodyBytes := b
				req.Body = ioutil.NopCloser(bytes.NewReader(b))
				if !strings.Contains(req.Header.Get("Content-Type"), "application/json") {
					parseErr = req.ParseMultipartForm(c.MaxMemory)
					if strings.Contains(req.Header.Get("Content-Type"), "multipart/form-data") {
						if req.MultipartForm != nil {
							b, _ = json.Marshal(req.MultipartForm.Value)
						}
					} else if strings.Contains(req.Header.Get("Content-Type"), "application/x-www-form-urlencoded") {
						b = []byte(req.Form.Encode())
					}
					req.Body = ioutil.NopCloser(bytes.NewReader(bodyBytes))
				}
				if parseErr != nil && c.Log != nil && parseErr != http.ErrNotMultipart {
					c.Log.Warnf(req.Context(), legoTrace.DLTagUndefined,
						"errmsg=Trace middleware parse form error:%s", parseErr)
				}

				maxBody := c.MaxBody

				if maxBody > 0 {
					if maxBody > int64(len(b)) {
						maxBody = int64(len(b))
					}
					body = string(b[:maxBody])
				} else {
					body = string(b)
				}
				body = strings.ReplaceAll(strings.ReplaceAll(body, "\r", ""), "\n", "")
			}
			c.Log.Infof(req.Context(), legoTrace.DLTagRequestIn,
				"proto=%s||user_agent=%s||content_type=%s||args=%s",
				req.Proto,
				req.UserAgent(),
				req.Header.Get("Content-Type"),
				body)

			rec := &bytes.Buffer{}
			writer := &traceWriter{ctx: req.Context(), log: c.Log, basicWriter: w, rec: rec, code: http.StatusOK}
			next.ServeHTTP(writer, req)
			traceRequestOut(writer, rec)
		})
	}
}

func traceRequestOut(b *traceWriter, rec fmt.Stringer) {
	var (
		output string
		format string
	)
	requestTime, ok := b.ctx.Value(RequestTimeKey).(time.Time)
	var duration time.Duration
	if ok {
		duration = time.Since(requestTime)
	}

	header := b.Header()
	contentType := strings.ToLower(header.Get("Content-Type"))
	ms := int64(duration / time.Millisecond)

	if strings.Contains(contentType, "application/json") {
		output = rec.String()
	} else {
		output = ""
	}

	format = "status=%d||response=%s||proc_time=%v"

	if b.log != nil {
		b.log.Infof(b.ctx, legoTrace.DLTagRequestOut,
			format,
			b.code, output, ms)
	}
}
