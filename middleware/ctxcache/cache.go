package ctxcache

import (
	"net/http"

	"git.xiaojukeji.com/gulfstream/mamba/common/reqctx"
)

// RequestContext 结构体
type RequestContext struct {
}

// ...
func InitRequestContext() func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, req *http.Request) {
			req = req.WithContext(reqctx.NewStore(req.Context()))
			next.ServeHTTP(w, req)
		})
	}
}
