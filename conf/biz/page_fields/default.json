{"receiver_struct_name": "proto.EstimateData", "index": {"page_type": 0}, "data": {"intro_msg": [{"conditions": ["product_category/eq/6 && city/in/1"], "render": "PlainText", "render_conf": {"text": "优享-极速出发"}}, {"render": "PlainText"}], "fee_msg": [{"comment": "拼车", "conditions": ["carpool_type/in/1,2,3,4,5"], "render": "CarpoolFeeMsg", "render_conf": {}}, {"comment": "一口价", "conditions": ["cap_price/gt/0"], "render": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "render_conf": {"dcmp_key": "estimate_data-fee_msg_conf_cap_price"}}, {"comment": "普通出租车，打表计价", "conditions": ["product_category/eq/7"], "render": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "render_conf": {"dcmp_key": "estimate_data-fee_msg_by_meter"}}, {"comment": "普通出租车，打表计价", "conditions": ["product_category/eq/300"], "render": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "render_conf": {"dcmp_key": "estimate_data-fee_msg_by_hk_fee_msg"}}, {"comment": "预估**元", "conditions": [], "render": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "render_conf": {"dcmp_key": "estimate_data-fee_msg_default"}}]}}