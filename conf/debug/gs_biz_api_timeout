{"toggle": {"namespace": "BizGoTimeout", "name": "gs_biz_api_timeout", "version": 0, "last_modify_time": 1631689778301, "log_rate": 0, "cache_plan": 0, "rule": {"subject": "bucket", "verb": "=", "objects": [[0, 1000]]}, "experiment": {"groups": [{"name": "new", "version": 3474968, "rule": {"subject": "exp_bucket", "verb": "=", "objects": [[0, 100]]}, "params": {"Bonus": {"default": {"connectTimeoutMsec": 30, "timeoutMsec": 200, "retry": 0}, "/volcano/loyalty_credit/Bonus/getBonusInfo": {"connectTimeoutMsec": 30, "timeoutMsec": 200, "retry": 0}}, "EstimateDecision": {"default": {"connectTimeoutMsec": 30, "timeoutMsec": 200, "retry": 0}, "/gulfstream/dds/products": {"connectTimeoutMsec": 500, "timeoutMsec": 1000, "retry": 0}}, "Coupon": {"default": {"connectTimeoutMsec": 30, "timeoutMsec": 200, "retry": 0}, "/foundation/coupon/v1/cardinterface/getAvailableCards": {"connectTimeoutMsec": 30, "timeoutMsec": 200, "retry": 0}}, "CommonplatCashier": {"default": {"connectTimeoutMsec": 30, "timeoutMsec": 200, "retry": 0}, "/gulfstream/pay/v1/cashier/getPassengerPayType": {"connectTimeoutMsec": 30, "timeoutMsec": 130, "retry": 0}}, "CommonPlatCoupon": {"default": {"connectTimeoutMsec": 30, "timeoutMsec": 200, "retry": 0}, "/foundation/coupon/v1/couponinterface/getAvailableCoupons": {"connectTimeoutMsec": 30, "timeoutMsec": 200, "retry": 0}, "/foundation/coupon/v3/couponinterface/multiGetAvailableCoupons": {"connectTimeoutMsec": 30, "timeoutMsec": 200, "retry": 0}}, "Geofence": {"default": {"connectTimeoutMsec": 30, "sendTimeoutMsec": 200, "recvTimeoutMsec": 200, "retry": 0}, "MultiInfence": {"connectTimeoutMsec": 30, "sendTimeoutMsec": 200, "recvTimeoutMsec": 200, "retry": 0}}, "Hotspot": {"default": {"connectTimeoutMsec": 30, "sendTimeoutMsec": 200, "recvTimeoutMsec": 200, "retry": 0}, "GetRecommendStationList": {"connectTimeoutMsec": 30, "sendTimeoutMsec": 50, "recvTimeoutMsec": 50, "retry": 0}, "MultiBubbleTrigger": {"connectTimeoutMsec": 30, "sendTimeoutMsec": 50, "recvTimeoutMsec": 50, "retry": 0}}, "MartiniProxy": {"default": {"connectTimeoutMsec": 30, "sendTimeoutMsec": 200, "recvTimeoutMsec": 200, "retry": 0}, "GetQueueLenForDP": {"connectTimeoutMsec": 30, "sendTimeoutMsec": 200, "recvTimeoutMsec": 200, "retry": 0}}, "Member": {"default": {"connectTimeoutMsec": 30, "timeoutMsec": 200, "retry": 0}, "/member/api/v1/query": {"connectTimeoutMsec": 30, "timeoutMsec": 100, "retry": 0}, "/member/api/v1/multi_products_query": {"connectTimeoutMsec": 30, "timeoutMsec": 100, "retry": 0}}, "Plutus": {"default": {"connectTimeoutMsec": 30, "timeoutMsec": 500, "retry": 1}, "/gulfstream/plutus/getEstimatePrice": {"connectTimeoutMsec": 30, "timeoutMsec": 500, "retry": 1}, "/gulfstream/plutus/getMultiEstimatePrice": {"connectTimeoutMsec": 30, "timeoutMsec": 500, "retry": 1}, "/gulfstream/plutus/getMultiComparePrice": {"connectTimeoutMsec": 30, "timeoutMsec": 500, "retry": 0}}, "PopeEngine": {"default": {"connectTimeoutMsec": 30, "timeoutMsec": 200, "retry": 0}, "/gulfstream/popeproxy/syncapi": {"connectTimeoutMsec": 30, "timeoutMsec": 250, "retry": 0}, "/gulfstream/popeproxy/batchsyncapi": {"connectTimeoutMsec": 30, "timeoutMsec": 300, "retry": 0}}, "TripCloudPassenger": {"default": {"connectTimeoutMsec": 30, "timeoutMsec": 200, "retry": 0}, "/tripcloud/v1/passenger/inner/pEstimatePrice": {"connectTimeoutMsec": 100, "timeoutMsec": 600}}, "Pandora": {"default": {"connectTimeoutMsec": 50, "timeoutMsec": 200, "retry": 0}, "/gulfstream/pandora/v1/cardInfo/getUsableCard": {"connectTimeoutMsec": 50, "timeoutMsec": 200}, "/gulfstream/pandora/v1/cardInfo/getUserCardList": {"connectTimeoutMsec": 50, "timeoutMsec": 200}, "/gulfstream/pandora/v1/cardManage/useCard": {"connectTimeoutMsec": 50, "timeoutMsec": 200}, "/gulfstream/pandora/v1/cardManage/addCardByRoute": {"connectTimeoutMsec": 50, "timeoutMsec": 200}}, "POPEActionProxy": {"default": {"connectTimeoutMsec": 20, "timeoutMsec": 200, "retry": 0}, "/gulfstream/pope_ap/actionproxy/eventhook/shake": {"timeoutMsec": 500, "connectTimeoutMsec": 100}}, "Popefs": {"default": {"connectTimeoutMsec": 50, "sendTimeoutMsec": 50, "recvTimeoutMsec": 100, "retry": 0}}, "GCS": {"default": {"connectTimeoutMsec": 30, "sendTimeoutMsec": 50, "recvTimeoutMsec": 50, "retry": 0}, "Coord2Gid": {"connectTimeoutMsec": 30, "sendTimeoutMsec": 50, "recvTimeoutMsec": 50, "retry": 0}, "Gid2Near": {"connectTimeoutMsec": 30, "sendTimeoutMsec": 50, "recvTimeoutMsec": 50, "retry": 0}}, "Adx": {"default": {"connectTimeoutMsec": 20, "timeoutMsec": 50, "retry": 0}, "/resapi/activity/mget": {"connectTimeoutMsec": 20, "timeoutMsec": 50, "retry": 0}}, "lifejuhe": {"default": {"connectTimeoutMsec": 50, "timeoutMsec": 300, "retry": 0}, "/automarket/lifejuhe/home/<USER>/widget": {"connectTimeoutMsec": 50, "timeoutMsec": 300, "retry": 0}}, "FinanceWidget": {"default": {"connectTimeoutMsec": 20, "timeoutMsec": 50, "retry": 0}, "/home/<USER>": {"connectTimeoutMsec": 20, "timeoutMsec": 100, "retry": 0}}, "MapPointSysTravelAssistant": {"default": {"connectTimeoutMsec": 20, "timeoutMsec": 100, "retry": 0}, "/map/poiservice/travel_assistant/hc_endinfo": {"connectTimeoutMsec": 20, "timeoutMsec": 100, "retry": 0}}, "TransitWind": {"default": {"connectTimeoutMsec": 50, "timeoutMsec": 300, "retry": 0}, "/inner/transit/station/widget": {"connectTimeoutMsec": 50, "timeoutMsec": 300, "retry": 0}}, "Locsvr": {"default": {"connectTimeoutMsec": 30, "sendTimeoutMsec": 50, "recvTimeoutMsec": 50, "retry": 0}, "MultiAreaInfoByCoord": {"connectTimeoutMsec": 500, "sendTimeoutMsec": 1000, "recvTimeoutMsec": 1000, "retry": 0}}, "Passport": {"default": {"connectTimeoutMsec": 30, "timeoutMsec": 100, "retry": 0}, "/passport/ticket/v5/validate": {"connectTimeoutMsec": 30, "timeoutMsec": 100, "retry": 0}}, "Ufs": {"default": {"connectTimeoutMsec": 30, "sendTimeoutMsec": 50, "recvTimeoutMsec": 50, "retry": 0}, "Mget": {"connectTimeoutMsec": 30, "sendTimeoutMsec": 50, "recvTimeoutMsec": 50, "retry": 0}, "Mset": {"connectTimeoutMsec": 30, "sendTimeoutMsec": 50, "recvTimeoutMsec": 50, "retry": 0}}, "PicassoJudge": {"default": {"connectTimeoutMsec": 30, "timeoutMsec": 50, "retry": 0}, "/gulfstream/picasso-judge/judge_tag": {"connectTimeoutMsec": 10, "timeoutMsec": 30, "retry": 0}}, "Horae": {"default": {"connectTimeoutMsec": 50, "timeoutMsec": 120, "retry": 0}, "/gulfstream/horae/v1/inner/geo/atScenes": {"connectTimeoutMsec": 50, "timeoutMsec": 120, "retry": 0}, "/gulfstream/horae/v1/inner/geo/atMultiFence": {"timeoutMsec": 100, "connectTimeoutMsec": 50}}, "PriceApi": {"default": {"connectTimeoutMsec": 500, "timeoutMsec": 2000, "retry": 0}, "/gulfstream/price/estimate/lite": {"connectTimeoutMsec": 500, "timeoutMsec": 1000, "retry": 0}, "/gulfstream/price/estimate/v3": {"connectTimeoutMsec": 500, "timeoutMsec": 2000, "retry": 0}}, "Prfs": {"default": {"connectTimeoutMsec": 50, "timeoutMsec": 200, "retry": 0}, "/gulfstream/prfs/geo/atMultiFence": {"connectTimeoutMsec": 50, "timeoutMsec": 100, "retry": 0}}, "TripCloudXiangdao": {"default": {"connectTimeoutMsec": 50, "timeoutMsec": 400, "retry": 0}}, "TripCloudGuangqi": {"default": {"connectTimeoutMsec": 50, "timeoutMsec": 400, "retry": 0}}, "TripCloudShouqi": {"default": {"connectTimeoutMsec": 50, "timeoutMsec": 400, "retry": 0}}, "TripcloudCommon": {"default": {"connectTimeoutMsec": 50, "timeoutMsec": 400, "retry": 0}}, "AxbV2Client": {"default": {"connectTimeoutMsec": 30, "timeoutMsec": 200, "retry": 0}, "/mp/axb/api/unbind": {"connectTimeoutMsec": 300, "timeoutMsec": 500, "retry": 0}}, "Carpool": {"default": {"connectTimeoutMsec": 50, "timeoutMsec": 200, "retry": 0}, "/gulfstream/carpool/v1/travel/pGetSeatNum": {"connectTimeoutMsec": 50, "timeoutMsec": 25, "retry": 0}}, "Aurora": {"default": {"connectTimeoutMsec": 50, "timeoutMsec": 200, "retry": 0}, "/aurora/v1/fetch/before-estimate-promotions": {"connectTimeoutMsec": 50, "timeoutMsec": 200, "retry": 0}}, "PbdBaiduMapClient": {"/order/external/IMNotify": {"connectTimeoutMsec": 300, "timeoutMsec": 3000, "retry": 0}, "default": {"connectTimeoutMsec": 300, "timeoutMsec": 3000, "retry": 0}}, "Dos": {"default": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 600}, "/dos/getOrderInfo": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 600}}, "AthenaApiv3": {"default": {"recvTimeoutMsec": 20, "retry": 0, "sendTimeoutMsec": 20}, "OrderMatchInfoV6": {"recvTimeoutMsec": 500, "retry": 0, "sendTimeoutMsec": 20}, "ModelAccess": {"recvTimeoutMsec": 50, "retry": 0, "sendTimeoutMsec": 20}, "QueryQueueStatus": {"recvTimeoutMsec": 500, "retry": 0, "sendTimeoutMsec": 50}}, "DispatchJudge": {"default": {"connectTimeoutMsec": 50, "sendTimeoutMsec": 50, "recvTimeoutMsec": 100, "retry": 0}, "/dmaster-dispatch/task-show": {"connectTimeoutMsec": 50, "sendTimeoutMsec": 50, "recvTimeoutMsec": 100, "retry": 0}, "/dmaster-dispatch/appeal": {"connectTimeoutMsec": 50, "sendTimeoutMsec": 50, "recvTimeoutMsec": 100, "retry": 0}, "/dmaster-dispatch/detail-lite": {"connectTimeoutMsec": 50, "sendTimeoutMsec": 50, "recvTimeoutMsec": 100, "retry": 0}, "/dmaster-dispatch/appeal-commit": {"connectTimeoutMsec": 50, "sendTimeoutMsec": 50, "recvTimeoutMsec": 100, "retry": 0}, "/dmaster-dispatch/bonus-detail": {"connectTimeoutMsec": 50, "sendTimeoutMsec": 50, "recvTimeoutMsec": 100, "retry": 0}, "/dmaster-dispatch/judge-status": {"connectTimeoutMsec": 50, "sendTimeoutMsec": 50, "recvTimeoutMsec": 50, "retry": 0}}, "mapdi": {"default": {"recvTimeoutMsec": 15, "retry": 0, "sendTimeoutMsec": 50}, "GetPoiInfoService": {"recvTimeoutMsec": 30, "retry": 0, "sendTimeoutMsec": 50}}, "DuseApi": {"AddDriver": {"recvTimeoutMsec": 100, "retry": 0, "sendTimeoutMsec": 50}, "AddDriverRoute": {"recvTimeoutMsec": 200, "retry": 1, "sendTimeoutMsec": 50}, "AddOrder": {"recvTimeoutMsec": 200, "retry": 1, "sendTimeoutMsec": 50}, "CancelV2": {"recvTimeoutMsec": 150, "retry": 0, "sendTimeoutMsec": 50}, "DelDriver": {"recvTimeoutMsec": 100, "retry": 0, "sendTimeoutMsec": 50}, "DelOrder": {"recvTimeoutMsec": 150, "retry": 0, "sendTimeoutMsec": 50}, "GetDriver": {"recvTimeoutMsec": 100, "retry": 0, "sendTimeoutMsec": 50}, "GetOrder": {"recvTimeoutMsec": 130, "retry": 0, "sendTimeoutMsec": 50}, "GrabOrderV2": {"recvTimeoutMsec": 170, "retry": 0, "sendTimeoutMsec": 50}, "PullOrderV2": {"recvTimeoutMsec": 120, "retry": 0, "sendTimeoutMsec": 50}, "QueryByWayFreshOrder": {"recvTimeoutMsec": 400, "retry": 0, "sendTimeoutMsec": 50}, "QueryCyborgOrder": {"recvTimeoutMsec": 400, "retry": 0, "sendTimeoutMsec": 50}, "QueryCyborgSwitch": {"recvTimeoutMsec": 130, "retry": 0, "sendTimeoutMsec": 50}, "QueryDriverLicenseFeature": {"recvTimeoutMsec": 100, "retry": 0, "sendTimeoutMsec": 50}, "QueryDriverRouteStatus": {"recvTimeoutMsec": 400, "retry": 0, "sendTimeoutMsec": 50}, "QueryNearByFreshOrder": {"recvTimeoutMsec": 400, "retry": 0, "sendTimeoutMsec": 50}, "QueryOrderInfo": {"recvTimeoutMsec": 100, "retry": 0, "sendTimeoutMsec": 50}, "QueryOrderPos": {"recvTimeoutMsec": 160, "retry": 0, "sendTimeoutMsec": 50}, "QueryQueueInfo": {"recvTimeoutMsec": 140, "retry": 0, "sendTimeoutMsec": 50}, "SearchCyborgOrder": {"recvTimeoutMsec": 400, "retry": 0, "sendTimeoutMsec": 50}, "UpdateDriver": {"recvTimeoutMsec": 140, "retry": 0, "sendTimeoutMsec": 50}, "UpdateOrder": {"recvTimeoutMsec": 150, "retry": 0, "sendTimeoutMsec": 50}, "UpdateOrderInfo": {"recvTimeoutMsec": 150, "retry": 0, "sendTimeoutMsec": 50}, "default": {"recvTimeoutMsec": 200, "retry": 0, "sendTimeoutMsec": 50}}, "Iapetos": {"/iapetos/dGetQuestionaire": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 60}, "/iapetos/dHasCommented": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 40}, "/iapetos/dSubmitThx": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 60}, "default": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 400}, "/iapetos/pHasCommented": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 100}, "/iapetos/pNeedComment": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 80}, "/iapetos/getWorkSheet": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 25}, "/iapetos/dGetCommentList": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 150}, "/iapetos/pGetCommentComplaint": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 150}}, "queryDriverCredit": {"default": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 400}, "credit/queryDriverCredit": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 200}, "credit/queryPStar": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 200}, "/gulfstream/nereus/queryRate": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 200}}, "Rohan": {"default": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 100}, "/rohan/rohaninner/securityStrategyBoard/checkPassengerDrunk": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 100}, "/rohan/rohaninner/shortUrlForCallCarAlarm": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 60}, "/rohan/rohaninner/checkRiskAfterOrder": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 70}, "/rohan/rohaninner/checkRiskBeforeOrder": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 40}, "/rohan/rohaninner/securityStrategyBoard/getOrderSecurityInfoForDriver": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 60}}, "DriverTask": {"default": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 50}}, "BlankaPixieService": {"default": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 200}}, "ManhattanService": {"default": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 200}}, "DcoinService": {"default": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 500}}, "nereus": {"default": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 200}}, "CashBack": {"default": {"connectTimeoutMsec": 100, "timeoutMsec": 500}, "/gulfstream/pope_ap/actionproxy/cashback/list": {"connectTimeoutMsec": 50, "timeoutMsec": 350}}, "Prepay": {"default": {"connectTimeoutMsec": 1000, "timeoutMsec": 3000}, "/gulfstream/pay/v1/openapi/getBatchPrepayOrder": {"connectTimeoutMsec": 50, "timeoutMsec": 350}}, "TripEntryService": {"default": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 50}, "/api/v1/entry/queryTripEntry": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 50}}, "MapDiThrift": {"default": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 50}, "GetPoiInfoService": {"connectTimeoutMsec": 50, "retry": 0, "timeoutMsec": 50}}, "GoMember": {"default": {"connectTimeoutMsec": 300, "timeoutMsec": 2000, "retry": 0}, "/go-member/api/v1/multi_products_query_v2": {"connectTimeoutMsec": 300, "timeoutMsec": 2000, "retry": 0}}}}]}, "schema_version": "1.0.0"}}