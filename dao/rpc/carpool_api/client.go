package carpool_api

import (
	"context"
	CarpoolApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolApi"
	CarpoolApiClient "git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/carpoolApiClient"
	"git.xiaojukeji.com/s3e/common-lib/v2/component/diff"
)

var client *CarpoolApiClient.Client

func Init() (err error) {
	if client, err = CarpoolApiClient.NewCarpoolApiClient("disf!engine-public-carpool-carpool_api"); err != nil {
		return err
	}
	return nil
}

func GetPreMatchRecommendInfo(ctx context.Context, req *CarpoolApi.PrematchRecommendRequest) (r *CarpoolApi.PrematchRecommendResponse, err error) {
	res, err := client.GetPrematchRecommendInfo(ctx, req)
	diff.CheckDiffAndLog(ctx, diff.DownStreamDirpcType, "GetPrematchRecommendInfo", res)
	return res, err
}
