package carpool_api

import (
	"context"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	"github.com/spf13/cast"
	"strconv"

	CarpoolApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolApi"
	context2 "git.xiaojukeji.com/lego/context-go"
	legoTrace "git.xiaojukeji.com/lego/context-go"
)

type MiniBusBuilder struct {
	inner *CarpoolApi.PrematchRecommendRequest
}

type GeoInfo struct {
	CityID            int32
	CurLng            float64
	CurLat            float64
	FromLng           float64
	FromLat           float64
	FromName          string
	ToLng             float64
	ToLat             float64
	ToName            string
	MapInfoCacheToken string
}

type PassengerInfo struct {
	Pid   string
	Phone string
}

func NewPrematchRecommendRequestBuilder() *MiniBusBuilder {
	return &MiniBusBuilder{
		inner: &CarpoolApi.PrematchRecommendRequest{},
	}
}

func (bd *MiniBusBuilder) SetGeoInfo(geo *GeoInfo) *MiniBusBuilder {
	bd.inner.CityID = geo.CityID

	bd.inner.CurLat = geo.CurLat
	bd.inner.CurLng = geo.CurLng

	bd.inner.StartLat = geo.FromLat
	bd.inner.StartLng = geo.FromLng
	bd.inner.StartName = geo.FromName

	bd.inner.DestLat = geo.ToLat
	bd.inner.DestLng = geo.ToLng
	bd.inner.DestName = geo.ToName

	bd.inner.ExtMap = map[string]string{
		"mapinfo_cache_token": geo.MapInfoCacheToken,
	}

	return bd
}

func (bd *MiniBusBuilder) SetPassengerInfo(passinfo *PassengerInfo) *MiniBusBuilder {
	bd.inner.Phone = passinfo.Phone
	bd.inner.Pid = passinfo.Pid
	return bd
}

func (bd *MiniBusBuilder) SetExtraInfo() *MiniBusBuilder {
	syncreq := int32(1)
	bd.inner.SyncReq = &syncreq
	return bd
}

func (bd *MiniBusBuilder) SetSyncReq(syncReq int32) *MiniBusBuilder {
	bd.inner.SyncReq = &syncReq
	return bd
}

func (bd *MiniBusBuilder) SetProductInfo(productReq []*CarpoolApi.ProductType) *MiniBusBuilder {
	bd.inner.ProductReq = productReq
	return bd
}

func (bd *MiniBusBuilder) GetReq() *CarpoolApi.PrematchRecommendRequest {
	return bd.inner
}

func (bd *MiniBusBuilder) SetWithCtx(ctx context.Context) *MiniBusBuilder {
	bd.inner.Traceid = context2.GetTrace(ctx).GetTraceId()
	if tracer, ok := legoTrace.GetCtxTrace(ctx); ok {
		bd.inner.TraceInfo = &CarpoolApi.Trace{
			LogId:       tracer.TraceId,
			Caller:      tracer.CallerFunc,
			SpanId:      &tracer.SpanId,
			SrcMethod:   &tracer.SrcMethod,
			HintContent: &tracer.HintContent,
			HintCode:    proto.Int64Ptr(cast.ToInt64(tracer.HintCode)),
		}
	}
	return bd
}

// AddProduct 添加产品
func (bd *MiniBusBuilder) AddProduct(rawProduct *Product) *MiniBusBuilder {
	prod := rawProduct.toProductType()
	bd.inner.ProductReq = append(bd.inner.ProductReq, prod)
	return bd
}

// AddExtMapRouteGroup 添加城际路线信息
func (bd *MiniBusBuilder) AddExtMapRouteGroup(routeGroup int64) *MiniBusBuilder {
	if routeGroup == 0 {
		return bd
	}
	if bd.inner.ExtMap == nil {
		bd.inner.ExtMap = make(map[string]string)
	}
	bd.inner.ExtMap["route_group"] = strconv.Itoa(int(routeGroup))
	return bd
}
