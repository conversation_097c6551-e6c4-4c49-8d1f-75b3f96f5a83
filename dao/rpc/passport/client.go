package passport

import (
	"context"
	"errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	"git.xiaojukeji.com/gulfstream/mamba/dao/rpc/kronos"
	"git.xiaojukeji.com/gulfstream/passenger-common/sdk/passport"
	"strconv"

	legoTrace "git.xiaojukeji.com/lego/context-go"

	"git.xiaojukeji.com/gulfstream/mamba/common/util"

	Passport "git.xiaojukeji.com/dirpc/dirpc-go-http-Passport"
	PassportClient "git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/passportClient"
)

// UserInfo ...
// https://base3.xiaojukeji.com/docs/passport/%E6%A6%82%E8%A7%88/%E6%9C%AF%E8%AF%AD.html
type UserInfo struct {
	PID      uint64
	UID      uint64
	Phone    string
	Role     int32
	DUid     uint64
	Channel  string
	AppID    int32
	OriginId string

	UserGender         int32
	CountryCallingCode string
	CreateTime         int64
	Cell               string
	IsTestCell         bool
}

const (
	Caller = "gs_api"
	Callee = "Kronos"

	ErrnoOfflinePassenger = -201
)

var (
	ErrNot200  = errors.New("errCodeNot200")
	ErrOffline = errors.New("errCodeOffline")
	client     *PassportClient.Client
)

func GetPassportClient() *Passport.Client {
	if client != nil {
		return client.Client
	}
	return nil
}

func InitClient() (err error) {
	client, err = PassportClient.NewPassportClient("disf!common-plat-public-passport")
	if err != nil {
		return err
	}
	return nil
}

func GetUserInfo(ctx context.Context, token string, funcCaller string) (*UserInfo, error) {
	var (
		resp     *Passport.ValidatePlusResp
		userInfo *UserInfo
		err      error
	)

	if apollo.FeatureToggle(ctx, "passport_switch_to_cache", token, map[string]string{
		"caller": "pre-sale-core",
	}) {
		resp, err = passport.GetPassportInfoByTokenWithFusion(ctx, token)
		if errors.Is(err, passport.Passport201Err) {
			return nil, ErrOffline
		}
	} else {
		c := Caller
		req := &Passport.ValidatePlusReq{
			Q: &Passport.ValidatePlusRawBody{
				Ticket:   token,
				CallerId: &c,
			},
		}
		resp, err = client.ValidatePlus(ctx, req)
	}

	if err != nil {
		return nil, err
	}

	if resp.Errno == ErrnoOfflinePassenger {
		return nil, ErrOffline
	}

	if resp.Errno != 0 {
		return nil, ErrNot200
	}

	userInfo = &UserInfo{}
	if resp.Uid != nil {
		userInfo.UID = uint64(*resp.Uid)
		userInfo.PID = util.GetPidByUid(userInfo.UID)
		if trace, ok := legoTrace.GetCtxTrace(ctx); ok {
			_ = trace.SetCustomKV("uid", strconv.FormatUint(userInfo.UID, 10))
			_ = trace.SetCustomKV("pid", strconv.FormatUint(util.GetPidByUid(userInfo.UID), 10))
			_ = trace.SetCustomKV("passenger_id", strconv.FormatUint(util.GetPidByUid(userInfo.UID), 10))
		}
	}
	if resp.Cell != nil {
		if resp.CountryCallingCode != nil {
			userInfo.Phone = *resp.CountryCallingCode + *resp.Cell
			userInfo.CountryCallingCode = *resp.CountryCallingCode
		} else {
			userInfo.Phone = *resp.Cell
		}
	}
	if resp.Role != nil {
		userInfo.Role = *resp.Role
	}
	if resp.Duid != nil {
		userInfo.DUid = uint64(*resp.Duid)
	}
	if resp.Channel != nil {
		userInfo.Channel = *resp.Channel
	}
	if resp.Appid != nil {
		userInfo.AppID = *resp.Appid
	}
	if resp.OriginId != nil {
		userInfo.OriginId = *resp.OriginId
	}
	if resp.CreateTime != nil {
		userInfo.CreateTime = *resp.CreateTime
	}
	if resp.Cell != nil {
		userInfo.Cell = *resp.Cell
	}
	if resp.IsTestCell != nil {
		userInfo.IsTestCell = *resp.IsTestCell
	}

	// 用户性别 0-未实名，1-实名女性，2-实名男性
	userInfo.UserGender = 0
	if funcCaller == Callee {
		// 目前只有anycarV3使用
		userInfo.UserGender = kronos.GetGenderByUid(ctx, userInfo.UID)
	}

	return userInfo, nil
}
