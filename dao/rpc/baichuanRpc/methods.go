package baichuanRpc

import (
	"context"
	baichuan "git.xiaojukeji.com/dirpc/dirpc-go-http-Baichuan"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
)

// GetPrivacyNotSign 获取没签署的协议列表
func GetPrivacyNotSign(ctx context.Context, scene string, appID int64, uid int64) (*Privacy, error) {
	req := &baichuan.PopListReq{
		SceneList: []string{scene},
		Appid:     appID,
		Caller:    consts.MambaCaller,
		Uid:       util.Int64Ptr(uid),
	}
	resp, err := SignPopListWithResp(ctx, req)
	if err != nil {
		return nil, err
	}

	if len(resp.Data.DocMap) == 0 {
		return nil, nil
	}
	ret := &Privacy{}
	// 只会有一个
	for _, v := range resp.Data.DocMap {
		ret.LinkUrl = v.LinkUrl
		ret.DocId = v.DocId
		ret.Name = v.Name
		break
	}

	return ret, nil
}
