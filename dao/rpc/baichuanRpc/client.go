package baichuanRpc

import (
	"context"
	"errors"
	baichuan "git.xiaojukeji.com/dirpc/dirpc-go-http-Baichuan"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/conf"
)

var client *baichuan.Client

const baichuanConfPath = "baichuan.disf"

func InitClient() error {
	var err error
	client, err = baichuan.NewClient(conf.Viper.GetString(baichuanConfPath))
	if err != nil {
		return err
	}
	return nil
}

func SignPopListWithResp(ctx context.Context, req *baichuan.PopListReq) (*baichuan.PopListResp, error) {
	res, response, err := client.SignPopListWithResp(ctx, req)
	if err != nil {
		return nil, err
	}
	if response.StatusCode != 200 {
		return nil, errors.New("baichuan sign pop list network not success")
	}
	if res == nil || res.Errno != 0 {
		return nil, errors.New("baichuan sign pop list failed")
	}

	return res, nil
}
