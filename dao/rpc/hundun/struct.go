package hundun

type HundunNodeReq struct {
	ProductCategory   int               `json:"product_category"`
	ProductId         int               `json:"product_id"`
	BusinessId        int               `json:"business_id"`
	ComboType         int               `json:"combo_type"`
	CarpoolType       int               `json:"carpool_type"`
	RequireLevel      string            `json:"require_level"`
	Pid               int64             `json:"pid"`
	Phone             string            `json:"phone"`
	AccessKeyId       int               `json:"access_key_id"`
	AppVersion        string            `json:"app_version"`
	Lang              string            `json:"lang"`
	Area              int               `json:"area"`
	Flat              float64           `json:"flat"`
	Flng              float64           `json:"flng"`
	Tlat              float64           `json:"tlat"`
	Tlng              float64           `json:"tlng"`
	Type              int               `json:"type"`
	PageType          int               `json:"page_type"`
	CallCarType       int               `json:"call_car_type"`
	DepartureTime     int64             `json:"departure_time"`
	MenuId            string            `json:"menu_id"`
	SubMenuId         string            `json:"sub_menu_id"`
	TrafficNumber     string            `json:"traffic_number"`
	TrafficDepTime    string            `json:"traffic_dep_time"`
	AirportId         int               `json:"airport_id"`
	RailwayType       int               `json:"railway_type"`
	FlightDepCode     string            `json:"flight_dep_code"`
	FlightDepTerminal string            `json:"flight_dep_terminal"`
	ExtraInfo         map[string]string `json:"extra_info"`
}
