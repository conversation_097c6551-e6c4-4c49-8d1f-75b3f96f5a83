package hundun

import (
	"context"
	hundunClient "git.xiaojukeji.com/dirpc/dirpc-go-http-Hundun"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime/models"
	"git.xiaojukeji.com/s3e/common-lib/v2/component/diff"
	"strconv"
)

const (
	disfName = "disf!biz-gs-hundun"
	Caller   = "mamba"
	Callee   = "hundun"
)

var (
	client *hundunClient.Client
)

// InitClient 初始化混沌 client
func InitClient() (err error) {
	client, err = hundunClient.NewClient(disfName)
	if err != nil {
		return err
	}
	return nil
}

func GetService(ctx context.Context, req *hundunClient.SceneReq) map[models.ProductCategory][]*hundunClient.PcServiceData {
	diff.CheckDiffAndLog(ctx, diff.DownStreamDirpcType, "/gulfstream/hundun/inner/scene/getScene", req)
	res, err := client.GetNewScene(ctx, req)
	if err != nil {
		return nil
	}

	if res == nil || res.Errno != 0 || len(res.Data) == 0 {
		return nil
	}

	resp := make(map[models.ProductCategory][]*hundunClient.PcServiceData, len(res.Data))
	for i, sceneData := range res.Data {
		productCategory, _ := strconv.ParseInt(i, 10, 64)
		resp[models.ProductCategory(productCategory)] = sceneData.GetServiceData()
	}

	return resp
}

func GetTailorServiceV2(ctx context.Context, req *hundunClient.GetTailorServiceReq) *hundunClient.PreferDataV2 {
	res, err := client.GetTailorServiceV2(ctx, req)
	if err != nil {
		return nil
	}

	if res == nil || res.Errno != 0 || res.Data == nil {
		return nil
	}

	return res.Data
}
