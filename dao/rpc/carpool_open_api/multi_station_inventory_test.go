package carpool_open_api

import (
	CarpoolOpenApi "git.xiaojukeji.com/dirpc/dirpc-go-thrift-CarpoolOpenApi"
	. "github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
	"testing"
)

func Test_CheckApolloToggle(t *testing.T) {
	PatchConvey("Test_CheckApolloToggle", t, func() {
		Convey("Case1: true ", func() {
			multiStationInventoryBuilder := &MultiStationInventoryBuilder{
				inner: &CarpoolOpenApi.QueryRouteShiftInventoryInfoReq{
					SortType: nil,
				},
			}
			var sort int32 = 1
			multiStationInventoryBuilder.SetSortType(&sort)
		})
	})
}
