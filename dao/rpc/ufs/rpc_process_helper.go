package ufs

import (
	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/ufsClient"
)

const (
	WaitSendEmptyCarTimeKey           = "low_price_carpool.wait_send_empty_car_time"
	StationEstimateCheckOptimizedData = "station_estimate.check_optimized_data" // StationEstimateCheckOptimizedData 乘客站点巴士批量预估检查optimizedData是否弹出
	TripcloudAuthKey                  = "tripcloud.is_used"
	SFCTripcloudAuthKey               = "sfc_guide_tripcloud.is_used"
	VideoAuthKey                      = "open_platform.record_auth"
	VideoAuthKeyNew                   = "video_sign_new"
	ButianAuthKey                     = "butian.is_authed"
	AlwaysPoolKey                     = "always_pool_success"
	FemaleDriverKey                   = "is_female_driver_first.selected"
	SFCWYCAuthKey                     = "video_sign_new"
	FeatureParamKey_EID               = "estimate_id"
	FeatureParamKey_PID               = "passenger_id"
)

func GenFeatureKey(feature ufsClient.Feature) string {
	keys, _ := ufsClient.BuildFeatures([]ufsClient.Feature{feature})
	if len(keys) > 0 {
		return keys[0]
	}

	return ""
}

func GenFeature(domain string, key string, params map[string]string) ufsClient.Feature {
	return ufsClient.Feature{
		Domain: domain,
		Keys:   []string{key},
		Params: params,
	}
}
