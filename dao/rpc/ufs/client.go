package ufs

import (
	"context"
	"errors"
	"fmt"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"

	ufsThrift "git.xiaojukeji.com/dirpc/dirpc-go-thrift-UFS"
	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/ufsClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	trace "git.xiaojukeji.com/lego/context-go"
)

const (
	LogTag = "ufs_get_feature"
	// ufs domain
	DomainPassenger = "passenger"
	DomainOrder     = "order"
	// ufs key
	KeyAthenaDefaultSelectInfo = "base.last_bubble_athena_default_select_info"
	KeyCommitCommentFrequency  = "composite_travel.commit_times"
	KeyBargainFirst            = "bargain.frist"
	KeyBargainMask             = "mask.layer"
	KeyPetsTravelCarryBag      = "bring_pet_order.pet_info"
	KeyOverseaProtocol         = "user_information_authorization.oversea"
)

var client *ufsClient.FeatureClient

func InitClient() (err error) {
	if client, err = ufsClient.NewFeatureClient("disf!biz-gs-ufs"); err != nil {
		return err
	}
	return nil
}

func GetFeature(ctx context.Context, domain string, keys []string, params map[string]string, srcMethod string) (map[string]*ufsThrift.FeatureResponse, error) {
	_trace, ok := trace.GetCtxTrace(ctx)
	if !ok {
		return nil, consts.GetErr(consts.ErrnoGetContextTrace)
	}
	_ufsTrace := &ufsThrift.Trace{
		TraceId:     _trace.TraceId,
		Caller:      "mamba",
		SpanId:      _trace.SpanId,
		SrcMethod:   srcMethod,
		HintCode:    _trace.HintCode,
		HintContent: _trace.HintContent,
	}
	resp, err := client.MgetFeatures(ctx, domain, keys, params, _ufsTrace)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("keys=%v||params=%v||err=%v", keys, params, err))
	}
	if resp.Errno != 0 {
		return nil, errors.New(fmt.Sprintf("keys=%v||params=%v||errno=%d||errmsg=%s", keys, params, resp.Errno, resp.Errmsg))
	}
	log.Trace.Infof(ctx, LogTag, "domain=%s||keys=%v||params=%v||resp_data=%v", domain, keys, params, util.ToString(resp.Data))
	return resp.Data, nil
}

// GetFeatureV2 获取UFS单key值
// ufs 可理解为 map[string]string的存储
// 目的也只是获取对应的值, 至于ufs如何组织的上层调用不关注
func GetFeatureV2(ctx context.Context, domain string, key string, params map[string]string) (string, error) {
	resp, err := GetFeature(ctx, domain, []string{key}, params, "")
	if err != nil {
		return "", err
	}
	oneResp, ok := resp[key]
	if !ok || oneResp == nil {
		return "", fmt.Errorf("keys=%v||params=%v||no this key", key, params)
	}
	if oneResp.Errno != 0 || oneResp.Value == nil {
		return "", fmt.Errorf("keys=%v||params=%v||errno=%d||errmsg=%s", key, params, oneResp.Errno, oneResp.Errmsg)
	}
	return oneResp.GetValue(), nil
}

func MultiGetFeatures(ctx context.Context, features []ufsClient.Feature, srcMethod string) (map[string]*ufsThrift.FeatureResponse, error) {
	_trace, ok := trace.GetCtxTrace(ctx)
	if !ok {
		return nil, consts.GetErr(consts.ErrnoGetContextTrace)
	}
	_ufsTrace := &ufsThrift.Trace{
		TraceId:     _trace.TraceId,
		Caller:      "mamba",
		SpanId:      _trace.SpanId,
		SrcMethod:   srcMethod,
		HintCode:    _trace.HintCode,
		HintContent: _trace.HintContent,
	}
	resp, err := client.MGet(ctx, features, _ufsTrace)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("features=%v||err=%v", features, err))
	}
	if resp.Errno != 0 {
		return nil, errors.New(fmt.Sprintf("features=%v||errno=%d||errmsg=%s", features, resp.Errno, resp.Errmsg))
	}

	return resp.Data, nil
}

func SetFeature(ctx context.Context, domain string, params map[string]string, kv map[string]string) (map[string]*ufsThrift.FeatureResponse, error) {
	_trace, ok := trace.GetCtxTrace(ctx)
	if !ok {
		return nil, consts.GetErr(consts.ErrnoGetContextTrace)
	}
	_ufsTrace := &ufsThrift.Trace{
		TraceId:     _trace.TraceId,
		Caller:      "mamba",
		SpanId:      _trace.SpanId,
		SrcMethod:   "",
		HintCode:    _trace.HintCode,
		HintContent: _trace.HintContent,
	}
	resp, err := client.MsetFeatures(ctx, domain, kv, params, _ufsTrace)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("kv=%v||params=%v||err=%v", kv, params, err))
	}
	if resp.Errno != 0 {
		return nil, errors.New(fmt.Sprintf("kv=%v||params=%v||errno=%d||errmsg=%s", kv, params, resp.Errno, resp.Errmsg))
	}
	log.Trace.Debugf(ctx, LogTag, "domain=%s||kv=%v||params=%v||resp_data=%v", domain, kv, params, resp.Data)
	return resp.Data, nil
}

// Mset 方法
func MSetMultiFeature(ctx context.Context, domain string, key, value string, params []*ufsClient.FeatureParams) (map[string]*ufsThrift.FeatureResponse, error) {
	_trace, ok := trace.GetCtxTrace(ctx)
	if !ok {
		return nil, consts.GetErr(consts.ErrnoGetContextTrace)
	}
	_ufsTrace := &ufsThrift.Trace{
		TraceId:     _trace.TraceId,
		Caller:      "mamba",
		SpanId:      _trace.SpanId,
		SrcMethod:   "",
		HintCode:    _trace.HintCode,
		HintContent: _trace.HintContent,
	}

	resp, err := client.MSetMultiFeature(ctx, domain, key, value, params, _ufsTrace)
	if err != nil {
		return nil, fmt.Errorf("key=%s||value=%s||params=%v||err=%v", key, value, params, err)
	}
	if resp.Errno != 0 {
		return nil, fmt.Errorf("key=%s||value=%s||params=%v||errno=%d||errmsg=%s", key, value, params, resp.Errno, resp.Errmsg)
	}
	log.Trace.Debugf(ctx, LogTag, "domain=%s||key=%s||value=%s||params=%v||resp_data=%v", domain, key, value, params, resp.Data)
	return resp.Data, nil

}

// MgetProxy ...
func MgetProxy(ctx context.Context, domain string, keys []string, param map[string]interface{}) (resp *ufsThrift.UFSResponse, err error) {
	_trace, ok := trace.GetCtxTrace(ctx)
	if !ok {
		return nil, consts.GetErr(consts.ErrnoGetContextTrace)
	}
	_ufsTrace := &ufsThrift.Trace{
		TraceId:     _trace.TraceId,
		Caller:      "mamba",
		SpanId:      _trace.SpanId,
		SrcMethod:   "",
		HintCode:    _trace.HintCode,
		HintContent: _trace.HintContent,
	}
	resp, err = client.MgetProxyFeatures(ctx, domain, keys, param, _ufsTrace)
	if err != nil {
		log.Trace.Warnf(ctx, trace.DLTagUndefined, "ufs mget fail||err=%+v||domain=%s||keys=%+v||param=%+v", err, domain, keys, param)
		return nil, err
	}

	return resp, nil
}
