package plutus

import (
	"context"
	"fmt"
	plutus "git.xiaojukeji.com/dirpc/dirpc-go-http-Plutus"
	plutusClient "git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/plutusClient"
)

var (
	client *plutusClient.Client
)

func InitClient() error {
	var err error
	client, err = plutusClient.NewPlutusClient("disf!commonplat-gs-plutus")
	if err != nil {
		return err
	}

	return nil
}

func GetMultiStrategies(ctx context.Context, req *plutus.GetMultiStrategiesRequest) (*plutus.GetMultiStrategiesResp, error) {
	rsp, err := client.GetMultiStrategies(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("GetMultiStrategies failed with err=%s", err.Error())
	}

	if rsp == nil || rsp.Data == nil || rsp.Data.MultiResponse == nil {
		return nil, fmt.Errorf("invalid GetMultiStrategies resp")
	}

	return rsp, nil
}

func GetStrategies(ctx context.Context, req *plutus.GetStrategiesRequest) (*plutus.GetStrategiesResp, error) {
	rsp, err := client.GetStrategies(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("GetStrategies failed with err=%s", err.Error())
	}

	if rsp == nil || rsp.Data == nil {
		return nil, fmt.Errorf("invalid GetStrategies resp")
	}

	return rsp, nil
}

func UpdateTravel(ctx context.Context, req *plutus.UpdateTravelReq) *plutus.UpdateTravelResponse {
	resp, err := client.UpdateTravel(ctx, req)
	if err != nil {
		//return nil, fmt.Errorf("UpdateTravel failed with err=%s", err.Error())
		return nil
	}
	if resp == nil {
		//return nil, fmt.Errorf("invalid UpdateTravel resp")
		return nil
	}
	if resp.Errno != 0 {
		//return nil, fmt.Errorf("errno is not equal zero")
		return nil
	}
	return resp
}

func DriverEstimate(ctx context.Context, req *plutus.MultilDriverEstimateRequest) (*plutus.GetMultiDriverEstimatePriceResp, error) {
	rsp, err := client.GetMultiDriverEstimatePrice(ctx, req)
	return rsp, err
}
