package hilda

import (
	"context"
	"errors"
	"git.xiaojukeji.com/s3e/common-lib/v2/component/diff"

	hilda "git.xiaojukeji.com/dirpc/dirpc-go-http-Hilda"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/nuwa/trace"
)

const disfName = "disf!biz-passenger-operation-hilda"

var (
	client *hilda.Client // 当 SDK 可用时使用这个
)

// InitClient 初始化 Hilda client
func InitClient() (err error) {
	client, err = hilda.NewClient(disfName)
	if err != nil {
		return err
	}
	return nil
}

// PrivilegeQueryV2 调用 Hilda 权益查询服务
func PrivilegeQueryV2(ctx context.Context, request *hilda.QueryPrivilegeReq) (*hilda.QueryPrivilegeResp, error) {
	diff.CheckDiffAndLog(ctx, diff.DownStreamDirpcType, "/volcano/hilda/user/query/privilege/v2", request)
	response, err := client.QueryPrivilegeV2(ctx, request)
	if err != nil {
		log.Trace.Errorf(ctx, trace.DLTagUndefined, "Failed to call hilda service: %v", err)
		return nil, err
	}

	if response.Errno != 0 {
		log.Trace.Errorf(ctx, trace.DLTagUndefined, "hilda service returned error: %v", response.Errmsg)
		return nil, errors.New(response.Errmsg)
	}

	return response, nil
}
