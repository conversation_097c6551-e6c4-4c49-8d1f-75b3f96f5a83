package bicycle_route

import (
	"context"

	route "git.xiaojukeji.com/dirpc/dirpc-go-http-OrderRouteApi"
	OrderRouteApiClient "git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/mapLineSysOrderRouteApiClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
)

const (
	disfName = "disf!map-line-sys-online-service-order_route_api"
	logTag   = "OrderRouteApi"
)

var (
	client *OrderRouteApiClient.Client
)

// InitClient 初始化client
func InitClient() (err error) {
	client, err = OrderRouteApiClient.NewOrderRouteApiClient(disfName)
	if err != nil {
		return err
	}
	return nil
}

func RecommendBicyclingTripInfo(ctx context.Context, req *route.RecommendBicyclingTripReq) *route.RecommendBicyclingTripRes {
	// 接口文档:http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=1054474934
	res, err := client.GetRecommendBicyclingTripInfo(ctx, req)

	if err != nil {
		log.Trace.Warnf(ctx, logTag, "RecommendBicyclingTrip error=[%v]", err)
		return nil
	}

	if res == nil || res.Ret != 0 || res.RouteDetail == nil || len(res.RouteDetail) == 0 {
		log.Trace.Warnf(ctx, logTag, "RecommendBicyclingTrip res exception=[%s]", util.ToJSONString(res))
		return nil
	}
	return res
}
