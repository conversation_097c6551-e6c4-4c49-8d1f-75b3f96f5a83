package seat

import (
	"context"

	Carpool "git.xiaojukeji.com/dirpc/dirpc-go-http-Carpool"
	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/carpoolClient"
)

var (
	client *carpoolClient.Client
)

func Init() (err error) {
	client, err = carpoolClient.NewCarpoolClient("disf!biz-gs-carpoolweb")
	if err != nil {
		return err
	}
	return nil
}

func GetSeatInfo(ctx context.Context, req *Carpool.PGetSeatNumReq) (*Carpool.PGetSeatNumResp, error) {
	return client.PGetSeatNum(ctx, req)
}
