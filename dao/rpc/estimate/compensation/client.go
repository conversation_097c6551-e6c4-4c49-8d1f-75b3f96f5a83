package compensation

import (
	"context"
	"errors"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/redis"
	"git.xiaojukeji.com/s3e/common-lib/v2/component/diff"

	Compensation "git.xiaojukeji.com/dirpc/dirpc-go-http-Compensation"
	"git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/compensationClient"
)

const (
	BusinessAirportInsurance   = "to_airport_insurance"
	BusinessAPlusInsurance     = "no_car_aplus"
	NoAnswerCompensation       = "no_answer_compensation"        // 未应答赔付
	NormalNoAnswerCompensation = "normal_no_answer_compensation" // 平台无车赔
)

var (
	client                           *compensationClient.Client
	ErrEmptyMultiCompensationAbility = errors.New("emptyMultiCompensationAbility")
)

// InitClient 初始化赔付client
func InitClient() (err error) {
	client, err = compensationClient.NewCompensationClient("disf!biz-gs-compensation")

	if err != nil {
		return err
	}
	return nil
}

// GetMultiCompensationAbility 使用构建的入参数, 去赔付服务获取数据
func GetMultiCompensationAbility(ctx context.Context, req *Compensation.GetMultiCompensationAbilityReq) (rsp *Compensation.GetMultiCompensationAbilityResp) {

	resp, err := client.GetMultiCompensationAbility(ctx, req)
	if err != nil {
		return nil
	}

	return resp
}

func GetMultiCompensationAbilityV2(ctx context.Context, req *Compensation.GetMultiCompensationAbilityV2Req) (rsp *Compensation.GetMultiCompensationAbilityV2Resp) {
	resp, err := client.GetMultiCompensationAbilityV2(ctx, req)
	if err != nil {
		return nil
	}

	return resp
}

func GetLightMultiCompensationAbility(ctx context.Context, req *Compensation.GetLightMultiCompensationAbilityReq) (rsp *Compensation.GetLightMultiCompensationAbilityResp, err error) {
	resp, err := client.GetLightMultiCompensationAbility(ctx, req)
	if err != nil {
		return resp, err
	}

	return resp, nil
}

func GetCompensationAbilityDetail(ctx context.Context, req *Compensation.GetCompensationAbilityDetailReq) *Compensation.GetCompensationAbilityDetailResp {
	resp, err := client.GetCompensationAbilityDetail(ctx, req)
	if err != nil {
		return nil
	}

	return resp
}

func TriggerTack(ctx context.Context, req *Compensation.TriggerTackReq) *Compensation.TriggerTackResp {
	if diff.CheckDiffStatus(ctx) {
		mocker := diff.NewMockInterceptor(redis.GetMultiEstimateClient(), diff.MultiEstimateDiffPrefix)
		resp := &Compensation.TriggerTackResp{}
		err := mocker.MockResp(ctx, diff.DownStreamDirpcType, "/gulfstream/compensation/triggerTack", req, resp)
		if err != nil {
			log.Trace.Warnf(ctx, diff.DiffTag, "mockResp fail:%v", err)
			return nil
		}

		return resp
	}

	resp, err := client.TriggerTack(ctx, req)
	if err != nil {
		return nil
	}

	return resp
}
