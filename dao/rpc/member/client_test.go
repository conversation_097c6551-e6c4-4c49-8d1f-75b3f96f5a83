package member

//func TestMain(m *testing.M) {
//
//	_, fn, _, _ := runtime.Caller(0)
//
//	confDir := filepath.Dir(fn)
//	confPath := filepath.Join(confDir, "../../../conf")
//	err := introspection.SetupFromConfig(confPath+"/disf.yaml.bak", nil)
//	if err != nil {
//		fmt.Println(err)
//		return
//	}
//	err = dirpc.Setup("", nil)
//	if err != nil {
//		fmt.Println(err)
//		return
//	}
//	err = InitClient()
//	if err != nil {
//		fmt.Println(err)
//		return
//	}
//
//	os.Exit(m.Run())
//}
//
//func TestMultiProductQuery(t *testing.T) {
//	type args struct {
//		ctx                context.Context
//		uid                int64
//		cityId             int32
//		productList        []Product
//		enableNewWaitReply int32
//	}
//	tests := []struct {
//		name               string
//		args               args
//		wantProductListRes *GoMember.MultiQueryV2Resp
//		wantErr            bool
//	}{
//		{
//			name: "",
//			args: args{
//				ctx:    context.TODO(),
//				uid:    299067164403671,
//				cityId: 1,
//				productList: []Product{
//					{
//						BusinessId: 260,
//					},
//				},
//				enableNewWaitReply: 1,
//			},
//			wantProductListRes: &GoMember.MultiQueryV2Resp{
//				Data: &GoMember.MultiQueryInfoV2{
//					ProductList: []*GoMember.ProductListRespV2{
//						{
//							ProductInfo: &GoMember.ProductInfo{
//								BusinessId: 260,
//							},
//						},
//					},
//				},
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			gotProductListRes, err := MultiProductQuery(tt.args.ctx, tt.args.uid, tt.args.cityId, tt.args.productList, tt.args.enableNewWaitReply)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("MultiProductQuery() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(gotProductListRes.Errno, tt.wantProductListRes.Errno) {
//				t.Errorf("MultiProductQuery() = %v, want %v", gotProductListRes, tt.wantProductListRes)
//			}
//			if !reflect.DeepEqual(len(gotProductListRes.Data.ProductList), len(tt.wantProductListRes.Data.ProductList)) {
//				t.Errorf("MultiProductQuery() = %v, want %v", gotProductListRes, tt.wantProductListRes)
//			}
//			if len(gotProductListRes.Data.ProductList) > 0 {
//				for i := range gotProductListRes.Data.ProductList {
//					if !reflect.DeepEqual(gotProductListRes.Data.ProductList[i].ProductInfo.BusinessId, tt.wantProductListRes.Data.ProductList[i].ProductInfo.BusinessId) {
//						t.Errorf("MultiProductQuery() = %v, want %v", gotProductListRes, tt.wantProductListRes)
//					}
//				}
//			}
//		})
//	}
//}
