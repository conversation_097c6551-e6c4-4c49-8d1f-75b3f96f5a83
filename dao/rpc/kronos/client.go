package kronos

import (
	"context"
	"errors"
	"fmt"
	kronosSystem "git.xiaojukeji.com/dirpc/dirpc-go-http-Kronos"
)

const (
	MOUDULE_NAME = "disf!common-plat-public-kronos"
	Caller       = "mamba"
	gender_field = "gender_cardid"
)

var client *kronosSystem.Client

// InitClient 初始化司机client

func InitClient() (err error) {
	if client, err = kronosSystem.NewClient(MOUDULE_NAME); err != nil {
		return err
	}

	return nil
}

func GetGenderByUid(ctx context.Context, uid uint64) int32 {
	resp, err := GetDataByUid(ctx, uid, []string{gender_field})
	if err != nil || resp == nil {
		return 0
	}

	gender := resp.Data[gender_field]
	if gender == "f" {
		return 1
	} else if gender == "m" {
		return 2
	} else {
		return 0
	}
}

func GetDataByUid(ctx context.Context, uid uint64, fields []string) (*kronosSystem.UserinfoGetByUidResp, error) {
	req := &kronosSystem.UserinfoGetByUidReq{
		Uid:    uid,
		Fields: fields,
		Caller: Caller,
	}

	resp, err := client.UserinfoGetByUid(ctx, req)
	if err != nil || resp == nil {
		return nil, errors.New(fmt.Sprintf("Trying to get kronos infomation gots wrong,err=%v", err))
	}
	if resp.Errno != 0 || resp.Data == nil {
		return nil, errors.New(fmt.Sprintf("The response from kronos has some mistakes,errno=%d||errmsg=%s", resp.Errno, resp.Errmsg))
	}

	return resp, nil
}
