package sps

import (
	"context"
	sps "git.xiaojukeji.com/dirpc/dirpc-go-http-Sps"
	"git.xiaojukeji.com/s3e/common-lib/v2/component/diff"
)

const disfName = "disf!biz-gs-sps"

var (
	client *sps.Client
)

// InitClient 初始化赔付client
func InitClient() (err error) {
	client, err = sps.NewClient(disfName)
	if err != nil {
		return err
	}
	return nil
}

func GetCustomServiceFeeInfo(ctx context.Context, req *sps.GetCustomedServiceFeeInfoReq) map[int32]map[int64]*sps.FeeItem {
	var (
		spsFeeMap = make(map[int32]map[int64]*sps.FeeItem) // [pcId]: ([serviceId]: feeInfo)
	)

	diff.CheckDiffAndLog(ctx, diff.DownStreamDirpcType, "/gulfstream/sps/v1/getCustomedServiceFeeInfo", req)
	resp, err := client.GetCustomedServiceFeeInfo(ctx, req)
	if err != nil || resp == nil || resp.Errno != 0 || resp.GetData() == nil || len(resp.GetData().GetFeeInfo()) == 0 {
		return nil
	}

	for _, spsFeeInfo := range resp.GetData().GetFeeInfo() {
		if len(spsFeeInfo.GetFeeList()) == 0 || spsFeeInfo.GetProductCategory() == 0 {
			continue
		}

		feeMap := make(map[int64]*sps.FeeItem)
		for _, feeInfo := range spsFeeInfo.GetFeeList() {
			feeMap[feeInfo.ServiceId] = feeInfo
		}

		spsFeeMap[spsFeeInfo.GetProductCategory()] = feeMap
	}

	return spsFeeMap
}
