package locsvr

import (
	"context"
	"errors"
	"strconv"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"

	Locsvr "git.xiaojukeji.com/dirpc/dirpc-go-thrift-Locsvr"
	LocsvrClient "git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/localSrvClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/conf"
)

const disfName = "disf!engine-gs-locserver"

var ErrFailToGetTokenString = errors.New("empty locsvr token")
var token string
var cli *LocsvrClient.Client

type StartDestInfo struct {
	FromCityId           int32
	FromDistrict         string
	FromCounty           int32
	FromAbstractDistrict string

	ToCityId   int32
	ToCounty   int32
	ToDistrict string
}

func Init() error {
	var err error
	token = conf.Viper.GetString("locsvr.token")
	if token == "" {
		return ErrFailToGetTokenString
	}
	cli, err = LocsvrClient.NewLocsvrClient(disfName)
	if err != nil {
		return err
	}
	return nil
}

func MultiAreaInfoByCoords(ctx context.Context, coordsList []map[string]float64, mapType string) (map[int64]*Locsvr.Cityinfo, error) {
	var (
		err     error
		coords  []*Locsvr.Coordinate
		resp    map[int64]*Locsvr.Cityinfo
		rpcResp *Locsvr.MultiAreaInfoResponse
	)
	for _, coordsMap := range coordsList {
		coords = append(coords, &Locsvr.Coordinate{Lat: coordsMap["lat"], Lng: coordsMap["lng"]})
	}

	req := &Locsvr.MultiCoordRequest{
		LogID:        1,
		Token:        token,
		Coords:       coords,
		ReqCoordType: ConvertMapType(mapType),
		AreaFilter: &Locsvr.AreaFilter{
			AreaType: Locsvr.Area_RET_TYPEPtr(Locsvr.Area_RET_TYPE_AREA_ADMIN_DETAIL),
			LanType:  Locsvr.LANGUAGE_TYPEPtr(Locsvr.LANGUAGE_TYPE_LNG_LOCAL),
		},
		CoordFilter: nil,
	}
	rpcResp, err = cli.MultiAreaInfoByCoord(ctx, req)
	if err != nil || 0 != rpcResp.RetCode {
		return resp, err
	}

	return rpcResp.CityInfos, nil
}

// BuildStartDestInfo 构建一套打平的起终点信息
func BuildStartDestInfo(ctx context.Context, coords []map[string]float64, mapType string) (*StartDestInfo, error) {
	cityInfos, err := MultiAreaInfoByCoords(ctx, coords, mapType)
	if err != nil {
		// 地点获取失败  -> down
		return nil, err
	}

	if len(cityInfos) == 0 {
		log.Trace.Errorf(ctx, "BuildAreaInfo", "empty cityInfos")
		return nil, errors.New("city not found")
	}

	sdInfo := &StartDestInfo{}
	if cityInfos[0] != nil {
		sdInfo.FromCityId = cityInfos[0].Cityid
		if cityInfos[0].DistrictCode != nil {
			sdInfo.FromDistrict = *cityInfos[0].DistrictCode
		}
		sdInfo.FromCounty = cityInfos[0].Countyid
	}
	sdInfo.FromAbstractDistrict = sdInfo.FromDistrict + "," + strconv.Itoa(int(sdInfo.FromCounty))

	if cityInfos[1] != nil {
		sdInfo.ToCityId = cityInfos[1].Cityid
		if cityInfos[1].DistrictCode != nil {
			sdInfo.ToDistrict = *cityInfos[0].DistrictCode
		}
		sdInfo.ToCounty = cityInfos[0].Countyid
	}

	return sdInfo, nil
}

// MapType2Int ...
func ConvertMapType(str string) Locsvr.CoordType {
	var ret Locsvr.CoordType
	if str == "soso" {
		ret = Locsvr.CoordType_SOSOGCJ
	} else if str == "baidu" {
		ret = Locsvr.CoordType_BAIDU
	} else if str == "wgs84" {
		ret = Locsvr.CoordType_WGS84
	} else {
		ret = Locsvr.CoordType_SOSOGCJ
	}
	return ret
}
