package transit_wind

import (
	"context"

	metro "git.xiaojukeji.com/dirpc/dirpc-go-http-TransitWind"
	transitWindClient "git.xiaojukeji.com/gulfstream/bizlib-go/dirpcClient/transitWindClient"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
)

const (
	disfName = "disf!galileo-transit2-wind"
	logTag   = "transit_wind"
)

var (
	client *transitWindClient.Client
)

// InitClient 初始化赔付client
func InitClient() (err error) {
	client, err = transitWindClient.NewTransitWindClient(disfName)
	if err != nil {
		return err
	}
	return nil
}

func TransitExpressRecommend(ctx context.Context, req *metro.TransitExpressRecommendReq) *metro.TransitExpressRecommendResp {
	// 接口文档:http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=911954457
	res, err := client.TransitExpressRecommend(ctx, req)

	if err != nil {
		log.Trace.Warnf(ctx, logTag, "TransitWidget error=[%v]", err)
		return nil
	}

	if res == nil || res.Errno != 0 || res.Result == nil {
		log.Trace.Warnf(ctx, logTag, "TransitWidget res exception:[%s]", util.ToJSONString(res))
		return nil
	}
	return res
}

func TransitInnerSimple(ctx context.Context, req *metro.TransitInnerSimpleReq) *metro.TransitInnerSimpleResp {
	// 接口文档:http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=937842587 TransitInnerSimple
	res, err := client.TransitInnerSimple(ctx, req)
	if err != nil {
		log.Trace.Warnf(ctx, logTag, "TransitInnerSimple error=[%v]", err)
		return nil
	}

	if res == nil || res.Errno != 0 || len(res.Transits) < 1 || len(res.Fid) == 0 {
		log.Trace.Infof(ctx, logTag, "TransitInnerSimple res exception:[%s]", util.ToJSONString(res))
		return nil
	}

	return res
}

func InnerRouteplanQuery(ctx context.Context, req *metro.InnerRouteplanQueryReq) *metro.InnerRouteplanQueryRsp {
	// 接口文档:http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=937842587 TransitInnerSimple
	res, err := client.InnerRouteplanQuery(ctx, req)
	if err != nil {
		log.Trace.Warnf(ctx, logTag, "TransitInnerSimple error=[%v]", err)
		return nil
	}

	if res == nil || res.Errno != 0 || res.Data == nil {
		log.Trace.Infof(ctx, logTag, "TransitInnerSimple res exception:[%s]", util.ToJSONString(res))
		return nil
	}

	return res
}
