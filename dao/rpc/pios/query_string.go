package pios

import (
	"bytes"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
)

type utf8String string

func (s utf8String) MarshalJSON() ([]byte, error) {
	str := strings.Replace(strconv.QuoteToASCII(string(s)), "/", `\/`, -1)
	return []byte(str), nil
}

// GenQueryString generate the querystring same as PHP version
func GenQueryString(params map[string]interface{}) (string, error) {
	queryString := ""
	newParams := DeepCopyMap(params)
	sortedKeys := getSortedStringKeys(DeepCopyMap(newParams))
	for _, key := range sortedKeys {
		size, result, err := encodeFieldToString(newParams[key])
		if err != nil {
			return "", err
		}
		if size == 0 {
			continue
		}

		if str, ok := result.(string); ok {
			queryString = fmt.Sprintf("%s%s=%v&", queryString, key, str)
		} else {
			buf := new(bytes.Buffer)
			encoder := json.NewEncoder(buf)
			encoder.SetEscapeHTML(false)
			err := encoder.Encode(result)
			if err != nil {
				return "", err
			}
			queryString = fmt.Sprintf("%s%s=%s&", queryString, key, strings.Replace(buf.String(), "\n", "", -1))
		}
	}
	return queryString, nil
}

func encodeFieldToString(params interface{}) (int, interface{}, error) {
	switch data := params.(type) {
	case []interface{}:
		for i := range data {
			_, str, err := encodeFieldToString(data[i])
			if err != nil {
				return 0, "", err
			}
			data[i] = str
		}
		return len(data), data, nil

	case map[string]interface{}:
		for key := range data {
			_, str, err := encodeFieldToString(data[key])
			if err != nil {
				return 0, "", err
			}
			data[key] = str
		}
		return len(data), data, nil

	case float64:
		return 1, int64(data), nil

	case int, int64:
		return 1, data, nil

	case nil:
		return 1, nil, nil

	default:
		str := fmt.Sprintf("%v", params)
		if str == "0" {
			return 0, str, nil
		}
		return len(str), string(str), nil
	}
}

func getSortedStringKeys(data map[string]interface{}) []string {
	var keys []string
	for key := range data {
		keys = append(keys, key)
	}
	sort.Slice(keys, func(i, j int) bool { return keys[i] < keys[j] })
	return keys
}

// DeepCopyMap deeply copy a map to a new one
func DeepCopyMap(m map[string]interface{}) map[string]interface{} {
	result := make(map[string]interface{})
	for k, v := range m {
		switch data := v.(type) {
		case map[string]interface{}:
			result[k] = DeepCopyMap(data)

		case []interface{}:
			result[k] = DeepCopySlice(data)

		default:
			result[k] = v
		}
	}
	return result
}

// DeepCopySlice deeply copy a slice to a new one
func DeepCopySlice(s []interface{}) []interface{} {
	result := make([]interface{}, len(s))
	for i, v := range s {
		switch data := v.(type) {
		case map[string]interface{}:
			result[i] = DeepCopyMap(data)

		case []interface{}:
			result[i] = DeepCopySlice(data)

		default:
			result[i] = v
		}
	}
	return result
}

func getSign(params interface{}) (string, error) {
	jsStr, err := json.Marshal(params)
	if err != nil {
		return "", err
	}

	paramsMap := make(map[string]interface{})
	err = json.Unmarshal(jsStr, &paramsMap)
	if err != nil {
		return "", err
	}

	return EncryptMapWithoutSign(paramsMap, EncryptKey)
}
