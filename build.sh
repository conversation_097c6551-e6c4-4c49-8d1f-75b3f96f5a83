#!/bin/bash

set -ex

# 项目名称，根据具体项目改动
MODULE_NAME=mamba
VERSION=1.22.0

# env
export GOPATH
export GOROOT=/usr/local/go$VERSION
export PATH=${GOROOT}/bin:$GOPATH/bin:${PATH}:$GOBIN
export GOPROXY=http://goproxy.intra.xiaojukeji.com, direct
export GOSUMDB=off

if [ ! -d $GOROOT  ];then
  echo "EEROR !!! GO VERSION should more than 1.13.5, default is 1.13.5, please modify the VERSION in build.sh for a suitable go version"
  exit 1
fi

function build() {

    # build
    echo "Building……" && make

    if [[ $? != 0 ]];then
        echo -e "Build failed !"
        exit 1
    fi
    echo -e "Build success!"
}

function install_goc(){
    wget -q https://s3-gzpu-inter.didistatic.com/kflower-qa/goc/goc && chmod 744 goc && mv goc /usr/local/bin/
    if [ $? != 0 ]; then
        COV=''
        echo "install goc failed"
    else
        echo "install goc success"
    fi
}

function install_nuwa_goc() {
    curl -s https://git.xiaojukeji.com/nuwa/tools/goc/raw/master/goc.linux >> nuwa_goc && chmod 744 nuwa_goc && mv nuwa_goc /usr/local/bin/
    curl -s https://git.xiaojukeji.com/nuwa/nuwa-online-coverage/raw/master/bin/goc_ticker >> goc_ticker && chmod 744 goc_ticker && mv goc_ticker /usr/local/bin/
}

if [[ "${COV}" == "yes" ]]; then
    # $COV is OE pipeline's global variable
    install_goc
fi
install_nuwa_goc
build
