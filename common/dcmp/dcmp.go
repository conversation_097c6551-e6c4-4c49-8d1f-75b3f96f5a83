package dcmp

import (
	"context"
	"regexp"
	"strings"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"

	"github.com/bitly/go-simplejson"
	"github.com/tidwall/gjson"

	"git.xiaojukeji.com/gulfstream/mamba/common/reqctx"

	dcmp "git.xiaojukeji.com/dirpc/dirpc-go-dcmp/v2"
)

const DefaultLang = "zh-CN"

// EmptyContent ...
const EmptyContent = ""

const (
	KeyHomePageCallCarNewDapan  = "home_page-call_car_msg_new_dapan"
	KeyHomePageCallCarNewXinliu = "home_page-call_car_msg_new_xinliu"
)

func GetDcmpContent(ctx context.Context, key string, tags map[string]string) string {
	lang := reqctx.GetLang(ctx)
	if lang == "" {
		lang = DefaultLang
	}
	return dcmp.GetContent(ctx, key, lang, tags)
}

func GetDcmpPlainContent(ctx context.Context, key string) string {
	return GetDcmpContent(ctx, key, nil)
}

// GetTextObject
// Deprecated: simplejson不建议使用
func GetTextObject(ctx context.Context, key string, tags map[string]string) (*simplejson.Json, error) {
	plainText := GetDcmpContent(ctx, key, tags)
	if "" == plainText {
		return nil, consts.GetErr(consts.ErrDCMPEmpty)
	}
	_json, err := simplejson.NewJson([]byte(plainText))
	if err != nil {
		return nil, err
	}
	return _json, nil
}

// GetSubContent
// Deprecated: simplejson不建议使用, 使用GetJSONContentWithPath
func GetSubContent(ctx context.Context, key string, subKey string) string {
	_json, err := GetTextObject(ctx, key, nil)
	if err != nil {
		return ""
	}

	str, err := _json.Get(subKey).String()
	if err != nil {
		return ""
	}

	return str
}

func TranslateTemplate(template string, tags map[string]string) string {
	if template == EmptyContent || tags == nil || len(tags) == 0 {
		return template
	}
	regExp := regexp.MustCompile("{{[^{]+?}}")
	return regExp.ReplaceAllStringFunc(template, func(src string) string {
		name := strings.TrimSuffix(strings.TrimLeft(src, "{{"), "}}")
		if tag, ok := tags[name]; ok {
			return tag
		}
		return src
	})
}

// GetJSONContentWithPath ...
// 这里没有对key做缓存
// 从dcmp的内部实现看, 每次都有unmarshal
// 暂时(2022-01-14)的解决方法是: 在获取的地方自己存
func GetJSONContentWithPath(ctx context.Context, key string, tags map[string]string, path string) string {
	lang := getLangFromCtx(ctx)
	raw := dcmp.GetContent(ctx, key, lang, tags)

	return gjson.Get(raw, path).String()
}

func GetJSONResult(ctx context.Context, key string, tags map[string]string) map[string]gjson.Result {
	lang := getLangFromCtx(ctx)
	raw := dcmp.GetContent(ctx, key, lang, tags)

	return gjson.Parse(raw).Map()
}

func GetJSONMap(ctx context.Context, key string, path string) map[string]gjson.Result {
	lang := getLangFromCtx(ctx)
	raw := dcmp.GetContent(ctx, key, lang, nil)
	return gjson.Get(raw, path).Map()
}

func GetJSONResultWithPath(ctx context.Context, key string, tags map[string]string, path string) gjson.Result {
	lang := getLangFromCtx(ctx)
	raw := dcmp.GetContent(ctx, key, lang, nil)
	return gjson.Get(raw, path)
}

func getLangFromCtx(ctx context.Context) string {
	lang := reqctx.GetLang(ctx)
	if lang == "" {
		lang = DefaultLang
	}
	return lang
}

func GetStringDcmpContentByPath(infoMap map[string]gjson.Result, path string) *string {
	var res string

	if infoMap == nil || len(infoMap) <= 0 {
		return &res
	}

	if tmp, ok := infoMap[path]; ok {
		res = tmp.Value().(string)
	}

	return &res
}

func GetNumContentByPath(infoMap map[string]gjson.Result, path string) float64 {
	var res float64

	if infoMap == nil || len(infoMap) <= 0 {
		return res
	}

	if tmp, ok := infoMap[path]; ok {
		if vv, ok := tmp.Value().(float64); ok {
			res = vv
		}
	}

	return res
}
