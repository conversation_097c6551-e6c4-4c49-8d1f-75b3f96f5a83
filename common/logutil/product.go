package logutil

import (
	"context"
	PriceApi "git.xiaojukeji.com/dirpc/dirpc-go-PriceApi"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
	"git.xiaojukeji.com/gulfstream/mamba/models/biz_runtime"
	"git.xiaojukeji.com/gulfstream/mamba/view/render/price_info_desc_list"
	LegoContext "git.xiaojukeji.com/lego/context-go"
	jsoniter "github.com/json-iterator/go"
)

func getProductsEID(pcids []string, productMap map[string]*biz_runtime.ProductInfoFull) []string {
	var res []string
	for _, pcid := range pcids {
		if productMap[pcid] != nil {
			res = append(res, productMap[pcid].GetEstimateID())
		}
	}
	return res
}

func getCouponInfoStr(channel int64, couponInfo *PriceApi.EstimateNewFormCouponInfo) string {
	type CouponItem struct {
		Channel int64  `json:"channel"`
		BatchID string `json:"batchid"`
		Amount  string `json:"amount"`
	}

	type CouponInfoRes struct {
		DefaultCoupon  *CouponItem `json:"default_coupon"`
		ActivityCoupon *CouponItem `json:"activity_coupon"`
	}

	if couponInfo == nil {
		return ""
	}

	item := &CouponItem{
		Channel: channel,
		BatchID: couponInfo.BatchId,
		Amount:  couponInfo.Amount,
	}
	res := &CouponInfoRes{}

	if couponInfo.CouponSource == "coupon" {
		res.DefaultCoupon = item
	} else {
		res.ActivityCoupon = item
	}

	resStr, err := jsoniter.Marshal(res)
	if err == nil {
		return string(resStr)
	} else {
		return ""
	}
}

func buildFeeDetailInfo(feeDetailInfo map[string]float64) string {
	type FeeDetailInfoRes struct {
		Key    string `json:"key"`
		Amount int64  `json:"amount"`
	}

	var (
		feeDetailList = make([]FeeDetailInfoRes, 0)
		feeDetailRes  FeeDetailInfoRes
	)

	if feeDetailInfo == nil || len(feeDetailInfo) == 0 {
		return ""
	}

	for feeName, feeAmount := range feeDetailInfo {
		feeDetailRes.Key = feeName
		feeDetailRes.Amount = int64(feeAmount * 100)
		feeDetailList = append(feeDetailList, feeDetailRes)
	}

	resStr, err := jsoniter.Marshal(feeDetailList)
	if err == nil {
		return string(resStr)
	} else {
		return ""
	}
}

func GenerateCommonProductMap(ctx context.Context, full *biz_runtime.ProductInfoFull) (logInfo map[string]interface{}) {
	logInfo = make(map[string]interface{})
	if full == nil || full.BaseReqData == nil || full.Product == nil ||
		full.GetBillInfo() == nil {
		return
	}

	reqData, product, bill, payInfo := full.BaseReqData, full.Product, full.GetBillInfo(), full.GetPaymentInfo()

	orderNTuple, _ := jsoniter.Marshal(product)

	logInfo["xpsid"] = reqData.CommonInfo.Xpsid
	logInfo["xpsid_root"] = reqData.CommonInfo.XpsidRoot

	// ..... 基础信息 .....
	logInfo["estimate_trace_id"] = LegoContext.GetTrace(ctx).GetTraceId()
	logInfo["estimate_id"] = product.EstimateID
	logInfo["client_type"] = reqData.CommonInfo.ClientType
	logInfo["access_key_id"] = reqData.CommonInfo.AccessKeyID
	logInfo["appversion"] = reqData.CommonInfo.AppVersion
	logInfo["pLang"] = reqData.CommonInfo.Lang
	logInfo["channel"] = reqData.CommonInfo.Channel
	logInfo["menu_id"] = reqData.CommonInfo.MenuID
	logInfo["page_type"] = reqData.CommonInfo.PageType
	logInfo["agent_type"] = "both_call_anycar"
	logInfo["origin_page_type"] = reqData.CommonInfo.PageType
	logInfo["source_id"] = reqData.CommonInfo.SourceID

	logInfo["product_category"] = product.ProductCategory
	logInfo["product_id"] = product.ProductID
	logInfo["require_level"] = product.RequireLevel
	logInfo["airport_type"] = product.AirportType
	logInfo["is_special_price"] = product.IsSpecialPrice
	logInfo["carpool_type"] = product.CarpoolType
	logInfo["order_n_tuple"] = string(orderNTuple)
	logInfo["order_type"] = product.OrderType
	logInfo["scene_type"] = product.SceneType
	logInfo["combo_type"] = product.ComboType

	logInfo["from_name"] = util.StringEscape(reqData.AreaInfo.FromName)
	logInfo["to_name"] = util.StringEscape(reqData.AreaInfo.ToName)
	logInfo["area"] = reqData.AreaInfo.Area
	logInfo["district"] = reqData.AreaInfo.District
	logInfo["county"] = reqData.AreaInfo.FromCounty
	logInfo["to_county"] = reqData.AreaInfo.ToCounty
	logInfo["to_area"] = reqData.AreaInfo.ToArea
	logInfo["flng"] = reqData.AreaInfo.FromLng
	logInfo["flat"] = reqData.AreaInfo.FromLat
	logInfo["tlng"] = reqData.AreaInfo.ToLng
	logInfo["tlat"] = reqData.AreaInfo.ToLat
	logInfo["current_lng"] = reqData.AreaInfo.CurLng
	logInfo["current_lat"] = reqData.AreaInfo.CurLat
	logInfo["stopover_points"] = util.JustJsonEncode(reqData.AreaInfo.StopoverPointInfo)

	logInfo["biz_user_type"] = reqData.PassengerInfo.UserType
	logInfo["phone"] = reqData.PassengerInfo.Phone
	logInfo["pid"] = reqData.PassengerInfo.PID

	logInfo["call_car_type"] = reqData.CommonInfo.CallCarType
	if payInfo != nil {
		logInfo["default_pay_type"] = payInfo.DefaultPayType
	}
	if product.BizInfo != nil {
		logInfo["carpool_seat_num"] = product.BizInfo.CarpoolSeatNum
		if product.BizInfo.MiniBusPreMatch != nil && product.BizInfo.MiniBusPreMatch.EtpInfo != nil {
			logInfo["etp"] = product.BizInfo.MiniBusPreMatch.EtpInfo.EtpTimeDuration
		}
		logInfo["member_level_id"] = product.BizInfo.UserMemberProfile.LevelID
	}

	logInfo["tab_id"] = reqData.CommonInfo.TabId
	logInfo["choose_f_searchid"] = reqData.AreaInfo.ChooseFSearchid
	logInfo["choose_t_searchid"] = reqData.AreaInfo.ChooseTSearchid
	logInfo["coupon"] = getCouponInfoStr(reqData.CommonInfo.Channel, full.GetCouponInfo())
	if reqData.CommonBizInfo.PersonalizedCustomOptions != nil {
		if customService, err := jsoniter.Marshal(reqData.CommonBizInfo.PersonalizedCustomOptions); err == nil {
			logInfo["custom_service"] = string(customService)
		}
	}
	// ..... 价格信息 .....
	//logInfo["is_carpool_open"] = bill.IsCarpoolOpen
	//logInfo["is_dynamic"] = bill.IsHasDynamic
	logInfo["driver_metre"] = bill.DriverMetre
	logInfo["time_cost"] = bill.DriverMinute
	logInfo["estimate_fee"] = full.GetEstimateFee()
	logInfo["pre_total_fee"] = bill.PreTotalFee
	//logInfo["discount_info"] = util.JustJsonEncode(discount)
	logInfo["dynamic_diff_price"] = bill.DynamicDiffPrice
	logInfo["dynamic_total_fee"] = bill.DynamicTotalFee
	logInfo["total_fee"] = bill.TotalFee
	logInfo["cap_price"] = bill.CapPrice
	logInfo["dynamic_times"] = bill.DynamicTimes
	// 开平新动调
	logInfo["dups_dynamic_raise"] = bill.DupsDynamicRaise
	logInfo["dups_dynamic_times"] = bill.DupsDynamicTimes

	if bill.DynamicInfo != nil {
		logInfo["dynamic_price_id"] = bill.DynamicInfo.DynamicPriceId
		//logInfo["dynamic_price"] = bill.DynamicInfo.DynamicPriceId
	}
	logInfo["count_price_type"] = bill.CountPriceType
	logInfo["discount_fee"] = full.GetEstimateFee()
	logInfo["designated_driver"] = ""          // 这个参数追加车型应该没有 预估参数里的
	logInfo["show_pickup_service"] = ""        // 追加车型预估应该没有请求3se，展示可选服务
	logInfo["user_status"] = ""                // 不走Athena，没有值
	logInfo["sp_capacity"] = ""                // 不走Athena
	logInfo["is_support_multi_selection"] = "" // 6.0主预估才有的参数
	logInfo["recommend_type"] = ""             // 不走Athena
	logInfo["preferred_route_id"] = ""         // 预估参数拿不到
	logInfo["sub_group_id"] = ""               // 预估参数拿不到
	logInfo["route_group"] = ""                // 追加车型没有城际
	logInfo["route_name"] = ""                 // 追加车型没有城际
	logInfo["combo_id"] = ""                   // 追加车型没有城际
	logInfo["inter_mode"] = ""                 // 追加车型没有城际
	logInfo["is_open_fence"] = ""              // 追加车型没有城际

	fee := price_info_desc_list.GetValueFromDisplayLines(full, price_info_desc_list.RedPacketFee)
	logInfo["red_packet"] = fee // 春节服务费
	logInfo["fee_detail_info"] = buildFeeDetailInfo(bill.FeeDetailInfo)

	// TODO 需要Price加字段的
	logInfo["dynamic_kind"] = ""                         // dynamic_info.dynamic_kind 暂时先不加
	logInfo["wait_minute"] = ""                          // dynamic_info.wait_minute
	logInfo["wait_discount"] = ""                        // bill.wait_discount
	logInfo["station_id"] = ""                           // bill_info.carpool_station_info.uid
	logInfo["is_hit_member_capping"] = ""                // bill_info.is_hit_member_capping
	logInfo["dynamic_price_without_member_capping"] = "" // bill_info.dynamic_price_without_member_capping
	logInfo["fence_ids"] = ""                            // 需要存dds/products的response里的fence_info
	logInfo["bill_combo_type"] = ""                      // 账单那边的comboType

	if estimateFee, ok := full.GetCarpoolFailEstimateFee(); product.IsDualCarpoolPrice && ok {
		logInfo["carpool_fail_discount_fee"] = estimateFee
		if full.GetCarpoolFailedBill() != nil {
			logInfo["carpool_fail_price"] = full.GetCarpoolFailedBill().DynamicTotalFee
		}
	}
	logInfo["from_type"] = full.BaseReqData.CommonInfo.FromType
	logInfo["railway_type"] = full.Product.RailwayType
	logInfo["guide_trace_id"] = full.BaseReqData.CommonInfo.GuideTraceId
	logInfo["form_style_exp"] = full.BaseReqData.CommonBizInfo.FormStyleExp
	logInfo["rec_form"] = full.BaseReqData.CommonBizInfo.RecForm
	return
}

func GetDualCarpoolDiscount(ctx context.Context, product *biz_runtime.ProductInfoFull) *PriceApi.EstimateNewFormExtend {
	extendList := product.GetExtendList()
	if extendList == nil {
		return nil
	}

	for _, extend := range extendList {
		if extend == nil || extend.SceneMark == nil {
			continue
		}

		if _, ok := extend.SceneMark["is_carpool_success"]; !ok {
			return extend
		}
	}

	return nil
}
