package consts

import "fmt"

const NormalFontSize = 0
const (
	// PressureHintCode is hintcode of Pressure
	PressureHintCode int64 = 1

	// AgentType
	AgentTypeBothCallAnyCar = "both_call_anycar"
	AgentTypeKeqiScan       = "keqi_scan"

	// 仓库名称
	Mamba = "mamba"

	// Dcmp 分支名称
	DCMPBranch = "master"
)

const (
	LangZhCN = "zh-CN"
	LangEnUS = "en-US"
)

// CarPoolType
const (
	CarPoolTypeNone             = 0  // 非拼车
	CarPoolTypeNormal           = 1  // 普通拼车
	CarPoolTypeStation          = 2  // 站点拼车
	CarPoolTypeInterCity        = 3  // 跨城拼车(特价)
	CarPoolTypeLowPrice         = 4  // 拼车车
	CarPoolTypeFLatRate         = 5  // 司乘一口价
	CarPoolTypeInterCityNew     = 6  // 城际拼车-新模式(特快)
	CarPoolTypeSFCar            = 7  // 顺风车拼车
	CarPoolTypeInterCityStation = 8  // 城际拼车-大车站点班车模式
	CarPoolTypeSFCarCrossCity   = 9  // 顺风车拼车-跨城模式
	CarPoolTypeMiniBus          = 10 // 小巴
	CarPoolTypeSmartBus         = 12
)

// ComboType
const (
	TypeComboDafault              = 0       // 普通
	TypeComboRented               = 1       // 时租
	TypeComboFromAirport          = 2       // 接机
	TypeComboToAirport            = 3       // 送机
	TypeComboCarpool              = 4       // 拼车
	TypeComboTestDrive            = 5       // 试乘试驾
	TypeComboOpenAPICapPriceStart = 6       // 开放平台一口价开始
	TypeComboOpenAPICapPriceEND   = 300     // 开放平台一口价结束
	TypeComboCarpoolInterCity     = 302     // 跨区域拼车
	TypeComboFlatRate             = 303     // 区域一口价
	TypeComboRegion               = 306     // 顺路计价
	TypeComboShenGangFlatRate     = 308     // 深港一口价
	TypeComboMultiFactorFlatRate  = 309     // 快车拼车（多因素）一口价
	TypeComboInsuranceSurvey      = 310     // 保险查勘
	TypeComboFalCon               = 311     // 猎鹰订单
	TypeComboCarpoolFlatRate      = 312     // 拼车区域一口价
	TypeComboSpecialRate          = 314     // 线路一口价
	TypeComboCarpoolKFlower       = 404     // 拼车（霸王花）
	TypeComboXActivityNew         = 317     // ？？？
	TypeComboXActivityStart       = 1000000 // 一键叫起始值
)

// productID 业务线
const (
	ProductIDDefault               = 1   // 专车订单
	ProductIDBusiness              = 2   // 企业订单
	ProductIDFastCar               = 3   // 快车订单
	ProductIDBusinessFastCar       = 4   // 企业快车订单
	ProductIDTestDriver            = 5   // 试乘试驾订单
	ProductIDUBerDefault           = 6   // UBER专车订单
	ProductIDUBerFastCar           = 7   // UBER快车订单
	ProductIDYCAR                  = 8   // 长平订单
	ProductIDFirstClassCar         = 9   // 头等舱订单
	ProductIDDACHE                 = 10  //  出租车 http:// wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=87584818
	ProductIDUNITAXI               = 11  // 出租车.unione平台
	ProductIDHKTaixCar             = 12  // 香港出租车
	ProductIDHKFastCar             = 13  // 香港快车
	ProductIDBRAFastCar            = 16  // 巴西快车
	ProductIDREGULARTaixCar        = 17  // 巴西regular
	ProductIDTOPTaixCar            = 18  // 巴西TOP
	ProductIDTWTaixCar             = 19  // 台湾计程车
	ProductIDUBerYOUXIANGCar       = 20  // uber优享
	ProductIDBusinessTaixCar       = 21  // 企业出租车
	ProductIDBusinessFirstCLASSCar = 22  // 企业豪华车
	ProductIDBRABusinessFastCar    = 23  // 巴西企业快车
	ProductIDBRABusinessTaixCar    = 24  // 巴西企业出租车
	ProductIDTOPBusinessTaixCar    = 25  // 巴西企业高级出租车
	ProductIDANYCar                = 26  // anycar
	ProductIDMEXICOFastCar         = 30  // 墨西哥快车
	ProductIDJAPANTaixCar          = 31  // 日本出租车
	ProductIDAUFastCar             = 32  // 澳大利亚快车
	ProductIDDIONE                 = 700 // 滴滴定制车
	ProductIDSFC                   = 259 //顺风车订单
)

// businessID
const (
	BusinessIDDIDIMini       = 440 // 惠民小滴（小神车）
	BusinessIDSpecialPremium = 441 // 汉江专车

)

// productKey
var (
	ProductKeyCarpool         = fmt.Sprintf("%d_%s_%d", ProductIDFastCar, DidiPUTONGCarLevel, CarpoolOrder)         // 拼车两口价
	ProductKeyBusinessCarpool = fmt.Sprintf("%d_%s_%d", ProductIDBusinessFastCar, DidiPUTONGCarLevel, CarpoolOrder) // 企业拼车两口价
)

// anycar类型
const (
	TypeAnycarFirstClass = "first_class" // 豪华车anycar
	TypeAnycarFast       = "fast"        // 快车anycar
)

// 订单类型
const (
	NormalOrder          = 0 // 普通订单
	ComboOrder           = 1 // 时租订单
	AirportArrivedOrder  = 2 // 接机
	AirportDepartedOrder = 3 // 送机
	CarpoolOrder         = 4 // 拼车
	SCSJOrder            = 5 // 试乘试驾
)

// carLevel
const (
	YiHaoFREEDOMCarLevel          = "10100" // 一号freedom车型
	YiHaoGAODUANCarLevel          = "10200" // 一号高端车型
	YiHaoSHANGWUCarLevel          = "10300" //  一号7座商务
	YiHaoHAOHUACarLevel           = "10400" // 一号豪华型
	DidiACECarLevel               = "700"   // ACE levelid
	DidiHAOHUACarLevel            = "200"   // 豪华型levelid
	DidiSHANGWUCarLevel           = "400"   // 商务型levelid
	DidiSHUSHICarLevel            = "100"   // 舒适型levelid
	DidiYOUXUANCarLevel           = "500"   // 优选型levelid
	DidiPUTONGCarLevel            = "600"   // 快车levelid
	DidiXIAOBACarLevel            = "610"   // 快车小巴
	DidiKEYUNCarLevel             = "620"   // 快车客运
	DidiXIAODICarLevel            = "800"   // 快车小滴
	DidiYOUXIANGCarLevel          = "900"   // A+快车
	DidiXINGZHENGJICarLevel       = "1000"  // 豪华车
	DidiUNITaixPUTONGCarLevel     = "1100"  // unione出租车-普通型
	DidiUNITaixYOUXUANCarLevel    = "2000"  // unione出租车-优选型
	DidiHKStandardCarLevel        = "1200"  // 香港快车-标准型
	DidiBRAStandardCarLevel       = "1300"  // 巴西快车-标准型
	DidiBAOMACarLevel             = "1400"  // 豪华车-宝马
	DidiAUDIA6CarLevel            = "1401"  // 豪华车-奥迪a6
	DidiHAOHUANEWCarLevel         = "1402"  // 在专车顶导下预估时曝光,属于豪华车业务线
	DidiBENCHICarLevel            = "1500"  // 豪车车-奔驰
	DidiINFINITICarLevel          = "1600"  // 豪华车-英菲尼迪
	DidiBRATopTaixLEVEL           = "1700"  // 巴西出租车-TOP型
	DidiBRARegularTaixLEVEL       = "1800"  // 巴西出租车-REGULAR型
	DidiBRADMTaixLEVEL            = "1900"  // 巴西出租车-DM折扣型
	DidiTWTaixCarLevel            = "2100"  // 台湾出租车
	DidiHKTaixCarLevel            = "2200"  // 香港出租车
	DidiAnycarCarLevel            = "2300"  // anycar
	DidiMEXICOFastCarLevel        = "2400"  // 墨西哥快车
	DidiJAPANSmallTaixLEVEL       = "2700"  // 日本小型出租车
	DidiJAPANMiddleTaixLEVEL      = "2800"  // 日本中型出租车
	DidiJAPANLargeTaixLEVEL       = "2900"  // 日本大型出租车
	DidiJAPANTopTaixLEVEL         = "3000"  // 日本豪华型出租车
	DidiAUFastCarLevel            = "3100"  // 澳大利亚快车
	DidiLITECarLevel              = "3300"  // 专车休闲舒适型
	DidiCHARTEREDCompactCarLevel  = "3400"  // 包车紧凑型
	DidiCHARTEREDSoaciousCarLevel = "3500"  // 包车宽敞型
	DidiAutoDrivingCarLevel       = "3600"  // 无人车-标准型
	TONGCHENGFastCarLevel         = "4500"  // 同程快车
	YIQIFastCarLevel              = "4520"  // 一汽快车
	DONGFENGFastCarLevel          = "4530"  // 东风快车
	GUANGQIFastCarLevel           = "4540"  // 广汽
	YANGGUANGFastCarLevel         = "4550"  // 阳光
	AIYOUWEIFastCarLevel          = "4560"  // 哎呦喂
	TONGGANGFastCarLevel          = "4580"  // 同港服务
	AAFastCarLevel                = "4570"  // AA
	ShortDistanceFastCarLevel     = "4700"  // 短途特惠
	OULEFastCarLevel              = "4710"  // 欧了
	KFlowerCarLevel               = "4800"  // 霸王花
	XIANGDAOFastCarLevel          = "4590"  // 上汽
	SHOUQIFastCarLevel            = "4730"  // 首汽畅享
	SHOUQIComfortCarLevel         = "4731"  // 首汽舒适
	SHOUQIBusinessCarLevel        = "4732"  // 首汽商务
	DidiDIONECarLevel             = "10500" // D1
	DidiCRUISECarLevel            = "11000" // 巡游网约车
	DidiNOREGISTERDRIVERLevel     = "999"   // 未注册滴滴司机,只是普通车主用户
	DidiNOREGISTERDRIVERLevelHK   = "998"   // 香港未注册完成司机
	DidiNOREGISTERDRIVERLevelTW   = "997"   // 香港未注册完成司机
	KFlowerNOREGISTERDRIVERLevel  = "996"   // 霸王花未注册完成司机
	SFCarCarLevel                 = "30001" // 顺风车
)

// level_type
const (
	LevelTypeOneCarTwoPrice = 5 // 一车两价: 普通出租车（一车两价）/优选出租车（一车两价）
)

// 计价类型 count_price_type
const (
	CountPriceTypeMultiFactorCapPrice = 11
	CountPriceTypeAPlus               = 105
	CountPriceTypeDOne                = 108
	CountPriceTypePickOnTime          = 109
)

// pricing_type
const (
	PricingTypeCarpoolDay = 5
)

// city_id
const (
	HKCityId = 357
)

const (
	MenuIDDaCheAnyCar = "dache_anycar"
)

const (
	BusinessPaymentType    = 21 // 企业余额支付
	BusinessPayByTeam      = 25 // 企业团队付
	BusinnessPayWithRefund = 23 // 个人支付(需报销)
)

// 出租车峰期加价
const (
	ServiceIDUniTaxi = 107
	CanSelect        = "1"
	HasFee           = 1
)

const (
	HXZProductID  = "77"
	HXZBusinessID = "427"
)

const (
	UnChecked = 0
	Checked   = 1 // 勾选
)

const (
	Enable  = 1
	Disable = 0 // 勾选
)

const (
	RealTimeOrder = 0 // 实时单
	BookOrder     = 1 // 预约单
)

// 优惠类型
const (
	CouponType = "coupon" // 券
)

// 券类型
const (
	DeductionCouponType = "3"   // 立减券
	DiscountCouponType  = "100" // 折扣券
)

// 支付方式
const (
	PayTypeByPerson = 2
	PayTypeByFriend = 100
	PayTypeByCash   = 1024
)

const (
	DateFormat         = "2006-01-02"
	OpenTimeFormat     = "2006-01-02 15:04"
	DateTimeLiteLength = 10 // 仅有年月日的日期格式字符串长度小于10
)

// 基础常量信息
const (
	TwoInt = 2
	TenInt = 10
)

// 接口请求来源标识
const (
	FromTypeCombinedTravel = "pCombinedTravelEstimate"
)

// 组合出行内部请求标识
const (
	CombinedTravelBizTypeGulfstream = "gs"    // 平乘组合出行
	CombinedTravelBizTypeUT80       = "ut_80" // 平乘客
)

// 一键叫业务人群
const (
	CallCarBizTagNewLossHomePage = "new_loss_homepage" // 新流首页
	CallCarBizTagXinliu          = "xinliu"            // 原来的新流一键叫
	CallCarBizTagDefault         = "default"           // 和其他人群取非集
	CallCarBizTagErr             = "tag_err"           // 因一些情况无法判断用户属于哪个人群（如未登录）
)

// 一键叫终点类型
const (
	CallCarDestTypeGuess      = "guess_dest"
	CallCarDestTypeLastBubble = "last_dest"
)

const (
	// HomepageCallCarUserTypeNormal 普通用户
	HomepageCallCarUserTypeNormal = 1
	// HomepageCallCarUserTypeCompany 企业用户
	HomepageCallCarUserTypeCompany = 2
)

const (
	// CHECK 勾选
	CHECK = 1
	// UNCHECK 不勾选
	UNCHECK = 0
)

const (
	SourceIdIntercityStation = 1
)

// 一键叫1.2实验相关
const (
	ExpNameXinLiuCallCarV12        = "xinliu_yijianjiaoche_v12"   // 实验名称
	ExpXinLiuCallCarHideCard       = "category_hide"              // 无组件
	ExpXinLiuCallCarShowCarType    = "category_show_car_type"     // 有车型展示
	ExpXinLiuCallCarNotShowCarType = "category_not_show_car_type" // 无车型展示
	ExpXinLiuCallCarCurrentLogic   = "category_current_logic"     // 维持当前逻辑
)

// 预估定价类型
const (
	FeeTypeDefault     = 0   // 预估
	FeeTypeCapPrice    = 1   // 一口价
	FeeTypeBusinessPay = 2   // 企业付
	FeeTypeTable       = 3   // 打表计价
	FeeTypeTwoPrice    = 4   // 两口价
	FeeTypeRange       = 5   // 特快range
	FeeTypeUpgrade     = 6   // 升舱
	FeeTypeError       = 100 // 不展示情景
)

// 大巴检票端代发单场景类型
const (
	SceneTypeDefault         = 0 // 默认
	SceneTypeChangeSite      = 1 // 切换站点
	SceneTypeChangeShiftId   = 2 // 切换班次
	SceneTypeChangePassenger = 3 // 乘车人变动
	SceneTypeCharter         = 4 // 包车
)

const (
	ServiceIdFemaleDriver = 114
	SceneIDFemaleDriver   = 34
)

const (
	DriverPickUpService = 112
)

// 大巴后台发单channel
const (
	WebTicketCollector = 40082 // 大巴后台检票代发单
	WebPhoneCallOrder  = 40083 // 大巴后台客服代发单
)

const (
	PrivilegeFastUpgradePremier   = 6  // 快车升舱专车
	PrivilegePremierUpgradeLuxury = 7  // 专车升舱豪华车
	WaitUpgrade                   = 33 // 长时间未应答升舱
)

const (
	IosAccessKeyId     = 1
	AndroidAccessKeyId = 2
	WeChatAccessKeyId  = 9
	AlipayAccessKeyId  = 22
	HarmonyAccessKeyId = 60
)

const (
	FastRangeFeeInfo    = "fast_range_recommend_fee"
	BorderPriceLower    = "border_left_price"     // 惠选车推荐价左边界
	BorderPriceUpper    = "border_right_price"    // 惠选车推荐价右边界
	RecommendPriceLower = "recommend_left_price"  // 惠选车推荐价左边界
	RecommendPriceUpper = "recommend_right_price" // 惠选车推荐价右边界
	MinPrice            = "min_price"             // 惠选车发单价左边界
	MaxPrice            = "max_price"             // 惠选车发单价右边界
)

const (
	// CarpoolLargeFont 两口价字体放大
	CarpoolLargeFont = 1
	// CarpoolSmallFont 两口价字体缩小
	CarpoolSmallFont = 0
	DualPriceSort    = "dual_price_sort"
	ASC              = "ASC"
)

const (
	ShuttleBusCarName   = "景区接驳车 %d人乘车"
	ShuttleBusPriceDesc = "预估{{num}}元"
)

const (
	CallCarOrder    = 1
	NotCallCarOrder = 0
)

const (
	Normal      = 1
	Cache       = 2
	TripCloud   = 3
	Favorable   = 4 //惠选车
	Intercity   = 5 //城际拼车
	CityCarpool = 6 //市内拼车
)

const (
	MyLocationFromName  = "我的位置"
	MyLocationFromPoiId = "my_location"
)

const EmptyObjectJson = "{}"

const (
	ControlGroup   = "control_group"   // 默认组
	TreatmentGroup = "treatment_group" // 对照组
)

const MambaCaller = "pre-sale-core"

const (
	MarkComma = ","
)
