package consts

import (
	"errors"
)

const (
	NoErr = 0

	FilterSendProduct = "FilterSendProduct" // 过滤已发单品类
	FilterThirdParty  = "FilterThirdParty"  // 过滤三方未授权等

)

var (
	ErrorInvalidParams        = errors.New("params error")
	ErrorFailToParseLocation  = errors.New("get area info error")
	ErrorFailToGetUserInfo    = errors.New("get passenger info error")
	ErrorGetFromDDSFail       = errors.New("fail to get products from dds")
	ErrorNoProductCanEstimate = errors.New("no product can estimate")
	ErrorDDSTimeSliceNotMatch = errors.New("dds time slice not match")
	ErrorBaseReqNotBuild      = errors.New("build base req before generating products")
	ErrorEmptyPriceReq        = errors.New("no valid price req")
	ErrorGetFromPriceApiFail  = errors.New("fail to get price info from price-api")
	ErrorGetDosOrderFail      = errors.New("fail to get order info from dos")
	ErrorGetUfsInfoFail       = errors.New("fail to get info from ufs")
	ErrorGetRoutePriceFail    = errors.New("fail to get route price from tc")
	ErrorGetRedisNilValue     = errors.New("get redis nil value")
	ErrorGetRedisError        = errors.New("get redis error")
	ErrorSelectedBusCard      = errors.New("select bus card batch id error")
)

// 公共错误码
const (
	ErrnoPanic = iota + 2000
	ErrnoParams
	ErrnoSystemError
	ErrnoEncode
	ErrnoDecode
	ErrnoNoProductOpen
	ErrnoGetOrderInfo
	ErrnoGetUserInfo
	ErrnoGetProducts
	ErrnoInvalidProductsRsp
	ErrnoGetMultiEstimatePrice
	ErrnoOrderNotBelongUser
	ErrnoGetPriceEstimateReq
	ErrnoGetPriceEstimateRsp
	ErrnoGetContextTrace
	ErrnoGetOrderFeature
	ErrDCMPEmpty
	ErrnoGetNoBillInfo
	ErronoGetMemberInfo
	ErrnoGetInitialOrderIdFeature
	ErrnoGetQuotationErr
	ErrnoSeatReset
	ErrnoConcurrentErr
	ErrnoRenderErr
	ErrnoRedisErr

	ErrNoStopoverPointsConflictsWithScenes = 530012
)

func GetErrorNum(err error) int {
	switch {
	case errors.Is(err, ErrorFailToParseLocation), errors.Is(err, ErrorFailToGetUserInfo):
		return ErrnoParams
	case errors.Is(err, ErrorGetFromDDSFail):
		return ErrnoInvalidProductsRsp
	case errors.Is(err, ErrorGetFromPriceApiFail):
		return ErrnoGetPriceEstimateRsp
	default:
		return ErrnoSystemError
	}

}

// pAnyCarEstimate 等待应答追加车型错误码
const (
	ErrRenderEstimate = iota + 4000
)

func GetErrMessage(errno int) string {
	switch errno {
	case NoErr:
		return "ok"
	case ErrnoParams:
		return "invalid params"
	case ErrnoEncode:
		return "encode error"
	case ErrnoDecode:
		return "decode error"
	case ErrnoGetOrderInfo:
		return "fail to get orderInfo"
	case ErrnoGetUserInfo:
		return "fail to get userInfo"
	case ErrnoGetProducts:
		return "failed to get dds products"
	case ErrnoInvalidProductsRsp:
		return "invalid products response"
	case ErrnoGetPriceEstimateReq:
		return "invalid price estimate request"
	case ErrnoGetPriceEstimateRsp:
		return "invalid price estimate response"
	case ErrnoGetMultiEstimatePrice:
		return "getMultiEstimatePrice error"
	case ErrnoOrderNotBelongUser:
		return "order not belong to user"
	case ErrnoGetContextTrace:
		return "fail to get context trace"
	case ErrnoGetOrderFeature:
		return "fail to get order feature"
	case ErrnoNoProductOpen:
		return "no product open"
	case ErrDCMPEmpty:
		return "empty dcmp text"
	case ErrnoGetNoBillInfo:
		return "do not get bill info"
	case ErrNoStopoverPointsConflictsWithScenes:
		return "途经点暂不支持设置火车站、机场区域"
	case ErrnoGetQuotationErr:
		return "fail to get quotation"
	case ErrnoSeatReset:
		return "seat info reset"
	case ErrnoConcurrentErr:
		return "concurrent err"
	case ErrnoRedisErr:
		return "redis err"
	default:
		return "unknown error"
	}
}

func GetErr(errno int) error {
	return errors.New(GetErrMessage(errno))
}
