package consts

type AthenaSceneFlag int

// scene_flag GetAthenaBubbleExpectInfo接口
const (
	// QueueSceneFlag 排队场景
	QueueSceneFlag AthenaSceneFlag = 2
	// NoCarSceneFlag 无车场景
	NoCarSceneFlag AthenaSceneFlag = 3
)

// ETPInvalidScene in queue/NoCar scenario etp is invalid and should not be treated like 0
var ETPInvalidScene = map[AthenaSceneFlag]struct{}{
	QueueSceneFlag: {},
	NoCarSceneFlag: {},
}

// expect_scene
const (
	// ProductSceneExpectScene 品类预期
	ProductSceneExpectScene = "product_scene"
	// V3FormGlobalSceneExpectScene v3表单全局预期
	V3FormGlobalSceneExpectScene = "v3_form_global_scene"
	// ClassifyFormGlobalSceneExpectScene 三方聚合表单
	ClassifyFormGlobalSceneExpectScene = "classify_form_global_scene"
	// MapQueueSceneExpectScene 地图区域队列预期
	MapQueueSceneExpectScene = "map_queue_scene"
	// PriceAxisSceneExpectScene 价格轴预期
	PriceAxisSceneExpectScene = "price_axis_scene"
	// OrderMatchSaveTimeScene 追加车型全局预期
	OrderMatchSaveTimeScene = "order_match_save_time_scene"
	// OrderMatchGlobalSceneExpectScene 追加车型全局预期
	OrderMatchGlobalSceneExpectScene = "order_match_global_scene"
)
