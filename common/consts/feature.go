package consts

// 功能列表常量，使用驼峰命名
const (
	FeatureCarpoolShunluTag          = 1000 // 拼车顺路标签
	FeatureComboSale                 = 1001 // 车型下挂的套餐搭售
	FeatureSpaciousFeeDescList       = 1002 // 车大的优惠标签
	FeatureStationCarpoolFastCarDiff = 1003 // 极速拼车的"比快车省"标签
	FeatureCategoryInfo              = 1004 // 侧边栏，v3返回的category_info字段
	FeatureXDiscountBox              = 1005 // X折特价车盒子
	FeatureFilterInfo                = 1007 // 筛选器
	FeatureSinkingExp                = 1008 // 下沉表单实验

	FeaturePersonalizedSwitchLayer   = 1010 // 个性化开关弹层
	FeatureGuidePopup                = 1011 // 导流弹窗
	FeatureGuideBar                  = 1012 // 导流bar
	FeatureUserGuideInfo             = 1012 // 新流手势引导
	FeatureRecommendBubble           = 1013 // 勾选推荐气泡
	FeatureStationCarpoolDiscountTag = 1014 // 极速拼车优惠标签

	// 价格沟通 1050-1100
	FeatureInterCityCarpoolCapPrice = 1050 // 价格沟通：远途拼车一口价 rule_type = 17
	FeatureCarpoolDualPriceRule     = 1051 // 价格沟通：两口价 rule_type = 21
	FeatureSpecialRateRule          = 1052 // 价格沟通：特惠一口价 rule_type = 7
	FeaturePremiumCapPriceRule      = 1053 // 价格沟通：专车一口价 rule_type = 4
	FeatureFastCarCapPriceRule      = 1054 // 价格沟通：快车一口价 rule_type = 13
	FeatureAPlusCapPriceRule        = 1055 // 价格沟通：A+一口价 rule_type = 14
	FeatureTaxiCapPriceRule         = 1056 // 价格沟通：出租车一口价 rule_type = 16
)
