package consts

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestConvertAccessKeyID2BaiChuanAppID(t *testing.T) {
	tests := []struct {
		name        string
		accessKeyID int32
		expected    int64
	}{
		{
			name:        "DiDi iOS",
			accessKeyID: AccessKeyIDDiDiIos,
			expected:    BaiChuanAppIDMainClient,
		},
		{
			name:        "DiDi Android",
			accessKeyID: AccessKeyIDDiDiAndroid,
			expected:    BaiChuanAppIDMainClient,
		},
		{
			name:        "<PERSON><PERSON><PERSON>",
			accessKeyID: AccessKeyIDDiDiHongMeng,
			expected:    BaiChuanAppIDMainClient,
		},
		{
			name:        "DiDi Wechat Mini",
			accessKeyID: AccessKeyIDDiDiWechatMini,
			expected:    BaiChuanAppIDWechatMini,
		},
		{
			name:        "DiD<PERSON> Alipay Mini",
			accessKeyID: AccessKeyIDDiDiAlipayMini,
			expected:    BaiChuanAppIDAlipayMini,
		},
		{
			name:        "SFC Wechat Mini",
			accessKeyID: AppAccessKeyIDSFCWechatMini,
			expected:    BaiChuanAppIDSFCWechatMini,
		},
		{
			name:        "SFC Alipay Mini",
			accessKeyID: AppAccessKeyIDSFCAlipayMini,
			expected:    BaiChuanAppIDSFCAlipayMini,
		},
		{
			name:        "Unknown AccessKeyID",
			accessKeyID: 999, // some unknown value
			expected:    0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ConvertAccessKeyID2BaiChuanAppID(tt.accessKeyID)
			assert.Equal(t, tt.expected, result)
		})
	}
}
