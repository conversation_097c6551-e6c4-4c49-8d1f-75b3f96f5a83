package httpserv

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/middleware/ctxcache"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/conf"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
	"git.xiaojukeji.com/gulfstream/mamba/controller"
	"git.xiaojukeji.com/gulfstream/mamba/idl/proto"
	commonMiddlewareCache "git.xiaojukeji.com/intercity/biz-api/intercity-common-go/middleware/cache"

	httpTrace "git.xiaojukeji.com/gulfstream/mamba/middleware/http-trace"

	"git.xiaojukeji.com/nuwa/golibs/httpserver/middleware"
	"git.xiaojukeji.com/nuwa/golibs/rpcserver/v2/rpcserver"
	"github.com/grpc-ecosystem/grpc-gateway/runtime"
)

var (
	svr = rpcserver.New()

	httpRegister = func(ctx context.Context, mux *runtime.ServeMux) error {
		return proto.RegisterMambaHandlerServer(ctx, mux, controller.MambaServerImplement)
	}
)

// Run 启动服务
func Run() error {
	/* 读取conf配置 */
	svr.SetHTTPAddr(conf.Viper.GetString("rpc.http_addr"))
	svr.SetHTTPReadTimeout(conf.Viper.GetInt("rpc.http_read_timeout"))
	svr.SetHTTPIdleTimeout(conf.Viper.GetInt("rpc.http_idle_timeout"))

	/* 服务注册 */
	svr.SetHTTPRegister(httpRegister)

	/* http 中间件，可以自定义添加， 更多http中间件参考：http://wiki.intra.xiaojukeji.com/pages/viewpage.action?pageId=132079586 */
	// 先加后调用, 压栈
	// recovery中间件
	svr.AddHTTPMiddleware(middleware.RecoveryWithConfig(middleware.RecoveryConfig{Log: log.Trace}))

	// trace中间件
	svr.AddHTTPMiddleware(httpTrace.TraceWithConfig(httpTrace.TraceConfig{Log: log.Trace}))

	// metric 上报中间件
	svr.AddHTTPMiddleware(middleware.MetricAccess(middleware.MetricConfig{Log: log.Trace}))

	// knife 缓存中间价
	svr.AddHTTPMiddleware(commonMiddlewareCache.KnifeWithConfig(commonMiddlewareCache.KnifeConfig{}))

	// add request context
	svr.AddHTTPMiddleware(ctxcache.InitRequestContext())
	//跨域中间件
	svr.AddHTTPMiddleware(middleware.CorsWithConfig(middleware.CORSConfig{
		AllowedMethods: []string{"GET", "HEAD", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders: []string{"Accept", "Accept-Language", "Content-Language", "Origin", "X-Requested-With", "Didi-Header-Rid", "Content-Type", "Didi-Header-Hint-Content", "Cityid", "Productid"},
		AllowedOrigins: []string{"*"},
	}))

	// 设置默认编解码方式
	svr.SetJsonHttpMarshaler()

	runtime.HTTPError = DefaultHTTPError

	/* 添加http handle，如上传文件，页面等等 */
	// nolint: gocritic
	// svr.AddHTTPHandleFunc("/test", func(w http.ResponseWriter, r *http.Request) {
	// 	w.Write([]byte("test"))
	// })

	return svr.HttpRun()
}
