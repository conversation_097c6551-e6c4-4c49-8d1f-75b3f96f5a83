package group_id

import (
	"github.com/spf13/cast"
	"strings"
)

// athena车型类型，group_id 第一个参数
const (
	SINGLETYPE         = 0
	AggregationBoxType = 2
	FLOWBOXType        = 5
)

// 聚合车型盒子id，聚合车型 group_id 第2个参数
const (
	SubGroupShortDistance                = 1
	SubGroupIdTaxi                       = 2
	SubGroupIdTaxiPricing                = 5
	SubGroupIdFastMustCheaper            = 6
	SubGroupIdXDiscount                  = 7
	SubGroupIdDiscountAlliance           = 8
	SubGroupIdTaxiChaoZhiDa              = 10
	SubGroupIdTaxiHkThirdBusinessNormal  = 21
	SubGroupIdTaxiHkThirdBusinessComfort = 22
)

func BuildGroupId(athenaType int, subGroupId int64) string {
	return cast.ToString(athenaType) + "_" + cast.ToString(subGroupId)
}

func BuildGroupIdById(subGroupId int64, productCategory int64) string {
	if subGroupId != 0 {
		return BuildGroupId(AggregationBoxType, subGroupId)
	}

	return BuildGroupId(SINGLETYPE, productCategory)
}

type GroupId struct {
	GroupType  int32
	SubGroupID int32
}

func NewGroupId(groupId string) *GroupId {
	split := strings.Split(groupId, "_")
	if len(split) != 2 {
		return nil
	}
	return &GroupId{
		GroupType:  cast.ToInt32(split[0]),
		SubGroupID: cast.ToInt32(split[1]),
	}
}

func (g *GroupId) GetSubGroupIdByGroupId() *int32 {
	if g.GroupType == AggregationBoxType {
		return &g.SubGroupID
	}

	return nil
}

func (g *GroupId) IsFlowBoxByGroupId() bool {
	return g.GroupType == FLOWBOXType
}
