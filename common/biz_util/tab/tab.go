package tab

import (
	"encoding/json"
)

const (
	// TabIdClassify 三方聚合表单
	TabIdClassify = "classify"
	// TabIdMaas80 composite_travel
	TabIdMaas80 = "composite_travel"
	//	TabIdMaas85 aggregation_travel
	TabIdMaas85 = "aggregation_travel"
	// TabIdNormal normal
	TabIdNormal = "normal"
)

type Tab struct {
	TabID         string `json:"tab_id"`
	DefaultSelect bool   `json:"default_select"`
}

func GetTabList(tabListStr string) []Tab {
	tabList := []Tab{}
	if tabListStr == "" {
		return tabList
	}
	_ = json.Unmarshal([]byte(tabListStr), &tabList)
	return tabList
}

func IsClassifyTab(tabId string) bool {
	return tabId == TabIdClassify
}

func IsNormalTab(tabId string) bool {
	return tabId == TabIdNormal
}

func IsStopAggTab(tabId string) bool {
	return tabId == TabIdMaas85
}
