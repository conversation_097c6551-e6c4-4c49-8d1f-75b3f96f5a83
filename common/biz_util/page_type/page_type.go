package page_type

const (
	PageTypeUndefined                     = 0
	PageTypeCharterCar                    = 2
	PageTypeAccompany                     = 6 // 陪伴出行
	PageTypeIntercityEstimate             = 8
	PageTypePetCar                        = 10 // 宠物车
	PageTypeRecCarpoolEstimate            = 17
	PageTypeGuideAnyCar                   = 20
	PageTypeCancelEstimate                = 21
	PageTypeCarpoolTabEstimate            = 27
	PageTypeCallCarEstimate               = 30
	PageTypeBusinessAnyCarEstimate        = 31
	PageTypeSimpleEstimate                = 33
	PageTypeCompositeTravel               = 34
	PageTypeSFCEstimate                   = 35
	PageTypeIntercityStationEstimate      = 39
	PageTypeMinibusEstimate               = 40
	PageTypeOrderBookingPreCancelEstimate = 42
	PageTypeLowCarpoolEstimate            = 43 // 新拼成乐独立页
	PageTypeLankeBao                      = 45
	PageTypePetsTravel                    = 49
	PageTypeSmartBusEstimate              = 51
	PageTypeNewEnergyCar                  = 53 // 新能源独立页
	PageTypePetAppendFormCar              = 54 // 宠物出行追加车型
	PageTypeXinZhu                        = 55 // 新竹
)
