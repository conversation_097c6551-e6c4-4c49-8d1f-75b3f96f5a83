package taxi

import (
	"context"

	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/apollo"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

func IsTaxi(productID int64) bool {
	if productID == consts.ProductIDUNITAXI {
		return true
	}

	return false
}

func disableUnioneGroup(ctx context.Context, apolloParam *ApolloModel.User) bool {
	toggle := apollo.HitExperimentInLayer(ctx, "taxi_form", apolloParam)
	return toggle != nil && toggle.GetAssignment().GetParameter("disable_taxi_box", "0") == "1"
}
