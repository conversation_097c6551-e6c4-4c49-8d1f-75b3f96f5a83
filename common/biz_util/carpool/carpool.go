package carpool

import (
	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	consts2 "git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/ntuple"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/util"
)

const ComboType = 4

const (
	SendNew    = "send"        // 极速拼车赠卡新用户
	SendLoss   = "send_loss"   // 极速拼车赠卡流失用户
	Usable     = "usable"      // 极速拼车可用卡，需要和PayStatus结合使用来判断是否是赠卡可用卡
	UsableGive = "usable_give" // 极速拼车买赠卡
	PayStatus  = 0             // 当Vcard.PayStatus = 0 && Vcard.Usable == 'usable'表示赠卡-可用卡，当Vcard.PayStatus != 0 && Vcard.Usable == 'usable'表示 买卡-可用卡

	CardTypeGive = 3 // 买赠卡
)

// carpool_type 拼车类型
const (
	TypeNone         = 0  // 非拼车
	TypeNormal       = 1  // 普通拼车
	TypeStation      = 2  // 站点拼车
	TypeIntercity    = 3  // 跨城拼车
	TypeLowPrice     = 4  // 拼车车
	TypeFlatRate     = 5  // 司乘一口价
	TypeIntercityNew = 6  // 城际拼车-新模式
	TypeMiniBus      = 10 // 市内小巴
)

// pricing_type
const (
	PricingTypeCarpoolDay = 5
)

// carpool_price_type 拼车计费类型
const (
	CarPoolPriceTypeDefault              = 0 // 字段默认值
	CarPoolPriceUnSucceedRealTimePricing = 1 // 拼车拼不成实时计价
	CarPoolPriceUnSucceedCapPrice        = 2 // 拼成乐边走边拼
	CarPoolPriceMultiCapPrice            = 3 // 拼成乐v2 多口价
	CarPoolPriceTwoCapPrice              = 4 // 两口价（拼成、拼不成都一口价）
)

type BaseData struct {
	City        int
	Phone       string
	AccessKeyID int
}

func IsCarpool(carpoolType int64) bool {
	return carpoolType > 0
}

func IsCarpoolDay(priceType int32) bool {
	return priceType == PricingTypeCarpoolDay
}

// 拼车两口价V2 （拼成一口价拼不成实时计价）
func IsCarpoolDualPriceV2(carpoolType int64, isDualCarpoolPrice bool, carpoolPriceType int32) bool {
	return carpoolType == TypeStation && isDualCarpoolPrice && carpoolPriceType == CarPoolPriceUnSucceedRealTimePricing
}

// IsInterCityCarpool 是否是城际拼车
func IsInterCityCarpool(carpoolType, comboType int64) bool {
	// 跨城拼车 || 城际拼车-新模式 || 跨区域拼车
	return carpoolType == consts.CarPoolTypeInterCity ||
		carpoolType == consts.CarPoolTypeInterCityNew ||
		comboType == consts.TypeComboCarpoolInterCity ||
		carpoolType == consts.CarPoolTypeInterCityStation
}

func IsIntercitySmallCar(carpoolType, comboType int64) bool {
	return carpoolType == consts.CarPoolTypeInterCity && comboType == consts.TypeComboCarpoolInterCity
}

func IsInterCityDualPrice(carpoolType int64, comboType int64, carpoolPriceType int32) bool {
	if !IsInterCityCarpool(carpoolType, comboType) {
		return false
	}

	if carpoolPriceType == CarPoolPriceTwoCapPrice {
		return true
	}

	return false
}

func IsCarpoolUnsuccRealTimePrice(carpoolPriceType int) bool {
	return CarPoolPriceUnSucceedRealTimePricing == carpoolPriceType
}

func IsLowPriceCarpoolByInfos(comboType, productId, carpoolType int64, requireLevel string) bool {
	if comboType == ComboType && requireLevel == "600" && productId == int64(consts.ProductIDFastCar) && carpoolType == int64(TypeLowPrice) {
		return true
	}
	return false
}

// 是否是站点拼车
func IsStationCarpool(carpoolType int32) bool {
	if util.InArrayInt32(carpoolType, []int32{TypeStation, TypeLowPrice}) {
		return true
	}
	return false
}

// 是否是拼车两口价
func IsCarpoolDualPrice(carpoolType int32, isDualCarpoolPrice bool) bool {
	if !IsStationCarpool(carpoolType) {
		return false
	}
	if isDualCarpoolPrice {
		return true
	}
	return false
}

func IsCarpoolDualPriceFull(tuple ntuple.Provider) bool {
	if IsCarpoolUnSuccessFlatPrice(tuple) {
		return true
	}

	if IsInterCityDualPrice(tuple.GetCarpoolType(), tuple.GetComboType(), tuple.GetCarpoolPriceType()) {
		return true
	}

	return false
}

// 是否是拼车两口价V3
func IsCarpoolDualPriceV3(carpoolType, carpoolPriceType int32, isDualCarpoolPrice bool) bool {
	if !IsCarpoolDualPrice(carpoolType, isDualCarpoolPrice) {
		return false
	}
	if CarPoolPriceTypeDefault == carpoolPriceType {
		return true
	}
	return false
}

// 是否是拼成乐V2
func IsPinCheCheV2(productCategory int64, carpoolPriceType int32) bool {
	if !IsPinCheChe(productCategory) {
		return false
	}

	return carpoolPriceType == CarPoolPriceMultiCapPrice
}

// 是否是拼成乐
func IsPinCheChe(productCategory int64) bool {
	return productCategory == estimate_pc_id.EstimatePcIdLowPriceCarpool
}

func IsPinCheCheNewForm(productCategory int64, formStyle int64) bool {
	return productCategory == estimate_pc_id.EstimatePcIdLowPriceCarpool && formStyle == 1
}

// IsCarpoolFlatRate 是否是站点拼车区域渗透
func IsCarpoolFlatRate(carpoolType int64) bool {
	return carpoolType == consts.CarPoolTypeFLatRate
}

// IsCarpoolUnSuccessFlatPrice 是否是拼车两口价未拼成一口价
func IsCarpoolUnSuccessFlatPrice(tuple ntuple.Provider) bool {
	if !IsCarpoolDualPrice(int32(tuple.GetCarpoolType()), tuple.IsDualCarpoolPrice()) {
		return false
	}

	if consts2.CarpoolPriceTypeDefault == int(tuple.GetCarpoolPriceType()) {
		return true
	}

	return false
}

func IsMiniBus(carpoolType int) bool {
	if carpoolType == consts.CarPoolTypeMiniBus {
		return true
	}
	return false
}

func IsTaxiCarpool(productCategory int64) bool {
	return productCategory == estimate_pc_id.EstimatePcIdTaxiCarpool
}

func IsSmartBus(carpoolType int) bool {
	if carpoolType == consts.CarPoolTypeSmartBus {
		return true
	}
	return false
}
