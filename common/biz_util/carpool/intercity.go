package carpool

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/tripcloud"
	"git.xiaojukeji.com/gulfstream/mamba/common/consts"
	ApolloSDK "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2"
	ApolloModel "go.intra.xiaojukeji.com/apollo/apollo-golang-sdk-v2/v2/model"
)

var guideProductCategory = []int64{estimate_pc_id.EstimatePcIdTrainTicket, estimate_pc_id.EstimatePcIdCarpoolSFCar, estimate_pc_id.EstimatePcIdCarpoolCrossSFCar}

// 是否是城际拼满走
func IsIntercityFullGo(productCategory int64, route_type int64) bool {
	if productCategory == estimate_pc_id.EstimatePcIdCarpoolInter && route_type == 3 {
		return true
	}
	return false
}

func IsIntercityNewMode(productCategory int64, route_type int64, inter_mode int64) bool {
	return IsIntercityFullGo(productCategory, route_type) && inter_mode == 1
}

func IsIntercityThirdPID(ctx context.Context, prodcutId int) bool {
	return tripcloud.IsIntercityThirdProduct(ctx, prodcutId)
}

func IsIntercityStation(ctx context.Context, carpoolType int) bool {
	if carpoolType == consts.CarPoolTypeInterCityStation {
		return true
	}
	return false
}

// 此方法不包含城际自营的判断
func IsSkuModelNoSelf(baseReq BaseData, routeGroup int) bool {
	param := ApolloModel.NewUser("").
		With("city", strconv.Itoa(baseReq.City)).
		With("phone", baseReq.Phone).
		With("route_group", strconv.Itoa(routeGroup)).
		With("access_key_id", strconv.Itoa(baseReq.AccessKeyID)).
		With("source", "mamba")
	toggle, err := ApolloSDK.FeatureToggle("intercity_carpool_sku_model_grayscale", param)
	if err != nil || !toggle.IsAllow() {
		return false
	}
	return true
}

func IsTargetGuide(pcID, carpoolType, comboType int64) bool {
	// 检查产品类别是否在导流产品类别列表中
	for _, category := range guideProductCategory {
		if pcID == category {
			return true
		}
	}

	// 检查是否为城际拼车类型
	if carpoolType == consts.CarPoolTypeInterCity || comboType == consts.TypeComboCarpoolInterCity {
		return true
	}

	return false
}
