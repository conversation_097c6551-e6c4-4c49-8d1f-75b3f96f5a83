package client_type

// 客户端类型定义
const (
	// ClientTypeAndroid ...
	ClientTypeAndroid = (iota * 100) + 1
	// ClientTypeIOS 101
	ClientTypeIOS
	// ClientTypeWebApp 201
	ClientTypeWebApp
	// ClientTypeB2B 301
	ClientTypeB2B
	// ClientTypeOpenAPI 401
	ClientTypeOpenAPI
	// ClientTypeGuide 501
	ClientTypeGuide
)

func IsFromB2B(clientType int32) bool {
	return ClientTypeB2B == clientType
}

func IsFromWebApp(clientType int32) bool {
	return ClientTypeWebApp == clientType
}
