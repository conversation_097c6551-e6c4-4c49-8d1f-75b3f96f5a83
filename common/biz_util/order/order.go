package order

import (
	"context"
	"strconv"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/estimate_pc_id"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/combo_type"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/product_id"

	"git.xiaojukeji.com/dukang/property-const-go-sdk/order/count_price_type"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/ntuple"
	consts2 "git.xiaojukeji.com/gulfstream/mamba/common/consts"
	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
)

const (
	OrderTypeNow     = 0
	OrderTypeBooking = 1
)

const (
	OLineUp = 30
	OPrePay = 34
)

func IsBookingOrder(orderType int32) bool {
	return orderType == OrderTypeBooking
}

func IsLineUpOrder(orderExtraType uint64) bool {
	return orderExtraType&(1<<(OLineUp-1)) > 0
}

func IsPrepayOrder(orderExtraType uint64) bool {
	return orderExtraType&(1<<(OPrePay-1)) > 0
}

// IsSpecialRateV2 IsSpecialRateV2
func IsSpecialRateV2(tuple ntuple.Provider, sceneType int) bool {
	if IsSpecialRate(tuple.GetComboType(), sceneType) {
		return true
	}

	if isIndependentSpecialRate(tuple.GetProductId(), tuple.GetRequireLevel()) {
		return true
	}

	return false
}

func IsSpecialRate(comboType int64, sceneType int) bool {
	if comboType == consts.ComboTypeSpecialRate {
		return true
	}

	if sceneType == int(consts.SceneTypeSpecialRate) {
		return true
	}

	return false
}

func isIndependentSpecialRate(productID int64, requireLevel string) bool {
	iRequireLevel, err := strconv.Atoi(requireLevel)
	if err != nil {
		return false
	}

	return (productID == consts.ProductIDSpecialRate || productID == consts.ProductIDBusinessSpecialRate) && iRequireLevel == consts.CarLevelSpecialRate
}

// IsFlatRate 是否快车 & 专车（不包括企业）区域一口价场景.
func IsFlatRate(comboType int64, sceneType int) bool {
	if comboType == consts.ComboTypeFlatRate {
		return true
	}

	if sceneType == int(consts.SceneTypeFlatRate) {
		return true
	}

	return false
}

func IsAirportFlatRate(productID int64, comboType int64, capPrice float64) bool {
	if product_id.IsDefault(productID) && (comboType == combo_type.ComboTypeFromAirport || comboType == combo_type.ComboTypeToAirport) {
		if capPrice > 0 {
			return true
		}
	}
	return false
}

// IsAPlus 是否A+单
func IsAPlus(_ context.Context, tuple ntuple.Provider) bool {
	return tuple.GetProductCategory() == estimate_pc_id.EstimatePcIdAplus ||
		tuple.GetProductCategory() == estimate_pc_id.EstimatePcIdBusinessAPlusCar
}

func IsMultiFactorFlatRateV2(tuple ntuple.Provider) bool {
	isMultiFactor := false
	if tuple.GetComboType() == consts2.TypeComboMultiFactorFlatRate {
		isMultiFactor = true
	}

	if tuple.GetCountPriceType() == consts2.CountPriceTypeMultiFactorCapPrice {
		isMultiFactor = true
	}

	return isMultiFactor
}

func IsCapFast(tuple ntuple.Provider) bool {
	if tuple.GetProductId() != int64(consts.ProductIDFastCar) {
		return false
	}

	if tuple.GetCountPriceType() != count_price_type.CountPriceTypeCapFast {
		return false
	}

	return true
}

func IsCapAPlus(ctx context.Context, tuple ntuple.Provider) bool {
	if !IsAPlus(ctx, tuple) {
		return false
	}

	if tuple.GetCountPriceType() != count_price_type.CountPriceTypeAplus {
		return false
	}

	return true
}

func IsDiOne(ctx context.Context, tuple ntuple.Provider) bool {
	if tuple.GetProductId() != consts.ProductIDDiOne {
		return false
	}

	requireLevel, err := strconv.Atoi(tuple.GetRequireLevel())
	if err != nil {
		log.Trace.Warnf(ctx, "IsDiOne", "require level to int fail, require_level:%v, err:%v", requireLevel, err)
	}
	if requireLevel != int(consts.CarLevelDiOne) {
		return false
	}

	if tuple.GetComboType() != consts.ComboTypeDefault {
		return false
	}

	return true
}

func IsDioneCountPriceType(tuple ntuple.Provider) bool {
	return tuple.GetCountPriceType() == consts2.CountPriceTypeDOne
}
