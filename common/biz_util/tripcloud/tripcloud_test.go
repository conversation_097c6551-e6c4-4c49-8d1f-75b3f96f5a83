package tripcloud

import (
	"context"
	"testing"

	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/consts"
	"git.xiaojukeji.com/gulfstream/biz-common-go/v6/tripcloud"
	"git.xiaojukeji.com/nuwa/golibs/knife"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

func TestIsTripcloudProductID(t *testing.T) {
	patch := mockey.Mock(tripcloud.GetTripcloudProducts).To(func() []consts.ProductID {
		return []consts.ProductID{101, 102, 103}
	}).Build()
	defer patch.UnPatch()

	ctx := context.Background()
	// ctx无key，走constructTcProductMap并set
	assert.True(t, IsTripcloudProductID(ctx, 101))
	assert.False(t, IsTripcloudProductID(ctx, 999))

	ctx = knife.New(ctx)
	// IsTripcloudProductID 命中 ctx 注入
	assert.True(t, IsTripcloudProductID(ctx, 101))
	assert.True(t, IsTripcloudProductID(ctx, 102))
	assert.False(t, IsTripcloudProductID(ctx, 203))
}
