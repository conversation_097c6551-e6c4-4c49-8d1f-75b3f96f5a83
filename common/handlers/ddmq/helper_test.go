package ddmq

import (
	"testing"

	"go.intra.xiaojukeji.com/foundation/carrera-go-sdk/producer/src/carrera"
)

func Test_getMsgFromBuilderUnsafe(t *testing.T) {
	type tTestcase struct {
		name    string
		input   *carrera.MessageBuilder
		wantNil bool
	}

	testcases := []tTestcase{
		{
			name:    "bd-nil",
			input:   nil,
			wantNil: true,
		},
		{
			name:    "bd-not-valid",
			input:   &carrera.MessageBuilder{},
			wantNil: true,
		},
		{
			name:    "bd-valid",
			input:   carrera.NewMessageBuilder(nil),
			wantNil: false,
		},
	}

	for _, testcase := range testcases {

		t.Run(testcase.name, func(t *testing.T) {
			actual := getMsgFromBuilderUnsafe(testcase.input)
			if testcase.wantNil && actual != nil {
				t.E<PERSON>rf("expect: <nil>, got: %#v", actual)
			} else if !testcase.wantNil && actual == nil {
				t.<PERSON>("got unexpect nil")
			}
		})
	}
}
