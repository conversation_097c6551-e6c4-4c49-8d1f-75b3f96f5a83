package apollo

import (
	"context"
	gaiaSdk "git.xiaojukeji.com/engine/gaia-sdk"
	"github.com/spf13/cast"
	"gopkg.in/yaml.v2"

	"git.xiaojukeji.com/gulfstream/mamba/common/handlers/log"
)

const GaiaNs = "athena"

type Config struct {
	Param map[string]interface{} `yaml:"param" json:"param,omitempty" `
}

func FeatureGaia(ctx context.Context, factor string, gaiaParam map[string]string, featureKey string) (bool, *Config) {
	res, err := gaiaSdk.FindGaiaExperiment(ctx, factor, GaiaNs, gaiaParam)
	if err != nil {
		// log.Trace.Warnf(ctx, "gaia", "GET_TOGGLE_GAIA||err=%v", err)
		return false, nil
	}
	log.Trace.Infof(ctx, "gaia", "GET_TOGGLE_GAIA||exp_name=%s||exp_group=%s||stg_id=%d",
		res.ExperimentName,
		res.GroupName,
		res.StgId)

	if confStr, ok := res.StgConf.(string); ok {
		var configs []Config
		err = yaml.Unmarshal([]byte(confStr), &configs)
		if err != nil || configs == nil || len(configs) < 1 {
			return false, nil
		}
		cfg := configs[0]
		if v, ok := cfg.Param[featureKey]; ok && "1" == cast.ToString(v) {
			return true, &cfg
		}
	}

	return false, nil
}

func (c *Config) FeatureInConfigArray(featureKey string, target interface{}) bool {
	if c == nil || c.Param == nil {
		return false
	}
	value, exists := c.Param[featureKey]
	if !exists {
		return false
	}
	arr, ok := value.([]interface{})
	if !ok {
		return false
	}
	for _, item := range arr {
		if c.isEqual(item, target) {
			return true
		}
	}
	return false
}

// isEqual compares two values by converting both to strings
func (c *Config) isEqual(a, b interface{}) bool {
	return cast.ToString(a) == cast.ToString(b)
}

func (c *Config) FeatureConfigAllow(featureKey string) bool {
	if c == nil || c.Param == nil {
		return false
	}
	value, exists := c.Param[featureKey]
	if !exists {
		return false
	}
	return cast.ToBool(value)
}

func (c *Config) GetParameter(key, defaultValue string) string {
	if c == nil || c.Param == nil {
		return defaultValue
	}
	value, exists := c.Param[key]
	if exists {
		return cast.ToString(value)
	}

	return defaultValue
}
