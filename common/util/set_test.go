package util

import (
	"fmt"
	"testing"
)

func TestSet_Basic(t *testing.T) {
	// 测试基本操作
	set := NewSet[int]()

	// 测试添加
	set.Add(1)
	set.Add(2)
	set.Add(3)

	if set.Size() != 3 {
		t.<PERSON><PERSON><PERSON>("Expected size 3, got %d", set.Size())
	}

	// 测试包含
	if !set.Contains(1) {
		t.<PERSON><PERSON>("Set should contain 1")
	}

	if set.Contains(4) {
		t.<PERSON><PERSON>("Set should not contain 4")
	}

	// 测试移除
	set.Remove(2)
	if set.Contains(2) {
		t.<PERSON>rror("Set should not contain 2 after removal")
	}

	if set.Size() != 2 {
		t.<PERSON><PERSON>("Expected size 2 after removal, got %d", set.Size())
	}
}

func TestSet_StringType(t *testing.T) {
	// 测试字符串类型
	set := NewSet[string]("apple", "banana", "cherry")

	if set.Size() != 3 {
		t.<PERSON>("Expected size 3, got %d", set.<PERSON>ze())
	}

	if !set.Contains("apple") {
		t.<PERSON><PERSON><PERSON>("Set should contain 'apple'")
	}

	set.Add("apple") // 重复添加
	if set.Size() != 3 {
		t.Error("Set size should not change when adding duplicate")
	}
}

func TestSet_SetOperations(t *testing.T) {
	set1 := NewSet[int](1, 2, 3, 4)
	set2 := NewSet[int](3, 4, 5, 6)

	// 测试并集
	union := set1.Union(set2)
	expectedUnionSize := 6 // {1, 2, 3, 4, 5, 6}
	if union.Size() != expectedUnionSize {
		t.Errorf("Expected union size %d, got %d", expectedUnionSize, union.Size())
	}

	// 测试交集
	intersection := set1.Intersection(set2)
	expectedIntersectionSize := 2 // {3, 4}
	if intersection.Size() != expectedIntersectionSize {
		t.Errorf("Expected intersection size %d, got %d", expectedIntersectionSize, intersection.Size())
	}

	if !intersection.Contains(3) || !intersection.Contains(4) {
		t.Error("Intersection should contain 3 and 4")
	}

	// 测试差集
	difference := set1.Difference(set2)
	expectedDifferenceSize := 2 // {1, 2}
	if difference.Size() != expectedDifferenceSize {
		t.Errorf("Expected difference size %d, got %d", expectedDifferenceSize, difference.Size())
	}

	if !difference.Contains(1) || !difference.Contains(2) {
		t.Error("Difference should contain 1 and 2")
	}
}

func TestSet_SubsetSuperset(t *testing.T) {
	set1 := NewSet[int](1, 2)
	set2 := NewSet[int](1, 2, 3, 4)

	if !set1.IsSubset(set2) {
		t.Error("set1 should be a subset of set2")
	}

	if !set2.IsSuperset(set1) {
		t.Error("set2 should be a superset of set1")
	}

	if set2.IsSubset(set1) {
		t.Error("set2 should not be a subset of set1")
	}
}

func TestSet_Equal(t *testing.T) {
	set1 := NewSet[int](1, 2, 3)
	set2 := NewSet[int](3, 2, 1) // 顺序不同
	set3 := NewSet[int](1, 2, 3, 4)

	if !set1.Equal(set2) {
		t.Error("set1 and set2 should be equal")
	}

	if set1.Equal(set3) {
		t.Error("set1 and set3 should not be equal")
	}
}

func TestSet_Filter(t *testing.T) {
	set := NewSet[int](1, 2, 3, 4, 5, 6)

	// 过滤出偶数
	evenSet := set.Filter(func(n int) bool {
		return n%2 == 0
	})

	if evenSet.Size() != 3 {
		t.Errorf("Expected 3 even numbers, got %d", evenSet.Size())
	}

	if !evenSet.Contains(2) || !evenSet.Contains(4) || !evenSet.Contains(6) {
		t.Error("Even set should contain 2, 4, and 6")
	}
}

func TestSet_Clone(t *testing.T) {
	original := NewSet[int](1, 2, 3)
	cloned := original.Clone()

	if !original.Equal(cloned) {
		t.Error("Cloned set should be equal to original")
	}

	// 修改原始set不应该影响克隆的set
	original.Add(4)
	if cloned.Contains(4) {
		t.Error("Cloned set should not be affected by changes to original")
	}
}

func TestSet_ToSlice(t *testing.T) {
	set := NewSet[int](1, 2, 3)
	slice := set.ToSlice()

	if len(slice) != 3 {
		t.Errorf("Expected slice length 3, got %d", len(slice))
	}

	// 检查所有元素都在切片中
	for _, item := range slice {
		if !set.Contains(item) {
			t.Errorf("Slice contains item %d that is not in set", item)
		}
	}
}

func TestSet_Clear(t *testing.T) {
	set := NewSet[int](1, 2, 3)
	set.Clear()

	if !set.IsEmpty() {
		t.Error("Set should be empty after clear")
	}

	if set.Size() != 0 {
		t.Errorf("Expected size 0 after clear, got %d", set.Size())
	}
}

func TestSet_AddAll(t *testing.T) {
	set := NewSet[int]()
	set.AddAll(1, 2, 3, 4, 5)

	if set.Size() != 5 {
		t.Errorf("Expected size 5, got %d", set.Size())
	}

	for i := 1; i <= 5; i++ {
		if !set.Contains(i) {
			t.Errorf("Set should contain %d", i)
		}
	}
}

func TestSet_RemoveAll(t *testing.T) {
	set := NewSet[int](1, 2, 3, 4, 5)
	set.RemoveAll(2, 4)

	if set.Size() != 3 {
		t.Errorf("Expected size 3, got %d", set.Size())
	}

	if set.Contains(2) || set.Contains(4) {
		t.Error("Set should not contain 2 or 4")
	}

	if !set.Contains(1) || !set.Contains(3) || !set.Contains(5) {
		t.Error("Set should still contain 1, 3, and 5")
	}
}

func TestSet_ContainsAll(t *testing.T) {
	set := NewSet[int](1, 2, 3, 4, 5)

	if !set.ContainsAll(1, 3, 5) {
		t.Error("Set should contain all of 1, 3, 5")
	}

	if set.ContainsAll(1, 3, 6) {
		t.Error("Set should not contain all of 1, 3, 6")
	}
}

func TestSet_ContainsAny(t *testing.T) {
	set := NewSet[int](1, 2, 3)

	if !set.ContainsAny(1, 6, 7) {
		t.Error("Set should contain at least one of 1, 6, 7")
	}

	if set.ContainsAny(6, 7, 8) {
		t.Error("Set should not contain any of 6, 7, 8")
	}
}

func TestSet_SymmetricDifference(t *testing.T) {
	set1 := NewSet[int](1, 2, 3, 4)
	set2 := NewSet[int](3, 4, 5, 6)

	symDiff := set1.SymmetricDifference(set2)

	// 对称差集应该包含 {1, 2, 5, 6}
	if symDiff.Size() != 4 {
		t.Errorf("Expected symmetric difference size 4, got %d", symDiff.Size())
	}

	if !symDiff.ContainsAll(1, 2, 5, 6) {
		t.Error("Symmetric difference should contain 1, 2, 5, 6")
	}

	if symDiff.ContainsAny(3, 4) {
		t.Error("Symmetric difference should not contain 3 or 4")
	}
}

func TestFromSlice(t *testing.T) {
	slice := []int{1, 2, 3, 2, 1}
	set := FromSlice(slice)

	if set.Size() != 3 {
		t.Errorf("Expected size 3, got %d", set.Size())
	}

	if !set.ContainsAll(1, 2, 3) {
		t.Error("Set should contain 1, 2, 3")
	}
}

func TestMap(t *testing.T) {
	intSet := NewSet[int](1, 2, 3, 4, 5)

	// 将int转换为string
	stringSet := Map(intSet, func(n int) string {
		return fmt.Sprintf("num_%d", n)
	})

	if stringSet.Size() != 5 {
		t.Errorf("Expected mapped set size 5, got %d", stringSet.Size())
	}

	if !stringSet.Contains("num_1") {
		t.Error("Mapped set should contain 'num_1'")
	}
}

func TestReduce(t *testing.T) {
	set := NewSet[int](1, 2, 3, 4, 5)

	// 计算所有元素的和
	sum := Reduce(set, 0, func(acc int, item int) int {
		return acc + item
	})

	expectedSum := 15 // 1+2+3+4+5
	if sum != expectedSum {
		t.Errorf("Expected sum %d, got %d", expectedSum, sum)
	}
}
