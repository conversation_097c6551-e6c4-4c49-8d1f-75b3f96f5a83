package util

// MergeAnyMap 合并两个任意类型的map，后者覆盖前者，支持nil安全
func MergeAnyMap[K comparable, V any](m1, m2 map[K]V) map[K]V {
	// 如果两个都是nil，返回空map
	if m1 == nil && m2 == nil {
		return make(map[K]V)
	}
	// 如果m1为nil，直接复制m2
	if m1 == nil {
		result := make(map[K]V, len(m2))
		for k, v := range m2 {
			result[k] = v
		}
		return result
	}
	// 如果m2为nil，直接复制m1
	if m2 == nil {
		result := make(map[K]V, len(m1))
		for k, v := range m1 {
			result[k] = v
		}
		return result
	}
	// 都不为nil，合并
	result := make(map[K]V, len(m1)+len(m2))
	for k, v := range m1 {
		result[k] = v
	}
	for k, v := range m2 {
		result[k] = v
	}
	return result
}
