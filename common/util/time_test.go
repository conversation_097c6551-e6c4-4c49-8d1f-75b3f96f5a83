package util

import (
	"github.com/smartystreets/goconvey/convey"
	"testing"
	"time"
)

func TestIsCurrentTimeSpanRangeValue(t *testing.T) {
	convey.Convey("Test IsCurrentTimeSpanRangeValue", t, func() {

		// 测试正常情况：当前时间在范围内
		convey.Convey("Case 1: Current time is within the range", func() {
			value := "[1700000000,1700000400]"
			now := time.Unix(1700000200, 0).UTC()
			result := IsCurrentTimeSpanRangeValue(value, now)
			convey.So(result, convey.ShouldBeTrue)
		})

		// 测试时间戳格式错误：缺少方括号
		convey.Convey("Case 2: Invalid format - missing brackets", func() {
			value := "1700000000,1700000400"
			now := time.Now().UTC()
			result := IsCurrentTimeSpanRangeValue(value, now)
			convey.So(result, convey.ShouldBeFalse)
		})

		// 测试时间戳格式错误：无法转换为整数
		convey.Convey("Case 3: Invalid format - non-numeric values", func() {
			value := "[abc,123]"
			now := time.Now().UTC()
			result := IsCurrentTimeSpanRangeValue(value, now)
			convey.So(result, convey.ShouldBeFalse)
		})

		// 测试时间戳格式错误：分割后不是两部分
		convey.Convey("Case 4: Invalid format - not two parts", func() {
			value := "[1700000000,1700000400,1700000800]"
			now := time.Now().UTC()
			result := IsCurrentTimeSpanRangeValue(value, now)
			convey.So(result, convey.ShouldBeFalse)
		})

		// 测试当前时间不在范围内：早于起始时间
		convey.Convey("Case 5: Current time is before start time", func() {
			value := "[1700000000,1700000400]"
			now := time.Unix(1699999900, 0).UTC()
			result := IsCurrentTimeSpanRangeValue(value, now)
			convey.So(result, convey.ShouldBeFalse)
		})

		// 测试当前时间不在范围内：晚于结束时间
		convey.Convey("Case 6: Current time is after end time", func() {
			value := "[1700000000,1700000400]"
			now := time.Unix(1700000500, 0).UTC()
			result := IsCurrentTimeSpanRangeValue(value, now)
			convey.So(result, convey.ShouldBeFalse)
		})

		// 测试边界情况：当前时间等于起始时间
		convey.Convey("Case 7: Current time equals start time", func() {
			value := "[1700000000,1700000400]"
			now := time.Unix(1700000000, 0).UTC()
			result := IsCurrentTimeSpanRangeValue(value, now)
			convey.So(result, convey.ShouldBeTrue)
		})

		// 测试边界情况：当前时间等于结束时间
		convey.Convey("Case 8: Current time equals end time", func() {
			value := "[1700000000,1700000400]"
			now := time.Unix(1700000400, 0).UTC()
			result := IsCurrentTimeSpanRangeValue(value, now)
			convey.So(result, convey.ShouldBeTrue)
		})
	})
}

func TestIsCurrentTimeDepartureRange(t *testing.T) {
	convey.Convey("Test IsCurrentTimeDepartureRange", t, func() {
		// 测试用例1：times长度不为2
		convey.Convey("Case 1: times length is not 2", func() {
			times := []int64{1700000000}
			now := time.Unix(1700000200, 0).UTC()
			result := IsCurrentTimeDepartureRange(times, now)
			convey.So(result, convey.ShouldBeTrue)
		})

		// 测试用例2：当前时间在范围内
		convey.Convey("Case 2: Current time is within the range", func() {
			times := []int64{1700000000, 1700000400}
			now := time.Unix(1700000200, 0).UTC()
			result := IsCurrentTimeDepartureRange(times, now)
			convey.So(result, convey.ShouldBeTrue)
		})

		// 测试用例3：当前时间早于起始时间
		convey.Convey("Case 3: Current time is before start time", func() {
			times := []int64{1700000000, 1700000400}
			now := time.Unix(1699999900, 0).UTC()
			result := IsCurrentTimeDepartureRange(times, now)
			convey.So(result, convey.ShouldBeFalse)
		})

		// 测试用例4：当前时间晚于结束时间
		convey.Convey("Case 4: Current time is after end time", func() {
			times := []int64{1700000000, 1700000400}
			now := time.Unix(1700000500, 0).UTC()
			result := IsCurrentTimeDepartureRange(times, now)
			convey.So(result, convey.ShouldBeFalse)
		})

		// 测试用例5：当前时间等于起始时间
		convey.Convey("Case 5: Current time equals start time", func() {
			times := []int64{1700000000, 1700000400}
			now := time.Unix(1700000000, 0).UTC()
			result := IsCurrentTimeDepartureRange(times, now)
			convey.So(result, convey.ShouldBeTrue)
		})

		// 测试用例6：当前时间等于结束时间
		convey.Convey("Case 6: Current time equals end time", func() {
			times := []int64{1700000000, 1700000400}
			now := time.Unix(1700000400, 0).UTC()
			result := IsCurrentTimeDepartureRange(times, now)
			convey.So(result, convey.ShouldBeTrue)
		})
	})
}
