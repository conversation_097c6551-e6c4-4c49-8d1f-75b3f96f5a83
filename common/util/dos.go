package util

import (
	orderidUtil "git.xiaojukeji.com/gulfstream/orderid-util/orderid"
)

func DecodeOrderID(oid string) (orderID int64, district string, err error) {
	orderID, district, err = orderidUtil.DecodeOrderID(oid)
	return
}

// EncodeOrderIdWithoutBase64 低位转高
func EncodeOrderIdWithoutBase64(orderId int64, district string) (int64, error) {
	return orderidUtil.EncodeOrderIDWithoutBase64(orderId, district)
}
