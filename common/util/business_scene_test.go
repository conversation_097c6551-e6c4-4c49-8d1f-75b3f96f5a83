package util

import (
	"testing"

	"git.xiaojukeji.com/gulfstream/mamba/common/biz_util/source_id"
)

func TestIsXinZhu(t *testing.T) {
	tests := []struct {
		name        string
		sourceId    int32
		accessKeyId int32
		appVersion  string
		lang        string
		want        bool
	}{
		{
			name:        "正常场景-主端-新版本",
			sourceId:    source_id.SourceIDXinZhuAppendCar,
			accessKeyId: 1, // 假设1是主端的accessKeyId
			appVersion:  "7.0.20",
			lang:        "zh-CN",
			want:        true,
		},
		{
			name:        "正常场景-小程序-新版本",
			sourceId:    source_id.SourceIDXinZhuAppendCar,
			accessKeyId: 9, // 假设2是小程序的accessKeyId
			appVersion:  "6.11.0",
			lang:        "zh-CN",
			want:        true,
		},
		{
			name:        "错误场景-错误的sourceId",
			sourceId:    source_id.SourceIDXinZhuAppendCar + 1,
			accessKeyId: 1,
			appVersion:  "7.0.20",
			lang:        "zh-CN",
			want:        false,
		},
		{
			name:        "错误场景-非中文",
			sourceId:    source_id.SourceIDXinZhuAppendCar,
			accessKeyId: 1,
			appVersion:  "7.0.20",
			lang:        "en-US",
			want:        false,
		},
		{
			name:        "错误场景-主端版本过低",
			sourceId:    source_id.SourceIDXinZhuAppendCar,
			accessKeyId: 1,
			appVersion:  "7.0.19",
			lang:        "zh-CN",
			want:        false,
		},
		{
			name:        "错误场景-小程序版本过低",
			sourceId:    source_id.SourceIDXinZhuAppendCar,
			accessKeyId: 2,
			appVersion:  "6.10.9",
			lang:        "zh-CN",
			want:        false,
		},
		{
			name:        "错误场景-未知的accessKeyId",
			sourceId:    source_id.SourceIDXinZhuAppendCar,
			accessKeyId: 999,
			appVersion:  "7.0.20",
			lang:        "zh-CN",
			want:        false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := IsXinZhu(tt.sourceId, tt.accessKeyId, tt.appVersion, tt.lang)
			if got != tt.want {
				t.Errorf("IsXinZhu() = %v, want %v", got, tt.want)
			}
		})
	}
}
