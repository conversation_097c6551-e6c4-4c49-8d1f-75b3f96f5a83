package util

import (
	"fmt"
	"sync"
)

// BiMap 双向映射，支持正向和反向查找
type BiMap[K comparable, V comparable] struct {
	forward  map[K]V // 正向映射 K -> V
	backward map[V]K // 反向映射 V -> K
	mutex    sync.RWMutex
}

// NewBiMap 创建一个新的双向映射
func NewBiMap[K comparable, V comparable]() *BiMap[K, V] {
	return &BiMap[K, V]{
		forward:  make(map[K]V),
		backward: make(map[V]K),
	}
}

// Put 添加键值对，如果键或值已存在，会覆盖原有映射
func (bm *BiMap[K, V]) Put(key K, value V) {
	bm.mutex.Lock()
	defer bm.mutex.Unlock()

	// 如果key已存在，先删除旧的反向映射
	if oldValue, exists := bm.forward[key]; exists {
		delete(bm.backward, oldValue)
	}

	// 如果value已存在，先删除旧的正向映射
	if oldKey, exists := bm.backward[value]; exists {
		delete(bm.forward, oldKey)
	}

	// 添加新的映射
	bm.forward[key] = value
	bm.backward[value] = key
}

// GetByKey 通过键获取值
func (bm *BiMap[K, V]) GetByKey(key K) (V, bool) {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	value, exists := bm.forward[key]
	return value, exists
}

// GetByValue 通过值获取键
func (bm *BiMap[K, V]) GetByValue(value V) (K, bool) {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	key, exists := bm.backward[value]
	return key, exists
}

// ContainsKey 检查是否包含指定的键
func (bm *BiMap[K, V]) ContainsKey(key K) bool {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	_, exists := bm.forward[key]
	return exists
}

// ContainsValue 检查是否包含指定的值
func (bm *BiMap[K, V]) ContainsValue(value V) bool {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	_, exists := bm.backward[value]
	return exists
}

// RemoveByKey 通过键删除映射
func (bm *BiMap[K, V]) RemoveByKey(key K) bool {
	bm.mutex.Lock()
	defer bm.mutex.Unlock()

	if value, exists := bm.forward[key]; exists {
		delete(bm.forward, key)
		delete(bm.backward, value)
		return true
	}
	return false
}

// RemoveByValue 通过值删除映射
func (bm *BiMap[K, V]) RemoveByValue(value V) bool {
	bm.mutex.Lock()
	defer bm.mutex.Unlock()

	if key, exists := bm.backward[value]; exists {
		delete(bm.forward, key)
		delete(bm.backward, value)
		return true
	}
	return false
}

// Size 返回映射的大小
func (bm *BiMap[K, V]) Size() int {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	return len(bm.forward)
}

// IsEmpty 检查映射是否为空
func (bm *BiMap[K, V]) IsEmpty() bool {
	return bm.Size() == 0
}

// Clear 清空所有映射
func (bm *BiMap[K, V]) Clear() {
	bm.mutex.Lock()
	defer bm.mutex.Unlock()

	bm.forward = make(map[K]V)
	bm.backward = make(map[V]K)
}

// Keys 返回所有键的切片
func (bm *BiMap[K, V]) Keys() []K {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	keys := make([]K, 0, len(bm.forward))
	for key := range bm.forward {
		keys = append(keys, key)
	}
	return keys
}

// Values 返回所有值的切片
func (bm *BiMap[K, V]) Values() []V {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	values := make([]V, 0, len(bm.backward))
	for value := range bm.backward {
		values = append(values, value)
	}
	return values
}

// ForEach 遍历所有键值对
func (bm *BiMap[K, V]) ForEach(fn func(K, V)) {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	for key, value := range bm.forward {
		fn(key, value)
	}
}

// ToMap 转换为普通的map[K]V
func (bm *BiMap[K, V]) ToMap() map[K]V {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	result := make(map[K]V, len(bm.forward))
	for key, value := range bm.forward {
		result[key] = value
	}
	return result
}

// ToReverseMap 转换为反向的map[V]K
func (bm *BiMap[K, V]) ToReverseMap() map[V]K {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	result := make(map[V]K, len(bm.backward))
	for value, key := range bm.backward {
		result[value] = key
	}
	return result
}

// Clone 克隆BiMap
func (bm *BiMap[K, V]) Clone() *BiMap[K, V] {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	clone := NewBiMap[K, V]()
	for key, value := range bm.forward {
		clone.forward[key] = value
		clone.backward[value] = key
	}
	return clone
}

// String 返回BiMap的字符串表示
func (bm *BiMap[K, V]) String() string {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	return fmt.Sprintf("BiMap{forward: %v, backward: %v}", bm.forward, bm.backward)
}

// Inverse 返回一个反向的BiMap视图
func (bm *BiMap[K, V]) Inverse() *BiMap[V, K] {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	inverse := NewBiMap[V, K]()
	for key, value := range bm.forward {
		inverse.forward[value] = key
		inverse.backward[key] = value
	}
	return inverse
}

// PutAll 批量添加映射
func (bm *BiMap[K, V]) PutAll(pairs map[K]V) {
	for key, value := range pairs {
		bm.Put(key, value)
	}
}

// Filter 根据条件过滤BiMap，返回新的BiMap
func (bm *BiMap[K, V]) Filter(predicate func(K, V) bool) *BiMap[K, V] {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	result := NewBiMap[K, V]()
	for key, value := range bm.forward {
		if predicate(key, value) {
			result.Put(key, value)
		}
	}
	return result
}

// MapKeys 将所有键通过函数转换，返回新的BiMap
func MapKeys[K comparable, V comparable, NK comparable](bm *BiMap[K, V], mapper func(K) NK) *BiMap[NK, V] {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	result := NewBiMap[NK, V]()
	for key, value := range bm.forward {
		newKey := mapper(key)
		result.Put(newKey, value)
	}
	return result
}

// MapValues 将所有值通过函数转换，返回新的BiMap
func MapValues[K comparable, V comparable, NV comparable](bm *BiMap[K, V], mapper func(V) NV) *BiMap[K, NV] {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	result := NewBiMap[K, NV]()
	for key, value := range bm.forward {
		newValue := mapper(value)
		result.Put(key, newValue)
	}
	return result
}
