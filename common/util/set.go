package util

// Set 泛型Set数据结构
type Set[T comparable] struct {
	data map[T]struct{}
}

// NewSet 创建一个新的Set
func NewSet[T comparable](items ...T) *Set[T] {
	s := &Set[T]{
		data: make(map[T]struct{}),
	}
	for _, item := range items {
		s.Add(item)
	}
	return s
}

// Add 添加元素到Set中
func (s *Set[T]) Add(item T) {
	s.data[item] = struct{}{}
}

// Remove 从Set中移除元素
func (s *Set[T]) Remove(item T) {
	delete(s.data, item)
}

// Contains 检查Set中是否包含指定元素
func (s *Set[T]) Contains(item T) bool {
	_, exists := s.data[item]
	return exists
}

// Size 返回Set的大小
func (s *Set[T]) Size() int {
	return len(s.data)
}

// IsEmpty 检查Set是否为空
func (s *Set[T]) IsEmpty() bool {
	return len(s.data) == 0
}

// Clear 清空Set
func (s *Set[T]) Clear() {
	s.data = make(map[T]struct{})
}

// ToSlice 将Set转换为切片
func (s *Set[T]) ToSlice() []T {
	result := make([]T, 0, len(s.data))
	for item := range s.data {
		result = append(result, item)
	}
	return result
}

// Union 返回两个Set的并集
func (s *Set[T]) Union(other *Set[T]) *Set[T] {
	result := NewSet[T]()
	for item := range s.data {
		result.Add(item)
	}
	for item := range other.data {
		result.Add(item)
	}
	return result
}

// Intersection 返回两个Set的交集
func (s *Set[T]) Intersection(other *Set[T]) *Set[T] {
	result := NewSet[T]()
	for item := range s.data {
		if other.Contains(item) {
			result.Add(item)
		}
	}
	return result
}

// Difference 返回两个Set的差集 (s - other)
func (s *Set[T]) Difference(other *Set[T]) *Set[T] {
	result := NewSet[T]()
	for item := range s.data {
		if !other.Contains(item) {
			result.Add(item)
		}
	}
	return result
}

// IsSubset 检查当前Set是否是另一个Set的子集
func (s *Set[T]) IsSubset(other *Set[T]) bool {
	for item := range s.data {
		if !other.Contains(item) {
			return false
		}
	}
	return true
}

// IsSuperset 检查当前Set是否是另一个Set的超集
func (s *Set[T]) IsSuperset(other *Set[T]) bool {
	return other.IsSubset(s)
}

// Equal 检查两个Set是否相等
func (s *Set[T]) Equal(other *Set[T]) bool {
	if s.Size() != other.Size() {
		return false
	}
	for item := range s.data {
		if !other.Contains(item) {
			return false
		}
	}
	return true
}

// ForEach 遍历Set中的每个元素
func (s *Set[T]) ForEach(fn func(T)) {
	for item := range s.data {
		fn(item)
	}
}

// Filter 根据条件过滤Set中的元素，返回新的Set
func (s *Set[T]) Filter(predicate func(T) bool) *Set[T] {
	result := NewSet[T]()
	for item := range s.data {
		if predicate(item) {
			result.Add(item)
		}
	}
	return result
}

// Clone 克隆Set
func (s *Set[T]) Clone() *Set[T] {
	result := NewSet[T]()
	for item := range s.data {
		result.Add(item)
	}
	return result
}

// String 返回Set的字符串表示
func (s *Set[T]) String() string {
	items := s.ToSlice()
	return ToString(items)
}

// AddAll 批量添加元素
func (s *Set[T]) AddAll(items ...T) {
	for _, item := range items {
		s.Add(item)
	}
}

// RemoveAll 批量移除元素
func (s *Set[T]) RemoveAll(items ...T) {
	for _, item := range items {
		s.Remove(item)
	}
}

// ContainsAll 检查是否包含所有指定元素
func (s *Set[T]) ContainsAll(items ...T) bool {
	for _, item := range items {
		if !s.Contains(item) {
			return false
		}
	}
	return true
}

// ContainsAny 检查是否包含任意一个指定元素
func (s *Set[T]) ContainsAny(items ...T) bool {
	for _, item := range items {
		if s.Contains(item) {
			return true
		}
	}
	return false
}

// SymmetricDifference 返回两个Set的对称差集 (A ∪ B) - (A ∩ B)
func (s *Set[T]) SymmetricDifference(other *Set[T]) *Set[T] {
	union := s.Union(other)
	intersection := s.Intersection(other)
	return union.Difference(intersection)
}

// FromSlice 从切片创建Set
func FromSlice[T comparable](slice []T) *Set[T] {
	return NewSet[T](slice...)
}

// Map 将Set中的每个元素通过函数转换为新类型的Set
func Map[T comparable, U comparable](s *Set[T], mapper func(T) U) *Set[U] {
	result := NewSet[U]()
	for item := range s.data {
		result.Add(mapper(item))
	}
	return result
}

// Reduce 将Set中的元素归约为单个值
func Reduce[T comparable, U any](s *Set[T], initial U, reducer func(U, T) U) U {
	result := initial
	for item := range s.data {
		result = reducer(result, item)
	}
	return result
}
