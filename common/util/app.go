package util

import (
	"git.xiaojukeji.com/gulfstream/biz-common-go/consts"
)

func IsNA(accessKeyId int32) bool {
	if accessKeyId == consts.AccessKeyIDDiDiIos || accessKeyId == consts.AccessKeyIDDiDiAndroid {
		return true
	}
	return false
}

func IsMini(accessKeyId int32) bool {
	if accessKeyId == consts.AccessKeyIDDiDiWechatMini || accessKeyId == consts.AccessKeyIDDiDiAlipayMini {
		return true
	}
	return false
}
