package util

import "git.xiaojukeji.com/gulfstream/mamba/common/biz_util/source_id"

const (
	BusinessTravelOrderTypeXinZhu = "xinzhu_v1"
)

func IsXinZhu(sourceId int32, accessKeyId int32, appVersion, lang string) bool {

	if sourceId != source_id.SourceIDXinZhuAppendCar {
		return false
	}

	if lang != "zh-CN" {
		return false
	}

	if !IsFromMain(accessKeyId) && !IsFromMiniProgram(accessKeyId) {
		return false
	}

	// mock 阶段，先不交验版本号
	// return true

	if IsFromMain(accessKeyId) && CompareAppVersion(appVersion, "7.0.20") < 0 {
		return false
	}

	if IsFromMiniProgram(accessKeyId) && CompareAppVersion(appVersion, "6.11.0") < 0 {
		return false
	}

	return true
}

// 是否来自新竹返现-订单
func IsFromXinZhuWithBusinessTravelOrderType(businessTravelOrderType string) bool {
	return businessTravelOrderType == BusinessTravelOrderTypeXinZhu
}
