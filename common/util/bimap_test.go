package util

import (
	"strconv"
	"testing"
)

func TestBiMap_Basic(t *testing.T) {
	bm := NewBiMap[string, int]()

	// 测试添加
	bm.Put("one", 1)
	bm.Put("two", 2)
	bm.Put("three", 3)

	if bm.<PERSON>ze() != 3 {
		t.<PERSON><PERSON>("Expected size 3, got %d", bm.<PERSON><PERSON>())
	}

	// 测试正向查找
	if value, exists := bm.GetByKey("one"); !exists || value != 1 {
		t.<PERSON><PERSON>("Expected value 1 for key 'one', got %d, exists: %t", value, exists)
	}

	// 测试反向查找
	if key, exists := bm.GetByValue(2); !exists || key != "two" {
		t.<PERSON><PERSON>("Expected key 'two' for value 2, got %s, exists: %t", key, exists)
	}

	// 测试不存在的键值
	if _, exists := bm.GetByKey("four"); exists {
		t.<PERSON><PERSON>("Should not find key 'four'")
	}

	if _, exists := bm.GetByValue(4); exists {
		t.Error("Should not find value 4")
	}
}

func TestBiMap_Contains(t *testing.T) {
	bm := NewBiMap[string, int]()
	bm.Put("hello", 100)
	bm.Put("world", 200)

	// 测试包含键
	if !bm.ContainsKey("hello") {
		t.Error("Should contain key 'hello'")
	}

	if bm.ContainsKey("foo") {
		t.Error("Should not contain key 'foo'")
	}

	// 测试包含值
	if !bm.ContainsValue(100) {
		t.Error("Should contain value 100")
	}

	if bm.ContainsValue(300) {
		t.Error("Should not contain value 300")
	}
}

func TestBiMap_Remove(t *testing.T) {
	bm := NewBiMap[string, int]()
	bm.Put("a", 1)
	bm.Put("b", 2)
	bm.Put("c", 3)

	// 通过键删除
	if !bm.RemoveByKey("b") {
		t.Error("Should successfully remove key 'b'")
	}

	if bm.ContainsKey("b") || bm.ContainsValue(2) {
		t.Error("Should not contain 'b' or 2 after removal")
	}

	// 通过值删除
	if !bm.RemoveByValue(3) {
		t.Error("Should successfully remove value 3")
	}

	if bm.ContainsKey("c") || bm.ContainsValue(3) {
		t.Error("Should not contain 'c' or 3 after removal")
	}

	if bm.Size() != 1 {
		t.Errorf("Expected size 1 after removals, got %d", bm.Size())
	}

	// 删除不存在的键值
	if bm.RemoveByKey("nonexistent") {
		t.Error("Should not remove nonexistent key")
	}

	if bm.RemoveByValue(999) {
		t.Error("Should not remove nonexistent value")
	}
}

func TestBiMap_Overwrite(t *testing.T) {
	bm := NewBiMap[string, int]()

	// 初始映射
	bm.Put("key1", 100)
	bm.Put("key2", 200)

	// 覆盖键的值
	bm.Put("key1", 300)

	if value, _ := bm.GetByKey("key1"); value != 300 {
		t.Errorf("Expected value 300 for key1, got %d", value)
	}

	// 原来的值100应该不再存在
	if bm.ContainsValue(100) {
		t.Error("Should not contain old value 100")
	}

	// 覆盖值的键
	bm.Put("key3", 200) // 200已经被key2使用

	if key, _ := bm.GetByValue(200); key != "key3" {
		t.Errorf("Expected key3 for value 200, got %s", key)
	}

	// 原来的key2应该不再存在
	if bm.ContainsKey("key2") {
		t.Error("Should not contain old key2")
	}
}

func TestBiMap_KeysValues(t *testing.T) {
	bm := NewBiMap[string, int]()
	bm.Put("a", 1)
	bm.Put("b", 2)
	bm.Put("c", 3)

	keys := bm.Keys()
	values := bm.Values()

	if len(keys) != 3 {
		t.Errorf("Expected 3 keys, got %d", len(keys))
	}

	if len(values) != 3 {
		t.Errorf("Expected 3 values, got %d", len(values))
	}

	// 检查所有键都存在
	keySet := NewSet[string](keys...)
	if !keySet.ContainsAll("a", "b", "c") {
		t.Error("Keys should contain a, b, c")
	}

	// 检查所有值都存在
	valueSet := NewSet[int](values...)
	if !valueSet.ContainsAll(1, 2, 3) {
		t.Error("Values should contain 1, 2, 3")
	}
}

func TestBiMap_ForEach(t *testing.T) {
	bm := NewBiMap[string, int]()
	bm.Put("x", 10)
	bm.Put("y", 20)
	bm.Put("z", 30)

	sum := 0
	keyCount := 0

	bm.ForEach(func(key string, value int) {
		sum += value
		keyCount++
	})

	if sum != 60 {
		t.Errorf("Expected sum 60, got %d", sum)
	}

	if keyCount != 3 {
		t.Errorf("Expected to iterate 3 times, got %d", keyCount)
	}
}

func TestBiMap_ToMap(t *testing.T) {
	bm := NewBiMap[string, int]()
	bm.Put("alpha", 1)
	bm.Put("beta", 2)

	forwardMap := bm.ToMap()
	reverseMap := bm.ToReverseMap()

	if len(forwardMap) != 2 {
		t.Errorf("Expected forward map size 2, got %d", len(forwardMap))
	}

	if len(reverseMap) != 2 {
		t.Errorf("Expected reverse map size 2, got %d", len(reverseMap))
	}

	if forwardMap["alpha"] != 1 {
		t.Error("Forward map should contain alpha -> 1")
	}

	if reverseMap[2] != "beta" {
		t.Error("Reverse map should contain 2 -> beta")
	}
}

func TestBiMap_Clone(t *testing.T) {
	original := NewBiMap[string, int]()
	original.Put("test", 42)

	cloned := original.Clone()

	if cloned.Size() != original.Size() {
		t.Error("Cloned BiMap should have same size as original")
	}

	if value, _ := cloned.GetByKey("test"); value != 42 {
		t.Error("Cloned BiMap should contain same data")
	}

	// 修改原始BiMap不应该影响克隆的BiMap
	original.Put("new", 99)

	if cloned.ContainsKey("new") {
		t.Error("Cloned BiMap should not be affected by changes to original")
	}
}

func TestBiMap_Inverse(t *testing.T) {
	bm := NewBiMap[string, int]()
	bm.Put("one", 1)
	bm.Put("two", 2)

	inverse := bm.Inverse()

	if inverse.Size() != 2 {
		t.Errorf("Expected inverse size 2, got %d", inverse.Size())
	}

	if key, _ := inverse.GetByKey(1); key != "one" {
		t.Errorf("Expected key 'one' for value 1 in inverse, got %s", key)
	}

	if value, _ := inverse.GetByValue("two"); value != 2 {
		t.Errorf("Expected value 2 for key 'two' in inverse, got %d", value)
	}
}

func TestBiMap_Filter(t *testing.T) {
	bm := NewBiMap[string, int]()
	bm.Put("a", 1)
	bm.Put("b", 2)
	bm.Put("c", 3)
	bm.Put("d", 4)

	// 过滤出值为偶数的映射
	filtered := bm.Filter(func(key string, value int) bool {
		return value%2 == 0
	})

	if filtered.Size() != 2 {
		t.Errorf("Expected filtered size 2, got %d", filtered.Size())
	}

	if !filtered.ContainsKey("b") || !filtered.ContainsKey("d") {
		t.Error("Filtered BiMap should contain keys 'b' and 'd'")
	}
}

func TestMapKeys(t *testing.T) {
	bm := NewBiMap[int, string]()
	bm.Put(1, "one")
	bm.Put(2, "two")
	bm.Put(3, "three")

	// 将int键转换为string
	mapped := MapKeys(bm, func(key int) string {
		return "key_" + strconv.Itoa(key)
	})

	if mapped.Size() != 3 {
		t.Errorf("Expected mapped size 3, got %d", mapped.Size())
	}

	if value, _ := mapped.GetByKey("key_1"); value != "one" {
		t.Errorf("Expected value 'one' for key 'key_1', got %s", value)
	}
}

func TestMapValues(t *testing.T) {
	bm := NewBiMap[string, int]()
	bm.Put("a", 1)
	bm.Put("b", 2)
	bm.Put("c", 3)

	// 将int值转换为string
	mapped := MapValues(bm, func(value int) string {
		return "val_" + strconv.Itoa(value)
	})

	if mapped.Size() != 3 {
		t.Errorf("Expected mapped size 3, got %d", mapped.Size())
	}

	if value, _ := mapped.GetByKey("a"); value != "val_1" {
		t.Errorf("Expected value 'val_1' for key 'a', got %s", value)
	}
}

func TestBiMap_Clear(t *testing.T) {
	bm := NewBiMap[string, int]()
	bm.Put("test", 123)

	bm.Clear()

	if !bm.IsEmpty() {
		t.Error("BiMap should be empty after clear")
	}

	if bm.Size() != 0 {
		t.Errorf("Expected size 0 after clear, got %d", bm.Size())
	}
}
