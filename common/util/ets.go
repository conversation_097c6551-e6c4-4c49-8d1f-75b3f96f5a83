package util

import "git.xiaojukeji.com/gulfstream/mamba/common/consts"

const (
	SecondUnitCN = "秒"
	MinuteUnitCN = "分钟"
)

func GetETSInfo(ets int64, lang string) (timeValue int64, timeUnit string) {
	secondUnit := SecondUnitCN
	minuteUnit := MinuteUnitCN
	switch lang {
	case consts.LangZhCN:
		secondUnit = SecondUnitCN
		minuteUnit = MinuteUnitCN
	}

	if ets < 60 { //秒
		return ets, secondUnit
	} else { //分钟
		return ConvertETSToMinutes(ets), minuteUnit
	}
}

// ets-秒转分
func ConvertETSToMinutes(ets int64) int64 {
	// 计算总分钟数
	minutes := ets / 60

	// 如果有余数，则舍入到下一个整分钟
	if ets%60 > 0 {
		minutes++
	}

	return minutes
}
